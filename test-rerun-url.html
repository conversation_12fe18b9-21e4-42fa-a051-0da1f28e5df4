<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试重新运行URL参数</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-link {
            display: block;
            margin: 10px 0;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 5px;
            text-decoration: none;
            color: #333;
        }
        .test-link:hover {
            background: #e0e0e0;
        }
        .description {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }
        .url {
            font-family: monospace;
            background: #fff;
            padding: 5px;
            border-radius: 3px;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <h1>测试重新运行URL参数传递</h1>
    
    <p>以下是测试重新运行功能的URL示例，点击可以验证参数是否正确传递到测试页面：</p>
    
    <h2>测试场景</h2>
    
    <a href="http://localhost:3001/test?agent_id=agent_72a7e1599497&input_text=测试输入文本" 
       class="test-link" target="_blank">
        <strong>基本参数测试</strong>
        <div class="description">测试agent_id和input_text参数传递</div>
        <div class="url">agent_id=agent_72a7e1599497&input_text=测试输入文本</div>
    </a>
    
    <a href="http://localhost:3001/test?agent_id=agent_72a7e1599497&input_text=带有AI配置的测试&ai_config=%7B%22provider%22%3A%22openai%22%2C%22model%22%3A%22gpt-4%22%2C%22temperature%22%3A0.8%2C%22max_tokens%22%3A1500%7D" 
       class="test-link" target="_blank">
        <strong>AI配置参数测试</strong>
        <div class="description">测试AI配置JSON参数传递</div>
        <div class="url">包含ai_config JSON配置</div>
    </a>
    
    <a href="http://localhost:3001/test?agent_id=agent_72a7e1599497&input_text=带有API密钥的测试&api_key_name=default_key" 
       class="test-link" target="_blank">
        <strong>API密钥参数测试</strong>
        <div class="description">测试API密钥名称参数传递</div>
        <div class="url">包含api_key_name参数</div>
    </a>
    
    <a href="http://localhost:3001/test?agent_id=agent_72a7e1599497&input_text=完整参数测试&ai_config=%7B%22provider%22%3A%22openai%22%2C%22model%22%3A%22gpt-3.5-turbo%22%2C%22temperature%22%3A0.7%2C%22max_tokens%22%3A2000%7D&api_key_name=test_key" 
       class="test-link" target="_blank">
        <strong>完整参数测试</strong>
        <div class="description">测试所有参数同时传递</div>
        <div class="url">包含所有参数：agent_id, input_text, ai_config, api_key_name</div>
    </a>
    
    <h2>验证步骤</h2>
    <ol>
        <li>确保前端服务器运行在 http://localhost:3001</li>
        <li>确保已经登录系统</li>
        <li>点击上面的测试链接</li>
        <li>验证以下内容：
            <ul>
                <li>页面不会触发重新登录</li>
                <li>自动选择了正确的Agent</li>
                <li>输入框中预填充了正确的文本</li>
                <li>AI配置正确设置（如果有）</li>
                <li>API密钥正确选择（如果有）</li>
            </ul>
        </li>
    </ol>
    
    <h2>预期结果</h2>
    <ul>
        <li>✅ 直接跳转到测试页面，无需重新登录</li>
        <li>✅ Agent自动选择并进入测试界面</li>
        <li>✅ 输入文本正确预填充</li>
        <li>✅ AI配置正确应用（如果提供）</li>
        <li>✅ API密钥正确选择（如果提供）</li>
        <li>✅ 可以直接运行测试或修改配置</li>
    </ul>
    
    <h2>调试信息</h2>
    <p>打开浏览器开发者工具的控制台，查看以下调试信息：</p>
    <ul>
        <li><code>Processing initial parameters:</code> - 初始参数处理</li>
        <li><code>Set initial input:</code> - 输入文本设置</li>
        <li><code>Parsed AI config:</code> - AI配置解析</li>
        <li><code>Found matching API key:</code> - API密钥匹配</li>
    </ul>
</body>
</html>
