{"folders": [{"name": "Root", "path": "."}, {"name": "Frontend", "path": "./frontend"}, {"name": "Backend", "path": "./backend"}, {"name": "Docs", "path": "./docs"}], "settings": {"typescript.preferences.includePackageJsonAutoImports": "auto", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": true}, "python.defaultInterpreterPath": "./backend/venv/bin/python", "python.formatting.provider": "black", "python.linting.enabled": true, "python.linting.pylintEnabled": true, "files.exclude": {"**/node_modules": true, "**/__pycache__": true, "**/.next": true, "**/dist": true, "**/build": true}, "search.exclude": {"**/node_modules": true, "**/.next": true, "**/dist": true, "**/build": true}}, "extensions": {"recommendations": ["ms-python.python", "ms-python.black-formatter", "ms-python.pylint", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "dbaeumer.vscode-eslint", "ms-vscode.vscode-typescript-next", "ms-vscode.vscode-json", "redhat.vscode-yaml", "ms-vscode.vscode-docker"]}, "tasks": {"version": "2.0.0", "tasks": [{"label": "Frontend: Install Dependencies", "type": "shell", "command": "npm install", "options": {"cwd": "${workspaceFolder}/frontend"}, "group": "build"}, {"label": "Frontend: Dev Server", "type": "shell", "command": "npm run dev", "options": {"cwd": "${workspaceFolder}/frontend"}, "group": "build", "isBackground": true}, {"label": "Backend: Install Dependencies", "type": "shell", "command": "pip install -r requirements.txt", "options": {"cwd": "${workspaceFolder}/backend"}, "group": "build"}, {"label": "Backend: Dev Server", "type": "shell", "command": "uvicorn app.main:app --reload --host 0.0.0.0 --port 8000", "options": {"cwd": "${workspaceFolder}/backend"}, "group": "build", "isBackground": true}, {"label": "Docker: Build & Start", "type": "shell", "command": "docker-compose up --build", "options": {"cwd": "${workspaceFolder}"}, "group": "build"}]}}