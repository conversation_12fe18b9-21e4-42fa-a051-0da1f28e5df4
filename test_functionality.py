#!/usr/bin/env python3
"""
Test script to verify the template functionality and agent creation workflow.
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_login():
    """Test user login and get auth token."""
    print("🔐 Testing login...")
    response = requests.post(f"{BASE_URL}/api/v1/auth/login", json={
        "email": "<EMAIL>",
        "password": "admin123"
    })
    
    if response.status_code == 200:
        data = response.json()
        # Token is nested in tokens object
        tokens = data.get("tokens", {})
        token = tokens.get("access_token")
        if token:
            print("✅ Login successful")
            return token
        else:
            print("❌ Login failed: No access token in response")
            print("Response:", data)
            return None
    else:
        print(f"❌ Login failed: {response.status_code}")
        print("Response:", response.text)
        return None

def test_template_list(token):
    """Test template listing."""
    print("\n📋 Testing template list...")
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/api/v1/templates/", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        # API returns list directly or wrapped in data
        if isinstance(data, list):
            templates = data
        else:
            templates = data.get("data", [])
        print(f"✅ Template list successful: {len(templates)} templates found")
        return templates
    else:
        print(f"❌ Template list failed: {response.status_code}")
        print("Response:", response.text)
        return []

def test_template_duplicate(token, template_id):
    """Test template duplication."""
    print(f"\n📄 Testing template duplication for {template_id}...")
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.post(f"{BASE_URL}/api/v1/templates/{template_id}/duplicate", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        new_template_id = data.get("template_id")
        print(f"✅ Template duplication successful: {new_template_id}")
        return new_template_id
    else:
        print(f"❌ Template duplication failed: {response.status_code}")
        print("Response:", response.text)
        return None

def test_agent_from_template(token, template_id):
    """Test creating agent from template."""
    print(f"\n🤖 Testing agent creation from template {template_id}...")
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.post(f"{BASE_URL}/api/v1/agents/from-template", 
                           headers=headers,
                           json={
                               "template_id": template_id,
                               "customizations": {}
                           })
    
    if response.status_code == 200:
        data = response.json()
        agent_id = data.get("agent_id")
        print(f"✅ Agent creation successful: {agent_id}")
        return agent_id
    else:
        print(f"❌ Agent creation failed: {response.status_code}")
        print("Response:", response.text)
        return None

def main():
    """Main test function."""
    print("🚀 Starting functionality tests...\n")
    
    # Test login
    token = test_login()
    if not token:
        print("❌ Cannot proceed without authentication")
        return
    
    # Test template list
    templates = test_template_list(token)
    if not templates:
        print("❌ No templates available for testing")
        return
    
    # Use the first template for testing
    test_template = templates[0]
    template_id = test_template.get("template_id")
    template_name = test_template.get("name")
    print(f"\n🎯 Using template: {template_name} ({template_id})")
    
    # Test template duplication
    new_template_id = test_template_duplicate(token, template_id)
    
    # Test agent creation from template
    agent_id = test_agent_from_template(token, template_id)
    
    print("\n📊 Test Summary:")
    print(f"✅ Login: {'Success' if token else 'Failed'}")
    print(f"✅ Template List: {'Success' if templates else 'Failed'}")
    print(f"✅ Template Duplicate: {'Success' if new_template_id else 'Failed'}")
    print(f"✅ Agent from Template: {'Success' if agent_id else 'Failed'}")
    
    if all([token, templates, new_template_id, agent_id]):
        print("\n🎉 All tests passed!")
    else:
        print("\n⚠️ Some tests failed. Check the output above.")

if __name__ == "__main__":
    main()
