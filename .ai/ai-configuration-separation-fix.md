# AI Model Configuration Separation Fix

## Problem Statement

There was confusion about the purpose of AI model settings in the agent creation form. The system was incorrectly mixing:

1. **Generation-time AI settings**: Used to *generate* the agent team during planning/creation
2. **Runtime AI settings**: Used when the agent is *executed/tested* later

## Correct Understanding

### Two Separate Contexts

1. **Generation-time (Team Planning)**:
   - Uses system-configured AI settings from admin settings
   - Controls which AI model generates the team structure, roles, and workflows
   - Should NOT be configurable by users during agent creation

2. **Runtime (Agent Execution)**:
   - Uses user-specified AI settings from the creation form
   - Controls which AI model the agent uses when processing user requests
   - Should be configurable by users as agent properties

## Implementation Changes

### 1. Frontend Agent Creation Form

**File**: `frontend/src/components/features/agent-creation/agent-creation-form.tsx`

**Changes Made**:
- Updated section title from "高级选项" to "Agent 运行时 AI 配置"
- Added clear description explaining these settings are for agent execution, not team generation
- Changed visual styling to blue theme to distinguish from system settings
- Added helper text for each field clarifying their purpose

**Before**:
```jsx
<h3 className="font-medium flex items-center gap-2">
  ⚙️ 高级选项
  <Badge variant="secondary">可选</Badge>
</h3>
```

**After**:
```jsx
<h3 className="font-medium flex items-center gap-2">
  🤖 Agent 运行时 AI 配置
  <Badge variant="secondary">可选</Badge>
</h3>
<p className="text-sm text-muted-foreground">
  这些设置将用于 Agent 执行任务时的 AI 模型配置。团队生成过程使用系统设置中的 AI 配置。
</p>
```

### 2. Frontend Agent Creation Process

**File**: `frontend/src/app/create/page.tsx`

**Changes Made**:
- Separated runtime AI config from generation process
- Planning API no longer receives user AI configuration
- Runtime AI config is stored and passed to agent creation
- Added proper state management for runtime configuration

**Key Changes**:
```javascript
// Store runtime AI configuration for later use when creating the agent
const runtimeAiConfig = formData.options ? {
  provider: "openai",
  model: formData.options.model || "gpt-4",
  temperature: formData.options.temperature || 0.7,
  max_tokens: 2000,
} : undefined;

// Store runtime AI config for agent creation
setRuntimeAiConfig(runtimeAiConfig);

// Use team generation API (uses system settings, not user runtime config)
const planningResponse = await api.planning.analyze(formData.description);
```

### 3. Backend Planning API

**File**: `backend/app/api/v1/endpoints/planning.py`

**Changes Made**:
- Removed acceptance of user-provided AI configuration
- Planning now uses system settings for team generation
- Added proper system settings lookup for AI configuration

**Before**:
```python
ai_model_config = request.get("ai_model_config", {})
model = ai_model_config.get("model", "gpt-4")
temperature = ai_model_config.get("temperature", 0.7)
```

**After**:
```python
# Get AI model configuration from system settings (not from user request)
from sqlmodel import select
from app.models.settings import SystemSettings

# Get system AI settings for team generation
result = await db.execute(select(SystemSettings).where(SystemSettings.key == "ai_team_generation_provider"))
provider_setting = result.scalar_one_or_none()

result = await db.execute(select(SystemSettings).where(SystemSettings.key == "ai_team_generation_model"))
model_setting = result.scalar_one_or_none()

# Use system settings or defaults for team generation
model = model_setting.value if model_setting else "gpt-4"
temperature = float(temperature_setting.value) if temperature_setting else 0.7
```

### 4. Backend Agent Creation

**File**: `backend/app/api/v1/endpoints/agents.py`

**Changes Made**:
- Enhanced to handle top-level AI configuration from frontend
- Maintains backward compatibility with nested ai_model_config
- Properly stores runtime AI configuration as agent properties

**Enhancement**:
```python
# Extract AI model configuration from team_plan or top-level request
# Check for top-level AI config first (from frontend form), then fallback to team_plan
ai_config = {}
if "ai_provider" in team_plan or "ai_model" in team_plan:
    # Top-level AI config from frontend form (runtime configuration)
    ai_config = {
        "provider": team_plan.get("ai_provider"),
        "model": team_plan.get("ai_model"),
        "temperature": team_plan.get("ai_temperature"),
        "max_tokens": team_plan.get("ai_max_tokens"),
        "base_url": team_plan.get("ai_base_url"),
        "custom_provider_name": team_plan.get("ai_custom_provider_name")
    }
else:
    # Fallback to nested ai_model_config (legacy format)
    ai_config = team_plan.get("ai_model_config", {})
```

### 5. UI Label Updates

**Files Updated**:
- `frontend/src/components/features/agent-management/agent-list.tsx`
- `frontend/src/components/features/agent-management/agent-edit-dialog.tsx`
- `frontend/src/components/features/agent-testing/agent-selector.tsx`
- `frontend/src/components/features/agent-testing/test-interface.tsx`

**Changes Made**:
- Updated all AI configuration section titles to "Agent 运行时 AI 配置" or "运行时 AI 配置"
- Added explanatory text where appropriate
- Maintained consistent terminology across all components

### 6. API Method Cleanup

**File**: `frontend/src/lib/api.ts`

**Changes Made**:
- Removed unused `aiConfig` parameter from planning analyze method
- Updated method signature to reflect correct usage
- Maintained backward compatibility

## Verification

### 1. Separation of Concerns ✅
- **Generation-time**: Planning API uses system settings
- **Runtime**: Agent properties store user-specified AI configuration
- **Clear distinction**: UI labels and descriptions clarify the difference

### 2. Data Flow ✅
```
User Form Input (Runtime AI Config)
    ↓
Stored in Frontend State
    ↓
Passed to Agent Creation API
    ↓
Stored as Agent Properties in Database
    ↓
Used During Agent Execution

System Settings (Generation AI Config)
    ↓
Used by Planning API
    ↓
Generates Team Structure
    ↓
Team Plan Created
```

### 3. Backward Compatibility ✅
- Existing agents continue to work
- Legacy ai_model_config format still supported
- No breaking changes to API responses

### 4. User Experience ✅
- Clear labeling of AI configuration purposes
- Visual distinction (blue theme for runtime config)
- Helpful descriptions and tooltips
- Consistent terminology across all interfaces

## Testing

### Frontend Build ✅
- All TypeScript compilation successful
- No breaking changes to existing functionality
- UI components render correctly

### Backend Compatibility ✅
- Agent creation handles both new and legacy formats
- Planning API properly uses system settings
- Database schema supports all required fields

## Future Enhancements

1. **System Settings UI**: Add interface for admins to configure generation-time AI settings
2. **Validation**: Add validation to ensure runtime AI settings are compatible with system capabilities
3. **Documentation**: Update user documentation to explain the distinction
4. **Testing**: Add automated tests to verify the separation is maintained

## Conclusion

The AI model configuration separation has been successfully implemented, providing:

- **Clear separation** between generation-time and runtime AI settings
- **Proper data flow** with system settings controlling team generation
- **User control** over agent runtime behavior
- **Backward compatibility** with existing agents and templates
- **Improved UX** with clear labeling and descriptions

Users now understand that:
- **Team generation** uses admin-configured system settings
- **Agent execution** uses their specified runtime configuration
- **Both contexts** serve different purposes and can use different AI models
