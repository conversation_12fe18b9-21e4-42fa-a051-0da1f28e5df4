# 2FA功能测试指南

## 🎯 测试概述

本指南提供了完整的2FA功能测试步骤，包括设置、登录流程和敏感操作保护。

## 🚀 系统状态

- **后端API**: ✅ http://127.0.0.1:8001 (正常运行)
- **前端应用**: ✅ http://localhost:3001 (正常运行)
- **演示用户**: ✅ <EMAIL> / demo123 (已启用2FA)

## 📋 测试步骤

### 1. 基础登录测试（无2FA）

如果需要测试无2FA的登录流程：

```bash
# 禁用演示用户的2FA
cd backend
python test_2fa_setup.py disable
```

然后访问 http://localhost:3001/login 进行正常登录。

### 2. 启用2FA测试

```bash
# 启用演示用户的2FA
cd backend
python test_2fa_setup.py enable
```

这将输出：
- TOTP密钥
- 备用代码列表
- QR码URL
- 当前TOTP验证码

### 3. 2FA登录流程测试

#### 步骤1: 访问登录页面
访问: http://localhost:3001/login

#### 步骤2: 输入凭据
- 邮箱: `<EMAIL>`
- 密码: `demo123`
- 点击"Sign in"

#### 步骤3: 2FA验证
系统会显示2FA验证对话框，可以使用：

**选项A: TOTP验证码**
```bash
# 生成当前TOTP代码
cd backend
python3 -c "import pyotp; totp = pyotp.TOTP('4OQKL62SRPYCSFIVWNG7JXQIN6XLDNUS'); print('Current TOTP:', totp.now())"
```

**选项B: 备用代码**
使用以下任一备用代码（每个只能使用一次）：
- HBDBPGY5
- AWPINDE8
- 1RCXYTBH
- R7SSVJ5L
- P6TAH2J1
- MQ9H6WPW
- VKZ82A18
- YDFB0CGS

#### 步骤4: 完成登录
验证成功后，用户将被重定向到主页面。

### 4. 敏感操作测试

#### 测试页面
访问: http://localhost:3001/test-2fa

这个页面提供了模拟的敏感操作测试：
- 密码修改
- 邮箱修改
- 账户删除

#### 实际敏感操作测试
访问: http://localhost:3001/account

在"安全设置"标签页中：
- 尝试修改密码（需要2FA验证）
- 尝试禁用2FA（需要2FA验证）

### 5. API测试

#### 完整2FA登录流程测试
```bash
cd backend
python test_2fa_login.py
```

#### 手动API测试
```bash
# 步骤1: 登录获取临时会话
curl -X POST "http://127.0.0.1:8001/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "demo123", "remember_me": false}'

# 步骤2: 生成TOTP代码
python3 -c "import pyotp; totp = pyotp.TOTP('4OQKL62SRPYCSFIVWNG7JXQIN6XLDNUS'); print(totp.now())"

# 步骤3: 验证2FA（使用上面的临时会话ID和TOTP代码）
curl -X POST "http://127.0.0.1:8001/api/v1/auth/verify-2fa" \
  -H "Content-Type: application/json" \
  -d '{"temp_session_id": "TEMP_SESSION_ID", "totp_code": "TOTP_CODE", "remember_me": false}'
```

## 🔧 故障排除

### 常见问题

1. **"Invalid verification code"**
   - 检查设备时间同步
   - 确保使用最新的TOTP代码
   - 尝试使用备用代码

2. **"Invalid or expired verification session"**
   - 临时会话10分钟过期
   - 重新开始登录流程

3. **"Backup code invalid"**
   - 每个备用代码只能使用一次
   - 检查代码是否已被使用

4. **前端2FA对话框不显示**
   - 检查浏览器控制台错误
   - 确认后端API正常响应

### 重置2FA设置

如果需要重置演示用户的2FA设置：

```bash
cd backend
python test_2fa_setup.py disable
python test_2fa_setup.py enable
```

### 检查用户状态

```bash
cd backend
python test_2fa_setup.py status
```

## 📱 认证应用设置

如果要使用真实的认证应用：

1. **获取QR码URL**:
   ```bash
   cd backend
   python test_2fa_setup.py enable
   ```

2. **手动输入密钥**: `4OQKL62SRPYCSFIVWNG7JXQIN6XLDNUS`

3. **支持的应用**:
   - Google Authenticator
   - Microsoft Authenticator
   - Authy
   - 1Password
   - Bitwarden

## ✅ 验证清单

- [ ] 无2FA用户可以正常登录
- [ ] 启用2FA的用户需要验证码
- [ ] TOTP验证码正常工作
- [ ] 备用代码正常工作
- [ ] 2FA验证对话框正确显示
- [ ] 验证失败有适当错误提示
- [ ] 敏感操作需要2FA验证
- [ ] 密码修改需要2FA验证
- [ ] 2FA禁用需要2FA验证
- [ ] 错误处理和用户指导完善

## 🎉 成功标准

所有2FA功能正常工作时，系统应该：

1. ✅ 支持完整的2FA登录流程
2. ✅ 提供清晰的用户界面和错误提示
3. ✅ 保护所有敏感操作
4. ✅ 支持TOTP和备用代码两种验证方式
5. ✅ 提供完整的错误处理和恢复机制

系统现在已经完全实现了企业级的双因素认证功能！🛡️✨
