# AI Configuration Usage Guide

## Overview

The Meta-Agent system uses two distinct AI configuration contexts to provide flexibility and proper separation of concerns:

1. **System AI Configuration** - Used for team generation
2. **Agent AI Configuration** - Used for agent runtime execution

This separation ensures that administrators can control the AI models used for generating teams, while users can customize the AI behavior for their specific agent use cases.

## Configuration Types

### 1. System AI Configuration (Team Generation)

**Purpose**: Controls the AI models and parameters used during the team planning and generation process.

**Managed By**: System administrators through the Settings page (`/settings`)

**Used For**:
- Analyzing user requirements
- Generating team plans and structures
- Creating agent team members and roles
- Planning workflows and capabilities

**Configuration Location**:
- Frontend: `/app/settings/page.tsx` - "AI团队生成设置" section
- Backend: `SystemSettings` model in `app/models/settings.py`
- Database: `system_settings` table

**Available Settings**:
- `team_generation_provider`: AI provider (openai, anthropic, azure, custom)
- `team_generation_model`: Model name (gpt-4, claude-3-sonnet, etc.)
- `team_generation_temperature`: Creativity level (0.0-2.0)
- `team_generation_max_tokens`: Maximum response length
- `team_generation_base_url`: Custom API endpoint (for custom providers)
- `team_generation_api_key`: API key for team generation

### 2. Agent AI Configuration (Runtime Execution)

**Purpose**: Controls the AI models and parameters used when agents execute tasks and respond to user inputs.

**Managed By**: Users during agent creation through the creation form

**Used For**:
- Processing user inputs during agent execution
- Generating agent responses
- Performing agent-specific tasks
- Real-time interaction with users

**Configuration Location**:
- Frontend: `/components/features/agent-creation/agent-creation-form.tsx` - "Agent 运行时 AI 配置" section
- Backend: `Agent` model in `app/models/agent.py`
- Database: `agents` table (ai_provider, ai_model, ai_temperature, etc.)

**Available Settings**:
- `ai_provider`: AI provider for runtime execution
- `ai_model`: Model name for agent responses
- `ai_temperature`: Creativity level for agent responses
- `ai_max_tokens`: Maximum response length
- `ai_base_url`: Custom API endpoint
- `ai_custom_provider_name`: Display name for custom providers

## Implementation Flow

### Team Generation Process

```mermaid
graph TD
    A[User submits requirements] --> B[Frontend calls /api/v1/planning/analyze]
    B --> C[Backend reads system AI settings]
    C --> D[AI generates team plan using system config]
    D --> E[Team plan returned to user]
    E --> F[User configures agent runtime AI settings]
    F --> G[Agent created with runtime config]
```

**Code Flow**:
1. User submits description via creation form
2. Frontend calls `api.planning.analyze(description)` - **NO AI config sent**
3. Backend `generate_team()` reads system settings:
   ```python
   # Get system AI settings for team generation
   result = await db.execute(select(SystemSettings).where(
       SystemSettings.key == "ai_team_generation_model"
   ))
   model = model_setting.value if model_setting else "gpt-4"
   ```
4. AI planner uses system configuration to generate team
5. Generated team plan returned to frontend

### Agent Execution Process

```mermaid
graph TD
    A[User tests/executes agent] --> B[Frontend calls /api/v1/agents/{id}/execute]
    B --> C[Backend loads agent with AI config]
    C --> D[ConfigDrivenAgent uses agent's AI settings]
    D --> E[AI processes input with agent config]
    E --> F[Response returned to user]
```

**Code Flow**:
1. User executes agent via testing interface
2. Backend loads agent record with AI configuration
3. `ConfigDrivenAgent` extracts AI config:
   ```python
   self.ai_config = {
       "provider": agent_config.get("ai_provider", "openai"),
       "model": agent_config.get("ai_model", "gpt-4"),
       "temperature": agent_config.get("ai_temperature", 0.7),
       # ... other settings
   }
   ```
4. Agent execution uses `_call_ai_with_agent_config()` method
5. AI service called with agent's specific configuration

## User Interface

### System Settings Page

**Location**: `/settings` (Admin only)

**Section**: "AI团队生成设置"

**Description**: "配置AI自动生成团队的参数。这些设置用于团队生成过程，与Agent运行时的AI配置分开管理。"

**Visual Indicators**:
- Clear section title distinguishing team generation settings
- Explanatory text about separation from runtime config

### Agent Creation Form

**Location**: `/create` - Step 2

**Section**: "Agent 运行时 AI 配置"

**Description**: "这些设置将用于 Agent 执行任务时的 AI 模型配置。团队生成过程使用系统设置中的 AI 配置。"

**Visual Indicators**:
- Blue background section highlighting runtime nature
- "可选" badge indicating optional configuration
- Helpful note: "💡 配置说明：团队生成使用系统设置，Agent运行使用这里的配置"

### Agent Testing Interface

**Location**: `/test`

**Section**: "🤖 Agent 运行时 AI 配置"

**Purpose**: Shows which AI configuration will be used during testing

**Display**: Grid showing provider, model, temperature, max tokens, and custom settings

## Default Behavior

### System Defaults
- Provider: OpenAI
- Model: gpt-4
- Temperature: 0.7
- Max Tokens: 4000

### Agent Defaults
- Provider: openai
- Model: gpt-4
- Temperature: 0.7
- Max Tokens: 2000

### Fallback Logic
1. **Team Generation**: Always uses system settings, falls back to hardcoded defaults if not configured
2. **Agent Execution**: Uses agent's stored config, falls back to defaults if not specified

## Benefits of This Approach

### 1. Administrative Control
- Admins control which AI models are used for team generation
- Centralized management of generation costs and capabilities
- Consistent team generation quality across all users

### 2. User Flexibility
- Users can optimize AI settings for their specific use cases
- Different agents can use different AI configurations
- Runtime behavior can be tuned independently

### 3. Cost Management
- System-level control over expensive generation operations
- User-level control over runtime costs
- Clear separation of generation vs execution costs

### 4. Security & Compliance
- Centralized API key management for team generation
- User-specific configurations for runtime execution
- Clear audit trail of which AI models are used when

## Troubleshooting

### Common Issues

**Problem**: Agent not using custom AI configuration
**Solution**: Verify agent was created with AI config and `ConfigDrivenAgent` is using `_call_ai_with_agent_config()`

**Problem**: Team generation using wrong AI model
**Solution**: Check system settings in admin panel, ensure correct model is configured

**Problem**: AI calls failing during execution
**Solution**: Check agent's AI configuration, verify API keys and endpoints are valid

### Verification Steps

1. **Check System Settings**: Verify team generation AI config in `/settings`
2. **Check Agent Config**: Verify agent's AI settings in agent detail modal
3. **Check Execution Logs**: Look for AI configuration usage in agent execution responses
4. **Test Different Configs**: Create agents with different AI settings to verify separation

## Future Enhancements

### Planned Improvements
1. **Usage Analytics**: Track AI usage by configuration type
2. **Cost Tracking**: Monitor costs for generation vs execution separately
3. **Model Performance**: Compare performance across different configurations
4. **Configuration Templates**: Pre-defined AI configuration templates for common use cases

### Potential Features
1. **Dynamic Configuration**: Allow runtime AI config changes
2. **A/B Testing**: Compare different AI configurations for same agent
3. **Auto-optimization**: Suggest optimal AI settings based on usage patterns
4. **Compliance Modes**: Predefined configurations for regulatory compliance
