### **产品需求文档 (PRD): Agent自动生成服务**

**文档信息**

*   **产品名称:** "元界" (Project Metaverse) - Agent自动生成子服务
*   **文档版本:** 1.0
*   **日期:** 2025年6月27日
*   **作者/负责人:** [您的名字/团队]

---

### **1. 背景与问题陈述 (Background & Problem Statement)**

在我们的对话型虚拟世界应用中，核心用户体验来自于与多样化、富有深度的NPC Agent进行互动。然而，每一个Agent的开发，从角色设定、能力定义到代码实现和部署，都是一个耗时且重复的劳动密集型过程。这极大地限制了我们扩展虚拟世界、增加内容多样性的速度。

我们需要一个内部工具来解决这个瓶颈：一个能够**自动化、规模化、高质量**地生成这些对话型Agent的服务。这个服务的目标是将Agent的开发周期从数天缩短到数分钟，让我们的团队能将精力集中在更高层次的世界观构建和创意设计上。

---

### **2. 目标与成功指标 (Goals & Success Metrics)**

#### **2.1 产品目标**

*   **G1: 提升开发效率:** 极大降低创建新Agent所需的时间和技术门槛。
*   **G2: 保证生成质量:** 确保自动生成的Agent具备合理的角色逻辑、多样的个性和稳定的协作能力。
*   **G3: 实现快速部署:** 允许非核心技术人员也能通过简单的需求描述，快速创建一个可用的Agent API。

#### **2.2 成功指标**

*   **M1: 开发周期 (Time-to-Agent):** 从提交需求到获得可用的Agent API的端到端时间应**小于5分钟**。
*   **M2: 用户满意度 (Developer Satisfaction):** 对目标用户进行调研，衡量他们对生成结果的满意度，目标**满意度 > 85%**。
*   **M3: 系统可靠性 (Reliability):** 服务可用性达到**99.9%**，单个Agent的故障不应影响其他Agent或主应用。

---

### **3. 目标用户 (Target Persona)**

*   **角色:** 大卫，我们虚拟世界项目的一名后端开发者/内容设计师。
*   **技能:** 熟悉业务逻辑和世界观设定，具备基本的API调用能力，但不希望深入复杂的AI Agent编程细节。
*   **痛点:** “我构思了一个很酷的NPC角色——一个由赛博僧侣和愤世嫉俗的侦探组成的二人组，他们共同解决赛博空间的谜题。但要将这个想法变成代码，我需要写大量的Prompt、状态管理和协作逻辑，这太花时间了。”
*   **期望:** “我希望能在一个界面上用自然语言描述我的这个‘僧侣侦探二人组’的想法，然后系统能自动帮我生成一个API。我只需要调用这个API，就能和他们互动了。”

---

### **4. 用户故事与核心流程 (User Story & Core Flow)**

1.  **提交需求:** 大卫访问我们的内部工具平台，打开“Agent生成服务”页面。他看到一个简洁的文本框，提示他“请详细描述您希望这个AI团队为您做什么？”。他输入了他的想法：“我需要一个AI团队来扮演一个二人侦探组合。一个是名叫'Zen'的僧侣，他说话富有哲理，能看透事物的本质；另一个是名叫'Rizzo'的街头老兵，他愤世嫉su，但总能找到关键线索。当用户给他们一个案子时，他们需要通过对话来互相启发，最终给出解决方案。”

2.  **AI规划与用户确认:** 大卫点击“规划团队”按钮。几秒钟后，界面上显示出AI规划师的设计蓝图：
    > **AI已为您规划好团队，请确认：**
    >
    > *   **团队名称:** 禅探二人组 (Zen & Rizzo Investigations)
    > *   **主管 (Orchestrator):** 案件协调员，负责引导对话流程，决定下一步由Zen还是Rizzo发言。
    > *   **专家1 (Zen):** 角色设定为“一位洞察本质的僧侣，发言充满禅意，专注于'为什么'”。
    > *   **专家2 (Rizzo):** 角色设定为“一位经验丰富的老兵，说话直接，专注于'什么'和'哪里'”。

    大卫觉得这个规划很棒，点击了“确认并生成”按钮。

3.  **后台生成与集成:** 系统后台开始工作。它根据蓝图生成了主管和两个专家的Python代码，然后将这个新编译的LangGraph应用动态加载到主FastAPI应用中，并为其分配了一个唯一的ID：`zen-rizzo-001`。

4.  **获取API并使用:** 页面提示“Agent生成成功！”并返回了可用的API信息：
    *   **Agent ID:** `zen-rizzo-001`
    *   **API端点:** `POST /agents/zen-rizzo-001/invoke`
    *   **请求体示例:** `{"input": "城北数据中心的管理员失踪了，这是唯一的线索：一张写着'枯蝉'的纸条。"}`

    大卫立刻使用这个API进行测试，成功地与他刚刚创造的AI侦探二人组展开了对话。

---

### **5. 功能需求详述 (Functional Requirements)**

#### **FR-1: AI规划师 (Planner Agent)**
*   **FR-1.1:** 必须能接收长篇的、非结构化的自然语言作为输入。
*   **FR-1.2:** 其核心Prompt必须经过精心设计，包含明确的角色、思考框架和高质量范例，以引导其进行高质量的团队规划。
*   **FR-1.3:** 输出必须是严格的、定义好的JSON格式，包含`team_name`, `orchestrator_prompt`, 和一个`specialists`列表（每个专家包含`name`和`system_prompt`）。

#### **FR-2: 动态多智能体团队 (Generated Agent Team)**
*   **FR-2.1 (V1范围):** 生成的专家Agent应为纯Prompt驱动，不集成外部工具（如搜索、代码执行）。
*   **FR-2.2:** 团队的协作模式必须是动态的，由一个Orchestrator（主管）Agent在每一步决策，决定下一个发言的专家。
*   **FR-2.3:** 整个团队应被编译成一个独立的、可执行的LangGraph应用对象。

#### **FR-3: 代码生成与集成 (Code Generation & Integration)**
*   **FR-3.1:** 必须使用模板引擎（如Jinja2）根据AI规划师的JSON蓝图生成Python代码。
*   **FR-3.2 (V1范围):** 采用“集成模式”。生成的代码应作为模块，在不重启服务器的情况下，被动态加载到主FastAPI应用的内存中。
*   **FR-3.3:** 主应用需维护一个全局的“Agent注册中心”（如一个Python字典），用于存储和索引所有已加载的Agent应用，键为唯一的`agent_id`。

#### **FR-4: API接口 (API Endpoints)**
*   **FR-4.1:** 提供一个内部管理API `POST /internal/agents` 用于创建新的Agent。该接口应启动后台任务，并立即返回一个任务ID。
*   **FR-4.2:** 提供一个用于轮询创建状态的API `GET /internal/agents/status/{task_id}`。
*   **FR-4.3:** 提供一个统一的、面向用户的公共API `POST /agents/{agent_id}/invoke`，用于与指定ID的Agent进行交互。该API应支持流式响应（Streaming）。

---

### **6. 非功能性需求 (Non-Functional Requirements)**

*   **NFR-1: 可靠性 (Reliability):**
    *   **NFR-1.1 (超时):** 所有对外部AI Provider的API调用必须设置严格的连接、读取和总超时限制（例如，总超时30秒）。
    *   **NFR-1.2 (重试):** 必须实现对瞬时网络错误或API错误的自动重试机制（例如，指数退避，最多3次）。
    *   **NFR-1.3 (异常处理):** 必须有精细的`try...except`块，优雅地处理各类预期和意外的异常，并返回有意义的错误信息给调用者，同时记录详细的错误日志。
*   **NFR-2: 性能 (Performance):**
    *   **NFR-2.1 (P95延迟):** `POST /agents/{agent_id}/invoke` 接口的端到端响应时间（不含外部LLM的思考时间）P95应小于200毫秒。
*   **NFR-3: 安全性 (Security):**
    *   **NFR-3.1:** 严禁将用户输入的任何内容直接或间接用作代码或系统命令执行，防止任何形式的代码注入攻击。所有生成的内容只能作为LLM的Prompt或配置文件。

---

### **7. V1版本范围与排除项 (V1 Scope & Exclusions)**

#### **包含在V1中:**

*   完整的“规划师 -> 用户确认 -> 代码生成 -> 动态集成 -> API调用”核心流程。
*   仅支持纯Prompt驱动的多智能体团队。
*   仅支持“集成模式”部署。

#### **排除在V1之外:**

*   **独立部署模式:** 不提供将Agent部署为独立Docker服务的功能。
*   **Agent的工具使用:** 生成的Agent不能使用搜索、代码执行等外部工具。
*   **长期记忆:** Agent的记忆仅限于当前的单次对话（即通过请求体传入的对话历史），不具备跨会话的持久化记忆。
*   **复杂的UI编辑器:** 用户确认步骤只提供接受或拒绝的选项，不提供对规划蓝图的在线编辑功能。

---

### **8. 未来展望 (Future Roadmap - V2 and beyond)**

*   引入“独立部署（Docker）”模式作为高级选项，以应对高负载和高稳定性的需求。
*   为“规划师Agent”增加工具分配能力，使其能为专家Agent配置搜索、图像生成等工具。
*   集成向量数据库，为Agent团队提供长期记忆和私有知识库查询能力。
*   开发一个更高级的UI界面，允许用户通过拖拽等方式来编辑和设计Agent的协作流程。
