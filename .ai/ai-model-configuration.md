# AI Model Configuration for Agents

## Overview

The Meta-Agent system now supports per-agent AI model configuration, allowing users to specify and modify AI provider settings for each individual agent. This provides flexibility to use different AI models, providers, and parameters for different use cases.

## Features

### Database Schema

Each agent now stores the following AI model configuration fields:

- `ai_provider`: AI provider name (e.g., "openai", "anthropic", "google", "custom")
- `ai_model`: Specific model name (e.g., "gpt-4", "claude-3-sonnet")
- `ai_temperature`: Model temperature (0.0 - 2.0)
- `ai_max_tokens`: Maximum tokens for responses (1 - 32000)
- `ai_base_url`: Custom API base URL (optional)
- `ai_custom_provider_name`: Custom provider display name (optional)

### Agent Creation

When creating agents, users can specify AI model configuration through the creation form:

1. **Frontend Form**: The agent creation form includes AI model selection fields
2. **API Integration**: Configuration is passed to the planning API via `ai_model_config`
3. **Team Plan Generation**: AI planner uses user-specified model settings
4. **Database Persistence**: Configuration is stored with the agent record

### Agent Editing

Users can modify AI model configuration through the agent edit dialog:

1. **Edit Interface**: Dedicated AI configuration section with form controls
2. **Real-time Updates**: Changes are immediately saved to the database
3. **Validation**: Form validation ensures valid parameter ranges
4. **Data Isolation**: Users can only edit their own agents

## API Changes

### Agent Creation Endpoints

#### POST `/api/v1/agents/create`
- Accepts `ai_model_config` in team plan data
- Extracts and persists AI configuration to agent record

#### POST `/api/v1/agents/from-template`
- Supports AI configuration from template data
- Falls back to default values if not specified

### Agent Update Endpoint

#### PUT `/api/v1/agents/{agent_id}`
- Supports updating all AI model configuration fields
- Uses `AgentUpdate` model with optional AI config fields

### Planning API

#### POST `/api/v1/planning/analyze`
- Accepts optional `ai_model_config` parameter
- Passes configuration to AI planner for team generation
- Includes AI config in generated team plan

## Frontend Components

### Agent Creation Form
- AI model selection dropdown
- Temperature slider/input
- Model name input field
- Configuration passed to planning API

### Agent Edit Dialog
- Dedicated AI configuration section
- Form controls for all AI settings:
  - Provider selection (OpenAI, Anthropic, Google, Custom)
  - Model name input
  - Temperature input (0-2 range)
  - Max tokens input (1-32000 range)
  - Custom base URL input
  - Custom provider name input

### Type Definitions
- Updated `Agent` interface with AI config fields
- Updated `AgentUpdate` interface for editing
- Updated `TeamPlan` interface with `ai_model_config`

## Backend Services

### AI Planner Service
- `generate_ai_powered_team()` accepts model/temperature overrides
- Uses user-specified configuration instead of system defaults
- Includes AI config in generated team plans

### Database Models
- `AgentBase` model includes all AI configuration fields
- `AgentUpdate` model supports partial AI config updates
- Proper validation and constraints on all fields

## Migration

### Database Migration
- Added AI configuration columns to `agents` table
- Set default values for existing agents
- Supports both upgrade and downgrade operations

### Backward Compatibility
- Existing agents work with default AI configuration
- System-level AI settings still used as fallbacks
- No breaking changes to existing APIs

## Usage Examples

### Creating Agent with Custom AI Config
```javascript
const teamPlan = {
  team_name: "Custom AI Team",
  description: "Team with custom AI configuration",
  ai_model_config: {
    provider: "anthropic",
    model: "claude-3-sonnet",
    temperature: 0.8,
    max_tokens: 4000
  }
};
```

### Updating Agent AI Configuration
```javascript
const updateData = {
  ai_provider: "openai",
  ai_model: "gpt-4",
  ai_temperature: 0.7,
  ai_max_tokens: 2000,
  ai_base_url: "https://api.openai.com/v1"
};
```

## Security Considerations

- User data isolation: Users can only modify their own agents
- Input validation: All AI config parameters are validated
- API key security: Custom base URLs don't expose API keys
- Authentication required for all AI config operations

## Testing

The implementation includes comprehensive testing:

- Database schema validation
- Agent creation with AI configuration
- Agent update operations
- API endpoint functionality
- Frontend form validation
- Data persistence verification

## Future Enhancements

Potential future improvements:

1. **Provider-specific validation**: Validate model names against provider capabilities
2. **Cost estimation**: Show estimated costs for different model configurations
3. **Performance metrics**: Track performance by AI configuration
4. **Bulk updates**: Allow updating AI config for multiple agents
5. **Configuration templates**: Save and reuse AI configuration presets
