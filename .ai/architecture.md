# Agent自动生成服务 - 系统架构设计

## 1. 整体架构概览

### 1.1 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    用户界面层 (Frontend)                      │
├─────────────────────────────────────────────────────────────┤
│  Next.js + TypeScript + Tailwind CSS + shadcn/ui            │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   Agent创建页面  │ │   Agent管理页面  │ │   Agent测试页面  │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │           状态管理 (Zustand) + API调用层                │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                            HTTP/REST API
                                │
┌─────────────────────────────────────────────────────────────┐
│                    API网关层 (Backend)                       │
├─────────────────────────────────────────────────────────────┤
│                   FastAPI + SQLModel                        │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   内部管理API    │ │   公共调用API    │ │   状态查询API    │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    核心业务层                                │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   AI规划师       │ │   代码生成器     │ │   Agent注册中心  │ │
│  │   (Planner)     │ │  (CodeGen)      │ │   (Registry)    │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              动态Agent加载与执行引擎                     │ │
│  │                  (LangGraph Runtime)                   │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    数据存储层                                │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   SQLite/PG     │ │   内存缓存       │ │   文件存储       │ │
│  │   (元数据)       │ │  (Agent实例)     │ │  (生成代码)      │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    外部服务层                                │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   OpenAI API    │ │   Claude API    │ │   其他LLM API    │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 2. 技术栈选择

### 2.1 前端技术栈
- **框架**: Next.js 14 (App Router)
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **组件库**: shadcn/ui
- **状态管理**: Zustand
- **表单处理**: React Hook Form
- **HTTP客户端**: Axios
- **Mock服务**: MSW (Mock Service Worker)

### 2.2 后端技术栈
- **框架**: FastAPI
- **ORM**: SQLModel
- **数据库**: SQLite (开发) / PostgreSQL (生产)
- **AI框架**: LangGraph
- **LLM SDK**: OpenAI SDK, Anthropic SDK
- **模板引擎**: Jinja2
- **异步任务**: asyncio + 内存队列
- **日志**: Python logging + structlog

## 3. 项目目录结构

```
meta-agent/
├── .ai/                          # 技术文档和设计
│   ├── PRD.md
│   ├── tasks.md
│   ├── architecture.md
│   └── api-spec.md
├── frontend/                     # Next.js前端项目
│   ├── src/
│   │   ├── app/                  # App Router页面
│   │   ├── components/           # React组件
│   │   │   ├── ui/              # shadcn/ui组件
│   │   │   ├── forms/           # 表单组件
│   │   │   ├── layout/          # 布局组件
│   │   │   └── features/        # 功能组件
│   │   ├── lib/                 # 工具库
│   │   │   ├── api.ts           # API调用
│   │   │   ├── store.ts         # Zustand状态
│   │   │   ├── types.ts         # TypeScript类型
│   │   │   └── utils.ts         # 工具函数
│   │   ├── hooks/               # 自定义Hooks
│   │   └── mocks/               # Mock数据
│   ├── public/
│   ├── package.json
│   ├── tailwind.config.js
│   ├── next.config.js
│   └── tsconfig.json
├── backend/                      # FastAPI后端项目
│   ├── app/
│   │   ├── main.py              # FastAPI应用入口
│   │   ├── config.py            # 配置管理
│   │   ├── models/              # SQLModel数据模型
│   │   ├── api/                 # API路由
│   │   │   ├── internal/        # 内部管理API
│   │   │   └── public/          # 公共调用API
│   │   ├── core/                # 核心业务逻辑
│   │   │   ├── planner.py       # AI规划师
│   │   │   ├── codegen.py       # 代码生成器
│   │   │   ├── registry.py      # Agent注册中心
│   │   │   └── executor.py      # Agent执行引擎
│   │   ├── templates/           # Jinja2模板
│   │   ├── utils/               # 工具函数
│   │   └── tests/               # 测试文件
│   ├── requirements.txt
│   ├── alembic.ini
│   └── Dockerfile
├── docs/                         # 项目文档
├── scripts/                      # 部署和工具脚本
├── docker-compose.yml
├── .gitignore
└── README.md
```

## 4. 核心组件设计

### 4.1 AI规划师 (Planner Agent)
- **职责**: 解析用户需求，生成团队规划JSON
- **输入**: 自然语言描述
- **输出**: 标准化的TeamPlan JSON
- **技术**: LangGraph + OpenAI/Claude API

### 4.2 代码生成器 (Code Generator)
- **职责**: 根据TeamPlan生成LangGraph代码
- **输入**: TeamPlan JSON
- **输出**: 可执行的Python代码
- **技术**: Jinja2模板引擎

### 4.3 Agent注册中心 (Registry)
- **职责**: 管理动态生成的Agent实例
- **功能**: 注册、查询、删除Agent
- **存储**: 内存字典 + 数据库持久化

### 4.4 动态执行引擎 (Executor)
- **职责**: 动态编译和执行生成的Agent代码
- **安全**: 代码沙箱和安全检查
- **性能**: 内存缓存和懒加载

## 5. 数据流设计

### 5.1 Agent创建流程
```
用户输入 → AI规划师 → 用户确认 → 代码生成 → 动态加载 → 注册Agent → 返回API信息
```

### 5.2 Agent调用流程
```
API请求 → 路由解析 → Registry查询 → Agent执行 → 流式响应 → 客户端接收
```

## 6. 安全考虑

### 6.1 代码注入防护
- 严格的输入验证
- 模板渲染安全检查
- 代码执行沙箱

### 6.2 API安全
- 请求频率限制
- 输入参数验证
- 错误信息脱敏

## 7. 性能优化

### 7.1 前端优化
- 组件懒加载
- API请求缓存
- 虚拟滚动

### 7.2 后端优化
- Agent实例缓存
- 异步处理
- 连接池管理

## 8. 监控和日志

### 8.1 关键指标
- API响应时间
- Agent创建成功率
- 系统资源使用率

### 8.2 日志策略
- 结构化日志
- 错误追踪
- 性能监控
