# Two-Factor Authentication (2FA) Integration Guide

## Overview

This document describes the comprehensive 2FA integration implemented in the Meta-Agent system, including login flow integration and sensitive operation protection.

## Features Implemented

### 1. Login Flow Integration

- **Enhanced Login Process**: Modified login flow to detect 2FA-enabled users and require verification
- **Seamless UX**: Users with 2FA enabled see a verification dialog after password validation
- **Multiple Verification Methods**: Support for both TOTP codes and backup codes
- **Session Management**: Temporary sessions for 2FA verification flow

### 2. Sensitive Operation Protection

The following operations now require 2FA verification for users who have 2FA enabled:

- **Password Changes**: Requires 2FA verification before allowing password modification
- **2FA Disable**: Requires 2FA verification before disabling two-factor authentication
- **Email Changes**: Requires 2FA verification before changing email address
- **Account Deletion**: Requires 2FA verification before account deletion
- **System Settings**: Requires 2FA verification for critical system configuration changes

### 3. User Experience Enhancements

- **Clear Error Messages**: Intelligent error handling with specific guidance
- **Status Indicators**: Visual indicators showing 2FA status
- **Confirmation Dialogs**: Clear confirmation steps for destructive operations
- **Backup Code Support**: Full support for backup codes as TOTP alternative

## Technical Implementation

### Backend Components

#### 1. Enhanced Login Endpoints

```python
# Modified login endpoint
@router.post("/login", response_model=Union[LoginResponse, LoginStepResponse])
async def login_user(...)

# New 2FA verification endpoint
@router.post("/verify-2fa", response_model=LoginResponse)
async def verify_2fa_login(...)
```

#### 2. 2FA Middleware

```python
# Two-factor authentication dependency
def create_two_factor_dependency(operation_name: str = "this operation"):
    async def two_factor_required(...) -> User:
        # Verify 2FA if user has it enabled
        # Allow operation if 2FA is not enabled
```

#### 3. Protected Endpoints

```python
# Example: Password change with 2FA protection
@router.put("/change-password")
async def change_user_password(
    password_data: ChangePassword,
    totp_code: Optional[str] = None,
    backup_code: Optional[str] = None,
    current_user: User = Depends(RequireTwoFactorForPasswordChange),
    ...
)
```

### Frontend Components

#### 1. Enhanced Login Form

- Integrated 2FA verification dialog
- Automatic detection of 2FA requirement
- Seamless transition from password to 2FA verification

#### 2. Sensitive Operation Hook

```typescript
const passwordChangeOperation = useSensitiveOperation(
  async (data) => {
    return await changePassword({
      ...data,
      totp_code: data.totp_code,
      backup_code: data.backup_code,
    });
  },
  {
    operationName: 'change your password',
    requiresConfirmation: true,
    onSuccess: (result) => { /* handle success */ },
    onError: (error) => { /* handle error */ },
  }
);
```

#### 3. Reusable Components

- `TwoFactorVerificationDialog`: Universal 2FA verification dialog
- `SensitiveOperationDialog`: Confirmation dialog for sensitive operations
- `TwoFactorStatusIndicator`: Visual status indicator
- `TwoFactorErrorHandler`: Intelligent error handling with suggestions

## Usage Examples

### 1. Adding 2FA Protection to New Operations

```typescript
// 1. Create sensitive operation hook
const myOperation = useSensitiveOperation(
  async (data) => {
    return await apiClient.myProtectedOperation({
      ...data,
      totp_code: data.totp_code,
      backup_code: data.backup_code,
    });
  },
  {
    operationName: 'perform sensitive action',
    requiresConfirmation: true,
    onSuccess: (result) => { /* success handling */ },
    onError: (error) => { /* error handling */ },
  }
);

// 2. Add backend dependency
@router.post("/my-protected-operation")
async def my_protected_operation(
    data: MyOperationData,
    current_user: User = Depends(create_two_factor_dependency("my operation")),
    ...
)

// 3. Add dialogs to component
<SensitiveOperationDialog
  open={myOperation.showConfirmation}
  onConfirm={myOperation.confirmOperation}
  onCancel={myOperation.cancelOperation}
  // ... other props
/>

<TwoFactorVerificationDialog
  open={myOperation.twoFactorVerification.state.isOpen}
  onVerify={myOperation.twoFactorVerification.actions.verify}
  // ... other props
/>
```

### 2. Backend API Integration

```python
# Add to your endpoint
from app.api.dependencies.two_factor import create_two_factor_dependency

RequireTwoFactorForMyOperation = create_two_factor_dependency("my operation")

@router.post("/my-endpoint")
async def my_endpoint(
    data: MyData,
    totp_code: Optional[str] = None,
    backup_code: Optional[str] = None,
    current_user: User = Depends(RequireTwoFactorForMyOperation),
    ...
):
    # Your operation logic here
    pass
```

## Testing

### Test Page

A comprehensive test page is available at `/test-2fa` that demonstrates:

- 2FA status indicators
- Sensitive operation flows
- Error handling scenarios
- Confirmation dialogs

### Manual Testing Steps

1. **Login Flow Testing**:
   - Login with 2FA-disabled user (should complete normally)
   - Enable 2FA for user
   - Login with 2FA-enabled user (should show verification dialog)
   - Test with correct/incorrect TOTP codes
   - Test with backup codes

2. **Sensitive Operation Testing**:
   - Test password change with 2FA disabled (should work normally)
   - Test password change with 2FA enabled (should require verification)
   - Test with invalid verification codes
   - Test confirmation dialogs

3. **Error Handling Testing**:
   - Test with expired codes
   - Test with invalid backup codes
   - Test rate limiting scenarios

## Security Considerations

1. **Temporary Sessions**: 2FA verification sessions expire after 10 minutes
2. **Backup Code Usage**: Each backup code can only be used once
3. **Rate Limiting**: Protection against brute force attacks
4. **Secure Storage**: All sensitive data is properly encrypted
5. **Session Invalidation**: Other sessions are invalidated on password change

## Future Enhancements

1. **Remember Device**: Option to remember trusted devices
2. **SMS Backup**: SMS-based backup verification method
3. **Hardware Keys**: Support for FIDO2/WebAuthn hardware keys
4. **Risk-Based Authentication**: Adaptive authentication based on risk factors
5. **Audit Logging**: Enhanced logging for 2FA events

## API Reference

### Authentication Endpoints

#### POST /api/v1/auth/login
Enhanced login endpoint that handles 2FA flow.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "remember_me": false
}
```

**Response (No 2FA):**
```json
{
  "user": { /* user object */ },
  "tokens": { /* auth tokens */ },
  "session_id": "session-uuid"
}
```

**Response (2FA Required):**
```json
{
  "requires_2fa": true,
  "user_id": "temp-session-id",
  "message": "Two-factor authentication required"
}
```

#### POST /api/v1/auth/verify-2fa
Complete login with 2FA verification.

**Request:**
```json
{
  "temp_session_id": "temp-session-id",
  "totp_code": "123456",  // Optional
  "backup_code": "ABCD1234",  // Optional
  "remember_me": false
}
```

**Response:**
```json
{
  "user": { /* user object */ },
  "tokens": { /* auth tokens */ },
  "session_id": "session-uuid"
}
```

### Protected Endpoints

#### PUT /api/v1/auth/change-password
Change user password with 2FA protection.

**Request:**
```json
{
  "current_password": "oldpass",
  "new_password": "newpass",
  "confirm_password": "newpass",
  "totp_code": "123456",  // Required if 2FA enabled
  "backup_code": "ABCD1234"  // Alternative to totp_code
}
```

### 2FA Management Endpoints

#### GET /api/v1/2fa/status
Get current 2FA status.

#### POST /api/v1/2fa/setup
Setup 2FA (requires password).

#### POST /api/v1/2fa/enable
Enable 2FA (requires TOTP verification).

#### POST /api/v1/2fa/disable
Disable 2FA (requires password + 2FA verification).

## Troubleshooting

### Common Issues

1. **"Invalid verification code"**: Check device time synchronization
2. **"Code expired"**: Generate fresh code from authenticator app
3. **"Backup code invalid"**: Ensure code hasn't been used before
4. **"Too many attempts"**: Wait before retrying

### Support Resources

- Account Settings: `/account` (Security tab)
- Test Page: `/test-2fa`
- Setup Guide: Available in 2FA settings dialog
