# Agent自动生成服务 - API接口规范

## 1. API概览

### 1.1 基础信息
- **Base URL**: `http://localhost:8000` (开发环境)
- **Content-Type**: `application/json`
- **认证方式**: 暂无 (V1版本内部使用)

### 1.2 响应格式
所有API响应都遵循统一格式：
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2025-06-30T10:00:00Z"
}
```

错误响应格式：
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "输入参数验证失败",
    "details": {}
  },
  "timestamp": "2025-06-30T10:00:00Z"
}
```

## 2. 内部管理API

### 2.1 创建Agent
**POST** `/internal/agents`

创建新的Agent团队，启动异步生成任务。

**请求体**:
```json
{
  "description": "我需要一个AI团队来扮演一个二人侦探组合...",
  "user_id": "user_123",
  "options": {
    "model": "gpt-4",
    "temperature": 0.7
  }
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "task_id": "task_abc123",
    "status": "pending",
    "estimated_time": 180
  },
  "message": "Agent创建任务已启动"
}
```

### 2.2 查询任务状态
**GET** `/internal/agents/status/{task_id}`

查询Agent创建任务的状态和进度。

**路径参数**:
- `task_id`: 任务ID

**响应**:
```json
{
  "success": true,
  "data": {
    "task_id": "task_abc123",
    "status": "completed",
    "progress": 100,
    "result": {
      "agent_id": "zen-rizzo-001",
      "team_plan": {
        "team_name": "禅探二人组",
        "orchestrator_prompt": "...",
        "specialists": [...]
      },
      "api_endpoint": "/agents/zen-rizzo-001/invoke"
    },
    "created_at": "2025-06-30T10:00:00Z",
    "completed_at": "2025-06-30T10:03:00Z"
  }
}
```

状态值说明：
- `pending`: 等待处理
- `planning`: AI规划中
- `generating`: 代码生成中
- `loading`: 动态加载中
- `completed`: 完成
- `failed`: 失败

### 2.3 获取Agent列表
**GET** `/internal/agents`

获取所有已创建的Agent列表。

**查询参数**:
- `page`: 页码 (默认: 1)
- `size`: 每页数量 (默认: 20)
- `status`: 状态筛选 (可选)

**响应**:
```json
{
  "success": true,
  "data": {
    "agents": [
      {
        "agent_id": "zen-rizzo-001",
        "team_name": "禅探二人组",
        "status": "active",
        "created_at": "2025-06-30T10:00:00Z",
        "last_used": "2025-06-30T11:30:00Z",
        "usage_count": 15
      }
    ],
    "total": 1,
    "page": 1,
    "size": 20
  }
}
```

### 2.4 删除Agent
**DELETE** `/internal/agents/{agent_id}`

删除指定的Agent。

**路径参数**:
- `agent_id`: Agent ID

**响应**:
```json
{
  "success": true,
  "message": "Agent已成功删除"
}
```

## 3. 公共调用API

### 3.1 调用Agent
**POST** `/agents/{agent_id}/execute`

与指定Agent进行对话交互。

**路径参数**:
- `agent_id`: Agent ID

**请求体**:
```json
{
  "input": "城北数据中心的管理员失踪了，这是唯一的线索：一张写着'枯蝉'的纸条。",
  "conversation_id": "conv_123",
  "stream": true,
  "options": {
    "max_tokens": 1000,
    "temperature": 0.7
  }
}
```

**响应** (非流式):
```json
{
  "success": true,
  "data": {
    "response": "Zen: 枯蝉...这个词让我想到了生命的轮回...\nRizzo: 等等，枯蝉可能是个代号或者地点...",
    "conversation_id": "conv_123",
    "agent_id": "zen-rizzo-001",
    "usage": {
      "input_tokens": 50,
      "output_tokens": 120,
      "total_tokens": 170
    }
  }
}
```

**流式响应** (stream=true):
```
data: {"type": "start", "agent": "orchestrator"}

data: {"type": "agent_response", "agent": "zen", "content": "枯蝉...这个词让我想到了生命的轮回..."}

data: {"type": "agent_response", "agent": "rizzo", "content": "等等，枯蝉可能是个代号或者地点..."}

data: {"type": "end", "conversation_id": "conv_123"}
```

### 3.2 获取Agent信息
**GET** `/agents/{agent_id}/info`

获取Agent的基本信息和能力描述。

**路径参数**:
- `agent_id`: Agent ID

**响应**:
```json
{
  "success": true,
  "data": {
    "agent_id": "zen-rizzo-001",
    "team_name": "禅探二人组",
    "description": "一个由禅意僧侣和街头老兵组成的侦探二人组",
    "specialists": [
      {
        "name": "Zen",
        "role": "哲学思考者",
        "description": "洞察本质的僧侣，专注于'为什么'"
      },
      {
        "name": "Rizzo", 
        "role": "实用主义者",
        "description": "经验丰富的老兵，专注于'什么'和'哪里'"
      }
    ],
    "created_at": "2025-06-30T10:00:00Z",
    "status": "active"
  }
}
```

## 4. 数据模型

### 4.1 TeamPlan模型
```typescript
interface TeamPlan {
  team_name: string;
  orchestrator_prompt: string;
  specialists: Specialist[];
}

interface Specialist {
  name: string;
  system_prompt: string;
  role?: string;
  description?: string;
}
```

### 4.2 Agent模型
```typescript
interface Agent {
  agent_id: string;
  team_name: string;
  team_plan: TeamPlan;
  status: 'active' | 'inactive' | 'error';
  created_at: string;
  last_used?: string;
  usage_count: number;
}
```

### 4.3 Task模型
```typescript
interface Task {
  task_id: string;
  status: 'pending' | 'planning' | 'generating' | 'loading' | 'completed' | 'failed';
  progress: number;
  description: string;
  result?: {
    agent_id: string;
    team_plan: TeamPlan;
    api_endpoint: string;
  };
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  created_at: string;
  completed_at?: string;
}
```

## 5. 错误码定义

| 错误码 | HTTP状态码 | 描述 |
|--------|------------|------|
| VALIDATION_ERROR | 400 | 请求参数验证失败 |
| AGENT_NOT_FOUND | 404 | Agent不存在 |
| TASK_NOT_FOUND | 404 | 任务不存在 |
| AGENT_CREATION_FAILED | 500 | Agent创建失败 |
| AGENT_EXECUTION_ERROR | 500 | Agent执行错误 |
| EXTERNAL_API_ERROR | 502 | 外部API调用失败 |
| RATE_LIMIT_EXCEEDED | 429 | 请求频率超限 |
| INTERNAL_SERVER_ERROR | 500 | 服务器内部错误 |

## 6. 限制和约束

### 6.1 请求限制
- 单次请求最大大小: 10MB
- Agent描述最大长度: 5000字符
- 并发Agent创建数: 5个
- 单个Agent最大调用频率: 100次/分钟

### 6.2 响应限制
- Agent响应最大长度: 10000字符
- 流式响应超时: 30秒
- 任务状态保留时间: 24小时
