# 模板库系统重新设计方案

## 核心概念转变

### 当前问题
- 模板只是"半成品"，需要AI进一步生成才能使用
- team_structure_template 缺乏完整的团队定义
- 从模板创建Agent时仍需要AI生成步骤
- 模板预览无法显示具体的团队成员和工作流程

### 新设计理念
**模板 = 完整的、可直接部署的Agent Team定义**

每个模板应该包含：
1. **完整的团队成员定义**
   - 每个成员的详细system_prompt
   - 明确的capabilities和tools
   - 具体的角色描述和职责

2. **预定义的工作流程**
   - 具体的执行步骤
   - 成员间的协作方式
   - 输入输出规范

3. **可直接使用的配置**
   - 模型参数设置
   - 温度、token限制等
   - 环境配置

## 数据结构重新设计

### 完整的team_structure_template结构
```json
{
  "team_name": "侦探二人组",
  "description": "专业侦探团队，解决复杂案件",
  "objective": "通过深度分析和实地调查解决复杂案件",
  "domain": "investigation",
  "complexity": "intermediate",
  
  "team_members": [
    {
      "name": "禅意僧侣",
      "role": "analyst",
      "description": "冷静理性的分析师，擅长逻辑推理",
      "system_prompt": "你是一位禅意僧侣，拥有深邃的智慧...",
      "capabilities": ["逻辑推理", "深度分析", "模式识别"],
      "tools": ["思维导图", "逻辑分析框架"],
      "model_config": {
        "model": "gpt-4",
        "temperature": 0.3,
        "max_tokens": 2000
      }
    },
    {
      "name": "街头老兵",
      "role": "investigator", 
      "description": "经验丰富的实地调查员",
      "system_prompt": "你是一位经验丰富的街头老兵...",
      "capabilities": ["实地调查", "人际交往", "信息收集"],
      "tools": ["调查问卷", "访谈技巧"],
      "model_config": {
        "model": "gpt-4",
        "temperature": 0.7,
        "max_tokens": 1500
      }
    }
  ],
  
  "workflow": {
    "steps": [
      {
        "name": "案件分析",
        "description": "深度分析案件背景和关键信息",
        "assignee": "禅意僧侣",
        "inputs": ["案件描述", "相关资料"],
        "outputs": ["分析报告", "调查方向"],
        "dependencies": []
      },
      {
        "name": "实地调查",
        "description": "根据分析结果进行实地调查",
        "assignee": "街头老兵", 
        "inputs": ["分析报告", "调查方向"],
        "outputs": ["调查结果", "证据收集"],
        "dependencies": ["案件分析"]
      },
      {
        "name": "综合推理",
        "description": "结合分析和调查结果得出结论",
        "assignee": "禅意僧侣",
        "inputs": ["调查结果", "证据收集"],
        "outputs": ["最终结论", "解决方案"],
        "dependencies": ["实地调查"]
      }
    ],
    "coordination": {
      "orchestrator": "禅意僧侣",
      "communication_style": "协作式对话",
      "decision_making": "共识决策"
    }
  },
  
  "configuration": {
    "execution_mode": "sequential",
    "timeout_per_step": 300,
    "max_iterations": 3,
    "error_handling": "graceful_degradation"
  },
  
  "metadata": {
    "version": "1.0.0",
    "created_by": "system",
    "last_updated": "2025-01-06",
    "performance_metrics": {
      "avg_execution_time": "5-10分钟",
      "success_rate": "85%",
      "user_satisfaction": 4.5
    }
  }
}
```

## 工作流程重新设计

### 1. 从Agent创建模板
```
现有Agent → 提取完整team_plan → 保存为模板
```
- 保存Agent的完整team_plan配置
- 包含所有成员的system_prompt和配置
- 保留工作流程定义

### 2. 从模板创建Agent
```
选择模板 → 直接使用配置 → 部署Agent
```
- 无需AI生成步骤
- 直接使用模板的完整配置
- 可选的自定义参数调整

### 3. 模板部署
```
模板配置 → dynamic_loader → 可执行Agent
```
- dynamic_loader直接从模板配置创建Agent实例
- 无需代码生成步骤
- 基于配置的运行时Agent

## 实现计划

1. **修改数据结构** - 确保team_structure_template包含完整定义
2. **重构创建流程** - 从Agent保存完整配置到模板
3. **重构使用流程** - 从模板直接创建可用Agent
4. **更新dynamic_loader** - 支持基于配置的Agent部署
5. **创建完整模板** - 提供真正可用的模板示例
6. **更新API和前端** - 显示和使用完整的模板信息
7. **添加验证机制** - 确保模板完整性
8. **端到端测试** - 验证整个工作流程

## 预期效果

- 用户可以直接预览模板的具体团队成员和工作流程
- 从模板创建的Agent可以立即使用，无需等待AI生成
- 模板真正成为"开箱即用"的解决方案
- 提高系统的可预测性和可靠性
