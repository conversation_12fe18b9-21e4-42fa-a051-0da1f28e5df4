# Meta-Agent 端口配置总结

## 🚀 当前服务配置

### 前端服务
- **URL**: http://localhost:3001
- **端口**: 3001 (自动分配，因为 3000 被占用)
- **框架**: Next.js 15.3.4
- **状态**: ✅ 正常运行

### 后端服务  
- **URL**: http://localhost:8000
- **端口**: 8000 (标准配置)
- **框架**: FastAPI + Uvicorn
- **状态**: ✅ 正常运行

## 📋 配置文件

### 前端环境配置 (`frontend/.env.local`)
```env
# API 配置
NEXT_PUBLIC_API_URL=http://localhost:8000

# 其他配置
NEXT_PUBLIC_APP_NAME=Meta-Agent
NEXT_PUBLIC_APP_VERSION=1.0.0
```

### 后端启动配置
```bash
# 标准启动命令
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 开发环境启动
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

## 🔧 端口使用情况

### 当前占用端口
```bash
# 检查端口使用情况
ss -tlnp | grep :800

# 结果显示
LISTEN 0 2048 127.0.0.1:8000 0.0.0.0:* users:(("python",pid=56265,fd=3),("uvicorn",pid=53516,fd=3))
```

### 端口分配策略
- **8000**: 后端 API 服务 (FastAPI)
- **3000/3001**: 前端开发服务器 (Next.js)
- **5432**: PostgreSQL (如果使用)
- **6379**: Redis (如果使用)

## 🌐 API 端点访问

### 后端 API 文档
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

### 主要 API 端点
```
# 认证相关
GET  /api/v1/auth/me
POST /api/v1/auth/login
POST /api/v1/auth/logout

# 日志相关
GET  /api/v1/logs
GET  /api/v1/logs/{log_id}
GET  /api/v1/logs/export
GET  /api/v1/logs/stats

# Agent 相关
GET  /api/v1/agents
POST /api/v1/agents
GET  /api/v1/agents/{agent_id}

# 测试历史
GET  /api/v1/test-history
POST /api/v1/test-history
```

## 🔄 服务管理

### 启动服务
```bash
# 启动后端 (在 backend 目录)
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 启动前端 (在 frontend 目录)
npm run dev
```

### 停止服务
```bash
# 停止后端
pkill -f "uvicorn.*8000"

# 停止前端
pkill -f "next.*dev"
```

### 检查服务状态
```bash
# 检查进程
ps aux | grep uvicorn
ps aux | grep next

# 检查端口
ss -tlnp | grep :8000
ss -tlnp | grep :3000
```

## 🛠️ 开发环境配置

### 环境变量
```env
# 开发环境
NODE_ENV=development
NEXT_PUBLIC_API_URL=http://localhost:8000

# 生产环境
NODE_ENV=production
NEXT_PUBLIC_API_URL=https://api.meta-agent.com
```

### 代理配置 (如需要)
```javascript
// next.config.js
module.exports = {
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: 'http://localhost:8000/api/:path*',
      },
    ]
  },
}
```

## 🔒 安全配置

### CORS 设置
```python
# backend/app/main.py
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3001"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

### 防火墙配置 (生产环境)
```bash
# 允许必要端口
ufw allow 8000/tcp  # API 服务
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
```

## 📊 监控和日志

### 服务监控
```bash
# 检查服务健康状态
curl -I http://localhost:8000/docs
curl -I http://localhost:3001

# 查看服务日志
tail -f logs/uvicorn.log
```

### 性能监控
```bash
# 检查资源使用
top -p $(pgrep -f uvicorn)
top -p $(pgrep -f next)
```

## 🚀 部署配置

### Docker 配置 (可选)
```dockerfile
# 后端 Dockerfile
FROM python:3.11
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 8000
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]

# 前端 Dockerfile
FROM node:18
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
EXPOSE 3000
CMD ["npm", "run", "dev"]
```

### Docker Compose
```yaml
version: '3.8'
services:
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=sqlite:///./meta_agent.db
  
  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
    depends_on:
      - backend
```

## ✅ 配置验证

### 连接测试
```bash
# 测试后端 API
curl http://localhost:8000/docs

# 测试前端页面
curl http://localhost:3001

# 测试 API 连接
curl http://localhost:8000/api/v1/logs
```

### 功能测试
- ✅ 前端可以访问后端 API
- ✅ 日志页面正常加载
- ✅ API 文档可以访问
- ✅ 跨域请求正常工作

## 🎯 总结

当前配置状态：
- **后端**: 端口 8000 ✅
- **前端**: 端口 3001 ✅  
- **API 连接**: 正常 ✅
- **日志功能**: 完全可用 ✅

所有服务都在正确的端口上运行，前后端通信正常，日志功能完全可用。
