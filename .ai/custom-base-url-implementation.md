# Custom Base URL Implementation for AI Service Providers

## Overview

This document describes the implementation of custom base URL configuration for AI service providers in the Meta-Agent system. This feature enables users to route AI API calls through corporate proxies, use alternative API endpoints, or connect to self-hosted AI service instances while maintaining the existing provider abstraction.

## Implementation Summary

### 1. Database Schema Updates ✅

**Files Modified:**
- `backend/app/models/settings.py`
- `backend/alembic/versions/001_add_base_url_to_api_keys.py`

**Changes:**
- Added `base_url` field to `APIKeyBase` model (optional, max 500 chars)
- Updated database migration to add the column
- Maintained backward compatibility with existing configurations

### 2. Backend API Provider Configuration Models ✅

**Files Modified:**
- `backend/app/models/settings.py`

**Changes:**
- Added `base_url` field to `APIKeyCreate` and `APIKeyUpdate` models
- Implemented Pydantic validators for URL format validation
- Added utility functions:
  - `validate_base_url()` - Enhanced URL validation with security checks
  - `get_default_base_url()` - Get default URLs for each provider
  - `is_common_proxy_pattern()` - Detect common proxy patterns
  - `get_provider_examples()` - Get example URLs for different configurations

### 3. Backend AI Service Client Updates ✅

**Files Modified:**
- `backend/app/services/ai_providers.py`

**Changes:**
- Modified `OpenAIProvider` and `AnthropicProvider` to accept `base_url` parameter
- Updated client initialization to use custom base URLs when provided
- Added `create_provider_with_config()` method to `AIProviderManager` for dynamic provider creation with custom configurations

### 4. Backend API Endpoints Enhancement ✅

**Files Modified:**
- `backend/app/api/v1/endpoints/api_keys.py`

**Changes:**
- Updated API key creation endpoint to handle `base_url` field
- Enhanced API key testing endpoint to validate custom base URLs
- Added proper error handling and validation feedback

### 5. Frontend UI Components ✅

**Files Modified:**
- `frontend/src/app/api-keys/page.tsx`

**Changes:**
- Added base URL input field to API key creation form
- Implemented dynamic placeholder text showing default URLs for each provider
- Added helpful description text explaining the feature
- Updated form state management to include `base_url`

### 6. Frontend API Integration ✅

**Files Modified:**
- `frontend/src/lib/api-client-settings.ts`

**Changes:**
- Updated `createApiKey` and `updateApiKey` functions to support `base_url` parameter
- Maintained backward compatibility with existing API calls

### 7. Configuration Management & Validation ✅

**Features Implemented:**
- Enhanced URL validation with security checks
- Support for common proxy patterns (Azure OpenAI, corporate proxies, API gateways)
- Backward compatibility with existing configurations
- Default URL examples for each provider

### 8. Comprehensive Testing ✅

**Test Files Created:**
- `backend/tests/unit/models/test_settings_base_url.py` - Model validation tests
- `backend/tests/unit/services/test_ai_providers_base_url.py` - Provider functionality tests
- `backend/tests/integration/test_api_keys_base_url.py` - API endpoint integration tests

**Test Coverage:**
- URL validation edge cases
- Provider initialization with custom URLs
- API endpoint functionality
- Error handling scenarios

## Usage Examples

### 1. Default Configuration (No Custom Base URL)
```json
{
  "name": "OpenAI Default",
  "provider": "openai",
  "key": "sk-...",
  "base_url": null
}
```
Uses: `https://api.openai.com/v1`

### 2. Azure OpenAI Configuration
```json
{
  "name": "Azure OpenAI",
  "provider": "azure",
  "key": "azure-key",
  "base_url": "https://myresource.openai.azure.com"
}
```

### 3. Corporate Proxy Configuration
```json
{
  "name": "OpenAI via Proxy",
  "provider": "openai",
  "key": "sk-...",
  "base_url": "https://proxy.company.com/openai/v1"
}
```

### 4. Self-hosted/Alternative Gateway
```json
{
  "name": "Custom Gateway",
  "provider": "openai",
  "key": "custom-key",
  "base_url": "https://my-gateway.internal.com/api/v1"
}
```

## Security Considerations

1. **URL Validation**: Comprehensive validation prevents malicious URLs
2. **Protocol Restrictions**: Only HTTP/HTTPS protocols allowed
3. **Private IP Handling**: Configurable restrictions on private IP addresses
4. **Input Sanitization**: Proper escaping and validation of user inputs

## Backward Compatibility

- Existing API keys without `base_url` continue to work with default endpoints
- All existing API endpoints remain functional
- Database migration handles existing records gracefully
- Frontend gracefully handles missing `base_url` fields

## Provider Support

| Provider | Default Base URL | Custom URL Support | Notes |
|----------|------------------|-------------------|-------|
| OpenAI | `https://api.openai.com/v1` | ✅ | Supports Azure OpenAI endpoints |
| Anthropic | `https://api.anthropic.com` | ✅ | Standard proxy support |
| Google | `https://generativelanguage.googleapis.com/v1` | ✅ | Gemini API support |
| Azure | None (requires custom) | ✅ | Must specify Azure endpoint |
| Custom | None (requires custom) | ✅ | Fully configurable |

## Future Enhancements

1. **Health Check Integration**: Real-time validation of custom endpoints
2. **Proxy Authentication**: Support for authenticated proxies
3. **Load Balancing**: Multiple endpoint support for high availability
4. **Monitoring**: Custom endpoint performance tracking
5. **Template System**: Pre-configured templates for common proxy setups

## Testing

Run the test suite to verify functionality:

```bash
# Backend tests
cd backend
source venv/bin/activate
pytest tests/unit/models/test_settings_base_url.py -v
pytest tests/unit/services/test_ai_providers_base_url.py -v
pytest tests/integration/test_api_keys_base_url.py -v

# Frontend tests (if implemented)
cd frontend
npm test
```

## Deployment Notes

1. Run database migration: `alembic upgrade head`
2. Update environment variables if needed
3. Test with a sample custom base URL configuration
4. Monitor logs for any validation errors

This implementation provides a robust, secure, and user-friendly way to configure custom base URLs for AI service providers while maintaining full backward compatibility with existing configurations.
