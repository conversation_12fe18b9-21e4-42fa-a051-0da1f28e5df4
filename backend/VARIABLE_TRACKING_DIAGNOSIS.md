# 变量跟踪数据库存储问题诊断和修复

## 问题分析

经过深入分析，我发现了变量信息没有保存到数据库的根本原因：

### 1. 调用链分离问题

**问题**: 变量跟踪和数据库更新逻辑在两个独立的调用路径中：

**路径1（变量跟踪）**:
```
ConfigDrivenAgent._track_step_variables 
→ variable_tracker.track_variable_resolution 
→ _store_variable_data (存储到内存)
```

**路径2（数据库更新）**:
```
Agent执行端点的progress_callback 
→ extract_and_broadcast_variables 
→ update_test_variables_in_db
```

**问题**: 路径2中的`extract_and_broadcast_variables`处理的是`progress_data`，而不是VariableTracker中存储的变量数据。

### 2. test_id传递问题

**问题**: 在同步执行路径中，test_id没有被正确传递到ConfigDrivenAgent实例。

**原因**: `execute_agent_sync`函数直接加载和执行agent，没有调用DynamicLoader的`execute_agent`方法，因此test_id设置逻辑被跳过。

### 3. 数据库更新时机问题

**问题**: 数据库更新只在`extract_and_broadcast_variables`中进行，但这个函数可能不会在变量跟踪发生时被调用。

## 修复方案

### 1. 修复aiohttp依赖问题 ✅

- 移除了VariableTracker中的HTTP API调用
- 简化了数据存储逻辑
- 添加了`get_stored_variables`方法

### 2. 修复test_id传递问题 ✅

**在DynamicLoader中**:
- 修改`execute_agent`方法签名，添加`test_id`参数
- 在agent加载后设置test_id

**在execute_agent_sync中**:
- 添加test_id设置逻辑
- 确保同步和流式执行都支持test_id

### 3. 增强数据库更新逻辑 ✅

**在extract_and_broadcast_variables中**:
- 添加详细的调试日志
- 在每次调用时检查VariableTracker的存储数据

**在流式执行完成后**:
- 添加最终的数据库更新调用
- 确保所有变量数据都被保存

### 4. 添加调试和监控 ✅

**调试日志**:
- 在关键点添加INFO级别日志
- 跟踪test_id传递过程
- 监控变量数据存储状态

**测试工具**:
- `test_simple_variable_tracking.py`: 直接测试VariableTracker和数据库更新
- `debug_variable_tracking.py`: 完整的调用链调试
- `check_variable_storage.py`: 数据库存储验证

## 修复后的数据流程

```
1. 前端调用test-execution/start获取test_id
2. 前端在agent执行请求中传递test_id
3. Agent执行端点提取test_id
4. DynamicLoader设置agent.test_id
5. ConfigDrivenAgent在步骤完成后调用_track_step_variables
6. _track_step_variables调用variable_tracker.track_variable_resolution(test_id=test_id)
7. VariableTracker存储变量数据到内存
8. extract_and_broadcast_variables被调用（每个progress回调）
9. update_test_variables_in_db检查VariableTracker数据并更新数据库
10. 执行完成后进行最终的数据库更新
```

## 关键修改点

### 1. backend/app/services/websocket_service.py
- 移除aiohttp依赖
- 简化数据存储逻辑
- 添加详细日志

### 2. backend/app/api/v1/endpoints/agents.py
- 增强update_test_variables_in_db函数
- 添加详细调试日志
- 在执行完成后添加最终数据库更新

### 3. backend/app/services/dynamic_loader.py
- 修复execute_agent方法的test_id传递
- 在execute_agent_sync中添加test_id设置
- 添加调试日志

## 验证方法

### 1. 直接测试
```bash
cd backend
python test_simple_variable_tracking.py
```

### 2. 完整调试
```bash
cd backend
python debug_variable_tracking.py
```

### 3. 数据库检查
```bash
cd backend
python check_variable_storage.py
```

### 4. 日志监控
查看服务器日志中的以下关键信息：
- `Test ID set for agent {agent_id}: {test_id}`
- `Tracking variables for step '{step_name}' by {assignee}, test_id: {test_id}`
- `Variable data prepared for test {test_id}`
- `Updated variable data in database for test {test_id}`

## 预期结果

修复后，应该能看到：

1. **VariableTracker日志**:
   ```
   Variable data prepared for test test_xxx: N placeholders, M interactions
   ```

2. **数据库更新日志**:
   ```
   Successfully updated variable data in database for test test_xxx (affected rows: 1)
   ```

3. **数据库中的数据**:
   - `context_placeholders_used`: JSON数组，包含变量信息
   - `team_member_interactions`: JSON数组，包含交互信息
   - `context_summary`: JSON对象，包含统计信息

## 故障排除

如果仍然没有数据：

1. **检查test_id传递**:
   - 确认前端正确传递test_id
   - 检查日志中的"Test ID set for agent"消息

2. **检查变量跟踪调用**:
   - 确认agent有可跟踪的变量
   - 检查_track_step_variables是否被调用

3. **检查数据库更新**:
   - 确认update_test_variables_in_db被调用
   - 检查数据库连接和权限

4. **检查agent配置**:
   - 确认agent有定义的变量
   - 确认agent执行产生了输出

## 后续改进

1. **性能优化**: 考虑批量更新变量数据
2. **错误处理**: 增强数据库更新的错误恢复机制
3. **监控仪表板**: 创建变量跟踪状态的实时监控
4. **单元测试**: 添加自动化测试覆盖变量跟踪功能
