[run]
source = app
omit = 
    */tests/*
    */venv/*
    */migrations/*
    */__pycache__/*
    */site-packages/*
    app/main.py
branch = True
parallel = True

[report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod
    @overload
    # Type checking imports
    if TYPE_CHECKING:

ignore_errors = True
show_missing = True
precision = 2
skip_covered = False
skip_empty = False

[html]
directory = htmlcov
title = Meta-Agent Backend Coverage Report

[xml]
output = coverage.xml
