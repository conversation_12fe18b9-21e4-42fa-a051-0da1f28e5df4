# AI-Powered Agent Team Generation System

## 🎯 项目概述

本项目实现了一个完整的AI驱动的智能团队生成系统，为Meta-Agent平台提供了强大的团队创建能力。系统支持多种AI提供商，具备完整的分析、监控和成本管理功能。

## 🚀 核心功能

### 1. 智能团队生成
- **AI驱动生成**: 使用配置的AI模型动态生成团队方案
- **模板降级**: AI失败时自动降级到模板生成
- **多提供商支持**: OpenAI、Anthropic、Google等主流AI提供商
- **自定义配置**: 可配置模型、温度、最大令牌数等参数

### 2. 用户管理与认证
- **用户隔离**: 每个用户使用自己的API密钥
- **安全认证**: JWT令牌认证，确保数据安全
- **权限控制**: 管理员可查看系统级统计
- **API密钥管理**: 加密存储用户API密钥

### 3. 分析与监控
- **实时统计**: 用户和系统级使用统计
- **质量评分**: 自动评估生成团队的质量
- **成本跟踪**: 实时成本估算和预算管理
- **性能监控**: 生成时间、成功率等指标

### 4. 质量保证
- **内容验证**: 自动验证生成内容的完整性
- **错误处理**: 完善的错误分类和处理机制
- **用户反馈**: 评分和反馈收集系统
- **持续改进**: 基于反馈优化生成质量

## 📁 项目结构

```
backend/
├── app/
│   ├── services/
│   │   ├── ai_planner.py              # 增强的AI规划师服务
│   │   └── ai_team_analytics.py       # 分析和统计服务
│   ├── api/v1/endpoints/
│   │   └── planning.py                # 增强的规划API端点
│   ├── models/
│   │   └── settings.py                # 增强的系统设置模型
│   └── ...
├── tests/
│   ├── unit/services/
│   │   └── test_ai_planner.py         # AI规划师测试
│   └── integration/api/
│       └── test_ai_planning_endpoints.py  # API端点测试
├── scripts/
│   └── demo_ai_team_generation.py     # 演示脚本
├── alembic/versions/
│   └── 1f88a529465a_add_ai_team_generation_analytics.py  # 数据库迁移
└── .ai/
    ├── ai-team-generation-implementation.md  # 详细实现文档
    └── README-AI-Team-Generation.md          # 本文件
```

## 🔧 安装与配置

### 1. 环境准备
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows
pip install -r requirements.txt
```

### 2. 数据库迁移
```bash
alembic upgrade head
```

### 3. 系统配置
在系统设置中配置AI团队生成参数：
- `enable_ai_team_generation`: 启用AI生成
- `team_generation_provider`: AI提供商 (openai/anthropic/google)
- `team_generation_model`: 使用的模型
- `team_generation_temperature`: 生成温度 (0.0-2.0)
- `team_generation_max_tokens`: 最大令牌数

### 4. 用户API密钥
用户需要在个人设置中添加对应AI提供商的API密钥。

## 📚 API 使用指南

### 1. AI团队生成
```bash
# 纯AI生成
curl -X POST "http://localhost:8000/api/v1/planning/ai-generate" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"user_description": "我需要一个技术咨询团队"}'

# AI生成带降级
curl -X POST "http://localhost:8000/api/v1/planning/ai-generate-with-fallback" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_description": "我需要一个创意写作团队",
    "template_id": "creative_writer"
  }'
```

### 2. 获取AI设置
```bash
curl -X GET "http://localhost:8000/api/v1/planning/ai-settings" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. 用户分析
```bash
curl -X GET "http://localhost:8000/api/v1/planning/analytics/user?days=30" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 4. 系统分析 (管理员)
```bash
curl -X GET "http://localhost:8000/api/v1/planning/analytics/system?days=30" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

### 5. 提交反馈
```bash
curl -X POST "http://localhost:8000/api/v1/planning/feedback/RECORD_ID" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"rating": 5, "feedback": "生成的团队非常专业！"}'
```

## 🧪 测试

### 运行单元测试
```bash
python -m pytest tests/unit/services/test_ai_planner.py -v
```

### 运行集成测试
```bash
python -m pytest tests/integration/api/test_ai_planning_endpoints.py -v
```

### 运行演示脚本
```bash
python scripts/demo_ai_team_generation.py
```

## 📊 质量与成本管理

### 质量评分标准
- **9-10分**: 卓越质量，内容全面详细
- **7-8分**: 高质量，结构良好，细节充分
- **5-6分**: 良好质量，基本满足要求
- **3-4分**: 一般质量，存在明显不足
- **1-2分**: 质量较差，内容不完整

### 成本估算
系统提供实时成本估算，支持：
- 按提供商和模型的精确定价
- 月度成本预测
- 预算超支警告
- 成本优化建议

## 🔒 安全考虑

### 数据保护
- API密钥加密存储
- 用户数据完全隔离
- 敏感信息不记录日志
- 定期安全审计

### 访问控制
- JWT令牌认证
- 基于角色的权限控制
- API速率限制
- 请求来源验证

## 🚀 部署建议

### 生产环境配置
1. **数据库**: 使用PostgreSQL替代SQLite
2. **缓存**: 配置Redis缓存提高性能
3. **日志**: 配置结构化日志和日志聚合
4. **备份**: 定期数据备份和恢复测试

### 扩展性考虑
- 支持水平扩展
- 异步任务队列
- 负载均衡配置
- 微服务架构准备

## 🔮 未来规划

### 短期目标 (1-2个月)
- [ ] 高级提示工程
- [ ] 多模型集成
- [ ] 自动化测试增强

### 中期目标 (3-6个月)
- [ ] 机器学习优化
- [ ] 协作功能
- [ ] 模板市场
- [ ] 移动端支持

### 长期目标 (6-12个月)
- [ ] 自定义模型训练
- [ ] 企业级功能
- [ ] 国际化支持
- [ ] 生态系统集成

## 📞 支持与贡献

### 技术支持
- 查看详细文档: `backend/.ai/ai-team-generation-implementation.md`
- 运行演示脚本了解功能
- 查看测试用例了解API使用

### 贡献指南
1. Fork项目仓库
2. 创建功能分支
3. 编写测试用例
4. 提交Pull Request
5. 代码审查和合并

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

---

**🎉 恭喜！AI驱动的智能团队生成系统已经完全实现并可投入使用！**
