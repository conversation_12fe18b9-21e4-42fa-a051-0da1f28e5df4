# Team Collaboration Fix

## Issue Description

The backend was logging a warning when executing agents with workflow steps assigned to "团队协作" (Team Collaboration):

```
WARNING | app.services.dynamic_loader:_execute_workflow_step_stream:400 | 未找到团队成员: 团队协作
```

This warning occurred because the dynamic loader's `_find_team_member` method only looked for exact matches with individual team member names, but "团队协作" is a special assignee value indicating collaborative work involving multiple team members.

## Root Cause

1. **Special Assignee Value**: According to the AI planner template, workflow steps can have an assignee of either:
   - A specific team member name (e.g., "分析师", "策略师")
   - The special value "团队协作" (Team Collaboration) for collaborative steps

2. **Missing Handler**: The `_find_team_member` method in `ConfigDrivenAgent` only handled individual team member lookups and didn't recognize the special "团队协作" value.

3. **Fallback Behavior**: When a team member wasn't found, the system would log a warning and fall back to using the first available team member, which worked but generated confusing log messages.

## Solution Implemented

### 1. Enhanced Team Member Lookup

Updated `_find_team_member` method in `backend/app/services/dynamic_loader.py`:

```python
def _find_team_member(self, name: str) -> Optional[Dict]:
    """查找团队成员"""
    # Handle special case for team collaboration
    if name == "团队协作" or name == "Team Collaboration":
        # For collaborative steps, use the first team member as the primary executor
        # but mark it as a collaborative step
        if self.team_members:
            primary_member = self.team_members[0].copy()
            primary_member["is_collaborative"] = True
            primary_member["collaboration_type"] = "team_collaboration"
            return primary_member
        else:
            # Fallback if no team members exist
            return {
                "name": "团队协作",
                "role": "collaborative_assistant",
                "description": "团队协作执行",
                "is_collaborative": True,
                "collaboration_type": "team_collaboration"
            }
    
    # Regular team member lookup
    for member in self.team_members:
        if member.get("name") == name:
            return member
    return None
```

### 2. Collaborative Context Building

Added `_build_collaborative_context` method to provide team-wide context:

```python
def _build_collaborative_context(self) -> Dict[str, Any]:
    """构建团队协作上下文信息"""
    return {
        "team_size": len(self.team_members),
        "team_members": [
            {
                "name": member.get("name", "Unknown"),
                "role": member.get("role", "Assistant"),
                "description": member.get("description", ""),
                "capabilities": member.get("capabilities", [])
            }
            for member in self.team_members
        ],
        "collaboration_style": "综合各成员专长，协作完成任务",
        "decision_making": "基于团队共识和专业判断"
    }
```

### 3. Enhanced Prompt Generation

Added `build_collaborative_step_prompt` method to `ContextService` for generating specialized prompts for collaborative steps:

```python
def build_collaborative_step_prompt(
    self, 
    execution_id: str, 
    step: ContextAwareWorkflowStep, 
    primary_member: ContextAwareTeamMember,
    team_members: List[Dict],
    user_input: str = "",
    collaborative_context: Dict = None
) -> str:
    """Build a prompt for collaborative team steps."""
    # ... implementation details
```

### 4. Workflow Step Processing

Updated workflow step execution to detect and handle collaborative steps:

```python
# Check if this is a collaborative step
is_collaborative = member.get("is_collaborative", False)
if is_collaborative:
    logger.info(f"执行团队协作步骤: {step_name}")
    # Use collaborative prompt generation
    step_prompt = self.context_service.build_collaborative_step_prompt(...)
else:
    logger.info(f"执行个人步骤: {step_name} (负责人: {assignee})")
    # Use regular prompt generation
    step_prompt = self.context_service.build_step_prompt(...)
```

## Features

### Supported Assignee Values

- **Individual Members**: Exact team member names (e.g., "分析师", "策略师")
- **Team Collaboration (Chinese)**: "团队协作"
- **Team Collaboration (English)**: "Team Collaboration"

### Collaborative Step Behavior

1. **Primary Executor**: Uses the first team member as the primary executor
2. **Collaborative Marking**: Marks the step as collaborative with `is_collaborative: true`
3. **Enhanced Prompts**: Generates prompts that emphasize team collaboration and multiple perspectives
4. **Team Context**: Includes information about all team members and their capabilities

### Error Handling

- **Empty Teams**: Gracefully handles cases where no team members exist
- **Fallback Behavior**: Provides sensible defaults when team collaboration is requested but no members are available
- **Logging**: Provides clear, informative log messages for collaborative vs. individual steps

## Testing

Created comprehensive test suite in `backend/tests/test_team_collaboration.py`:

- ✅ Regular team member lookup
- ✅ Team collaboration detection (Chinese and English)
- ✅ Collaborative context building
- ✅ Empty team handling
- ✅ Workflow step type identification
- ✅ Collaborative prompt generation
- ✅ Edge cases (special characters, case sensitivity)

All 11 tests pass successfully.

## Benefits

1. **Eliminates Warnings**: No more "未找到团队成员: 团队协作" warnings in logs
2. **Proper Collaboration**: Collaborative steps are now properly identified and handled
3. **Enhanced Prompts**: Collaborative steps get specialized prompts that emphasize teamwork
4. **Better Logging**: Clear distinction between individual and collaborative steps in logs
5. **Robust Error Handling**: Graceful handling of edge cases and empty teams
6. **Backward Compatibility**: Existing individual team member assignments continue to work unchanged

## Usage Example

```json
{
  "workflow": {
    "steps": [
      {
        "name": "数据分析",
        "description": "分析相关数据",
        "assignee": "分析师",
        "inputs": ["原始数据"],
        "outputs": ["分析报告"]
      },
      {
        "name": "团队讨论",
        "description": "团队协作讨论解决方案",
        "assignee": "团队协作",
        "inputs": ["分析报告"],
        "outputs": ["讨论结果", "初步方案"]
      },
      {
        "name": "策略制定",
        "description": "制定最终策略",
        "assignee": "策略师",
        "inputs": ["讨论结果"],
        "outputs": ["最终策略"]
      }
    ]
  }
}
```

The second step will now be properly recognized as a collaborative step and executed with appropriate team-wide context and prompting.
