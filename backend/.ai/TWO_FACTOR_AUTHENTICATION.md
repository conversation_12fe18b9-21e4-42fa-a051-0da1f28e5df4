# Two-Factor Authentication (2FA) Implementation

## 🎯 Overview

This document describes the comprehensive Two-Factor Authentication (2FA) system implemented for the Meta-Agent platform. The system provides TOTP (Time-based One-Time Password) authentication using industry-standard protocols.

## 🏗️ Architecture

### Core Components

1. **Backend Models** (`app/models/user.py`)
   - Extended `User` model with 2FA fields
   - 2FA-specific request/response models
   - Backup codes support

2. **Security Functions** (`app/core/security.py`)
   - TOTP secret generation and verification
   - QR code generation
   - Backup codes management
   - Encryption utilities

3. **API Endpoints** (`app/api/v1/endpoints/two_factor.py`)
   - 2FA setup and configuration
   - Enable/disable 2FA
   - Code verification
   - QR code generation

4. **Frontend Components**
   - 2FA settings interface
   - Login flow integration
   - QR code display and setup wizard

## 📊 Database Schema

### User Table Extensions

```sql
-- Added to users table
is_2fa_enabled BOOLEAN NOT NULL DEFAULT FALSE
totp_secret VARCHAR(32) NULL  -- Base32 encoded TOTP secret
backup_codes VARCHAR(1000) NULL  -- JSON array of hashed backup codes
two_fa_enabled_at DATETIME NULL  -- Timestamp when 2FA was enabled
```

## 🔧 API Endpoints

### Setup 2FA
```
POST /api/v1/2fa/setup
Content-Type: application/json

{
  "password": "user_password"
}

Response:
{
  "secret": "JBSWY3DPEHPK3PXP",
  "qr_code_url": "otpauth://totp/Meta-Agent:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=Meta-Agent",
  "backup_codes": ["ABCD1234", "EFGH5678", ...]
}
```

### Enable 2FA
```
POST /api/v1/2fa/enable
Content-Type: application/json

{
  "totp_code": "123456"
}

Response:
{
  "message": "Two-factor authentication enabled successfully"
}
```

### Disable 2FA
```
POST /api/v1/2fa/disable
Content-Type: application/json

{
  "password": "user_password",
  "totp_code": "123456"  // OR backup_code
}

Response:
{
  "message": "Two-factor authentication disabled successfully"
}
```

### Login with 2FA
```
POST /api/v1/auth/login-2fa
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "user_password",
  "totp_code": "123456",  // OR backup_code
  "remember_me": false
}

Response: Same as regular login
```

### Get QR Code
```
GET /api/v1/2fa/qr-code
Response: PNG image
```

### Check 2FA Status
```
GET /api/v1/2fa/status

Response:
{
  "is_enabled": true,
  "enabled_at": "2025-07-21T02:30:00Z",
  "has_backup_codes": true
}
```

## 🔐 Security Features

### TOTP Implementation
- Uses industry-standard TOTP algorithm (RFC 6238)
- 30-second time window
- 6-digit codes
- 1-step tolerance for clock drift

### Backup Codes
- 8 single-use backup codes generated during setup
- Codes are hashed using bcrypt before storage
- Used codes are automatically removed
- Can be used instead of TOTP codes

### Security Measures
- Password verification required for setup/disable
- Rate limiting on authentication endpoints
- Secure secret generation using cryptographically secure random
- QR codes generated server-side for security

## 🎨 Frontend Integration

### 2FA Settings Component
- Located in `/components/features/security/two-factor-settings.tsx`
- Integrated into user account settings page
- Step-by-step setup wizard
- QR code display and manual secret entry
- Backup codes download functionality

### Login Flow Enhancement
- Modified `/components/auth/login-form.tsx`
- Two-step authentication process
- Support for both TOTP and backup codes
- Graceful fallback for non-2FA users

## 📱 Supported Authenticator Apps

The system is compatible with any TOTP-compliant authenticator app:

- **Google Authenticator** (iOS/Android)
- **Microsoft Authenticator** (iOS/Android)
- **Authy** (iOS/Android/Desktop)
- **1Password** (with TOTP support)
- **Bitwarden** (with TOTP support)
- **LastPass Authenticator**

## 🚀 Setup Process

### For Users

1. **Navigate to Account Settings**
   - Go to `/account` page
   - Click on "Security Settings" tab

2. **Enable 2FA**
   - Toggle the "Two-Factor Authentication" switch
   - Enter your password to confirm

3. **Scan QR Code**
   - Use your authenticator app to scan the QR code
   - Or manually enter the secret key

4. **Verify Setup**
   - Enter the 6-digit code from your authenticator app
   - Save your backup codes in a secure location

5. **Complete Setup**
   - 2FA is now enabled for your account

### For Administrators

1. **System Configuration**
   - 2FA can be enabled/disabled system-wide in settings
   - No additional configuration required

2. **User Management**
   - Admins can view 2FA status for users
   - Can assist with 2FA recovery if needed

## 🔄 Recovery Process

### Using Backup Codes
1. During login, click "Use backup code instead"
2. Enter one of your 8-character backup codes
3. The used code will be automatically invalidated

### Account Recovery
1. Contact system administrator
2. Admin can disable 2FA for the account
3. User can re-enable 2FA with new setup

## 🧪 Testing

### Manual Testing
1. Enable 2FA on a test account
2. Verify QR code scanning works
3. Test login with TOTP codes
4. Test backup code functionality
5. Test disable process

### Automated Testing
- Unit tests for TOTP generation/verification
- Integration tests for API endpoints
- Frontend component tests

## 📈 Monitoring

### Metrics to Track
- 2FA adoption rate
- Failed 2FA attempts
- Backup code usage
- Account recovery requests

### Logging
- All 2FA events are logged with appropriate detail
- Failed attempts are tracked for security monitoring
- Successful setups and authentications are recorded

## 🔒 Security Considerations

### Best Practices Implemented
- Secrets are generated using cryptographically secure random
- Backup codes are hashed before storage
- Time-based codes prevent replay attacks
- Rate limiting prevents brute force attacks

### Recommendations
- Encourage users to enable 2FA
- Provide clear setup instructions
- Maintain backup code security
- Regular security audits

## 🚨 Troubleshooting

### Common Issues
1. **Clock Synchronization**: Ensure server and client clocks are synchronized
2. **QR Code Issues**: Provide manual secret entry as fallback
3. **Lost Device**: Use backup codes or contact admin
4. **Code Not Working**: Check time synchronization and try again

### Support Procedures
1. Verify user identity
2. Check 2FA status in admin panel
3. Assist with backup code usage
4. Reset 2FA if necessary (admin only)

## 📝 Future Enhancements

### Planned Features
- SMS backup option
- Hardware token support (FIDO2/WebAuthn)
- Admin-enforced 2FA policies
- Bulk 2FA management tools

### Considerations
- Mobile app integration
- SSO integration
- Advanced recovery options
- Compliance requirements (SOX, HIPAA, etc.)
