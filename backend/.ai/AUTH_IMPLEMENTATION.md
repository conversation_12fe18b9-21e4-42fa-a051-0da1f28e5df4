# User Authentication System Implementation

## 🎯 Overview

This document describes the comprehensive user authentication and management system implemented for the Meta-Agent backend. The system provides secure user registration, login, profile management, password reset, and session management with JWT tokens.

## 🏗️ Architecture

### Core Components

1. **Models** (`app/models/user.py`)
   - `User`: Main user model with authentication fields
   - `UserSession`: Session management with device tracking
   - `UserToken`: Tokens for email verification and password reset
   - `LoginHistory`: Security audit trail

2. **Services** (`app/services/user_service.py`)
   - `UserService`: Business logic for user operations
   - User registration and authentication
   - Password management and reset
   - Session and token management

3. **API Endpoints** (`app/api/v1/endpoints/auth.py`)
   - Registration, login, logout
   - Profile management
   - Password change and reset
   - Session and history management

4. **Security** (`app/core/security.py`, `app/core/middleware.py`)
   - JWT token generation and validation
   - Password hashing with bcrypt
   - Rate limiting and attack prevention
   - Security headers and CSRF protection

## 🔐 Security Features

### Authentication
- JWT Bearer tokens with configurable expiry
- Secure password hashing using bcrypt
- Account lockout after failed attempts
- Session management with device tracking

### Security Middleware
- Rate limiting for authentication endpoints
- IP-based blocking for suspicious activity
- Request logging for security monitoring
- CSRF protection for state-changing operations

### Data Protection
- Password hashing with salt
- Secure token generation
- Input sanitization
- SQL injection prevention

## 📊 Database Schema

### Users Table
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY,
    uuid VARCHAR UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR NOT NULL,
    role VARCHAR DEFAULT 'user',
    status VARCHAR DEFAULT 'pending_verification',
    avatar VARCHAR(500),
    bio VARCHAR(1000),
    timezone VARCHAR(50),
    language VARCHAR(10) DEFAULT 'en',
    last_login_at DATETIME,
    last_activity_at DATETIME,
    login_count INTEGER DEFAULT 0,
    is_email_verified BOOLEAN DEFAULT FALSE,
    email_verified_at DATETIME,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until DATETIME,
    password_changed_at DATETIME,
    preferences JSON DEFAULT '{}',
    metadata JSON DEFAULT '{}',
    created_at DATETIME NOT NULL,
    updated_at DATETIME
);
```

### User Sessions Table
```sql
CREATE TABLE user_sessions (
    id INTEGER PRIMARY KEY,
    uuid VARCHAR UNIQUE NOT NULL,
    user_id INTEGER NOT NULL,
    session_token VARCHAR UNIQUE NOT NULL,
    status VARCHAR DEFAULT 'active',
    ip_address VARCHAR(45),
    user_agent VARCHAR(500),
    device_info VARCHAR(255),
    location VARCHAR(255),
    expires_at DATETIME NOT NULL,
    last_activity_at DATETIME NOT NULL,
    is_secure BOOLEAN DEFAULT TRUE,
    is_mobile BOOLEAN DEFAULT FALSE,
    metadata JSON DEFAULT '{}',
    created_at DATETIME NOT NULL,
    updated_at DATETIME,
    FOREIGN KEY (user_id) REFERENCES users (id)
);
```

## 🚀 API Endpoints

### Authentication Endpoints

#### POST /api/v1/auth/register
Register a new user account.

**Request:**
```json
{
    "name": "John Doe",
    "email": "<EMAIL>",
    "password": "securepassword123",
    "confirm_password": "securepassword123",
    "timezone": "UTC",
    "language": "en"
}
```

**Response:**
```json
{
    "user": {
        "id": 1,
        "uuid": "abc123...",
        "name": "John Doe",
        "email": "<EMAIL>",
        "role": "user",
        "status": "pending_verification",
        "is_email_verified": false,
        "created_at": "2024-01-01T00:00:00Z"
    },
    "tokens": {
        "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "token_type": "bearer",
        "expires_in": 2592000
    },
    "session_id": "session_uuid"
}
```

#### POST /api/v1/auth/login
Authenticate user and create session.

**Request:**
```json
{
    "email": "<EMAIL>",
    "password": "securepassword123",
    "remember_me": false
}
```

#### GET /api/v1/auth/me
Get current user profile (requires authentication).

#### PUT /api/v1/auth/me
Update user profile (requires authentication).

#### PUT /api/v1/auth/change-password
Change user password (requires authentication).

#### POST /api/v1/auth/reset-password
Request password reset token.

#### POST /api/v1/auth/logout
Logout and invalidate session.

## 🧪 Testing

### Test Suite Structure
```
tests/
├── unit/
│   ├── models/test_user.py          # User model tests
│   ├── services/test_user_service.py # Service layer tests
│   └── core/test_security.py        # Security utility tests
├── integration/
│   └── api/test_auth_endpoints.py   # API endpoint tests
└── test_auth_suite.py               # Test runner
```

### Running Tests
```bash
# Run all authentication tests
python tests/test_auth_suite.py all

# Run specific test types
python tests/test_auth_suite.py unit
python tests/test_auth_suite.py integration
python tests/test_auth_suite.py security

# Run with pytest directly
pytest tests/unit/models/test_user.py -v
pytest tests/integration/api/test_auth_endpoints.py -v --cov
```

### Coverage Requirements
- Unit tests: 85% minimum coverage
- Integration tests: 80% minimum coverage
- Overall authentication system: 80% minimum coverage

## 🔧 Setup and Configuration

### 1. Initialize Database Tables
```bash
python scripts/init_user_tables.py
```

### 2. Environment Variables
```bash
# Security
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Database
DATABASE_URL=sqlite+aiosqlite:///./data/meta_agent.db

# Rate Limiting
RATE_LIMIT_PER_MINUTE=100
```

### 3. Start Application
```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

## 📝 Usage Examples

### Frontend Integration
```typescript
// Login user
const loginResponse = await fetch('/api/v1/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
    })
});

const { user, tokens } = await loginResponse.json();

// Store token for future requests
localStorage.setItem('auth_token', tokens.access_token);

// Make authenticated requests
const profileResponse = await fetch('/api/v1/auth/me', {
    headers: {
        'Authorization': `Bearer ${tokens.access_token}`
    }
});
```

### Python Client
```python
import httpx

# Login
async with httpx.AsyncClient() as client:
    login_response = await client.post('/api/v1/auth/login', json={
        'email': '<EMAIL>',
        'password': 'password123'
    })
    
    tokens = login_response.json()['tokens']
    
    # Make authenticated request
    profile_response = await client.get('/api/v1/auth/me', headers={
        'Authorization': f"Bearer {tokens['access_token']}"
    })
```

## 🛡️ Security Best Practices

### For Developers
1. Always validate input data
2. Use parameterized queries to prevent SQL injection
3. Implement proper error handling without information disclosure
4. Log security events for monitoring
5. Keep dependencies updated

### For Deployment
1. Use HTTPS in production
2. Set secure environment variables
3. Configure rate limiting appropriately
4. Monitor authentication logs
5. Implement backup and recovery procedures

## 🔄 Migration from Frontend-Only Auth

The new backend authentication system is designed to be compatible with the existing frontend authentication flow. Key migration steps:

1. **Database Migration**: Run `python scripts/init_user_tables.py`
2. **Frontend Updates**: Update API calls to use new endpoints
3. **Token Management**: Switch from localStorage tokens to JWT tokens
4. **Session Handling**: Implement proper session management
5. **Error Handling**: Update error handling for new response formats

## 📈 Performance Considerations

- JWT tokens are stateless and don't require database lookups for validation
- Session management is optimized with proper indexing
- Rate limiting prevents abuse and improves performance
- Password hashing is tuned for security vs. performance balance

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Check DATABASE_URL configuration
   - Ensure database file permissions
   - Run table initialization script

2. **Authentication Failures**
   - Verify JWT secret key configuration
   - Check token expiry settings
   - Review rate limiting configuration

3. **Test Failures**
   - Ensure test database is properly isolated
   - Check for missing test dependencies
   - Verify mock configurations

### Debug Mode
Enable debug logging by setting `LOG_LEVEL=DEBUG` in environment variables.

## 📚 Additional Resources

- [FastAPI Security Documentation](https://fastapi.tiangolo.com/tutorial/security/)
- [SQLModel Documentation](https://sqlmodel.tiangolo.com/)
- [JWT Best Practices](https://auth0.com/blog/a-look-at-the-latest-draft-for-jwt-bcp/)
- [OWASP Authentication Guidelines](https://owasp.org/www-project-cheat-sheets/cheatsheets/Authentication_Cheat_Sheet.html)
