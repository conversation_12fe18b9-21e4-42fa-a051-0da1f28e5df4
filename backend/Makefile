# Makefile for Meta-Agent Backend

.PHONY: help install test test-unit test-integration test-performance test-all test-coverage test-fast clean lint format check

# Default target
help:
	@echo "Available targets:"
	@echo "  install          - Install dependencies"
	@echo "  test             - Run all tests"
	@echo "  test-unit        - Run unit tests only"
	@echo "  test-integration - Run integration tests only"
	@echo "  test-performance - Run performance tests only"
	@echo "  test-coverage    - Run tests with coverage report"
	@echo "  test-fast        - Run tests excluding slow ones"
	@echo "  test-parallel    - Run tests in parallel"
	@echo "  lint             - Run linting checks"
	@echo "  format           - Format code"
	@echo "  check            - Run all checks (lint + test)"
	@echo "  clean            - Clean up generated files"

# Installation
install:
	pip install -e ".[dev]"

install-dev:
	pip install -e ".[dev]"
	pip install pytest pytest-asyncio pytest-cov httpx

# Testing targets
test:
	python -m pytest tests/ -k "not database_operations and not test_simple_database_creation and not test_memory_database" -v

test-unit:
	python -m pytest tests/unit/ -k "not database_operations" -v

test-integration:
	python -m pytest tests/integration/ -k "not database_operations" -v

test-performance:
	python -m pytest tests/performance/ -v

test-all:
	python -m pytest tests/ -k "not database_operations and not test_simple_database_creation and not test_memory_database" -v --tb=short

test-coverage:
	python -m pytest tests/ -k "not database_operations and not test_simple_database_creation and not test_memory_database" --cov=app --cov-report=term-missing --cov-report=html:htmlcov --cov-report=xml:coverage.xml

test-fast:
	python -m pytest tests/ -k "not slow and not database_operations and not test_simple_database_creation and not test_memory_database" -v

test-parallel:
	python -m pytest tests/ -k "not database_operations and not test_simple_database_creation and not test_memory_database" -n auto -v

test-watch:
	python -m pytest tests/ -k "not database_operations and not test_simple_database_creation and not test_memory_database" --looponfail

# Database-specific tests (may have index conflicts)
test-database:
	@echo "⚠️  Running database tests (may have index conflicts)"
	python -m pytest tests/ -k "database_operations or test_simple_database_creation or test_memory_database" -v

test-database-clean:
	@echo "🧹 Cleaning database files and running database tests"
	rm -f test_*.db meta_agent.db
	python -m pytest tests/ -k "database_operations or test_simple_database_creation or test_memory_database" -v

# Quality checks
lint:
	python -m flake8 app tests
	python -m mypy app

format:
	python -m black app tests
	python -m isort app tests

check: lint test-fast

# Cleanup
clean:
	rm -rf htmlcov/
	rm -rf .coverage
	rm -rf coverage.xml
	rm -rf .pytest_cache/
	rm -rf __pycache__/
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

# Development helpers
dev-setup: install-dev
	@echo "Development environment setup complete!"

run-dev:
	uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Database operations
db-upgrade:
	alembic upgrade head

db-downgrade:
	alembic downgrade -1

db-reset:
	rm -f meta_agent.db
	alembic upgrade head

# Docker operations
docker-build:
	docker build -t meta-agent-backend .

docker-run:
	docker run -p 8000:8000 meta-agent-backend

docker-test:
	docker run --rm meta-agent-backend python -m pytest tests/

# CI/CD helpers
ci-test:
	python -m pytest tests/ --cov=app --cov-report=xml --cov-fail-under=80 --tb=short

ci-lint:
	python -m flake8 app tests --count --select=E9,F63,F7,F82 --show-source --statistics

# Documentation
docs-build:
	cd docs && make html

docs-serve:
	cd docs/_build/html && python -m http.server 8080

# Security checks
security-check:
	python -m bandit -r app/



# Test data generation
generate-test-data:
	python scripts/generate_test_data.py

# Backup and restore
backup-db:
	cp meta_agent.db meta_agent.db.backup

restore-db:
	cp meta_agent.db.backup meta_agent.db

# Environment checks
check-env:
	@echo "Python version: $(shell python --version)"
	@echo "Pip version: $(shell pip --version)"
	@echo "Virtual environment: $(VIRTUAL_ENV)"
	@echo "Current directory: $(PWD)"

# Quick development workflow
quick-check: test-fast lint
	@echo "Quick checks passed! ✅"

full-check: test-coverage lint security-check
	@echo "Full checks completed! ✅"
