# 变量跟踪数据库存储修复

## 问题描述

1. **aiohttp模块缺失错误**：
   ```
   2025-07-25 14:16:09.174 | ERROR | app.services.websocket_service:_update_test_database:460 | Failed to update test database for test_6a2d5de793144b97: No module named 'aiohttp'
   ```

2. **变量信息未保存到数据库**：
   - 变量跟踪信息没有正确存储到test_history表
   - 每个步骤执行结束后应该将变量信息同时存入数据库和通过WebSocket发送

## 根本原因分析

### 问题1：aiohttp依赖
- VariableTracker服务中的`_update_test_database`方法使用了aiohttp来调用HTTP API
- 这种设计存在以下问题：
  - 增加了不必要的外部依赖
  - 需要处理认证token传递
  - 增加了网络开销和复杂性
  - 可能导致循环依赖

### 问题2：数据库存储逻辑
- 变量跟踪和数据库存储逻辑分离在不同的服务中
- 缺乏直接的数据库访问权限
- 错误处理不够完善

## 修复方案

### 1. 移除aiohttp依赖

**修改文件**: `backend/app/services/websocket_service.py`

- **移除**: `_update_test_database`方法中的aiohttp调用
- **移除**: `set_user_token`方法
- **简化**: `_store_variable_data`方法，只做数据准备
- **新增**: `get_stored_variables`方法，供外部获取存储的变量数据

### 2. 在Agent执行端点中添加数据库更新

**修改文件**: `backend/app/api/v1/endpoints/agents.py`

- **新增**: `update_test_variables_in_db`函数，直接操作数据库
- **修改**: `extract_and_broadcast_variables`函数，添加数据库会话参数
- **集成**: 在同步和流式执行端点中调用数据库更新

### 3. 数据流程优化

**新的数据流程**:
```
步骤执行完成
    ↓
_track_step_variables (ConfigDrivenAgent)
    ↓
variable_tracker.track_variable_resolution (WebSocket广播)
    ↓
_store_variable_data (数据准备)
    ↓
extract_and_broadcast_variables (Agent端点)
    ↓
update_test_variables_in_db (直接数据库操作)
```

## 具体修改内容

### 1. VariableTracker服务简化

```python
# 移除了aiohttp相关代码
async def _store_variable_data(self, test_id: str, variable_update: VariableUpdate, metadata: Optional[Dict[str, Any]] = None):
    # 只做数据准备，不直接更新数据库
    # 存储到内存中供后续使用
    
def get_stored_variables(self, test_id: str) -> Dict[str, Any]:
    # 新增方法，供外部获取存储的变量数据
```

### 2. Agent执行端点增强

```python
async def update_test_variables_in_db(test_id: str, agent_id: str, db: AsyncSession):
    # 直接操作数据库更新变量数据
    # 从VariableTracker获取准备好的数据
    # 更新test_history表的相关字段
```

### 3. 数据库字段更新

更新test_history表的以下字段：
- `context_placeholders_used`: 变量占位符使用情况
- `team_member_interactions`: 团队成员交互数据  
- `context_summary`: 变量统计摘要

## 测试验证

### 1. 单元测试脚本

**文件**: `backend/test_variable_tracking_fix.py`
- 测试test execution start API
- 测试VariableTracker不再依赖aiohttp
- 验证数据库存储功能

### 2. 数据库检查脚本

**文件**: `backend/check_variable_storage.py`
- 检查最近的测试记录
- 验证变量数据是否正确存储
- 分析变量数据的完整性

### 3. 运行测试

```bash
# 测试修复效果
cd backend
python test_variable_tracking_fix.py

# 检查数据库存储
python check_variable_storage.py

# 检查特定测试记录
python check_variable_storage.py <test_id>
```

## 预期效果

### 1. 错误修复
- ✅ 不再出现aiohttp模块缺失错误
- ✅ 变量跟踪正常工作
- ✅ 数据库更新成功

### 2. 功能改进
- ✅ 简化了架构，移除不必要的HTTP调用
- ✅ 提高了性能，减少网络开销
- ✅ 增强了可靠性，直接数据库操作
- ✅ 改善了错误处理

### 3. 数据完整性
- ✅ 变量信息实时存储到数据库
- ✅ WebSocket广播功能保持不变
- ✅ 测试历史记录包含完整的变量数据

## 向后兼容性

- ✅ 现有的WebSocket变量跟踪接口不变
- ✅ 前端代码无需修改
- ✅ 测试历史API保持兼容
- ✅ 数据库schema无变化

## 监控和调试

### 日志级别
- `INFO`: 成功的数据库更新
- `DEBUG`: 变量数据准备过程
- `ERROR`: 数据库更新失败

### 关键日志信息
```
Updated variable data in database for test {test_id}
Variable data prepared for test {test_id}
Failed to update test variables in database for {test_id}: {error}
```

### 调试建议
1. 检查日志中是否有数据库更新成功的信息
2. 使用数据库检查脚本验证数据存储
3. 确认test_id正确传递到所有相关函数
4. 验证数据库连接和权限正常

## 后续改进建议

1. **批量更新优化**: 考虑批量更新多个变量以提高性能
2. **缓存机制**: 添加变量数据缓存以减少数据库压力
3. **监控仪表板**: 创建变量跟踪状态的实时监控
4. **数据压缩**: 对大量变量数据进行压缩存储
