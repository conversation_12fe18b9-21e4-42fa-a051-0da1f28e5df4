# Application Settings
APP_NAME=Meta-Agent
APP_VERSION=0.1.0
APP_DESCRIPTION=AI Agent自动生成服务
DEBUG=true
ENVIRONMENT=development

# Server Settings
HOST=0.0.0.0
PORT=8000
RELOAD=true

# Database Settings
DATABASE_URL=sqlite+aiosqlite:///./data/meta_agent.db
# For PostgreSQL: postgresql+asyncpg://user:password@localhost/meta_agent
DATABASE_ECHO=false

# Security Settings
SECRET_KEY=your-super-secret-key-change-this-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM=HS256

# CORS Settings
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:3001","http://127.0.0.1:3000"]

# AI Provider API Keys
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
GOOGLE_API_KEY=your-google-api-key

# LangSmith (Optional for tracing)
LANGCHAIN_TRACING_V2=false
LANGCHAIN_API_KEY=your-langsmith-api-key
LANGCHAIN_PROJECT=meta-agent

# Redis Settings (for caching and background tasks)
REDIS_URL=redis://localhost:6379/0

# Logging Settings
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=logs/meta-agent.log
LOG_ROTATION=1 day
LOG_RETENTION=30 days

# Rate Limiting
RATE_LIMIT_PER_MINUTE=100
RATE_LIMIT_BURST=20

# File Upload Settings
MAX_UPLOAD_SIZE=10485760  # 10MB
UPLOAD_DIR=data/uploads

# Agent Settings
MAX_CONCURRENT_AGENTS=10
AGENT_TIMEOUT_SECONDS=300
DEFAULT_MODEL=gpt-4
DEFAULT_TEMPERATURE=0.7

# Template Settings
TEMPLATE_DIR=templates
GENERATED_AGENTS_DIR=data/generated_agents



# Background Tasks
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# Development Settings
RELOAD_DIRS=["app"]
RELOAD_INCLUDES=["*.py"]
RELOAD_EXCLUDES=["*.pyc", "__pycache__"]
