# 重构测试记录创建和变量跟踪流程

## 概述

本次重构实现了后端主导的测试管理系统，将test_id生成逻辑从前端移至后端，并增强了变量跟踪的数据库存储功能。

## 主要变更

### 1. 新增测试执行启动API端点

**文件**: `backend/app/api/v1/endpoints/test_execution.py`

- **端点**: `POST /api/v1/test-execution/start`
- **功能**: 生成唯一test_id并创建test_history记录
- **返回**: test_id供前端使用

**请求示例**:
```json
{
  "agent_id": "agent_123",
  "input_text": "用户输入的测试内容",
  "ai_config_override": {
    "provider": "openai",
    "model": "gpt-4",
    "temperature": 0.7
  },
  "api_key_id": 1,
  "api_key_name": "测试密钥",
  "input_metadata": {
    "timestamp": "2025-01-25T10:00:00Z",
    "user_agent": "Mozilla/5.0..."
  }
}
```

**响应示例**:
```json
{
  "test_id": "test_a1b2c3d4e5f6g7h8",
  "status": "started",
  "message": "Test execution initialized successfully",
  "created_at": "2025-01-25T10:00:00Z"
}
```

### 2. 增强VariableTracker服务

**文件**: `backend/app/services/websocket_service.py`

- **新增**: 数据库存储功能
- **方法**: `track_variable_resolution` 现在支持 `test_id` 参数
- **功能**: 实时更新test_history表的变量字段

**主要改进**:
- 添加了 `_store_variable_data` 方法
- 添加了 `_update_test_database` 方法
- 支持变量数据的实时数据库存储

### 3. 修改前端测试执行流程

**文件**: `frontend/src/components/features/agent-testing/test-interface.tsx`

- **移除**: 前端test_id生成逻辑
- **新增**: 调用新的测试启动端点
- **改进**: 错误处理和用户反馈

**主要变更**:
- 使用 `api.testExecution.start()` 获取test_id
- 在agent执行请求中传递test_id
- 改进了错误处理机制

### 4. 更新Agent执行端点

**文件**: `backend/app/api/v1/endpoints/agents.py`

- **支持**: test_id关联
- **集成**: 变量跟踪与测试记录关联
- **改进**: 流式和同步执行都支持test_id

**主要变更**:
- 修改 `execute_agent` 和 `execute_agent_stream` 端点
- 更新 `extract_and_broadcast_variables` 函数
- 在ConfigDrivenAgent中添加test_id支持

## 数据流程

```
前端发起测试
    ↓
后端生成test_id并创建记录 (/api/v1/test-execution/start)
    ↓
前端获取test_id
    ↓
建立WebSocket连接
    ↓
执行测试 (传递test_id)
    ↓
变量信息实时存储到数据库
    ↓
测试完成，更新最终状态
```

## 测试方法

### 1. 后端测试

运行测试脚本:
```bash
cd backend
python test_refactored_flow.py
```

### 2. 前端测试

打开测试页面:
```
http://localhost:3000/test-refactored-flow.html
```

### 3. 集成测试

1. 启动后端服务:
```bash
cd backend
python -m uvicorn app.main:app --reload --port 8000
```

2. 启动前端服务:
```bash
cd frontend
npm run dev
```

3. 在主应用中测试agent执行功能

## 向后兼容性

- 现有的test_history API仍然可用
- 前端可以继续使用旧的创建方式作为备选
- WebSocket变量跟踪保持现有接口不变

## 错误处理

### 1. 测试启动失败
- 前端显示错误信息并阻止继续执行
- 后端记录详细错误日志

### 2. 变量跟踪失败
- 不影响主要执行流程
- 记录警告日志但继续执行

### 3. 数据库更新失败
- 变量跟踪服务有重试机制
- 失败时记录错误但不中断WebSocket广播

## 配置说明

### 环境变量
无需额外配置，使用现有的数据库和认证设置。

### 数据库字段
test_history表中的以下字段用于存储变量数据:
- `context_placeholders_used`: JSON字段，存储变量占位符使用情况
- `team_member_interactions`: JSON字段，存储团队成员交互数据
- `context_summary`: JSON字段，存储变量统计摘要

## 性能考虑

1. **UUID生成**: 使用Python的uuid模块生成唯一标识符
2. **数据库写入**: 变量更新使用异步写入，不阻塞主流程
3. **WebSocket广播**: 保持现有的高效广播机制

## 监控和日志

- 所有API调用都有详细日志记录
- 变量跟踪事件记录在应用日志中
- 数据库操作有事务保护和错误恢复

## 未来改进

1. **批量变量更新**: 支持一次更新多个变量
2. **变量依赖跟踪**: 跟踪变量之间的依赖关系
3. **性能优化**: 缓存机制和批量写入优化
4. **监控仪表板**: 实时变量跟踪状态监控
