"""add_api_key_usage_statistics

Revision ID: ca7f7cae3f3a
Revises: 094b3c888f10
Create Date: 2025-07-02 05:52:41.270822

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ca7f7cae3f3a'
down_revision = '094b3c888f10'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Skip agents table changes for now due to SQLite limitations
    # op.alter_column('agents', 'uuid',
    #            existing_type=sa.VARCHAR(),
    #            nullable=False)
    # op.create_unique_constraint(None, 'agents', ['uuid'])

    op.add_column('api_keys', sa.Column('requests_today', sa.Integer(), nullable=False, server_default='0'))
    op.add_column('api_keys', sa.Column('requests_month', sa.Integer(), nullable=False, server_default='0'))
    op.add_column('api_keys', sa.Column('cost_today', sa.Float(), nullable=False, server_default='0.0'))
    op.add_column('api_keys', sa.Column('cost_month', sa.Float(), nullable=False, server_default='0.0'))
    op.add_column('api_keys', sa.Column('last_daily_reset', sa.DateTime(), nullable=True))
    op.add_column('api_keys', sa.Column('last_monthly_reset', sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('api_keys', 'last_monthly_reset')
    op.drop_column('api_keys', 'last_daily_reset')
    op.drop_column('api_keys', 'cost_month')
    op.drop_column('api_keys', 'cost_today')
    op.drop_column('api_keys', 'requests_month')
    op.drop_column('api_keys', 'requests_today')

    # Skip agents table changes for now due to SQLite limitations
    # op.drop_constraint(None, 'agents', type_='unique')
    # op.alter_column('agents', 'uuid',
    #            existing_type=sa.VARCHAR(),
    #            nullable=True)
    # ### end Alembic commands ###
