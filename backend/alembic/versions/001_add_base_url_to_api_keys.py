"""Add base_url field to api_keys table

Revision ID: 001_add_base_url
Revises: 
Create Date: 2025-07-02 03:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision = '001_add_base_url'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add base_url column to api_keys table."""
    # Add base_url column to api_keys table (nullable, so safe to add)
    op.add_column('api_keys', sa.Column('base_url', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=True))


def downgrade() -> None:
    """Remove base_url column from api_keys table."""
    op.drop_column('api_keys', 'base_url')
