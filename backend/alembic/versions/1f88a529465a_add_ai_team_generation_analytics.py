"""add_ai_team_generation_analytics

Revision ID: 1f88a529465a
Revises: ddff568d88af
Create Date: 2025-07-03 06:35:33.088144

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision = '1f88a529465a'
down_revision = 'ddff568d88af'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('ai_team_generation_records',
    sa.Column('uuid', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.<PERSON>umn('user_description', sqlmodel.sql.sqltypes.AutoString(length=2000), nullable=False),
    sa.Column('provider', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
    sa.Column('model', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False),
    sa.Column('temperature', sa.Float(), nullable=False),
    sa.Column('max_tokens', sa.Integer(), nullable=False),
    sa.Column('success', sa.Boolean(), nullable=False),
    sa.Column('generation_method', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
    sa.Column('team_plan', sa.JSON(), nullable=True),
    sa.Column('generation_time_seconds', sa.Float(), nullable=True),
    sa.Column('tokens_used', sa.Integer(), nullable=True),
    sa.Column('estimated_cost', sa.Float(), nullable=True),
    sa.Column('error_message', sqlmodel.sql.sqltypes.AutoString(length=1000), nullable=True),
    sa.Column('error_type', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=True),
    sa.Column('quality_score', sa.Float(), nullable=True),
    sa.Column('user_rating', sa.Integer(), nullable=True),
    sa.Column('user_feedback', sqlmodel.sql.sqltypes.AutoString(length=1000), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('uuid')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('ai_team_generation_records')
    # ### end Alembic commands ###
