"""enhance_context_placeholders_semantic_naming

Revision ID: 7ff6cf9864ac
Revises: 23d760aadbd6
Create Date: 2025-07-17 02:58:05.464967

Enhance existing context placeholders to use semantic naming convention
with source_agent_role and semantic_description fields.
"""
from alembic import op
import sqlalchemy as sa
import json


# revision identifiers, used by Alembic.
revision = '7ff6cf9864ac'
down_revision = '23d760aadbd6'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Enhance context placeholders with semantic naming convention."""
    # Get database connection
    connection = op.get_bind()

    # Update existing agents to enhance context placeholders
    result = connection.execute(
        sa.text("SELECT id, team_plan FROM agents WHERE team_plan IS NOT NULL")
    )

    for row in result:
        agent_id, team_plan_json = row

        try:
            # Parse team plan
            if team_plan_json:
                team_plan = json.loads(team_plan_json) if isinstance(team_plan_json, str) else team_plan_json

                # Enhance context placeholders in team members
                if "team_members" in team_plan:
                    for member in team_plan["team_members"]:
                        if "context_placeholders" in member and isinstance(member["context_placeholders"], list):
                            for placeholder in member["context_placeholders"]:
                                if isinstance(placeholder, dict):
                                    # Add source_agent_role if missing
                                    if "source_agent_role" not in placeholder:
                                        placeholder_name = placeholder.get("placeholder_name", "")
                                        if placeholder_name.startswith('{') and placeholder_name.endswith('}'):
                                            content = placeholder_name[1:-1]
                                            if '.' in content:
                                                placeholder["source_agent_role"] = content.split('.')[0]
                                            else:
                                                # Map legacy placeholders to agent roles
                                                legacy_mappings = {
                                                    "user_input": "user",
                                                    "user_requirements": "user",
                                                    "previous_analysis": "analyst",
                                                    "analysis_results": "analyst",
                                                    "content": "content_creator",
                                                    "data": "data_collector"
                                                }
                                                placeholder["source_agent_role"] = legacy_mappings.get(content, "previous_member")
                                        else:
                                            placeholder["source_agent_role"] = "previous_member"

                                    # Add semantic_description if missing
                                    if "semantic_description" not in placeholder:
                                        # Use existing description or create semantic one
                                        existing_desc = placeholder.get("description", "")
                                        if existing_desc and len(existing_desc) >= 10:
                                            placeholder["semantic_description"] = existing_desc
                                        else:
                                            # Generate semantic description based on placeholder name
                                            placeholder_name = placeholder.get("placeholder_name", "")
                                            if "dataset" in placeholder_name.lower():
                                                placeholder["semantic_description"] = "Processed dataset ready for analysis"
                                            elif "analysis" in placeholder_name.lower():
                                                placeholder["semantic_description"] = "Analysis results and insights from data processing"
                                            elif "content" in placeholder_name.lower():
                                                placeholder["semantic_description"] = "Generated or processed content output"
                                            elif "requirements" in placeholder_name.lower():
                                                placeholder["semantic_description"] = "User requirements and specifications"
                                            else:
                                                placeholder["semantic_description"] = "Context information from previous team member"

                # Update the database
                connection.execute(
                    sa.text("UPDATE agents SET team_plan = :team_plan WHERE id = :id"),
                    {"team_plan": json.dumps(team_plan), "id": agent_id}
                )

        except (json.JSONDecodeError, TypeError) as e:
            # Skip agents with invalid JSON
            print(f"Skipping agent {agent_id} due to JSON error: {e}")
            continue

    print("Semantic placeholder enhancement migration completed successfully")


def downgrade() -> None:
    """Remove semantic fields from context placeholders."""
    # Get database connection
    connection = op.get_bind()

    # Remove semantic fields from existing agents
    result = connection.execute(
        sa.text("SELECT id, team_plan FROM agents WHERE team_plan IS NOT NULL")
    )

    for row in result:
        agent_id, team_plan_json = row

        try:
            # Parse team plan
            if team_plan_json:
                team_plan = json.loads(team_plan_json) if isinstance(team_plan_json, str) else team_plan_json

                # Remove semantic fields from context placeholders
                if "team_members" in team_plan:
                    for member in team_plan["team_members"]:
                        if "context_placeholders" in member and isinstance(member["context_placeholders"], list):
                            for placeholder in member["context_placeholders"]:
                                if isinstance(placeholder, dict):
                                    placeholder.pop("source_agent_role", None)
                                    placeholder.pop("semantic_description", None)

                # Update the database
                connection.execute(
                    sa.text("UPDATE agents SET team_plan = :team_plan WHERE id = :id"),
                    {"team_plan": json.dumps(team_plan), "id": agent_id}
                )

        except (json.JSONDecodeError, TypeError) as e:
            # Skip agents with invalid JSON
            print(f"Skipping agent {agent_id} during downgrade due to JSON error: {e}")
            continue

    print("Semantic placeholder enhancement downgrade completed successfully")
