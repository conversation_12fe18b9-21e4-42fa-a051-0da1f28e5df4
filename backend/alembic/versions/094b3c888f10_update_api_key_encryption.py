"""update_api_key_encryption

Revision ID: 094b3c888f10
Revises: 001_add_base_url
Create Date: 2025-07-02 05:42:48.412126

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision = '094b3c888f10'
down_revision = '001_add_base_url'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Skip agents table changes for now due to SQLite limitations
    # op.alter_column('agents', 'uuid',
    #            existing_type=sa.VARCHAR(),
    #            nullable=False)
    # op.create_unique_constraint(None, 'agents', ['uuid'])

    # Add encrypted_key column with default value first
    op.add_column('api_keys', sa.Column('encrypted_key', sqlmodel.sql.sqltypes.AutoString(length=1000), nullable=True))

    # Update existing rows with empty encrypted_key (they will need to be re-created)
    op.execute("UPDATE api_keys SET encrypted_key = '' WHERE encrypted_key IS NULL")

    # Now make it NOT NULL
    # Note: SQLite doesn't support ALTER COLUMN, so we'll handle this in application logic

    op.drop_index(op.f('idx_api_keys_key_hash'), table_name='api_keys')
    op.create_index('idx_api_keys_key_prefix', 'api_keys', ['key_prefix'], unique=False)
    op.drop_column('api_keys', 'key_hash')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('api_keys', sa.Column('key_hash', sa.VARCHAR(length=128), nullable=True))

    # Update existing rows with empty key_hash (they will need to be re-created)
    op.execute("UPDATE api_keys SET key_hash = '' WHERE key_hash IS NULL")

    op.drop_index('idx_api_keys_key_prefix', table_name='api_keys')
    op.create_index(op.f('idx_api_keys_key_hash'), 'api_keys', ['key_hash'], unique=False)
    op.drop_column('api_keys', 'encrypted_key')

    # Skip agents table changes for now due to SQLite limitations
    # op.drop_constraint(None, 'agents', type_='unique')
    # op.alter_column('agents', 'uuid',
    #            existing_type=sa.VARCHAR(),
    #            nullable=True)
    # ### end Alembic commands ###
