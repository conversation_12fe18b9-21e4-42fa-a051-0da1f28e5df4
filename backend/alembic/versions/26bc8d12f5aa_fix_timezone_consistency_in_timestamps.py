"""fix_timezone_consistency_in_timestamps

Revision ID: 26bc8d12f5aa
Revises: 7ff6cf9864ac
Create Date: 2025-07-17 14:37:54.195297

"""
from alembic import op
import sqlalchemy as sa
from datetime import datetime, timezone


# revision identifiers, used by Alembic.
revision = '26bc8d12f5aa'
down_revision = '7ff6cf9864ac'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Fix timezone consistency in timestamp columns."""

    # Get connection to execute raw SQL
    connection = op.get_bind()

    # Fix test_history table timezone inconsistencies
    # Update completed_at timestamps that have timezone info to be naive UTC
    connection.execute(sa.text("""
        UPDATE test_history
        SET completed_at = REPLACE(completed_at, '+00:00', '')
        WHERE completed_at LIKE '%+00:00'
    """))

    # Recalculate execution_duration_ms for records where we can
    # This handles cases where timezone differences caused incorrect calculations
    connection.execute(sa.text("""
        UPDATE test_history
        SET execution_duration_ms = (
            CAST((julianday(completed_at) - julianday(started_at)) * 86400000 AS INTEGER)
        )
        WHERE completed_at IS NOT NULL
        AND started_at IS NOT NULL
        AND (
            execution_duration_ms IS NULL
            OR ABS(execution_duration_ms - (julianday(completed_at) - julianday(started_at)) * 86400000) > 5000
        )
    """))

    # Update any other timestamp fields that might have timezone info
    # This is a safety measure for any other tables that might have similar issues

    # Note: SQLite doesn't have native timezone support, so we normalize everything to naive UTC
    print("✅ Fixed timezone consistency in timestamp columns")


def downgrade() -> None:
    """Downgrade is not supported for this migration as it involves data normalization."""
    # We don't provide a downgrade path because:
    # 1. The original data had inconsistent timezone information
    # 2. Reverting would restore the inconsistent state
    # 3. The changes are data normalization improvements
    pass
