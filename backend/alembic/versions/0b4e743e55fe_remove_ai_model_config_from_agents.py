"""remove_ai_model_config_from_agents

Revision ID: 0b4e743e55fe
Revises: 8143eab6d05b
Create Date: 2025-07-10 09:50:04.972318

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0b4e743e55fe'
down_revision = '8143eab6d05b'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Remove AI model configuration columns from agents table
    # These columns are no longer needed as AI configuration is not stored per agent
    op.drop_column('agents', 'ai_custom_provider_name')
    op.drop_column('agents', 'ai_base_url')
    op.drop_column('agents', 'ai_max_tokens')
    op.drop_column('agents', 'ai_temperature')
    op.drop_column('agents', 'ai_model')
    op.drop_column('agents', 'ai_provider')


def downgrade() -> None:
    # Re-add AI model configuration columns to agents table
    # This reverses the removal of AI configuration columns
    op.add_column('agents', sa.Column('ai_provider', sa.String(length=50), nullable=True))
    op.add_column('agents', sa.Column('ai_model', sa.String(length=100), nullable=True))
    op.add_column('agents', sa.Column('ai_temperature', sa.Float(), nullable=True))
    op.add_column('agents', sa.Column('ai_max_tokens', sa.Integer(), nullable=True))
    op.add_column('agents', sa.Column('ai_base_url', sa.String(length=500), nullable=True))
    op.add_column('agents', sa.Column('ai_custom_provider_name', sa.String(length=100), nullable=True))

    # Set default values for the restored columns
    op.execute("UPDATE agents SET ai_provider = 'openai' WHERE ai_provider IS NULL")
    op.execute("UPDATE agents SET ai_model = 'gpt-4' WHERE ai_model IS NULL")
    op.execute("UPDATE agents SET ai_temperature = 0.7 WHERE ai_temperature IS NULL")
    op.execute("UPDATE agents SET ai_max_tokens = 2000 WHERE ai_max_tokens IS NULL")
