"""Add application logs table

Revision ID: f1a2b3c4d5e6
Revises: 26bc8d12f5aa
Create Date: 2025-07-18 14:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'f1a2b3c4d5e6'
down_revision = '26bc8d12f5aa'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Create application_logs table."""
    op.create_table(
        'application_logs',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uuid', sa.String(), nullable=False),
        sa.Column('level', sa.String(length=20), nullable=False),
        sa.Column('event_type', sa.String(length=50), nullable=False),
        sa.Column('message', sa.Text(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=True),
        sa.Column('session_id', sa.String(length=255), nullable=True),
        sa.Column('request_id', sa.String(length=255), nullable=True),
        sa.Column('source_module', sa.String(length=100), nullable=True),
        sa.Column('source_function', sa.String(length=100), nullable=True),
        sa.Column('source_file', sa.String(length=255), nullable=True),
        sa.Column('source_line', sa.Integer(), nullable=True),
        sa.Column('request_method', sa.String(length=10), nullable=True),
        sa.Column('request_path', sa.String(length=500), nullable=True),
        sa.Column('request_headers', sa.JSON(), nullable=True),
        sa.Column('request_body', sa.JSON(), nullable=True),
        sa.Column('response_status', sa.Integer(), nullable=True),
        sa.Column('response_headers', sa.JSON(), nullable=True),
        sa.Column('response_body', sa.JSON(), nullable=True),
        sa.Column('execution_time_ms', sa.Float(), nullable=True),
        sa.Column('memory_usage_mb', sa.Float(), nullable=True),
        sa.Column('cpu_usage_percent', sa.Float(), nullable=True),
        sa.Column('agent_id', sa.String(length=255), nullable=True),
        sa.Column('test_id', sa.String(length=255), nullable=True),
        sa.Column('template_id', sa.String(length=255), nullable=True),
        sa.Column('api_key_id', sa.Integer(), nullable=True),
        sa.Column('error_code', sa.String(length=50), nullable=True),
        sa.Column('error_type', sa.String(length=100), nullable=True),
        sa.Column('stack_trace', sa.Text(), nullable=True),
        sa.Column('metadata', sa.JSON(), nullable=True),
        sa.Column('tags', sa.JSON(), nullable=True),
        sa.Column('ip_address', sa.String(length=45), nullable=True),
        sa.Column('user_agent', sa.String(length=500), nullable=True),
        sa.Column('timestamp', sa.DateTime(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.UniqueConstraint('uuid')
    )
    
    # Create indexes for efficient querying
    op.create_index('idx_app_logs_user_timestamp', 'application_logs', ['user_id', 'timestamp'])
    op.create_index('idx_app_logs_level_timestamp', 'application_logs', ['level', 'timestamp'])
    op.create_index('idx_app_logs_event_timestamp', 'application_logs', ['event_type', 'timestamp'])
    op.create_index('idx_app_logs_agent_timestamp', 'application_logs', ['agent_id', 'timestamp'])
    op.create_index('idx_app_logs_test_timestamp', 'application_logs', ['test_id', 'timestamp'])
    op.create_index('idx_app_logs_request_id', 'application_logs', ['request_id'])
    op.create_index('idx_app_logs_session_id', 'application_logs', ['session_id'])
    op.create_index('idx_app_logs_source_module', 'application_logs', ['source_module'])
    op.create_index('idx_app_logs_error_code', 'application_logs', ['error_code'])
    op.create_index('idx_app_logs_ip_address', 'application_logs', ['ip_address'])
    op.create_index('idx_app_logs_timestamp', 'application_logs', ['timestamp'])
    op.create_index('idx_app_logs_level', 'application_logs', ['level'])
    op.create_index('idx_app_logs_event_type', 'application_logs', ['event_type'])
    op.create_index('idx_app_logs_uuid', 'application_logs', ['uuid'])


def downgrade() -> None:
    """Drop application_logs table."""
    # Drop indexes first
    op.drop_index('idx_app_logs_uuid', table_name='application_logs')
    op.drop_index('idx_app_logs_event_type', table_name='application_logs')
    op.drop_index('idx_app_logs_level', table_name='application_logs')
    op.drop_index('idx_app_logs_timestamp', table_name='application_logs')
    op.drop_index('idx_app_logs_ip_address', table_name='application_logs')
    op.drop_index('idx_app_logs_error_code', table_name='application_logs')
    op.drop_index('idx_app_logs_source_module', table_name='application_logs')
    op.drop_index('idx_app_logs_session_id', table_name='application_logs')
    op.drop_index('idx_app_logs_request_id', table_name='application_logs')
    op.drop_index('idx_app_logs_test_timestamp', table_name='application_logs')
    op.drop_index('idx_app_logs_agent_timestamp', table_name='application_logs')
    op.drop_index('idx_app_logs_event_timestamp', table_name='application_logs')
    op.drop_index('idx_app_logs_level_timestamp', table_name='application_logs')
    op.drop_index('idx_app_logs_user_timestamp', table_name='application_logs')
    
    # Drop table
    op.drop_table('application_logs')
