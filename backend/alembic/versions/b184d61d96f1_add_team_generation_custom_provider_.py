"""Add team_generation_custom_provider_name field

Revision ID: b184d61d96f1
Revises: 97b0c2fe4ad5
Create Date: 2025-07-03 11:41:53.635147

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'b184d61d96f1'
down_revision = '97b0c2fe4ad5'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('system_settings', sa.Column('team_generation_custom_provider_name', sa.String(length=100), nullable=True))
    # Skip ALTER COLUMN for SQLite compatibility
    # Skip DROP COLUMN for SQLite compatibility
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('system_settings', 'team_generation_custom_provider_name')
    # ### end Alembic commands ###
