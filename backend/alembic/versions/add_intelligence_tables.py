"""Add intelligence and analytics tables

Revision ID: add_intelligence_tables
Revises: 8143eab6d05b
Create Date: 2025-01-14 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision = 'add_intelligence_tables'
down_revision = '8143eab6d05b'
branch_labels = None
depends_on = None


def upgrade():
    """Add intelligence and analytics tables."""
    
    # Create agent_metrics table
    op.create_table('agent_metrics',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('agent_id', sa.String(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('execution_count', sa.Integer(), nullable=True, default=0),
        sa.Column('success_count', sa.Integer(), nullable=True, default=0),
        sa.Column('error_count', sa.Integer(), nullable=True, default=0),
        sa.Column('total_response_time', sa.Float(), nullable=True, default=0.0),
        sa.Column('avg_response_time', sa.Float(), nullable=True, default=0.0),
        sa.Column('p95_response_time', sa.Float(), nullable=True, default=0.0),
        sa.Column('p99_response_time', sa.Float(), nullable=True, default=0.0),
        sa.Column('total_tokens_used', sa.Integer(), nullable=True, default=0),
        sa.Column('total_cost', sa.Float(), nullable=True, default=0.0),
        sa.Column('avg_cost_per_execution', sa.Float(), nullable=True, default=0.0),
        sa.Column('user_satisfaction_score', sa.Float(), nullable=True, default=0.0),
        sa.Column('output_quality_score', sa.Float(), nullable=True, default=0.0),
        sa.Column('uptime_percentage', sa.Float(), nullable=True, default=100.0),
        sa.Column('last_execution_time', sa.DateTime(), nullable=True),
        sa.Column('last_error_time', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['agent_id'], ['agents.agent_id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_agent_metrics_agent_user', 'agent_metrics', ['agent_id', 'user_id'])
    op.create_index('idx_agent_metrics_updated', 'agent_metrics', ['updated_at'])
    op.create_index(op.f('ix_agent_metrics_agent_id'), 'agent_metrics', ['agent_id'])
    op.create_index(op.f('ix_agent_metrics_id'), 'agent_metrics', ['id'])
    op.create_index(op.f('ix_agent_metrics_user_id'), 'agent_metrics', ['user_id'])

    # Create system_metrics table
    op.create_table('system_metrics',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('total_agents', sa.Integer(), nullable=True, default=0),
        sa.Column('active_agents', sa.Integer(), nullable=True, default=0),
        sa.Column('healthy_agents', sa.Integer(), nullable=True, default=0),
        sa.Column('warning_agents', sa.Integer(), nullable=True, default=0),
        sa.Column('critical_agents', sa.Integer(), nullable=True, default=0),
        sa.Column('offline_agents', sa.Integer(), nullable=True, default=0),
        sa.Column('total_executions', sa.Integer(), nullable=True, default=0),
        sa.Column('total_users', sa.Integer(), nullable=True, default=0),
        sa.Column('active_users_24h', sa.Integer(), nullable=True, default=0),
        sa.Column('active_users_7d', sa.Integer(), nullable=True, default=0),
        sa.Column('active_users_30d', sa.Integer(), nullable=True, default=0),
        sa.Column('avg_response_time', sa.Float(), nullable=True, default=0.0),
        sa.Column('system_success_rate', sa.Float(), nullable=True, default=0.0),
        sa.Column('system_error_rate', sa.Float(), nullable=True, default=0.0),
        sa.Column('system_load', sa.Float(), nullable=True, default=0.0),
        sa.Column('total_cost_24h', sa.Float(), nullable=True, default=0.0),
        sa.Column('total_cost_7d', sa.Float(), nullable=True, default=0.0),
        sa.Column('total_cost_30d', sa.Float(), nullable=True, default=0.0),
        sa.Column('timestamp', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_system_metrics_timestamp', 'system_metrics', ['timestamp'])
    op.create_index(op.f('ix_system_metrics_id'), 'system_metrics', ['id'])
    op.create_index(op.f('ix_system_metrics_timestamp'), 'system_metrics', ['timestamp'])

    # Create agent_insights table
    op.create_table('agent_insights',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('insight_id', sa.String(), nullable=True),
        sa.Column('agent_id', sa.String(), nullable=True),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('insight_type', sa.String(), nullable=False),
        sa.Column('severity', sa.String(), nullable=False),
        sa.Column('category', sa.String(), nullable=False),
        sa.Column('title', sa.String(), nullable=False),
        sa.Column('description', sa.Text(), nullable=False),
        sa.Column('recommendation', sa.Text(), nullable=False),
        sa.Column('impact_description', sa.Text(), nullable=True),
        sa.Column('confidence_score', sa.Float(), nullable=True, default=0.0),
        sa.Column('estimated_savings', sa.Float(), nullable=True),
        sa.Column('estimated_improvement', sa.Float(), nullable=True),
        sa.Column('priority_score', sa.Integer(), nullable=True, default=0),
        sa.Column('is_actionable', sa.Boolean(), nullable=True, default=True),
        sa.Column('is_acknowledged', sa.Boolean(), nullable=True, default=False),
        sa.Column('is_resolved', sa.Boolean(), nullable=True, default=False),
        sa.Column('is_dismissed', sa.Boolean(), nullable=True, default=False),
        sa.Column('metrics_data', sa.JSON(), nullable=True),
        sa.Column('analysis_data', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('acknowledged_at', sa.DateTime(), nullable=True),
        sa.Column('resolved_at', sa.DateTime(), nullable=True),
        sa.Column('dismissed_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['agent_id'], ['agents.agent_id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_insights_agent_user', 'agent_insights', ['agent_id', 'user_id'])
    op.create_index('idx_insights_status', 'agent_insights', ['is_acknowledged', 'is_resolved', 'is_dismissed'])
    op.create_index('idx_insights_type_severity', 'agent_insights', ['insight_type', 'severity'])
    op.create_index(op.f('ix_agent_insights_agent_id'), 'agent_insights', ['agent_id'])
    op.create_index(op.f('ix_agent_insights_created_at'), 'agent_insights', ['created_at'])
    op.create_index(op.f('ix_agent_insights_id'), 'agent_insights', ['id'])
    op.create_index(op.f('ix_agent_insights_insight_id'), 'agent_insights', ['insight_id'])
    op.create_index(op.f('ix_agent_insights_user_id'), 'agent_insights', ['user_id'])

    # Create user_behavior_patterns table
    op.create_table('user_behavior_patterns',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('most_used_agents', sa.JSON(), nullable=True),
        sa.Column('preferred_time_slots', sa.JSON(), nullable=True),
        sa.Column('common_workflows', sa.JSON(), nullable=True),
        sa.Column('failure_patterns', sa.JSON(), nullable=True),
        sa.Column('expertise_level', sa.String(), nullable=True, default='beginner'),
        sa.Column('preferred_complexity', sa.String(), nullable=True, default='basic'),
        sa.Column('ui_preferences', sa.JSON(), nullable=True),
        sa.Column('total_sessions', sa.Integer(), nullable=True, default=0),
        sa.Column('avg_session_duration', sa.Float(), nullable=True, default=0.0),
        sa.Column('feature_usage_stats', sa.JSON(), nullable=True),
        sa.Column('help_requests', sa.JSON(), nullable=True),
        sa.Column('completed_tutorials', sa.JSON(), nullable=True),
        sa.Column('skill_assessments', sa.JSON(), nullable=True),
        sa.Column('adaptation_history', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('last_activity', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_behavior_user_activity', 'user_behavior_patterns', ['user_id', 'last_activity'])
    op.create_index(op.f('ix_user_behavior_patterns_id'), 'user_behavior_patterns', ['id'])
    op.create_index(op.f('ix_user_behavior_patterns_user_id'), 'user_behavior_patterns', ['user_id'])

    # Create optimization_history table
    op.create_table('optimization_history',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('optimization_id', sa.String(), nullable=True),
        sa.Column('insight_id', sa.String(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('agent_id', sa.String(), nullable=True),
        sa.Column('optimization_type', sa.String(), nullable=False),
        sa.Column('description', sa.Text(), nullable=False),
        sa.Column('implementation_details', sa.JSON(), nullable=True),
        sa.Column('status', sa.String(), nullable=True, default='pending'),
        sa.Column('applied_at', sa.DateTime(), nullable=True),
        sa.Column('reverted_at', sa.DateTime(), nullable=True),
        sa.Column('baseline_metrics', sa.JSON(), nullable=True),
        sa.Column('outcome_metrics', sa.JSON(), nullable=True),
        sa.Column('actual_savings', sa.Float(), nullable=True),
        sa.Column('actual_improvement', sa.Float(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['agent_id'], ['agents.agent_id'], ),
        sa.ForeignKeyConstraint(['insight_id'], ['agent_insights.insight_id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_optimization_status', 'optimization_history', ['status'])
    op.create_index('idx_optimization_user_agent', 'optimization_history', ['user_id', 'agent_id'])
    op.create_index(op.f('ix_optimization_history_agent_id'), 'optimization_history', ['agent_id'])
    op.create_index(op.f('ix_optimization_history_id'), 'optimization_history', ['id'])
    op.create_index(op.f('ix_optimization_history_insight_id'), 'optimization_history', ['insight_id'])
    op.create_index(op.f('ix_optimization_history_optimization_id'), 'optimization_history', ['optimization_id'])
    op.create_index(op.f('ix_optimization_history_user_id'), 'optimization_history', ['user_id'])

    # Create alert_rules table
    op.create_table('alert_rules',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('rule_id', sa.String(), nullable=True),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('agent_id', sa.String(), nullable=True),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('metric_name', sa.String(), nullable=False),
        sa.Column('operator', sa.String(), nullable=False),
        sa.Column('threshold_value', sa.Float(), nullable=False),
        sa.Column('severity', sa.String(), nullable=False),
        sa.Column('is_enabled', sa.Boolean(), nullable=True, default=True),
        sa.Column('cooldown_minutes', sa.Integer(), nullable=True, default=15),
        sa.Column('notification_channels', sa.JSON(), nullable=True),
        sa.Column('last_triggered', sa.DateTime(), nullable=True),
        sa.Column('trigger_count', sa.Integer(), nullable=True, default=0),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['agent_id'], ['agents.agent_id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_alert_rules_enabled', 'alert_rules', ['is_enabled'])
    op.create_index('idx_alert_rules_user_agent', 'alert_rules', ['user_id', 'agent_id'])
    op.create_index(op.f('ix_alert_rules_agent_id'), 'alert_rules', ['agent_id'])
    op.create_index(op.f('ix_alert_rules_id'), 'alert_rules', ['id'])
    op.create_index(op.f('ix_alert_rules_rule_id'), 'alert_rules', ['rule_id'])
    op.create_index(op.f('ix_alert_rules_user_id'), 'alert_rules', ['user_id'])

    # Create alert_events table
    op.create_table('alert_events',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('event_id', sa.String(), nullable=True),
        sa.Column('rule_id', sa.String(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('agent_id', sa.String(), nullable=True),
        sa.Column('alert_type', sa.String(), nullable=False),
        sa.Column('severity', sa.String(), nullable=False),
        sa.Column('title', sa.String(), nullable=False),
        sa.Column('message', sa.Text(), nullable=False),
        sa.Column('metric_value', sa.Float(), nullable=False),
        sa.Column('threshold_value', sa.Float(), nullable=False),
        sa.Column('trigger_data', sa.JSON(), nullable=True),
        sa.Column('is_acknowledged', sa.Boolean(), nullable=True, default=False),
        sa.Column('is_resolved', sa.Boolean(), nullable=True, default=False),
        sa.Column('acknowledged_by', sa.Integer(), nullable=True),
        sa.Column('resolved_by', sa.Integer(), nullable=True),
        sa.Column('triggered_at', sa.DateTime(), nullable=True),
        sa.Column('acknowledged_at', sa.DateTime(), nullable=True),
        sa.Column('resolved_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['acknowledged_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['agent_id'], ['agents.agent_id'], ),
        sa.ForeignKeyConstraint(['resolved_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['rule_id'], ['alert_rules.rule_id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_alert_events_severity', 'alert_events', ['severity', 'triggered_at'])
    op.create_index('idx_alert_events_status', 'alert_events', ['is_acknowledged', 'is_resolved'])
    op.create_index(op.f('ix_alert_events_agent_id'), 'alert_events', ['agent_id'])
    op.create_index(op.f('ix_alert_events_event_id'), 'alert_events', ['event_id'])
    op.create_index(op.f('ix_alert_events_id'), 'alert_events', ['id'])
    op.create_index(op.f('ix_alert_events_rule_id'), 'alert_events', ['rule_id'])
    op.create_index(op.f('ix_alert_events_triggered_at'), 'alert_events', ['triggered_at'])
    op.create_index(op.f('ix_alert_events_user_id'), 'alert_events', ['user_id'])


def downgrade():
    """Remove intelligence and analytics tables."""
    
    # Drop tables in reverse order due to foreign key constraints
    op.drop_table('alert_events')
    op.drop_table('alert_rules')
    op.drop_table('optimization_history')
    op.drop_table('user_behavior_patterns')
    op.drop_table('agent_insights')
    op.drop_table('system_metrics')
    op.drop_table('agent_metrics')
