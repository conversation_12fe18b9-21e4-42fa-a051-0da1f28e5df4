"""simplify_api_keys

Revision ID: simplify_api_keys
Revises: ca7f7cae3f3a
Create Date: 2025-07-10 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel

# revision identifiers, used by Alembic.
revision = 'simplify_api_keys'
down_revision = 'ca7f7cae3f3a'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Remove provider and base_url fields from api_keys table."""
    # Drop the provider index first
    try:
        op.drop_index('idx_api_keys_provider', table_name='api_keys')
    except Exception:
        # Index might not exist, continue
        pass
    
    # Remove provider and base_url columns
    # Note: SQLite doesn't support DROP COLUMN directly, so we need to recreate the table
    # For production PostgreSQL/MySQL, you would use:
    # op.drop_column('api_keys', 'provider')
    # op.drop_column('api_keys', 'base_url')
    
    # For SQLite, we'll handle this in the application logic by ignoring these fields
    # The columns will remain in the database but won't be used
    pass


def downgrade() -> None:
    """Restore provider and base_url fields to api_keys table."""
    # Add back the provider column with a default value
    # op.add_column('api_keys', sa.Column('provider', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=True))
    # op.add_column('api_keys', sa.Column('base_url', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=True))
    
    # Recreate the provider index
    # op.create_index('idx_api_keys_provider', 'api_keys', ['provider'], unique=False)
    
    # For SQLite compatibility, we'll skip the actual column operations
    pass
