"""Add user_agent_favorites table

Revision ID: add_user_agent_favorites
Revises: 5c5c2969eecb
Create Date: 2024-01-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision = 'add_user_agent_favorites'
down_revision = '5c5c2969eecb'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Create user_agent_favorites table with proper indexes."""
    
    # Create user_agent_favorites table
    op.create_table(
        'user_agent_favorites',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.<PERSON>umn('uuid', sa.String(), nullable=False),
        sa.<PERSON>umn('user_id', sa.Integer(), nullable=False),
        sa.Column('agent_id', sa.String(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        
        # Primary key
        sa.PrimaryKeyConstraint('id'),
        
        # Foreign key constraints
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['agent_id'], ['agents.agent_id'], ondelete='CASCADE'),
        
        # Unique constraint to prevent duplicate favorites
        sa.UniqueConstraint('user_id', 'agent_id', name='uq_user_agent_favorite'),
        sa.UniqueConstraint('uuid', name='uq_user_agent_favorites_uuid')
    )
    
    # Create indexes for performance
    op.create_index('idx_user_agent_favorites_user_id', 'user_agent_favorites', ['user_id'])
    op.create_index('idx_user_agent_favorites_agent_id', 'user_agent_favorites', ['agent_id'])
    op.create_index('idx_user_agent_favorites_created_at', 'user_agent_favorites', ['created_at'])
    op.create_index('idx_user_agent_favorites_uuid', 'user_agent_favorites', ['uuid'], unique=True)
    
    # Composite index for efficient user-specific queries
    op.create_index('idx_user_agent_favorites_user_created', 'user_agent_favorites', ['user_id', 'created_at'])


def downgrade() -> None:
    """Drop user_agent_favorites table and indexes."""
    
    # Drop indexes first
    op.drop_index('idx_user_agent_favorites_user_created', table_name='user_agent_favorites')
    op.drop_index('idx_user_agent_favorites_uuid', table_name='user_agent_favorites')
    op.drop_index('idx_user_agent_favorites_created_at', table_name='user_agent_favorites')
    op.drop_index('idx_user_agent_favorites_agent_id', table_name='user_agent_favorites')
    op.drop_index('idx_user_agent_favorites_user_id', table_name='user_agent_favorites')
    
    # Drop table
    op.drop_table('user_agent_favorites')
