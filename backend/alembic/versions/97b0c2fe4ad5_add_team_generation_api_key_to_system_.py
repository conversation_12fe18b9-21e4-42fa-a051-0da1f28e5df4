"""add_team_generation_api_key_to_system_settings

Revision ID: 97b0c2fe4ad5
Revises: 1f88a529465a
Create Date: 2025-07-03 09:24:56.210921

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision = '97b0c2fe4ad5'
down_revision = '1f88a529465a'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('system_settings', sa.Column('team_generation_api_key', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('system_settings', 'team_generation_api_key')
    # ### end Alembic commands ###
