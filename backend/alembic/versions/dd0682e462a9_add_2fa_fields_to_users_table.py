"""Add 2FA fields to users table

Revision ID: dd0682e462a9
Revises: f1a2b3c4d5e6
Create Date: 2025-07-21 02:23:04.298470

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'dd0682e462a9'
down_revision = 'f1a2b3c4d5e6'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Add 2FA fields to users table
    op.add_column('users', sa.Column('is_2fa_enabled', sa.<PERSON>(), nullable=False, server_default='0'))
    op.add_column('users', sa.Column('totp_secret', sa.String(length=32), nullable=True))
    op.add_column('users', sa.Column('backup_codes', sa.String(length=1000), nullable=True))
    op.add_column('users', sa.Column('two_fa_enabled_at', sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Remove 2FA fields from users table
    op.drop_column('users', 'two_fa_enabled_at')
    op.drop_column('users', 'backup_codes')
    op.drop_column('users', 'totp_secret')
    op.drop_column('users', 'is_2fa_enabled')
    # ### end Alembic commands ###
