"""add_team_generation_settings

Revision ID: d5547d7dca88
Revises: ca7f7cae3f3a
Create Date: 2025-07-03 02:51:01.882434

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd5547d7dca88'
down_revision = 'ca7f7cae3f3a'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('agents', 'uuid',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.create_unique_constraint(None, 'agents', ['uuid'])
    op.add_column('system_settings', sa.Column('team_generation_provider', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False))
    op.add_column('system_settings', sa.Column('team_generation_model', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False))
    op.add_column('system_settings', sa.Column('team_generation_temperature', sa.Float(), nullable=False))
    op.add_column('system_settings', sa.Column('team_generation_max_tokens', sa.Integer(), nullable=True))
    op.add_column('system_settings', sa.Column('enable_ai_team_generation', sa.Boolean(), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('system_settings', 'enable_ai_team_generation')
    op.drop_column('system_settings', 'team_generation_max_tokens')
    op.drop_column('system_settings', 'team_generation_temperature')
    op.drop_column('system_settings', 'team_generation_model')
    op.drop_column('system_settings', 'team_generation_provider')
    op.drop_constraint(None, 'agents', type_='unique')
    op.alter_column('agents', 'uuid',
               existing_type=sa.VARCHAR(),
               nullable=True)
    # ### end Alembic commands ###
