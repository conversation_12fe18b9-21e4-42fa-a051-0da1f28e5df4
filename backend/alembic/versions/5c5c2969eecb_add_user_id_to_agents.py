"""add_user_id_to_agents

Revision ID: 5c5c2969eecb
Revises: b184d61d96f1
Create Date: 2025-07-04 08:40:55.256285

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision = '5c5c2969eecb'
down_revision = 'b184d61d96f1'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    # Clean up old tables that are no longer used
    try:
        op.drop_index(op.f('ix_workflow_steps_agent_id'), table_name='workflow_steps')
        op.drop_table('workflow_steps')
    except:
        pass  # Table might not exist

    try:
        op.drop_index(op.f('ix_team_members_agent_id'), table_name='team_members')
        op.drop_table('team_members')
    except:
        pass  # Table might not exist

    try:
        op.drop_index(op.f('ix_ai_provider_configs_agent_id'), table_name='ai_provider_configs')
        op.drop_table('ai_provider_configs')
    except:
        pass  # Table might not exist

    # Get the first admin user ID to assign to existing agents
    connection = op.get_bind()
    result = connection.execute(sa.text("SELECT id FROM users WHERE role = 'ADMIN' ORDER BY id LIMIT 1"))
    admin_user = result.fetchone()

    if admin_user:
        admin_user_id = admin_user[0]
    else:
        # If no admin user exists, use the first user
        result = connection.execute(sa.text("SELECT id FROM users ORDER BY id LIMIT 1"))
        first_user = result.fetchone()
        admin_user_id = first_user[0] if first_user else 1

    # Add user_id column with default value for existing records
    op.add_column('agents', sa.Column('user_id', sa.Integer(), nullable=False, server_default=str(admin_user_id)))

    # Remove the server default after adding the column
    op.alter_column('agents', 'user_id', server_default=None)

    # Create indexes and constraints
    op.create_index(op.f('ix_agents_agent_id'), 'agents', ['agent_id'], unique=True)
    op.create_index(op.f('ix_agents_user_id'), 'agents', ['user_id'], unique=False)
    op.create_unique_constraint(None, 'agents', ['uuid'])
    op.create_foreign_key(None, 'agents', 'users', ['user_id'], ['id'])

    # Update system settings
    op.alter_column('system_settings', 'team_generation_max_tokens',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.alter_column('system_settings', 'agent_api_base_url',
               existing_type=sa.VARCHAR(length=500),
               nullable=False,
               existing_server_default=sa.text("'http://localhost:8000/api/v1/agents'"))

    try:
        op.drop_column('system_settings', 'team_generation_custom_model')
    except:
        pass  # Column might not exist
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('system_settings', sa.Column('team_generation_custom_model', sa.VARCHAR(length=200), nullable=True))
    op.alter_column('system_settings', 'agent_api_base_url',
               existing_type=sa.VARCHAR(length=500),
               nullable=True,
               existing_server_default=sa.text("'http://localhost:8000/api/v1/agents'"))
    op.alter_column('system_settings', 'team_generation_max_tokens',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.drop_constraint(None, 'agents', type_='foreignkey')
    op.drop_constraint(None, 'agents', type_='unique')
    op.drop_index(op.f('ix_agents_user_id'), table_name='agents')
    op.drop_index(op.f('ix_agents_agent_id'), table_name='agents')
    op.drop_column('agents', 'user_id')
    op.create_table('ai_provider_configs',
    sa.Column('uuid', sa.VARCHAR(), nullable=False),
    sa.Column('created_at', sa.DATETIME(), nullable=False),
    sa.Column('updated_at', sa.DATETIME(), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('provider', sa.VARCHAR(length=10), nullable=False),
    sa.Column('provider_name', sa.VARCHAR(length=100), nullable=False),
    sa.Column('api_key_id', sa.INTEGER(), nullable=True),
    sa.Column('base_url', sa.VARCHAR(length=500), nullable=True),
    sa.Column('default_model', sa.VARCHAR(length=100), nullable=False),
    sa.Column('default_temperature', sa.FLOAT(), nullable=False),
    sa.Column('default_max_tokens', sa.INTEGER(), nullable=True),
    sa.Column('provider_config', sqlite.JSON(), nullable=True),
    sa.Column('is_active', sa.BOOLEAN(), nullable=False),
    sa.Column('is_default', sa.BOOLEAN(), nullable=False),
    sa.Column('config_metadata', sqlite.JSON(), nullable=True),
    sa.Column('agent_id', sa.VARCHAR(), nullable=False),
    sa.ForeignKeyConstraint(['agent_id'], ['agents.agent_id'], ),
    sa.ForeignKeyConstraint(['api_key_id'], ['api_keys.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('uuid')
    )
    op.create_index(op.f('ix_ai_provider_configs_agent_id'), 'ai_provider_configs', ['agent_id'], unique=False)
    op.create_table('team_members',
    sa.Column('uuid', sa.VARCHAR(), nullable=False),
    sa.Column('created_at', sa.DATETIME(), nullable=False),
    sa.Column('updated_at', sa.DATETIME(), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('name', sa.VARCHAR(length=255), nullable=False),
    sa.Column('role', sa.VARCHAR(length=255), nullable=False),
    sa.Column('description', sa.VARCHAR(length=1000), nullable=False),
    sa.Column('system_prompt', sa.TEXT(), nullable=False),
    sa.Column('ai_provider', sa.VARCHAR(length=8), nullable=False),
    sa.Column('model', sa.VARCHAR(length=100), nullable=False),
    sa.Column('temperature', sa.FLOAT(), nullable=False),
    sa.Column('max_tokens', sa.INTEGER(), nullable=True),
    sa.Column('custom_base_url', sa.VARCHAR(length=500), nullable=True),
    sa.Column('capabilities', sqlite.JSON(), nullable=True),
    sa.Column('tools', sqlite.JSON(), nullable=True),
    sa.Column('workflow_position', sa.INTEGER(), nullable=False),
    sa.Column('dependencies', sqlite.JSON(), nullable=True),
    sa.Column('is_active', sa.BOOLEAN(), nullable=False),
    sa.Column('member_metadata', sqlite.JSON(), nullable=True),
    sa.Column('agent_id', sa.VARCHAR(), nullable=False),
    sa.ForeignKeyConstraint(['agent_id'], ['agents.agent_id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('uuid')
    )
    op.create_index(op.f('ix_team_members_agent_id'), 'team_members', ['agent_id'], unique=False)
    op.create_table('workflow_steps',
    sa.Column('uuid', sa.VARCHAR(), nullable=False),
    sa.Column('created_at', sa.DATETIME(), nullable=False),
    sa.Column('updated_at', sa.DATETIME(), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('name', sa.VARCHAR(length=255), nullable=False),
    sa.Column('description', sa.VARCHAR(length=1000), nullable=False),
    sa.Column('step_type', sa.VARCHAR(length=12), nullable=False),
    sa.Column('step_order', sa.INTEGER(), nullable=False),
    sa.Column('assigned_member_ids', sqlite.JSON(), nullable=True),
    sa.Column('timeout_seconds', sa.INTEGER(), nullable=True),
    sa.Column('retry_count', sa.INTEGER(), nullable=False),
    sa.Column('conditions', sqlite.JSON(), nullable=True),
    sa.Column('input_schema', sqlite.JSON(), nullable=True),
    sa.Column('output_schema', sqlite.JSON(), nullable=True),
    sa.Column('is_active', sa.BOOLEAN(), nullable=False),
    sa.Column('step_metadata', sqlite.JSON(), nullable=True),
    sa.Column('agent_id', sa.VARCHAR(), nullable=False),
    sa.ForeignKeyConstraint(['agent_id'], ['agents.agent_id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('uuid')
    )
    op.create_index(op.f('ix_workflow_steps_agent_id'), 'workflow_steps', ['agent_id'], unique=False)
    # ### end Alembic commands ###
