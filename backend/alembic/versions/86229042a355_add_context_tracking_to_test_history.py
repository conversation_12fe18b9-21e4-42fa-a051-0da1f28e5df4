"""add_context_tracking_to_test_history

Revision ID: 86229042a355
Revises: c2fb00bf4dea
Create Date: 2025-07-17 02:32:34.179678

Add context tracking fields to test_history table for monitoring
context-aware agent execution flow and placeholder usage.
"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '86229042a355'
down_revision = 'c2fb00bf4dea'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add context tracking fields to test_history table."""
    # Add new JSON columns for context tracking
    op.add_column('test_history', sa.Column('context_summary', sa.JSON(), nullable=True))
    op.add_column('test_history', sa.Column('context_placeholders_used', sa.JSON(), nullable=True))
    op.add_column('test_history', sa.Column('team_member_interactions', sa.JSON(), nullable=True))


def downgrade() -> None:
    """Remove context tracking fields from test_history table."""
    op.drop_column('test_history', 'team_member_interactions')
    op.drop_column('test_history', 'context_placeholders_used')
    op.drop_column('test_history', 'context_summary')
