"""add_team_generation_base_url_and_custom_model

Revision ID: ddff568d88af
Revises: d5547d7dca88
Create Date: 2025-07-03 03:30:06.958057

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ddff568d88af'
down_revision = 'd5547d7dca88'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('agents', 'uuid',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.create_unique_constraint(None, 'agents', ['uuid'])
    op.add_column('system_settings', sa.Column('team_generation_base_url', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=True))
    op.add_column('system_settings', sa.Column('team_generation_custom_model', sqlmodel.sql.sqltypes.AutoString(length=200), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('system_settings', 'team_generation_custom_model')
    op.drop_column('system_settings', 'team_generation_base_url')
    op.drop_constraint(None, 'agents', type_='unique')
    op.alter_column('agents', 'uuid',
               existing_type=sa.VARCHAR(),
               nullable=True)
    # ### end Alembic commands ###
