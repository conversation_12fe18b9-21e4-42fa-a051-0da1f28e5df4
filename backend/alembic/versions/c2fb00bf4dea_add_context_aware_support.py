"""add_context_aware_support

Revision ID: c2fb00bf4dea
Revises: 5d922fa0d3c7
Create Date: 2025-07-17 02:21:05.452441

This migration ensures existing agent team plans are compatible with
context-aware execution by adding context_placeholders and context_dependencies
fields to existing JSON structures.
"""
from alembic import op
import sqlalchemy as sa
import json


# revision identifiers, used by Alembic.
revision = 'c2fb00bf4dea'
down_revision = '5d922fa0d3c7'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add context-aware support to existing agent team plans."""
    # Get database connection
    connection = op.get_bind()

    # Update existing agents to ensure context compatibility
    result = connection.execute(
        sa.text("SELECT id, team_plan, team_members FROM agents WHERE team_plan IS NOT NULL")
    )

    for row in result:
        agent_id, team_plan_json, team_members_json = row

        try:
            # Parse team plan
            if team_plan_json:
                team_plan = json.loads(team_plan_json) if isinstance(team_plan_json, str) else team_plan_json

                # Ensure team members have context_placeholders
                if "team_members" in team_plan:
                    for member in team_plan["team_members"]:
                        if "context_placeholders" not in member:
                            member["context_placeholders"] = []

                # Ensure workflow steps have context_dependencies
                if "workflow" in team_plan and "steps" in team_plan["workflow"]:
                    for step in team_plan["workflow"]["steps"]:
                        if "context_dependencies" not in step:
                            step["context_dependencies"] = []

                # Update the database
                connection.execute(
                    sa.text("UPDATE agents SET team_plan = :team_plan WHERE id = :id"),
                    {"team_plan": json.dumps(team_plan), "id": agent_id}
                )

            # Update team_members JSON if it exists separately
            if team_members_json:
                team_members = json.loads(team_members_json) if isinstance(team_members_json, str) else team_members_json

                if isinstance(team_members, list):
                    for member in team_members:
                        if isinstance(member, dict) and "context_placeholders" not in member:
                            member["context_placeholders"] = []

                    # Update the database
                    connection.execute(
                        sa.text("UPDATE agents SET team_members = :team_members WHERE id = :id"),
                        {"team_members": json.dumps(team_members), "id": agent_id}
                    )

        except (json.JSONDecodeError, TypeError) as e:
            # Skip agents with invalid JSON
            print(f"Skipping agent {agent_id} due to JSON error: {e}")
            continue

    print("Context-aware support migration completed successfully")


def downgrade() -> None:
    """Remove context-aware fields from agent team plans."""
    # Get database connection
    connection = op.get_bind()

    # Remove context fields from existing agents
    result = connection.execute(
        sa.text("SELECT id, team_plan, team_members FROM agents WHERE team_plan IS NOT NULL")
    )

    for row in result:
        agent_id, team_plan_json, team_members_json = row

        try:
            # Parse team plan
            if team_plan_json:
                team_plan = json.loads(team_plan_json) if isinstance(team_plan_json, str) else team_plan_json

                # Remove context_placeholders from team members
                if "team_members" in team_plan:
                    for member in team_plan["team_members"]:
                        member.pop("context_placeholders", None)

                # Remove context_dependencies from workflow steps
                if "workflow" in team_plan and "steps" in team_plan["workflow"]:
                    for step in team_plan["workflow"]["steps"]:
                        step.pop("context_dependencies", None)

                # Update the database
                connection.execute(
                    sa.text("UPDATE agents SET team_plan = :team_plan WHERE id = :id"),
                    {"team_plan": json.dumps(team_plan), "id": agent_id}
                )

            # Update team_members JSON if it exists separately
            if team_members_json:
                team_members = json.loads(team_members_json) if isinstance(team_members_json, str) else team_members_json

                if isinstance(team_members, list):
                    for member in team_members:
                        if isinstance(member, dict):
                            member.pop("context_placeholders", None)

                    # Update the database
                    connection.execute(
                        sa.text("UPDATE agents SET team_members = :team_members WHERE id = :id"),
                        {"team_members": json.dumps(team_members), "id": agent_id}
                    )

        except (json.JSONDecodeError, TypeError) as e:
            # Skip agents with invalid JSON
            print(f"Skipping agent {agent_id} during downgrade due to JSON error: {e}")
            continue

    print("Context-aware support downgrade completed successfully")
