"""add_test_history_table

Revision ID: a6f9d0c52021
Revises: 0b4e743e55fe
Create Date: 2025-07-10 14:06:34.761396

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision = 'a6f9d0c52021'
down_revision = '0b4e743e55fe'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    # Create test_history table
    op.create_table('test_history',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('test_id', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('agent_id', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column('status', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column('started_at', sa.DateTime(), nullable=False),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.Column('execution_duration_ms', sa.Integer(), nullable=True),
        sa.Column('ai_config_override', sa.JSON(), nullable=True),
        sa.Column('api_key_id', sa.Integer(), nullable=True),
        sa.Column('api_key_name', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column('input_text', sa.Text(), nullable=False),
        sa.Column('input_metadata', sa.JSON(), nullable=True),
        sa.Column('execution_stages', sa.JSON(), nullable=True),
        sa.Column('progress_updates', sa.JSON(), nullable=True),
        sa.Column('final_output', sa.Text(), nullable=True),
        sa.Column('response_metadata', sa.JSON(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('error_details', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_test_history_test_id'), 'test_history', ['test_id'], unique=True)
    op.create_index(op.f('ix_test_history_user_id'), 'test_history', ['user_id'], unique=False)
    op.create_index(op.f('ix_test_history_agent_id'), 'test_history', ['agent_id'], unique=False)

    # Clean up old tables and columns
    op.drop_table('ai_team_generation_records')
    op.alter_column('planning_requests', 'team_plan_json',
               existing_type=sa.TEXT(),
               type_=sqlmodel.sql.sqltypes.AutoString(),
               existing_nullable=True)
    op.drop_column('system_settings', 'team_generation_custom_model')
    op.drop_column('templates', 'expected_output')
    op.drop_column('templates', 'example_input')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    # Drop test_history table
    op.drop_index(op.f('ix_test_history_agent_id'), table_name='test_history')
    op.drop_index(op.f('ix_test_history_user_id'), table_name='test_history')
    op.drop_index(op.f('ix_test_history_test_id'), table_name='test_history')
    op.drop_table('test_history')

    # Restore old columns and tables
    op.add_column('templates', sa.Column('example_input', sa.VARCHAR(length=1000), nullable=True))
    op.add_column('templates', sa.Column('expected_output', sa.VARCHAR(length=1000), nullable=True))
    op.add_column('system_settings', sa.Column('team_generation_custom_model', sa.VARCHAR(length=200), nullable=True))
    op.alter_column('planning_requests', 'team_plan_json',
               existing_type=sqlmodel.sql.sqltypes.AutoString(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.create_table('ai_team_generation_records',
    sa.Column('id', sa.INTEGER(), nullable=True),
    sa.Column('uuid', sa.VARCHAR(), nullable=False),
    sa.Column('created_at', sa.DATETIME(), nullable=False),
    sa.Column('updated_at', sa.DATETIME(), nullable=True),
    sa.Column('user_id', sa.INTEGER(), nullable=False),
    sa.Column('user_description', sa.VARCHAR(length=2000), nullable=False),
    sa.Column('provider', sa.VARCHAR(length=50), nullable=False),
    sa.Column('model', sa.VARCHAR(length=100), nullable=False),
    sa.Column('temperature', sa.FLOAT(), nullable=False),
    sa.Column('max_tokens', sa.INTEGER(), nullable=False),
    sa.Column('success', sa.BOOLEAN(), nullable=False),
    sa.Column('generation_method', sa.VARCHAR(length=50), nullable=False),
    sa.Column('team_plan', sqlite.JSON(), nullable=True),
    sa.Column('generation_time_seconds', sa.FLOAT(), nullable=True),
    sa.Column('tokens_used', sa.INTEGER(), nullable=True),
    sa.Column('estimated_cost', sa.FLOAT(), nullable=True),
    sa.Column('error_message', sa.VARCHAR(length=1000), nullable=True),
    sa.Column('error_type', sa.VARCHAR(length=100), nullable=True),
    sa.Column('quality_score', sa.FLOAT(), nullable=True),
    sa.Column('user_rating', sa.INTEGER(), nullable=True),
    sa.Column('user_feedback', sa.VARCHAR(length=1000), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('uuid')
    )
    # ### end Alembic commands ###
