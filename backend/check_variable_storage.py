#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to check variable data storage in the database.

This script directly queries the database to verify that variable tracking
data is being stored correctly in the test_history table.
"""

import asyncio
import json
import sys
import os
from datetime import datetime

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def check_recent_test_records():
    """Check recent test records for variable data."""
    try:
        from app.core.database import AsyncSessionLocal
        from sqlalchemy import text
        
        print("🔍 Checking recent test records for variable data...\n")
        
        async with AsyncSessionLocal() as db:
            # Get recent test records
            query = """
            SELECT test_id, agent_id, status, started_at, 
                   context_placeholders_used, team_member_interactions, context_summary
            FROM test_history 
            ORDER BY started_at DESC 
            LIMIT 10
            """
            
            result = await db.execute(text(query))
            records = result.fetchall()
            
            if not records:
                print("❌ No test records found in database")
                return
            
            print(f"📊 Found {len(records)} recent test records:\n")
            
            for i, record in enumerate(records, 1):
                print(f"🧪 Test {i}: {record.test_id}")
                print(f"   Agent: {record.agent_id}")
                print(f"   Status: {record.status}")
                print(f"   Started: {record.started_at}")
                
                # Check variable data
                has_variable_data = False
                
                if record.context_placeholders_used:
                    try:
                        placeholders = json.loads(record.context_placeholders_used) if isinstance(record.context_placeholders_used, str) else record.context_placeholders_used
                        print(f"   ✅ Context placeholders: {len(placeholders)} items")
                        has_variable_data = True
                    except (json.JSONDecodeError, TypeError):
                        print(f"   ⚠️ Context placeholders: Invalid JSON data")
                
                if record.team_member_interactions:
                    try:
                        interactions = json.loads(record.team_member_interactions) if isinstance(record.team_member_interactions, str) else record.team_member_interactions
                        print(f"   ✅ Team interactions: {len(interactions)} items")
                        has_variable_data = True
                    except (json.JSONDecodeError, TypeError):
                        print(f"   ⚠️ Team interactions: Invalid JSON data")
                
                if record.context_summary:
                    try:
                        summary = json.loads(record.context_summary) if isinstance(record.context_summary, str) else record.context_summary
                        print(f"   ✅ Context summary: {summary}")
                        has_variable_data = True
                    except (json.JSONDecodeError, TypeError):
                        print(f"   ⚠️ Context summary: Invalid JSON data")
                
                if not has_variable_data:
                    print(f"   ❌ No variable data found")
                
                print()
            
            # Summary
            records_with_variables = sum(1 for r in records if r.context_placeholders_used or r.team_member_interactions or r.context_summary)
            print(f"📈 Summary: {records_with_variables}/{len(records)} records have variable data")
            
            if records_with_variables == 0:
                print("\n⚠️ No variable data found in recent tests!")
                print("This might indicate:")
                print("1. Variable tracking is not being triggered")
                print("2. Database updates are failing")
                print("3. Tests haven't been run with the new system")
            else:
                print(f"\n✅ Variable tracking appears to be working!")
                
    except Exception as e:
        print(f"❌ Error checking database: {str(e)}")

async def check_specific_test(test_id: str):
    """Check a specific test record for variable data."""
    try:
        from app.core.database import AsyncSessionLocal
        from sqlalchemy import text
        
        print(f"🔍 Checking test {test_id} for variable data...\n")
        
        async with AsyncSessionLocal() as db:
            query = """
            SELECT test_id, agent_id, status, started_at, completed_at,
                   context_placeholders_used, team_member_interactions, context_summary,
                   input_text, final_output
            FROM test_history 
            WHERE test_id = :test_id
            """
            
            result = await db.execute(text(query), {"test_id": test_id})
            record = result.fetchone()
            
            if not record:
                print(f"❌ Test {test_id} not found in database")
                return
            
            print(f"📋 Test Details:")
            print(f"   ID: {record.test_id}")
            print(f"   Agent: {record.agent_id}")
            print(f"   Status: {record.status}")
            print(f"   Started: {record.started_at}")
            print(f"   Completed: {record.completed_at}")
            print(f"   Input: {record.input_text[:100]}..." if record.input_text else "   Input: None")
            print(f"   Output: {record.final_output[:100]}..." if record.final_output else "   Output: None")
            print()
            
            # Detailed variable data analysis
            print("🔍 Variable Data Analysis:")
            
            if record.context_placeholders_used:
                try:
                    placeholders = json.loads(record.context_placeholders_used) if isinstance(record.context_placeholders_used, str) else record.context_placeholders_used
                    print(f"   ✅ Context Placeholders ({len(placeholders)} items):")
                    for i, placeholder in enumerate(placeholders[:3], 1):  # Show first 3
                        print(f"      {i}. {placeholder.get('variable_name', 'Unknown')}: {placeholder.get('variable_value', 'No value')[:50]}...")
                    if len(placeholders) > 3:
                        print(f"      ... and {len(placeholders) - 3} more")
                except (json.JSONDecodeError, TypeError) as e:
                    print(f"   ❌ Context placeholders: JSON decode error - {e}")
            else:
                print("   ❌ No context placeholders found")
            
            if record.team_member_interactions:
                try:
                    interactions = json.loads(record.team_member_interactions) if isinstance(record.team_member_interactions, str) else record.team_member_interactions
                    print(f"   ✅ Team Interactions ({len(interactions)} items):")
                    for i, interaction in enumerate(interactions[:3], 1):  # Show first 3
                        print(f"      {i}. {interaction.get('source_agent', 'Unknown')} → {interaction.get('destination_agents', [])}")
                    if len(interactions) > 3:
                        print(f"      ... and {len(interactions) - 3} more")
                except (json.JSONDecodeError, TypeError) as e:
                    print(f"   ❌ Team interactions: JSON decode error - {e}")
            else:
                print("   ❌ No team interactions found")
            
            if record.context_summary:
                try:
                    summary = json.loads(record.context_summary) if isinstance(record.context_summary, str) else record.context_summary
                    print(f"   ✅ Context Summary:")
                    print(f"      Total variables: {summary.get('total_variables', 0)}")
                    print(f"      Resolved variables: {summary.get('resolved_variables', 0)}")
                    print(f"      Variable types: {summary.get('variable_types', {})}")
                except (json.JSONDecodeError, TypeError) as e:
                    print(f"   ❌ Context summary: JSON decode error - {e}")
            else:
                print("   ❌ No context summary found")
                
    except Exception as e:
        print(f"❌ Error checking specific test: {str(e)}")

async def main():
    """Main function."""
    print("🗄️ Variable Storage Database Checker\n")
    
    if len(sys.argv) > 1:
        # Check specific test ID
        test_id = sys.argv[1]
        await check_specific_test(test_id)
    else:
        # Check recent records
        await check_recent_test_records()
    
    print("\n💡 Usage:")
    print("   python check_variable_storage.py                    # Check recent records")
    print("   python check_variable_storage.py <test_id>          # Check specific test")

if __name__ == "__main__":
    asyncio.run(main())
