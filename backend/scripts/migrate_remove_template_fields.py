#!/usr/bin/env python3
"""
数据库迁移脚本：移除模板表中的 example_input 和 expected_output 字段

这个脚本会安全地从 templates 表中删除 example_input 和 expected_output 列，
同时保留所有其他数据。

使用方法:
    cd backend
    python scripts/migrate_remove_template_fields.py
"""

import asyncio
import sqlite3
import os
import sys
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from app.core.database import AsyncSessionLocal, engine
from app.core.logging import database_logger
from sqlalchemy import text


async def backup_database():
    """创建数据库备份"""
    try:
        # 获取数据库文件路径
        db_path = "data/meta_agent.db"
        if not os.path.exists(db_path):
            print("❌ 数据库文件不存在，无需迁移")
            return False
            
        # 创建备份
        backup_path = f"data/meta_agent_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        
        # 使用 SQLite 的备份功能
        source = sqlite3.connect(db_path)
        backup = sqlite3.connect(backup_path)
        source.backup(backup)
        source.close()
        backup.close()
        
        print(f"✅ 数据库备份已创建: {backup_path}")
        return True
        
    except Exception as e:
        print(f"❌ 创建数据库备份失败: {e}")
        return False


async def check_columns_exist():
    """检查要删除的列是否存在"""
    try:
        async with AsyncSessionLocal() as session:
            # 检查表结构
            result = await session.execute(text("PRAGMA table_info(templates)"))
            columns = result.fetchall()
            
            column_names = [col[1] for col in columns]
            
            has_example_input = "example_input" in column_names
            has_expected_output = "expected_output" in column_names
            
            print(f"📋 当前 templates 表列数: {len(column_names)}")
            print(f"🔍 example_input 列存在: {has_example_input}")
            print(f"🔍 expected_output 列存在: {has_expected_output}")
            
            return has_example_input or has_expected_output
            
    except Exception as e:
        print(f"❌ 检查表结构失败: {e}")
        return False


async def get_template_count():
    """获取模板数量"""
    try:
        async with AsyncSessionLocal() as session:
            result = await session.execute(text("SELECT COUNT(*) FROM templates"))
            count = result.scalar()
            print(f"📊 当前模板数量: {count}")
            return count
    except Exception as e:
        print(f"❌ 获取模板数量失败: {e}")
        return 0


async def migrate_remove_fields():
    """执行迁移：移除 example_input 和 expected_output 字段"""
    try:
        async with AsyncSessionLocal() as session:
            print("🔄 开始迁移过程...")
            
            # 步骤1: 创建新的临时表（不包含要删除的字段）
            create_temp_table_sql = """
            CREATE TABLE templates_new (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                uuid TEXT NOT NULL UNIQUE,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP,
                template_id TEXT NOT NULL UNIQUE,
                name TEXT NOT NULL,
                description TEXT NOT NULL,
                category TEXT NOT NULL,
                difficulty TEXT NOT NULL,
                user_id INTEGER,
                visibility TEXT NOT NULL DEFAULT 'private',
                status TEXT NOT NULL DEFAULT 'active',
                prompt_template TEXT NOT NULL,
                team_structure_template TEXT NOT NULL,
                default_config TEXT NOT NULL DEFAULT '{}',
                tags TEXT NOT NULL DEFAULT '[]',
                keywords TEXT NOT NULL DEFAULT '[]',
                version TEXT NOT NULL DEFAULT '1.0.0',
                parent_template_id TEXT,
                source_agent_id TEXT,
                usage_count INTEGER NOT NULL DEFAULT 0,
                rating REAL,
                rating_count INTEGER NOT NULL DEFAULT 0,
                use_case TEXT,
                is_active BOOLEAN NOT NULL DEFAULT 1,
                is_featured BOOLEAN NOT NULL DEFAULT 0,
                author TEXT,
                author_name TEXT,
                template_metadata TEXT NOT NULL DEFAULT '{}',
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
            """
            
            await session.execute(text(create_temp_table_sql))
            print("✅ 创建临时表成功")
            
            # 步骤2: 复制数据到新表（排除要删除的字段）
            copy_data_sql = """
            INSERT INTO templates_new (
                id, uuid, created_at, updated_at, template_id, name, description,
                category, difficulty, user_id, visibility, status, prompt_template,
                team_structure_template, default_config, tags, keywords, version,
                parent_template_id, source_agent_id, usage_count, rating, rating_count,
                use_case, is_active, is_featured, author, author_name, template_metadata
            )
            SELECT 
                id, uuid, created_at, updated_at, template_id, name, description,
                category, difficulty, user_id, visibility, status, prompt_template,
                team_structure_template, default_config, tags, keywords, version,
                parent_template_id, source_agent_id, usage_count, rating, rating_count,
                use_case, is_active, is_featured, author, author_name, template_metadata
            FROM templates
            """
            
            await session.execute(text(copy_data_sql))
            print("✅ 数据复制成功")
            
            # 步骤3: 删除原表
            await session.execute(text("DROP TABLE templates"))
            print("✅ 删除原表成功")
            
            # 步骤4: 重命名新表
            await session.execute(text("ALTER TABLE templates_new RENAME TO templates"))
            print("✅ 重命名新表成功")
            
            # 步骤5: 重建索引
            index_sqls = [
                "CREATE INDEX idx_templates_user_id ON templates(user_id)",
                "CREATE INDEX idx_templates_category ON templates(category)",
                "CREATE INDEX idx_templates_visibility ON templates(visibility)",
                "CREATE INDEX idx_templates_status ON templates(status)",
                "CREATE INDEX idx_templates_difficulty ON templates(difficulty)",
                "CREATE INDEX idx_templates_parent_template_id ON templates(parent_template_id)",
                "CREATE INDEX idx_templates_source_agent_id ON templates(source_agent_id)",
                "CREATE INDEX idx_templates_usage_count ON templates(usage_count)",
                "CREATE INDEX idx_templates_rating ON templates(rating)"
            ]
            
            for index_sql in index_sqls:
                try:
                    await session.execute(text(index_sql))
                except Exception as e:
                    print(f"⚠️  创建索引时出现警告: {e}")
            
            print("✅ 重建索引完成")
            
            # 提交事务
            await session.commit()
            print("✅ 迁移事务提交成功")
            
            return True
            
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        await session.rollback()
        return False


async def verify_migration():
    """验证迁移结果"""
    try:
        async with AsyncSessionLocal() as session:
            # 检查新表结构
            result = await session.execute(text("PRAGMA table_info(templates)"))
            columns = result.fetchall()
            
            column_names = [col[1] for col in columns]
            
            has_example_input = "example_input" in column_names
            has_expected_output = "expected_output" in column_names
            
            print(f"🔍 迁移后检查:")
            print(f"   - example_input 列存在: {has_example_input}")
            print(f"   - expected_output 列存在: {has_expected_output}")
            print(f"   - 总列数: {len(column_names)}")
            
            # 检查数据完整性
            result = await session.execute(text("SELECT COUNT(*) FROM templates"))
            count = result.scalar()
            print(f"   - 模板数量: {count}")
            
            if not has_example_input and not has_expected_output:
                print("✅ 迁移验证成功：目标字段已成功移除")
                return True
            else:
                print("❌ 迁移验证失败：目标字段仍然存在")
                return False
                
    except Exception as e:
        print(f"❌ 迁移验证失败: {e}")
        return False


async def main():
    """主函数"""
    print("🚀 开始模板字段移除迁移...")
    print("=" * 50)
    
    # 检查是否需要迁移
    if not await check_columns_exist():
        print("ℹ️  目标字段不存在，无需迁移")
        return
    
    # 获取当前数据统计
    await get_template_count()
    
    # 创建备份
    if not await backup_database():
        print("❌ 备份失败，迁移中止")
        return
    
    # 确认迁移
    print("\n⚠️  即将执行以下操作:")
    print("   1. 从 templates 表中移除 example_input 列")
    print("   2. 从 templates 表中移除 expected_output 列")
    print("   3. 保留所有其他数据和结构")
    
    confirm = input("\n是否继续？(y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 用户取消迁移")
        return
    
    # 执行迁移
    if await migrate_remove_fields():
        # 验证迁移
        if await verify_migration():
            print("\n🎉 迁移成功完成！")
            print("✅ example_input 和 expected_output 字段已从模板表中移除")
            print("✅ 所有其他数据保持完整")
        else:
            print("\n❌ 迁移验证失败，请检查数据库状态")
    else:
        print("\n❌ 迁移失败，数据库已回滚")


if __name__ == "__main__":
    asyncio.run(main())
