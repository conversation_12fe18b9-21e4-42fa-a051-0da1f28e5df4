#!/usr/bin/env python3
"""
Safe test runner that avoids database index conflicts.
"""

import argparse
import subprocess
import sys
import os
from pathlib import Path


def run_command(cmd, description=""):
    """Run a command and return the result."""
    print(f"🚀 {description}")
    print(f"📝 Command: {' '.join(cmd)}")
    print("-" * 60)
    
    result = subprocess.run(cmd, capture_output=False)
    
    if result.returncode == 0:
        print(f"✅ {description} - PASSED")
    else:
        print(f"❌ {description} - FAILED")
    
    print("-" * 60)
    return result.returncode


def main():
    parser = argparse.ArgumentParser(description="Safe test runner for Meta-Agent backend")
    parser.add_argument(
        "--type", 
        choices=["unit", "integration", "performance", "all", "fast", "coverage"],
        default="fast",
        help="Type of tests to run"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Verbose output"
    )
    parser.add_argument(
        "--parallel", "-p",
        action="store_true",
        help="Run tests in parallel"
    )
    parser.add_argument(
        "--include-database",
        action="store_true",
        help="Include database tests (may have index conflicts)"
    )
    
    args = parser.parse_args()
    
    # Change to backend directory
    backend_dir = Path(__file__).parent.parent
    os.chdir(backend_dir)
    
    # Base pytest command
    base_cmd = ["python", "-m", "pytest"]
    
    # Add verbosity
    if args.verbose:
        base_cmd.append("-v")
    
    # Add parallel execution
    if args.parallel:
        base_cmd.extend(["-n", "auto"])
    
    # Define test filters
    safe_filter = "not database_operations and not test_simple_database_creation and not test_memory_database and not index_conflict"
    
    if not args.include_database:
        base_cmd.extend(["-k", safe_filter])
    
    # Test type specific commands
    test_commands = {
        "unit": {
            "cmd": base_cmd + ["tests/unit/"],
            "description": "Running unit tests"
        },
        "integration": {
            "cmd": base_cmd + ["tests/integration/"],
            "description": "Running integration tests"
        },
        "performance": {
            "cmd": base_cmd + ["tests/performance/"],
            "description": "Running performance tests"
        },
        "fast": {
            "cmd": base_cmd + ["tests/", "-m", "not slow"],
            "description": "Running fast tests"
        },
        "coverage": {
            "cmd": base_cmd + [
                "tests/",
                "--cov=app",
                "--cov-report=term-missing",
                "--cov-report=html:htmlcov",
                "--cov-report=xml:coverage.xml"
            ],
            "description": "Running tests with coverage"
        },
        "all": {
            "cmd": base_cmd + ["tests/"],
            "description": "Running all tests"
        }
    }
    
    # Run the selected test type
    test_config = test_commands[args.type]
    exit_code = run_command(test_config["cmd"], test_config["description"])
    
    # Summary
    print("\n" + "=" * 60)
    if exit_code == 0:
        print("🎉 All tests passed!")
        if not args.include_database:
            print("ℹ️  Database tests were excluded to avoid index conflicts")
            print("   Use --include-database to run them (may fail)")
    else:
        print("💥 Some tests failed!")
    print("=" * 60)
    
    # Additional information
    if args.type == "coverage":
        print("\n📊 Coverage report generated:")
        print("   - Terminal: See above output")
        print("   - HTML: Open htmlcov/index.html")
        print("   - XML: coverage.xml")
    
    return exit_code


if __name__ == "__main__":
    sys.exit(main())
