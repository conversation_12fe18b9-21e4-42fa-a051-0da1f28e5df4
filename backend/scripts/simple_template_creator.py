#!/usr/bin/env python3
"""
简化的模板创建脚本
直接创建表和插入模板数据
"""

import sqlite3
import json
import uuid
from datetime import datetime, timezone
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.services.featured_templates import FeaturedTemplatesCollection
from app.services.featured_templates_part2 import create_product_development_team, create_education_training_team
from app.services.featured_templates_part3 import create_legal_consulting_team, create_medical_diagnosis_team


def create_tables(conn):
    """创建必要的表"""
    
    # 创建用户表
    conn.execute("""
        CREATE TABLE IF NOT EXISTS users (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            hashed_password TEXT NOT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            is_admin BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # 创建模板表
    conn.execute("""
        CREATE TABLE IF NOT EXISTS templates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            template_id TEXT UNIQUE NOT NULL,
            name TEXT NOT NULL,
            description TEXT NOT NULL,
            category TEXT NOT NULL,
            difficulty TEXT NOT NULL,
            visibility TEXT NOT NULL,
            status TEXT NOT NULL,
            prompt_template TEXT,
            team_structure_template TEXT,
            default_config TEXT,
            tags TEXT,
            keywords TEXT,
            use_case TEXT,
            example_input TEXT,
            expected_output TEXT,
            template_metadata TEXT,
            user_id TEXT NOT NULL,
            author_name TEXT NOT NULL,
            usage_count INTEGER DEFAULT 0,
            rating REAL DEFAULT 0.0,
            rating_count INTEGER DEFAULT 0,
            version TEXT DEFAULT '1.0.0',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    """)
    
    conn.commit()
    print("✅ 数据库表创建完成")


def create_system_user(conn):
    """创建系统用户"""
    user_id = str(uuid.uuid4())
    
    try:
        conn.execute("""
            INSERT INTO users (id, name, email, hashed_password, is_active, is_admin, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            user_id,
            "System",
            "<EMAIL>",
            "system_user_no_login",
            True,
            True,
            datetime.now(timezone.utc).isoformat()
        ))
        conn.commit()
        print("✅ 系统用户创建完成")
        return user_id
    except sqlite3.IntegrityError:
        # 用户已存在，获取现有用户ID
        cursor = conn.execute("SELECT id FROM users WHERE email = ?", ("<EMAIL>",))
        result = cursor.fetchone()
        if result:
            print("✅ 系统用户已存在")
            return result[0]
        else:
            raise Exception("无法创建或获取系统用户")


def validate_template_quality(template_config):
    """验证模板质量"""
    issues = []
    
    # 检查基本信息
    if not template_config.get("name", "").strip():
        issues.append("缺少模板名称")
    
    if not template_config.get("description", "").strip():
        issues.append("缺少模板描述")
    elif len(template_config["description"]) < 30:
        issues.append("模板描述过短")
    
    # 检查团队结构
    team_structure = template_config.get("team_structure_template", {})
    if not team_structure:
        issues.append("缺少团队结构配置")
        return False, issues
    
    # 检查团队成员
    team_members = team_structure.get("team_members", [])
    if not team_members:
        issues.append("缺少团队成员")
    else:
        for i, member in enumerate(team_members):
            if not member.get("name", "").strip():
                issues.append(f"成员 {i+1} 缺少名称")
            if not member.get("system_prompt", "").strip():
                issues.append(f"成员 {i+1} 缺少系统提示词")
            elif len(member["system_prompt"]) < 100:
                issues.append(f"成员 {i+1} 系统提示词过短 (< 100字符)")
    
    # 检查工作流程
    workflow = team_structure.get("workflow", {})
    if not workflow.get("steps"):
        issues.append("缺少工作流程步骤")
    elif len(workflow["steps"]) < 3:
        issues.append("工作流程步骤少于3个")
    
    if issues:
        return False, issues
    
    return True, []


def create_template(conn, template_config, system_user_id):
    """创建单个模板"""
    try:
        # 检查模板是否已存在
        cursor = conn.execute("SELECT template_id FROM templates WHERE template_id = ?", (template_config["template_id"],))
        if cursor.fetchone():
            print(f"⚠️ 模板已存在，跳过: {template_config['name']}")
            return True
        
        # 验证模板质量
        is_valid, issues = validate_template_quality(template_config)
        if not is_valid:
            print(f"❌ 模板质量验证失败 {template_config['name']}:")
            for issue in issues:
                print(f"   - {issue}")
            return False
        
        # 插入模板
        conn.execute("""
            INSERT INTO templates (
                template_id, name, description, category, difficulty, visibility, status,
                prompt_template, team_structure_template, default_config, tags, keywords,
                use_case, example_input, expected_output, template_metadata,
                user_id, author_name, usage_count, rating, rating_count, version,
                created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            template_config["template_id"],
            template_config["name"],
            template_config["description"],
            template_config["category"],
            template_config["difficulty"],
            template_config["visibility"],
            template_config["status"],
            "",  # prompt_template
            json.dumps(template_config["team_structure_template"]),
            json.dumps({}),  # default_config
            json.dumps(template_config.get("tags", [])),
            json.dumps(template_config.get("keywords", [])),
            template_config.get("use_case", ""),
            template_config.get("example_input", ""),
            template_config.get("expected_output", ""),
            json.dumps(template_config.get("metadata", {})),
            system_user_id,
            "System",
            0,  # usage_count
            0.0,  # rating
            0,  # rating_count
            template_config.get("metadata", {}).get("version", "1.0.0"),
            datetime.now(timezone.utc).isoformat(),
            datetime.now(timezone.utc).isoformat()
        ))
        
        conn.commit()
        print(f"✅ 模板创建成功: {template_config['name']}")
        return True
        
    except Exception as e:
        print(f"❌ 创建模板失败 {template_config['name']}: {str(e)}")
        return False


def main():
    """主函数"""
    print("=" * 60)
    print("🏗️ 简化特色模板创建工具")
    print("=" * 60)
    
    # 连接数据库
    db_path = "data/meta_agent.db"
    conn = sqlite3.connect(db_path)
    
    try:
        # 创建表
        print("\n📋 创建数据库表...")
        create_tables(conn)
        
        # 创建系统用户
        print("\n👤 准备系统用户...")
        system_user_id = create_system_user(conn)
        
        # 收集所有模板
        print("\n📋 收集模板配置...")
        all_templates = []
        
        # 从第一部分获取模板
        featured_collection = FeaturedTemplatesCollection()
        all_templates.extend(featured_collection.templates)
        
        # 从第二部分获取模板
        all_templates.append(create_product_development_team())
        all_templates.append(create_education_training_team())
        
        # 从第三部分获取模板
        all_templates.append(create_legal_consulting_team())
        all_templates.append(create_medical_diagnosis_team())
        
        print(f"✅ 收集到 {len(all_templates)} 个模板配置")
        
        # 创建模板
        print("\n🔍 创建模板...")
        success_count = 0
        failed_count = 0
        
        for i, template_config in enumerate(all_templates, 1):
            print(f"\n处理模板 {i}/{len(all_templates)}: {template_config['name']}")
            
            if create_template(conn, template_config, system_user_id):
                success_count += 1
            else:
                failed_count += 1
        
        # 统计结果
        print(f"\n📊 创建结果统计:")
        print(f"✅ 成功创建: {success_count} 个模板")
        print(f"❌ 创建失败: {failed_count} 个模板")
        print(f"📈 成功率: {success_count/(success_count+failed_count)*100:.1f}%")
        
        # 验证创建结果
        print("\n🧪 验证创建结果...")
        cursor = conn.execute("""
            SELECT template_id, name, category, difficulty, visibility 
            FROM templates 
            WHERE visibility = 'featured' AND status = 'active'
            ORDER BY created_at DESC
        """)
        templates = cursor.fetchall()
        
        print(f"✅ 数据库中找到 {len(templates)} 个特色模板:")
        for template in templates:
            print(f"   - {template[1]} ({template[2]}, {template[3]})")
        
        if success_count > 0:
            print(f"\n🎉 特色模板创建完成！")
            print(f"✅ 用户现在可以在模板库中看到这些高质量的预置模板")
            return True
        else:
            print(f"\n❌ 没有成功创建任何模板！")
            return False
            
    except Exception as e:
        print(f"\n❌ 创建过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        conn.close()


if __name__ == "__main__":
    success = main()
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有操作完成！")
    else:
        print("❌ 操作失败！")
        sys.exit(1)
    print("=" * 60)
