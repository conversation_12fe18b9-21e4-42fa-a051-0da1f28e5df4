#!/usr/bin/env python3
"""
创建特色模板脚本
将设计的完整Agent团队模板持久化到数据库中
"""

import asyncio
import json
import sys
import uuid
from pathlib import Path
from datetime import datetime, timezone

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy import text

from app.services.featured_templates import FeaturedTemplatesCollection
from app.services.featured_templates_part2 import create_product_development_team, create_education_training_team
from app.services.featured_templates_part3 import create_legal_consulting_team, create_medical_diagnosis_team

# 数据库配置
DATABASE_URL = "sqlite+aiosqlite:///./data/meta_agent.db"

# 创建数据库引擎
engine = create_async_engine(
    DATABASE_URL,
    echo=False,
    future=True
)

# 创建会话工厂
async_session = sessionmaker(
    engine, class_=AsyncSession, expire_on_commit=False
)


class SimpleUser:
    """简单用户类"""
    def __init__(self, id: str, name: str, email: str, is_active: bool = True):
        self.id = id
        self.name = name
        self.email = email
        self.is_active = is_active


async def get_or_create_system_user(db: AsyncSession) -> SimpleUser:
    """获取或创建系统用户"""
    try:
        # 查找系统用户
        query = "SELECT * FROM users WHERE email = :email"
        result = await db.execute(text(query), {"email": "<EMAIL>"})
        user = result.fetchone()
        
        if user:
            # 转换为SimpleUser对象
            return SimpleUser(
                id=user.id,
                name=user.name,
                email=user.email,
                is_active=user.is_active
            )
        
        # 创建系统用户
        user_id = str(uuid.uuid4())
        insert_query = """
            INSERT INTO users (id, name, email, hashed_password, is_active, is_admin, created_at)
            VALUES (:id, :name, :email, :password, :is_active, :is_admin, :created_at)
        """
        
        await db.execute(text(insert_query), {
            "id": user_id,
            "name": "System",
            "email": "<EMAIL>",
            "password": "system_user_no_login",
            "is_active": True,
            "is_admin": True,
            "created_at": datetime.now(timezone.utc)
        })
        
        await db.commit()

        return SimpleUser(
            id=user_id,
            name="System",
            email="<EMAIL>",
            is_active=True
        )
        
    except Exception as e:
        print(f"❌ 创建系统用户失败: {str(e)}")
        await db.rollback()
        raise


async def create_template_from_config(db: AsyncSession, template_config: dict, system_user: SimpleUser) -> bool:
    """从配置创建模板"""
    try:
        # 检查模板是否已存在
        check_query = "SELECT template_id FROM templates WHERE template_id = :template_id"
        result = await db.execute(text(check_query), {"template_id": template_config["template_id"]})
        existing = result.fetchone()
        
        if existing:
            print(f"⚠️ 模板已存在，跳过: {template_config['name']}")
            return True
        
        # 生成唯一ID
        template_id = template_config["template_id"]
        
        # 准备模板数据
        template_data = {
            "template_id": template_id,
            "name": template_config["name"],
            "description": template_config["description"],
            "category": template_config["category"],
            "difficulty": template_config["difficulty"],
            "visibility": template_config["visibility"],
            "status": template_config["status"],
            "prompt_template": "",  # 新模板系统不需要prompt_template
            "team_structure_template": json.dumps(template_config["team_structure_template"]),
            "default_config": json.dumps({}),
            "tags": json.dumps(template_config.get("tags", [])),
            "keywords": json.dumps(template_config.get("keywords", [])),
            "use_case": template_config.get("use_case", ""),
            "example_input": template_config.get("example_input", ""),
            "expected_output": template_config.get("expected_output", ""),
            "template_metadata": json.dumps(template_config.get("metadata", {})),
            "user_id": system_user.id,
            "author_name": "System",
            "usage_count": 0,
            "rating": 0.0,
            "rating_count": 0,
            "version": template_config.get("metadata", {}).get("version", "1.0.0"),
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc)
        }
        
        # 插入模板
        insert_query = """
            INSERT INTO templates (
                template_id, name, description, category, difficulty, visibility, status,
                prompt_template, team_structure_template, default_config, tags, keywords,
                use_case, example_input, expected_output, template_metadata,
                user_id, author_name, usage_count, rating, rating_count, version,
                created_at, updated_at
            ) VALUES (
                :template_id, :name, :description, :category, :difficulty, :visibility, :status,
                :prompt_template, :team_structure_template, :default_config, :tags, :keywords,
                :use_case, :example_input, :expected_output, :template_metadata,
                :user_id, :author_name, :usage_count, :rating, :rating_count, :version,
                :created_at, :updated_at
            )
        """
        
        await db.execute(text(insert_query), template_data)
        await db.commit()
        
        print(f"✅ 模板创建成功: {template_config['name']}")
        return True
        
    except Exception as e:
        print(f"❌ 创建模板失败 {template_config['name']}: {str(e)}")
        await db.rollback()
        return False


async def validate_template_quality(template_config: dict) -> bool:
    """验证模板质量"""
    issues = []
    
    # 检查基本信息
    if not template_config.get("name", "").strip():
        issues.append("缺少模板名称")
    
    if not template_config.get("description", "").strip():
        issues.append("缺少模板描述")
    elif len(template_config["description"]) < 50:
        issues.append("模板描述过短")
    
    # 检查团队结构
    team_structure = template_config.get("team_structure_template", {})
    if not team_structure:
        issues.append("缺少团队结构配置")
        return False
    
    # 检查团队成员
    team_members = team_structure.get("team_members", [])
    if not team_members:
        issues.append("缺少团队成员")
    else:
        for i, member in enumerate(team_members):
            if not member.get("name", "").strip():
                issues.append(f"成员 {i+1} 缺少名称")
            if not member.get("system_prompt", "").strip():
                issues.append(f"成员 {i+1} 缺少系统提示词")
            elif len(member["system_prompt"]) < 200:
                issues.append(f"成员 {i+1} 系统提示词过短 (< 200字符)")
    
    # 检查工作流程
    workflow = team_structure.get("workflow", {})
    if not workflow.get("steps"):
        issues.append("缺少工作流程步骤")
    elif len(workflow["steps"]) < 3:
        issues.append("工作流程步骤少于3个")
    
    # 检查用例和示例
    if not template_config.get("use_case", "").strip():
        issues.append("缺少使用场景描述")
    
    if not template_config.get("example_input", "").strip():
        issues.append("缺少示例输入")
    
    if not template_config.get("expected_output", "").strip():
        issues.append("缺少预期输出")
    
    if issues:
        print(f"❌ 模板质量验证失败 {template_config['name']}:")
        for issue in issues:
            print(f"   - {issue}")
        return False
    
    print(f"✅ 模板质量验证通过: {template_config['name']}")
    return True


async def create_all_featured_templates():
    """创建所有特色模板"""
    print("🚀 开始创建特色Agent团队模板...")
    
    async with async_session() as db:
        try:
            # 获取或创建系统用户
            print("\n👤 准备系统用户...")
            system_user = await get_or_create_system_user(db)
            print(f"✅ 系统用户准备完成: {system_user.name}")
            
            # 收集所有模板
            print("\n📋 收集模板配置...")
            all_templates = []
            
            # 从第一部分获取模板
            featured_collection = FeaturedTemplatesCollection()
            all_templates.extend(featured_collection.templates)
            
            # 从第二部分获取模板
            all_templates.append(create_product_development_team())
            all_templates.append(create_education_training_team())
            
            # 从第三部分获取模板
            all_templates.append(create_legal_consulting_team())
            all_templates.append(create_medical_diagnosis_team())
            
            print(f"✅ 收集到 {len(all_templates)} 个模板配置")
            
            # 验证和创建模板
            print("\n🔍 验证模板质量...")
            success_count = 0
            failed_count = 0
            
            for i, template_config in enumerate(all_templates, 1):
                print(f"\n处理模板 {i}/{len(all_templates)}: {template_config['name']}")
                
                # 验证模板质量
                if not await validate_template_quality(template_config):
                    failed_count += 1
                    continue
                
                # 创建模板
                if await create_template_from_config(db, template_config, system_user):
                    success_count += 1
                else:
                    failed_count += 1
            
            print(f"\n📊 创建结果统计:")
            print(f"✅ 成功创建: {success_count} 个模板")
            print(f"❌ 创建失败: {failed_count} 个模板")
            print(f"📈 成功率: {success_count/(success_count+failed_count)*100:.1f}%")
            
            if success_count > 0:
                print(f"\n🎉 特色模板创建完成！用户现在可以在模板库中看到这些高质量的预置模板。")
                return True
            else:
                print(f"\n❌ 没有成功创建任何模板！")
                return False
                
        except Exception as e:
            print(f"\n❌ 创建过程中出现错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return False


async def test_template_retrieval():
    """测试模板检索"""
    print("\n🧪 测试模板检索...")
    
    async with async_session() as db:
        try:
            # 查询所有特色模板
            query = """
                SELECT template_id, name, category, difficulty, visibility, 
                       team_structure_template, template_metadata
                FROM templates 
                WHERE visibility = 'featured' AND status = 'active'
                ORDER BY created_at DESC
            """
            result = await db.execute(text(query))
            templates = result.fetchall()
            
            print(f"✅ 找到 {len(templates)} 个特色模板:")
            
            for template in templates:
                # 解析团队结构
                try:
                    team_structure = json.loads(template.team_structure_template)
                    team_members_count = len(team_structure.get("team_members", []))
                    workflow_steps_count = len(team_structure.get("workflow", {}).get("steps", []))
                    
                    # 解析元数据
                    metadata = json.loads(template.template_metadata) if template.template_metadata else {}
                    ready_to_deploy = metadata.get("ready_to_deploy", False)
                    
                    print(f"   - {template.name} ({template.category})")
                    print(f"     团队成员: {team_members_count} 人")
                    print(f"     工作流程: {workflow_steps_count} 步")
                    print(f"     可直接部署: {'✅' if ready_to_deploy else '❌'}")
                    
                except Exception as e:
                    print(f"   - {template.name} (解析错误: {str(e)})")
            
            return len(templates) > 0
            
        except Exception as e:
            print(f"❌ 模板检索测试失败: {str(e)}")
            return False


async def main():
    """主函数"""
    print("=" * 60)
    print("🏗️ 特色Agent团队模板创建工具")
    print("=" * 60)
    
    # 创建模板
    success = await create_all_featured_templates()
    
    if success:
        # 测试检索
        await test_template_retrieval()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有操作完成！")
        print("✅ 特色模板已成功创建并持久化到数据库")
        print("✅ 用户现在可以在模板库中使用这些完整的Agent团队模板")
    else:
        print("❌ 操作失败！")
        sys.exit(1)
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
