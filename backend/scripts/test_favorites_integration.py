#!/usr/bin/env python3
"""
Integration test script for agent favorites functionality.
This script tests the favorites API endpoints manually.
"""

import asyncio
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy import text
from app.core.config import get_database_url
from app.models.user import User
from app.models.agent import Agent, AgentStatus, AgentType
from app.models.favorites import UserAgentFavorite
from app.core.logging import database_logger


async def test_favorites_integration():
    """Test favorites functionality integration."""
    
    # Create async engine
    engine = create_async_engine(get_database_url(), echo=True)
    
    async with AsyncSession(engine) as session:
        try:
            print("🧪 Testing Agent Favorites Integration...")
            
            # 1. Check if tables exist
            print("\n1. Checking database tables...")
            
            # Check users table
            result = await session.execute(text("SELECT COUNT(*) FROM users LIMIT 1"))
            print(f"✅ Users table accessible")
            
            # Check agents table
            result = await session.execute(text("SELECT COUNT(*) FROM agents LIMIT 1"))
            print(f"✅ Agents table accessible")
            
            # Check favorites table
            try:
                result = await session.execute(text("SELECT COUNT(*) FROM user_agent_favorites LIMIT 1"))
                print(f"✅ User agent favorites table accessible")
            except Exception as e:
                print(f"❌ User agent favorites table not found: {e}")
                print("💡 Run the migration: alembic upgrade head")
                return False
            
            # 2. Find or create test user
            print("\n2. Setting up test data...")
            
            result = await session.execute(
                text("SELECT * FROM users WHERE email = '<EMAIL>' LIMIT 1")
            )
            user_row = result.fetchone()
            
            if user_row:
                user_id = user_row.id
                print(f"✅ Found test user with ID: {user_id}")
            else:
                print("⚠️  No test user found. Please create a user first.")
                return False
            
            # 3. Find or create test agent
            result = await session.execute(
                text("SELECT * FROM agents WHERE user_id = :user_id LIMIT 1"),
                {"user_id": user_id}
            )
            agent_row = result.fetchone()
            
            if agent_row:
                agent_id = agent_row.agent_id
                print(f"✅ Found test agent with ID: {agent_id}")
            else:
                print("⚠️  No test agent found. Please create an agent first.")
                return False
            
            # 4. Test favorite operations
            print("\n3. Testing favorite operations...")
            
            # Check if already favorited
            result = await session.execute(
                text("SELECT * FROM user_agent_favorites WHERE user_id = :user_id AND agent_id = :agent_id"),
                {"user_id": user_id, "agent_id": agent_id}
            )
            existing_favorite = result.fetchone()
            
            if existing_favorite:
                print(f"✅ Agent is already favorited (ID: {existing_favorite.id})")
                
                # Test removing favorite
                await session.execute(
                    text("DELETE FROM user_agent_favorites WHERE user_id = :user_id AND agent_id = :agent_id"),
                    {"user_id": user_id, "agent_id": agent_id}
                )
                await session.commit()
                print("✅ Removed from favorites")
            
            # Test adding favorite
            import uuid
            favorite_uuid = uuid.uuid4().hex
            await session.execute(
                text("""
                    INSERT INTO user_agent_favorites (uuid, user_id, agent_id, created_at, updated_at)
                    VALUES (:uuid, :user_id, :agent_id, CURRENT_TIMESTAMP, NULL)
                """),
                {
                    "uuid": favorite_uuid,
                    "user_id": user_id,
                    "agent_id": agent_id
                }
            )
            await session.commit()
            print("✅ Added to favorites")
            
            # 5. Test retrieving favorites
            print("\n4. Testing favorites retrieval...")
            
            result = await session.execute(
                text("""
                    SELECT 
                        f.id as favorite_id,
                        f.uuid as favorite_uuid,
                        f.created_at as favorited_at,
                        a.agent_id,
                        a.team_name as name,
                        a.description,
                        a.status,
                        a.agent_type,
                        a.created_at,
                        a.updated_at,
                        a.last_used,
                        a.usage_count
                    FROM user_agent_favorites f
                    JOIN agents a ON f.agent_id = a.agent_id
                    WHERE f.user_id = :user_id AND a.user_id = :user_id
                    ORDER BY f.created_at DESC
                """),
                {"user_id": user_id}
            )
            favorites = result.fetchall()
            
            print(f"✅ Retrieved {len(favorites)} favorite(s)")
            for fav in favorites:
                print(f"   - {fav.name} ({fav.agent_id})")
            
            # 6. Test user isolation
            print("\n5. Testing user isolation...")
            
            # Check that we can't see other users' favorites
            result = await session.execute(
                text("""
                    SELECT COUNT(*) as count
                    FROM user_agent_favorites f
                    JOIN agents a ON f.agent_id = a.agent_id
                    WHERE f.user_id != :user_id
                """),
                {"user_id": user_id}
            )
            other_favorites_count = result.scalar()
            print(f"✅ Other users have {other_favorites_count} favorites (properly isolated)")
            
            # 7. Test constraints
            print("\n6. Testing database constraints...")
            
            try:
                # Try to create duplicate favorite
                await session.execute(
                    text("""
                        INSERT INTO user_agent_favorites (uuid, user_id, agent_id, created_at, updated_at)
                        VALUES (:uuid, :user_id, :agent_id, CURRENT_TIMESTAMP, NULL)
                    """),
                    {
                        "uuid": uuid.uuid4().hex,
                        "user_id": user_id,
                        "agent_id": agent_id
                    }
                )
                await session.commit()
                print("❌ Duplicate favorite was allowed (constraint not working)")
            except Exception as e:
                print("✅ Duplicate favorite prevented by database constraint")
                await session.rollback()
            
            # 8. Cleanup
            print("\n7. Cleaning up...")
            await session.execute(
                text("DELETE FROM user_agent_favorites WHERE user_id = :user_id AND agent_id = :agent_id"),
                {"user_id": user_id, "agent_id": agent_id}
            )
            await session.commit()
            print("✅ Cleaned up test data")
            
            print("\n🎉 All tests passed! Favorites functionality is working correctly.")
            return True
            
        except Exception as e:
            print(f"\n❌ Test failed with error: {e}")
            database_logger.error("Favorites integration test failed", error=str(e))
            await session.rollback()
            return False
        
        finally:
            await engine.dispose()


async def main():
    """Main function."""
    success = await test_favorites_integration()
    if not success:
        sys.exit(1)
    print("\n✅ Integration test completed successfully!")


if __name__ == "__main__":
    asyncio.run(main())
