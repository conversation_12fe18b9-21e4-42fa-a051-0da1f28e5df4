#!/usr/bin/env python3
"""
AI团队生成系统演示脚本

这个脚本演示了AI驱动的团队生成系统的核心功能，包括：
1. AI团队生成
2. 分析和统计
3. 质量评分
4. 成本估算
"""

import asyncio
import json
import sys
import os
from datetime import datetime, timezone

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.services.ai_planner import get_ai_planner
from app.services.ai_team_analytics import AITeamAnalyticsService
from app.models.user import User
from app.models.settings import SystemSettings
from app.core.database import get_async_session
from sqlalchemy.ext.asyncio import AsyncSession


class AITeamGenerationDemo:
    """AI团队生成演示类"""
    
    def __init__(self):
        self.db: AsyncSession = None
        self.demo_user: User = None
    
    async def setup(self):
        """设置演示环境"""
        print("🚀 设置AI团队生成演示环境...")
        
        # 获取数据库会话
        async for session in get_async_session():
            self.db = session
            break
        
        # 创建演示用户
        self.demo_user = User(
            id=999,
            email="<EMAIL>",
            name="Demo User",
            is_active=True
        )
        
        print("✅ 演示环境设置完成")
    
    async def demo_deployable_templates(self):
        """演示可部署模板"""
        print("\n📋 演示1: 可部署模板展示")
        print("-" * 50)

        from app.services.deployable_templates import get_deployable_templates_service

        templates_service = get_deployable_templates_service()

        # 获取所有可用模板
        templates = templates_service.get_all_templates()
        print(f"可用模板数量: {len(templates)}")

        for template in templates:
            print(f"\n模板ID: {template['id']}")
            print(f"名称: {template['name']}")
            print(f"描述: {template['description']}")
            print(f"类别: {template['category']}")
            print(f"难度: {template['difficulty']}")
            print(f"用例: {template['use_case']}")

        # 部署一个模板
        template_id = "tech_consultant"
        deployable_config = templates_service.get_deployable_config(template_id)

        if deployable_config:
            print(f"\n🚀 部署模板: {template_id}")
            print(f"团队名称: {deployable_config['team_name']}")
            print(f"团队成员数: {len(deployable_config['team_members'])}")
            print(f"工作流步骤: {len(deployable_config['workflow']['steps'])}")
            print(f"部署状态: {deployable_config.get('ready_to_deploy', False)}")
            print(f"模板类型: {deployable_config.get('template_type', 'unknown')}")

        return deployable_config
    
    async def demo_ai_generation(self):
        """演示AI生成（模拟）"""
        print("\n🤖 演示2: AI驱动的团队生成（模拟）")
        print("-" * 50)
        
        # 模拟AI生成的团队方案
        ai_generated_plan = {
            "team_name": "智能数据分析团队",
            "description": "专业的数据科学团队，专注于大数据分析和机器学习解决方案",
            "objective": "为企业提供数据驱动的决策支持和预测分析",
            "domain": "technical",
            "complexity": "advanced",
            "team_members": [
                {
                    "name": "数据科学家",
                    "role": "data_scientist",
                    "description": "负责数据建模和机器学习算法开发",
                    "system_prompt": "你是一位经验丰富的数据科学家，擅长统计分析、机器学习和数据可视化。你的任务是分析数据模式，构建预测模型，并提供数据驱动的洞察。",
                    "capabilities": ["统计分析", "机器学习", "数据可视化", "预测建模"],
                    "tools": ["Python", "R", "SQL", "Tableau", "TensorFlow"],
                    "model": "gpt-4",
                    "temperature": 0.3,
                    "max_tokens": 2000
                },
                {
                    "name": "数据工程师",
                    "role": "data_engineer",
                    "description": "负责数据管道构建和数据基础设施管理",
                    "system_prompt": "你是一位专业的数据工程师，专注于构建可靠的数据管道和维护数据基础设施。你的职责包括数据采集、清洗、转换和存储。",
                    "capabilities": ["数据管道", "ETL开发", "数据库设计", "云平台"],
                    "tools": ["Apache Spark", "Kafka", "Docker", "AWS", "PostgreSQL"],
                    "model": "gpt-4",
                    "temperature": 0.4,
                    "max_tokens": 1800
                },
                {
                    "name": "业务分析师",
                    "role": "business_analyst",
                    "description": "负责业务需求分析和数据解读",
                    "system_prompt": "你是一位业务分析师，擅长将技术分析结果转化为业务洞察。你的任务是理解业务需求，解释数据发现，并提供可行的业务建议。",
                    "capabilities": ["需求分析", "业务建模", "报告撰写", "沟通协调"],
                    "tools": ["Excel", "PowerBI", "Jira", "Confluence"],
                    "model": "gpt-3.5-turbo",
                    "temperature": 0.6,
                    "max_tokens": 1500
                }
            ],
            "workflow": {
                "steps": [
                    {
                        "name": "需求收集",
                        "description": "与客户沟通，明确分析目标和业务需求",
                        "assignee": "业务分析师",
                        "inputs": ["客户需求", "业务背景"],
                        "outputs": ["需求文档", "分析目标"]
                    },
                    {
                        "name": "数据准备",
                        "description": "收集、清洗和准备分析所需的数据",
                        "assignee": "数据工程师",
                        "inputs": ["数据源", "需求文档"],
                        "outputs": ["清洗数据", "数据字典"]
                    },
                    {
                        "name": "数据分析",
                        "description": "执行统计分析和机器学习建模",
                        "assignee": "数据科学家",
                        "inputs": ["清洗数据", "分析目标"],
                        "outputs": ["分析结果", "预测模型"]
                    },
                    {
                        "name": "结果解读",
                        "description": "将技术结果转化为业务洞察和建议",
                        "assignee": "业务分析师",
                        "inputs": ["分析结果", "业务背景"],
                        "outputs": ["业务报告", "行动建议"]
                    }
                ]
            },
            "generation_method": "ai_powered",
            "created_at": datetime.now(timezone.utc).isoformat()
        }
        
        print(f"AI生成团队: {ai_generated_plan['team_name']}")
        print(f"团队描述: {ai_generated_plan['description']}")
        print(f"团队成员: {', '.join([m['name'] for m in ai_generated_plan['team_members']])}")
        
        # 计算质量评分
        ai_planner = get_ai_planner(db=self.db, user=self.demo_user)
        enhanced_plan = ai_planner._enhance_ai_generated_plan(ai_generated_plan, "数据分析团队需求")
        
        print(f"生成方法: {enhanced_plan['generation_method']}")
        print(f"创建时间: {enhanced_plan['created_at']}")
        
        return enhanced_plan
    
    async def demo_analytics(self):
        """演示分析功能"""
        print("\n📊 演示3: 分析和统计功能")
        print("-" * 50)
        
        analytics_service = AITeamAnalyticsService(self.db)
        
        # 模拟记录一些生成历史
        print("📝 模拟记录AI团队生成历史...")
        
        sample_records = [
            {
                "user_description": "技术咨询团队",
                "provider": "openai",
                "model": "gpt-4",
                "success": True,
                "generation_method": "ai_powered",
                "tokens_used": 1500,
                "estimated_cost": 0.045
            },
            {
                "user_description": "创意写作团队",
                "provider": "anthropic",
                "model": "claude-3-sonnet",
                "success": True,
                "generation_method": "ai_powered",
                "tokens_used": 1200,
                "estimated_cost": 0.036
            },
            {
                "user_description": "市场营销团队",
                "provider": "openai",
                "model": "gpt-3.5-turbo",
                "success": False,
                "generation_method": "template_fallback",
                "error_message": "API key expired"
            }
        ]
        
        for record_data in sample_records:
            await analytics_service.record_generation(
                user=self.demo_user,
                user_description=record_data["user_description"],
                provider=record_data["provider"],
                model=record_data["model"],
                temperature=0.7,
                max_tokens=4000,
                success=record_data["success"],
                generation_method=record_data["generation_method"],
                tokens_used=record_data.get("tokens_used"),
                estimated_cost=record_data.get("estimated_cost"),
                error_message=record_data.get("error_message")
            )
        
        # 获取用户统计
        user_stats = await analytics_service.get_user_statistics(self.demo_user, days=30)
        
        print(f"📈 用户统计 (最近30天):")
        print(f"  总生成次数: {user_stats['total_generations']}")
        print(f"  成功次数: {user_stats['successful_generations']}")
        print(f"  成功率: {user_stats['success_rate']:.1f}%")
        print(f"  总成本: ${user_stats['total_cost']:.4f}")
        print(f"  平均质量: {user_stats['average_quality_score']:.1f}/10")
        print(f"  生成方法分布: {user_stats['generation_methods']}")
        
        return user_stats
    
    async def demo_cost_estimation(self):
        """演示成本估算"""
        print("\n💰 演示4: 成本估算功能")
        print("-" * 50)
        
        ai_planner = get_ai_planner(db=self.db, user=self.demo_user)
        
        # 测试不同提供商的成本估算
        test_cases = [
            ("openai", "gpt-4", 2000),
            ("openai", "gpt-3.5-turbo", 2000),
            ("anthropic", "claude-3-sonnet", 2000),
            ("google", "gemini-pro", 2000)
        ]
        
        print("💸 不同模型的成本估算 (2000 tokens):")
        for provider, model, tokens in test_cases:
            cost = ai_planner._estimate_cost(provider, model, tokens)
            print(f"  {provider.title()} {model}: ${cost:.4f}")
        
        # 月度成本预测
        monthly_generations = 100
        avg_tokens = 2000
        
        print(f"\n📅 月度成本预测 ({monthly_generations}次生成, 平均{avg_tokens} tokens):")
        for provider, model, tokens in test_cases:
            monthly_cost = ai_planner._estimate_cost(provider, model, avg_tokens) * monthly_generations
            print(f"  {provider.title()} {model}: ${monthly_cost:.2f}/月")
    
    async def run_demo(self):
        """运行完整演示"""
        print("🎯 AI团队生成系统完整演示")
        print("=" * 60)
        
        await self.setup()
        
        try:
            # 运行各个演示
            await self.demo_deployable_templates()
            await self.demo_ai_generation()
            await self.demo_analytics()
            await self.demo_cost_estimation()

            print("\n🎉 演示完成！")
            print("\n📋 系统功能总结:")
            print("✅ 可部署模板管理")
            print("✅ AI驱动的智能团队生成")
            print("✅ 实时分析和统计")
            print("✅ 质量评分和反馈")
            print("✅ 成本估算和预算管理")
            print("✅ 错误处理和降级机制")
            print("✅ 用户认证和数据隔离")
            
        except Exception as e:
            print(f"❌ 演示过程中出现错误: {str(e)}")
            import traceback
            traceback.print_exc()
        
        finally:
            if self.db:
                await self.db.close()


async def main():
    """主函数"""
    demo = AITeamGenerationDemo()
    await demo.run_demo()


if __name__ == "__main__":
    asyncio.run(main())
