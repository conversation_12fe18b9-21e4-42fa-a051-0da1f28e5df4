#!/usr/bin/env python3
"""
Initialize user authentication tables in the database.
"""

import asyncio
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from sqlalchemy.ext.asyncio import create_async_engine
from sqlmodel import SQLModel

from app.core.config import get_database_url, settings
from app.core.logging import database_logger
from app.models.user import User, UserSession, UserToken, LoginHistory
from app.models.agent import Agent
from app.models.planning import PlanningRequest
from app.models.settings import SystemSettings, APIKey


async def create_default_settings(engine):
    """Create default system settings if they don't exist."""
    try:
        from sqlalchemy.ext.asyncio import AsyncSession
        from sqlalchemy import select

        async with AsyncSession(engine) as session:
            # Check if settings already exist
            result = await session.execute(select(SystemSettings).where(SystemSettings.is_active == True))
            existing_settings = result.scalar_one_or_none()

            if not existing_settings:
                database_logger.info("Creating default system settings")

                # Create default settings
                default_settings = SystemSettings(
                    app_name="Meta-Agent",
                    app_description="AI Agent自动生成服务",
                    default_language="zh-CN",
                    timezone="Asia/Shanghai",
                    theme="system",
                    max_concurrent_agents=10,
                    default_model="gpt-4",
                    default_temperature=0.7,
                    max_response_length=10000,
                    timeout_seconds=30,
                    rate_limit_per_minute=100,
                    enable_cors=True,
                    cors_origins="http://localhost:3000,http://127.0.0.1:3000",
                    enable_docs=True,
                    enable_debug=True,
                    log_level="INFO",
                    max_log_files=10,
                    log_retention_days=30,
                    enable_file_logging=True,
                    enable_auth=False,
                    session_timeout_minutes=60,
                    max_login_attempts=5,
                    enable_2fa=False,
                    is_active=True
                )

                session.add(default_settings)
                await session.commit()
                database_logger.info("Default system settings created successfully")
            else:
                database_logger.info("System settings already exist, skipping creation")

    except Exception as e:
        database_logger.error(f"Failed to create default settings: {str(e)}")
        raise


async def create_user_tables():
    """Create user authentication tables."""
    try:
        database_logger.info("Creating user authentication tables")
        
        # Create async engine
        engine = create_async_engine(
            get_database_url(),
            echo=settings.DATABASE_ECHO,
            future=True,
        )
        
        # Create all tables
        async with engine.begin() as conn:
            # Import all models to ensure they're registered
            import app.models.user
            import app.models.agent
            import app.models.settings
            import app.models.planning

            # Create tables
            await conn.run_sync(SQLModel.metadata.create_all)
        
        database_logger.info("User authentication tables created successfully")

        # Create default system settings if they don't exist
        await create_default_settings(engine)

        # Close engine
        await engine.dispose()
        
        return True
        
    except Exception as e:
        database_logger.error(f"Failed to create user tables: {e}")
        return False


async def verify_tables():
    """Verify that all required tables exist."""
    try:
        database_logger.info("Verifying user authentication tables")
        
        engine = create_async_engine(
            get_database_url(),
            echo=False,
            future=True,
        )
        
        # Check if tables exist by trying to query them
        from sqlalchemy import text
        async with engine.begin() as conn:
            # Check users table
            result = await conn.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='users'"))
            users_table = result.fetchone()

            # Check user_sessions table
            result = await conn.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='user_sessions'"))
            sessions_table = result.fetchone()

            # Check user_tokens table
            result = await conn.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='user_tokens'"))
            tokens_table = result.fetchone()

            # Check login_history table
            result = await conn.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='login_history'"))
            history_table = result.fetchone()
        
        await engine.dispose()
        
        tables_status = {
            "users": users_table is not None,
            "user_sessions": sessions_table is not None,
            "user_tokens": tokens_table is not None,
            "login_history": history_table is not None,
        }
        
        all_exist = all(tables_status.values())
        
        if all_exist:
            database_logger.info("All user authentication tables exist")
        else:
            database_logger.warning(f"Missing tables: {[name for name, exists in tables_status.items() if not exists]}")
        
        return all_exist, tables_status
        
    except Exception as e:
        database_logger.error(f"Failed to verify tables: {e}")
        return False, {}


async def create_default_admin_user():
    """Create a default admin user if none exists."""
    try:
        from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
        from sqlalchemy.orm import sessionmaker
        from sqlmodel import select
        from app.core.security import get_password_hash
        from app.models.user import User, UserRole, UserStatus
        
        database_logger.info("Checking for admin user")
        
        engine = create_async_engine(get_database_url(), echo=False)
        AsyncSessionLocal = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
        
        async with AsyncSessionLocal() as session:
            # Check if any admin user exists
            statement = select(User).where(User.role == UserRole.ADMIN)
            result = await session.execute(statement)
            admin_user = result.scalar_one_or_none()
            
            if not admin_user:
                # Create default admin user
                admin_user = User(
                    name="Admin User",
                    email="<EMAIL>",
                    password_hash=get_password_hash("admin123"),
                    role=UserRole.ADMIN,
                    status=UserStatus.ACTIVE,
                    is_email_verified=True,
                    language="en",
                    timezone="UTC"
                )
                
                session.add(admin_user)
                await session.commit()
                await session.refresh(admin_user)
                
                database_logger.info(f"Created default admin user: {admin_user.email}")
                print(f"✅ Default admin user created:")
                print(f"   Email: {admin_user.email}")
                print(f"   Password: admin123")
                print(f"   ⚠️  Please change the password after first login!")
            else:
                database_logger.info("Admin user already exists")
                print(f"ℹ️  Admin user already exists: {admin_user.email}")
        
        await engine.dispose()
        return True
        
    except Exception as e:
        database_logger.error(f"Failed to create admin user: {e}")
        print(f"❌ Failed to create admin user: {e}")
        return False


async def main():
    """Main function."""
    print("🔐 Initializing User Authentication System")
    print("=" * 50)
    
    # Step 1: Create tables
    print("📋 Creating database tables...")
    success = await create_user_tables()
    if not success:
        print("❌ Failed to create tables")
        return 1
    
    # Step 2: Verify tables
    print("🔍 Verifying tables...")
    all_exist, tables_status = await verify_tables()
    if not all_exist:
        print("❌ Some tables are missing:")
        for table, exists in tables_status.items():
            status = "✅" if exists else "❌"
            print(f"   {status} {table}")
        return 1
    
    print("✅ All tables verified")
    
    # Step 3: Create default admin user
    print("👤 Setting up admin user...")
    admin_success = await create_default_admin_user()
    if not admin_success:
        print("⚠️  Failed to create admin user, but tables are ready")
        return 1
    
    print("\n🎉 User authentication system initialized successfully!")
    print("\n📚 Next steps:")
    print("1. Start the application: python -m uvicorn app.main:app --reload")
    print("2. Visit http://localhost:8000/docs to see the API documentation")
    print("3. Use the authentication endpoints to register/login users")
    print("4. Change the default admin password!")
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
