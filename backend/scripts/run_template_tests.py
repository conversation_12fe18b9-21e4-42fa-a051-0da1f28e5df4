#!/usr/bin/env python3
"""
Script to run template-related tests and generate coverage report.
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(command: str, description: str) -> bool:
    """Run a command and return success status."""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {command}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        return True
    except subprocess.CalledProcessError as e:
        print(f"ERROR: Command failed with exit code {e.returncode}")
        print("STDOUT:", e.stdout)
        print("STDERR:", e.stderr)
        return False


def main():
    """Run template tests with coverage."""
    # Change to backend directory
    backend_dir = Path(__file__).parent.parent
    os.chdir(backend_dir)
    
    print("Template Testing Suite")
    print("=" * 60)
    print(f"Working directory: {os.getcwd()}")
    
    # Test commands to run
    test_commands = [
        {
            "command": "python -m pytest tests/test_models/test_template_models.py -v",
            "description": "Template Model Tests"
        },
        {
            "command": "python -m pytest tests/test_api/test_template_endpoints.py -v",
            "description": "Template API Endpoint Tests"
        },
        {
            "command": "python -m pytest tests/test_services/test_template_management_service.py -v",
            "description": "Template Service Tests"
        },
        {
            "command": "python -m pytest tests/test_integration/test_template_integration.py -v",
            "description": "Template Integration Tests"
        },
        {
            "command": "python -m pytest tests/ -k template --cov=app.models.planning --cov=app.api.v1.endpoints.templates --cov=app.services.template_management_service --cov-report=html --cov-report=term-missing",
            "description": "Template Tests with Coverage"
        }
    ]
    
    # Run each test command
    results = []
    for test_config in test_commands:
        success = run_command(test_config["command"], test_config["description"])
        results.append((test_config["description"], success))
    
    # Print summary
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print(f"{'='*60}")
    
    all_passed = True
    for description, success in results:
        status = "PASSED" if success else "FAILED"
        print(f"{description}: {status}")
        if not success:
            all_passed = False
    
    print(f"\nOverall Result: {'ALL TESTS PASSED' if all_passed else 'SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n✅ Template functionality is working correctly!")
        print("📊 Coverage report generated in htmlcov/index.html")
    else:
        print("\n❌ Some tests failed. Please check the output above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
