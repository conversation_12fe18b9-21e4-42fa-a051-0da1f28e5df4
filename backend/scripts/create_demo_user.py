#!/usr/bin/env python3
"""
Create demo user for testing.
"""

import asyncio
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlmodel import select

from app.core.config import get_database_url
from app.core.security import get_password_hash
from app.models.user import User, UserRole, UserStatus


async def create_demo_user():
    """Create demo user if it doesn't exist."""
    try:
        engine = create_async_engine(get_database_url(), echo=False)
        AsyncSessionLocal = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
        
        async with AsyncSessionLocal() as session:
            # Check if demo user exists
            statement = select(User).where(User.email == "<EMAIL>")
            result = await session.execute(statement)
            demo_user = result.scalar_one_or_none()
            
            if not demo_user:
                # Create demo user
                demo_user = User(
                    name="Demo User",
                    email="<EMAIL>",
                    password_hash=get_password_hash("demo123"),
                    role=UserRole.USER,
                    status=UserStatus.ACTIVE,
                    is_email_verified=True,
                    language="en",
                    timezone="UTC"
                )
                
                session.add(demo_user)
                await session.commit()
                await session.refresh(demo_user)
                
                print(f"✅ Demo user created:")
                print(f"   Email: {demo_user.email}")
                print(f"   Password: demo123")
            else:
                print(f"ℹ️  Demo user already exists: {demo_user.email}")
        
        await engine.dispose()
        return True
        
    except Exception as e:
        print(f"❌ Failed to create demo user: {e}")
        return False


async def main():
    """Main function."""
    print("👤 Creating demo user...")
    success = await create_demo_user()
    if success:
        print("🎉 Demo user setup completed!")
    else:
        print("❌ Demo user setup failed!")
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
