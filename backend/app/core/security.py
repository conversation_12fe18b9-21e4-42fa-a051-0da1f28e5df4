"""
Security utilities for authentication and authorization.
"""

import base64
from datetime import datetime, timed<PERSON><PERSON>
from typing import Any, Dict, List, Optional, Union

from cryptography.fernet import <PERSON><PERSON><PERSON>
from jose import JW<PERSON>rror, jwt
from passlib.context import Crypt<PERSON>ontext

from app.core.config import settings

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Encryption for sensitive data (API keys)
def get_encryption_key() -> bytes:
    """Get or generate encryption key for sensitive data."""
    # Use SECRET_KEY as base for encryption key
    key_material = settings.SECRET_KEY.encode()
    # Ensure key is 32 bytes for Fernet
    if len(key_material) < 32:
        key_material = key_material.ljust(32, b'0')
    else:
        key_material = key_material[:32]
    return base64.urlsafe_b64encode(key_material)

# Global encryption instance
_fernet = Fernet(get_encryption_key())


def encrypt_sensitive_data(data: str) -> str:
    """Encrypt sensitive data like API keys."""
    if not data:
        return ""
    encrypted_data = _fernet.encrypt(data.encode())
    return base64.urlsafe_b64encode(encrypted_data).decode()


def decrypt_sensitive_data(encrypted_data: str) -> str:
    """Decrypt sensitive data like API keys."""
    if not encrypted_data:
        return ""
    try:
        decoded_data = base64.urlsafe_b64decode(encrypted_data.encode())
        decrypted_data = _fernet.decrypt(decoded_data)
        return decrypted_data.decode()
    except Exception:
        # If decryption fails, return empty string
        return ""


def create_access_token(
    subject: Union[str, Any], expires_delta: Optional[timedelta] = None
) -> str:
    """Create access token."""
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )
    
    to_encode = {"exp": expire, "sub": str(subject)}
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt


def verify_token(token: str) -> Optional[str]:
    """Verify access token and return subject."""
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        token_data = payload.get("sub")
        return token_data
    except JWTError:
        return None


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify password against hash."""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """Generate password hash."""
    return pwd_context.hash(password)


def generate_api_key() -> str:
    """Generate API key."""
    import secrets
    return f"ma_{secrets.token_urlsafe(32)}"


def validate_api_key(api_key: str) -> bool:
    """Validate API key format."""
    return api_key.startswith("ma_") and len(api_key) == 46


# Two-Factor Authentication Functions
def generate_totp_secret() -> str:
    """Generate a new TOTP secret."""
    import pyotp
    return pyotp.random_base32()


def generate_totp_qr_url(secret: str, user_email: str, issuer_name: str = "Meta-Agent") -> str:
    """Generate TOTP QR code URL."""
    import pyotp
    totp = pyotp.TOTP(secret)
    return totp.provisioning_uri(
        name=user_email,
        issuer_name=issuer_name
    )


def verify_totp_code(secret: str, code: str, window: int = 1) -> bool:
    """Verify TOTP code."""
    import pyotp
    totp = pyotp.TOTP(secret)
    return totp.verify(code, valid_window=window)


def generate_backup_codes(count: int = 8) -> List[str]:
    """Generate backup codes for 2FA."""
    import secrets
    import string
    codes = []
    for _ in range(count):
        # Generate 8-character alphanumeric codes
        code = ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(8))
        codes.append(code)
    return codes


def hash_backup_codes(codes: List[str]) -> List[str]:
    """Hash backup codes for secure storage."""
    return [get_password_hash(code) for code in codes]


def verify_backup_code(code: str, hashed_codes: List[str]) -> bool:
    """Verify backup code against hashed codes."""
    for hashed_code in hashed_codes:
        if verify_password(code, hashed_code):
            return True
    return False


def generate_qr_code_image(qr_url: str) -> bytes:
    """Generate QR code image as bytes."""
    import qrcode
    import io

    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=10,
        border=4,
    )
    qr.add_data(qr_url)
    qr.make(fit=True)

    img = qr.make_image(fill_color="black", back_color="white")
    img_buffer = io.BytesIO()
    img.save(img_buffer, format='PNG')
    img_buffer.seek(0)
    return img_buffer.getvalue()


class SecurityHeaders:
    """Security headers for HTTP responses."""
    
    @staticmethod
    def get_headers() -> Dict[str, str]:
        """Get security headers."""
        return {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Content-Security-Policy": "default-src 'self'",
            "Referrer-Policy": "strict-origin-when-cross-origin",
        }


def sanitize_input(input_str: str) -> str:
    """Sanitize user input."""
    import html
    import re
    
    # HTML escape
    sanitized = html.escape(input_str)
    
    # Remove potentially dangerous patterns
    dangerous_patterns = [
        r'<script.*?</script>',
        r'javascript:',
        r'vbscript:',
        r'onload=',
        r'onerror=',
        r'onclick=',
    ]
    
    for pattern in dangerous_patterns:
        sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE)
    
    return sanitized.strip()


def validate_file_upload(filename: str, content_type: str) -> bool:
    """Validate file upload."""
    allowed_extensions = {'.txt', '.json', '.yaml', '.yml', '.py', '.md'}
    allowed_content_types = {
        'text/plain',
        'application/json',
        'application/x-yaml',
        'text/yaml',
        'text/x-python',
        'text/markdown',
    }
    
    # Check file extension
    import os
    _, ext = os.path.splitext(filename.lower())
    if ext not in allowed_extensions:
        return False
    
    # Check content type
    if content_type not in allowed_content_types:
        return False
    
    return True


def rate_limit_key(identifier: str, endpoint: str) -> str:
    """Generate rate limit key."""
    return f"rate_limit:{identifier}:{endpoint}"


class RateLimiter:
    """Rate limiter implementation."""
    
    def __init__(self, redis_client=None):
        self.redis = redis_client
    
    async def is_allowed(
        self, 
        identifier: str, 
        endpoint: str, 
        limit: int = None, 
        window: int = 60
    ) -> bool:
        """Check if request is allowed under rate limit."""
        if not self.redis:
            return True  # No rate limiting if Redis is not available
        
        if limit is None:
            limit = settings.RATE_LIMIT_PER_MINUTE
        
        key = rate_limit_key(identifier, endpoint)
        
        try:
            current = await self.redis.get(key)
            if current is None:
                await self.redis.setex(key, window, 1)
                return True
            
            if int(current) >= limit:
                return False
            
            await self.redis.incr(key)
            return True
        except Exception:
            # If Redis fails, allow the request
            return True
    
    async def get_remaining(
        self, 
        identifier: str, 
        endpoint: str, 
        limit: int = None
    ) -> int:
        """Get remaining requests for rate limit."""
        if not self.redis:
            return limit or settings.RATE_LIMIT_PER_MINUTE
        
        if limit is None:
            limit = settings.RATE_LIMIT_PER_MINUTE
        
        key = rate_limit_key(identifier, endpoint)
        
        try:
            current = await self.redis.get(key)
            if current is None:
                return limit
            
            return max(0, limit - int(current))
        except Exception:
            return limit
