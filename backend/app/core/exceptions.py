"""
Custom exceptions for the application.
"""

from typing import Any, Dict, Optional


class MetaAgentException(Exception):
    """Base exception for Meta-Agent application."""
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class ValidationError(MetaAgentException):
    """Validation error exception."""
    
    def __init__(self, message: str, field: Optional[str] = None, **kwargs):
        super().__init__(message, error_code="VALIDATION_ERROR", **kwargs)
        self.field = field


class NotFoundError(MetaAgentException):
    """Resource not found exception."""
    
    def __init__(self, resource: str, identifier: str, **kwargs):
        message = f"{resource} with identifier '{identifier}' not found"
        super().__init__(message, error_code="NOT_FOUND", **kwargs)
        self.resource = resource
        self.identifier = identifier


class ConflictError(MetaAgentException):
    """Resource conflict exception."""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(message, error_code="CONFLICT", **kwargs)


class AuthenticationError(MetaAgentException):
    """Authentication error exception."""
    
    def __init__(self, message: str = "Authentication failed", **kwargs):
        super().__init__(message, error_code="AUTHENTICATION_ERROR", **kwargs)


class AuthorizationError(MetaAgentException):
    """Authorization error exception."""
    
    def __init__(self, message: str = "Access denied", **kwargs):
        super().__init__(message, error_code="AUTHORIZATION_ERROR", **kwargs)


class RateLimitError(MetaAgentException):
    """Rate limit exceeded exception."""
    
    def __init__(self, message: str = "Rate limit exceeded", **kwargs):
        super().__init__(message, error_code="RATE_LIMIT_EXCEEDED", **kwargs)


class ExternalServiceError(MetaAgentException):
    """External service error exception."""
    
    def __init__(self, service: str, message: str, **kwargs):
        full_message = f"External service '{service}' error: {message}"
        super().__init__(full_message, error_code="EXTERNAL_SERVICE_ERROR", **kwargs)
        self.service = service


class AIProviderError(ExternalServiceError):
    """AI provider error exception."""
    
    def __init__(self, provider: str, message: str, **kwargs):
        super().__init__(f"ai_provider_{provider}", message, **kwargs)
        self.provider = provider


class PlanningError(MetaAgentException):
    """Planning process error exception."""
    
    def __init__(self, message: str, stage: Optional[str] = None, **kwargs):
        super().__init__(message, error_code="PLANNING_ERROR", **kwargs)
        self.stage = stage


class AgentCreationError(MetaAgentException):
    """Agent creation error exception."""
    
    def __init__(self, message: str, stage: Optional[str] = None, **kwargs):
        super().__init__(message, error_code="AGENT_CREATION_ERROR", **kwargs)
        self.stage = stage


class AgentExecutionError(MetaAgentException):
    """Agent execution error exception."""
    
    def __init__(self, agent_id: str, message: str, **kwargs):
        full_message = f"Agent '{agent_id}' execution error: {message}"
        super().__init__(full_message, error_code="AGENT_EXECUTION_ERROR", **kwargs)
        self.agent_id = agent_id


class DatabaseError(MetaAgentException):
    """Database operation error exception."""
    
    def __init__(self, message: str, operation: Optional[str] = None, **kwargs):
        super().__init__(message, error_code="DATABASE_ERROR", **kwargs)
        self.operation = operation


class ConfigurationError(MetaAgentException):
    """Configuration error exception."""
    
    def __init__(self, message: str, setting: Optional[str] = None, **kwargs):
        super().__init__(message, error_code="CONFIGURATION_ERROR", **kwargs)
        self.setting = setting


class FileOperationError(MetaAgentException):
    """File operation error exception."""
    
    def __init__(self, message: str, file_path: Optional[str] = None, **kwargs):
        super().__init__(message, error_code="FILE_OPERATION_ERROR", **kwargs)
        self.file_path = file_path


class TemplateError(MetaAgentException):
    """Template processing error exception."""
    
    def __init__(self, message: str, template_name: Optional[str] = None, **kwargs):
        super().__init__(message, error_code="TEMPLATE_ERROR", **kwargs)
        self.template_name = template_name


# Exception mapping for HTTP status codes
EXCEPTION_STATUS_MAP = {
    ValidationError: 400,
    NotFoundError: 404,
    ConflictError: 409,
    AuthenticationError: 401,
    AuthorizationError: 403,
    RateLimitError: 429,
    ExternalServiceError: 502,
    AIProviderError: 502,
    PlanningError: 500,
    AgentCreationError: 500,
    AgentExecutionError: 500,
    DatabaseError: 500,
    ConfigurationError: 500,
    FileOperationError: 500,
    TemplateError: 500,
    MetaAgentException: 500,
}
