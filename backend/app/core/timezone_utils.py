"""
Timezone utilities for consistent datetime handling across the application.
"""

from datetime import datetime, timezone
from typing import Optional, Union
import pytz


def utc_now() -> datetime:
    """
    Get current UTC time as timezone-aware datetime.
    
    Returns:
        datetime: Current UTC time with timezone info
    """
    return datetime.now(timezone.utc)


def to_utc(dt: Union[datetime, str, None]) -> Optional[datetime]:
    """
    Convert datetime or ISO string to UTC timezone-aware datetime.
    
    Args:
        dt: Datetime object, ISO string, or None
        
    Returns:
        datetime: UTC timezone-aware datetime or None
    """
    if dt is None:
        return None
    
    if isinstance(dt, str):
        # Parse ISO string
        try:
            # Handle various ISO formats
            if dt.endswith('Z'):
                dt = dt[:-1] + '+00:00'
            parsed_dt = datetime.fromisoformat(dt)
        except ValueError:
            # Fallback for other formats (common with SQLite)
            try:
                # Try SQLite datetime format with microseconds
                parsed_dt = datetime.strptime(dt, '%Y-%m-%d %H:%M:%S.%f')
                parsed_dt = parsed_dt.replace(tzinfo=timezone.utc)
            except ValueError:
                try:
                    # Try SQLite datetime format without microseconds
                    parsed_dt = datetime.strptime(dt, '%Y-%m-%d %H:%M:%S')
                    parsed_dt = parsed_dt.replace(tzinfo=timezone.utc)
                except ValueError:
                    try:
                        # Try ISO format without timezone
                        parsed_dt = datetime.strptime(dt, '%Y-%m-%dT%H:%M:%S.%f')
                        parsed_dt = parsed_dt.replace(tzinfo=timezone.utc)
                    except ValueError:
                        try:
                            # Try ISO format without microseconds and timezone
                            parsed_dt = datetime.strptime(dt, '%Y-%m-%dT%H:%M:%S')
                            parsed_dt = parsed_dt.replace(tzinfo=timezone.utc)
                        except ValueError:
                            raise ValueError(f"Unable to parse datetime string: {dt}")
        dt = parsed_dt
    
    if isinstance(dt, datetime):
        if dt.tzinfo is None:
            # Assume naive datetime is UTC
            return dt.replace(tzinfo=timezone.utc)
        else:
            # Convert to UTC if not already
            return dt.astimezone(timezone.utc)
    
    raise TypeError(f"Expected datetime or string, got {type(dt)}")


def to_naive_utc(dt: Union[datetime, str, None]) -> Optional[datetime]:
    """
    Convert datetime or ISO string to naive UTC datetime (for SQLite compatibility).
    
    Args:
        dt: Datetime object, ISO string, or None
        
    Returns:
        datetime: Naive UTC datetime or None
    """
    utc_dt = to_utc(dt)
    if utc_dt is None:
        return None
    return utc_dt.replace(tzinfo=None)


def calculate_duration_ms(start_dt: Union[datetime, str, None], 
                         end_dt: Union[datetime, str, None]) -> Optional[int]:
    """
    Calculate duration in milliseconds between two datetimes.
    
    Args:
        start_dt: Start datetime (datetime object or ISO string)
        end_dt: End datetime (datetime object or ISO string)
        
    Returns:
        int: Duration in milliseconds or None if either datetime is None
    """
    if start_dt is None or end_dt is None:
        return None
    
    start_utc = to_utc(start_dt)
    end_utc = to_utc(end_dt)
    
    if start_utc is None or end_utc is None:
        return None
    
    duration = end_utc - start_utc
    return int(duration.total_seconds() * 1000)


def format_duration(duration_ms: Optional[int]) -> str:
    """
    Format duration in milliseconds to human-readable string.
    
    Args:
        duration_ms: Duration in milliseconds
        
    Returns:
        str: Formatted duration string
    """
    if duration_ms is None:
        return "未知"
    
    if duration_ms < 1000:
        return f"{duration_ms}ms"
    elif duration_ms < 60000:
        seconds = duration_ms / 1000
        return f"{seconds:.1f}s"
    else:
        minutes = duration_ms // 60000
        seconds = (duration_ms % 60000) / 1000
        return f"{minutes}m {seconds:.1f}s"


def ensure_timezone_aware(dt: datetime, default_tz: timezone = timezone.utc) -> datetime:
    """
    Ensure datetime is timezone-aware, adding default timezone if naive.
    
    Args:
        dt: Datetime object
        default_tz: Default timezone to use for naive datetimes
        
    Returns:
        datetime: Timezone-aware datetime
    """
    if dt.tzinfo is None:
        return dt.replace(tzinfo=default_tz)
    return dt


def normalize_datetime_for_db(dt: Union[datetime, str, None]) -> Optional[datetime]:
    """
    Normalize datetime for database storage (naive UTC for SQLite compatibility).
    
    Args:
        dt: Datetime object, ISO string, or None
        
    Returns:
        datetime: Naive UTC datetime suitable for database storage
    """
    return to_naive_utc(dt)


def normalize_datetime_for_api(dt: Union[datetime, str, None]) -> Optional[datetime]:
    """
    Normalize datetime for API responses (timezone-aware UTC).

    Args:
        dt: Datetime object, string, or None

    Returns:
        datetime: Timezone-aware UTC datetime for API responses
    """
    if dt is None:
        return None

    # Handle string input (common with SQLite raw queries)
    if isinstance(dt, str):
        dt = to_utc(dt)
        if dt is None:
            return None
        return dt

    if dt.tzinfo is None:
        # Assume naive datetime from DB is UTC
        return dt.replace(tzinfo=timezone.utc)

    return dt.astimezone(timezone.utc)


def validate_datetime_consistency(started_at: Optional[datetime], 
                                completed_at: Optional[datetime]) -> bool:
    """
    Validate that datetime objects are consistent (completed_at >= started_at).
    
    Args:
        started_at: Start datetime
        completed_at: Completion datetime
        
    Returns:
        bool: True if consistent, False otherwise
    """
    if started_at is None or completed_at is None:
        return True  # Can't validate incomplete data
    
    start_utc = to_utc(started_at)
    end_utc = to_utc(completed_at)
    
    if start_utc is None or end_utc is None:
        return False
    
    return end_utc >= start_utc


# Timezone constants for common use
UTC = timezone.utc
ASIA_SHANGHAI = pytz.timezone('Asia/Shanghai')
