"""
Custom middleware for the application.
"""

import time
from datetime import datetime, timedelta
from typing import Callable, Dict, Optional, Set
from collections import defaultdict

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import J<PERSON><PERSON>esponse

from app.core.config import settings
from app.core.logging import api_logger


class TimingMiddleware(BaseHTTPMiddleware):
    """Middleware to add timing information to responses."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        
        response = await call_next(request)
        
        process_time = time.time() - start_time
        response.headers["X-Process-Time"] = str(process_time)
        
        return response


class RequestIDMiddleware(BaseHTTPMiddleware):
    """Middleware to add request ID to responses."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        import uuid
        
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        response = await call_next(request)
        response.headers["X-Request-ID"] = request_id
        
        return response


class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """Middleware for centralized error handling."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        try:
            response = await call_next(request)
            return response
        except Exception as exc:
            api_logger.error(
                "Unhandled exception in middleware",
                error=str(exc),
                error_type=type(exc).__name__,
                path=request.url.path,
                method=request.method,
            )
            raise


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Middleware to add security headers."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # Add security headers
        security_headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Referrer-Policy": "strict-origin-when-cross-origin",
        }
        
        for header, value in security_headers.items():
            response.headers[header] = value
        
        return response


class AuthSecurityMiddleware(BaseHTTPMiddleware):
    """Enhanced security middleware for authentication endpoints."""

    def __init__(self, app):
        super().__init__(app)
        # In-memory storage for rate limiting (use Redis in production)
        self.request_counts: Dict[str, Dict[str, int]] = defaultdict(lambda: defaultdict(int))
        self.blocked_ips: Dict[str, datetime] = {}
        self.suspicious_ips: Set[str] = set()

        # Rate limiting configuration for auth endpoints
        self.auth_rate_limits = {
            "/api/v1/auth/login": {"requests": 5, "window": 300},  # 5 requests per 5 minutes
            "/api/v1/auth/register": {"requests": 3, "window": 3600},  # 3 requests per hour
            "/api/v1/auth/reset-password": {"requests": 3, "window": 3600},  # 3 requests per hour
            "/api/v1/auth/change-password": {"requests": 5, "window": 300},  # 5 requests per 5 minutes
        }

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request through auth security middleware."""
        # Only apply to auth endpoints
        if not request.url.path.startswith("/api/v1/auth/"):
            return await call_next(request)

        client_ip = self._get_client_ip(request)

        # Check if IP is blocked
        if self._is_ip_blocked(client_ip):
            api_logger.warning(
                "Blocked IP attempted auth access",
                ip_address=client_ip,
                path=request.url.path,
                method=request.method
            )
            return JSONResponse(
                status_code=429,
                content={
                    "error": {
                        "code": 429,
                        "message": "IP temporarily blocked due to suspicious activity",
                        "type": "security_block"
                    }
                }
            )

        # Apply rate limiting for auth endpoints
        if not self._check_auth_rate_limit(client_ip, request.url.path):
            self._mark_suspicious_activity(client_ip, "auth_rate_limit_exceeded")
            api_logger.warning(
                "Auth rate limit exceeded",
                ip_address=client_ip,
                path=request.url.path,
                method=request.method
            )
            return JSONResponse(
                status_code=429,
                content={
                    "error": {
                        "code": 429,
                        "message": "Too many authentication attempts. Please try again later.",
                        "type": "auth_rate_limit"
                    }
                },
                headers={"Retry-After": "300"}
            )

        # Detect suspicious patterns
        self._detect_auth_suspicious_patterns(request, client_ip)

        response = await call_next(request)

        # Monitor failed auth attempts
        if response.status_code in [401, 403] and request.method == "POST":
            self._handle_failed_auth(client_ip, request.url.path)

        return response

    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP address."""
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        return request.client.host if request.client else "unknown"

    def _is_ip_blocked(self, ip: str) -> bool:
        """Check if IP is currently blocked."""
        if ip in self.blocked_ips:
            block_time = self.blocked_ips[ip]
            if datetime.now() < block_time:
                return True
            else:
                del self.blocked_ips[ip]
                if ip in self.suspicious_ips:
                    self.suspicious_ips.remove(ip)
        return False

    def _check_auth_rate_limit(self, ip: str, path: str) -> bool:
        """Check auth-specific rate limits."""
        if path not in self.auth_rate_limits:
            return True

        rate_limit = self.auth_rate_limits[path]
        current_time = int(time.time())
        window_start = current_time - rate_limit["window"]

        # Clean old entries
        key = f"{ip}:{path}"
        self.request_counts[key] = {
            timestamp: count for timestamp, count in self.request_counts[key].items()
            if int(timestamp) > window_start
        }

        # Count requests in current window
        total_requests = sum(self.request_counts[key].values())

        if total_requests >= rate_limit["requests"]:
            return False

        # Record this request
        timestamp_key = str(current_time)
        if timestamp_key not in self.request_counts[key]:
            self.request_counts[key][timestamp_key] = 0
        self.request_counts[key][timestamp_key] += 1

        return True

    def _detect_auth_suspicious_patterns(self, request: Request, ip: str):
        """Detect suspicious patterns in auth requests."""
        suspicious_indicators = []

        # Check for unusual user agents
        user_agent = request.headers.get("user-agent", "").lower()
        if not user_agent or len(user_agent) < 10:
            suspicious_indicators.append("suspicious_user_agent")

        # Check for missing common headers
        if not request.headers.get("accept"):
            suspicious_indicators.append("missing_accept_header")

        if suspicious_indicators:
            self._mark_suspicious_activity(ip, suspicious_indicators)

    def _handle_failed_auth(self, ip: str, path: str):
        """Handle failed authentication attempts."""
        # Track failed attempts
        key = f"{ip}:failed_auth"
        current_time = int(time.time())
        window_start = current_time - 300  # 5 minute window

        # Clean old entries
        self.request_counts[key] = {
            timestamp: count for timestamp, count in self.request_counts[key].items()
            if int(timestamp) > window_start
        }

        # Record failed attempt
        self.request_counts[key][str(current_time)] = 1

        # Count failed attempts in window
        failed_attempts = sum(self.request_counts[key].values())

        # Block IP after too many failed attempts
        if failed_attempts >= 10:  # 10 failed attempts in 5 minutes
            self.blocked_ips[ip] = datetime.now() + timedelta(hours=1)
            api_logger.error(
                "IP blocked due to repeated failed auth attempts",
                ip_address=ip,
                failed_attempts=failed_attempts
            )

    def _mark_suspicious_activity(self, ip: str, indicators):
        """Mark IP as suspicious."""
        self.suspicious_ips.add(ip)
        api_logger.warning(
            "Suspicious auth activity detected",
            ip_address=ip,
            indicators=indicators if isinstance(indicators, list) else [indicators]
        )
