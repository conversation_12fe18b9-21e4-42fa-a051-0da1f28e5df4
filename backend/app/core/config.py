"""
Application configuration settings.
"""

import secrets
from typing import Any, Dict, List, Optional, Union

from pydantic import AnyHttpUrl, field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings."""
    
    # Application
    APP_NAME: str = "Meta-Agent"
    APP_VERSION: str = "0.1.0"
    APP_DESCRIPTION: str = "AI Agent自动生成服务"
    DEBUG: bool = False
    ENVIRONMENT: str = "production"
    
    # Server
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    RELOAD: bool = False
    
    # Database
    DATABASE_URL: str = "sqlite+aiosqlite:///./data/meta_agent.db"
    DATABASE_ECHO: bool = False
    
    # Security
    SECRET_KEY: str = secrets.token_urlsafe(32)
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    ALGORITHM: str = "HS256"
    
    # CORS
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = []
    
    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    # AI Providers
    OPENAI_API_KEY: Optional[str] = None
    ANTHROPIC_API_KEY: Optional[str] = None
    GOOGLE_API_KEY: Optional[str] = None
    
    # LangSmith
    LANGCHAIN_TRACING_V2: bool = False
    LANGCHAIN_API_KEY: Optional[str] = None
    LANGCHAIN_PROJECT: str = "meta-agent"
    
    # Redis
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"
    LOG_FILE: str = "logs/meta-agent.log"
    LOG_ROTATION: str = "1 day"
    LOG_RETENTION: str = "30 days"
    
    # Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = 100
    RATE_LIMIT_BURST: int = 20
    
    # File Upload
    MAX_UPLOAD_SIZE: int = 10 * 1024 * 1024  # 10MB
    UPLOAD_DIR: str = "data/uploads"
    
    # Agent Settings
    MAX_CONCURRENT_AGENTS: int = 10
    AGENT_TIMEOUT_SECONDS: int = 300
    DEFAULT_MODEL: str = "gpt-4"
    DEFAULT_TEMPERATURE: float = 0.7
    
    # Template Settings
    TEMPLATE_DIR: str = "templates"
    GENERATED_AGENTS_DIR: str = "data/generated_agents"
    

    
    # Background Tasks
    CELERY_BROKER_URL: str = "redis://localhost:6379/1"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/2"
    
    # Development
    RELOAD_DIRS: List[str] = ["app"]
    RELOAD_INCLUDES: List[str] = ["*.py"]
    RELOAD_EXCLUDES: List[str] = ["*.pyc", "__pycache__"]
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings."""
    return settings


# Database configuration
def get_database_url() -> str:
    """Get database URL."""
    return settings.DATABASE_URL


# AI Provider configurations
def get_openai_config() -> Dict[str, Any]:
    """Get OpenAI configuration."""
    return {
        "api_key": settings.OPENAI_API_KEY,
        "model": settings.DEFAULT_MODEL,
        "temperature": settings.DEFAULT_TEMPERATURE,
    }


def get_anthropic_config() -> Dict[str, Any]:
    """Get Anthropic configuration."""
    return {
        "api_key": settings.ANTHROPIC_API_KEY,
        "model": "claude-3-sonnet-20240229",
        "temperature": settings.DEFAULT_TEMPERATURE,
    }


def get_langsmith_config() -> Dict[str, Any]:
    """Get LangSmith configuration."""
    return {
        "tracing": settings.LANGCHAIN_TRACING_V2,
        "api_key": settings.LANGCHAIN_API_KEY,
        "project": settings.LANGCHAIN_PROJECT,
    }


# Logging configuration
def get_logging_config() -> Dict[str, Any]:
    """Get logging configuration."""
    return {
        "level": settings.LOG_LEVEL,
        "format": settings.LOG_FORMAT,
        "file": settings.LOG_FILE,
        "rotation": settings.LOG_ROTATION,
        "retention": settings.LOG_RETENTION,
    }


# CORS configuration
def get_cors_config() -> Dict[str, Any]:
    """Get CORS configuration."""
    return {
        "allow_origins": [str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
        "allow_credentials": True,
        "allow_methods": ["*"],
        "allow_headers": ["*"],
    }


# Rate limiting configuration
def get_rate_limit_config() -> Dict[str, Any]:
    """Get rate limiting configuration."""
    return {
        "per_minute": settings.RATE_LIMIT_PER_MINUTE,
        "burst": settings.RATE_LIMIT_BURST,
    }


# Agent configuration
def get_agent_config() -> Dict[str, Any]:
    """Get agent configuration."""
    return {
        "max_concurrent": settings.MAX_CONCURRENT_AGENTS,
        "timeout": settings.AGENT_TIMEOUT_SECONDS,
        "default_model": settings.DEFAULT_MODEL,
        "default_temperature": settings.DEFAULT_TEMPERATURE,
        "template_dir": settings.TEMPLATE_DIR,
        "generated_dir": settings.GENERATED_AGENTS_DIR,
    }
