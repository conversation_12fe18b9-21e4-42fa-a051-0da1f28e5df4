"""
Logging configuration for the application.
"""

import sys
from pathlib import Path
from typing import Any, Dict

from loguru import logger

from app.core.config import get_logging_config, settings


def setup_logging() -> None:
    """Setup application logging."""
    config = get_logging_config()
    
    # Remove default logger
    logger.remove()
    
    # Console logging
    logger.add(
        sys.stderr,
        level=config["level"],
        format=get_log_format(config["format"]),
        colorize=True,
        backtrace=True,
        diagnose=True,
    )
    
    # File logging
    if config["file"]:
        log_file = Path(config["file"])
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        logger.add(
            log_file,
            level=config["level"],
            format=get_log_format("json"),
            rotation=config["rotation"],
            retention=config["retention"],
            compression="gz",
            serialize=True,
            backtrace=True,
            diagnose=True,
        )
    
    # Set up structured logging for different modules
    setup_module_loggers()


def get_log_format(format_type: str) -> str:
    """Get log format string."""
    if format_type == "json":
        return "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {name}:{function}:{line} | {message}"
    else:
        return (
            "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )


def setup_module_loggers() -> None:
    """Setup loggers for different modules."""
    # Database logging
    logger.add(
        "logs/database.log",
        level="DEBUG" if settings.DATABASE_ECHO else "INFO",
        format=get_log_format("json"),
        filter=lambda record: "database" in record["extra"],
        rotation="1 day",
        retention="7 days",
        compression="gz",
    )
    
    # Agent logging
    logger.add(
        "logs/agents.log",
        level="INFO",
        format=get_log_format("json"),
        filter=lambda record: "agent" in record["extra"],
        rotation="1 day",
        retention="30 days",
        compression="gz",
    )
    
    # API logging
    logger.add(
        "logs/api.log",
        level="INFO",
        format=get_log_format("json"),
        filter=lambda record: "api" in record["extra"],
        rotation="1 day",
        retention="30 days",
        compression="gz",
    )
    
    # Error logging
    logger.add(
        "logs/errors.log",
        level="ERROR",
        format=get_log_format("json"),
        rotation="1 day",
        retention="90 days",
        compression="gz",
    )


def get_logger(name: str, **extra: Any) -> Any:
    """Get a logger with extra context."""
    return logger.bind(module=name, **extra)


# Pre-configured loggers
database_logger = get_logger("database", database=True)
agent_logger = get_logger("agent", agent=True)
api_logger = get_logger("api", api=True)


class LoggerMixin:
    """Mixin class to add logging capabilities."""
    
    @property
    def logger(self) -> Any:
        """Get logger for this class."""
        return get_logger(self.__class__.__name__)


def log_function_call(func_name: str, args: Dict[str, Any], result: Any = None, error: Exception = None) -> None:
    """Log function call with arguments and result."""
    log_data = {
        "function": func_name,
        "arguments": args,
    }
    
    if error:
        log_data["error"] = str(error)
        log_data["error_type"] = type(error).__name__
        logger.error("Function call failed", **log_data)
    else:
        if result is not None:
            log_data["result"] = result
        logger.info("Function call completed", **log_data)


def log_agent_activity(agent_id: str, activity: str, details: Dict[str, Any] = None) -> None:
    """Log agent activity."""
    log_data = {
        "agent_id": agent_id,
        "activity": activity,
    }
    
    if details:
        log_data.update(details)
    
    agent_logger.info("Agent activity", **log_data)


def log_api_request(method: str, path: str, status_code: int, duration: float, user_id: str = None) -> None:
    """Log API request."""
    log_data = {
        "method": method,
        "path": path,
        "status_code": status_code,
        "duration_ms": round(duration * 1000, 2),
    }
    
    if user_id:
        log_data["user_id"] = user_id
    
    api_logger.info("API request", **log_data)


def log_database_query(query: str, duration: float, error: Exception = None) -> None:
    """Log database query."""
    log_data = {
        "query": query,
        "duration_ms": round(duration * 1000, 2),
    }
    
    if error:
        log_data["error"] = str(error)
        database_logger.error("Database query failed", **log_data)
    else:
        database_logger.debug("Database query executed", **log_data)
