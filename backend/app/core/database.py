"""
Database configuration and session management.
"""

from typing import AsyncGenerator

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
import sqlite3
from sqlmodel import SQLModel

from app.core.config import get_database_url, settings
from app.core.logging import database_logger

# Create async engine
engine = create_async_engine(
    get_database_url(),
    echo=settings.DATABASE_ECHO,
    future=True,
)

# Create async session factory
AsyncSessionLocal = sessionmaker(
    engine, class_=AsyncSession, expire_on_commit=False
)


async def create_db_and_tables():
    """Create database and tables."""
    database_logger.info("Creating database tables")
    async with engine.begin() as conn:
        await conn.run_sync(SQLModel.metadata.create_all)
    database_logger.info("Database tables created successfully")


async def get_session() -> AsyncGenerator[AsyncSession, None]:
    """Get database session."""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception as e:
            database_logger.error("Database session error", error=str(e))
            await session.rollback()
            raise
        finally:
            await session.close()


async def init_db():
    """Initialize database."""
    database_logger.info("Initializing database")
    await create_db_and_tables()
    database_logger.info("Database initialized successfully")


async def close_db():
    """Close database connections."""
    database_logger.info("Closing database connections")
    await engine.dispose()
    database_logger.info("Database connections closed")
