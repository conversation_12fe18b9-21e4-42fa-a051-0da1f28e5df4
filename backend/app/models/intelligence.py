"""
Simplified intelligence and analytics models for storing AI insights and metrics.
"""

from datetime import datetime
from typing import Optional, Dict, Any, List, TYPE_CHECKING
from sqlmodel import Field, SQLModel, Column, Relationship
from sqlalchemy import JSON, Text
import uuid

from app.models.base import BaseModel
from app.core.timezone_utils import utc_now, normalize_datetime_for_db

if TYPE_CHECKING:
    from app.models.user import User
    from app.models.agent import Agent


class AgentMetrics(BaseModel, table=True):
    """Store real-time and historical metrics for agents."""
    __tablename__ = "agent_metrics"

    # Foreign keys
    agent_id: str = Field(foreign_key="agents.agent_id", index=True)
    user_id: int = Field(foreign_key="users.id", index=True)
    
    # Performance metrics
    execution_count: int = Field(default=0)
    success_count: int = Field(default=0)
    error_count: int = Field(default=0)
    total_response_time: float = Field(default=0.0)  # milliseconds
    avg_response_time: float = Field(default=0.0)
    p95_response_time: float = Field(default=0.0)
    p99_response_time: float = Field(default=0.0)
    
    # Resource usage
    total_tokens_used: int = Field(default=0)
    total_cost: float = Field(default=0.0)
    avg_cost_per_execution: float = Field(default=0.0)
    
    # Quality metrics
    user_satisfaction_score: float = Field(default=0.0)  # 0-10 scale
    output_quality_score: float = Field(default=0.0)    # 0-10 scale
    
    # Time-based metrics
    uptime_percentage: float = Field(default=100.0)
    last_execution_time: Optional[datetime] = Field(default=None)
    last_error_time: Optional[datetime] = Field(default=None)
    
    # Relationships
    agent: Optional["Agent"] = Relationship(back_populates="metrics")
    user: Optional["User"] = Relationship()


class SystemMetrics(BaseModel, table=True):
    """Store system-wide metrics and health data."""
    __tablename__ = "system_metrics"
    
    # System health
    total_agents: int = Field(default=0)
    active_agents: int = Field(default=0)
    healthy_agents: int = Field(default=0)
    warning_agents: int = Field(default=0)
    critical_agents: int = Field(default=0)
    offline_agents: int = Field(default=0)
    
    # Usage metrics
    total_executions: int = Field(default=0)
    total_users: int = Field(default=0)
    active_users_24h: int = Field(default=0)
    active_users_7d: int = Field(default=0)
    active_users_30d: int = Field(default=0)
    
    # Performance metrics
    avg_response_time: float = Field(default=0.0)
    system_success_rate: float = Field(default=0.0)
    system_error_rate: float = Field(default=0.0)
    system_load: float = Field(default=0.0)
    
    # Cost metrics
    total_cost_24h: float = Field(default=0.0)
    total_cost_7d: float = Field(default=0.0)
    total_cost_30d: float = Field(default=0.0)
    
    # Metadata
    timestamp: datetime = Field(default_factory=lambda: normalize_datetime_for_db(utc_now()), index=True)


class AgentInsight(BaseModel, table=True):
    """Store AI-generated insights and recommendations for agents."""
    __tablename__ = "agent_insights"

    insight_id: str = Field(default_factory=lambda: str(uuid.uuid4()), unique=True, index=True)
    agent_id: Optional[str] = Field(default=None, foreign_key="agents.agent_id", index=True)
    user_id: int = Field(foreign_key="users.id", index=True)
    
    # Insight classification
    insight_type: str = Field()  # performance, cost, usage, quality, optimization
    severity: str = Field()      # low, medium, high, critical
    category: str = Field()      # specific category within type
    
    # Insight content
    title: str = Field()
    description: str = Field(sa_column=Column(Text))
    recommendation: str = Field(sa_column=Column(Text))
    impact_description: Optional[str] = Field(default=None, sa_column=Column(Text))
    
    # Confidence and metrics
    confidence_score: float = Field(default=0.0)  # 0-100
    estimated_savings: Optional[float] = Field(default=None)
    estimated_improvement: Optional[float] = Field(default=None)
    priority_score: int = Field(default=0)   # 1-10 priority
    
    # Status and actions
    is_actionable: bool = Field(default=True)
    is_acknowledged: bool = Field(default=False)
    is_resolved: bool = Field(default=False)
    is_dismissed: bool = Field(default=False)
    
    # Supporting data
    metrics_data: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON))
    analysis_data: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON))
    
    # Timestamps
    acknowledged_at: Optional[datetime] = Field(default=None)
    resolved_at: Optional[datetime] = Field(default=None)
    dismissed_at: Optional[datetime] = Field(default=None)
    
    # Relationships
    agent: Optional["Agent"] = Relationship()
    user: Optional["User"] = Relationship()


class UserBehaviorPattern(BaseModel, table=True):
    """Store user behavior patterns and preferences for personalization."""
    __tablename__ = "user_behavior_patterns"

    user_id: int = Field(foreign_key="users.id", unique=True, index=True)
    
    # Usage patterns
    most_used_agents: Optional[List[str]] = Field(default=None, sa_column=Column(JSON))
    preferred_time_slots: Optional[List[str]] = Field(default=None, sa_column=Column(JSON))
    common_workflows: Optional[List[str]] = Field(default=None, sa_column=Column(JSON))
    failure_patterns: Optional[List[str]] = Field(default=None, sa_column=Column(JSON))
    
    # Preferences
    expertise_level: str = Field(default="beginner")  # beginner, intermediate, advanced, expert
    preferred_complexity: str = Field(default="basic") # basic, standard, detailed, comprehensive
    ui_preferences: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON))
    
    # Behavior metrics
    total_sessions: int = Field(default=0)
    avg_session_duration: float = Field(default=0.0)  # minutes
    feature_usage_stats: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON))
    help_requests: Optional[List[str]] = Field(default=None, sa_column=Column(JSON))
    
    # Learning and adaptation
    completed_tutorials: Optional[List[str]] = Field(default=None, sa_column=Column(JSON))
    skill_assessments: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON))
    adaptation_history: Optional[List[Dict[str, Any]]] = Field(default=None, sa_column=Column(JSON))
    
    # Activity tracking
    last_activity: datetime = Field(default_factory=lambda: normalize_datetime_for_db(utc_now()))
    
    # Relationships
    user: Optional["User"] = Relationship()


# Simplified versions of remaining models for basic functionality
class OptimizationHistory(BaseModel, table=True):
    """Track optimization recommendations and their outcomes."""
    __tablename__ = "optimization_history"

    optimization_id: str = Field(default_factory=lambda: str(uuid.uuid4()), unique=True, index=True)
    user_id: int = Field(foreign_key="users.id", index=True)
    agent_id: Optional[str] = Field(default=None, foreign_key="agents.agent_id", index=True)
    
    # Optimization details
    optimization_type: str = Field()  # model_switch, workflow_change, etc.
    description: str = Field(sa_column=Column(Text))
    status: str = Field(default="pending")    # pending, applied, reverted, failed
    
    # Outcome metrics
    actual_savings: Optional[float] = Field(default=None)
    actual_improvement: Optional[float] = Field(default=None)
    
    # Relationships
    user: Optional["User"] = Relationship()
    agent: Optional["Agent"] = Relationship()


class AlertRule(BaseModel, table=True):
    """Store configurable alert rules for monitoring."""
    __tablename__ = "alert_rules"

    rule_id: str = Field(default_factory=lambda: str(uuid.uuid4()), unique=True, index=True)
    user_id: int = Field(foreign_key="users.id", index=True)
    agent_id: Optional[str] = Field(default=None, foreign_key="agents.agent_id", index=True)
    
    # Rule configuration
    name: str = Field()
    metric_name: str = Field()  # response_time, success_rate, etc.
    operator: str = Field()     # gt, lt, eq, gte, lte
    threshold_value: float = Field()
    severity: str = Field()     # low, medium, high, critical
    is_enabled: bool = Field(default=True)
    
    # Relationships
    user: Optional["User"] = Relationship()
    agent: Optional["Agent"] = Relationship()


class AlertEvent(BaseModel, table=True):
    """Store triggered alert events."""
    __tablename__ = "alert_events"

    event_id: str = Field(default_factory=lambda: str(uuid.uuid4()), unique=True, index=True)
    rule_id: str = Field(foreign_key="alert_rules.rule_id", index=True)
    user_id: int = Field(foreign_key="users.id", index=True)
    agent_id: Optional[str] = Field(default=None, foreign_key="agents.agent_id", index=True)
    
    # Alert details
    alert_type: str = Field()
    severity: str = Field()
    title: str = Field()
    message: str = Field(sa_column=Column(Text))
    
    # Trigger information
    metric_value: float = Field()
    threshold_value: float = Field()
    
    # Status
    is_acknowledged: bool = Field(default=False)
    is_resolved: bool = Field(default=False)
    
    # Timestamps
    triggered_at: datetime = Field(default_factory=lambda: normalize_datetime_for_db(utc_now()), index=True)
    
    # Relationships
    rule: Optional["AlertRule"] = Relationship()
    user: Optional["User"] = Relationship()
    agent: Optional["Agent"] = Relationship()
