"""
Test history models for storing agent test execution data.
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
from sqlmodel import SQLModel, Field, Column, JSON, Text
from enum import Enum

from app.core.timezone_utils import utc_now, normalize_datetime_for_db


class TestStatus(str, Enum):
    """Test execution status."""
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TestHistory(SQLModel, table=True):
    """Model for storing agent test execution history."""
    __tablename__ = "test_history"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    test_id: str = Field(unique=True, index=True, description="Unique test identifier")
    
    # Test metadata
    user_id: int = Field(foreign_key="users.id", index=True)
    agent_id: str = Field(index=True, description="Agent that was tested")
    status: TestStatus = Field(default=TestStatus.RUNNING)
    
    # Timing information
    started_at: datetime = Field(default_factory=lambda: normalize_datetime_for_db(utc_now()))
    completed_at: Optional[datetime] = Field(default=None)
    execution_duration_ms: Optional[int] = Field(default=None, description="Total execution time in milliseconds")
    
    # Test configuration
    ai_config_override: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON))
    api_key_id: Optional[int] = Field(default=None, description="API key used for the test")
    api_key_name: Optional[str] = Field(default=None, description="Name of the API key used")
    
    # Input data
    input_text: str = Field(sa_column=Column(Text), description="User's original input")
    input_metadata: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON))
    
    # Execution data
    execution_stages: Optional[List[Dict[str, Any]]] = Field(default=None, sa_column=Column(JSON))
    progress_updates: Optional[List[Dict[str, Any]]] = Field(default=None, sa_column=Column(JSON))
    
    # Results
    final_output: Optional[str] = Field(default=None, sa_column=Column(Text))
    response_metadata: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON))

    # Context-aware execution tracking
    context_summary: Optional[Dict[str, Any]] = Field(
        default=None,
        sa_column=Column(JSON),
        description="Summary of context flow during execution"
    )
    context_placeholders_used: Optional[List[Dict[str, Any]]] = Field(
        default=None,
        sa_column=Column(JSON),
        description="List of context placeholders that were resolved during execution"
    )
    team_member_interactions: Optional[List[Dict[str, Any]]] = Field(
        default=None,
        sa_column=Column(JSON),
        description="Detailed log of team member interactions and context sharing"
    )

    # Error information
    error_message: Optional[str] = Field(default=None, sa_column=Column(Text))
    error_details: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON))
    
    # Additional metadata
    created_at: datetime = Field(default_factory=lambda: normalize_datetime_for_db(utc_now()))
    updated_at: datetime = Field(default_factory=lambda: normalize_datetime_for_db(utc_now()))


class TestHistoryCreate(SQLModel):
    """Model for creating test history records."""
    test_id: str
    agent_id: str
    input_text: str
    ai_config_override: Optional[Dict[str, Any]] = None
    api_key_id: Optional[int] = None
    api_key_name: Optional[str] = None
    input_metadata: Optional[Dict[str, Any]] = None


class TestHistoryUpdate(SQLModel):
    """Model for updating test history records."""
    status: Optional[TestStatus] = None
    completed_at: Optional[datetime] = None
    execution_duration_ms: Optional[int] = None
    execution_stages: Optional[List[Dict[str, Any]]] = None
    progress_updates: Optional[List[Dict[str, Any]]] = None
    final_output: Optional[str] = None
    response_metadata: Optional[Dict[str, Any]] = None
    context_summary: Optional[Dict[str, Any]] = None
    context_placeholders_used: Optional[List[Dict[str, Any]]] = None
    team_member_interactions: Optional[List[Dict[str, Any]]] = None
    error_message: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None


class TestHistoryResponse(SQLModel):
    """Model for test history API responses."""
    id: int
    test_id: str
    user_id: int
    agent_id: str
    status: TestStatus
    started_at: datetime
    completed_at: Optional[datetime]
    execution_duration_ms: Optional[int]
    ai_config_override: Optional[Dict[str, Any]]
    api_key_id: Optional[int]
    api_key_name: Optional[str]
    input_text: str
    final_output: Optional[str]
    error_message: Optional[str]
    created_at: datetime


class TestHistoryDetailResponse(TestHistoryResponse):
    """Detailed test history response including execution stages and context information."""
    execution_stages: Optional[List[Dict[str, Any]]]
    progress_updates: Optional[List[Dict[str, Any]]]
    response_metadata: Optional[Dict[str, Any]]
    context_summary: Optional[Dict[str, Any]]
    context_placeholders_used: Optional[List[Dict[str, Any]]]
    team_member_interactions: Optional[List[Dict[str, Any]]]
    error_details: Optional[Dict[str, Any]]
    input_metadata: Optional[Dict[str, Any]]


class TestHistoryListResponse(SQLModel):
    """Response model for paginated test history list."""
    tests: List[TestHistoryResponse]
    total: int
    page: int
    limit: int
    has_next: bool
    has_prev: bool
