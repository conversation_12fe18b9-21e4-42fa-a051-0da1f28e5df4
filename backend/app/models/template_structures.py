"""
完整的模板数据结构定义
定义了"开箱即用"的Agent Team模板所需的所有组件
"""

from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field
from enum import Enum


class ModelConfig(BaseModel):
    """AI模型配置"""
    model: str = Field(default="gpt-4", description="使用的AI模型")
    temperature: float = Field(default=0.7, ge=0.0, le=2.0, description="创造性参数")
    max_tokens: int = Field(default=2000, ge=1, le=8000, description="最大token数")
    top_p: Optional[float] = Field(default=None, ge=0.0, le=1.0, description="核采样参数")
    frequency_penalty: Optional[float] = Field(default=None, ge=-2.0, le=2.0, description="频率惩罚")
    presence_penalty: Optional[float] = Field(default=None, ge=-2.0, le=2.0, description="存在惩罚")


class TeamMember(BaseModel):
    """完整的团队成员定义"""
    name: str = Field(description="成员名称")
    role: str = Field(description="成员角色")
    description: str = Field(description="成员描述和职责")
    system_prompt: str = Field(description="完整的系统提示词")
    
    # 能力和工具
    capabilities: List[str] = Field(default_factory=list, description="成员能力列表")
    tools: List[str] = Field(default_factory=list, description="可用工具列表")
    
    # 模型配置
    model_config: ModelConfig = Field(default_factory=ModelConfig, description="AI模型配置")
    
    # 工作流程相关
    workflow_position: Optional[int] = Field(default=None, description="在工作流中的位置")
    dependencies: List[str] = Field(default_factory=list, description="依赖的其他成员")
    
    # 扩展元数据
    metadata: Dict[str, Any] = Field(default_factory=dict, description="扩展元数据")


class WorkflowStep(BaseModel):
    """工作流程步骤定义"""
    name: str = Field(description="步骤名称")
    description: str = Field(description="步骤描述")
    assignee: str = Field(description="负责的团队成员名称")
    
    # 输入输出
    inputs: List[str] = Field(default_factory=list, description="输入要求")
    outputs: List[str] = Field(default_factory=list, description="输出产物")
    
    # 依赖关系
    dependencies: List[str] = Field(default_factory=list, description="依赖的前置步骤")
    
    # 执行配置
    timeout: Optional[int] = Field(default=300, description="步骤超时时间(秒)")
    retry_count: Optional[int] = Field(default=1, description="重试次数")
    
    # 条件执行
    conditions: Optional[Dict[str, Any]] = Field(default=None, description="执行条件")
    
    # 扩展配置
    metadata: Dict[str, Any] = Field(default_factory=dict, description="步骤元数据")


class CoordinationConfig(BaseModel):
    """团队协调配置"""
    orchestrator: str = Field(description="协调者/主导成员")
    communication_style: str = Field(default="collaborative", description="沟通方式")
    decision_making: str = Field(default="consensus", description="决策方式")
    conflict_resolution: str = Field(default="discussion", description="冲突解决方式")
    
    # 协调规则
    turn_taking_rules: Optional[Dict[str, Any]] = Field(default=None, description="发言轮次规则")
    escalation_rules: Optional[Dict[str, Any]] = Field(default=None, description="升级规则")


class WorkflowDefinition(BaseModel):
    """完整的工作流程定义"""
    steps: List[WorkflowStep] = Field(description="工作流程步骤")
    coordination: CoordinationConfig = Field(description="团队协调配置")
    
    # 执行配置
    execution_mode: str = Field(default="sequential", description="执行模式: sequential, parallel, hybrid")
    max_iterations: int = Field(default=1, description="最大迭代次数")
    convergence_criteria: Optional[Dict[str, Any]] = Field(default=None, description="收敛条件")


class ExecutionConfig(BaseModel):
    """执行配置"""
    execution_mode: str = Field(default="sequential", description="执行模式")
    timeout_per_step: int = Field(default=300, description="每步超时时间(秒)")
    max_iterations: int = Field(default=3, description="最大迭代次数")
    error_handling: str = Field(default="graceful_degradation", description="错误处理策略")
    
    # 性能配置
    parallel_execution: bool = Field(default=False, description="是否支持并行执行")
    resource_limits: Optional[Dict[str, Any]] = Field(default=None, description="资源限制")
    
    # 监控配置
    logging_level: str = Field(default="INFO", description="日志级别")



class TemplateMetadata(BaseModel):
    """模板元数据"""
    version: str = Field(default="1.0.0", description="模板版本")
    created_by: str = Field(description="创建者")
    last_updated: str = Field(description="最后更新时间")
    

    
    # 使用统计
    usage_count: int = Field(default=0, description="使用次数")
    rating_count: int = Field(default=0, description="评分次数")
    avg_rating: Optional[float] = Field(default=None, ge=1.0, le=5.0, description="平均评分")
    
    # 标签和分类
    tags: List[str] = Field(default_factory=list, description="标签")
    categories: List[str] = Field(default_factory=list, description="分类")
    
    # 扩展信息
    changelog: Optional[List[Dict[str, Any]]] = Field(default=None, description="变更日志")
    documentation: Optional[str] = Field(default=None, description="文档链接")


class CompleteTeamStructure(BaseModel):
    """完整的团队结构定义 - 这是模板的核心"""
    
    # 基本信息
    team_name: str = Field(description="团队名称")
    description: str = Field(description="团队描述")
    objective: str = Field(description="团队目标")
    domain: str = Field(description="应用领域")
    complexity: str = Field(description="复杂度级别")
    
    # 核心组件
    team_members: List[TeamMember] = Field(description="团队成员列表")
    workflow: WorkflowDefinition = Field(description="工作流程定义")
    configuration: ExecutionConfig = Field(default_factory=ExecutionConfig, description="执行配置")
    
    # 元数据
    metadata: TemplateMetadata = Field(description="模板元数据")
    
    # 扩展配置
    environment_requirements: Optional[Dict[str, Any]] = Field(default=None, description="环境要求")
    integration_points: Optional[Dict[str, Any]] = Field(default=None, description="集成点配置")
    customization_options: Optional[Dict[str, Any]] = Field(default=None, description="可自定义选项")


class TemplateValidationResult(BaseModel):
    """模板验证结果"""
    is_valid: bool = Field(description="是否有效")
    errors: List[str] = Field(default_factory=list, description="错误列表")
    warnings: List[str] = Field(default_factory=list, description="警告列表")
    suggestions: List[str] = Field(default_factory=list, description="建议列表")
    
    # 详细检查结果
    team_members_valid: bool = Field(default=True, description="团队成员配置是否有效")
    workflow_valid: bool = Field(default=True, description="工作流程是否有效")
    configuration_valid: bool = Field(default=True, description="配置是否有效")
    
    # 完整性检查
    completeness_score: float = Field(default=1.0, ge=0.0, le=1.0, description="完整性评分")
    readiness_score: float = Field(default=1.0, ge=0.0, le=1.0, description="就绪度评分")


class TemplateValidator:
    """模板验证器 - 确保模板的完整性和可用性"""

    @staticmethod
    def validate_template(team_structure: CompleteTeamStructure) -> TemplateValidationResult:
        """验证模板的完整性"""
        result = TemplateValidationResult(is_valid=True)

        # 验证基本信息
        if not team_structure.team_name.strip():
            result.errors.append("团队名称不能为空")
            result.is_valid = False

        if not team_structure.description.strip():
            result.errors.append("团队描述不能为空")
            result.is_valid = False

        # 验证团队成员
        if not team_structure.team_members:
            result.errors.append("至少需要一个团队成员")
            result.is_valid = False
            result.team_members_valid = False
        else:
            result.team_members_valid = TemplateValidator._validate_team_members(
                team_structure.team_members, result
            )

        # 验证工作流程
        result.workflow_valid = TemplateValidator._validate_workflow(
            team_structure.workflow, team_structure.team_members, result
        )

        # 验证配置
        result.configuration_valid = TemplateValidator._validate_configuration(
            team_structure.configuration, result
        )

        # 计算完整性和就绪度评分
        result.completeness_score = TemplateValidator._calculate_completeness_score(team_structure)
        result.readiness_score = TemplateValidator._calculate_readiness_score(team_structure, result)

        return result

    @staticmethod
    def _validate_team_members(members: List[TeamMember], result: TemplateValidationResult) -> bool:
        """验证团队成员"""
        is_valid = True
        member_names = set()

        for i, member in enumerate(members):
            # 检查必填字段
            if not member.name.strip():
                result.errors.append(f"成员 {i+1}: 名称不能为空")
                is_valid = False
            elif member.name in member_names:
                result.errors.append(f"成员名称重复: {member.name}")
                is_valid = False
            else:
                member_names.add(member.name)

            if not member.role.strip():
                result.errors.append(f"成员 {member.name}: 角色不能为空")
                is_valid = False

            if not member.system_prompt.strip():
                result.errors.append(f"成员 {member.name}: system_prompt不能为空")
                is_valid = False
            elif len(member.system_prompt) < 50:
                result.warnings.append(f"成员 {member.name}: system_prompt过短，建议至少50字符")

            # 检查能力和工具
            if not member.capabilities:
                result.warnings.append(f"成员 {member.name}: 建议定义具体能力")

            if not member.tools:
                result.suggestions.append(f"成员 {member.name}: 可以定义可用工具")

        return is_valid

    @staticmethod
    def _validate_workflow(workflow: WorkflowDefinition, members: List[TeamMember], result: TemplateValidationResult) -> bool:
        """验证工作流程"""
        is_valid = True
        member_names = {member.name for member in members}
        step_names = set()

        if not workflow.steps:
            result.errors.append("工作流程至少需要一个步骤")
            return False

        for i, step in enumerate(workflow.steps):
            # 检查步骤基本信息
            if not step.name.strip():
                result.errors.append(f"步骤 {i+1}: 名称不能为空")
                is_valid = False
            elif step.name in step_names:
                result.errors.append(f"步骤名称重复: {step.name}")
                is_valid = False
            else:
                step_names.add(step.name)

            # 检查负责人
            if step.assignee not in member_names:
                result.errors.append(f"步骤 {step.name}: 负责人 {step.assignee} 不在团队成员中")
                is_valid = False

            # 检查依赖关系
            for dep in step.dependencies:
                if dep not in step_names and dep not in [s.name for s in workflow.steps[:i]]:
                    result.warnings.append(f"步骤 {step.name}: 依赖步骤 {dep} 可能不存在或顺序有误")

        # 检查协调配置
        if workflow.coordination.orchestrator not in member_names:
            result.errors.append(f"协调者 {workflow.coordination.orchestrator} 不在团队成员中")
            is_valid = False

        return is_valid

    @staticmethod
    def _validate_configuration(config: ExecutionConfig, result: TemplateValidationResult) -> bool:
        """验证执行配置"""
        is_valid = True

        if config.timeout_per_step <= 0:
            result.errors.append("步骤超时时间必须大于0")
            is_valid = False

        if config.max_iterations <= 0:
            result.errors.append("最大迭代次数必须大于0")
            is_valid = False

        return is_valid

    @staticmethod
    def _calculate_completeness_score(team_structure: CompleteTeamStructure) -> float:
        """计算完整性评分"""
        score = 0.0

        # 基本信息 (20%)
        if team_structure.team_name.strip(): score += 0.1
        if team_structure.description.strip(): score += 0.1

        # 团队成员 (40%)
        if team_structure.team_members:
            member_score = 0
            for member in team_structure.team_members:
                if member.system_prompt.strip(): member_score += 1
                if member.capabilities: member_score += 1
            score += 0.4 * min(1.0, member_score / (len(team_structure.team_members) * 2))

        # 工作流程 (30%)
        if team_structure.workflow.steps: score += 0.15
        if len(team_structure.workflow.steps) >= 2: score += 0.15

        # 配置和元数据 (10%)
        if team_structure.configuration: score += 0.05
        if team_structure.metadata: score += 0.05

        return min(1.0, score)

    @staticmethod
    def _calculate_readiness_score(team_structure: CompleteTeamStructure, validation_result: TemplateValidationResult) -> float:
        """计算就绪度评分"""
        if not validation_result.is_valid:
            return 0.0

        score = 0.8  # 基础分

        # 减分项
        score -= len(validation_result.warnings) * 0.05
        score -= len(validation_result.errors) * 0.2

        # 加分项 - 基于完整性和团队规模
        if validation_result.completeness_score >= 0.9:
            score += 0.1

        if len(team_structure.team_members) >= 2:
            score += 0.05  # 多成员团队加分

        return max(0.0, min(1.0, score))


# 导出主要类型
__all__ = [
    "CompleteTeamStructure",
    "TeamMember",
    "WorkflowDefinition",
    "WorkflowStep",
    "ExecutionConfig",
    "TemplateMetadata",
    "ModelConfig",
    "TemplateValidationResult",
    "TemplateValidator"
]
