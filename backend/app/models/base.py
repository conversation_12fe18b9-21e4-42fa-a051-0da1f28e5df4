"""
Base model classes.
"""

from datetime import datetime
from typing import Optional

from sqlmodel import Field, SQLModel

from app.core.timezone_utils import utc_now, normalize_datetime_for_db


class TimestampMixin(SQLModel):
    """Mixin for timestamp fields."""
    
    created_at: datetime = Field(default_factory=lambda: normalize_datetime_for_db(utc_now()), nullable=False)
    updated_at: Optional[datetime] = Field(default=None, nullable=True)


class BaseModel(TimestampMixin):
    """Base model with common fields."""
    
    id: Optional[int] = Field(default=None, primary_key=True)


class UUIDMixin(SQLModel):
    """Mixin for UUID fields."""

    uuid: str = Field(default_factory=lambda: __import__('uuid').uuid4().hex, unique=True, nullable=False)
