"""
Settings and API key management models.
"""

from datetime import datetime
from typing import Dict, Optional, Any
from enum import Enum

from sqlmodel import SQLModel, Field, Column, JSON, Index, Relationship

from app.models.base import BaseModel, TimestampMixin, UUIDMixin


# Enums for settings
class LogLevel(str, Enum):
    """Log level enumeration."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"


class Theme(str, Enum):
    """Theme enumeration."""
    LIGHT = "light"
    DARK = "dark"
    SYSTEM = "system"


class APIKeyStatus(str, Enum):
    """API key status enumeration."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    EXPIRED = "expired"
    REVOKED = "revoked"
    ERROR = "error"


class APIKeyProvider(str, Enum):
    """API key provider enumeration."""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    AZURE = "azure"
    CUSTOM = "custom"


# System Settings Models
class SystemSettingsBase(SQLModel):
    """Base system settings model."""
    
    # General settings
    app_name: str = Field(default="Meta-Agent", max_length=255)
    app_description: str = Field(default="AI Agent自动生成服务", max_length=1000)
    default_language: str = Field(default="zh-CN", max_length=10)
    timezone: str = Field(default="Asia/Shanghai", max_length=50)
    theme: Theme = Field(default=Theme.SYSTEM)
    
    # Agent settings
    max_concurrent_agents: int = Field(default=10, ge=1, le=100)
    default_model: str = Field(default="gpt-4", max_length=100)
    default_temperature: float = Field(default=0.7, ge=0.0, le=2.0)
    max_response_length: int = Field(default=10000, ge=100, le=100000)
    timeout_seconds: int = Field(default=30, ge=5, le=300)

    # AI Team Generation settings
    enable_ai_team_generation: bool = Field(default=True)
    team_generation_provider: str = Field(default="openai", max_length=50)
    team_generation_custom_provider_name: Optional[str] = Field(default=None, max_length=100)  # Custom provider display name
    team_generation_model: str = Field(default="gpt-4", max_length=100)
    team_generation_temperature: float = Field(default=0.7, ge=0.0, le=2.0)
    team_generation_max_tokens: int = Field(default=4000, ge=100, le=32000)
    team_generation_base_url: Optional[str] = Field(default=None, max_length=500)  # Custom API endpoint URL
    team_generation_api_key: Optional[str] = Field(default=None, max_length=500)  # Encrypted API key for team generation
    
    # API settings
    rate_limit_per_minute: int = Field(default=100, ge=1, le=10000)
    enable_cors: bool = Field(default=True)
    cors_origins: str = Field(default="http://localhost:3000,http://127.0.0.1:3000", max_length=1000)
    enable_docs: bool = Field(default=True)
    enable_debug: bool = Field(default=True)

    # Agent API settings
    agent_api_base_url: str = Field(default="http://localhost:8000/api/v1/agents", max_length=500)
    
    # Logging settings
    log_level: LogLevel = Field(default=LogLevel.INFO)
    max_log_files: int = Field(default=10, ge=1, le=100)
    log_retention_days: int = Field(default=30, ge=1, le=365)
    enable_file_logging: bool = Field(default=True)
    
    # Security settings
    enable_auth: bool = Field(default=False)
    session_timeout_minutes: int = Field(default=60, ge=5, le=1440)
    max_login_attempts: int = Field(default=5, ge=1, le=20)
    enable_2fa: bool = Field(default=False)
    
    # Additional configuration as JSON
    additional_config: Optional[Dict[str, Any]] = Field(default_factory=dict, sa_column=Column(JSON))


class SystemSettings(SystemSettingsBase, BaseModel, UUIDMixin, table=True):
    """System settings database model."""
    __tablename__ = "system_settings"
    __table_args__ = (
        Index('idx_settings_app_name', 'app_name'),
        {'extend_existing': True}
    )
    
    # Only one settings record should exist
    is_active: bool = Field(default=True)


# API Key Models
class APIKeyBase(SQLModel):
    """Base API key model."""
    name: str = Field(max_length=255, nullable=False)
    description: Optional[str] = Field(default=None, max_length=1000)

    # Key management
    key_prefix: str = Field(max_length=20, nullable=False)  # First few chars for identification
    encrypted_key: Optional[str] = Field(default=None, max_length=1000)  # Encrypted API key for secure storage

    # Status and expiration
    status: APIKeyStatus = Field(default=APIKeyStatus.ACTIVE)
    expires_at: Optional[datetime] = Field(default=None)

    # Usage tracking
    usage_count: int = Field(default=0, ge=0)
    last_used: Optional[datetime] = Field(default=None)

    # Daily usage statistics
    requests_today: int = Field(default=0, ge=0)
    requests_month: int = Field(default=0, ge=0)
    cost_today: float = Field(default=0.0, ge=0.0)
    cost_month: float = Field(default=0.0, ge=0.0)

    # Last reset dates for daily/monthly counters
    last_daily_reset: Optional[datetime] = Field(default=None)
    last_monthly_reset: Optional[datetime] = Field(default=None)

    # Rate limiting
    rate_limit_per_minute: Optional[int] = Field(default=None, ge=1, le=10000)
    rate_limit_per_day: Optional[int] = Field(default=None, ge=1, le=1000000)

    # Metadata (renamed to avoid conflict with SQLModel.metadata)
    key_metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, sa_column=Column(JSON))


class APIKey(APIKeyBase, BaseModel, UUIDMixin, table=True):
    """API key database model."""
    __tablename__ = "api_keys"
    __table_args__ = (
        Index('idx_api_keys_user_id', 'user_id'),
        Index('idx_api_keys_status', 'status'),
        Index('idx_api_keys_key_prefix', 'key_prefix'),
        {'extend_existing': True}
    )
    
    # Foreign key to user
    user_id: int = Field(foreign_key="users.id", nullable=False)
    
    # Relationships
    user: Optional["User"] = Relationship(back_populates="api_keys")


# API Request/Response Models
class SystemSettingsCreate(SystemSettingsBase):
    """System settings creation model."""
    pass


class SystemSettingsUpdate(SQLModel):
    """System settings update model."""
    app_name: Optional[str] = Field(default=None, max_length=255)
    app_description: Optional[str] = Field(default=None, max_length=1000)
    default_language: Optional[str] = Field(default=None, max_length=10)
    timezone: Optional[str] = Field(default=None, max_length=50)
    theme: Optional[Theme] = Field(default=None)
    
    max_concurrent_agents: Optional[int] = Field(default=None, ge=1, le=100)
    default_model: Optional[str] = Field(default=None, max_length=100)
    default_temperature: Optional[float] = Field(default=None, ge=0.0, le=2.0)
    max_response_length: Optional[int] = Field(default=None, ge=100, le=100000)
    timeout_seconds: Optional[int] = Field(default=None, ge=5, le=300)

    enable_ai_team_generation: Optional[bool] = Field(default=None)
    team_generation_provider: Optional[str] = Field(default=None, max_length=50)
    team_generation_custom_provider_name: Optional[str] = Field(default=None, max_length=100)
    team_generation_model: Optional[str] = Field(default=None, max_length=100)
    team_generation_temperature: Optional[float] = Field(default=None, ge=0.0, le=2.0)
    team_generation_max_tokens: Optional[int] = Field(default=None, ge=100, le=32000)
    team_generation_base_url: Optional[str] = Field(default=None, max_length=500)
    team_generation_api_key: Optional[str] = Field(default=None, max_length=500)
    
    rate_limit_per_minute: Optional[int] = Field(default=None, ge=1, le=10000)
    enable_cors: Optional[bool] = Field(default=None)
    cors_origins: Optional[str] = Field(default=None, max_length=1000)
    enable_docs: Optional[bool] = Field(default=None)
    enable_debug: Optional[bool] = Field(default=None)
    agent_api_base_url: Optional[str] = Field(default=None, max_length=500)
    
    log_level: Optional[LogLevel] = Field(default=None)
    max_log_files: Optional[int] = Field(default=None, ge=1, le=100)
    log_retention_days: Optional[int] = Field(default=None, ge=1, le=365)
    enable_file_logging: Optional[bool] = Field(default=None)
    
    enable_auth: Optional[bool] = Field(default=None)
    session_timeout_minutes: Optional[int] = Field(default=None, ge=5, le=1440)
    max_login_attempts: Optional[int] = Field(default=None, ge=1, le=20)
    enable_2fa: Optional[bool] = Field(default=None)
    
    additional_config: Optional[Dict[str, Any]] = Field(default=None)


class SystemSettingsResponse(SystemSettingsBase, UUIDMixin):
    """System settings response model."""
    id: int
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime]


class APIKeyCreate(SQLModel):
    """API key creation model."""
    name: str = Field(max_length=255)
    description: Optional[str] = Field(default=None, max_length=1000)
    key: str = Field(min_length=1, max_length=500)  # The actual API key
    expires_at: Optional[datetime] = Field(default=None)
    rate_limit_per_minute: Optional[int] = Field(default=None, ge=1, le=10000)
    rate_limit_per_day: Optional[int] = Field(default=None, ge=1, le=1000000)

    # Removed base_url validation since we simplified API key management


class APIKeyUpdate(SQLModel):
    """API key update model."""
    name: Optional[str] = Field(default=None, max_length=255)
    description: Optional[str] = Field(default=None, max_length=1000)
    status: Optional[APIKeyStatus] = Field(default=None)
    expires_at: Optional[datetime] = Field(default=None)
    rate_limit_per_minute: Optional[int] = Field(default=None, ge=1, le=10000)
    rate_limit_per_day: Optional[int] = Field(default=None, ge=1, le=1000000)


class APIKeyResponse(APIKeyBase, UUIDMixin):
    """API key response model (without sensitive data)."""
    id: int
    user_id: int
    created_at: datetime
    updated_at: Optional[datetime]


class APIKeyTest(SQLModel):
    """API key test model."""
    test_endpoint: str = Field(default="/api/v1/health")
    test_data: Optional[Dict[str, Any]] = Field(default_factory=dict)


# Utility functions
def encrypt_api_key(key: str) -> str:
    """Encrypt an API key for secure storage."""
    from app.core.security import encrypt_sensitive_data
    return encrypt_sensitive_data(key)


def decrypt_api_key(encrypted_key: str) -> str:
    """Decrypt an API key from storage."""
    from app.core.security import decrypt_sensitive_data
    return decrypt_sensitive_data(encrypted_key)


def generate_key_prefix(key: str, length: int = 8) -> str:
    """Generate a key prefix for identification."""
    return key[:length] if len(key) >= length else key


def verify_api_key(key: str, encrypted_key: str) -> bool:
    """Verify an API key against its encrypted version."""
    decrypted_key = decrypt_api_key(encrypted_key)
    return key == decrypted_key


def validate_base_url(url: str) -> bool:
    """Validate a base URL for API endpoints with enhanced security checks."""
    if not url:
        return True  # Optional field

    # Strip whitespace
    url = url.strip()

    # Basic URL validation
    import re
    from urllib.parse import urlparse

    try:
        parsed = urlparse(url)

        # Must have scheme and netloc
        if not parsed.scheme or not parsed.netloc:
            return False

        # Only allow HTTP/HTTPS
        if parsed.scheme not in ['http', 'https']:
            return False

        # Security checks - block potentially dangerous URLs
        hostname = parsed.hostname
        if hostname:
            # Block localhost/private IPs in production (allow for development)
            import ipaddress
            try:
                ip = ipaddress.ip_address(hostname)
                # Allow localhost for development, but could be restricted in production
                if ip.is_private and hostname not in ['localhost', '127.0.0.1']:
                    # In production, you might want to block private IPs
                    pass
            except ValueError:
                # Not an IP address, check domain
                pass

        # Additional pattern validation for common formats
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:'
            r'(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,}\.?|'  # domain (allow longer TLDs)
            r'localhost|'  # localhost
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}'  # IP address
            r')'
            r'(?::\d+)?'  # optional port
            r'(?:/[^\s]*)?$',  # optional path
            re.IGNORECASE
        )

        return bool(url_pattern.match(url))

    except Exception:
        return False


def get_default_base_url(provider: APIKeyProvider) -> Optional[str]:
    """Get the default base URL for a provider."""
    defaults = {
        APIKeyProvider.OPENAI: "https://api.openai.com/v1",
        APIKeyProvider.ANTHROPIC: "https://api.anthropic.com",
        APIKeyProvider.GOOGLE: "https://generativelanguage.googleapis.com/v1",
        APIKeyProvider.AZURE: None,  # Azure requires custom URL
        APIKeyProvider.CUSTOM: None,  # Custom providers require custom URL
    }
    return defaults.get(provider)


def is_common_proxy_pattern(url: str) -> bool:
    """Check if URL matches common proxy/gateway patterns."""
    if not url:
        return False

    common_patterns = [
        # Azure OpenAI
        r'https://.*\.openai\.azure\.com',
        # Corporate proxies
        r'https://.*proxy.*\.com',
        r'https://.*gateway.*\.com',
        # API gateways
        r'https://.*\.amazonaws\.com',
        r'https://.*\.cloudflare\.com',
        r'https://.*\.vercel\.app',
        # Self-hosted solutions
        r'https://.*\.local',
        r'https://.*\.internal',
    ]

    import re
    for pattern in common_patterns:
        if re.match(pattern, url, re.IGNORECASE):
            return True
    return False


def get_provider_examples(provider: APIKeyProvider) -> Dict[str, str]:
    """Get example URLs for different provider configurations."""
    examples = {
        APIKeyProvider.OPENAI: {
            "default": "https://api.openai.com/v1",
            "azure": "https://your-resource.openai.azure.com",
            "proxy": "https://your-proxy.com/openai/v1"
        },
        APIKeyProvider.ANTHROPIC: {
            "default": "https://api.anthropic.com",
            "proxy": "https://your-proxy.com/anthropic"
        },
        APIKeyProvider.GOOGLE: {
            "default": "https://generativelanguage.googleapis.com/v1",
            "proxy": "https://your-proxy.com/google/v1"
        },
        APIKeyProvider.AZURE: {
            "required": "https://your-resource.openai.azure.com"
        },
        APIKeyProvider.CUSTOM: {
            "example": "https://your-api.com/v1"
        }
    }
    return examples.get(provider, {})
