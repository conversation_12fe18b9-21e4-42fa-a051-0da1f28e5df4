"""
User authentication and management models.
"""

from datetime import datetime, timedelta, timezone
from enum import Enum
from typing import Dict, List, Optional, TYPE_CHECKING
from uuid import uuid4

from sqlmodel import Field, SQLModel, Relationship, Column, String, JSON
from sqlalchemy import Index

from app.models.base import BaseModel, UUIDMixin

if TYPE_CHECKING:
    from app.models.agent import Agent
    from app.models.settings import APIKey
    from app.models.planning import Template


class UserRole(str, Enum):
    """User role enumeration."""
    ADMIN = "admin"
    USER = "user"
    MODERATOR = "moderator"


class UserStatus(str, Enum):
    """User status enumeration."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    PENDING_VERIFICATION = "pending_verification"


class SessionStatus(str, Enum):
    """Session status enumeration."""
    ACTIVE = "active"
    EXPIRED = "expired"
    REVOKED = "revoked"
    PENDING = "pending"  # For 2FA verification sessions


class TokenType(str, Enum):
    """Token type enumeration."""
    EMAIL_VERIFICATION = "email_verification"
    PASSWORD_RESET = "password_reset"
    ACCOUNT_ACTIVATION = "account_activation"


# User Models
class UserBase(SQLModel):
    """Base user model."""
    name: str = Field(max_length=255, nullable=False)
    email: str = Field(max_length=255, unique=True, index=True, nullable=False)
    role: UserRole = Field(default=UserRole.USER)
    status: UserStatus = Field(default=UserStatus.PENDING_VERIFICATION)
    
    # Profile information
    avatar: Optional[str] = Field(default=None, max_length=500)
    bio: Optional[str] = Field(default=None, max_length=1000)
    timezone: Optional[str] = Field(default=None, max_length=50)
    language: Optional[str] = Field(default="en", max_length=10)
    
    # Activity tracking
    last_login_at: Optional[datetime] = Field(default=None)
    last_activity_at: Optional[datetime] = Field(default=None)
    login_count: int = Field(default=0)
    
    # Email verification
    is_email_verified: bool = Field(default=False)
    email_verified_at: Optional[datetime] = Field(default=None)
    
    # Account security
    failed_login_attempts: int = Field(default=0)
    locked_until: Optional[datetime] = Field(default=None)
    password_changed_at: Optional[datetime] = Field(default=None)

    # Two-Factor Authentication
    is_2fa_enabled: bool = Field(default=False)
    totp_secret: Optional[str] = Field(default=None, max_length=32)  # Base32 encoded secret
    backup_codes: Optional[str] = Field(default=None, max_length=1000)  # JSON array of backup codes
    two_fa_enabled_at: Optional[datetime] = Field(default=None)
    
    # Preferences
    preferences: Optional[Dict] = Field(default_factory=dict, sa_column=Column(JSON))

    # User metadata (renamed to avoid conflict with SQLModel.metadata)
    user_metadata: Optional[Dict] = Field(default_factory=dict, sa_column=Column(JSON))


class User(UserBase, BaseModel, UUIDMixin, table=True):
    """User database model."""
    __tablename__ = "users"
    __table_args__ = (
        Index('idx_users_email', 'email'),
        Index('idx_users_status', 'status'),
        Index('idx_users_role', 'role'),
        {'extend_existing': True}
    )
    
    # Password hash (stored separately for security)
    password_hash: str = Field(nullable=False)
    
    # Relationships
    sessions: List["UserSession"] = Relationship(back_populates="user", cascade_delete=True)
    tokens: List["UserToken"] = Relationship(back_populates="user", cascade_delete=True)
    login_history: List["LoginHistory"] = Relationship(back_populates="user", cascade_delete=True)
    api_keys: List["APIKey"] = Relationship(back_populates="user", cascade_delete=True)
    agents: List["Agent"] = Relationship(back_populates="user", cascade_delete=True)
    templates: List["Template"] = Relationship(back_populates="user", cascade_delete=True)


# User Session Models
class UserSessionBase(SQLModel):
    """Base user session model."""
    session_token: str = Field(unique=True, index=True, nullable=False)
    status: SessionStatus = Field(default=SessionStatus.ACTIVE)
    
    # Session information
    ip_address: Optional[str] = Field(default=None, max_length=45)  # IPv6 support
    user_agent: Optional[str] = Field(default=None, max_length=500)
    device_info: Optional[str] = Field(default=None, max_length=255)
    location: Optional[str] = Field(default=None, max_length=255)
    
    # Timing
    expires_at: datetime = Field(nullable=False)
    last_activity_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc).replace(tzinfo=None))
    
    # Security
    is_secure: bool = Field(default=True)  # HTTPS
    is_mobile: bool = Field(default=False)
    
    # Session metadata
    session_metadata: Optional[Dict] = Field(default_factory=dict, sa_column=Column(JSON))


class UserSession(UserSessionBase, BaseModel, UUIDMixin, table=True):
    """User session database model."""
    __tablename__ = "user_sessions"
    __table_args__ = (
        Index('idx_sessions_token', 'session_token'),
        Index('idx_sessions_user_status', 'user_id', 'status'),
        Index('idx_sessions_expires', 'expires_at'),
        {'extend_existing': True}
    )
    
    # Foreign keys
    user_id: int = Field(foreign_key="users.id", nullable=False)
    
    # Relationships
    user: User = Relationship(back_populates="sessions")


# User Token Models (for email verification, password reset, etc.)
class UserTokenBase(SQLModel):
    """Base user token model."""
    token: str = Field(unique=True, index=True, nullable=False)
    token_type: TokenType = Field(nullable=False)
    
    # Token data
    data: Optional[Dict] = Field(default_factory=dict, sa_column=Column(JSON))
    
    # Timing
    expires_at: datetime = Field(nullable=False)
    used_at: Optional[datetime] = Field(default=None)
    
    # Status
    is_used: bool = Field(default=False)
    is_revoked: bool = Field(default=False)


class UserToken(UserTokenBase, BaseModel, UUIDMixin, table=True):
    """User token database model."""
    __tablename__ = "user_tokens"
    __table_args__ = (
        Index('idx_tokens_token', 'token'),
        Index('idx_tokens_user_type', 'user_id', 'token_type'),
        Index('idx_tokens_expires', 'expires_at'),
        {'extend_existing': True}
    )
    
    # Foreign keys
    user_id: int = Field(foreign_key="users.id", nullable=False)
    
    # Relationships
    user: User = Relationship(back_populates="tokens")


# Login History Models
class LoginHistoryBase(SQLModel):
    """Base login history model."""
    # Login information
    ip_address: Optional[str] = Field(default=None, max_length=45)
    user_agent: Optional[str] = Field(default=None, max_length=500)
    device_info: Optional[str] = Field(default=None, max_length=255)
    location: Optional[str] = Field(default=None, max_length=255)
    
    # Login result
    success: bool = Field(nullable=False)
    failure_reason: Optional[str] = Field(default=None, max_length=255)
    
    # Security
    is_suspicious: bool = Field(default=False)
    risk_score: Optional[float] = Field(default=None, ge=0.0, le=1.0)
    
    # Login metadata
    login_metadata: Optional[Dict] = Field(default_factory=dict, sa_column=Column(JSON))


class LoginHistory(LoginHistoryBase, BaseModel, UUIDMixin, table=True):
    """Login history database model."""
    __tablename__ = "login_history"
    __table_args__ = (
        Index('idx_login_history_user', 'user_id'),
        Index('idx_login_history_success', 'success'),
        Index('idx_login_history_created', 'created_at'),
        {'extend_existing': True}
    )
    
    # Foreign keys
    user_id: int = Field(foreign_key="users.id", nullable=False)
    
    # Relationships
    user: User = Relationship(back_populates="login_history")


# API Request/Response Models
class UserRegister(SQLModel):
    """User registration model."""
    name: str = Field(min_length=1, max_length=255)
    email: str = Field(min_length=5, max_length=255)
    password: str = Field(min_length=8, max_length=128)
    confirm_password: str = Field(min_length=8, max_length=128)
    timezone: Optional[str] = Field(default=None, max_length=50)
    language: Optional[str] = Field(default="en", max_length=10)


class UserLogin(SQLModel):
    """User login model."""
    email: str = Field(min_length=5, max_length=255)
    password: str = Field(min_length=1, max_length=128)
    remember_me: bool = Field(default=False)


class UserUpdate(SQLModel):
    """User update model."""
    name: Optional[str] = Field(default=None, min_length=1, max_length=255)
    bio: Optional[str] = Field(default=None, max_length=1000)
    timezone: Optional[str] = Field(default=None, max_length=50)
    language: Optional[str] = Field(default=None, max_length=10)
    preferences: Optional[Dict] = Field(default=None)


class ChangePassword(SQLModel):
    """Change password model."""
    current_password: str = Field(min_length=1, max_length=128)
    new_password: str = Field(min_length=8, max_length=128)
    confirm_password: str = Field(min_length=8, max_length=128)


class ResetPassword(SQLModel):
    """Reset password request model."""
    email: str = Field(min_length=5, max_length=255)


class ResetPasswordConfirm(SQLModel):
    """Reset password confirmation model."""
    token: str = Field(min_length=1)
    new_password: str = Field(min_length=8, max_length=128)
    confirm_password: str = Field(min_length=8, max_length=128)


# Two-Factor Authentication Models
class TwoFactorSetup(SQLModel):
    """2FA setup initiation model."""
    password: str = Field(min_length=1, max_length=128)


class TwoFactorEnable(SQLModel):
    """2FA enable confirmation model."""
    totp_code: str = Field(min_length=6, max_length=6, regex=r'^\d{6}$')


class TwoFactorDisable(SQLModel):
    """2FA disable model."""
    password: str = Field(min_length=1, max_length=128)
    totp_code: Optional[str] = Field(default=None, min_length=6, max_length=6, regex=r'^\d{6}$')
    backup_code: Optional[str] = Field(default=None, min_length=8, max_length=8)


class TwoFactorVerify(SQLModel):
    """2FA verification model."""
    totp_code: Optional[str] = Field(default=None, min_length=6, max_length=6, regex=r'^\d{6}$')
    backup_code: Optional[str] = Field(default=None, min_length=8, max_length=8)


class TwoFactorSetupResponse(SQLModel):
    """2FA setup response model."""
    secret: str
    qr_code_url: str
    backup_codes: List[str]


class LoginWith2FA(SQLModel):
    """Login with 2FA model."""
    email: str = Field(min_length=5, max_length=255)
    password: str = Field(min_length=1, max_length=128)
    totp_code: Optional[str] = Field(default=None, min_length=6, max_length=6, regex=r'^\d{6}$')
    backup_code: Optional[str] = Field(default=None, min_length=8, max_length=8)
    remember_me: bool = Field(default=False)


class UserResponse(SQLModel):
    """User response model (public data only)."""
    id: int
    uuid: str
    name: str
    email: str
    role: UserRole
    status: UserStatus
    avatar: Optional[str]
    bio: Optional[str]
    timezone: Optional[str]
    language: str
    last_login_at: Optional[datetime]
    is_email_verified: bool
    login_count: int
    is_2fa_enabled: bool
    two_fa_enabled_at: Optional[datetime]
    created_at: datetime
    updated_at: Optional[datetime]


class UserProfile(UserResponse):
    """Extended user profile model."""
    preferences: Optional[Dict]
    last_activity_at: Optional[datetime]
    email_verified_at: Optional[datetime]
    password_changed_at: Optional[datetime]


class AuthTokens(SQLModel):
    """Authentication tokens response."""
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    refresh_token: Optional[str] = None


class LoginResponse(SQLModel):
    """Login response model."""
    user: UserResponse
    tokens: AuthTokens
    session_id: str


class LoginStepResponse(SQLModel):
    """Login step response model for 2FA flow."""
    requires_2fa: bool
    user_id: Optional[str] = None  # Temporary identifier for 2FA flow
    message: str


class SessionResponse(SQLModel):
    """Session response model."""
    id: int
    uuid: str
    session_token: str
    status: SessionStatus
    ip_address: Optional[str]
    user_agent: Optional[str]
    device_info: Optional[str]
    location: Optional[str]
    expires_at: datetime
    last_activity_at: datetime
    is_secure: bool
    is_mobile: bool
    created_at: datetime


class LoginHistoryResponse(SQLModel):
    """Login history response model."""
    id: int
    uuid: str
    ip_address: Optional[str]
    user_agent: Optional[str]
    device_info: Optional[str]
    location: Optional[str]
    success: bool
    failure_reason: Optional[str]
    is_suspicious: bool
    risk_score: Optional[float]
    created_at: datetime
