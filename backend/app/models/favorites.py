"""
User agent favorites database models.
"""

from datetime import datetime
from typing import Optional, TYPE_CHECKING

from sqlmodel import Field, SQLModel, Relationship
from sqlalchemy import Index

from app.models.base import BaseModel, UUIDMixin

if TYPE_CHECKING:
    from app.models.user import User
    from app.models.agent import Agent


class UserAgentFavoriteBase(SQLModel):
    """Base user agent favorite model."""
    user_id: int = Field(foreign_key="users.id", nullable=False, index=True)
    agent_id: str = Field(foreign_key="agents.agent_id", nullable=False, index=True)


class UserAgentFavorite(UserAgentFavoriteBase, BaseModel, UUIDMixin, table=True):
    """User agent favorite database model."""
    __tablename__ = "user_agent_favorites"
    __table_args__ = (
        Index('idx_user_agent_favorites_user_id', 'user_id'),
        Index('idx_user_agent_favorites_agent_id', 'agent_id'),
        Index('idx_user_agent_favorites_created_at', 'created_at'),
        Index('idx_user_agent_favorites_uuid', 'uuid', unique=True),
        Index('idx_user_agent_favorites_user_created', 'user_id', 'created_at'),
        {'extend_existing': True}
    )
    
    # Relationships
    user: Optional["User"] = Relationship()
    agent: Optional["Agent"] = Relationship()


class UserAgentFavoriteCreate(UserAgentFavoriteBase):
    """User agent favorite creation model."""
    pass


class UserAgentFavoriteResponse(UserAgentFavoriteBase, UUIDMixin):
    """User agent favorite response model."""
    id: int
    created_at: datetime
    updated_at: Optional[datetime]


class FavoriteAgentResponse(SQLModel):
    """Response model for favorite agent with performance metrics."""
    # Agent information
    agent_id: str
    name: str
    description: str
    status: str
    agent_type: str
    created_at: datetime
    updated_at: Optional[datetime]
    last_used: Optional[datetime]
    usage_count: int
    
    # Favorite information
    favorite_id: int
    favorite_uuid: str
    favorited_at: datetime
    
    # Performance metrics (optional)
    performance: Optional[dict] = None


class ToggleFavoriteRequest(SQLModel):
    """Request model for toggling favorite status."""
    pass  # No additional fields needed, agent_id comes from URL


class ToggleFavoriteResponse(SQLModel):
    """Response model for toggle favorite operation."""
    success: bool
    is_favorite: bool
    message: str
    agent_id: str
    favorite_id: Optional[int] = None
