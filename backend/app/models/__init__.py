# Models package

# Import all models to ensure they are registered with SQLModel
from app.models.base import BaseModel, TimestampMixin, UUIDMixin
from app.models.user import (
    User, UserSession, UserToken, LoginHistory,
    UserRole, UserStatus, SessionStatus, TokenType,
    UserRegister, UserLogin, UserUpdate, ChangePassword,
    ResetPassword, ResetPasswordConfirm, UserResponse,
    UserProfile, AuthTokens, LoginResponse, SessionResponse,
    LoginHistoryResponse
)
from app.models.agent import Agent, AgentStatus, AgentType
from app.models.planning import PlanningRequest, PlanningStatus
from app.models.settings import (
    SystemSettings, APIKey, LogLevel, Theme, APIKeyStatus, APIKeyProvider,
    SystemSettingsCreate, SystemSettingsUpdate, SystemSettingsResponse,
    APIKeyCreate, APIKeyUpdate, APIKeyResponse, APIKeyTest,
    encrypt_api_key, decrypt_api_key, generate_key_prefix, verify_api_key
)
from app.models.test_history import (
    TestHistory, TestStatus, TestHistoryCreate, TestHistoryUpdate,
    TestHistoryResponse, TestHistoryDetailResponse, TestHistoryListResponse
)
from app.models.intelligence import (
    AgentMetrics, SystemMetrics, AgentInsight, UserBehaviorPattern,
    OptimizationHistory, AlertRule, AlertEvent
)
from app.models.application_log import (
    ApplicationLog, LogLevel, EventType, ApplicationLogCreate,
    ApplicationLogResponse, ApplicationLogDetailResponse, ApplicationLogListResponse,
    LogFilterParams
)
from app.services.ai_team_analytics import AITeamGenerationRecord
from app.models.favorites import (
    UserAgentFavorite, UserAgentFavoriteCreate, UserAgentFavoriteResponse,
    FavoriteAgentResponse, ToggleFavoriteResponse
)



__all__ = [
    # Base models
    "BaseModel", "TimestampMixin", "UUIDMixin",

    # User models
    "User", "UserSession", "UserToken", "LoginHistory",
    "UserRole", "UserStatus", "SessionStatus", "TokenType",

    # User API models
    "UserRegister", "UserLogin", "UserUpdate", "ChangePassword",
    "ResetPassword", "ResetPasswordConfirm", "UserResponse",
    "UserProfile", "AuthTokens", "LoginResponse", "SessionResponse",
    "LoginHistoryResponse",

    # Agent models
    "Agent", "AgentStatus", "AgentType",

    # Planning models
    "PlanningRequest", "PlanningStatus",

    # Settings models
    "SystemSettings", "APIKey", "LogLevel", "Theme", "APIKeyStatus", "APIKeyProvider",
    "SystemSettingsCreate", "SystemSettingsUpdate", "SystemSettingsResponse",
    "APIKeyCreate", "APIKeyUpdate", "APIKeyResponse", "APIKeyTest",
    "encrypt_api_key", "decrypt_api_key", "generate_key_prefix", "verify_api_key",

    # Test history models
    "TestHistory", "TestStatus", "TestHistoryCreate", "TestHistoryUpdate",
    "TestHistoryResponse", "TestHistoryDetailResponse", "TestHistoryListResponse",

    # Intelligence models
    "AgentMetrics", "SystemMetrics", "AgentInsight", "UserBehaviorPattern",
    "OptimizationHistory", "AlertRule", "AlertEvent",

    # Application log models
    "ApplicationLog", "LogLevel", "EventType", "ApplicationLogCreate",
    "ApplicationLogResponse", "ApplicationLogDetailResponse", "ApplicationLogListResponse",
    "LogFilterParams",

    # AI Team Generation models
    "AITeamGenerationRecord",

    # Favorites models
    "UserAgentFavorite", "UserAgentFavoriteCreate", "UserAgentFavoriteResponse",
    "FavoriteAgentResponse", "ToggleFavoriteResponse",
]
