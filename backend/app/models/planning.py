"""
Planning-related database models.
"""

from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, TYPE_CHECKING

from sqlmodel import Field, SQLModel, Relationship, Column
from sqlalchemy import <PERSON><PERSON><PERSON>, Index

from app.models.base import BaseModel, UUIDMixin

if TYPE_CHECKING:
    from app.models.user import User


class PlanningStatus(str, Enum):
    """Planning status enumeration."""
    PENDING = "PENDING"
    ANALYZING = "ANALYZING"
    PLANNING = "PLANNING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"


class PlanningRequestBase(SQLModel):
    """Base planning request model."""
    request_id: str = Field(unique=True, index=True)

    # User input
    user_description: str

    # Planning configuration
    model: str = Field(default="gpt-4")
    temperature: float = Field(default=0.7, ge=0.0, le=2.0)

    # Status
    status: PlanningStatus = Field(default=PlanningStatus.PENDING)

    # Results
    generated_code: Optional[str] = None
    team_plan_json: Optional[str] = None

    # Timing
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    duration_seconds: Optional[float] = None

    # Error handling
    error_message: Optional[str] = None


class PlanningRequest(PlanningRequestBase, BaseModel, UUIDMixin, table=True):
    """Planning request database model."""
    __tablename__ = "planning_requests"
    __table_args__ = {'extend_existing': True}

    # Relationships
    team_plans: List["TeamPlan"] = Relationship(back_populates="planning_request")


class PlanningRequestCreate(SQLModel):
    """Planning request creation model."""
    user_description: str
    requirements: Optional[Dict] = None
    model: Optional[str] = "gpt-4"
    temperature: Optional[float] = 0.7
    request_metadata: Optional[Dict] = None


class PlanningRequestUpdate(SQLModel):
    """Planning request update model."""
    status: Optional[PlanningStatus] = None
    team_plan: Optional[Dict] = None
    generated_code: Optional[str] = None
    team_plan_json: Optional[str] = None
    completed_at: Optional[datetime] = None
    duration_seconds: Optional[float] = None
    error_message: Optional[str] = None
    error_details: Optional[Dict] = None
    request_metadata: Optional[Dict] = None


class PlanningRequestResponse(PlanningRequestBase, UUIDMixin):
    """Planning request response model."""
    id: int
    created_at: datetime
    updated_at: Optional[datetime]


# Team Plan Models
class TeamPlanBase(SQLModel):
    """Base team plan model."""
    plan_id: str = Field(unique=True, index=True)
    
    # Plan details
    team_name: str = Field(max_length=255)
    description: str
    objective: str
    
    # Team structure
    team_structure: Dict = Field(sa_column=Column(JSON))
    workflow: Dict = Field(sa_column=Column(JSON))
    
    # Configuration
    ai_model_config: Dict = Field(default_factory=dict, sa_column=Column(JSON))
    
    # Validation
    is_validated: bool = Field(default=False)
    validation_score: Optional[float] = None
    validation_feedback: Optional[str] = None
    
    # User feedback
    user_approved: Optional[bool] = None
    user_feedback: Optional[str] = None
    
    # Metadata
    tags: List[str] = Field(default_factory=list, sa_column=Column(JSON))
    plan_metadata: Dict = Field(default_factory=dict, sa_column=Column(JSON))


class TeamPlan(TeamPlanBase, BaseModel, UUIDMixin, table=True):
    """Team plan database model."""
    __tablename__ = "team_plans"
    __table_args__ = {'extend_existing': True}

    # Foreign keys
    planning_request_id: int = Field(foreign_key="planning_requests.id")
    
    # Relationships
    planning_request: PlanningRequest = Relationship(back_populates="team_plans")
    specialists: List["SpecialistPlan"] = Relationship(back_populates="team_plan")


class TeamPlanCreate(TeamPlanBase):
    """Team plan creation model."""
    pass


class TeamPlanUpdate(SQLModel):
    """Team plan update model."""
    team_name: Optional[str] = None
    description: Optional[str] = None
    objective: Optional[str] = None
    team_structure: Optional[Dict] = None
    workflow: Optional[Dict] = None
    ai_model_config: Optional[Dict] = None
    is_validated: Optional[bool] = None
    validation_score: Optional[float] = None
    validation_feedback: Optional[str] = None
    user_approved: Optional[bool] = None
    user_feedback: Optional[str] = None
    tags: Optional[List[str]] = None
    plan_metadata: Optional[Dict] = None


class TeamPlanResponse(TeamPlanBase, UUIDMixin):
    """Team plan response model."""
    id: int
    planning_request_id: int
    created_at: datetime
    updated_at: Optional[datetime]


# Specialist Plan Models
class SpecialistPlanBase(SQLModel):
    """Base specialist plan model."""
    name: str = Field(max_length=255)
    role: str = Field(max_length=255)
    description: str
    
    # AI Configuration
    model: str = Field(default="gpt-4")
    temperature: float = Field(default=0.7, ge=0.0, le=2.0)
    max_tokens: Optional[int] = Field(default=None, gt=0)
    
    # Prompts
    system_prompt: str
    role_prompt: Optional[str] = None
    
    # Capabilities
    capabilities: List[str] = Field(default_factory=list, sa_column=Column(JSON))
    tools: List[str] = Field(default_factory=list, sa_column=Column(JSON))
    
    # Workflow
    workflow_position: int = Field(default=0)
    dependencies: List[str] = Field(default_factory=list, sa_column=Column(JSON))
    
    # Metadata
    specialist_metadata: Dict = Field(default_factory=dict, sa_column=Column(JSON))


class SpecialistPlan(SpecialistPlanBase, BaseModel, table=True):
    """Specialist plan database model."""
    __tablename__ = "specialist_plans"
    __table_args__ = {'extend_existing': True}

    # Foreign keys
    team_plan_id: int = Field(foreign_key="team_plans.id")
    
    # Relationships
    team_plan: TeamPlan = Relationship(back_populates="specialists")


class SpecialistPlanCreate(SpecialistPlanBase):
    """Specialist plan creation model."""
    pass


class SpecialistPlanUpdate(SQLModel):
    """Specialist plan update model."""
    name: Optional[str] = None
    role: Optional[str] = None
    description: Optional[str] = None
    model: Optional[str] = None
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None
    system_prompt: Optional[str] = None
    role_prompt: Optional[str] = None
    capabilities: Optional[List[str]] = None
    tools: Optional[List[str]] = None
    workflow_position: Optional[int] = None
    dependencies: Optional[List[str]] = None
    specialist_metadata: Optional[Dict] = None


class SpecialistPlanResponse(SpecialistPlanBase):
    """Specialist plan response model."""
    id: int
    team_plan_id: int
    created_at: datetime
    updated_at: Optional[datetime]


# Template Models
class TemplateCategory(str, Enum):
    """Template category enumeration."""
    BUSINESS = "business"
    TECHNICAL = "technical"
    CREATIVE = "creative"
    ANALYSIS = "analysis"
    ANALYTICS = "analytics"  # 添加数据分析类别
    PRODUCT = "product"      # 添加产品开发类别
    SUPPORT = "support"
    EDUCATION = "education"
    INVESTIGATION = "investigation"
    CONSULTING = "consulting"
    RESEARCH = "research"
    CUSTOMER_SERVICE = "customer_service"
    MARKETING = "marketing"
    SALES = "sales"
    HEALTHCARE = "healthcare"
    FINANCE = "finance"
    LEGAL = "legal"
    OTHER = "other"


class TemplateDifficulty(str, Enum):
    """Template difficulty enumeration."""
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"


class TemplateVisibility(str, Enum):
    """Template visibility enumeration."""
    PRIVATE = "private"  # Only visible to the owner
    SHARED = "shared"    # Shared with specific users
    PUBLIC = "public"    # Visible to all users
    FEATURED = "featured"  # Featured public templates


class TemplateStatus(str, Enum):
    """Template status enumeration."""
    DRAFT = "draft"
    ACTIVE = "active"
    ARCHIVED = "archived"
    DEPRECATED = "deprecated"


class TemplateBase(SQLModel):
    """Base template model."""
    template_id: str = Field(unique=True, index=True)

    # Basic info
    name: str = Field(max_length=255)
    description: str = Field(max_length=2000)
    category: TemplateCategory
    difficulty: TemplateDifficulty

    # User ownership and visibility
    user_id: Optional[int] = Field(default=None, foreign_key="users.id", index=True)
    visibility: TemplateVisibility = Field(default=TemplateVisibility.PRIVATE)
    status: TemplateStatus = Field(default=TemplateStatus.ACTIVE)

    # Template content
    prompt_template: str = Field(max_length=10000)
    team_structure_template: Dict = Field(sa_column=Column(JSON))

    # Configuration
    default_config: Dict = Field(default_factory=dict, sa_column=Column(JSON))

    # Usage and rating statistics
    usage_count: int = Field(default=0)
    rating: Optional[float] = Field(default=None, ge=1.0, le=5.0)
    rating_count: int = Field(default=0)  # Number of ratings

    # Organization and discovery
    tags: List[str] = Field(default_factory=list, sa_column=Column(JSON))
    keywords: List[str] = Field(default_factory=list, sa_column=Column(JSON))  # For search

    # Versioning
    version: str = Field(default="1.0.0", max_length=20)
    parent_template_id: Optional[str] = Field(default=None, index=True)  # For versioning/forking

    # Source information
    source_agent_id: Optional[str] = Field(default=None, index=True)  # If created from an agent
    author_name: Optional[str] = Field(default=None, max_length=255)

    # Additional metadata
    use_case: Optional[str] = Field(default=None, max_length=500)  # Brief use case description

    # Legacy fields for backward compatibility
    is_active: bool = Field(default=True)  # Maps to status
    is_featured: bool = Field(default=False)  # Maps to visibility
    author: Optional[str] = Field(default=None, max_length=255)  # Legacy author field

    # Extended metadata
    template_metadata: Dict = Field(default_factory=dict, sa_column=Column(JSON))


class Template(TemplateBase, BaseModel, UUIDMixin, table=True):
    """Template database model."""
    __tablename__ = "templates"
    __table_args__ = (
        Index('idx_templates_user_id', 'user_id'),
        Index('idx_templates_category', 'category'),
        Index('idx_templates_visibility', 'visibility'),
        Index('idx_templates_status', 'status'),
        Index('idx_templates_difficulty', 'difficulty'),
        Index('idx_templates_parent_template_id', 'parent_template_id'),
        Index('idx_templates_source_agent_id', 'source_agent_id'),
        Index('idx_templates_usage_count', 'usage_count'),
        Index('idx_templates_rating', 'rating'),
        {'extend_existing': True}
    )

    # Relationships
    user: Optional["User"] = Relationship(back_populates="templates")


class TemplateCreate(SQLModel):
    """Template creation model."""
    name: str = Field(max_length=255)
    description: str = Field(max_length=2000)
    category: TemplateCategory
    difficulty: TemplateDifficulty

    # Template content
    prompt_template: str = Field(max_length=10000)
    team_structure_template: Dict

    # Optional fields
    visibility: TemplateVisibility = Field(default=TemplateVisibility.PRIVATE)
    status: TemplateStatus = Field(default=TemplateStatus.ACTIVE)
    default_config: Optional[Dict] = Field(default_factory=dict)
    tags: Optional[List[str]] = Field(default_factory=list)
    keywords: Optional[List[str]] = Field(default_factory=list)
    use_case: Optional[str] = Field(default=None, max_length=500)
    source_agent_id: Optional[str] = None
    parent_template_id: Optional[str] = None
    template_metadata: Optional[Dict] = Field(default_factory=dict, sa_column=Column(JSON))


class TemplateUpdate(SQLModel):
    """Template update model."""
    name: Optional[str] = Field(default=None, max_length=255)
    description: Optional[str] = Field(default=None, max_length=2000)
    category: Optional[TemplateCategory] = None
    difficulty: Optional[TemplateDifficulty] = None
    visibility: Optional[TemplateVisibility] = None
    status: Optional[TemplateStatus] = None
    prompt_template: Optional[str] = Field(default=None, max_length=10000)
    team_structure_template: Optional[Dict] = None
    default_config: Optional[Dict] = None
    tags: Optional[List[str]] = None
    keywords: Optional[List[str]] = None
    use_case: Optional[str] = Field(default=None, max_length=500)
    template_metadata: Optional[Dict] = None

    # Legacy fields for backward compatibility
    is_active: Optional[bool] = None
    is_featured: Optional[bool] = None
    author: Optional[str] = Field(default=None, max_length=255)
    version: Optional[str] = Field(default=None, max_length=20)


class TemplateResponse(TemplateBase, UUIDMixin):
    """Template response model."""
    id: int
    created_at: datetime
    updated_at: Optional[datetime]

    # Computed fields
    is_owner: Optional[bool] = None  # Set by API based on current user
    can_edit: Optional[bool] = None  # Set by API based on permissions


class TemplateListResponse(SQLModel):
    """Template list response model for efficient listing."""
    id: int
    template_id: str
    name: str
    description: str
    category: TemplateCategory
    difficulty: TemplateDifficulty
    visibility: TemplateVisibility
    status: TemplateStatus
    tags: List[str]
    usage_count: int
    rating: Optional[float]
    rating_count: int
    version: str
    author_name: Optional[str]
    use_case: Optional[str]
    created_at: datetime
    updated_at: Optional[datetime]

    # Computed fields
    is_owner: Optional[bool] = None
    can_edit: Optional[bool] = None


class TemplateFromAgentRequest(SQLModel):
    """Request model for creating template from agent."""
    agent_id: str
    name: str = Field(max_length=255)
    description: str = Field(max_length=2000)
    category: TemplateCategory
    difficulty: TemplateDifficulty
    visibility: TemplateVisibility = Field(default=TemplateVisibility.PRIVATE)
    tags: Optional[List[str]] = Field(default_factory=list)
    keywords: Optional[List[str]] = Field(default_factory=list)
    use_case: Optional[str] = Field(default=None, max_length=500)
    example_input: Optional[str] = Field(default=None, max_length=1000)
    expected_output: Optional[str] = Field(default=None, max_length=1000)


# Pagination models
class PaginationMeta(SQLModel):
    """Pagination metadata."""
    page: int = Field(ge=1, description="Current page number")
    limit: int = Field(ge=1, le=100, description="Items per page")
    total: int = Field(ge=0, description="Total number of items")
    total_pages: int = Field(ge=0, description="Total number of pages")
    has_next: bool = Field(description="Whether there is a next page")
    has_prev: bool = Field(description="Whether there is a previous page")


class PaginatedTemplateListResponse(SQLModel):
    """Paginated template list response."""
    items: List[TemplateListResponse] = Field(description="List of templates")
    pagination: PaginationMeta = Field(description="Pagination metadata")
