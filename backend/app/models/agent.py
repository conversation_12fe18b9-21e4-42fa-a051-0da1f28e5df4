"""
Agent-related database models.
"""

from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any, TYPE_CHECKING

from sqlmodel import Field, SQLModel, Column, Relationship
from sqlalchemy import <PERSON><PERSON><PERSON>, ForeignKey

from app.models.base import BaseModel, UUIDMixin

if TYPE_CHECKING:
    from app.models.user import User
    from app.models.intelligence import AgentMetrics


class AgentStatus(str, Enum):
    """Agent status enumeration."""
    CREATING = "creating"
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    DELETED = "deleted"


class AgentType(str, Enum):
    """Agent type enumeration."""
    SINGLE = "single"
    TEAM = "team"
    WORKFLOW = "workflow"


# Agent Models
class AgentBase(SQLModel):
    """Base agent model."""
    agent_id: str = Field(unique=True, index=True)
    team_name: str = Field(max_length=255)
    description: str
    agent_type: AgentType = Field(default=AgentType.TEAM)
    status: AgentStatus = Field(default=AgentStatus.CREATING)

    # User ownership
    user_id: Optional[int] = Field(default=None, foreign_key="users.id", index=True)

    # Configuration
    prompt_template: Optional[str] = None
    system_prompt: Optional[str] = None



    # Team configuration - store complete team plan as JSON
    team_plan: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON))

    # Extracted team members for quick access
    team_members: Optional[List[Dict[str, Any]]] = Field(default=None, sa_column=Column(JSON))

    # Usage statistics
    usage_count: int = Field(default=0)
    last_used: Optional[datetime] = None




class Agent(AgentBase, BaseModel, UUIDMixin, table=True):
    """Agent database model."""
    __tablename__ = "agents"

    # Override table args to handle potential conflicts
    __table_args__ = {'extend_existing': True}

    # Relationships
    user: Optional["User"] = Relationship(back_populates="agents")
    metrics: Optional["AgentMetrics"] = Relationship(back_populates="agent")


class AgentCreate(AgentBase):
    """Agent creation model."""
    # Override user_id to make it optional for creation (will be set in API endpoint)
    user_id: Optional[int] = Field(default=None, foreign_key="users.id", index=True)


class AgentUpdate(SQLModel):
    """Agent update model."""
    team_name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[AgentStatus] = None
    prompt_template: Optional[str] = None
    system_prompt: Optional[str] = None



    team_plan: Optional[Dict[str, Any]] = None
    team_members: Optional[List[Dict[str, Any]]] = None


class AgentResponse(AgentBase, UUIDMixin):
    """Agent response model."""
    id: int
    created_at: datetime
    updated_at: Optional[datetime]


class AgentWithTeamResponse(AgentResponse):
    """Enhanced agent response model with team information."""
    # This inherits team_plan and team_members from AgentBase
    # Additional computed fields can be added here if needed
    specialists: Optional[List[Dict[str, Any]]] = None  # Alias for team_members for backward compatibility
