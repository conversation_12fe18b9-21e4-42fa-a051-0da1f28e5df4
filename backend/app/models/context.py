"""
Context-aware agent models for dynamic context sharing between team members.
"""

from typing import List, Optional, Dict, Any
from sqlmodel import SQLModel, Field
from pydantic import BaseModel, field_validator


class ContextPlaceholder(BaseModel):
    """Model for context placeholders used in team member prompts with semantic naming."""
    placeholder_name: str = Field(
        description="Semantic placeholder name with format {agent_role.output_type}, e.g., '{data_collector.validated_dataset}'"
    )
    source_agent_role: str = Field(
        description="Role identifier of the team member that provides this context"
    )
    semantic_description: str = Field(
        description="Clear semantic description of what type of information this represents"
    )
    source_step: str = Field(
        description="Source workflow step that generates this context"
    )

    # Legacy field for backward compatibility
    description: Optional[str] = Field(
        default=None,
        description="Legacy description field (deprecated, use semantic_description)"
    )

    @field_validator('placeholder_name')
    @classmethod
    def validate_placeholder_name(cls, v):
        """Ensure placeholder name is properly formatted with curly braces and semantic naming."""
        if not v.startswith('{') or not v.endswith('}'):
            raise ValueError("Placeholder name must be wrapped in curly braces")

        # Extract content inside braces
        content = v[1:-1]

        # Check for semantic format {agent_role.output_type} or legacy format
        if '.' in content:
            parts = content.split('.')
            if len(parts) != 2:
                raise ValueError("Semantic placeholder format should be {agent_role.output_type}")
            agent_role, output_type = parts
            if not agent_role or not output_type:
                raise ValueError("Both agent_role and output_type must be non-empty")
        # Allow legacy formats for backward compatibility
        elif content in ['user_input', 'user_requirements', 'previous_analysis', 'analysis_results']:
            pass  # Legacy format is acceptable
        else:
            # For new placeholders, encourage semantic format
            import warnings
            warnings.warn(
                f"Consider using semantic format {{agent_role.output_type}} instead of {v}",
                DeprecationWarning
            )

        return v

    @field_validator('semantic_description')
    @classmethod
    def validate_semantic_description(cls, v):
        """Ensure semantic description is meaningful."""
        stripped = v.strip()
        if len(stripped) < 5:
            raise ValueError("Semantic description should be at least 5 characters long")
        return stripped

    def get_agent_role(self) -> Optional[str]:
        """Extract agent role from semantic placeholder name."""
        if not self.placeholder_name.startswith('{') or not self.placeholder_name.endswith('}'):
            return None

        content = self.placeholder_name[1:-1]
        if '.' in content:
            return content.split('.')[0]
        return None

    def get_output_type(self) -> Optional[str]:
        """Extract output type from semantic placeholder name."""
        if not self.placeholder_name.startswith('{') or not self.placeholder_name.endswith('}'):
            return None

        content = self.placeholder_name[1:-1]
        if '.' in content:
            return content.split('.')[1]
        return None

    def is_semantic_format(self) -> bool:
        """Check if placeholder uses semantic naming format."""
        content = self.placeholder_name[1:-1] if self.placeholder_name.startswith('{') else self.placeholder_name
        return '.' in content and len(content.split('.')) == 2


class ContextAwareTeamMember(BaseModel):
    """Enhanced team member model with context placeholder support."""
    name: str = Field(description="Team member name")
    role: str = Field(description="Team member role identifier")
    description: str = Field(description="Team member description")
    system_prompt: str = Field(
        description="System prompt with optional context placeholders"
    )
    capabilities: List[str] = Field(default_factory=list)
    tools: List[str] = Field(default_factory=list)
    model: str = Field(default="gpt-4")
    temperature: float = Field(default=0.7, ge=0.0, le=2.0)
    max_tokens: int = Field(default=2000, gt=0)
    
    # New context-aware fields
    context_placeholders: List[ContextPlaceholder] = Field(
        default_factory=list,
        description="List of context placeholders this member can use"
    )


class ContextAwareWorkflowStep(BaseModel):
    """Enhanced workflow step model with context dependencies."""
    name: str = Field(description="Step name")
    description: str = Field(description="Step description")
    assignee: str = Field(description="Team member responsible for this step")
    inputs: List[str] = Field(default_factory=list)
    outputs: List[str] = Field(default_factory=list)
    
    # New context-aware fields
    context_dependencies: List[str] = Field(
        default_factory=list,
        description="List of previous steps this step depends on for context"
    )


class ContextAwareWorkflow(BaseModel):
    """Enhanced workflow model with context-aware steps."""
    steps: List[ContextAwareWorkflowStep] = Field(default_factory=list)


class ContextAwareTeamPlan(BaseModel):
    """Enhanced team plan model with context-aware features."""
    team_name: str
    description: str
    objective: str
    domain: str = Field(default="general")
    complexity: str = Field(default="intermediate")
    
    team_members: List[ContextAwareTeamMember]
    workflow: ContextAwareWorkflow
    
    # Metadata
    created_at: Optional[str] = None
    generation_method: Optional[str] = None
    user_request: Optional[str] = None
    ai_model_config: Optional[Dict[str, Any]] = None


class RuntimeContext(BaseModel):
    """Model for storing runtime context during team execution."""
    execution_id: str = Field(description="Unique execution identifier")
    step_name: str = Field(description="Current step name")
    member_name: str = Field(description="Team member name")
    context_data: Dict[str, Any] = Field(
        default_factory=dict,
        description="Context data generated by this step"
    )
    timestamp: str = Field(description="When this context was created")
    
    # Context metadata
    input_tokens: Optional[int] = None
    output_tokens: Optional[int] = None
    processing_time_ms: Optional[int] = None


class ContextManager(BaseModel):
    """Model for managing context during team execution."""
    execution_id: str
    contexts: Dict[str, RuntimeContext] = Field(
        default_factory=dict,
        description="Map of step_name -> RuntimeContext"
    )
    placeholder_mappings: Dict[str, str] = Field(
        default_factory=dict,
        description="Map of placeholder_name -> actual_value"
    )
    
    def add_context(self, context: RuntimeContext) -> None:
        """Add a new runtime context."""
        self.contexts[context.step_name] = context
        
    def get_context(self, step_name: str) -> Optional[RuntimeContext]:
        """Get context for a specific step."""
        return self.contexts.get(step_name)
    
    def resolve_placeholder(self, placeholder_name: str, source_step: str, source_agent_role: Optional[str] = None) -> Optional[str]:
        """Resolve a placeholder to its actual value using semantic naming."""
        context = self.get_context(source_step)
        if not context:
            return None

        # Handle semantic placeholder format {agent_role.output_type}
        if placeholder_name.startswith('{') and placeholder_name.endswith('}'):
            content = placeholder_name[1:-1]

            if '.' in content:
                # Semantic format: {agent_role.output_type}
                agent_role, output_type = content.split('.', 1)

                # Verify the source agent role matches if provided
                if source_agent_role and agent_role != source_agent_role:
                    # Log warning but continue with resolution
                    pass

                # Try to resolve based on output type
                return self._resolve_semantic_output(context, output_type)
            else:
                # Legacy format handling
                return self._resolve_legacy_placeholder(placeholder_name, context)

        # Fallback to generic resolution
        return context.context_data.get("output", "")

    def _resolve_semantic_output(self, context: RuntimeContext, output_type: str) -> Optional[str]:
        """Resolve semantic output type to actual value."""
        context_data = context.context_data

        # Map semantic output types to context data keys
        semantic_mappings = {
            # Data-related outputs
            "validated_dataset": context_data.get("validated_data", context_data.get("output", "")),
            "raw_dataset": context_data.get("raw_data", context_data.get("output", "")),
            "cleaned_data": context_data.get("cleaned_data", context_data.get("output", "")),
            "analysis_results": context_data.get("analysis", context_data.get("output", "")),
            "insights": context_data.get("insights", context_data.get("output", "")),
            "recommendations": context_data.get("recommendations", context_data.get("output", "")),

            # Content-related outputs
            "content_outline": context_data.get("outline", context_data.get("output", "")),
            "draft_content": context_data.get("draft", context_data.get("output", "")),
            "revised_content": context_data.get("revised", context_data.get("output", "")),
            "final_content": context_data.get("final", context_data.get("output", "")),

            # Research-related outputs
            "research_findings": context_data.get("findings", context_data.get("output", "")),
            "market_analysis": context_data.get("market_data", context_data.get("output", "")),
            "trend_insights": context_data.get("trends", context_data.get("output", "")),

            # Technical outputs
            "code_implementation": context_data.get("code", context_data.get("output", "")),
            "test_results": context_data.get("tests", context_data.get("output", "")),
            "documentation": context_data.get("docs", context_data.get("output", "")),

            # User inputs
            "requirements": context_data.get("user_input", context_data.get("output", "")),
            "brief": context_data.get("user_input", context_data.get("output", "")),
            "specifications": context_data.get("user_input", context_data.get("output", "")),
        }

        # Try exact match first
        if output_type in semantic_mappings:
            return semantic_mappings[output_type]

        # Try partial matches for flexibility
        for key, value in semantic_mappings.items():
            if output_type.lower() in key.lower() or key.lower() in output_type.lower():
                return value

        # Fallback to generic output
        return context_data.get("output", "")

    def _resolve_legacy_placeholder(self, placeholder_name: str, context: RuntimeContext) -> Optional[str]:
        """Resolve legacy placeholder formats for backward compatibility."""
        context_data = context.context_data

        # Legacy placeholder mappings
        legacy_mappings = {
            "{user_input}": context_data.get("user_input", ""),
            "{user_requirements}": context_data.get("user_input", ""),
            "{previous_analysis}": context_data.get("output", ""),
            "{analysis_results}": context_data.get("output", ""),
            "{content}": context_data.get("output", ""),
            "{data}": context_data.get("output", ""),
            "{results}": context_data.get("output", ""),
        }

        return legacy_mappings.get(placeholder_name, context_data.get("output", ""))

    def replace_placeholders(self, text: str, placeholders: List[ContextPlaceholder]) -> str:
        """Replace all placeholders in text with their actual values using semantic resolution."""
        result = text

        for placeholder in placeholders:
            placeholder_name = placeholder.placeholder_name
            source_step = placeholder.source_step
            source_agent_role = getattr(placeholder, 'source_agent_role', None)

            if placeholder_name in result:
                actual_value = self.resolve_placeholder(
                    placeholder_name,
                    source_step,
                    source_agent_role
                )
                if actual_value:
                    result = result.replace(placeholder_name, actual_value)
                else:
                    # Keep placeholder if no value found, or replace with informative message
                    semantic_desc = getattr(placeholder, 'semantic_description', 'context data')
                    result = result.replace(
                        placeholder_name,
                        f"[{semantic_desc} - no data available from {source_agent_role or 'previous step'}]"
                    )

        return result


# Validation functions
def validate_team_plan_context_structure(team_plan: Dict[str, Any]) -> bool:
    """Validate that a team plan has proper context structure."""
    try:
        # Try to parse as context-aware team plan
        ContextAwareTeamPlan(**team_plan)
        return True
    except Exception:
        return False


def ensure_context_compatibility(team_plan: Dict[str, Any]) -> Dict[str, Any]:
    """Ensure team plan is compatible with context-aware execution."""
    # Add missing context fields to existing team plans
    if "team_members" in team_plan:
        for member in team_plan["team_members"]:
            if "context_placeholders" not in member:
                member["context_placeholders"] = []
    
    if "workflow" in team_plan and "steps" in team_plan["workflow"]:
        for step in team_plan["workflow"]["steps"]:
            if "context_dependencies" not in step:
                step["context_dependencies"] = []
    
    return team_plan
