"""
Application logging models for storing comprehensive system events.
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
from sqlmodel import SQLModel, Field, Column, Text, Index
from enum import Enum

from app.core.timezone_utils import utc_now, normalize_datetime_for_db
from app.models.base import BaseModel, UUIDMixin


class LogLevel(str, Enum):
    """Log level enumeration."""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class EventType(str, Enum):
    """Event type enumeration for categorizing log entries."""
    # Authentication events
    USER_LOGIN = "user_login"
    USER_LOGIN_2FA = "user_login_2fa"
    USER_LOGOUT = "user_logout"
    USER_REGISTER = "user_register"
    PASSWORD_CHANGE = "password_change"
    PASSWORD_RESET = "password_reset"
    
    # Agent operations
    AGENT_CREATE = "agent_create"
    AGENT_UPDATE = "agent_update"
    AGENT_DELETE = "agent_delete"
    AGENT_EXECUTE = "agent_execute"
    
    # Test operations
    TEST_START = "test_start"
    TEST_COMPLETE = "test_complete"
    TEST_FAIL = "test_fail"
    TEST_CANCEL = "test_cancel"
    
    # API key management
    API_KEY_CREATE = "api_key_create"
    API_KEY_UPDATE = "api_key_update"
    API_KEY_DELETE = "api_key_delete"
    API_KEY_USE = "api_key_use"
    
    # System configuration
    SYSTEM_SETTINGS_UPDATE = "system_settings_update"
    SYSTEM_STARTUP = "system_startup"
    SYSTEM_SHUTDOWN = "system_shutdown"
    
    # Template operations
    TEMPLATE_CREATE = "template_create"
    TEMPLATE_UPDATE = "template_update"
    TEMPLATE_DELETE = "template_delete"
    TEMPLATE_USE = "template_use"
    
    # Error events
    SYSTEM_ERROR = "system_error"
    API_ERROR = "api_error"
    DATABASE_ERROR = "database_error"
    EXTERNAL_API_ERROR = "external_api_error"
    
    # Performance events
    PERFORMANCE_METRIC = "performance_metric"
    SLOW_QUERY = "slow_query"
    HIGH_MEMORY_USAGE = "high_memory_usage"
    
    # Security events
    SUSPICIOUS_ACTIVITY = "suspicious_activity"
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    UNAUTHORIZED_ACCESS = "unauthorized_access"


class ApplicationLogBase(SQLModel):
    """Base application log model."""
    # Core log information
    level: LogLevel = Field(index=True, description="Log level")
    event_type: EventType = Field(index=True, description="Type of event being logged")
    message: str = Field(sa_column=Column(Text), description="Human-readable log message")
    
    # Context information
    user_id: Optional[int] = Field(default=None, foreign_key="users.id", index=True, description="User associated with this log entry")
    session_id: Optional[str] = Field(default=None, index=True, description="Session identifier")
    request_id: Optional[str] = Field(default=None, index=True, description="Request identifier for tracing")
    
    # Source information
    source_module: Optional[str] = Field(default=None, max_length=100, description="Module or component that generated the log")
    source_function: Optional[str] = Field(default=None, max_length=100, description="Function that generated the log")
    source_file: Optional[str] = Field(default=None, max_length=255, description="Source file path")
    source_line: Optional[int] = Field(default=None, description="Line number in source file")
    
    # Request/Response data
    request_method: Optional[str] = Field(default=None, max_length=10, description="HTTP method")
    request_path: Optional[str] = Field(default=None, max_length=500, description="Request path")
    request_headers: Optional[str] = Field(default=None, sa_column=Column(Text), description="Request headers (JSON string)")
    request_body: Optional[str] = Field(default=None, sa_column=Column(Text), description="Request body (JSON string)")
    response_status: Optional[int] = Field(default=None, description="HTTP response status code")
    response_headers: Optional[str] = Field(default=None, sa_column=Column(Text), description="Response headers (JSON string)")
    response_body: Optional[str] = Field(default=None, sa_column=Column(Text), description="Response body (JSON string)")
    
    # Performance metrics
    execution_time_ms: Optional[float] = Field(default=None, description="Execution time in milliseconds")
    memory_usage_mb: Optional[float] = Field(default=None, description="Memory usage in megabytes")
    cpu_usage_percent: Optional[float] = Field(default=None, description="CPU usage percentage")
    
    # Entity references
    agent_id: Optional[str] = Field(default=None, index=True, description="Agent ID if applicable")
    test_id: Optional[str] = Field(default=None, index=True, description="Test ID if applicable")
    template_id: Optional[str] = Field(default=None, index=True, description="Template ID if applicable")
    api_key_id: Optional[int] = Field(default=None, description="API key ID if applicable")
    
    # Error information
    error_code: Optional[str] = Field(default=None, max_length=50, description="Error code")
    error_type: Optional[str] = Field(default=None, max_length=100, description="Error type/class")
    stack_trace: Optional[str] = Field(default=None, sa_column=Column(Text), description="Stack trace for errors")
    
    # Additional metadata
    log_metadata: Optional[str] = Field(default=None, sa_column=Column(Text), description="Additional structured data (JSON string)")
    tags: Optional[str] = Field(default=None, sa_column=Column(Text), description="Tags for categorization (JSON string)")
    
    # Client information
    ip_address: Optional[str] = Field(default=None, max_length=45, description="Client IP address")
    user_agent: Optional[str] = Field(default=None, max_length=500, description="Client user agent")
    
    # Timestamp
    timestamp: datetime = Field(default_factory=lambda: normalize_datetime_for_db(utc_now()), index=True, description="When the log entry was created")


class ApplicationLog(ApplicationLogBase, BaseModel, UUIDMixin, table=True):
    """Application log database model."""
    __tablename__ = "application_logs"
    __table_args__ = (
        # Indexes for efficient querying
        Index('idx_app_logs_user_timestamp', 'user_id', 'timestamp'),
        Index('idx_app_logs_level_timestamp', 'level', 'timestamp'),
        Index('idx_app_logs_event_timestamp', 'event_type', 'timestamp'),
        Index('idx_app_logs_agent_timestamp', 'agent_id', 'timestamp'),
        Index('idx_app_logs_test_timestamp', 'test_id', 'timestamp'),
        Index('idx_app_logs_request_id', 'request_id'),
        Index('idx_app_logs_session_id', 'session_id'),
        Index('idx_app_logs_source_module', 'source_module'),
        Index('idx_app_logs_error_code', 'error_code'),
        Index('idx_app_logs_ip_address', 'ip_address'),
        {'extend_existing': True}
    )


class ApplicationLogCreate(SQLModel):
    """Model for creating application log entries."""
    level: LogLevel
    event_type: EventType
    message: str
    user_id: Optional[int] = None
    session_id: Optional[str] = None
    request_id: Optional[str] = None
    source_module: Optional[str] = None
    source_function: Optional[str] = None
    source_file: Optional[str] = None
    source_line: Optional[int] = None
    request_method: Optional[str] = None
    request_path: Optional[str] = None
    request_headers: Optional[str] = None
    request_body: Optional[str] = None
    response_status: Optional[int] = None
    response_headers: Optional[str] = None
    response_body: Optional[str] = None
    execution_time_ms: Optional[float] = None
    memory_usage_mb: Optional[float] = None
    cpu_usage_percent: Optional[float] = None
    agent_id: Optional[str] = None
    test_id: Optional[str] = None
    template_id: Optional[str] = None
    api_key_id: Optional[int] = None
    error_code: Optional[str] = None
    error_type: Optional[str] = None
    stack_trace: Optional[str] = None
    log_metadata: Optional[str] = None
    tags: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None


class ApplicationLogResponse(SQLModel):
    """Model for application log API responses."""
    id: int
    uuid: str
    level: LogLevel
    event_type: EventType
    message: str
    user_id: Optional[int]
    session_id: Optional[str]
    request_id: Optional[str]
    source_module: Optional[str]
    source_function: Optional[str]
    execution_time_ms: Optional[float]
    agent_id: Optional[str]
    test_id: Optional[str]
    template_id: Optional[str]
    error_code: Optional[str]
    error_type: Optional[str]
    ip_address: Optional[str]
    timestamp: datetime
    created_at: datetime


class ApplicationLogDetailResponse(ApplicationLogResponse):
    """Detailed application log response including all fields."""
    source_file: Optional[str]
    source_line: Optional[int]
    request_method: Optional[str]
    request_path: Optional[str]
    request_headers: Optional[Dict[str, Any]]
    request_body: Optional[Dict[str, Any]]
    response_status: Optional[int]
    response_headers: Optional[Dict[str, Any]]
    response_body: Optional[Dict[str, Any]]
    memory_usage_mb: Optional[float]
    cpu_usage_percent: Optional[float]
    api_key_id: Optional[int]
    stack_trace: Optional[str]
    log_metadata: Optional[Dict[str, Any]]
    tags: Optional[List[str]]
    user_agent: Optional[str]


class ApplicationLogListResponse(SQLModel):
    """Response model for paginated application log list."""
    logs: List[ApplicationLogResponse]
    total: int
    page: int
    limit: int
    has_next: bool
    has_prev: bool


class LogFilterParams(SQLModel):
    """Parameters for filtering logs."""
    level: Optional[LogLevel] = None
    event_type: Optional[EventType] = None
    user_id: Optional[int] = None
    agent_id: Optional[str] = None
    test_id: Optional[str] = None
    template_id: Optional[str] = None
    source_module: Optional[str] = None
    error_code: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    search_query: Optional[str] = None
    tags: Optional[List[str]] = None
    ip_address: Optional[str] = None
