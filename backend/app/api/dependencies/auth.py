"""
Authentication dependencies.
"""

from datetime import datetime, timezone
from typing import Optional

from fastapi import Depends, HTTPException, Request, status
from fastapi.security import HTT<PERSON>uthorizationCredentials, HTTPBearer
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select

from app.api.dependencies.database import get_db
from app.core.security import verify_token
from app.models.user import User, UserSession, UserRole, UserStatus, SessionStatus

# Security scheme
security = HTTPBearer(auto_error=False)


async def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: AsyncSession = Depends(get_db),
) -> Optional[User]:
    """Get current user from token."""
    if not credentials:
        return None

    token = credentials.credentials
    user_id = verify_token(token)

    if not user_id:
        return None

    # Get user from database
    try:
        user_id_int = int(user_id)
        statement = select(User).where(User.id == user_id_int)
        result = await db.execute(statement)
        user = result.scalar_one_or_none()

        if not user:
            return None

        # Check if user is active
        if user.status not in [UserStatus.ACTIVE, UserStatus.PENDING_VERIFICATION]:
            return None

        # Update last activity
        user.last_activity_at = datetime.now(timezone.utc).replace(tzinfo=None)
        db.add(user)
        await db.commit()
        await db.refresh(user)

        return user
    except (ValueError, Exception):
        return None


async def get_current_user_required(
    current_user: Optional[User] = Depends(get_current_user),
) -> User:
    """Get current user (required)."""
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return current_user


async def get_current_active_user(
    current_user: User = Depends(get_current_user_required),
) -> User:
    """Get current active user."""
    if current_user.status != UserStatus.ACTIVE:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User account is not active",
        )

    return current_user


async def get_current_verified_user(
    current_user: User = Depends(get_current_active_user),
) -> User:
    """Get current verified user."""
    if not current_user.is_email_verified:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Email verification required",
        )

    return current_user


async def get_current_admin_user(
    current_user: User = Depends(get_current_verified_user),
) -> User:
    """Get current admin user."""
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin privileges required",
        )

    return current_user


async def get_request_info(request: Request) -> dict:
    """Extract request information for security logging."""
    client_ip = request.client.host if request.client else "unknown"
    if "x-forwarded-for" in request.headers:
        client_ip = request.headers["x-forwarded-for"].split(",")[0].strip()

    user_agent = request.headers.get("user-agent", "")

    # Basic device detection
    is_mobile = any(device in user_agent.lower() for device in [
        "mobile", "android", "iphone", "ipad", "tablet"
    ])

    return {
        "ip_address": client_ip,
        "user_agent": user_agent,
        "is_mobile": is_mobile,
        "is_secure": request.url.scheme == "https",
    }


async def validate_session(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: AsyncSession = Depends(get_db),
) -> Optional[UserSession]:
    """Validate user session from token."""
    if not credentials:
        return None

    token = credentials.credentials

    # Find active session with this token
    statement = select(UserSession).where(
        UserSession.session_token == token,
        UserSession.status == SessionStatus.ACTIVE,
        UserSession.expires_at > datetime.now(timezone.utc).replace(tzinfo=None)
    )
    result = await db.execute(statement)
    session = result.scalar_one_or_none()

    if not session:
        return None

    # Update session activity
    session.last_activity_at = datetime.now(timezone.utc).replace(tzinfo=None)

    # Update request info if needed
    request_info = await get_request_info(request)
    if session.ip_address != request_info["ip_address"]:
        # IP changed - could be suspicious, but allow for now
        # In production, you might want to invalidate the session
        pass

    db.add(session)
    await db.commit()
    await db.refresh(session)

    return session


async def get_api_key(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
) -> Optional[str]:
    """Get API key from Authorization header."""
    if not credentials:
        return None
    
    # Check if it's an API key (starts with 'ma_')
    if credentials.credentials.startswith('ma_'):
        return credentials.credentials
    
    return None


async def validate_api_key(
    api_key: Optional[str] = Depends(get_api_key),
    db: AsyncSession = Depends(get_db),
) -> bool:
    """Validate API key against database."""
    if not api_key:
        return False

    try:
        from app.models.settings import APIKey, APIKeyStatus
        from sqlalchemy import select

        # First check format
        from app.core.security import validate_api_key as validate_key_format
        if not validate_key_format(api_key):
            return False

        # Check against database
        result = await db.execute(
            select(APIKey).where(
                APIKey.encrypted_key == api_key,
                APIKey.status == APIKeyStatus.ACTIVE
            )
        )
        api_key_record = result.scalar_one_or_none()

        return api_key_record is not None
    except Exception:
        # If database check fails, fall back to format validation
        from app.core.security import validate_api_key as validate_key_format
        return validate_key_format(api_key)


# Dependencies
CurrentUser = Depends(get_current_user)
CurrentUserRequired = Depends(get_current_user_required)
CurrentActiveUser = Depends(get_current_active_user)
CurrentVerifiedUser = Depends(get_current_verified_user)
CurrentAdminUser = Depends(get_current_admin_user)
RequestInfo = Depends(get_request_info)
ValidSession = Depends(validate_session)
APIKey = Depends(get_api_key)
ValidAPIKey = Depends(validate_api_key)
