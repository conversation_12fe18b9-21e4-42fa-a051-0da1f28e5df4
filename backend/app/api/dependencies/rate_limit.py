"""
Rate limiting dependencies.
"""

from fastapi import Depends, HTTPException, Request, status

from app.core.config import get_rate_limit_config
from app.core.security import RateLimiter


async def get_rate_limiter() -> RateLimiter:
    """Get rate limiter instance."""
    # Initialize with in-memory storage for now
    # In production, this should be replaced with Redis for distributed rate limiting
    return RateLimiter()


async def rate_limit_check(
    request: Request,
    rate_limiter: RateLimiter = Depends(get_rate_limiter),
) -> None:
    """Check rate limit for request."""
    # Get client identifier
    client_ip = request.client.host if request.client else "unknown"
    if "x-forwarded-for" in request.headers:
        client_ip = request.headers["x-forwarded-for"].split(",")[0].strip()
    
    # Get endpoint
    endpoint = f"{request.method}:{request.url.path}"
    
    # Check rate limit
    config = get_rate_limit_config()
    is_allowed = await rate_limiter.is_allowed(
        identifier=client_ip,
        endpoint=endpoint,
        limit=config["per_minute"],
        window=60,
    )
    
    if not is_allowed:
        remaining = await rate_limiter.get_remaining(
            identifier=client_ip,
            endpoint=endpoint,
            limit=config["per_minute"],
        )
        
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Rate limit exceeded",
            headers={
                "X-RateLimit-Limit": str(config["per_minute"]),
                "X-RateLimit-Remaining": str(remaining),
                "X-RateLimit-Reset": "60",
            },
        )


# Dependency
RateLimit = Depends(rate_limit_check)
