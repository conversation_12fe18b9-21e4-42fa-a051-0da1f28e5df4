"""
Two-Factor Authentication dependencies and middleware.
"""

import json
from typing import Optional
from fastapi import Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.dependencies.auth import get_current_active_user
from app.api.dependencies.database import get_db
from app.core.security import verify_totp_code, verify_backup_code
from app.models.user import User


def create_two_factor_dependency(operation_name: str = "this operation"):
    """Create a dependency that requires 2FA verification for sensitive operations."""

    async def two_factor_required(
        totp_code: Optional[str] = None,
        backup_code: Optional[str] = None,
        current_user: User = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_db)
    ) -> User:
        """Verify 2FA if user has it enabled."""
        
        # If user doesn't have 2FA enabled, allow operation
        if not current_user.is_2fa_enabled:
            return current_user
        
        # If user has 2FA enabled, require verification
        if not totp_code and not backup_code:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Two-factor authentication required for {operation_name}. Please provide totp_code or backup_code."
            )

        verification_passed = False

        # Verify TOTP code
        if totp_code:
            verification_passed = verify_totp_code(current_user.totp_secret, totp_code)

        # Verify backup code
        elif backup_code:
            if current_user.backup_codes:
                hashed_codes = json.loads(current_user.backup_codes)
                verification_passed = verify_backup_code(backup_code, hashed_codes)

                # If backup code is used, remove it from the list
                if verification_passed:
                    from app.core.security import verify_password
                    remaining_codes = [
                        code for code in hashed_codes
                        if not verify_password(backup_code, code)
                    ]
                    current_user.backup_codes = json.dumps(remaining_codes)
                    db.add(current_user)
                    await db.commit()

        if not verification_passed:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid verification code"
            )

        return current_user

    return two_factor_required


# Pre-configured dependencies for common operations
RequireTwoFactorForPasswordChange = create_two_factor_dependency("password change")
RequireTwoFactorForEmailChange = create_two_factor_dependency("email change")
RequireTwoFactorForAccountDeletion = create_two_factor_dependency("account deletion")
RequireTwoFactorForTwoFactorDisable = create_two_factor_dependency("disabling two-factor authentication")
RequireTwoFactorForSystemSettings = create_two_factor_dependency("system settings change")


async def verify_2fa_for_operation(
    operation_name: str,
    totp_code: Optional[str] = None,
    backup_code: Optional[str] = None,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> bool:
    """
    Standalone function to verify 2FA for any operation.
    Returns True if verification passed or 2FA is not enabled.
    Raises HTTPException if verification fails.
    """
    
    # If user doesn't have 2FA enabled, allow operation
    if not current_user.is_2fa_enabled:
        return True
    
    # If user has 2FA enabled, require verification
    if not totp_code and not backup_code:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Two-factor authentication required for {operation_name}. Please provide totp_code or backup_code."
        )
    
    verification_passed = False
    
    # Verify TOTP code
    if totp_code:
        verification_passed = verify_totp_code(current_user.totp_secret, totp_code)
    
    # Verify backup code
    elif backup_code:
        if current_user.backup_codes:
            hashed_codes = json.loads(current_user.backup_codes)
            verification_passed = verify_backup_code(backup_code, hashed_codes)
            
            # If backup code is used, remove it from the list
            if verification_passed:
                from app.core.security import verify_password
                remaining_codes = [
                    code for code in hashed_codes 
                    if not verify_password(backup_code, code)
                ]
                current_user.backup_codes = json.dumps(remaining_codes)
                db.add(current_user)
                await db.commit()
    
    if not verification_passed:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid verification code"
        )
    
    return True
