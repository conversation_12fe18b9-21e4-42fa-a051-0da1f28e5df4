"""
API v1 router.
"""

from fastapi import APIRouter

from app.api.v1.endpoints import agents, auth, health, planning, system, logs, api_keys, settings, templates, test_history, test_execution, intelligence, two_factor, dashboard, websocket

api_router = APIRouter()

# Include endpoint routers
api_router.include_router(health.router, prefix="/health", tags=["Health"])
api_router.include_router(auth.router, prefix="/auth", tags=["Authentication"])
api_router.include_router(planning.router, prefix="/planning", tags=["Planning"])
api_router.include_router(agents.router, prefix="/agents", tags=["Agents"])
api_router.include_router(templates.router, prefix="/templates", tags=["Templates"])
api_router.include_router(system.router, prefix="/system", tags=["System"])
api_router.include_router(logs.router, prefix="/logs", tags=["Logs"])
api_router.include_router(api_keys.router, prefix="/api-keys", tags=["API Keys"])
api_router.include_router(settings.router, prefix="/settings", tags=["Settings"])
api_router.include_router(test_history.router, prefix="/test-history", tags=["Test History"])
api_router.include_router(test_execution.router, prefix="/test-execution", tags=["Test Execution"])
api_router.include_router(intelligence.router, prefix="/intelligence", tags=["Intelligence"])
api_router.include_router(two_factor.router, prefix="/2fa", tags=["Two-Factor Authentication"])
api_router.include_router(dashboard.router, prefix="/dashboard", tags=["Dashboard"])
api_router.include_router(websocket.router, prefix="/ws", tags=["WebSocket"])
