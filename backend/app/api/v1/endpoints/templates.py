"""
Template management endpoints.
"""

import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text, and_, or_

from app.api.dependencies.database import get_db
from app.api.dependencies.auth import get_current_active_user
from app.core.exceptions import NotFoundError, ValidationError
from app.core.logging import api_logger
from app.models.planning import (
    Template, TemplateCreate, TemplateUpdate, TemplateResponse,
    TemplateListResponse, TemplateFromAgentRequest, PaginationMeta, PaginatedTemplateListResponse,
    TemplateCategory, TemplateDifficulty, TemplateVisibility, TemplateStatus
)
from app.models.user import User
from app.models.agent import Agent

router = APIRouter()


def parse_template_json_fields(template_dict: dict) -> dict:
    """Parse JSON fields in template dictionary to ensure proper types and formats."""
    import json

    # Parse team_structure_template if it's a string
    if isinstance(template_dict.get("team_structure_template"), str):
        try:
            template_dict["team_structure_template"] = json.loads(template_dict["team_structure_template"])
        except (json.JSONDecodeError, TypeError):
            template_dict["team_structure_template"] = {}
    elif template_dict.get("team_structure_template") is None:
        template_dict["team_structure_template"] = {}

    # Parse default_config if it's a string
    if isinstance(template_dict.get("default_config"), str):
        try:
            template_dict["default_config"] = json.loads(template_dict["default_config"])
        except (json.JSONDecodeError, TypeError):
            template_dict["default_config"] = {}
    elif template_dict.get("default_config") is None:
        template_dict["default_config"] = {}

    # Parse template_metadata if it's a string
    if isinstance(template_dict.get("template_metadata"), str):
        try:
            template_dict["template_metadata"] = json.loads(template_dict["template_metadata"])
        except (json.JSONDecodeError, TypeError):
            template_dict["template_metadata"] = {}
    elif template_dict.get("template_metadata") is None:
        template_dict["template_metadata"] = {}

    # Parse tags if it's a string
    if isinstance(template_dict.get("tags"), str):
        try:
            template_dict["tags"] = json.loads(template_dict["tags"])
        except (json.JSONDecodeError, TypeError):
            template_dict["tags"] = []
    elif template_dict.get("tags") is None:
        template_dict["tags"] = []

    # Parse keywords if it's a string
    if isinstance(template_dict.get("keywords"), str):
        try:
            template_dict["keywords"] = json.loads(template_dict["keywords"])
        except (json.JSONDecodeError, TypeError):
            template_dict["keywords"] = []
    elif template_dict.get("keywords") is None:
        template_dict["keywords"] = []

    # Convert enum values to lowercase for Pydantic compatibility
    enum_fields = ["category", "difficulty", "visibility", "status"]
    for field in enum_fields:
        if template_dict.get(field) and isinstance(template_dict[field], str):
            template_dict[field] = template_dict[field].lower()

    return template_dict


@router.get("/", response_model=PaginatedTemplateListResponse)
async def list_templates(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page"),
    category: Optional[TemplateCategory] = Query(None),
    difficulty: Optional[TemplateDifficulty] = Query(None),
    visibility: Optional[TemplateVisibility] = Query(None),
    status: Optional[TemplateStatus] = Query(None),
    search: Optional[str] = Query(None),
    tags: Optional[str] = Query(None, description="Comma-separated tags"),
    my_templates: bool = Query(False, description="Show only user's templates"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> PaginatedTemplateListResponse:
    """List templates with filtering and pagination."""
    try:
        # Calculate skip from page
        skip = (page - 1) * limit

        # Build base query
        query_conditions = []
        params = {}

        # User isolation - show public templates and user's own templates
        if my_templates:
            query_conditions.append("user_id = :user_id")
            params["user_id"] = current_user.id
        else:
            query_conditions.append(
                "(visibility IN ('public', 'featured') OR user_id = :user_id)"
            )
            params["user_id"] = current_user.id

        # Status filter - exclude deleted by default
        if status:
            query_conditions.append("status = :status")
            params["status"] = status
        else:
            query_conditions.append("status != 'archived'")

        # Category filter
        if category:
            query_conditions.append("category = :category")
            params["category"] = category

        # Difficulty filter
        if difficulty:
            query_conditions.append("difficulty = :difficulty")
            params["difficulty"] = difficulty

        # Visibility filter
        if visibility:
            query_conditions.append("visibility = :visibility")
            params["visibility"] = visibility

        # Search filter
        if search:
            search_term = f"%{search}%"
            query_conditions.append(
                "(name ILIKE :search OR description ILIKE :search OR use_case ILIKE :search)"
            )
            params["search"] = search_term

        # Tags filter
        if tags:
            tag_list = [tag.strip() for tag in tags.split(",")]
            # Use JSON contains operator for tag search
            for i, tag in enumerate(tag_list):
                query_conditions.append(f"JSON_CONTAINS(tags, :tag_{i})")
                params[f"tag_{i}"] = f'"{tag}"'

        # Build where clause
        where_clause = " AND ".join(query_conditions)

        # First, get total count
        count_query = f"""
            SELECT COUNT(*) as total
            FROM templates
            WHERE {where_clause}
        """

        count_result = await db.execute(text(count_query), params)
        total = count_result.scalar()

        # Calculate pagination metadata
        total_pages = (total + limit - 1) // limit  # Ceiling division
        has_next = page < total_pages
        has_prev = page > 1

        # Build data query
        data_query = f"""
            SELECT id, template_id, name, description, category, difficulty,
                   visibility, status, tags, usage_count, rating, rating_count,
                   version, author_name, use_case, created_at, updated_at, user_id
            FROM templates
            WHERE {where_clause}
            ORDER BY
                CASE WHEN visibility = 'featured' THEN 0 ELSE 1 END,
                rating DESC NULLS LAST,
                usage_count DESC,
                created_at DESC
            LIMIT :limit OFFSET :skip
        """

        params.update({"limit": limit, "skip": skip})

        result = await db.execute(text(data_query), params)
        templates = result.fetchall()

        # Convert to response models
        template_responses = []
        for template in templates:
            template_dict = {column: getattr(template, column) for column in template._fields}

            # Parse JSON fields and normalize enum values
            template_dict = parse_template_json_fields(template_dict)

            template_dict["is_owner"] = template_dict["user_id"] == current_user.id
            template_dict["can_edit"] = template_dict["user_id"] == current_user.id
            template_responses.append(TemplateListResponse(**template_dict))

        # Create pagination metadata
        pagination = PaginationMeta(
            page=page,
            limit=limit,
            total=total,
            total_pages=total_pages,
            has_next=has_next,
            has_prev=has_prev
        )

        return PaginatedTemplateListResponse(
            items=template_responses,
            pagination=pagination
        )

    except Exception as e:
        api_logger.error(f"Failed to list templates: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list templates: {str(e)}",
        )


@router.get("/legacy", response_model=List[TemplateListResponse])
async def list_templates_legacy(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    category: Optional[TemplateCategory] = Query(None),
    difficulty: Optional[TemplateDifficulty] = Query(None),
    visibility: Optional[TemplateVisibility] = Query(None),
    status: Optional[TemplateStatus] = Query(None),
    search: Optional[str] = Query(None),
    tags: Optional[str] = Query(None, description="Comma-separated tags"),
    my_templates: bool = Query(False, description="Show only user's templates"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> List[TemplateListResponse]:
    """Legacy endpoint for backward compatibility - returns templates without pagination metadata."""
    # Convert skip/limit to page-based parameters
    page = (skip // limit) + 1 if limit > 0 else 1

    # Call the main paginated endpoint
    paginated_response = await list_templates(
        page=page,
        limit=limit,
        category=category,
        difficulty=difficulty,
        visibility=visibility,
        status=status,
        search=search,
        tags=tags,
        my_templates=my_templates,
        db=db,
        current_user=current_user
    )

    # Return just the items for backward compatibility
    return paginated_response.items


@router.get("/{template_id}", response_model=TemplateResponse)
async def get_template(
    template_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> TemplateResponse:
    """Get template by ID."""
    try:
        # Check if template exists and user has access
        query = """
            SELECT * FROM templates 
            WHERE template_id = :template_id 
            AND (visibility IN ('public', 'featured') OR user_id = :user_id)
        """
        result = await db.execute(
            text(query), 
            {"template_id": template_id, "user_id": current_user.id}
        )
        template = result.fetchone()

        if not template:
            raise NotFoundError("Template", template_id)
        
        # Convert to dict and add computed fields
        template_dict = {column: getattr(template, column) for column in template._fields}

        # Parse JSON fields and normalize enum values
        template_dict = parse_template_json_fields(template_dict)

        template_dict["is_owner"] = template_dict["user_id"] == current_user.id
        template_dict["can_edit"] = template_dict["user_id"] == current_user.id

        return TemplateResponse(**template_dict)

    except NotFoundError:
        raise
    except Exception as e:
        api_logger.error(f"Failed to get template {template_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get template: {str(e)}",
        )


@router.get("/{template_id}/agent-config", response_model=Dict[str, Any])
async def get_template_agent_config(
    template_id: str,
    customizations: Optional[str] = Query(None, description="JSON string of customizations"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Dict[str, Any]:
    """Get complete agent configuration from template for direct deployment."""
    try:
        from app.services.template_management_service import TemplateManagementService

        # Parse customizations if provided
        custom_dict = {}
        if customizations:
            try:
                import json
                custom_dict = json.loads(customizations)
            except (json.JSONDecodeError, TypeError):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid customizations JSON format"
                )

        # Get complete agent configuration from template
        template_service = TemplateManagementService(db)
        agent_config = await template_service.transform_template_for_agent_creation(
            template_id, current_user, custom_dict if custom_dict else None
        )

        # Add deployment readiness information
        response = {
            "template_id": template_id,
            "agent_config": agent_config,
            "ready_to_deploy": agent_config["metadata"]["ready_to_deploy"],
            "requires_ai_generation": agent_config["metadata"]["requires_ai_generation"],
            "team_members_count": len(agent_config.get("team_members", [])),
            "has_workflow": bool(agent_config.get("team_plan", {}).get("workflow", {}).get("steps")),
            "customizations_applied": bool(custom_dict),
            "deployment_method": "config_driven" if agent_config["metadata"]["ready_to_deploy"] else "ai_generation",
        }

        api_logger.info(
            "Template agent config retrieved",
            template_id=template_id,
            user_id=current_user.id,
            ready_to_deploy=response["ready_to_deploy"],
            team_members_count=response["team_members_count"],
        )

        return response

    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Template '{template_id}' not found"
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        api_logger.error(f"Failed to get template agent config {template_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get template agent config: {str(e)}",
        )


@router.get("/complete-library", response_model=List[Dict[str, Any]])
async def get_complete_template_library(
    include_user_templates: bool = Query(True, description="Include user's private templates"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> List[Dict[str, Any]]:
    """Get complete template library including system templates and user templates."""
    try:
        templates = []

        # Get system/deployable templates
        from app.services.complete_templates import get_complete_templates_service
        complete_templates_service = get_complete_templates_service()
        system_templates = complete_templates_service.get_all_templates()

        # Add system template marker and enhance with deployment info
        for template in system_templates:
            enhanced_template = template.copy()
            enhanced_template.update({
                "source": "system",
                "is_deployable": True,
                "ready_to_deploy": True,
                "template_type": "complete_deployable",
                "is_owner": False,
                "can_edit": False,
            })
            templates.append(enhanced_template)

        # Get user templates from database if requested
        if include_user_templates:
            query = """
                SELECT template_id, name, description, category, difficulty,
                       visibility, status, tags, usage_count, rating, rating_count,
                       version, author_name, use_case, created_at, updated_at, user_id,
                       team_structure_template, template_metadata
                FROM templates
                WHERE (visibility IN ('public', 'featured') OR user_id = :user_id)
                AND status = 'active'
                ORDER BY created_at DESC
            """
            result = await db.execute(text(query), {"user_id": current_user.id})
            db_templates = result.fetchall()

            for template in db_templates:
                template_dict = {column: getattr(template, column) for column in template._fields}

                # Parse JSON fields
                if template_dict.get("tags") and isinstance(template_dict["tags"], str):
                    try:
                        import json
                        template_dict["tags"] = json.loads(template_dict["tags"])
                    except (json.JSONDecodeError, TypeError):
                        template_dict["tags"] = []

                if template_dict.get("team_structure_template") and isinstance(template_dict["team_structure_template"], str):
                    try:
                        import json
                        template_dict["team_structure_template"] = json.loads(template_dict["team_structure_template"])
                    except (json.JSONDecodeError, TypeError):
                        template_dict["team_structure_template"] = {}

                if template_dict.get("template_metadata") and isinstance(template_dict["template_metadata"], str):
                    try:
                        import json
                        template_dict["template_metadata"] = json.loads(template_dict["template_metadata"])
                    except (json.JSONDecodeError, TypeError):
                        template_dict["template_metadata"] = {}

                # Check if template is ready to deploy
                team_structure = template_dict.get("team_structure_template", {})
                metadata = template_dict.get("template_metadata", {})
                is_ready = (
                    metadata.get("ready_to_deploy", False) and
                    bool(team_structure.get("team_members")) and
                    not metadata.get("requires_ai_generation", True)
                )

                enhanced_template = {
                    "id": template_dict["template_id"],
                    "name": template_dict["name"],
                    "description": template_dict["description"],
                    "category": template_dict["category"],
                    "difficulty": template_dict["difficulty"],
                    "use_case": template_dict["use_case"],
                    "tags": template_dict["tags"],
                    "usage_count": template_dict["usage_count"],
                    "rating": template_dict["rating"],
                    "author_name": template_dict["author_name"],
                    "created_at": template_dict["created_at"].isoformat() if template_dict["created_at"] else None,
                    "source": "user",
                    "is_deployable": is_ready,
                    "ready_to_deploy": is_ready,
                    "template_type": metadata.get("template_type", "user_created"),
                    "is_owner": template_dict["user_id"] == current_user.id,
                    "can_edit": template_dict["user_id"] == current_user.id,
                    "team_members_count": len(team_structure.get("team_members", [])),
                    "has_workflow": bool(team_structure.get("workflow", {}).get("steps")),
                }
                templates.append(enhanced_template)

        api_logger.info(
            "Complete template library retrieved",
            user_id=current_user.id,
            total_templates=len(templates),
            system_templates=len(system_templates),
            user_templates=len(templates) - len(system_templates),
        )

        return templates

    except Exception as e:
        api_logger.error(f"Failed to get complete template library: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get complete template library: {str(e)}",
        )


@router.post("/{template_id}/validate", response_model=Dict[str, Any])
async def validate_template(
    template_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Dict[str, Any]:
    """Validate template completeness and deployability."""
    try:
        from app.services.template_management_service import TemplateManagementService

        template_service = TemplateManagementService(db)
        validation_result = await template_service.validate_template_completeness(
            template_id, current_user
        )

        api_logger.info(
            "Template validation completed",
            template_id=template_id,
            user_id=current_user.id,
            validation_score=validation_result["validation_score"],
            is_deployable=validation_result["is_deployable"],
        )

        return validation_result

    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Template '{template_id}' not found"
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        api_logger.error(f"Failed to validate template {template_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to validate template: {str(e)}",
        )


@router.post("/", response_model=TemplateResponse)
async def create_template(
    template_data: TemplateCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> TemplateResponse:
    """Create a new template."""
    try:
        # Generate unique template ID
        template_id = f"template_{uuid.uuid4().hex[:12]}"
        
        # Create template record
        template = Template(
            template_id=template_id,
            name=template_data.name,
            description=template_data.description,
            category=template_data.category,
            difficulty=template_data.difficulty,
            visibility=template_data.visibility,
            status=template_data.status,
            prompt_template=template_data.prompt_template,
            team_structure_template=template_data.team_structure_template,
            default_config=template_data.default_config or {},
            tags=template_data.tags or [],
            keywords=template_data.keywords or [],
            use_case=template_data.use_case,
            source_agent_id=template_data.source_agent_id,
            parent_template_id=template_data.parent_template_id,
            metadata=template_data.metadata or {},
            user_id=current_user.id,
            author_name=current_user.name,
            usage_count=0,
            rating_count=0,
        )

        db.add(template)
        await db.commit()
        await db.refresh(template)
        
        api_logger.info(
            "Template created",
            template_id=template_id,
            name=template.name,
            user_id=current_user.id,
        )
        
        # Convert to response model
        template_dict = {
            column: getattr(template, column)
            for column in template.__table__.columns.keys()
        }

        # Parse JSON fields to ensure proper types
        template_dict = parse_template_json_fields(template_dict)

        template_dict["is_owner"] = True
        template_dict["can_edit"] = True

        return TemplateResponse(**template_dict)
        
    except Exception as e:
        api_logger.error(f"Failed to create template: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create template: {str(e)}",
        )


@router.post("/from-agent", response_model=TemplateResponse)
async def create_template_from_agent(
    request: TemplateFromAgentRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> TemplateResponse:
    """Create a template from an existing agent."""
    try:
        # Check if agent exists and belongs to user
        agent_query = """
            SELECT * FROM agents 
            WHERE agent_id = :agent_id AND user_id = :user_id
        """
        result = await db.execute(
            text(agent_query), 
            {"agent_id": request.agent_id, "user_id": current_user.id}
        )
        agent = result.fetchone()

        if not agent:
            raise NotFoundError("Agent", request.agent_id)
        
        # Extract agent data for template
        agent_dict = {column: getattr(agent, column) for column in agent._fields}

        # Parse team_plan if it's a JSON string (to avoid double serialization)
        team_plan = agent_dict.get("team_plan", {})
        if isinstance(team_plan, str):
            try:
                import json
                team_plan = json.loads(team_plan)
            except (json.JSONDecodeError, TypeError):
                team_plan = {}
        elif team_plan is None:
            team_plan = {}

        # Generate unique template ID
        template_id = f"template_{uuid.uuid4().hex[:12]}"

        # Create template from agent data
        template = Template(
            template_id=template_id,
            name=request.name,
            description=request.description,
            category=request.category,
            difficulty=request.difficulty,
            visibility=request.visibility,
            status=TemplateStatus.ACTIVE,
            prompt_template=agent_dict.get("prompt_template", ""),
            team_structure_template=team_plan,  # Use parsed team_plan
            default_config={},
            tags=request.tags or [],
            keywords=request.keywords or [],
            use_case=request.use_case,
            source_agent_id=request.agent_id,
            template_metadata={
                "created_from_agent": True,
                "source_agent_name": agent_dict.get("team_name", ""),
            },
            user_id=current_user.id,
            author_name=current_user.name,
            usage_count=0,
            rating_count=0,
        )

        db.add(template)
        await db.commit()
        await db.refresh(template)
        
        api_logger.info(
            "Template created from agent",
            template_id=template_id,
            source_agent_id=request.agent_id,
            user_id=current_user.id,
        )
        
        # Convert to response model
        template_dict = {
            column: getattr(template, column)
            for column in template.__table__.columns.keys()
        }

        # Parse JSON fields to ensure proper types
        template_dict = parse_template_json_fields(template_dict)

        template_dict["is_owner"] = True
        template_dict["can_edit"] = True

        return TemplateResponse(**template_dict)
        
    except NotFoundError:
        raise
    except Exception as e:
        api_logger.error(f"Failed to create template from agent: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create template from agent: {str(e)}",
        )


@router.put("/{template_id}", response_model=TemplateResponse)
@router.patch("/{template_id}", response_model=TemplateResponse)
async def update_template(
    template_id: str,
    template_update: TemplateUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> TemplateResponse:
    """Update template."""
    try:
        # Check if template exists and belongs to current user
        result = await db.execute(
            text("SELECT * FROM templates WHERE template_id = :template_id AND user_id = :user_id"),
            {"template_id": template_id, "user_id": current_user.id}
        )
        template = result.fetchone()

        if not template:
            raise NotFoundError("Template", template_id)

        # Build update query dynamically
        update_fields = []
        params = {"template_id": template_id, "user_id": current_user.id}

        # Map update fields
        field_mapping = {
            "name": "name",
            "description": "description",
            "category": "category",
            "difficulty": "difficulty",
            "visibility": "visibility",
            "status": "status",
            "prompt_template": "prompt_template",
            "team_structure_template": "team_structure_template",
            "default_config": "default_config",
            "tags": "tags",
            "keywords": "keywords",
            "use_case": "use_case",
            "example_input": "example_input",
            "expected_output": "expected_output",
            "metadata": "metadata",
            "version": "version",
        }

        # Handle legacy fields
        if template_update.is_active is not None:
            template_update.status = TemplateStatus.ACTIVE if template_update.is_active else TemplateStatus.ARCHIVED

        if template_update.is_featured is not None:
            template_update.visibility = TemplateVisibility.FEATURED if template_update.is_featured else TemplateVisibility.PUBLIC

        # Build update fields
        for field, column in field_mapping.items():
            value = getattr(template_update, field, None)
            if value is not None:
                update_fields.append(f"{column} = :{field}")
                params[field] = value

        if not update_fields:
            # No fields to update, return current template
            template_dict = {column: getattr(template, column) for column in template._fields}

            # Parse JSON fields and normalize enum values
            template_dict = parse_template_json_fields(template_dict)

            template_dict["is_owner"] = True
            template_dict["can_edit"] = True
            return TemplateResponse(**template_dict)

        # Add updated_at
        update_fields.append("updated_at = :updated_at")
        params["updated_at"] = datetime.now(timezone.utc).replace(tzinfo=None)

        # Execute update
        update_query = f"""
            UPDATE templates
            SET {', '.join(update_fields)}
            WHERE template_id = :template_id AND user_id = :user_id
        """

        await db.execute(text(update_query), params)
        await db.commit()

        # Fetch updated template
        result = await db.execute(
            text("SELECT * FROM templates WHERE template_id = :template_id"),
            {"template_id": template_id}
        )
        updated_template = result.fetchone()

        api_logger.info(
            "Template updated",
            template_id=template_id,
            user_id=current_user.id,
        )

        # Convert to response model
        template_dict = {column: getattr(updated_template, column) for column in updated_template._fields}

        # Parse JSON fields and normalize enum values
        template_dict = parse_template_json_fields(template_dict)

        template_dict["is_owner"] = True
        template_dict["can_edit"] = True

        return TemplateResponse(**template_dict)

    except NotFoundError:
        raise
    except Exception as e:
        api_logger.error(f"Failed to update template {template_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update template: {str(e)}",
        )


@router.delete("/{template_id}")
async def delete_template(
    template_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Dict[str, str]:
    """Delete template."""
    try:
        # Check if template exists and belongs to current user
        result = await db.execute(
            text("SELECT * FROM templates WHERE template_id = :template_id AND user_id = :user_id"),
            {"template_id": template_id, "user_id": current_user.id}
        )
        template = result.fetchone()

        if not template:
            raise NotFoundError("Template", template_id)

        # Soft delete by setting status to archived
        await db.execute(
            text("""
                UPDATE templates
                SET status = 'archived', updated_at = :updated_at
                WHERE template_id = :template_id AND user_id = :user_id
            """),
            {
                "template_id": template_id,
                "user_id": current_user.id,
                "updated_at": datetime.now(timezone.utc).replace(tzinfo=None)
            }
        )
        await db.commit()

        api_logger.info(
            "Template deleted",
            template_id=template_id,
            user_id=current_user.id,
        )

        return {"message": "Template deleted successfully"}

    except NotFoundError:
        raise
    except Exception as e:
        api_logger.error(f"Failed to delete template {template_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete template: {str(e)}",
        )


@router.get("/categories/list", response_model=List[Dict[str, str]])
async def get_template_categories() -> List[Dict[str, str]]:
    """Get available template categories."""
    return [
        {"value": category.value, "label": category.value.replace("_", " ").title()}
        for category in TemplateCategory
    ]


@router.get("/difficulties/list", response_model=List[Dict[str, str]])
async def get_template_difficulties() -> List[Dict[str, str]]:
    """Get available template difficulties."""
    return [
        {"value": difficulty.value, "label": difficulty.value.title()}
        for difficulty in TemplateDifficulty
    ]


@router.post("/{template_id}/duplicate", response_model=TemplateResponse)
async def duplicate_template(
    template_id: str,
    name: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> TemplateResponse:
    """Duplicate an existing template."""
    try:
        # Check if template exists and user has access
        query = """
            SELECT * FROM templates
            WHERE template_id = :template_id
            AND (visibility IN ('public', 'featured') OR user_id = :user_id)
        """
        result = await db.execute(
            text(query),
            {"template_id": template_id, "user_id": current_user.id}
        )
        original_template = result.fetchone()

        if not original_template:
            raise NotFoundError("Template", template_id)

        # Generate new template ID
        new_template_id = f"template_{uuid.uuid4().hex[:12]}"

        # Create duplicate template
        original_dict = {column: getattr(original_template, column) for column in original_template._fields}

        # Parse JSON fields from original template
        def parse_json_field(field_value, default):
            if field_value and isinstance(field_value, str):
                try:
                    import json
                    return json.loads(field_value)
                except (json.JSONDecodeError, TypeError):
                    return default
            return field_value or default

        original_metadata = parse_json_field(original_dict.get("template_metadata"), {})

        # Parse JSON fields from original template before creating new one
        parsed_team_structure = parse_json_field(original_dict["team_structure_template"], {})
        parsed_default_config = parse_json_field(original_dict["default_config"], {})
        parsed_tags = parse_json_field(original_dict["tags"], [])
        parsed_keywords = parse_json_field(original_dict["keywords"], [])

        template = Template(
            template_id=new_template_id,
            name=name or f"{original_dict['name']} (Copy)",
            description=original_dict["description"],
            category=original_dict["category"],
            difficulty=original_dict["difficulty"],
            visibility=TemplateVisibility.PRIVATE,  # Always private for duplicates
            status=TemplateStatus.ACTIVE,
            prompt_template=original_dict["prompt_template"],
            team_structure_template=parsed_team_structure,
            default_config=parsed_default_config,
            tags=parsed_tags,
            keywords=parsed_keywords,
            use_case=original_dict["use_case"],
            example_input=original_dict["example_input"],
            expected_output=original_dict["expected_output"],
            parent_template_id=template_id,  # Link to original
            template_metadata={
                **original_metadata,
                "duplicated_from": template_id,
                "duplicated_at": datetime.now(timezone.utc).isoformat(),
            },
            user_id=current_user.id,
            author_name=current_user.name,
            usage_count=0,
            rating_count=0,
        )

        db.add(template)
        await db.commit()
        await db.refresh(template)

        api_logger.info(
            "Template duplicated",
            original_template_id=template_id,
            new_template_id=new_template_id,
            user_id=current_user.id,
        )

        # Convert to response model - manually build dict to avoid SQLAlchemy issues
        import json

        # Helper function to parse JSON fields
        def parse_json_field(field_value, default_value):
            if field_value is not None and isinstance(field_value, str):
                try:
                    return json.loads(field_value)
                except (json.JSONDecodeError, TypeError):
                    return default_value
            return field_value if field_value is not None else default_value

        # Manually construct the response dict with proper parsing
        template_dict = {
            "uuid": template.uuid,
            "created_at": template.created_at,
            "updated_at": template.updated_at,
            "id": template.id,
            "template_id": template.template_id,
            "name": template.name,
            "description": template.description,
            "category": template.category.lower() if template.category else None,
            "difficulty": template.difficulty.lower() if template.difficulty else None,
            "user_id": template.user_id,
            "visibility": template.visibility.lower() if template.visibility else None,
            "status": template.status.lower() if template.status else None,
            "prompt_template": template.prompt_template,
            "team_structure_template": parse_json_field(template.team_structure_template, {}),
            "default_config": parse_json_field(template.default_config, {}),
            "usage_count": template.usage_count,
            "rating": template.rating,
            "rating_count": template.rating_count,
            "tags": parse_json_field(template.tags, []),
            "keywords": parse_json_field(template.keywords, []),
            "version": template.version,
            "parent_template_id": template.parent_template_id,
            "source_agent_id": template.source_agent_id,
            "author_name": template.author_name,
            "use_case": template.use_case,
            "example_input": template.example_input,
            "expected_output": template.expected_output,
            "is_active": template.is_active,
            "is_featured": template.is_featured,
            "author": template.author,
            "template_metadata": parse_json_field(template.template_metadata, {}),
            "is_owner": True,
            "can_edit": True,
        }

        return TemplateResponse(**template_dict)

    except NotFoundError:
        raise
    except Exception as e:
        api_logger.error(f"Failed to duplicate template {template_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to duplicate template: {str(e)}",
        )


@router.get("/search", response_model=List[TemplateListResponse])
async def search_templates(
    q: str = Query(..., min_length=1, description="Search query"),
    category: Optional[TemplateCategory] = Query(None),
    difficulty: Optional[TemplateDifficulty] = Query(None),
    tags: Optional[str] = Query(None, description="Comma-separated tags"),
    limit: int = Query(20, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> List[TemplateListResponse]:
    """Advanced template search with full-text search capabilities."""
    try:
        # Build search conditions
        search_conditions = []
        params = {"user_id": current_user.id, "limit": limit}

        # Base visibility filter
        search_conditions.append("(visibility IN ('public', 'featured') OR user_id = :user_id)")
        search_conditions.append("status = 'active'")

        # Full-text search across multiple fields
        search_term = f"%{q}%"
        search_conditions.append("""
            (name ILIKE :search
             OR description ILIKE :search
             OR use_case ILIKE :search
             OR author_name ILIKE :search
             OR EXISTS (
                 SELECT 1 FROM json_array_elements_text(tags) AS tag
                 WHERE tag ILIKE :search
             )
             OR EXISTS (
                 SELECT 1 FROM json_array_elements_text(keywords) AS keyword
                 WHERE keyword ILIKE :search
             ))
        """)
        params["search"] = search_term

        # Category filter
        if category:
            search_conditions.append("category = :category")
            params["category"] = category

        # Difficulty filter
        if difficulty:
            search_conditions.append("difficulty = :difficulty")
            params["difficulty"] = difficulty

        # Tags filter
        if tags:
            tag_list = [tag.strip() for tag in tags.split(",")]
            for i, tag in enumerate(tag_list):
                search_conditions.append(f"JSON_CONTAINS(tags, :tag_{i})")
                params[f"tag_{i}"] = f'"{tag}"'

        # Build and execute query
        where_clause = " AND ".join(search_conditions)
        query = f"""
            SELECT id, template_id, name, description, category, difficulty,
                   visibility, status, tags, usage_count, rating, rating_count,
                   version, author_name, use_case, created_at, updated_at, user_id
            FROM templates
            WHERE {where_clause}
            ORDER BY
                -- Exact name matches first
                CASE WHEN name ILIKE :exact_search THEN 0 ELSE 1 END,
                -- Featured templates next
                CASE WHEN visibility = 'featured' THEN 0 ELSE 1 END,
                -- Then by relevance score (rating * usage)
                (COALESCE(rating, 0) * COALESCE(usage_count, 0)) DESC,
                created_at DESC
            LIMIT :limit
        """

        params["exact_search"] = f"{q}"

        result = await db.execute(text(query), params)
        templates = result.fetchall()

        # Convert to response models
        template_responses = []
        for template in templates:
            template_dict = {column: getattr(template, column) for column in template._fields}

            # Parse JSON fields and normalize enum values
            template_dict = parse_template_json_fields(template_dict)

            template_dict["is_owner"] = template_dict["user_id"] == current_user.id
            template_dict["can_edit"] = template_dict["user_id"] == current_user.id
            template_responses.append(TemplateListResponse(**template_dict))

        return template_responses

    except Exception as e:
        api_logger.error(f"Failed to search templates: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to search templates: {str(e)}",
        )


@router.get("/tags/popular", response_model=List[Dict[str, Any]])
async def get_popular_tags(
    limit: int = Query(20, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> List[Dict[str, Any]]:
    """Get popular tags across all public templates."""
    try:
        # Get all templates with tags (SQLite compatible approach)
        query = """
            SELECT tags, rating, usage_count
            FROM templates
            WHERE visibility IN ('public', 'featured')
            AND status = 'active'
            AND tags IS NOT NULL
            AND tags != '[]'
        """

        result = await db.execute(text(query))
        templates = result.fetchall()

        # Process tags in Python since SQLite doesn't have json_array_elements_text
        tag_stats = {}

        for template in templates:
            try:
                # Parse JSON tags
                import json
                tags = json.loads(template.tags) if isinstance(template.tags, str) else template.tags

                if isinstance(tags, list):
                    for tag in tags:
                        if tag not in tag_stats:
                            tag_stats[tag] = {
                                'count': 0,
                                'total_rating': 0,
                                'rating_count': 0,
                                'total_usage': 0
                            }

                        tag_stats[tag]['count'] += 1
                        tag_stats[tag]['total_usage'] += template.usage_count or 0

                        if template.rating:
                            tag_stats[tag]['total_rating'] += template.rating
                            tag_stats[tag]['rating_count'] += 1

            except (json.JSONDecodeError, TypeError):
                continue

        # Convert to result format and sort
        popular_tags = []
        for tag, stats in tag_stats.items():
            if stats['count'] > 1:  # Only tags used more than once
                avg_rating = (stats['total_rating'] / stats['rating_count']) if stats['rating_count'] > 0 else 0
                popular_tags.append({
                    "tag": tag,
                    "count": stats['count'],
                    "avg_rating": round(avg_rating, 2),
                    "total_usage": stats['total_usage']
                })

        # Sort by count desc, then by total usage desc
        popular_tags.sort(key=lambda x: (x['count'], x['total_usage']), reverse=True)

        return popular_tags[:limit]

    except Exception as e:
        api_logger.error(f"Failed to get popular tags: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get popular tags: {str(e)}",
        )


@router.get("/stats", response_model=Dict[str, Any])
async def get_template_stats(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Dict[str, Any]:
    """Get template statistics and analytics."""
    try:
        # Get overall stats
        stats_query = """
            SELECT
                COUNT(*) as total_templates,
                COUNT(CASE WHEN visibility = 'public' THEN 1 END) as public_templates,
                COUNT(CASE WHEN visibility = 'featured' THEN 1 END) as featured_templates,
                COUNT(CASE WHEN user_id = :user_id THEN 1 END) as user_templates,
                AVG(rating) as avg_rating,
                SUM(usage_count) as total_usage,
                COUNT(DISTINCT category) as categories_count
            FROM templates
            WHERE status = 'active'
        """

        result = await db.execute(text(stats_query), {"user_id": current_user.id})
        stats = result.fetchone()

        # Get category distribution
        category_query = """
            SELECT
                category,
                COUNT(*) as count,
                AVG(rating) as avg_rating,
                SUM(usage_count) as total_usage
            FROM templates
            WHERE status = 'active'
            AND visibility IN ('public', 'featured')
            GROUP BY category
            ORDER BY count DESC
        """

        result = await db.execute(text(category_query))
        categories = result.fetchall()

        # Get difficulty distribution
        difficulty_query = """
            SELECT
                difficulty,
                COUNT(*) as count,
                AVG(rating) as avg_rating
            FROM templates
            WHERE status = 'active'
            AND visibility IN ('public', 'featured')
            GROUP BY difficulty
            ORDER BY
                CASE difficulty
                    WHEN 'beginner' THEN 1
                    WHEN 'intermediate' THEN 2
                    WHEN 'advanced' THEN 3
                    WHEN 'expert' THEN 4
                END
        """

        result = await db.execute(text(difficulty_query))
        difficulties = result.fetchall()

        return {
            "overview": {
                "total_templates": stats.total_templates,
                "public_templates": stats.public_templates,
                "featured_templates": stats.featured_templates,
                "user_templates": stats.user_templates,
                "avg_rating": round(float(stats.avg_rating or 0), 2),
                "total_usage": stats.total_usage,
                "categories_count": stats.categories_count,
            },
            "categories": [
                {
                    "category": cat.category,
                    "count": cat.count,
                    "avg_rating": round(float(cat.avg_rating or 0), 2),
                    "total_usage": cat.total_usage,
                }
                for cat in categories
            ],
            "difficulties": [
                {
                    "difficulty": diff.difficulty,
                    "count": diff.count,
                    "avg_rating": round(float(diff.avg_rating or 0), 2),
                }
                for diff in difficulties
            ],
        }

    except Exception as e:
        api_logger.error(f"Failed to get template stats: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get template stats: {str(e)}",
        )


@router.get("/{template_id}/versions", response_model=List[TemplateListResponse])
async def get_template_versions(
    template_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> List[TemplateListResponse]:
    """Get all versions of a template."""
    try:
        # First check if user has access to the base template
        base_query = """
            SELECT * FROM templates
            WHERE template_id = :template_id
            AND (visibility IN ('public', 'featured') OR user_id = :user_id)
        """
        result = await db.execute(
            text(base_query),
            {"template_id": template_id, "user_id": current_user.id}
        )
        base_template = result.fetchone()

        if not base_template:
            raise NotFoundError("Template", template_id)

        # Get all versions (templates with this as parent or sharing the same parent)
        versions_query = """
            WITH template_family AS (
                -- Get the root template
                SELECT template_id as root_id FROM templates
                WHERE template_id = :template_id

                UNION

                -- Get the parent if this is a version
                SELECT parent_template_id as root_id FROM templates
                WHERE template_id = :template_id AND parent_template_id IS NOT NULL
            )
            SELECT DISTINCT t.id, t.template_id, t.name, t.description, t.category,
                   t.difficulty, t.visibility, t.status, t.tags, t.usage_count,
                   t.rating, t.rating_count, t.version, t.author_name, t.use_case,
                   t.created_at, t.updated_at, t.user_id
            FROM templates t
            CROSS JOIN template_family tf
            WHERE (t.template_id = tf.root_id OR t.parent_template_id = tf.root_id)
            AND (t.visibility IN ('public', 'featured') OR t.user_id = :user_id)
            ORDER BY t.created_at DESC
        """

        result = await db.execute(text(versions_query), {"template_id": template_id, "user_id": current_user.id})
        versions = result.fetchall()

        # Convert to response models
        version_responses = []
        for version in versions:
            version_dict = {column: getattr(version, column) for column in version._fields}

            # Parse JSON fields and normalize enum values
            version_dict = parse_template_json_fields(version_dict)

            version_dict["is_owner"] = version_dict["user_id"] == current_user.id
            version_dict["can_edit"] = version_dict["user_id"] == current_user.id
            version_responses.append(TemplateListResponse(**version_dict))

        return version_responses

    except NotFoundError:
        raise
    except Exception as e:
        api_logger.error(f"Failed to get template versions: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get template versions: {str(e)}",
        )


@router.post("/{template_id}/create-version", response_model=TemplateResponse)
async def create_template_version(
    template_id: str,
    template_update: TemplateUpdate,
    version: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> TemplateResponse:
    """Create a new version of an existing template."""
    try:
        # Check if template exists and user owns it
        result = await db.execute(
            text("SELECT * FROM templates WHERE template_id = :template_id AND user_id = :user_id"),
            {"template_id": template_id, "user_id": current_user.id}
        )
        original_template = result.fetchone()

        if not original_template:
            raise NotFoundError("Template", template_id)

        # Generate new template ID for the version
        new_template_id = f"template_{uuid.uuid4().hex[:12]}"

        # Get original template data
        original_dict = {column: getattr(original_template, column) for column in original_template._fields}

        # Auto-increment version if not provided
        if not version:
            # Parse current version and increment
            current_version = original_dict.get("version", "1.0.0")
            try:
                parts = current_version.split(".")
                if len(parts) >= 2:
                    major, minor = int(parts[0]), int(parts[1])
                    patch = int(parts[2]) if len(parts) > 2 else 0
                    version = f"{major}.{minor}.{patch + 1}"
                else:
                    version = "1.0.1"
            except (ValueError, IndexError):
                version = "1.0.1"

        # Create new version with updates
        new_template_data = {
            "template_id": new_template_id,
            "name": getattr(template_update, "name", None) or original_dict["name"],
            "description": getattr(template_update, "description", None) or original_dict["description"],
            "category": getattr(template_update, "category", None) or original_dict["category"],
            "difficulty": getattr(template_update, "difficulty", None) or original_dict["difficulty"],
            "visibility": getattr(template_update, "visibility", None) or original_dict["visibility"],
            "status": getattr(template_update, "status", None) or original_dict["status"],
            "prompt_template": getattr(template_update, "prompt_template", None) or original_dict["prompt_template"],
            "team_structure_template": getattr(template_update, "team_structure_template", None) or original_dict["team_structure_template"],
            "default_config": getattr(template_update, "default_config", None) or original_dict["default_config"],
            "tags": getattr(template_update, "tags", None) or original_dict["tags"],
            "keywords": getattr(template_update, "keywords", None) or original_dict["keywords"],
            "use_case": getattr(template_update, "use_case", None) or original_dict["use_case"],
            "example_input": getattr(template_update, "example_input", None) or original_dict["example_input"],
            "expected_output": getattr(template_update, "expected_output", None) or original_dict["expected_output"],
            "version": version,
            "parent_template_id": template_id,  # Link to original
            "source_agent_id": original_dict["source_agent_id"],
            "metadata": {
                **original_dict["metadata"],
                "version_of": template_id,
                "version_created_at": datetime.now(timezone.utc).isoformat(),
                "changes": getattr(template_update, "metadata", {}).get("changes", "Version update"),
            },
            "user_id": current_user.id,
            "author_name": current_user.name,
            "usage_count": 0,
            "rating_count": 0,
        }

        template = Template(**new_template_data)
        db.add(template)
        await db.commit()
        await db.refresh(template)

        api_logger.info(
            "Template version created",
            original_template_id=template_id,
            new_template_id=new_template_id,
            version=version,
            user_id=current_user.id,
        )

        # Convert to response model
        template_dict = {
            column: getattr(template, column)
            for column in template.__table__.columns.keys()
        }

        # Parse JSON fields and normalize enum values
        template_dict = parse_template_json_fields(template_dict)

        template_dict["is_owner"] = True
        template_dict["can_edit"] = True

        return TemplateResponse(**template_dict)

    except NotFoundError:
        raise
    except Exception as e:
        api_logger.error(f"Failed to create template version: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create template version: {str(e)}",
        )


@router.post("/{template_id}/rollback/{version_template_id}", response_model=TemplateResponse)
async def rollback_template(
    template_id: str,
    version_template_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> TemplateResponse:
    """Rollback template to a previous version."""
    try:
        # Check if user owns the main template
        result = await db.execute(
            text("SELECT * FROM templates WHERE template_id = :template_id AND user_id = :user_id"),
            {"template_id": template_id, "user_id": current_user.id}
        )
        main_template = result.fetchone()

        if not main_template:
            raise NotFoundError("Template", template_id)

        # Check if version template exists and is related
        result = await db.execute(
            text("""
                SELECT * FROM templates
                WHERE template_id = :version_template_id
                AND (parent_template_id = :template_id OR template_id = :template_id)
                AND user_id = :user_id
            """),
            {
                "version_template_id": version_template_id,
                "template_id": template_id,
                "user_id": current_user.id
            }
        )
        version_template = result.fetchone()

        if not version_template:
            raise NotFoundError("Template version", version_template_id)

        # Get version template data
        version_dict = {column: getattr(version_template, column) for column in version_template._fields}

        # Update main template with version data
        update_fields = [
            "name = :name",
            "description = :description",
            "category = :category",
            "difficulty = :difficulty",
            "prompt_template = :prompt_template",
            "team_structure_template = :team_structure_template",
            "default_config = :default_config",
            "tags = :tags",
            "keywords = :keywords",
            "use_case = :use_case",
            "example_input = :example_input",
            "expected_output = :expected_output",
            "version = :version",
            "metadata = :metadata",
            "updated_at = :updated_at"
        ]

        # Prepare rollback metadata
        rollback_metadata = {
            **version_dict["metadata"],
            "rolled_back_from": version_template_id,
            "rollback_at": datetime.now(timezone.utc).isoformat(),
            "rollback_reason": "Manual rollback to previous version",
        }

        params = {
            "template_id": template_id,
            "user_id": current_user.id,
            "name": version_dict["name"],
            "description": version_dict["description"],
            "category": version_dict["category"],
            "difficulty": version_dict["difficulty"],
            "prompt_template": version_dict["prompt_template"],
            "team_structure_template": version_dict["team_structure_template"],
            "default_config": version_dict["default_config"],
            "tags": version_dict["tags"],
            "keywords": version_dict["keywords"],
            "use_case": version_dict["use_case"],
            "example_input": version_dict["example_input"],
            "expected_output": version_dict["expected_output"],
            "version": version_dict["version"],
            "metadata": rollback_metadata,
            "updated_at": datetime.now(timezone.utc).replace(tzinfo=None),
        }

        update_query = f"""
            UPDATE templates
            SET {', '.join(update_fields)}
            WHERE template_id = :template_id AND user_id = :user_id
        """

        await db.execute(text(update_query), params)
        await db.commit()

        # Fetch updated template
        result = await db.execute(
            text("SELECT * FROM templates WHERE template_id = :template_id"),
            {"template_id": template_id}
        )
        updated_template = result.fetchone()

        api_logger.info(
            "Template rolled back",
            template_id=template_id,
            version_template_id=version_template_id,
            user_id=current_user.id,
        )

        # Convert to response model
        template_dict = {column: getattr(updated_template, column) for column in updated_template._fields}

        # Parse JSON fields and normalize enum values
        template_dict = parse_template_json_fields(template_dict)

        template_dict["is_owner"] = True
        template_dict["can_edit"] = True

        return TemplateResponse(**template_dict)

    except NotFoundError:
        raise
    except Exception as e:
        api_logger.error(f"Failed to rollback template: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to rollback template: {str(e)}",
        )


@router.post("/{template_id}/share", response_model=Dict[str, str])
async def share_template(
    template_id: str,
    visibility: TemplateVisibility,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Dict[str, str]:
    """Update template sharing settings."""
    try:
        # Check if template exists and belongs to current user
        result = await db.execute(
            text("SELECT * FROM templates WHERE template_id = :template_id AND user_id = :user_id"),
            {"template_id": template_id, "user_id": current_user.id}
        )
        template = result.fetchone()

        if not template:
            raise NotFoundError("Template", template_id)

        # Update visibility
        await db.execute(
            text("""
                UPDATE templates
                SET visibility = :visibility, updated_at = :updated_at
                WHERE template_id = :template_id AND user_id = :user_id
            """),
            {
                "template_id": template_id,
                "user_id": current_user.id,
                "visibility": visibility,
                "updated_at": datetime.now(timezone.utc).replace(tzinfo=None)
            }
        )
        await db.commit()

        api_logger.info(
            "Template sharing updated",
            template_id=template_id,
            visibility=visibility,
            user_id=current_user.id,
        )

        return {"message": f"Template sharing updated to {visibility}"}

    except NotFoundError:
        raise
    except Exception as e:
        api_logger.error(f"Failed to update template sharing: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update template sharing: {str(e)}",
        )


@router.get("/public/featured", response_model=List[TemplateListResponse])
async def get_featured_templates_public(
    limit: int = Query(10, ge=1, le=50),
    db: AsyncSession = Depends(get_db),
) -> List[TemplateListResponse]:
    """Get featured templates without authentication (public endpoint)."""
    try:
        # Query featured templates
        query = """
            SELECT * FROM templates
            WHERE visibility = 'featured' AND status = 'active'
            ORDER BY created_at DESC
            LIMIT :limit
        """
        result = await db.execute(text(query), {"limit": limit})
        templates = result.fetchall()

        # Convert to response models
        template_responses = []
        for template in templates:
            template_dict = {column: getattr(template, column) for column in template._fields}

            # Parse JSON fields and normalize enum values
            template_dict = parse_template_json_fields(template_dict)

            # For public endpoint, set default values for user-specific fields
            template_dict["is_owner"] = False
            template_dict["can_edit"] = False
            template_responses.append(TemplateListResponse(**template_dict))

        api_logger.info(
            "Featured templates retrieved (public)",
            count=len(template_responses),
            limit=limit,
        )

        return template_responses

    except Exception as e:
        api_logger.error(f"Failed to get featured templates (public): {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get featured templates: {str(e)}",
        )


@router.get("/featured", response_model=List[TemplateListResponse])
async def get_featured_templates(
    limit: int = Query(10, ge=1, le=50),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> List[TemplateListResponse]:
    """Get featured public templates."""
    try:
        query = """
            SELECT id, template_id, name, description, category, difficulty,
                   visibility, status, tags, usage_count, rating, rating_count,
                   version, author_name, use_case, created_at, updated_at, user_id
            FROM templates
            WHERE visibility = 'featured' AND status = 'active'
            ORDER BY rating DESC NULLS LAST, usage_count DESC, created_at DESC
            LIMIT :limit
        """

        result = await db.execute(text(query), {"limit": limit})
        templates = result.fetchall()

        # Convert to response models
        template_responses = []
        for template in templates:
            template_dict = {column: getattr(template, column) for column in template._fields}

            # Parse JSON fields and normalize enum values
            template_dict = parse_template_json_fields(template_dict)

            template_dict["is_owner"] = template_dict["user_id"] == current_user.id
            template_dict["can_edit"] = template_dict["user_id"] == current_user.id
            template_responses.append(TemplateListResponse(**template_dict))

        return template_responses

    except Exception as e:
        api_logger.error(f"Failed to get featured templates: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get featured templates: {str(e)}",
        )


@router.get("/community", response_model=List[TemplateListResponse])
async def get_community_templates(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    category: Optional[TemplateCategory] = Query(None),
    difficulty: Optional[TemplateDifficulty] = Query(None),
    sort_by: str = Query("popular", regex="^(popular|rating|newest|name)$"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> List[TemplateListResponse]:
    """Get community (public) templates."""
    try:
        # Build query conditions
        conditions = ["visibility IN ('public', 'featured')", "status = 'active'"]
        params = {"skip": skip, "limit": limit}

        if category:
            conditions.append("category = :category")
            params["category"] = category

        if difficulty:
            conditions.append("difficulty = :difficulty")
            params["difficulty"] = difficulty

        # Build order clause
        order_clauses = {
            "popular": "usage_count DESC, rating DESC NULLS LAST",
            "rating": "rating DESC NULLS LAST, usage_count DESC",
            "newest": "created_at DESC",
            "name": "name ASC"
        }
        order_clause = order_clauses.get(sort_by, order_clauses["popular"])

        where_clause = " AND ".join(conditions)
        query = f"""
            SELECT id, template_id, name, description, category, difficulty,
                   visibility, status, tags, usage_count, rating, rating_count,
                   version, author_name, use_case, created_at, updated_at, user_id
            FROM templates
            WHERE {where_clause}
            ORDER BY
                CASE WHEN visibility = 'featured' THEN 0 ELSE 1 END,
                {order_clause}
            LIMIT :limit OFFSET :skip
        """

        result = await db.execute(text(query), params)
        templates = result.fetchall()

        # Convert to response models
        template_responses = []
        for template in templates:
            template_dict = {column: getattr(template, column) for column in template._fields}

            # Parse JSON fields and normalize enum values
            template_dict = parse_template_json_fields(template_dict)

            template_dict["is_owner"] = template_dict["user_id"] == current_user.id
            template_dict["can_edit"] = template_dict["user_id"] == current_user.id
            template_responses.append(TemplateListResponse(**template_dict))

        return template_responses

    except Exception as e:
        api_logger.error(f"Failed to get community templates: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get community templates: {str(e)}",
        )
