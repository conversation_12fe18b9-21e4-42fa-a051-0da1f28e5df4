"""
Intelligence and analytics API endpoints.
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc
from pydantic import BaseModel

from app.api.dependencies.auth import get_current_user
from app.api.dependencies.database import get_db
from app.models.user import User
from app.models.intelligence import AgentInsight, SystemMetrics, AgentMetrics
from app.services.intelligence_service import IntelligenceService
from app.core.timezone_utils import utc_now

logger = logging.getLogger(__name__)
router = APIRouter()


# Response Models
class InsightResponse(BaseModel):
    """Response model for insights."""
    id: int
    insight_id: str
    agent_id: Optional[str]
    insight_type: str
    severity: str
    title: str
    description: str
    recommendation: str
    impact_description: Optional[str]
    confidence_score: float
    estimated_savings: Optional[float]
    estimated_improvement: Optional[float]
    priority_score: int
    is_actionable: bool
    is_acknowledged: bool
    is_resolved: bool
    metrics_data: Dict[str, Any]
    created_at: datetime

    class Config:
        from_attributes = True


class SystemMetricsResponse(BaseModel):
    """Response model for system metrics."""
    total_agents: int
    active_agents: int
    healthy_agents: int
    warning_agents: int
    critical_agents: int
    offline_agents: int
    total_executions: int
    total_users: int
    active_users_24h: int
    avg_response_time: float
    system_success_rate: float
    system_error_rate: float
    total_cost_24h: float
    timestamp: datetime

    class Config:
        from_attributes = True


class AgentMetricsResponse(BaseModel):
    """Response model for agent metrics."""
    agent_id: str
    execution_count: int
    success_count: int
    error_count: int
    avg_response_time: float
    p95_response_time: float
    total_cost: float
    avg_cost_per_execution: float
    uptime_percentage: float
    last_execution_time: Optional[datetime]
    updated_at: datetime

    class Config:
        from_attributes = True


class PredictiveAnalyticsResponse(BaseModel):
    """Response model for predictive analytics."""
    usage_trend: Dict[str, Any]
    cost_forecast: Dict[str, Any]
    performance_trend: Dict[str, Any]
    recommendations: List[Dict[str, Any]]


class InsightActionRequest(BaseModel):
    """Request model for insight actions."""
    action: str  # acknowledge, dismiss, resolve


# Endpoints
@router.get("/insights", response_model=List[InsightResponse])
async def get_insights(
    agent_id: Optional[str] = Query(None, description="Filter by agent ID"),
    insight_type: Optional[str] = Query(None, description="Filter by insight type"),
    severity: Optional[str] = Query(None, description="Filter by severity"),
    limit: int = Query(20, ge=1, le=100, description="Number of insights to return"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get AI-powered insights for the current user."""
    try:
        intelligence_service = IntelligenceService(db)
        
        # Get insights based on filters
        conditions = [
            AgentInsight.user_id == current_user.id,
            AgentInsight.is_dismissed == False
        ]

        if agent_id:
            conditions.append(AgentInsight.agent_id == agent_id)
        if insight_type:
            conditions.append(AgentInsight.insight_type == insight_type)
        if severity:
            conditions.append(AgentInsight.severity == severity)

        stmt = select(AgentInsight).where(
            and_(*conditions)
        ).order_by(
            desc(AgentInsight.priority_score),
            desc(AgentInsight.created_at)
        ).limit(limit)

        result = await db.execute(stmt)
        insights = result.scalars().all()
        
        return insights

    except Exception as e:
        logger.error(f"Error getting insights: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve insights"
        )


@router.post("/insights/generate")
async def generate_insights(
    agent_id: Optional[str] = Query(None, description="Generate insights for specific agent"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Generate new AI insights for agents."""
    try:
        intelligence_service = IntelligenceService(db)

        if agent_id:
            # Generate insights for specific agent
            insights = await intelligence_service.generate_agent_insights(agent_id, current_user.id)
        else:
            # Generate system-wide insights
            insights = await intelligence_service.generate_system_insights(current_user.id)
        
        return {
            "success": True,
            "message": f"Generated {len(insights)} insights",
            "data": {
                "insights_generated": len(insights),
                "agent_id": agent_id
            }
        }

    except Exception as e:
        logger.error(f"Error generating insights: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate insights"
        )


@router.post("/insights/{insight_id}/action")
async def insight_action(
    insight_id: str,
    action_request: InsightActionRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Perform action on an insight (acknowledge, dismiss, resolve)."""
    try:
        intelligence_service = IntelligenceService(db)

        if action_request.action == "acknowledge":
            success = await intelligence_service.acknowledge_insight(insight_id, current_user.id)
        elif action_request.action == "dismiss":
            success = await intelligence_service.dismiss_insight(insight_id, current_user.id)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid action: {action_request.action}"
            )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Insight not found"
            )
        
        return {
            "success": True,
            "message": f"Insight {action_request.action}d successfully",
            "data": {
                "insight_id": insight_id,
                "action": action_request.action
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error performing insight action: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to perform insight action"
        )


@router.get("/metrics/system", response_model=SystemMetricsResponse)
async def get_system_metrics(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get system-wide metrics and health data."""
    try:
        intelligence_service = IntelligenceService(db)

        # Update and get latest system metrics
        system_metrics = await intelligence_service.update_system_metrics()
        
        return system_metrics

    except Exception as e:
        logger.error(f"Error getting system metrics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve system metrics"
        )


@router.get("/metrics/agents", response_model=List[AgentMetricsResponse])
async def get_agent_metrics(
    agent_id: Optional[str] = Query(None, description="Filter by agent ID"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get agent performance metrics."""
    try:
        stmt = select(AgentMetrics).where(AgentMetrics.user_id == current_user.id)

        if agent_id:
            stmt = stmt.where(AgentMetrics.agent_id == agent_id)

        stmt = stmt.order_by(desc(AgentMetrics.updated_at))
        result = await db.execute(stmt)
        metrics = result.scalars().all()
        
        return metrics

    except Exception as e:
        logger.error(f"Error getting agent metrics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve agent metrics"
        )


@router.get("/analytics/predictive", response_model=PredictiveAnalyticsResponse)
async def get_predictive_analytics(
    timeframe: str = Query("30d", description="Timeframe for analysis (7d, 30d, 90d)"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get predictive analytics and forecasting."""
    try:
        # Calculate timeframe
        days = {"7d": 7, "30d": 30, "90d": 90}.get(timeframe, 30)
        start_date = utc_now() - timedelta(days=days)

        # Get user's agent metrics
        stmt = select(AgentMetrics).where(
            and_(
                AgentMetrics.user_id == current_user.id,
                AgentMetrics.updated_at >= start_date
            )
        )
        result = await db.execute(stmt)
        metrics = result.scalars().all()
        
        if not metrics:
            # Return default analytics for new users
            return PredictiveAnalyticsResponse(
                usage_trend={
                    "direction": "stable",
                    "confidence": 50,
                    "predicted_change": 0,
                    "timeframe": "下个月"
                },
                cost_forecast={
                    "next_month": 0,
                    "next_quarter": 0,
                    "trend": "stable",
                    "factors": ["新用户", "无历史数据"]
                },
                performance_trend={
                    "success_rate": {"current": 100, "predicted": 100, "trend": "stable"},
                    "response_time": {"current": 1000, "predicted": 1000, "trend": "stable"}
                },
                recommendations=[
                    {
                        "priority": "medium",
                        "action": "开始使用Agent收集数据",
                        "expected_impact": "建立性能基线",
                        "time_to_implement": "立即"
                    }
                ]
            )
        
        # Calculate trends
        total_executions = sum(m.execution_count for m in metrics)
        total_cost = sum(m.total_cost for m in metrics)
        avg_response_time = sum(m.avg_response_time for m in metrics) / len(metrics)
        avg_success_rate = sum((m.success_count / m.execution_count * 100) if m.execution_count > 0 else 100 for m in metrics) / len(metrics)
        
        # Predict usage trend (simplified)
        usage_direction = "increasing" if total_executions > 100 else "stable"
        predicted_change = min(50, total_executions / 10) if total_executions > 0 else 0
        
        # Predict costs
        monthly_cost = total_cost * (30 / days)
        quarterly_cost = monthly_cost * 3
        
        # Generate recommendations
        recommendations = []
        if total_cost > 50:
            recommendations.append({
                "priority": "high",
                "action": "实施成本优化策略",
                "expected_impact": f"节省${total_cost * 0.2:.2f}",
                "time_to_implement": "1-2周"
            })
        
        if avg_response_time > 2000:
            recommendations.append({
                "priority": "medium",
                "action": "优化Agent响应时间",
                "expected_impact": "提升用户体验30%",
                "time_to_implement": "2-3周"
            })
        
        return PredictiveAnalyticsResponse(
            usage_trend={
                "direction": usage_direction,
                "confidence": min(90, 50 + total_executions),
                "predicted_change": predicted_change,
                "timeframe": "下个月"
            },
            cost_forecast={
                "next_month": round(monthly_cost, 2),
                "next_quarter": round(quarterly_cost, 2),
                "trend": "increasing" if monthly_cost > 20 else "stable",
                "factors": ["使用量增长", "模型调用成本", "新功能使用"]
            },
            performance_trend={
                "success_rate": {
                    "current": round(avg_success_rate, 1),
                    "predicted": min(100, round(avg_success_rate + 2, 1)),
                    "trend": "improving"
                },
                "response_time": {
                    "current": round(avg_response_time, 0),
                    "predicted": max(1000, round(avg_response_time * 0.9, 0)),
                    "trend": "improving"
                }
            },
            recommendations=recommendations
        )

    except Exception as e:
        logger.error(f"Error getting predictive analytics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve predictive analytics"
        )


@router.post("/metrics/agent/{agent_id}/update")
async def update_agent_metrics(
    agent_id: str,
    execution_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Update agent metrics with execution data (internal endpoint)."""
    try:
        intelligence_service = IntelligenceService(db)

        metrics = await intelligence_service.update_agent_metrics(
            agent_id=agent_id,
            user_id=current_user.id,
            execution_data=execution_data
        )
        
        return {
            "success": True,
            "message": "Agent metrics updated successfully",
            "data": {
                "agent_id": agent_id,
                "execution_count": metrics.execution_count,
                "avg_response_time": metrics.avg_response_time,
                "success_rate": (metrics.success_count / metrics.execution_count * 100) if metrics.execution_count > 0 else 0
            }
        }

    except Exception as e:
        logger.error(f"Error updating agent metrics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update agent metrics"
        )
