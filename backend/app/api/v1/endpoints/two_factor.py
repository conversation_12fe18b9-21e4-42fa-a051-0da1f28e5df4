"""
Two-Factor Authentication API endpoints.
"""

import json
from typing import Any, Dict, List

from fastapi import APIRouter, Depends, HTTPException, Response, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.dependencies.auth import get_current_active_user
from app.api.dependencies.database import get_db
from app.core.security import (
    generate_backup_codes,
    generate_qr_code_image,
    generate_totp_qr_url,
    generate_totp_secret,
    get_password_hash,
    hash_backup_codes,
    verify_backup_code,
    verify_password,
    verify_totp_code,
)
from app.models.user import (
    TwoFactorDisable,
    TwoFactorEnable,
    TwoFactorSetup,
    TwoFactorSetupResponse,
    TwoFactorVerify,
    User,
)
from app.core.logging import get_logger

# Initialize router and logger
router = APIRouter()
api_logger = get_logger("api")


@router.post("/setup", response_model=TwoFactorSetupResponse)
async def setup_two_factor(
    setup_data: TwoFactorSetup,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> TwoFactorSetupResponse:
    """Setup 2FA for user account."""
    try:
        # Verify password
        if not verify_password(setup_data.password, current_user.password_hash):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid password"
            )
        
        # Check if 2FA is already enabled
        if current_user.is_2fa_enabled:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Two-factor authentication is already enabled"
            )
        
        # Generate TOTP secret
        secret = generate_totp_secret()
        
        # Generate QR code URL
        qr_url = generate_totp_qr_url(secret, current_user.email)
        
        # Generate backup codes
        backup_codes = generate_backup_codes()
        
        # Store secret temporarily (not enabled yet)
        current_user.totp_secret = secret
        current_user.backup_codes = json.dumps(hash_backup_codes(backup_codes))
        
        db.add(current_user)
        await db.commit()
        await db.refresh(current_user)
        
        api_logger.info(
            "2FA setup initiated",
            user_id=current_user.id,
            email=current_user.email
        )
        
        return TwoFactorSetupResponse(
            secret=secret,
            qr_code_url=qr_url,
            backup_codes=backup_codes
        )
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        api_logger.error(f"Failed to setup 2FA: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to setup two-factor authentication"
        )


@router.post("/enable")
async def enable_two_factor(
    enable_data: TwoFactorEnable,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Dict[str, str]:
    """Enable 2FA after verifying TOTP code."""
    try:
        # Check if 2FA is already enabled
        if current_user.is_2fa_enabled:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Two-factor authentication is already enabled"
            )
        
        # Check if setup was completed
        if not current_user.totp_secret:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Two-factor authentication setup not completed"
            )
        
        # Verify TOTP code
        if not verify_totp_code(current_user.totp_secret, enable_data.totp_code):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid verification code"
            )
        
        # Enable 2FA
        from datetime import datetime, timezone
        current_user.is_2fa_enabled = True
        current_user.two_fa_enabled_at = datetime.now(timezone.utc).replace(tzinfo=None)
        
        db.add(current_user)
        await db.commit()
        await db.refresh(current_user)
        
        api_logger.info(
            "2FA enabled successfully",
            user_id=current_user.id,
            email=current_user.email
        )
        
        return {"message": "Two-factor authentication enabled successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        api_logger.error(f"Failed to enable 2FA: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to enable two-factor authentication"
        )


@router.post("/disable")
async def disable_two_factor(
    disable_data: TwoFactorDisable,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Dict[str, str]:
    """Disable 2FA after verification."""
    try:
        # Check if 2FA is enabled
        if not current_user.is_2fa_enabled:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Two-factor authentication is not enabled"
            )
        
        # Verify password
        if not verify_password(disable_data.password, current_user.password_hash):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid password"
            )
        
        # Verify either TOTP code or backup code
        verification_passed = False
        
        if disable_data.totp_code:
            verification_passed = verify_totp_code(current_user.totp_secret, disable_data.totp_code)
        elif disable_data.backup_code:
            if current_user.backup_codes:
                hashed_codes = json.loads(current_user.backup_codes)
                verification_passed = verify_backup_code(disable_data.backup_code, hashed_codes)
        
        if not verification_passed:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid verification code"
            )
        
        # Disable 2FA
        current_user.is_2fa_enabled = False
        current_user.totp_secret = None
        current_user.backup_codes = None
        current_user.two_fa_enabled_at = None
        
        db.add(current_user)
        await db.commit()
        await db.refresh(current_user)
        
        api_logger.info(
            "2FA disabled successfully",
            user_id=current_user.id,
            email=current_user.email
        )
        
        return {"message": "Two-factor authentication disabled successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        api_logger.error(f"Failed to disable 2FA: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to disable two-factor authentication"
        )


@router.post("/verify")
async def verify_two_factor(
    verify_data: TwoFactorVerify,
    current_user: User = Depends(get_current_active_user),
) -> Dict[str, bool]:
    """Verify 2FA code (for testing purposes)."""
    try:
        # Check if 2FA is enabled
        if not current_user.is_2fa_enabled:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Two-factor authentication is not enabled"
            )
        
        verification_passed = False
        
        if verify_data.totp_code:
            verification_passed = verify_totp_code(current_user.totp_secret, verify_data.totp_code)
        elif verify_data.backup_code:
            if current_user.backup_codes:
                hashed_codes = json.loads(current_user.backup_codes)
                verification_passed = verify_backup_code(verify_data.backup_code, hashed_codes)
        
        return {"valid": verification_passed}
        
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to verify 2FA: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to verify two-factor authentication"
        )


@router.get("/qr-code")
async def get_qr_code(
    current_user: User = Depends(get_current_active_user),
) -> Response:
    """Get QR code image for 2FA setup."""
    try:
        # Check if setup was completed
        if not current_user.totp_secret:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Two-factor authentication setup not completed"
            )
        
        # Generate QR code URL
        qr_url = generate_totp_qr_url(current_user.totp_secret, current_user.email)
        
        # Generate QR code image
        qr_image = generate_qr_code_image(qr_url)
        
        return Response(
            content=qr_image,
            media_type="image/png",
            headers={"Content-Disposition": "inline; filename=qr-code.png"}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to generate QR code: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate QR code"
        )


@router.get("/status")
async def get_two_factor_status(
    current_user: User = Depends(get_current_active_user),
) -> Dict[str, Any]:
    """Get 2FA status for current user."""
    return {
        "is_enabled": current_user.is_2fa_enabled,
        "enabled_at": current_user.two_fa_enabled_at,
        "has_backup_codes": bool(current_user.backup_codes)
    }
