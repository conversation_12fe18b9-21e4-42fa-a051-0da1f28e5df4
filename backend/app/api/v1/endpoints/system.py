"""
System management endpoints.
"""

import time
import logging
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.api.dependencies.auth import get_current_user
from app.api.dependencies.database import get_db
from app.models.agent import Agent
from app.models.user import User
from app.models.intelligence import AgentMetrics

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/agents/{agent_id}/restart")
async def restart_agent(
    agent_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Restart a specific agent."""
    try:
        # Use raw SQL to avoid enum issues
        from sqlalchemy import text

        # Check if agent exists and user owns it
        result = await db.execute(text("""
            SELECT agent_id, user_id, status
            FROM agents
            WHERE agent_id = :agent_id
        """), {"agent_id": agent_id})

        agent_data = result.fetchone()
        if not agent_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found"
            )

        # Check if user owns the agent
        if agent_data[1] != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to restart this agent"
            )

        # Simulate restart process
        await db.execute(text("""
            UPDATE agents
            SET status = 'restarting'
            WHERE agent_id = :agent_id
        """), {"agent_id": agent_id})
        await db.commit()

        # In a real implementation, you would:
        # 1. Stop the agent process
        # 2. Clear any cached state
        # 3. Restart the agent
        # 4. Update status to "active"

        # For now, just set it back to active
        time.sleep(1)  # Simulate restart time
        await db.execute(text("""
            UPDATE agents
            SET status = 'active', last_used = :now
            WHERE agent_id = :agent_id
        """), {"agent_id": agent_id, "now": datetime.now()})
        await db.commit()
        
        return {
            "success": True,
            "message": f"Agent {agent_id} restarted successfully",
            "data": {
                "agent_id": agent_id,
                "status": "active",
                "restarted_at": datetime.now().isoformat()
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to restart agent: {str(e)}"
        )


@router.get("/metrics")
async def get_system_metrics(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get system metrics and statistics."""
    try:
        # Use raw SQL to avoid enum issues
        from sqlalchemy import text

        # Get agent counts by status using raw SQL
        result = await db.execute(text("""
            SELECT status, COUNT(*) as count
            FROM agents
            WHERE user_id = :user_id
            GROUP BY status
        """), {"user_id": current_user.id})

        status_counts = dict(result.fetchall())

        # Calculate basic stats
        total_agents = sum(status_counts.values())
        active_agents = status_counts.get("active", 0)
        inactive_agents = status_counts.get("inactive", 0)
        error_agents = status_counts.get("error", 0)

        # Get metrics for user's agents
        stmt = select(AgentMetrics).where(AgentMetrics.user_id == current_user.id)
        result = await db.execute(stmt)
        user_metrics = result.scalars().all()

        total_usage = sum(m.execution_count for m in user_metrics)
        recent_usage = sum(m.execution_count for m in user_metrics if m.last_execution_time and
                          (datetime.now() - m.last_execution_time).days < 1)

        # Calculate success rate
        total_success = sum(m.success_count for m in user_metrics)
        success_rate = (total_success / total_usage * 100) if total_usage > 0 else 100

        # Calculate average response time
        avg_response_time = sum(m.avg_response_time for m in user_metrics) / len(user_metrics) if user_metrics else 1200

        return {
            "success": True,
            "data": {
                "total_agents": total_agents,
                "active_agents": active_agents,
                "inactive_agents": inactive_agents,
                "error_agents": error_agents,
                "total_usage": total_usage,
                "recent_usage": recent_usage,
                "success_rate": round(success_rate, 1),
                "avg_response_time": round(avg_response_time, 0),
                "last_updated": datetime.now().isoformat()
            }
        }

    except Exception as e:
        logger.error(f"Error getting system metrics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get system metrics: {str(e)}"
        )
