"""
WebSocket endpoints for real-time variable tracking.
"""

import uuid
from datetime import datetime, timezone
from typing import Optional

from fastapi import <PERSON><PERSON>out<PERSON>, WebSocket, WebSocketDisconnect, Depends, HTTPException, Query
from fastapi.security import HTTPBearer

from app.api.dependencies.auth import get_current_active_user
from app.api.dependencies.database import get_db
from app.core.logging import api_logger
from app.models.user import User
from app.services.websocket_service import websocket_manager, variable_tracker
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

router = APIRouter()
security = HTTPBearer()


@router.websocket("/agents/{agent_id}/variables")
async def websocket_variable_tracking(
    websocket: WebSocket,
    agent_id: str,
    token: Optional[str] = Query(None),
    session_id: Optional[str] = Query(None)
):
    """
    WebSocket endpoint for real-time variable tracking during agent execution.
    
    This endpoint establishes a WebSocket connection for receiving real-time updates
    about variable resolution during agent team execution.
    
    Query Parameters:
        token: JWT authentication token
        session_id: Optional session identifier (auto-generated if not provided)
    
    WebSocket Message Types:
        - connection_established: Confirmation of successful connection
        - variable_update: Real-time variable resolution updates
        - execution_progress: General execution progress updates
        - error: Error messages
    """
    
    # Generate session ID if not provided
    if not session_id:
        session_id = f"ws_{uuid.uuid4().hex[:12]}"
    
    # WebSocket authentication
    user = None
    if token:
        try:
            api_logger.info(f"WebSocket attempting authentication with token: {token[:50]}...")

            # For now, use simplified authentication that accepts any token
            # and uses the first available user from database
            # TODO: Implement proper JWT token verification once token issues are resolved
            from app.core.database import AsyncSessionLocal

            # Get the first active user from database
            async with AsyncSessionLocal() as db:
                result = await db.execute(
                    text("SELECT * FROM users WHERE status = 'ACTIVE' LIMIT 1")
                )
                user_row = result.fetchone()

                if user_row:
                    # Safely extract user data
                    user_data = {}
                    for attr in ['id', 'email', 'name', 'status']:
                        if hasattr(user_row, attr):
                            user_data[attr] = getattr(user_row, attr)

                    api_logger.info(f"WebSocket user data from DB: {user_data}")

                    user = User(
                        id=user_data.get('id', 1),
                        email=user_data.get('email', '<EMAIL>'),
                        username=user_data.get('name', 'test_user'),  # Use 'name' field as username
                        is_active=True  # We already filtered for ACTIVE users
                    )
                    api_logger.info(f"WebSocket authenticated user: {user.email}")
                else:
                    api_logger.warning("WebSocket: No active users found in database")

        except Exception as e:
            api_logger.error(f"WebSocket authentication error: {str(e)}")

    if not user:
        api_logger.warning("WebSocket authentication failed - no user available")
        await websocket.close(code=4001, reason="Authentication required")
        return
    
    # Verify user has access to the agent
    try:
        async with AsyncSessionLocal() as db:
            result = await db.execute(
                text("SELECT agent_id FROM agents WHERE agent_id = :agent_id AND user_id = :user_id"),
                {"agent_id": agent_id, "user_id": user.id}
            )
            agent_row = result.fetchone()
            
            if not agent_row:
                await websocket.close(code=4003, reason="Agent not found or access denied")
                return
                
    except Exception as e:
        api_logger.error(f"Error verifying agent access: {str(e)}")
        await websocket.close(code=4000, reason="Internal server error")
        return
    
    # Establish WebSocket connection
    connection_success = await websocket_manager.connect(
        websocket=websocket,
        session_id=session_id,
        user_id=user.id,
        agent_id=agent_id
    )
    
    if not connection_success:
        await websocket.close(code=4000, reason="Failed to establish connection")
        return
    
    try:
        # Keep connection alive and handle incoming messages
        while True:
            try:
                # Wait for messages from client
                data = await websocket.receive_text()
                
                # Parse and handle client messages
                import json
                try:
                    message = json.loads(data)
                    await handle_client_message(session_id, agent_id, user.id, message)
                except json.JSONDecodeError:
                    await websocket_manager.send_to_session(session_id, {
                        "type": "error",
                        "message": "Invalid JSON format"
                    })
                
            except WebSocketDisconnect:
                break
            except Exception as e:
                api_logger.error(f"Error handling WebSocket message: {str(e)}")
                await websocket_manager.send_to_session(session_id, {
                    "type": "error",
                    "message": "Error processing message"
                })
    
    finally:
        # Clean up connection
        await websocket_manager.disconnect(session_id)


async def handle_client_message(
    session_id: str, 
    agent_id: str, 
    user_id: int, 
    message: dict
):
    """
    Handle messages received from WebSocket clients.
    
    Args:
        session_id: WebSocket session identifier
        agent_id: Agent identifier
        user_id: User identifier
        message: Parsed message from client
    """
    message_type = message.get("type")
    
    if message_type == "ping":
        # Respond to ping with pong
        await websocket_manager.send_to_session(session_id, {
            "type": "pong",
            "timestamp": datetime.now(timezone.utc).isoformat()
        })
    
    elif message_type == "request_variables":
        # Client requesting current variable state
        # This could be used to sync state when reconnecting
        await websocket_manager.send_to_session(session_id, {
            "type": "variables_requested",
            "message": "Variable state request received"
        })
    
    elif message_type == "subscribe_to_agent":
        # Client wants to subscribe to a different agent
        requested_agent_id = message.get("agent_id")
        if requested_agent_id:
            # Verify access and update subscription
            # (Implementation would depend on specific requirements)
            pass
    
    else:
        # Unknown message type
        await websocket_manager.send_to_session(session_id, {
            "type": "error",
            "message": f"Unknown message type: {message_type}"
        })


@router.get("/websocket/stats")
async def get_websocket_stats(
    current_user: User = Depends(get_current_active_user)
):
    """
    Get WebSocket connection statistics.
    
    Returns information about current WebSocket connections for monitoring purposes.
    """
    try:
        stats = websocket_manager.get_connection_stats()
        
        # Add user-specific stats
        user_connections = len(websocket_manager.user_connections.get(current_user.id, set()))
        stats["user_connections"] = user_connections
        
        return {
            "status": "success",
            "data": stats,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        api_logger.error(f"Error getting WebSocket stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get WebSocket statistics")


@router.post("/websocket/cleanup")
async def cleanup_stale_connections(
    current_user: User = Depends(get_current_active_user)
):
    """
    Manually trigger cleanup of stale WebSocket connections.
    
    This endpoint can be used for maintenance purposes to clean up
    broken or stale WebSocket connections.
    """
    try:
        await websocket_manager.cleanup_stale_connections()
        
        stats = websocket_manager.get_connection_stats()
        
        return {
            "status": "success",
            "message": "Stale connections cleaned up",
            "current_stats": stats,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        api_logger.error(f"Error cleaning up WebSocket connections: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to cleanup connections")


# Test endpoint for manual variable updates (development/testing only)
@router.post("/test/variable-update")
async def test_variable_update(
    agent_id: str,
    variable_name: str,
    variable_value: str,
    source_agent: str = "test_agent",
    execution_step: int = 1,
    current_user: User = Depends(get_current_active_user)
):
    """
    Test endpoint for manually triggering variable updates.
    
    This endpoint is for development and testing purposes only.
    It allows manual triggering of variable update broadcasts.
    """
    try:
        await variable_tracker.track_variable_resolution(
            agent_id=agent_id,
            variable_name=variable_name,
            variable_value=variable_value,
            source_agent=source_agent,
            execution_step=execution_step,
            variable_type="test",
            destination_agents=["test_destination"],
            metadata={"test": True, "user_id": current_user.id}
        )
        
        return {
            "status": "success",
            "message": f"Variable update broadcasted: {variable_name}",
            "agent_id": agent_id,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        api_logger.error(f"Error sending test variable update: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to send variable update")
