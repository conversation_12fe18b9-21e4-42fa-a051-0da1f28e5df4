"""
Health check endpoints.
"""

import time
from typing import Any, Dict

from fastapi import APIRouter

from app.core.config import settings
from app.core.logging import api_logger

router = APIRouter()


@router.get("/")
async def health_check() -> Dict[str, Any]:
    """Basic health check."""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "app_name": settings.APP_NAME,
        "version": settings.APP_VERSION,
    }


@router.get("/test-auth")
async def test_auth() -> Dict[str, Any]:
    """Test endpoint for authentication issues."""
    return {
        "status": "success",
        "message": "Test auth endpoint working",
        "timestamp": time.time(),
    }


@router.get("/logs-stats-test")
async def logs_stats_test() -> Dict[str, Any]:
    """Test logs statistics without authentication."""
    try:
        from app.core.database import AsyncSessionLocal
        from sqlalchemy import text

        async with AsyncSessionLocal() as db:
            # Test basic query
            result = await db.execute(text("SELECT COUNT(*) FROM application_logs WHERE user_id = 1"))
            count = result.scalar()

            # Test level stats
            level_result = await db.execute(text("""
                SELECT level, COUNT(*) as count
                FROM application_logs
                WHERE user_id = 1
                GROUP BY level
            """))
            level_stats = level_result.fetchall()

            return {
                "status": "success",
                "user_id": 1,
                "total_logs": count,
                "level_distribution": {row.level: row.count for row in level_stats},
                "timestamp": time.time()
            }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "timestamp": time.time()
        }


@router.get("/detailed")
async def detailed_health_check() -> Dict[str, Any]:
    """Detailed health check with system information."""
    import psutil
    import sys
    
    # System information
    system_info = {
        "python_version": sys.version,
        "platform": sys.platform,
        "cpu_count": psutil.cpu_count(),
        "memory_total": psutil.virtual_memory().total,
        "memory_available": psutil.virtual_memory().available,
        "disk_usage": psutil.disk_usage('/').percent,
    }
    
    # Application information
    app_info = {
        "name": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "environment": settings.ENVIRONMENT,
        "debug": settings.DEBUG,
    }
    
    # Service status
    services = {
        "database": await check_database_health(),
        "redis": await check_redis_health(),
        "ai_providers": await check_ai_providers_health(),
    }
    
    # Overall status
    overall_status = "healthy" if all(
        service["status"] == "healthy" for service in services.values()
    ) else "degraded"
    
    return {
        "status": overall_status,
        "timestamp": time.time(),
        "app": app_info,
        "system": system_info,
        "services": services,
    }


async def check_database_health() -> Dict[str, Any]:
    """Check database health."""
    import time
    from app.api.dependencies.database import get_db

    try:
        start_time = time.time()

        # Get database session and test connection
        async for db in get_db():
            # Simple query to test database connectivity
            result = await db.execute("SELECT 1")
            result.fetchone()
            break

        response_time = (time.time() - start_time) * 1000

        return {
            "status": "healthy",
            "message": "Database connection successful",
            "response_time_ms": round(response_time, 2),
        }
    except Exception as e:
        api_logger.error("Database health check failed", error=str(e))
        return {
            "status": "unhealthy",
            "message": f"Database connection failed: {str(e)}",
            "response_time_ms": None,
        }


async def check_redis_health() -> Dict[str, Any]:
    """Check Redis health."""
    try:
        # Redis is optional in this system, so we'll mark it as not configured
        # if Redis URL is not set in settings
        if not hasattr(settings, 'REDIS_URL') or not settings.REDIS_URL:
            return {
                "status": "not_configured",
                "message": "Redis not configured",
                "response_time_ms": None,
            }

        # If Redis is configured, we would implement actual health check here
        # For now, return a placeholder since Redis is not currently used
        return {
            "status": "not_implemented",
            "message": "Redis health check not implemented",
            "response_time_ms": None,
        }
    except Exception as e:
        api_logger.error("Redis health check failed", error=str(e))
        return {
            "status": "unhealthy",
            "message": f"Redis connection failed: {str(e)}",
            "response_time_ms": None,
        }


async def check_ai_providers_health() -> Dict[str, Any]:
    """Check AI providers health."""
    providers = {}
    
    # OpenAI
    if settings.OPENAI_API_KEY:
        try:
            # Basic validation - check if API key format is valid
            if settings.OPENAI_API_KEY.startswith('sk-'):
                providers["openai"] = {
                    "status": "configured",
                    "message": "OpenAI API key configured (format valid)",
                }
            else:
                providers["openai"] = {
                    "status": "misconfigured",
                    "message": "OpenAI API key format invalid",
                }
        except Exception as e:
            providers["openai"] = {
                "status": "unhealthy",
                "message": f"OpenAI API error: {str(e)}",
            }
    else:
        providers["openai"] = {
            "status": "not_configured",
            "message": "OpenAI API key not configured",
        }
    
    # Anthropic
    if settings.ANTHROPIC_API_KEY:
        try:
            # Basic validation - check if API key format is valid
            if settings.ANTHROPIC_API_KEY.startswith('sk-ant-'):
                providers["anthropic"] = {
                    "status": "configured",
                    "message": "Anthropic API key configured (format valid)",
                }
            else:
                providers["anthropic"] = {
                    "status": "misconfigured",
                    "message": "Anthropic API key format invalid",
                }
        except Exception as e:
            providers["anthropic"] = {
                "status": "unhealthy",
                "message": f"Anthropic API error: {str(e)}",
            }
    else:
        providers["anthropic"] = {
            "status": "not_configured",
            "message": "Anthropic API key not configured",
        }
    
    # Overall AI providers status
    healthy_count = sum(1 for p in providers.values() if p["status"] == "healthy")
    total_configured = sum(1 for p in providers.values() if p["status"] != "not_configured")
    
    if total_configured == 0:
        overall_status = "not_configured"
        message = "No AI providers configured"
    elif healthy_count == total_configured:
        overall_status = "healthy"
        message = f"All {total_configured} configured providers healthy"
    elif healthy_count > 0:
        overall_status = "degraded"
        message = f"{healthy_count}/{total_configured} providers healthy"
    else:
        overall_status = "unhealthy"
        message = "All configured providers unhealthy"
    
    return {
        "status": overall_status,
        "message": message,
        "providers": providers,
    }
