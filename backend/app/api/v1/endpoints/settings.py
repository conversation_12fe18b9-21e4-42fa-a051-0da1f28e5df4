"""
System settings management endpoints.
"""

from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.api.dependencies.auth import get_current_user, get_current_admin_user
from app.api.dependencies.database import get_db
from app.models.user import User, UserRole
from app.models.settings import (
    SystemSettings, SystemSettingsCreate, SystemSettingsUpdate, SystemSettingsResponse
)
from app.core.logging import api_logger

router = APIRouter()


@router.get("", response_model=SystemSettingsResponse)
async def get_system_settings(
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db)
):
    """Get current system settings. Requires admin role."""
    try:
        # Get active system settings
        result = await db.execute(
            select(SystemSettings).where(SystemSettings.is_active == True)
        )
        settings = result.scalar_one_or_none()

        if not settings:
            # Create default settings if none exist
            default_settings = SystemSettings(
                app_name="Meta-Agent",
                app_description="AI Agent自动生成服务",
                default_language="zh-CN",
                timezone="Asia/Shanghai",
                theme="system",
                max_concurrent_agents=10,
                default_model="gpt-4",
                default_temperature=0.7,
                max_response_length=10000,
                timeout_seconds=30,
                enable_ai_team_generation=True,
                team_generation_provider="openai",
                team_generation_custom_provider_name=None,  # Will be set for custom providers
                team_generation_model="gpt-4",
                team_generation_temperature=0.7,
                team_generation_max_tokens=4000,
                team_generation_base_url=None,  # Will be set for custom providers
                team_generation_api_key=None,  # Will be set by admin
                rate_limit_per_minute=100,
                enable_cors=True,
                cors_origins="http://localhost:3000,http://127.0.0.1:3000",
                enable_docs=True,
                enable_debug=True,
                log_level="INFO",
                max_log_files=10,
                log_retention_days=30,
                enable_file_logging=True,
                enable_auth=False,
                session_timeout_minutes=60,
                max_login_attempts=5,
                enable_2fa=False,
                is_active=True
            )

            db.add(default_settings)
            await db.commit()
            await db.refresh(default_settings)
            settings = default_settings

        # Decrypt API key for admin users
        from app.models.user import UserRole
        from app.models.settings import decrypt_api_key

        if current_user.role == UserRole.ADMIN and settings.team_generation_api_key:
            try:
                # Create a copy of settings to avoid modifying the original
                settings_dict = settings.model_dump()

                # Decrypt API key (handle double encryption if present)
                decrypted_key = decrypt_api_key(settings.team_generation_api_key)

                # Check if the result is still encrypted (double encryption case)
                if decrypted_key and decrypted_key.startswith('Z0FBQUFB'):  # Base64 encrypted pattern
                    try:
                        decrypted_key = decrypt_api_key(decrypted_key)
                    except Exception:
                        # If second decryption fails, use first decryption result
                        pass

                settings_dict['team_generation_api_key'] = decrypted_key

                # Return the modified settings with decrypted API key
                return SystemSettingsResponse(**settings_dict)
            except Exception as decrypt_error:
                # If decryption fails, log the error but continue with encrypted value
                # This ensures the API doesn't break if there are encryption issues
                api_logger.warning("Failed to decrypt team generation API key", error=str(decrypt_error))

        return settings

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get system settings: {str(e)}"
        )


@router.put("", response_model=SystemSettingsResponse)
async def update_system_settings(
    settings_update: SystemSettingsUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Update system settings. Requires admin role."""
    try:
        # Check if user has admin role
        if current_user.role != UserRole.ADMIN:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only administrators can update system settings"
            )
        
        # Get current active settings
        result = await db.execute(
            select(SystemSettings).where(SystemSettings.is_active == True)
        )
        current_settings = result.scalar_one_or_none()
        
        if not current_settings:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="System settings not found"
            )
        
        # Update settings with provided values
        update_data = settings_update.model_dump(exclude_unset=True)

        # Handle API key encryption if provided
        if 'team_generation_api_key' in update_data and update_data['team_generation_api_key']:
            from app.models.settings import encrypt_api_key
            update_data['team_generation_api_key'] = encrypt_api_key(update_data['team_generation_api_key'])

        for field, value in update_data.items():
            setattr(current_settings, field, value)
        
        current_settings.updated_at = datetime.now()
        
        await db.commit()
        await db.refresh(current_settings)
        
        return current_settings
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update system settings: {str(e)}"
        )


@router.post("/reset", response_model=SystemSettingsResponse)
async def reset_system_settings(
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db)
):
    """Reset system settings to defaults. Requires admin role."""
    try:
        # Get current active settings
        result = await db.execute(
            select(SystemSettings).where(SystemSettings.is_active == True)
        )
        current_settings = result.scalar_one_or_none()
        
        if current_settings:
            # Deactivate current settings
            current_settings.is_active = False
            current_settings.updated_at = datetime.now()
        
        # Create new default settings
        default_settings = SystemSettings(
            app_name="Meta-Agent",
            app_description="AI Agent自动生成服务",
            default_language="zh-CN",
            timezone="Asia/Shanghai",
            theme="system",
            max_concurrent_agents=10,
            default_model="gpt-4",
            default_temperature=0.7,
            max_response_length=10000,
            timeout_seconds=30,
            enable_ai_team_generation=True,
            team_generation_provider="openai",
            team_generation_custom_provider_name=None,  # Will be set for custom providers
            team_generation_model="gpt-4",
            team_generation_temperature=0.7,
            team_generation_max_tokens=4000,
            team_generation_base_url=None,  # Will be set for custom providers
            team_generation_api_key=None,  # Will be set by admin
            rate_limit_per_minute=100,
            enable_cors=True,
            cors_origins="http://localhost:3000,http://127.0.0.1:3000",
            enable_docs=True,
            enable_debug=True,
            log_level="INFO",
            max_log_files=10,
            log_retention_days=30,
            enable_file_logging=True,
            enable_auth=False,
            session_timeout_minutes=60,
            max_login_attempts=5,
            enable_2fa=False,
            is_active=True
        )
        
        db.add(default_settings)
        await db.commit()
        await db.refresh(default_settings)
        
        return default_settings
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to reset system settings: {str(e)}"
        )


@router.get("/export")
async def export_system_settings(
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db)
):
    """Export system settings as JSON. Requires admin role."""
    try:
        
        # Get active system settings
        result = await db.execute(
            select(SystemSettings).where(SystemSettings.is_active == True)
        )
        settings = result.scalar_one_or_none()
        
        if not settings:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="System settings not found"
            )
        
        # Convert to dict and remove internal fields
        settings_dict = settings.model_dump()
        settings_dict.pop('id', None)
        settings_dict.pop('uuid', None)
        settings_dict.pop('created_at', None)
        settings_dict.pop('updated_at', None)
        settings_dict.pop('is_active', None)

        # Decrypt API key for admin export
        from app.models.settings import decrypt_api_key
        if settings.team_generation_api_key:
            try:
                decrypted_key = decrypt_api_key(settings.team_generation_api_key)

                # Check if the result is still encrypted (double encryption case)
                if decrypted_key and decrypted_key.startswith('Z0FBQUFB'):  # Base64 encrypted pattern
                    try:
                        decrypted_key = decrypt_api_key(decrypted_key)
                    except Exception:
                        # If second decryption fails, use first decryption result
                        pass

                settings_dict['team_generation_api_key'] = decrypted_key
            except Exception as decrypt_error:
                # If decryption fails, log the error but continue with encrypted value
                import logging
                logging.warning(f"Failed to decrypt team generation API key for export: {decrypt_error}")
        
        return {
            "success": True,
            "data": settings_dict,
            "exported_at": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to export system settings: {str(e)}"
        )


@router.post("/import", response_model=SystemSettingsResponse)
async def import_system_settings(
    settings_data: SystemSettingsCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Import system settings from JSON. Requires admin role."""
    try:
        # Check if user has admin role
        if current_user.role != UserRole.ADMIN:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only administrators can import system settings"
            )
        
        # Get current active settings
        result = await db.execute(
            select(SystemSettings).where(SystemSettings.is_active == True)
        )
        current_settings = result.scalar_one_or_none()
        
        if current_settings:
            # Deactivate current settings
            current_settings.is_active = False
            current_settings.updated_at = datetime.now()
        
        # Create new settings from imported data
        new_settings = SystemSettings(**settings_data.model_dump(), is_active=True)
        
        db.add(new_settings)
        await db.commit()
        await db.refresh(new_settings)
        
        return new_settings
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to import system settings: {str(e)}"
        )
