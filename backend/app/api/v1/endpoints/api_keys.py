"""
API key management endpoints.
"""

from datetime import datetime
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.api.dependencies.auth import get_current_user
from app.api.dependencies.database import get_db
from app.models.user import User
from app.models.settings import (
    APIKey, APIKeyCreate, APIKeyUpdate, APIKeyResponse,
    APIKeyStatus, encrypt_api_key, decrypt_api_key, generate_key_prefix
)
from app.services.usage_tracking import UsageTracker

router = APIRouter()





@router.get("", response_model=List[APIKeyResponse])
async def list_api_keys(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """List user's API keys."""
    try:
        # Get user's API keys from database
        result = await db.execute(
            select(APIKey).where(APIKey.user_id == current_user.id)
        )
        api_keys = result.scalars().all()

        return api_keys

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list API keys: {str(e)}"
        )


@router.get("/{key_id}/reveal", response_model=dict)
async def reveal_api_key(
    key_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Reveal the actual API key for copying (security-sensitive operation)."""
    try:
        # Find the API key
        result = await db.execute(
            select(APIKey).where(
                APIKey.id == key_id,
                APIKey.user_id == current_user.id
            )
        )
        api_key = result.scalar_one_or_none()

        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API key not found"
            )

        # Check if encrypted_key exists
        if not api_key.encrypted_key:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="API key was created before encryption support. Please recreate the key."
            )

        # Decrypt and return the actual key
        decrypted_key = decrypt_api_key(api_key.encrypted_key)

        if not decrypted_key:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to decrypt API key"
            )

        return {
            "success": True,
            "data": {
                "id": key_id,
                "name": api_key.name,
                "key": decrypted_key,
                "revealed_at": datetime.now().isoformat()
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to reveal API key: {str(e)}"
        )


@router.post("", response_model=dict)
async def create_api_key(
    key_data: APIKeyCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new API key."""
    try:
        # Encrypt the user-provided API key
        encrypted_key = encrypt_api_key(key_data.key)
        key_prefix = generate_key_prefix(key_data.key)

        # Create API key record
        new_api_key = APIKey(
            user_id=current_user.id,
            name=key_data.name,
            description=key_data.description,
            key_prefix=key_prefix,
            encrypted_key=encrypted_key,
            status=APIKeyStatus.ACTIVE,
            expires_at=key_data.expires_at,
            rate_limit_per_minute=key_data.rate_limit_per_minute,
            rate_limit_per_day=key_data.rate_limit_per_day
        )

        db.add(new_api_key)
        await db.commit()
        await db.refresh(new_api_key)

        return {
            "success": True,
            "message": "API key created successfully",
            "data": {
                "id": new_api_key.id,
                "uuid": new_api_key.uuid,
                "name": new_api_key.name,
                "key": key_data.key,  # Return the actual key only once during creation
                "key_prefix": key_prefix,
                "created_at": new_api_key.created_at.isoformat()
            }
        }
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create API key: {str(e)}"
        )


@router.put("/{key_id}", response_model=APIKeyResponse)
async def update_api_key(
    key_id: int,
    key_update: APIKeyUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Update an API key."""
    try:
        # Find the API key
        result = await db.execute(
            select(APIKey).where(
                APIKey.id == key_id,
                APIKey.user_id == current_user.id
            )
        )
        api_key = result.scalar_one_or_none()

        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API key not found"
            )

        # Update the key data with validation
        update_data = key_update.model_dump(exclude_unset=True)

        # Special handling for status updates
        if 'status' in update_data:
            new_status = update_data['status']
            current_status = api_key.status

            # Validate status transitions
            if current_status in [APIKeyStatus.ERROR, APIKeyStatus.EXPIRED, APIKeyStatus.REVOKED]:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Cannot change status from {current_status.value}. Please create a new API key."
                )

            # Only allow transitions between ACTIVE and INACTIVE
            if new_status not in [APIKeyStatus.ACTIVE, APIKeyStatus.INACTIVE]:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid status transition to {new_status.value}"
                )

        # Apply updates
        for field, value in update_data.items():
            setattr(api_key, field, value)

        api_key.updated_at = datetime.now()

        await db.commit()
        await db.refresh(api_key)

        return api_key

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update API key: {str(e)}"
        )


@router.delete("/{key_id}")
async def delete_api_key(
    key_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Delete an API key."""
    try:
        # Find the API key
        result = await db.execute(
            select(APIKey).where(
                APIKey.id == key_id,
                APIKey.user_id == current_user.id
            )
        )
        api_key = result.scalar_one_or_none()

        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API key not found"
            )

        # Delete the key
        await db.delete(api_key)
        await db.commit()

        return {
            "success": True,
            "message": "API key deleted successfully",
            "data": {
                "id": key_id,
                "deleted_at": datetime.now().isoformat()
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete API key: {str(e)}"
        )


@router.post("/{key_id}/test")
async def test_api_key(
    key_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Test an API key."""
    try:
        # Find the API key
        result = await db.execute(
            select(APIKey).where(
                APIKey.id == key_id,
                APIKey.user_id == current_user.id
            )
        )
        api_key = result.scalar_one_or_none()

        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API key not found"
            )

        # Check if key is active
        if api_key.status != APIKeyStatus.ACTIVE:
            return {
                "success": False,
                "message": f"API key is {api_key.status.value}",
                "data": {
                    "key_id": key_id,
                    "status": api_key.status.value,
                    "tested_at": datetime.now().isoformat()
                }
            }

        # Check if key is expired
        if api_key.expires_at and datetime.now() > api_key.expires_at:
            # Update status to expired
            api_key.status = APIKeyStatus.EXPIRED
            await db.commit()

            return {
                "success": False,
                "message": "API key has expired",
                "data": {
                    "key_id": key_id,
                    "status": "expired",
                    "expires_at": api_key.expires_at.isoformat(),
                    "tested_at": datetime.now().isoformat()
                }
            }

        # Simplified API key validation
        try:
            # Check if encrypted_key exists
            if not api_key.encrypted_key:
                return {
                    "success": False,
                    "message": "API key was created before encryption support. Please recreate the key.",
                    "data": {
                        "key_id": key_id,
                        "status": "error",
                        "tested_at": datetime.now().isoformat()
                    }
                }

            # Decrypt the actual API key for basic validation
            decrypted_key = decrypt_api_key(api_key.encrypted_key)

            if not decrypted_key:
                return {
                    "success": False,
                    "message": "Failed to decrypt API key for testing",
                    "data": {
                        "key_id": key_id,
                        "status": "error",
                        "tested_at": datetime.now().isoformat()
                    }
                }

            # Basic validation - check if key is not empty and has reasonable format
            if len(decrypted_key.strip()) < 10:
                return {
                    "success": False,
                    "message": "API key appears to be invalid (too short)",
                    "data": {
                        "key_id": key_id,
                        "status": "invalid",
                        "tested_at": datetime.now().isoformat()
                    }
                }

            # Since we simplified API key management, we'll just do basic validation
            # Update usage statistics
            api_key.last_used = datetime.now()
            api_key.usage_count += 1
            await db.commit()

            return {
                "success": True,
                "message": "API key format validation passed",
                "data": {
                    "key_id": key_id,
                    "status": "valid",
                    "tested_at": datetime.now().isoformat(),
                    "note": "Basic format validation only - actual API functionality depends on the service you're using this key with"
                }
            }

        except Exception as test_error:
            return {
                "success": False,
                "message": f"API key test failed: {str(test_error)}",
                "data": {
                    "key_id": key_id,
                    "status": "error",
                    "error": str(test_error),
                    "tested_at": datetime.now().isoformat()
                }
            }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to test API key: {str(e)}"
        )


@router.get("/{key_id}/usage", response_model=dict)
async def get_api_key_usage(
    key_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get usage statistics for an API key."""
    try:
        # Verify the API key belongs to the user
        result = await db.execute(
            select(APIKey).where(
                APIKey.id == key_id,
                APIKey.user_id == current_user.id
            )
        )
        api_key = result.scalar_one_or_none()

        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API key not found"
            )

        # Get usage statistics
        tracker = UsageTracker(db)
        usage_stats = await tracker.get_usage_stats(key_id)

        if usage_stats is None:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve usage statistics"
            )

        return {
            "success": True,
            "data": {
                "key_id": key_id,
                "key_name": api_key.name,
                "provider": api_key.provider.value,
                "status": api_key.status.value,
                "usage": usage_stats
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get usage statistics: {str(e)}"
        )


@router.get("/usage/summary", response_model=dict)
async def get_usage_summary(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get usage summary for all user's API keys."""
    try:
        tracker = UsageTracker(db)
        summary = await tracker.get_user_usage_summary(current_user.id)

        return {
            "success": True,
            "data": summary
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get usage summary: {str(e)}"
        )
