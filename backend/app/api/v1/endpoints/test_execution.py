"""
Test execution API endpoints.

This module handles test execution lifecycle management,
including test initialization and variable tracking integration.
"""

import uuid
from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException
from fastapi import status as http_status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.dependencies.database import get_db
from app.api.dependencies.auth import get_current_active_user
from app.core.logging import api_logger
from app.services.logging_service import log_test_execution
from app.core.timezone_utils import utc_now, normalize_datetime_for_db
from app.models.user import User
from app.models.test_history import TestHistory, TestStatus
from pydantic import BaseModel


class TestExecutionStartRequest(BaseModel):
    """Request model for starting test execution."""
    agent_id: str
    input_text: str
    ai_config_override: Optional[Dict[str, Any]] = None
    api_key_id: Optional[int] = None
    api_key_name: Optional[str] = None
    input_metadata: Optional[Dict[str, Any]] = None


class TestExecutionStartResponse(BaseModel):
    """Response model for test execution start."""
    test_id: str
    status: str
    message: str
    created_at: str


router = APIRouter()


@router.post("/start", response_model=TestExecutionStartResponse)
async def start_test_execution(
    request: TestExecutionStartRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Start a new test execution and create test history record.
    
    This endpoint:
    1. Generates a unique test_id using UUID
    2. Creates a test_history record in the database
    3. Returns the test_id for frontend use
    
    Args:
        request: Test execution start request data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        TestExecutionStartResponse with test_id and status
    """
    try:
        # Generate unique test ID using UUID for better uniqueness
        test_id = f"test_{uuid.uuid4().hex[:16]}"
        
        # Create test history record
        test_history = TestHistory(
            test_id=test_id,
            user_id=current_user.id,
            agent_id=request.agent_id,
            input_text=request.input_text,
            ai_config_override=request.ai_config_override,
            api_key_id=request.api_key_id,
            api_key_name=request.api_key_name,
            input_metadata=request.input_metadata,
            status=TestStatus.RUNNING,
            started_at=normalize_datetime_for_db(utc_now())
        )
        
        db.add(test_history)
        await db.commit()
        await db.refresh(test_history)
        
        api_logger.info(
            f"Test execution started: {test_id} for user {current_user.id}, agent {request.agent_id}"
        )

        # Log test start event
        await log_test_execution(
            event_type="test_start",
            test_id=test_id,
            agent_id=request.agent_id,
            user_id=current_user.id,
            message=f"Test execution started for agent {request.agent_id}",
            metadata={
                "input_length": len(request.input_text),
                "api_key_id": request.api_key_id,
                "api_key_name": request.api_key_name,
                "has_ai_config_override": bool(request.ai_config_override)
            }
        )

        return TestExecutionStartResponse(
            test_id=test_id,
            status="started",
            message="Test execution initialized successfully",
            created_at=test_history.started_at.isoformat()
        )
        
    except Exception as e:
        api_logger.error(f"Failed to start test execution: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start test execution"
        )


@router.post("/{test_id}/variables", status_code=http_status.HTTP_200_OK)
async def update_test_variables(
    test_id: str,
    variables_data: Dict[str, Any],
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update variable tracking data for a test execution.
    
    This endpoint is called by the VariableTracker service to store
    variable resolution data in the test_history record.
    
    Args:
        test_id: Test execution identifier
        variables_data: Variable tracking data to store
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Success message
    """
    try:
        # Find the test history record
        from sqlalchemy import text
        result = await db.execute(
            text("SELECT id FROM test_history WHERE test_id = :test_id AND user_id = :user_id"),
            {"test_id": test_id, "user_id": current_user.id}
        )
        test_record = result.fetchone()
        
        if not test_record:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Test execution not found"
            )
        
        # Update variable tracking fields
        update_data = {
            "updated_at": normalize_datetime_for_db(utc_now())
        }
        
        # Handle context placeholders
        if "context_placeholders_used" in variables_data:
            update_data["context_placeholders_used"] = variables_data["context_placeholders_used"]
        
        # Handle team member interactions
        if "team_member_interactions" in variables_data:
            update_data["team_member_interactions"] = variables_data["team_member_interactions"]
        
        # Handle context summary
        if "context_summary" in variables_data:
            update_data["context_summary"] = variables_data["context_summary"]
        
        # Build update query
        set_clauses = []
        params = {"test_id": test_id, "user_id": current_user.id}
        
        for key, value in update_data.items():
            set_clauses.append(f"{key} = :{key}")
            # Serialize JSON fields for database storage
            if key in ["context_placeholders_used", "team_member_interactions", "context_summary"] and value is not None:
                if isinstance(value, (dict, list)):
                    import json
                    params[key] = json.dumps(value)
                else:
                    params[key] = value
            else:
                params[key] = value
        
        if set_clauses:
            from sqlalchemy import text
            query = f"UPDATE test_history SET {', '.join(set_clauses)} WHERE test_id = :test_id AND user_id = :user_id"
            await db.execute(text(query), params)
            await db.commit()
        
        api_logger.debug(f"Updated variable data for test {test_id}")
        
        return {"message": "Variable data updated successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to update test variables: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update variable data"
        )
