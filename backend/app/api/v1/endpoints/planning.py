"""
Minimal planning endpoints for testing.
"""

import uuid
from datetime import datetime, timezone
from typing import Any, Dict

from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from app.api.dependencies.database import get_db
from app.api.dependencies.auth import CurrentActiveUser
from app.core.logging import api_logger
from app.models.planning import (
    PlanningRequest,
    PlanningStatus,
)
from app.models.user import User
from app.services.ai_planner import get_ai_planner

router = APIRouter()


@router.get("/test")
async def test_endpoint():
    """Test endpoint."""
    return {"message": "Planning router is working"}





@router.get("/ai-settings", response_model=Dict[str, Any])
async def get_ai_generation_settings(
    current_user: User = CurrentActiveUser,
    db: AsyncSession = Depends(get_db),
) -> Dict[str, Any]:
    """Get AI team generation settings."""
    try:
        from sqlmodel import select
        from app.models.settings import SystemSettings

        # Get system settings
        statement = select(SystemSettings).where(SystemSettings.is_active == True)
        result = await db.execute(statement)
        settings = result.scalar_one_or_none()

        if not settings:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="System settings not found",
            )

        # Check if system has API key configured for team generation
        has_system_api_key = bool(settings.team_generation_api_key)

        return {
            "enabled": settings.enable_ai_team_generation,
            "provider": settings.team_generation_provider,
            "model": settings.team_generation_model,
            "temperature": settings.team_generation_temperature,
            "max_tokens": settings.team_generation_max_tokens,
            "has_api_key": has_system_api_key,
            "user_id": current_user.id,
        }

    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to get AI settings: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get AI settings: {str(e)}",
        )


@router.get("/analytics/user", response_model=Dict[str, Any])
async def get_user_analytics(
    days: int = 30,
    current_user: User = CurrentActiveUser,
    db: AsyncSession = Depends(get_db),
) -> Dict[str, Any]:
    """Get user AI generation analytics."""
    try:
        from app.services.ai_team_analytics import AITeamAnalyticsService

        analytics_service = AITeamAnalyticsService(db)
        statistics = await analytics_service.get_user_statistics(current_user, days)

        api_logger.info(
            "User analytics retrieved",
            user_id=current_user.id,
            days=days,
            total_generations=statistics["total_generations"]
        )

        return statistics

    except Exception as e:
        api_logger.error(f"Failed to get user analytics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get user analytics: {str(e)}",
        )


@router.get("/analytics/system", response_model=Dict[str, Any])
async def get_system_analytics(
    days: int = 30,
    current_user: User = CurrentActiveUser,
    db: AsyncSession = Depends(get_db),
) -> Dict[str, Any]:
    """Get system-wide AI generation analytics (admin only)."""
    try:
        # Check if user is admin
        from app.models.user import UserRole
        if current_user.role != UserRole.ADMIN:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Admin privileges required"
            )

        from app.services.ai_team_analytics import AITeamAnalyticsService

        analytics_service = AITeamAnalyticsService(db)
        statistics = await analytics_service.get_system_statistics(days)

        api_logger.info(
            "System analytics retrieved",
            admin_user_id=current_user.id,
            days=days,
            total_generations=statistics["total_generations"]
        )

        return statistics

    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to get system analytics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get system analytics: {str(e)}",
        )


@router.post("/feedback/{record_id}", response_model=Dict[str, Any])
async def submit_feedback(
    record_id: str,
    feedback_data: Dict[str, Any],
    current_user: User = CurrentActiveUser,
    db: AsyncSession = Depends(get_db),
) -> Dict[str, Any]:
    """Submit user feedback for a generation record."""
    try:
        rating = feedback_data.get("rating")
        feedback_text = feedback_data.get("feedback")

        if not rating or not isinstance(rating, int) or rating < 1 or rating > 5:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Rating must be an integer between 1 and 5"
            )

        from app.services.ai_team_analytics import AITeamAnalyticsService

        analytics_service = AITeamAnalyticsService(db)
        success = await analytics_service.update_user_feedback(
            record_id=record_id,
            user=current_user,
            rating=rating,
            feedback=feedback_text
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Generation record not found or access denied"
            )

        api_logger.info(
            "User feedback submitted",
            user_id=current_user.id,
            record_id=record_id,
            rating=rating
        )

        return {
            "success": True,
            "message": "Feedback submitted successfully",
            "record_id": record_id
        }

    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to submit feedback: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to submit feedback: {str(e)}",
        )


@router.post("/generate", response_model=Dict[str, Any])
async def generate_team(
    request: Dict[str, Any],
    background_tasks: BackgroundTasks,
    current_user: User = CurrentActiveUser,
    db: AsyncSession = Depends(get_db),
) -> Dict[str, Any]:
    """Generate AI-powered team plan asynchronously using system AI settings."""
    try:
        user_description = request.get("user_description")

        if not user_description:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="user_description is required",
            )

        # Get AI model configuration from system settings (not from user request)
        from sqlmodel import select
        from app.models.settings import SystemSettings

        # Get system AI settings for team generation
        result = await db.execute(select(SystemSettings).where(SystemSettings.is_active == True))
        settings = result.scalar_one_or_none()

        if not settings:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="System settings not found",
            )

        # Use system settings or defaults for team generation
        model = settings.team_generation_model
        temperature = settings.team_generation_temperature

        # Create planning request record with user context and AI config
        request_id = f"plan_{uuid.uuid4().hex[:12]}"

        planning_request = PlanningRequest(
            request_id=request_id,
            user_description=user_description,
            model=model,
            temperature=temperature,
            status=PlanningStatus.PENDING,
        )

        db.add(planning_request)
        await db.commit()
        await db.refresh(planning_request)

        # Start planning process in background with user context
        background_tasks.add_task(
            process_planning_request,
            planning_request.id,
            user_description,
            current_user.id,  # Pass user ID for proper context
        )

        api_logger.info(
            "AI team generation started",
            user_id=current_user.id,
            request_id=request_id,
            description=user_description[:100],
        )

        return {
            "success": True,
            "request_id": request_id,
            "status": "pending",
            "message": "Team generation started successfully",
            "estimated_time": "30-60 seconds",
            "user_id": current_user.id,
        }

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        api_logger.error(f"Failed to start team generation: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start team generation: {str(e)}",
        )


@router.post("/analyze", response_model=Dict[str, Any])
async def analyze_requirements(
    request: Dict[str, Any],
    background_tasks: BackgroundTasks,
    current_user: User = CurrentActiveUser,
    db: AsyncSession = Depends(get_db),
) -> Dict[str, Any]:
    """Analyze requirements and generate team plan (alias for /generate for frontend compatibility)."""
    # This is an alias to the generate endpoint for frontend compatibility
    return await generate_team(request, background_tasks, current_user, db)


@router.get("/status/{request_id}", response_model=Dict[str, Any])
async def get_planning_status(
    request_id: str,
    db: AsyncSession = Depends(get_db),
) -> Dict[str, Any]:
    """Get planning request status."""
    try:
        # Find planning request using raw SQL to avoid enum conversion issues
        result = await db.execute(
            text("SELECT * FROM planning_requests WHERE request_id = :request_id"),
            {"request_id": request_id}
        )
        row = result.fetchone()

        if not row:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Planning request '{request_id}' not found",
            )
        
        # Convert row to dict for easier access
        row_dict = dict(row._mapping) if hasattr(row, '_mapping') else dict(row)

        # Convert status to lowercase for frontend compatibility
        raw_status = row_dict.get("status", "unknown")
        status_lower = raw_status.lower() if raw_status else "unknown"

        response = {
            "request_id": request_id,
            "status": status_lower,
            "created_at": row_dict.get("created_at"),
        }

        # Convert datetime strings to ISO format if they're datetime objects
        if hasattr(response["created_at"], "isoformat"):
            response["created_at"] = response["created_at"].isoformat()

        if row_dict.get("started_at"):
            started_at = row_dict.get("started_at")
            response["started_at"] = started_at.isoformat() if hasattr(started_at, "isoformat") else started_at

        if row_dict.get("completed_at"):
            completed_at = row_dict.get("completed_at")
            response["completed_at"] = completed_at.isoformat() if hasattr(completed_at, "isoformat") else completed_at
            response["duration_seconds"] = row_dict.get("duration_seconds")

        if row_dict.get("error_message"):
            response["error"] = {
                "message": row_dict.get("error_message"),
            }

        if row_dict.get("team_plan_json"):
            import json
            try:
                response["team_plan"] = json.loads(row_dict.get("team_plan_json"))
            except json.JSONDecodeError:
                response["team_plan"] = row_dict.get("team_plan_json")
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to get planning status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get planning status: {str(e)}",
        )


@router.get("/result/{request_id}", response_model=Dict[str, Any])
async def get_planning_result(
    request_id: str,
    db: AsyncSession = Depends(get_db),
) -> Dict[str, Any]:
    """Get planning result."""
    try:
        # Find planning request
        result = await db.execute(
            "SELECT * FROM planning_requests WHERE request_id = ?", (request_id,)
        )
        planning_request = result.fetchone()
        
        if not planning_request:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Planning request '{request_id}' not found",
            )
        
        if planning_request.status != PlanningStatus.COMPLETED:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Planning request is not completed. Current status: {planning_request.status}",
            )
        
        if not planning_request.team_plan:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Planning completed but no team plan found",
            )
        
        return {
            "request_id": request_id,
            "status": "completed",
            "team_plan": planning_request.team_plan,
            "duration_seconds": planning_request.duration_seconds,
            "completed_at": planning_request.completed_at.isoformat(),
        }
        
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to get planning result: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get planning result: {str(e)}",
        )


@router.post("/validate", response_model=Dict[str, Any])
async def validate_team_plan(
    team_plan: Dict[str, Any],
) -> Dict[str, Any]:
    """Validate a team plan."""
    try:
        # Basic validation
        required_fields = ["team_name", "description", "objective", "team_members", "workflow"]
        missing_fields = [field for field in required_fields if field not in team_plan]

        if missing_fields:
            return {
                "valid": False,
                "score": 0.0,
                "errors": [f"Missing required field: {field}" for field in missing_fields],
                "warnings": [],
                "suggestions": [],
            }

        # Calculate validation score (simplified implementation)
        score = _calculate_validation_score(team_plan)
        
        errors = []
        warnings = []
        suggestions = []
        
        # Check team size
        team_size = len(team_plan.get("team_members", []))
        if team_size < 2:
            errors.append("Team must have at least 2 members")
        elif team_size > 6:
            warnings.append("Large teams (>6 members) may be difficult to coordinate")
        
        # Check workflow
        workflow_steps = len(team_plan.get("workflow", {}).get("steps", []))
        if workflow_steps < 3:
            warnings.append("Workflow should have at least 3 steps for completeness")
        
        # Check role diversity
        roles = [member.get("role", "") for member in team_plan.get("team_members", [])]
        if len(set(roles)) < len(roles):
            warnings.append("Some team members have duplicate roles")
        
        # Suggestions
        if score < 70:
            suggestions.append("Consider adding more detailed member descriptions")
            suggestions.append("Ensure all team members have clear responsibilities")
        
        return {
            "valid": len(errors) == 0,
            "score": score,
            "errors": errors,
            "warnings": warnings,
            "suggestions": suggestions,
        }
        
    except Exception as e:
        api_logger.error(f"Failed to validate team plan: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to validate team plan: {str(e)}",
        )






async def process_planning_request(
    planning_request_id: int,
    user_description: str,
    user_id: int,
) -> None:
    """Process planning request in background with user context."""
    from app.core.database import AsyncSessionLocal
    from sqlmodel import select

    async with AsyncSessionLocal() as db:
        try:
            # Update status to analyzing
            await db.execute(
                text("UPDATE planning_requests SET status = :status, started_at = :started_at WHERE id = :id"),
                {"status": PlanningStatus.ANALYZING.value, "started_at": datetime.now(timezone.utc), "id": planning_request_id},
            )
            await db.commit()

            # Get planning request to retrieve AI model configuration
            planning_result = await db.execute(select(PlanningRequest).where(PlanningRequest.id == planning_request_id))
            planning_request = planning_result.scalar_one_or_none()

            if not planning_request:
                raise ValueError(f"Planning request {planning_request_id} not found")

            # Get user for context
            user_result = await db.execute(select(User).where(User.id == user_id))
            user = user_result.scalar_one_or_none()

            if not user:
                raise ValueError(f"User {user_id} not found")

            # Get AI planner with user context
            ai_planner = get_ai_planner(db=db, user=user)

            # Create team plan using AI generation with user-specified AI config
            start_time = datetime.now()
            team_plan = await ai_planner.generate_ai_powered_team(
                user_description,
                model_override=planning_request.model,
                temperature_override=planning_request.temperature
            )
            end_time = datetime.now()

            duration = (end_time - start_time).total_seconds()

            # Update planning request with result
            import json
            await db.execute(
                text("""UPDATE planning_requests
                   SET status = :status, team_plan_json = :team_plan_json, completed_at = :completed_at, duration_seconds = :duration_seconds
                   WHERE id = :id"""),
                {
                    "status": PlanningStatus.COMPLETED.value,
                    "team_plan_json": json.dumps(team_plan),
                    "completed_at": end_time,
                    "duration_seconds": duration,
                    "id": planning_request_id,
                },
            )
            await db.commit()

            api_logger.info(
                "Planning request completed",
                request_id=planning_request_id,
                user_id=user_id,
                duration=duration,
                team_name=team_plan.get("team_name"),
            )

        except Exception as e:
            # Rollback any pending transaction first
            await db.rollback()

            # Update status to failed
            await db.execute(
                text("""UPDATE planning_requests
                   SET status = :status, error_message = :error_message, completed_at = :completed_at
                   WHERE id = :id"""),
                {
                    "status": PlanningStatus.FAILED.value,
                    "error_message": str(e),
                    "completed_at": datetime.now(timezone.utc),
                    "id": planning_request_id,
                },
            )
            await db.commit()

            api_logger.error(
                "Planning request failed",
                request_id=planning_request_id,
                user_id=user_id,
                error=str(e),
            )


def _calculate_validation_score(team_plan: Dict[str, Any]) -> float:
    """Calculate validation score for a team plan."""
    score = 0.0
    max_score = 100.0

    # Check required fields (40 points)
    required_fields = ["team_name", "description", "objective", "team_members", "workflow"]
    for field in required_fields:
        if field in team_plan and team_plan[field]:
            score += 8.0

    # Check team size (20 points)
    team_members = team_plan.get("team_members", [])
    if isinstance(team_members, list):
        team_size = len(team_members)
        if 2 <= team_size <= 6:
            score += 20.0
        elif team_size > 0:
            score += 10.0

    # Check workflow steps (20 points)
    workflow = team_plan.get("workflow", {})
    if isinstance(workflow, dict):
        steps = workflow.get("steps", [])
        if isinstance(steps, list) and len(steps) >= 3:
            score += 20.0
        elif isinstance(steps, list) and len(steps) > 0:
            score += 10.0

    # Check description quality (20 points)
    description = team_plan.get("description", "")
    if isinstance(description, str):
        if len(description) > 100:
            score += 20.0
        elif len(description) > 50:
            score += 15.0
        elif len(description) > 20:
            score += 10.0

    return min(score, max_score)
