"""
Application log management endpoints.
"""

import json
import csv
import io
from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Request, status
from fastapi.responses import StreamingResponse
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.dependencies.auth import get_current_active_user, get_current_user
from app.api.dependencies.database import get_db
from app.models.user import User, UserStatus
from app.models.application_log import (
    LogLevel, EventType, ApplicationLogResponse, ApplicationLogDetailResponse,
    ApplicationLogListResponse, LogFilterParams
)
from app.services.logging_service import ApplicationLoggingService
from app.core.logging import api_logger

router = APIRouter()


@router.get("/test-stats")
async def test_log_statistics():
    """Test endpoint for log statistics without authentication."""
    try:
        from app.core.database import AsyncSessionLocal

        async with AsyncSessionLocal() as db:
            # Simple test query
            result = await db.execute(text("SELECT COUNT(*) FROM application_logs"))
            count = result.scalar()

            return {
                "status": "success",
                "total_logs": count,
                "message": "Statistics endpoint is working"
            }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "message": "Statistics endpoint failed"
        }


@router.get("/stats-simple")
async def get_log_statistics_simple():
    """Simple log statistics endpoint without any dependencies."""
    try:
        return {
            "user_id": None,
            "total_logs": 0,
            "level_distribution": {},
            "event_type_distribution": {},
            "recent_activity_24h": 0,
            "generated_at": datetime.now().isoformat(),
            "message": "Simple stats endpoint working"
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "message": "Simple stats endpoint failed"
        }


@router.get("/test-db")
async def test_database_dependency(
    db: AsyncSession = Depends(get_db)
):
    """Test endpoint to check if database dependency works without auth."""
    try:
        # Simple test query
        result = await db.execute(text("SELECT COUNT(*) FROM application_logs"))
        count = result.scalar()

        return {
            "status": "success",
            "total_logs": count,
            "message": "Database dependency working"
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "message": "Database dependency failed"
        }


@router.get("", response_model=ApplicationLogListResponse)
async def get_application_logs(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(50, ge=1, le=1000, description="Number of logs per page"),
    level: Optional[LogLevel] = Query(None, description="Filter by log level"),
    event_type: Optional[EventType] = Query(None, description="Filter by event type"),
    agent_id: Optional[str] = Query(None, description="Filter by agent ID"),
    test_id: Optional[str] = Query(None, description="Filter by test ID"),
    template_id: Optional[str] = Query(None, description="Filter by template ID"),
    source_module: Optional[str] = Query(None, description="Filter by source module"),
    error_code: Optional[str] = Query(None, description="Filter by error code"),
    start_date: Optional[datetime] = Query(None, description="Filter logs after this date"),
    end_date: Optional[datetime] = Query(None, description="Filter logs before this date"),
    search_query: Optional[str] = Query(None, description="Search in log messages and metadata"),
    tags: Optional[List[str]] = Query(None, description="Filter by tags"),
    ip_address: Optional[str] = Query(None, description="Filter by IP address"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get application logs with filtering and pagination."""
    try:
        # Create filter parameters
        filters = LogFilterParams(
            level=level,
            event_type=event_type,
            agent_id=agent_id,
            test_id=test_id,
            template_id=template_id,
            source_module=source_module,
            error_code=error_code,
            start_date=start_date,
            end_date=end_date,
            search_query=search_query,
            tags=tags,
            ip_address=ip_address
        )

        # Get logs with user isolation
        logging_service = ApplicationLoggingService(db)
        result = await logging_service.get_logs(
            filters=filters,
            page=page,
            limit=limit,
            user_id=current_user.id  # User isolation
        )

        api_logger.info(
            f"Retrieved {len(result.logs)} application logs for user {current_user.id}",
            user_id=current_user.id,
            page=page,
            limit=limit,
            total=result.total
        )

        return result

    except Exception as e:
        api_logger.error(f"Failed to retrieve application logs: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve logs: {str(e)}"
        )


@router.get("/stats")
async def get_log_statistics(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get log statistics for the authenticated user."""
    try:
        # Get statistics using the logging service
        logging_service = ApplicationLoggingService(db)
        statistics = await logging_service.get_statistics(user_id=current_user.id)

        api_logger.info(
            f"Retrieved log statistics for user {current_user.id}",
            user_id=current_user.id,
            total_logs=statistics.get("total_logs", 0)
        )

        return statistics

    except Exception as e:
        api_logger.error(f"Failed to get log statistics: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get log statistics: {str(e)}"
        )


@router.get("/{log_id}", response_model=ApplicationLogDetailResponse)
async def get_application_log_detail(
    log_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get detailed information for a specific log entry."""
    try:
        logging_service = ApplicationLoggingService(db)
        log_detail = await logging_service.get_log_detail(
            log_id=log_id,
            user_id=current_user.id  # User isolation
        )

        if not log_detail:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Log entry not found"
            )

        api_logger.info(
            f"Retrieved log detail for log {log_id}",
            user_id=current_user.id,
            log_id=log_id
        )

        return log_detail

    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to retrieve log detail: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve log detail: {str(e)}"
        )


@router.get("/export")
async def export_application_logs(
    format: str = Query("json", description="Export format: json or csv"),
    level: Optional[LogLevel] = Query(None, description="Filter by log level"),
    event_type: Optional[EventType] = Query(None, description="Filter by event type"),
    agent_id: Optional[str] = Query(None, description="Filter by agent ID"),
    test_id: Optional[str] = Query(None, description="Filter by test ID"),
    start_date: Optional[datetime] = Query(None, description="Filter logs after this date"),
    end_date: Optional[datetime] = Query(None, description="Filter logs before this date"),
    search_query: Optional[str] = Query(None, description="Search in log messages"),
    limit: int = Query(10000, ge=1, le=50000, description="Maximum number of logs to export"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Export application logs to file."""
    try:
        # Create filter parameters
        filters = LogFilterParams(
            level=level,
            event_type=event_type,
            agent_id=agent_id,
            test_id=test_id,
            start_date=start_date,
            end_date=end_date,
            search_query=search_query
        )

        # Get logs for export
        logging_service = ApplicationLoggingService(db)
        result = await logging_service.get_logs(
            filters=filters,
            page=1,
            limit=limit,
            user_id=current_user.id  # User isolation
        )

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        if format.lower() == "csv":
            # Create CSV export
            output = io.StringIO()
            writer = csv.writer(output)

            # Write header
            writer.writerow([
                "ID", "UUID", "Timestamp", "Level", "Event Type", "Message",
                "User ID", "Agent ID", "Test ID", "Source Module", "Source Function",
                "Execution Time (ms)", "Error Code", "Error Type", "IP Address"
            ])

            # Write data
            for log in result.logs:
                writer.writerow([
                    log.id, log.uuid, log.timestamp.isoformat(), log.level.value,
                    log.event_type.value, log.message, log.user_id, log.agent_id,
                    log.test_id, log.source_module, log.source_function,
                    log.execution_time_ms, log.error_code, log.error_type, log.ip_address
                ])

            output.seek(0)
            return StreamingResponse(
                io.StringIO(output.getvalue()),
                media_type="text/csv",
                headers={"Content-Disposition": f"attachment; filename=application_logs_{timestamp}.csv"}
            )

        else:
            # Create JSON export
            export_data = {
                "exported_at": datetime.now().isoformat(),
                "exported_by": current_user.id,
                "filters": filters.model_dump(exclude_none=True),
                "total_entries": result.total,
                "exported_entries": len(result.logs),
                "logs": [log.model_dump() for log in result.logs]
            }

            json_str = json.dumps(export_data, indent=2, ensure_ascii=False, default=str)
            return StreamingResponse(
                io.StringIO(json_str),
                media_type="application/json",
                headers={"Content-Disposition": f"attachment; filename=application_logs_{timestamp}.json"}
            )

    except Exception as e:
        api_logger.error(f"Failed to export application logs: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to export logs: {str(e)}"
        )


@router.get("/stats-public")
async def get_log_statistics_public(
    db: AsyncSession = Depends(get_db)
):
    """Get log statistics without authentication (temporary for testing)."""
    try:
        # Use user_id = 1 for testing
        user_id = 1
        api_logger.info(f"Getting log statistics for user {user_id} (public endpoint)")

        # Simple approach: use SQLModel queries
        from sqlalchemy import select, func
        from app.models.application_log import ApplicationLog

        # Count total logs for user
        total_query = select(func.count(ApplicationLog.id)).where(ApplicationLog.user_id == user_id)
        total_result = await db.execute(total_query)
        total_logs = total_result.scalar() or 0

        # Count by level
        level_query = select(ApplicationLog.level, func.count(ApplicationLog.id)).where(
            ApplicationLog.user_id == user_id
        ).group_by(ApplicationLog.level)
        level_result = await db.execute(level_query)
        level_stats = level_result.fetchall()

        # Count by event type
        event_query = select(ApplicationLog.event_type, func.count(ApplicationLog.id)).where(
            ApplicationLog.user_id == user_id
        ).group_by(ApplicationLog.event_type)
        event_result = await db.execute(event_query)
        event_stats = event_result.fetchall()

        # Build response
        level_distribution = {str(row[0]): row[1] for row in level_stats}
        event_type_distribution = {str(row[0]): row[1] for row in event_stats}

        response = {
            "user_id": user_id,
            "total_logs": total_logs,
            "level_distribution": level_distribution,
            "event_type_distribution": event_type_distribution,
            "recent_activity_24h": total_logs,
            "generated_at": datetime.now().isoformat()
        }

        api_logger.info(f"Successfully generated log statistics for user {user_id}: {total_logs} logs")
        return response

    except Exception as e:
        api_logger.error(f"Failed to get log statistics: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get log statistics: {str(e)}"
        )



