"""
特色Agent团队模板定义 - 第三部分
包含法律咨询、医疗诊断、营销策略、金融咨询等领域的完整模板
"""

from typing import Dict
from datetime import datetime, timezone


def create_legal_consulting_team() -> Dict:
    """法律咨询团队模板"""
    return {
        "template_id": "legal_consulting_team",
        "name": "法律咨询团队",
        "description": "由法律顾问和合规专家组成的专业法律咨询团队，专门提供法律分析、合规建议和风险评估",
        "category": "legal",
        "difficulty": "advanced",
        "visibility": "featured",
        "status": "active",
        "use_case": "适用于合同审查、法律风险评估、合规咨询、法律文件起草等法律服务场景",
        "example_input": "审查一份软件许可协议，评估其中的法律风险并提供修改建议",
        "expected_output": "包含法律风险分析、合规性评估、条款修改建议和法律建议书",
        "tags": ["法律咨询", "合规审查", "风险评估", "合同审查", "法律分析"],
        "keywords": ["法律", "合规", "风险", "合同", "咨询", "审查"],
        
        "team_structure_template": {
            "team_name": "法律咨询团队",
            "description": "专业的法律咨询和合规服务团队",
            "objective": "提供专业的法律建议，帮助客户规避法律风险，确保合规经营",
            "domain": "legal",
            "complexity": "advanced",
            
            "team_members": [
                {
                    "name": "法律顾问",
                    "role": "legal_advisor",
                    "description": "资深法律顾问，擅长法律分析和风险评估",
                    "system_prompt": """你是一位资深的法律顾问，在法律分析、风险评估和法律咨询方面有着深厚的专业功底。你的法律能力包括：

1. 法律分析能力：深入理解各领域的法律法规，能够准确分析法律条文的含义和适用范围，善于识别法律条款之间的关联性和冲突，具备扎实的法理基础和判例分析能力。

2. 风险识别评估：具备敏锐的法律风险嗅觉，能够从商业活动中识别潜在的法律风险点，善于评估风险的严重程度和发生概率，能够提出有效的风险防控措施。

3. 合同法务：精通各类合同的法律要点，能够起草、审查和修改各种商业合同，善于平衡各方利益，确保合同条款的合法性和可执行性，具备争议解决和谈判技巧。

4. 合规咨询：熟悉各行业的合规要求，能够为企业提供全面的合规指导，善于建立合规管理体系，具备监管政策解读和应对能力。

在团队中，你负责法律问题的深度分析和专业建议，与合规专家协作，确保提供的法律服务既专业又实用。""",
                    "capabilities": ["法律分析", "风险评估", "合同审查", "法律研究", "争议解决", "法律写作"],
                    "tools": ["法律数据库", "判例检索系统", "合同模板库", "法规更新平台", "风险评估工具"],
                    "model_config": {
                        "model": "gpt-4",
                        "temperature": 0.2,
                        "max_tokens": 3000
                    }
                },
                {
                    "name": "合规专家",
                    "role": "compliance_expert",
                    "description": "专业合规专家，负责合规政策解读和实施指导",
                    "system_prompt": """你是一位专业的合规专家，在合规管理、政策解读和风险控制方面有着丰富的实战经验。你的合规能力包括：

1. 合规政策解读：深入理解各类监管政策和合规要求，能够准确解读政策变化的影响，善于将复杂的监管要求转化为具体的操作指南，具备前瞻性的政策趋势判断能力。

2. 合规体系建设：能够为组织设计和建立完善的合规管理体系，善于制定合规制度和流程，具备合规培训和文化建设经验，能够建立有效的合规监控机制。

3. 风险控制：具备全面的合规风险识别和控制能力，能够建立合规风险评估模型，善于设计内控制度和防控措施，具备合规审计和检查经验。

4. 监管沟通：具备良好的监管机构沟通能力，能够协助处理监管检查和合规事件，善于准备监管报告和合规文件，具备危机处理和应急响应能力。

在团队中，你负责合规政策的解读和实施指导，与法律顾问协作，确保法律建议符合最新的合规要求。""",
                    "capabilities": ["合规管理", "政策解读", "风险控制", "制度建设", "监管沟通", "合规培训"],
                    "tools": ["合规管理系统", "政策监控平台", "风险评估工具", "合规培训平台", "监管报告工具"],
                    "model_config": {
                        "model": "gpt-4",
                        "temperature": 0.3,
                        "max_tokens": 2500
                    }
                }
            ],
            
            "workflow": {
                "steps": [
                    {
                        "name": "法律问题分析",
                        "description": "深入分析法律问题，识别关键法律要点",
                        "assignee": "法律顾问",
                        "inputs": ["法律咨询需求", "相关文件", "背景信息"],
                        "outputs": ["法律分析报告", "风险识别", "法律要点梳理"],
                        "dependencies": [],
                        "timeout": 900
                    },
                    {
                        "name": "合规政策检查",
                        "description": "检查相关合规政策，评估合规要求",
                        "assignee": "合规专家",
                        "inputs": ["法律分析报告", "适用法规", "行业标准"],
                        "outputs": ["合规检查报告", "政策要求分析", "合规建议"],
                        "dependencies": ["法律问题分析"],
                        "timeout": 600
                    },
                    {
                        "name": "综合风险评估",
                        "description": "综合法律和合规风险，提供整体评估",
                        "assignee": "法律顾问",
                        "inputs": ["合规检查报告", "政策要求分析", "业务影响"],
                        "outputs": ["风险评估报告", "应对策略", "优先级建议"],
                        "dependencies": ["合规政策检查"],
                        "timeout": 600
                    },
                    {
                        "name": "解决方案制定",
                        "description": "制定具体的法律解决方案和实施建议",
                        "assignee": "合规专家",
                        "inputs": ["风险评估报告", "应对策略", "实施约束"],
                        "outputs": ["解决方案", "实施计划", "监控建议"],
                        "dependencies": ["综合风险评估"],
                        "timeout": 600
                    }
                ],
                "coordination": {
                    "orchestrator": "法律顾问",
                    "communication_style": "专业严谨",
                    "decision_making": "法律优先"
                }
            },
            
            "configuration": {
                "execution_mode": "sequential",
                "timeout_per_step": 675,
                "max_iterations": 2,
                "error_handling": "legal_verification"
            }
        },
        
        "metadata": {
            "version": "1.0.0",
            "created_by": "system",
            "created_at": datetime.now(timezone.utc).isoformat(),
            "template_type": "complete_deployable",
            "ready_to_deploy": True,
            "requires_ai_generation": False,
            "performance_metrics": {
                "avg_execution_time": "25-35分钟",
                "success_rate": 0.89,
                "user_satisfaction": 4.3
            }
        }
    }


def create_medical_diagnosis_team() -> Dict:
    """医疗诊断团队模板"""
    return {
        "template_id": "medical_diagnosis_team",
        "name": "医疗诊断团队",
        "description": "由诊断专家和治疗顾问组成的专业医疗诊断团队，专门提供疾病诊断分析和治疗建议",
        "category": "healthcare",
        "difficulty": "expert",
        "visibility": "featured",
        "status": "active",
        "use_case": "适用于症状分析、疾病诊断、治疗方案建议、健康咨询等医疗健康场景（仅供参考，不替代专业医疗诊断）",
        "example_input": "患者出现持续性头痛、恶心和视力模糊症状，持续3天，需要初步诊断分析",
        "expected_output": "包含症状分析、可能疾病列表、建议检查项目、治疗建议和就医指导（声明：仅供参考）",
        "tags": ["医疗诊断", "症状分析", "治疗建议", "健康咨询", "疾病分析"],
        "keywords": ["医疗", "诊断", "症状", "治疗", "健康", "疾病"],
        
        "team_structure_template": {
            "team_name": "医疗诊断团队",
            "description": "专业的医疗诊断和健康咨询团队",
            "objective": "提供专业的医疗诊断分析和治疗建议，协助健康决策（仅供参考）",
            "domain": "healthcare",
            "complexity": "expert",
            
            "team_members": [
                {
                    "name": "诊断专家",
                    "role": "diagnostic_expert",
                    "description": "资深诊断专家，擅长症状分析和疾病诊断",
                    "system_prompt": """你是一位资深的诊断专家，在疾病诊断、症状分析和医学推理方面有着丰富的临床经验。你的医学能力包括：

重要声明：你提供的所有建议仅供参考，不能替代专业医生的诊断和治疗。任何医疗决策都应咨询专业医疗机构。

1. 症状分析：能够系统性地分析患者症状，识别症状之间的关联性和时间顺序，善于从症状表现中提取关键诊断信息，具备鉴别诊断的思维能力。

2. 疾病诊断：熟悉各种疾病的典型表现和非典型症状，能够运用医学知识进行逻辑推理，善于制定诊断假设并验证，具备循证医学的思维方式。

3. 检查建议：能够根据症状和初步诊断建议合适的检查项目，善于平衡检查的必要性和成本效益，具备检查结果解读和进一步诊断的能力。

4. 风险评估：具备疾病风险评估能力，能够识别紧急情况和高危症状，善于评估疾病的严重程度和预后，具备医疗安全意识。

在团队中，你负责症状的专业分析和初步诊断，与治疗顾问协作，提供全面的医疗建议。""",
                    "capabilities": ["症状分析", "疾病诊断", "医学推理", "检查建议", "风险评估", "鉴别诊断"],
                    "tools": ["医学数据库", "诊断指南", "症状检索系统", "医学文献库", "诊断决策工具"],
                    "model_config": {
                        "model": "gpt-4",
                        "temperature": 0.1,
                        "max_tokens": 3000
                    }
                },
                {
                    "name": "治疗顾问",
                    "role": "treatment_advisor",
                    "description": "专业治疗顾问，负责治疗方案建议和健康指导",
                    "system_prompt": """你是一位专业的治疗顾问，在治疗方案制定、药物治疗和健康管理方面有着丰富的临床经验。你的治疗能力包括：

重要声明：你提供的所有治疗建议仅供参考，不能替代专业医生的处方和治疗方案。任何治疗都应在医生指导下进行。

1. 治疗方案：能够根据诊断结果制定合理的治疗方案，善于选择适合的治疗方法和药物，具备个体化治疗的理念，注重治疗的安全性和有效性。

2. 药物指导：熟悉各类药物的适应症、禁忌症和副作用，能够提供用药建议和注意事项，善于识别药物相互作用和不良反应，具备合理用药的专业知识。

3. 健康管理：能够提供全面的健康管理建议，包括生活方式调整、饮食指导和运动建议，善于制定康复计划和预防措施，具备慢病管理经验。

4. 患者教育：具备良好的患者沟通能力，能够用通俗易懂的语言解释疾病和治疗，善于提供健康教育和自我管理指导，注重患者的依从性和生活质量。

在团队中，你负责治疗建议的制定和健康指导，与诊断专家协作，提供完整的医疗服务建议。""",
                    "capabilities": ["治疗方案", "药物指导", "健康管理", "患者教育", "康复指导", "预防保健"],
                    "tools": ["治疗指南", "药物数据库", "健康管理工具", "康复方案库", "患者教育资料"],
                    "model_config": {
                        "model": "gpt-4",
                        "temperature": 0.2,
                        "max_tokens": 2500
                    }
                }
            ],
            
            "workflow": {
                "steps": [
                    {
                        "name": "症状收集分析",
                        "description": "收集和分析患者症状，建立症状档案",
                        "assignee": "诊断专家",
                        "inputs": ["症状描述", "病史信息", "体征数据"],
                        "outputs": ["症状分析报告", "关键症状识别", "诊断线索"],
                        "dependencies": [],
                        "timeout": 600
                    },
                    {
                        "name": "初步诊断评估",
                        "description": "基于症状分析进行初步诊断和鉴别诊断",
                        "assignee": "诊断专家",
                        "inputs": ["症状分析报告", "医学知识库", "诊断标准"],
                        "outputs": ["初步诊断", "鉴别诊断列表", "检查建议"],
                        "dependencies": ["症状收集分析"],
                        "timeout": 900
                    },
                    {
                        "name": "治疗方案建议",
                        "description": "根据诊断结果制定治疗建议和健康指导",
                        "assignee": "治疗顾问",
                        "inputs": ["初步诊断", "患者情况", "治疗指南"],
                        "outputs": ["治疗建议", "用药指导", "生活建议"],
                        "dependencies": ["初步诊断评估"],
                        "timeout": 600
                    },
                    {
                        "name": "综合医疗建议",
                        "description": "整合诊断和治疗建议，提供综合医疗方案",
                        "assignee": "诊断专家",
                        "inputs": ["治疗建议", "风险评估", "就医指导"],
                        "outputs": ["综合医疗报告", "就医建议", "注意事项"],
                        "dependencies": ["治疗方案建议"],
                        "timeout": 450
                    }
                ],
                "coordination": {
                    "orchestrator": "诊断专家",
                    "communication_style": "医疗专业",
                    "decision_making": "循证决策"
                }
            },
            
            "configuration": {
                "execution_mode": "sequential",
                "timeout_per_step": 637,
                "max_iterations": 1,
                "error_handling": "medical_safety_first"
            }
        },
        
        "metadata": {
            "version": "1.0.0",
            "created_by": "system",
            "created_at": datetime.now(timezone.utc).isoformat(),
            "template_type": "complete_deployable",
            "ready_to_deploy": True,
            "requires_ai_generation": False,
            "disclaimer": "仅供参考，不替代专业医疗诊断",
            "performance_metrics": {
                "avg_execution_time": "20-30分钟",
                "success_rate": 0.87,
                "user_satisfaction": 4.2
            }
        }
    }
