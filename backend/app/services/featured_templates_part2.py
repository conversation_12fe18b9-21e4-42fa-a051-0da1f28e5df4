"""
特色Agent团队模板定义 - 第二部分
包含产品开发、教育培训、法律咨询等领域的完整模板
"""

from typing import Dict
from datetime import datetime, timezone


def create_product_development_team() -> Dict:
    """产品开发团队模板"""
    return {
        "template_id": "product_development_team",
        "name": "产品开发团队",
        "description": "由产品经理、UX设计师和技术架构师组成的全栈产品开发团队，专门负责产品规划、设计和技术实现",
        "category": "product",
        "difficulty": "advanced",
        "visibility": "featured",
        "status": "active",
        "use_case": "适用于新产品开发、产品功能设计、用户体验优化、技术架构规划等产品全生命周期管理",
        "example_input": "设计一款面向年轻人的社交电商APP，需要包含社交功能、购物体验和个性化推荐",
        "expected_output": "包含产品需求文档、用户体验设计方案、技术架构设计、开发计划和上线策略",
        "tags": ["产品开发", "用户体验", "技术架构", "项目管理", "敏捷开发"],
        "keywords": ["产品", "设计", "开发", "架构", "用户体验", "敏捷"],
        
        "team_structure_template": {
            "team_name": "产品开发团队",
            "description": "全栈产品开发和设计团队",
            "objective": "打造用户喜爱的优质产品，实现商业价值和用户价值的平衡",
            "domain": "product",
            "complexity": "advanced",
            
            "team_members": [
                {
                    "name": "产品经理",
                    "role": "product_manager",
                    "description": "资深产品经理，负责产品策略、需求管理和项目协调",
                    "system_prompt": """你是一位资深的产品经理，在产品规划、需求管理和项目执行方面有着丰富的经验。你的核心能力包括：

1. 产品战略规划：深入理解市场需求和用户痛点，能够制定清晰的产品愿景和发展路线图，善于平衡商业目标和用户需求，具备敏锐的市场洞察力和竞争分析能力。

2. 需求管理：擅长收集、分析和优先级排序用户需求，能够将复杂的业务需求转化为清晰的产品功能规格，善于与各方利益相关者沟通协调，确保需求的准确性和可行性。

3. 项目协调：具备优秀的跨团队协作能力，能够协调设计、开发、测试等各个环节，善于制定合理的项目计划和里程碑，具备风险识别和问题解决能力。

4. 数据驱动决策：重视数据分析和用户反馈，能够通过A/B测试、用户调研等方式验证产品假设，善于从数据中发现产品优化机会，持续迭代改进产品体验。

在团队中，你负责整体产品方向的把控，协调设计和技术团队的工作，确保产品按时高质量交付。""",
                    "capabilities": ["产品规划", "需求管理", "项目协调", "数据分析", "用户研究", "商业分析"],
                    "tools": ["产品管理工具", "原型设计工具", "数据分析平台", "项目管理软件", "用户反馈系统"],
                    "model_config": {
                        "model": "gpt-4",
                        "temperature": 0.6,
                        "max_tokens": 2500
                    }
                },
                {
                    "name": "UX设计师",
                    "role": "ux_designer",
                    "description": "专业UX设计师，负责用户体验设计和交互设计",
                    "system_prompt": """你是一位专业的UX设计师，在用户体验设计、交互设计和视觉设计方面有着深厚的专业功底。你的设计理念和能力包括：

1. 用户体验设计：深入理解用户心理和行为模式，能够设计直观、易用的用户界面，善于通过用户旅程图、信息架构等方法优化用户体验，注重可用性和可访问性设计原则。

2. 交互设计：精通各种交互设计模式和最佳实践，能够设计流畅自然的交互流程，善于平衡功能复杂性和操作简便性，具备移动端和桌面端的设计经验。

3. 视觉设计：具备良好的视觉设计能力和审美素养，能够创建一致的视觉语言和设计系统，善于运用色彩、字体、布局等元素提升产品的视觉吸引力。

4. 设计验证：重视设计的可测试性和可验证性，能够通过用户测试、可用性测试等方法验证设计效果，善于根据反馈迭代优化设计方案。

在团队中，你负责产品的用户体验设计，与产品经理密切配合理解需求，与技术架构师协作确保设计的可实现性。""",
                    "capabilities": ["用户体验设计", "交互设计", "视觉设计", "原型制作", "用户测试", "设计系统"],
                    "tools": ["Figma/Sketch", "原型工具", "用户测试平台", "设计系统工具", "色彩工具"],
                    "model_config": {
                        "model": "gpt-4",
                        "temperature": 0.7,
                        "max_tokens": 2000
                    }
                },
                {
                    "name": "技术架构师",
                    "role": "tech_architect",
                    "description": "资深技术架构师，负责技术方案设计和架构规划",
                    "system_prompt": """你是一位资深的技术架构师，在系统架构设计、技术选型和工程实践方面有着丰富的经验。你的技术能力包括：

1. 系统架构设计：深入理解各种架构模式和设计原则，能够设计可扩展、高可用的系统架构，善于权衡性能、成本和复杂度，具备微服务、分布式系统的设计经验。

2. 技术选型：熟悉各种技术栈和开发框架，能够根据项目需求选择合适的技术方案，善于评估技术风险和实施难度，具备前端、后端、数据库等全栈技术视野。

3. 工程实践：重视代码质量和工程效率，能够制定合理的开发规范和最佳实践，善于设计CI/CD流程和自动化测试策略，具备DevOps和云原生技术经验。

4. 团队协作：具备良好的技术沟通能力，能够向非技术人员解释复杂的技术概念，善于指导开发团队的技术实现，具备技术债务管理和重构规划能力。

在团队中，你负责技术方案的设计和实现指导，与产品经理和设计师协作，确保技术实现能够支撑产品需求和用户体验。""",
                    "capabilities": ["系统架构", "技术选型", "性能优化", "安全设计", "工程实践", "团队指导"],
                    "tools": ["架构设计工具", "代码管理工具", "性能监控工具", "云服务平台", "自动化工具"],
                    "model_config": {
                        "model": "gpt-4",
                        "temperature": 0.4,
                        "max_tokens": 2500
                    }
                }
            ],
            
            "workflow": {
                "steps": [
                    {
                        "name": "产品需求分析",
                        "description": "分析产品需求，制定产品规划和功能规格",
                        "assignee": "产品经理",
                        "inputs": ["产品需求", "市场分析", "用户反馈"],
                        "outputs": ["产品需求文档", "功能规格", "优先级排序"],
                        "dependencies": [],
                        "timeout": 900
                    },
                    {
                        "name": "用户体验设计",
                        "description": "基于产品需求设计用户体验和交互流程",
                        "assignee": "UX设计师",
                        "inputs": ["产品需求文档", "功能规格", "用户画像"],
                        "outputs": ["设计原型", "交互规范", "视觉设计"],
                        "dependencies": ["产品需求分析"],
                        "timeout": 1200
                    },
                    {
                        "name": "技术架构设计",
                        "description": "设计技术架构和实现方案",
                        "assignee": "技术架构师",
                        "inputs": ["产品需求文档", "设计原型", "技术约束"],
                        "outputs": ["技术架构方案", "开发计划", "技术风险评估"],
                        "dependencies": ["产品需求分析"],
                        "timeout": 1200
                    },
                    {
                        "name": "方案整合优化",
                        "description": "整合产品、设计和技术方案，进行优化调整",
                        "assignee": "产品经理",
                        "inputs": ["设计原型", "技术架构方案", "开发计划"],
                        "outputs": ["最终产品方案", "实施计划", "里程碑规划"],
                        "dependencies": ["用户体验设计", "技术架构设计"],
                        "timeout": 600
                    }
                ],
                "coordination": {
                    "orchestrator": "产品经理",
                    "communication_style": "敏捷协作",
                    "decision_making": "共识决策"
                }
            },
            
            "configuration": {
                "execution_mode": "parallel_sequential",
                "timeout_per_step": 900,
                "max_iterations": 2,
                "error_handling": "collaborative_retry"
            }
        },
        
        "metadata": {
            "version": "1.0.0",
            "created_by": "system",
            "created_at": datetime.now(timezone.utc).isoformat(),
            "template_type": "complete_deployable",
            "ready_to_deploy": True,
            "requires_ai_generation": False,
            "performance_metrics": {
                "avg_execution_time": "30-45分钟",
                "success_rate": 0.90,
                "user_satisfaction": 4.7
            }
        }
    }


def create_education_training_team() -> Dict:
    """教育培训团队模板"""
    return {
        "template_id": "education_training_team",
        "name": "教育培训团队",
        "description": "由课程设计师和教学专家组成的专业教育培训团队，专门负责课程开发、教学设计和培训实施",
        "category": "education",
        "difficulty": "intermediate",
        "visibility": "featured",
        "status": "active",
        "use_case": "适用于企业培训、在线课程开发、教学方案设计、培训效果评估等教育培训场景",
        "example_input": "为IT公司设计一套完整的新员工技术培训课程，包含编程基础、项目管理和团队协作",
        "expected_output": "包含课程大纲、教学内容、实践项目、评估方案和培训实施计划",
        "tags": ["教育培训", "课程设计", "教学方法", "培训评估", "在线学习"],
        "keywords": ["教育", "培训", "课程", "教学", "学习", "评估"],
        
        "team_structure_template": {
            "team_name": "教育培训团队",
            "description": "专业的教育培训和课程开发团队",
            "objective": "设计高效的学习体验，提升学员能力和培训效果",
            "domain": "education",
            "complexity": "intermediate",
            
            "team_members": [
                {
                    "name": "课程设计师",
                    "role": "instructional_designer",
                    "description": "专业课程设计师，擅长教学设计和课程开发",
                    "system_prompt": """你是一位专业的课程设计师，在教学设计、课程开发和学习体验设计方面有着丰富的经验。你的专业能力包括：

1. 教学设计理论：深入理解各种教学设计模型和学习理论，能够运用ADDIE、SAM等设计方法论，善于分析学习者特征和学习目标，设计有效的学习路径和教学策略。

2. 课程内容开发：擅长将复杂的知识内容转化为易于理解和掌握的学习材料，能够设计多样化的学习活动和评估方式，善于运用多媒体和互动技术增强学习体验。

3. 学习体验设计：注重学习者的参与度和学习动机，能够设计引人入胜的学习情境和案例，善于运用游戏化、微学习等现代教学方法，提升学习效果和满意度。

4. 评估与改进：具备完善的课程评估和质量保证体系，能够设计有效的学习评估工具，善于收集和分析学习数据，持续优化课程内容和教学方法。

在团队中，你负责课程的整体设计和内容开发，与教学专家协作，确保课程既符合教学理论又具备实用性。""",
                    "capabilities": ["教学设计", "课程开发", "学习体验设计", "多媒体制作", "评估设计", "质量保证"],
                    "tools": ["教学设计工具", "课程制作平台", "多媒体编辑软件", "学习管理系统", "评估工具"],
                    "model_config": {
                        "model": "gpt-4",
                        "temperature": 0.6,
                        "max_tokens": 2500
                    }
                },
                {
                    "name": "教学专家",
                    "role": "teaching_expert",
                    "description": "资深教学专家，负责教学方法指导和培训实施",
                    "system_prompt": """你是一位资深的教学专家，在教学方法、培训实施和学员指导方面有着丰富的实战经验。你的教学能力包括：

1. 教学方法精通：掌握多种教学方法和技巧，能够根据不同的学习内容和学员特点选择最适合的教学方式，善于运用讲授、讨论、案例分析、实践操作等多种教学手段。

2. 课堂管理：具备优秀的课堂组织和管理能力，能够营造积极的学习氛围，善于调动学员的学习积极性，具备处理各种教学情况和学员问题的经验。

3. 个性化指导：能够识别不同学员的学习特点和需求，提供个性化的学习建议和指导，善于帮助学员克服学习困难，激发学习潜能。

4. 培训效果评估：重视培训效果的跟踪和评估，能够通过多种方式收集学员反馈，善于分析培训数据，提出改进建议，确保培训目标的达成。

在团队中，你负责教学方法的指导和培训的具体实施，与课程设计师协作，确保课程设计能够有效转化为实际的教学效果。""",
                    "capabilities": ["教学实施", "课堂管理", "学员指导", "培训评估", "教学技巧", "沟通协调"],
                    "tools": ["教学平台", "互动工具", "评估系统", "反馈收集工具", "学员管理系统"],
                    "model_config": {
                        "model": "gpt-4",
                        "temperature": 0.5,
                        "max_tokens": 2000
                    }
                }
            ],
            
            "workflow": {
                "steps": [
                    {
                        "name": "培训需求分析",
                        "description": "分析培训需求，明确学习目标和成功标准",
                        "assignee": "教学专家",
                        "inputs": ["培训需求", "学员背景", "组织目标"],
                        "outputs": ["需求分析报告", "学习目标", "成功指标"],
                        "dependencies": [],
                        "timeout": 600
                    },
                    {
                        "name": "课程设计开发",
                        "description": "基于需求分析设计课程结构和学习内容",
                        "assignee": "课程设计师",
                        "inputs": ["需求分析报告", "学习目标", "教学资源"],
                        "outputs": ["课程大纲", "教学内容", "学习活动设计"],
                        "dependencies": ["培训需求分析"],
                        "timeout": 1200
                    },
                    {
                        "name": "教学方法设计",
                        "description": "设计具体的教学方法和实施策略",
                        "assignee": "教学专家",
                        "inputs": ["课程大纲", "教学内容", "学员特点"],
                        "outputs": ["教学方案", "实施计划", "评估策略"],
                        "dependencies": ["课程设计开发"],
                        "timeout": 900
                    },
                    {
                        "name": "课程整合完善",
                        "description": "整合课程内容和教学方案，完善培训方案",
                        "assignee": "课程设计师",
                        "inputs": ["教学方案", "实施计划", "评估策略"],
                        "outputs": ["完整培训方案", "实施指南", "质量标准"],
                        "dependencies": ["教学方法设计"],
                        "timeout": 600
                    }
                ],
                "coordination": {
                    "orchestrator": "教学专家",
                    "communication_style": "教育协作",
                    "decision_making": "专业共识"
                }
            },
            
            "configuration": {
                "execution_mode": "sequential",
                "timeout_per_step": 750,
                "max_iterations": 2,
                "error_handling": "educational_retry"
            }
        },
        
        "metadata": {
            "version": "1.0.0",
            "created_by": "system",
            "created_at": datetime.now(timezone.utc).isoformat(),
            "template_type": "complete_deployable",
            "ready_to_deploy": True,
            "requires_ai_generation": False,
            "performance_metrics": {
                "avg_execution_time": "20-30分钟",
                "success_rate": 0.91,
                "user_satisfaction": 4.5
            }
        }
    }
