"""
User authentication and management service.
"""

import secrets
from datetime import datetime, timedelta, timezone
from typing import Dict, <PERSON>, Optional, Tuple

from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select

from app.core.security import get_password_hash, verify_password, create_access_token
from app.models.user import (
    User, UserSession, UserToken, LoginHistory,
    UserRole, UserStatus, SessionStatus, TokenType,
    UserRegister, UserLogin, ChangePassword, ResetPassword
)


class UserService:
    """User authentication and management service."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_user(
        self, 
        user_data: UserRegister,
        request_info: Optional[Dict] = None
    ) -> Tuple[User, str]:
        """Create a new user account."""
        # Check if user already exists
        statement = select(User).where(User.email == user_data.email)
        result = await self.db.execute(statement)
        existing_user = result.scalar_one_or_none()
        
        if existing_user:
            raise ValueError("User already exists with this email")
        
        # Validate password confirmation
        if user_data.password != user_data.confirm_password:
            raise ValueError("Passwords do not match")
        
        # Create user
        user = User(
            name=user_data.name,
            email=user_data.email,
            password_hash=get_password_hash(user_data.password),
            timezone=user_data.timezone,
            language=user_data.language or "en",
            status=UserStatus.PENDING_VERIFICATION,
            password_changed_at=datetime.now(timezone.utc).replace(tzinfo=None)
        )
        
        self.db.add(user)
        await self.db.commit()
        await self.db.refresh(user)
        
        # Create access token
        access_token = create_access_token(subject=str(user.id))
        
        # Create session
        await self._create_session(user, access_token, request_info)
        
        # Log registration
        await self._log_login_attempt(
            user=user,
            success=True,
            request_info=request_info
        )
        
        return user, access_token
    
    async def authenticate_user(
        self, 
        login_data: UserLogin,
        request_info: Optional[Dict] = None
    ) -> Tuple[Optional[User], Optional[str]]:
        """Authenticate user and create session."""
        # Get user by email
        statement = select(User).where(User.email == login_data.email)
        result = await self.db.execute(statement)
        user = result.scalar_one_or_none()
        
        if not user:
            # Log failed attempt
            await self._log_login_attempt(
                user=None,
                success=False,
                failure_reason="User not found",
                request_info=request_info,
                email=login_data.email
            )
            return None, None
        
        # Check if account is locked
        if user.locked_until and user.locked_until > datetime.now(timezone.utc).replace(tzinfo=None):
            await self._log_login_attempt(
                user=user,
                success=False,
                failure_reason="Account locked",
                request_info=request_info
            )
            raise ValueError("Account is temporarily locked")
        
        # Verify password
        if not verify_password(login_data.password, user.password_hash):
            # Increment failed attempts
            user.failed_login_attempts += 1
            
            # Lock account after 5 failed attempts
            if user.failed_login_attempts >= 5:
                user.locked_until = datetime.now(timezone.utc).replace(tzinfo=None) + timedelta(minutes=30)
            
            self.db.add(user)
            await self.db.commit()
            
            await self._log_login_attempt(
                user=user,
                success=False,
                failure_reason="Invalid password",
                request_info=request_info
            )
            return None, None
        
        # Check user status
        if user.status not in [UserStatus.ACTIVE, UserStatus.PENDING_VERIFICATION]:
            await self._log_login_attempt(
                user=user,
                success=False,
                failure_reason=f"Account status: {user.status}",
                request_info=request_info
            )
            raise ValueError(f"Account is {user.status.value}")
        
        # Reset failed attempts on successful login
        user.failed_login_attempts = 0
        user.locked_until = None
        user.last_login_at = datetime.now(timezone.utc).replace(tzinfo=None)
        user.login_count += 1
        
        self.db.add(user)
        await self.db.commit()
        await self.db.refresh(user)
        
        # Create access token
        token_expiry = timedelta(days=30) if login_data.remember_me else None
        access_token = create_access_token(subject=str(user.id), expires_delta=token_expiry)
        
        # Create session
        await self._create_session(user, access_token, request_info, login_data.remember_me)
        
        # Log successful login
        await self._log_login_attempt(
            user=user,
            success=True,
            request_info=request_info
        )
        
        return user, access_token

    async def verify_credentials(self, email: str, password: str) -> Optional[User]:
        """Verify user credentials without completing login."""
        # Get user by email
        statement = select(User).where(User.email == email)
        result = await self.db.execute(statement)
        user = result.scalar_one_or_none()

        if not user:
            return None

        # Check if account is locked
        if user.locked_until and user.locked_until > datetime.now(timezone.utc).replace(tzinfo=None):
            raise ValueError("Account is temporarily locked")

        # Verify password
        if not verify_password(password, user.password_hash):
            # Increment failed attempts
            user.failed_login_attempts += 1

            # Lock account after 5 failed attempts
            if user.failed_login_attempts >= 5:
                user.locked_until = datetime.now(timezone.utc).replace(tzinfo=None) + timedelta(minutes=30)

            self.db.add(user)
            await self.db.commit()
            return None

        # Check user status
        if user.status not in [UserStatus.ACTIVE, UserStatus.PENDING_VERIFICATION]:
            raise ValueError(f"Account is {user.status.value}")

        return user

    async def create_temp_2fa_session(self, user_id: int, request_info: Optional[Dict] = None) -> str:
        """Create temporary session for 2FA verification."""
        # Generate temporary session ID
        temp_session_id = secrets.token_urlsafe(32)

        # Store in cache or temporary table (for now, we'll use a simple approach)
        # In production, you might want to use Redis or a dedicated temp sessions table
        session = UserSession(
            user_id=user_id,
            session_token=temp_session_id,
            expires_at=datetime.now(timezone.utc).replace(tzinfo=None) + timedelta(minutes=10),  # 10 min expiry
            status=SessionStatus.PENDING,  # Special status for 2FA pending
            ip_address=request_info.get("ip_address") if request_info else None,
            user_agent=request_info.get("user_agent") if request_info else None,
            device_info=request_info.get("device_info") if request_info else None,
        )

        self.db.add(session)
        await self.db.commit()
        await self.db.refresh(session)

        return temp_session_id

    async def complete_login(self, user: User, request_info: Optional[Dict] = None, remember_me: bool = False) -> str:
        """Complete login process and create access token."""
        # Reset failed attempts on successful login
        user.failed_login_attempts = 0
        user.locked_until = None
        user.last_login_at = datetime.now(timezone.utc).replace(tzinfo=None)
        user.login_count += 1

        self.db.add(user)
        await self.db.commit()
        await self.db.refresh(user)

        # Create access token
        token_expiry = timedelta(days=30) if remember_me else None
        access_token = create_access_token(subject=str(user.id), expires_delta=token_expiry)

        # Create session
        await self._create_session(user, access_token, request_info, remember_me)

        # Log successful login
        await self._log_login_attempt(
            user=user,
            success=True,
            request_info=request_info
        )

        return access_token

    async def verify_2fa_and_complete_login(
        self,
        temp_session_id: str,
        totp_code: Optional[str] = None,
        backup_code: Optional[str] = None,
        remember_me: bool = False,
        request_info: Optional[Dict] = None
    ) -> Tuple[Optional[User], Optional[str]]:
        """Verify 2FA code and complete login."""
        # Find temporary session
        statement = select(UserSession).where(
            UserSession.session_token == temp_session_id,
            UserSession.status == SessionStatus.PENDING,
            UserSession.expires_at > datetime.now(timezone.utc).replace(tzinfo=None)
        )
        result = await self.db.execute(statement)
        temp_session = result.scalar_one_or_none()

        if not temp_session:
            raise ValueError("Invalid or expired verification session")

        # Get user
        statement = select(User).where(User.id == temp_session.user_id)
        result = await self.db.execute(statement)
        user = result.scalar_one_or_none()

        if not user:
            raise ValueError("User not found")

        # Verify 2FA code
        verification_passed = False

        if not totp_code and not backup_code:
            raise ValueError("Verification code is required")

        if totp_code:
            if not user.totp_secret:
                raise ValueError("Two-factor authentication is not properly set up for this user")
            from app.core.security import verify_totp_code
            verification_passed = verify_totp_code(user.totp_secret, totp_code)
        elif backup_code:
            if not user.backup_codes:
                raise ValueError("No backup codes available for this user")
            import json
            from app.core.security import verify_backup_code
            hashed_codes = json.loads(user.backup_codes)
            verification_passed = verify_backup_code(backup_code, hashed_codes)

            # If backup code is used, remove it from the list
            if verification_passed:
                from app.core.security import verify_password
                remaining_codes = [
                    code for code in hashed_codes
                    if not verify_password(backup_code, code)
                ]
                user.backup_codes = json.dumps(remaining_codes)
                self.db.add(user)

        if not verification_passed:
            # Increment failed attempts for 2FA failure
            user.failed_login_attempts += 1

            if user.failed_login_attempts >= 5:
                user.locked_until = datetime.now(timezone.utc).replace(tzinfo=None) + timedelta(minutes=30)

            self.db.add(user)
            await self.db.commit()

            # Clean up temp session
            await self.db.delete(temp_session)
            await self.db.commit()

            return None, None

        # Clean up temp session
        await self.db.delete(temp_session)

        # Complete login
        access_token = await self.complete_login(user, request_info, remember_me)

        return user, access_token

    async def authenticate_user_with_2fa(
        self,
        login_data,  # LoginWith2FA type
        request_info: Optional[Dict] = None
    ) -> Tuple[Optional[User], Optional[str]]:
        """Authenticate user with 2FA support."""
        # Get user by email
        statement = select(User).where(User.email == login_data.email)
        result = await self.db.execute(statement)
        user = result.scalar_one_or_none()

        if not user:
            # Log failed attempt
            await self._log_login_attempt(
                user=None,
                success=False,
                failure_reason="User not found",
                request_info=request_info,
                email=login_data.email
            )
            return None, None

        # Check if account is locked
        if user.locked_until and user.locked_until > datetime.now(timezone.utc).replace(tzinfo=None):
            await self._log_login_attempt(
                user=user,
                success=False,
                failure_reason="Account locked",
                request_info=request_info
            )
            raise ValueError("Account is temporarily locked")

        # Verify password
        if not verify_password(login_data.password, user.password_hash):
            # Increment failed attempts
            user.failed_login_attempts += 1

            # Lock account after max attempts (from system settings)
            max_attempts = 5  # Default, should be loaded from system settings
            if user.failed_login_attempts >= max_attempts:
                user.locked_until = datetime.now(timezone.utc).replace(tzinfo=None) + timedelta(minutes=30)

            self.db.add(user)
            await self.db.commit()

            await self._log_login_attempt(
                user=user,
                success=False,
                failure_reason="Invalid password",
                request_info=request_info
            )
            return None, None

        # If 2FA is enabled, verify 2FA code
        if user.is_2fa_enabled:
            verification_passed = False

            if login_data.totp_code:
                from app.core.security import verify_totp_code
                verification_passed = verify_totp_code(user.totp_secret, login_data.totp_code)
            elif login_data.backup_code:
                if user.backup_codes:
                    import json
                    from app.core.security import verify_backup_code
                    hashed_codes = json.loads(user.backup_codes)
                    verification_passed = verify_backup_code(login_data.backup_code, hashed_codes)

                    # If backup code is used, remove it from the list
                    if verification_passed:
                        from app.core.security import get_password_hash
                        # Remove the used backup code
                        remaining_codes = [
                            code for code in hashed_codes
                            if not verify_password(login_data.backup_code, code)
                        ]
                        user.backup_codes = json.dumps(remaining_codes)

            if not verification_passed:
                # Increment failed attempts for 2FA failure
                user.failed_login_attempts += 1

                if user.failed_login_attempts >= max_attempts:
                    user.locked_until = datetime.now(timezone.utc).replace(tzinfo=None) + timedelta(minutes=30)

                self.db.add(user)
                await self.db.commit()

                await self._log_login_attempt(
                    user=user,
                    success=False,
                    failure_reason="Invalid 2FA code",
                    request_info=request_info
                )
                return None, None

        # Reset failed attempts on successful login
        user.failed_login_attempts = 0
        user.locked_until = None
        user.last_login_at = datetime.now(timezone.utc).replace(tzinfo=None)
        user.login_count += 1

        self.db.add(user)
        await self.db.commit()
        await self.db.refresh(user)

        # Create access token
        token_expiry = timedelta(days=30) if login_data.remember_me else None
        access_token = create_access_token(subject=str(user.id), expires_delta=token_expiry)

        # Create session
        await self._create_session(user, access_token, request_info, login_data.remember_me)

        # Log successful login
        await self._log_login_attempt(
            user=user,
            success=True,
            request_info=request_info
        )

        return user, access_token

    async def _create_session(
        self,
        user: User,
        token: str,
        request_info: Optional[Dict] = None,
        remember_me: bool = False
    ) -> UserSession:
        """Create a new user session."""
        expires_at = datetime.now(timezone.utc).replace(tzinfo=None) + timedelta(
            days=30 if remember_me else 1
        )
        
        session = UserSession(
            user_id=user.id,
            session_token=token,
            expires_at=expires_at,
            ip_address=request_info.get("ip_address") if request_info else None,
            user_agent=request_info.get("user_agent") if request_info else None,
            is_secure=request_info.get("is_secure", True) if request_info else True,
            is_mobile=request_info.get("is_mobile", False) if request_info else False,
        )
        
        self.db.add(session)
        await self.db.commit()
        await self.db.refresh(session)
        
        return session
    
    async def _log_login_attempt(
        self,
        user: Optional[User],
        success: bool,
        request_info: Optional[Dict] = None,
        failure_reason: Optional[str] = None,
        email: Optional[str] = None
    ) -> Optional[LoginHistory]:
        """Log login attempt."""
        # For failed attempts without user, we need to create a temporary record
        # In a real system, you might want a separate table for this
        if not user and email:
            # Try to find user by email for logging purposes
            statement = select(User).where(User.email == email)
            result = await self.db.execute(statement)
            user = result.scalar_one_or_none()
        
        if not user:
            return  # Skip logging if no user found
        
        login_record = LoginHistory(
            user_id=user.id,
            success=success,
            failure_reason=failure_reason,
            ip_address=request_info.get("ip_address") if request_info else None,
            user_agent=request_info.get("user_agent") if request_info else None,
            is_suspicious=self._detect_suspicious_activity(user, request_info) if request_info else False,
        )
        
        self.db.add(login_record)
        await self.db.commit()
        
        return login_record
    
    def _detect_suspicious_activity(self, user: User, request_info: Dict) -> bool:
        """Basic suspicious activity detection."""
        # This is a simple implementation - in production you'd want more sophisticated detection
        suspicious_indicators = [
            # Multiple rapid login attempts
            user.failed_login_attempts > 3,
            # Login from unusual location (would need geolocation service)
            # Different user agent pattern
        ]
        
        return any(suspicious_indicators)

    async def update_user_profile(self, user: User, update_data: Dict) -> User:
        """Update user profile."""
        for field, value in update_data.items():
            if hasattr(user, field) and value is not None:
                setattr(user, field, value)

        user.updated_at = datetime.now(timezone.utc).replace(tzinfo=None)
        self.db.add(user)
        await self.db.commit()
        await self.db.refresh(user)

        return user

    async def change_password(self, user: User, password_data: ChangePassword) -> bool:
        """Change user password."""
        # Verify current password
        if not verify_password(password_data.current_password, user.password_hash):
            raise ValueError("Current password is incorrect")

        # Validate new password confirmation
        if password_data.new_password != password_data.confirm_password:
            raise ValueError("New passwords do not match")

        # Update password
        user.password_hash = get_password_hash(password_data.new_password)
        user.password_changed_at = datetime.now(timezone.utc).replace(tzinfo=None)
        user.updated_at = datetime.now(timezone.utc).replace(tzinfo=None)

        self.db.add(user)
        await self.db.commit()

        # Invalidate all existing sessions except current one
        await self._invalidate_user_sessions(user.id, exclude_current=True)

        return True

    async def create_password_reset_token(self, email: str) -> Optional[str]:
        """Create password reset token."""
        statement = select(User).where(User.email == email)
        result = await self.db.execute(statement)
        user = result.scalar_one_or_none()

        if not user:
            # Don't reveal if email exists
            return None

        # Generate reset token
        token = secrets.token_urlsafe(32)
        expires_at = datetime.now(timezone.utc).replace(tzinfo=None) + timedelta(hours=1)

        # Invalidate existing reset tokens
        statement = select(UserToken).where(
            UserToken.user_id == user.id,
            UserToken.token_type == TokenType.PASSWORD_RESET,
            UserToken.is_used == False,
            UserToken.is_revoked == False
        )
        result = await self.db.execute(statement)
        existing_tokens = result.scalars().all()

        for existing_token in existing_tokens:
            existing_token.is_revoked = True
            self.db.add(existing_token)

        # Create new reset token
        reset_token = UserToken(
            user_id=user.id,
            token=token,
            token_type=TokenType.PASSWORD_RESET,
            expires_at=expires_at,
            data={"email": email}
        )

        self.db.add(reset_token)
        await self.db.commit()

        return token

    async def reset_password_with_token(self, token: str, new_password: str) -> bool:
        """Reset password using token."""
        # Find valid token
        statement = select(UserToken).where(
            UserToken.token == token,
            UserToken.token_type == TokenType.PASSWORD_RESET,
            UserToken.is_used == False,
            UserToken.is_revoked == False,
            UserToken.expires_at > datetime.now(timezone.utc).replace(tzinfo=None)
        )
        result = await self.db.execute(statement)
        reset_token = result.scalar_one_or_none()

        if not reset_token:
            raise ValueError("Invalid or expired reset token")

        # Get user
        statement = select(User).where(User.id == reset_token.user_id)
        result = await self.db.execute(statement)
        user = result.scalar_one_or_none()

        if not user:
            raise ValueError("User not found")

        # Update password
        user.password_hash = get_password_hash(new_password)
        user.password_changed_at = datetime.now(timezone.utc).replace(tzinfo=None)
        user.updated_at = datetime.now(timezone.utc).replace(tzinfo=None)

        # Mark token as used
        reset_token.is_used = True
        reset_token.used_at = datetime.now(timezone.utc).replace(tzinfo=None)

        self.db.add(user)
        self.db.add(reset_token)
        await self.db.commit()

        # Invalidate all user sessions
        await self._invalidate_user_sessions(user.id)

        return True

    async def logout_user(self, session_token: str) -> bool:
        """Logout user by invalidating session."""
        statement = select(UserSession).where(
            UserSession.session_token == session_token,
            UserSession.status == SessionStatus.ACTIVE
        )
        result = await self.db.execute(statement)
        session = result.scalar_one_or_none()

        if not session:
            return False

        session.status = SessionStatus.REVOKED
        self.db.add(session)
        await self.db.commit()

        return True

    async def _invalidate_user_sessions(self, user_id: int, exclude_current: bool = False, current_token: str = None):
        """Invalidate all user sessions."""
        statement = select(UserSession).where(
            UserSession.user_id == user_id,
            UserSession.status == SessionStatus.ACTIVE
        )

        if exclude_current and current_token:
            statement = statement.where(UserSession.session_token != current_token)

        result = await self.db.execute(statement)
        sessions = result.scalars().all()

        for session in sessions:
            session.status = SessionStatus.REVOKED
            self.db.add(session)

        await self.db.commit()

    async def get_user_sessions(self, user_id: int) -> List[UserSession]:
        """Get all active user sessions."""
        statement = select(UserSession).where(
            UserSession.user_id == user_id,
            UserSession.status == SessionStatus.ACTIVE,
            UserSession.expires_at > datetime.now(timezone.utc).replace(tzinfo=None)
        ).order_by(UserSession.last_activity_at.desc())

        result = await self.db.execute(statement)
        return result.scalars().all()

    async def get_user_login_history(self, user_id: int, limit: int = 50) -> List[LoginHistory]:
        """Get user login history."""
        statement = select(LoginHistory).where(
            LoginHistory.user_id == user_id
        ).order_by(LoginHistory.created_at.desc()).limit(limit)

        result = await self.db.execute(statement)
        return result.scalars().all()
