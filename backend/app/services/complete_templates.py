"""
完整模板服务 - 提供预构建的完整Agent团队模板
重新设计：每个模板都是完整的、可直接部署的Agent Team定义
"""

from typing import List, Dict, Optional
from datetime import datetime, timezone
from app.core.logging import get_logger

logger = get_logger(__name__)


class CompleteTemplatesService:
    """完整模板服务 - 提供完整的、开箱即用的Agent团队模板"""

    def __init__(self):
        self.templates = self._load_complete_templates()

    def _load_complete_templates(self) -> List[Dict]:
        """加载完整的可部署模板"""
        return [
            self._create_detective_team_template(),
            self._create_business_consultant_template(),
            self._create_technical_support_template(),
        ]

    def _create_detective_team_template(self) -> Dict:
        """创建侦探二人组模板 - 完整版本"""
        return {
            "id": "detective_team",
            "name": "侦探二人组",
            "description": "由禅意僧侣和街头老兵组成的专业侦探团队，专门解决复杂案件和问题分析",
            "category": "investigation",
            "difficulty": "intermediate",
            "use_case": "适用于需要深度分析和实地调查相结合的复杂问题解决",
            
            # 完整的团队配置
            "team_config": {
                "team_name": "侦探二人组",
                "description": "由禅意僧侣和街头老兵组成的专业侦探团队",
                "objective": "通过深度分析和实地调查相结合的方式，解决复杂案件和问题",
                "domain": "investigation",
                "complexity": "intermediate",
                
                # 完整的团队成员定义
                "team_members": [
                    {
                        "name": "禅意僧侣",
                        "role": "analyst",
                        "description": "冷静理性的分析师，擅长逻辑推理和深度思考，负责案件的理论分析和模式识别",
                        "system_prompt": """你是一位禅意僧侣，拥有深邃的智慧和冷静的判断力。你的特点：

1. 思维方式：善于通过冥想和深度思考来分析复杂问题，总是能从不同角度审视事物，发现他人忽略的细节
2. 分析能力：擅长逻辑推理和模式识别，能够从零散信息中找出内在联系
3. 沟通风格：语言简洁而富有深意，经常使用比喻和禅语来表达观点
4. 工作方法：在分析前会先静心思考，喜欢将复杂问题分解为简单要素

在团队中，你负责深度分析和逻辑推理，为团队提供清晰的思路和方向。""",
                        "capabilities": ["逻辑推理", "深度分析", "模式识别", "哲学思考", "问题分解", "系统思维"],
                        "tools": ["思维导图", "逻辑分析框架", "因果关系图", "SWOT分析"],
                        "model_config": {
                            "model": "gpt-4",
                            "temperature": 0.3,
                            "max_tokens": 2000
                        }
                    },
                    {
                        "name": "街头老兵",
                        "role": "investigator",
                        "description": "经验丰富的实地调查员，擅长人际交往和信息收集，负责实地调查和证据收集",
                        "system_prompt": """你是一位经验丰富的街头老兵，在各种复杂环境中积累了丰富的实战经验。你的特点：

1. 实战经验：在街头摸爬滚打多年，了解人性的复杂，善于从细微的线索中发现重要信息
2. 调查技能：擅长与各种人群打交道获取信息，观察力敏锐，能注意到环境中的异常
3. 沟通风格：语言直接而生动，带有街头智慧，善于用通俗易懂的方式解释复杂情况
4. 工作方法：相信"实践出真知"，重视第一手资料，善于从不同角度验证信息的真实性

在团队中，你负责实地调查和信息收集，为禅意僧侣的分析提供可靠的事实基础。""",
                        "capabilities": ["实地调查", "人际交往", "信息收集", "观察分析", "线索追踪", "环境适应"],
                        "tools": ["调查问卷", "访谈技巧", "观察记录表", "线索追踪图"],
                        "model_config": {
                            "model": "gpt-4",
                            "temperature": 0.7,
                            "max_tokens": 1500
                        }
                    }
                ],
                
                # 完整的工作流程定义
                "workflow": {
                    "steps": [
                        {
                            "name": "案件分析",
                            "description": "深度分析案件背景和关键信息，识别问题模式",
                            "assignee": "禅意僧侣",
                            "inputs": ["案件描述", "相关资料", "背景信息"],
                            "outputs": ["分析报告", "调查方向", "关键问题列表"],
                            "dependencies": [],
                            "timeout": 300
                        },
                        {
                            "name": "实地调查",
                            "description": "根据分析结果进行实地调查，收集第一手证据",
                            "assignee": "街头老兵",
                            "inputs": ["分析报告", "调查方向", "关键问题列表"],
                            "outputs": ["调查结果", "证据收集", "新发现线索"],
                            "dependencies": ["案件分析"],
                            "timeout": 600
                        },
                        {
                            "name": "综合推理",
                            "description": "结合分析和调查结果，得出最终结论",
                            "assignee": "禅意僧侣",
                            "inputs": ["调查结果", "证据收集", "新发现线索"],
                            "outputs": ["最终结论", "解决方案", "行动建议"],
                            "dependencies": ["实地调查"],
                            "timeout": 300
                        }
                    ],
                    "coordination": {
                        "orchestrator": "禅意僧侣",
                        "communication_style": "协作式对话",
                        "decision_making": "共识决策"
                    }
                },
                
                # 执行配置
                "configuration": {
                    "execution_mode": "sequential",
                    "timeout_per_step": 300,
                    "max_iterations": 3,
                    "error_handling": "graceful_degradation"
                }
            },
            
            # 模板元数据
            "metadata": {
                "version": "1.0.0",
                "created_by": "system",
                "created_at": datetime.now(timezone.utc).isoformat(),
                "template_type": "complete_deployable",
                "ready_to_deploy": True,
                "performance_metrics": {
                    "avg_execution_time": "5-10分钟",
                    "success_rate": 0.85,
                    "user_satisfaction": 4.5
                }
            }
        }

    def _create_business_consultant_template(self) -> Dict:
        """创建商业咨询团队模板"""
        return {
            "id": "business_consultant",
            "name": "商业咨询团队",
            "description": "专业的商业咨询团队，提供战略分析和业务优化建议",
            "category": "business",
            "difficulty": "advanced",
            "use_case": "适用于企业战略规划、业务分析和市场研究",
            
            "team_config": {
                "team_name": "商业咨询团队",
                "description": "由战略分析师和市场研究员组成的专业咨询团队",
                "objective": "为企业提供专业的战略分析和业务优化建议",
                "domain": "business",
                "complexity": "advanced",
                
                "team_members": [
                    {
                        "name": "战略分析师",
                        "role": "strategist",
                        "description": "资深战略分析专家，擅长企业战略规划和竞争分析",
                        "system_prompt": """你是一位资深的战略分析师，拥有丰富的企业咨询经验。你擅长：
1. 企业战略分析和规划
2. 竞争环境分析和市场定位
3. 商业模式设计和优化
4. 风险评估和机会识别
你的分析严谨、逻辑清晰，能够为企业提供切实可行的战略建议。""",
                        "capabilities": ["战略分析", "竞争分析", "商业模式设计", "风险评估"],
                        "tools": ["SWOT分析", "波特五力模型", "商业画布", "财务分析"],
                        "model_config": {
                            "model": "gpt-4",
                            "temperature": 0.4,
                            "max_tokens": 2500
                        }
                    }
                ],
                
                "workflow": {
                    "steps": [
                        {
                            "name": "需求分析",
                            "description": "分析客户需求和业务现状",
                            "assignee": "战略分析师",
                            "inputs": ["客户需求", "业务资料"],
                            "outputs": ["需求分析报告", "战略建议"],
                            "dependencies": []
                        }
                    ]
                }
            },
            
            "metadata": {
                "version": "1.0.0",
                "created_by": "system",
                "created_at": datetime.now(timezone.utc).isoformat(),
                "template_type": "complete_deployable",
                "ready_to_deploy": True
            }
        }

    def _create_technical_support_template(self) -> Dict:
        """创建技术支持团队模板"""
        return {
            "id": "technical_support",
            "name": "技术支持团队",
            "description": "专业的技术支持团队，提供技术问题诊断和解决方案",
            "category": "technical",
            "difficulty": "intermediate",
            "use_case": "适用于技术问题诊断、故障排除和技术咨询",
            
            "team_config": {
                "team_name": "技术支持团队",
                "description": "专业的技术支持团队",
                "objective": "快速诊断和解决各类技术问题",
                "domain": "technical",
                "complexity": "intermediate",
                
                "team_members": [
                    {
                        "name": "技术专家",
                        "role": "technical_expert",
                        "description": "资深技术专家，擅长问题诊断和解决方案设计",
                        "system_prompt": """你是一位资深的技术专家，拥有丰富的技术问题解决经验。你擅长：
1. 快速诊断技术问题的根本原因
2. 设计有效的解决方案
3. 提供清晰的技术指导
4. 预防类似问题的再次发生
你的回答准确、实用，能够帮助用户快速解决技术难题。""",
                        "capabilities": ["问题诊断", "解决方案设计", "技术指导", "故障排除"],
                        "tools": ["诊断工具", "测试框架", "监控系统", "文档库"],
                        "model_config": {
                            "model": "gpt-4",
                            "temperature": 0.3,
                            "max_tokens": 2000
                        }
                    }
                ],
                
                "workflow": {
                    "steps": [
                        {
                            "name": "问题诊断",
                            "description": "分析技术问题，确定根本原因",
                            "assignee": "技术专家",
                            "inputs": ["问题描述", "错误信息", "系统环境"],
                            "outputs": ["诊断报告", "解决方案"],
                            "dependencies": []
                        }
                    ]
                }
            },
            
            "metadata": {
                "version": "1.0.0",
                "created_by": "system",
                "created_at": datetime.now(timezone.utc).isoformat(),
                "template_type": "complete_deployable",
                "ready_to_deploy": True
            }
        }

    def get_all_templates(self) -> List[Dict]:
        """获取所有可用的部署模板（仅元数据）"""
        return [
            {
                "id": template["id"],
                "name": template["name"],
                "description": template["description"],
                "category": template["category"],
                "difficulty": template["difficulty"],
                "use_case": template["use_case"],
                "ready_to_deploy": template["metadata"]["ready_to_deploy"]
            }
            for template in self.templates
        ]

    def get_template_by_id(self, template_id: str) -> Optional[Dict]:
        """根据ID获取完整的模板配置"""
        for template in self.templates:
            if template["id"] == template_id:
                return template
        return None

    def get_template_config(self, template_id: str) -> Optional[Dict]:
        """获取模板的团队配置（用于直接部署）"""
        template = self.get_template_by_id(template_id)
        if template:
            return template["team_config"]
        return None


# 全局实例
_complete_templates_service = None

def get_complete_templates_service() -> CompleteTemplatesService:
    """获取完整模板服务实例（单例模式）"""
    global _complete_templates_service
    if _complete_templates_service is None:
        _complete_templates_service = CompleteTemplatesService()
    return _complete_templates_service
