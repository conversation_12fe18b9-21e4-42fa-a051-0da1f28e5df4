"""
Intelligence service for AI-powered insights, analytics, and optimization recommendations.
"""

import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import func, desc, and_, or_, select

from app.models.agent import Agent
from app.models.user import User
from app.models.test_history import TestHistory
from app.models.intelligence import (
    AgentMetrics, SystemMetrics, AgentInsight, UserBehaviorPattern,
    OptimizationHistory, AlertRule, AlertEvent
)
from app.core.timezone_utils import utc_now, normalize_datetime_for_db

logger = logging.getLogger(__name__)


class IntelligenceService:
    """Service for AI-powered intelligence and analytics."""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def update_agent_metrics(self, agent_id: str, user_id: int, execution_data: Dict[str, Any]) -> AgentMetrics:
        """Update agent metrics with new execution data."""
        try:
            # Get or create metrics record
            stmt = select(AgentMetrics).where(
                and_(AgentMetrics.agent_id == agent_id, AgentMetrics.user_id == user_id)
            )
            result = await self.db.execute(stmt)
            metrics = result.scalar_one_or_none()

            if not metrics:
                metrics = AgentMetrics(agent_id=agent_id, user_id=user_id)
                self.db.add(metrics)

            # Update execution metrics
            metrics.execution_count += 1
            
            if execution_data.get('success', True):
                metrics.success_count += 1
            else:
                metrics.error_count += 1
                metrics.last_error_time = normalize_datetime_for_db(utc_now())

            # Update response time metrics
            response_time = execution_data.get('response_time', 0)
            if response_time > 0:
                metrics.total_response_time += response_time
                metrics.avg_response_time = metrics.total_response_time / metrics.execution_count
                
                # Update percentiles (simplified calculation)
                if response_time > metrics.p95_response_time:
                    metrics.p95_response_time = response_time * 0.95
                if response_time > metrics.p99_response_time:
                    metrics.p99_response_time = response_time * 0.99

            # Update cost metrics
            cost = execution_data.get('cost', 0)
            if cost > 0:
                metrics.total_cost += cost
                metrics.avg_cost_per_execution = metrics.total_cost / metrics.execution_count

            # Update token usage
            tokens = execution_data.get('tokens_used', 0)
            if tokens > 0:
                metrics.total_tokens_used += tokens

            # Update quality scores (if provided)
            if 'quality_score' in execution_data:
                metrics.output_quality_score = execution_data['quality_score']

            metrics.last_execution_time = normalize_datetime_for_db(utc_now())
            metrics.updated_at = normalize_datetime_for_db(utc_now())

            await self.db.commit()
            return metrics

        except Exception as e:
            logger.error(f"Error updating agent metrics: {str(e)}")
            await self.db.rollback()
            raise

    async def generate_agent_insights(self, agent_id: str, user_id: int) -> List[AgentInsight]:
        """Generate AI-powered insights for a specific agent."""
        try:
            insights = []

            # Get agent metrics
            stmt = select(AgentMetrics).where(
                and_(AgentMetrics.agent_id == agent_id, AgentMetrics.user_id == user_id)
            )
            result = await self.db.execute(stmt)
            metrics = result.scalar_one_or_none()

            if not metrics or metrics.execution_count == 0:
                return insights

            # Get agent details
            stmt = select(Agent).where(Agent.agent_id == agent_id)
            result = await self.db.execute(stmt)
            agent = result.scalar_one_or_none()
            if not agent:
                return insights

            # Performance insights
            success_rate = (metrics.success_count / metrics.execution_count) * 100
            if success_rate < 90:
                insights.append(self._create_insight(
                    agent_id=agent_id,
                    user_id=user_id,
                    insight_type="performance",
                    severity="high" if success_rate < 80 else "medium",
                    title=f"{agent.team_name}成功率偏低",
                    description=f"该Agent的成功率为{success_rate:.1f}%，低于预期的90%标准",
                    recommendation="检查Agent配置和输入数据质量，考虑优化提示词或增加错误处理",
                    confidence_score=85,
                    estimated_improvement=90 - success_rate,
                    metrics_data={"current_success_rate": success_rate, "target_success_rate": 90}
                ))

            # Response time insights
            if metrics.avg_response_time > 3000:  # 3 seconds
                insights.append(self._create_insight(
                    agent_id=agent_id,
                    user_id=user_id,
                    insight_type="performance",
                    severity="medium",
                    title=f"{agent.team_name}响应时间过长",
                    description=f"平均响应时间为{metrics.avg_response_time:.0f}ms，超过3秒阈值",
                    recommendation="考虑优化Agent工作流程、减少处理步骤或使用更快的AI模型",
                    confidence_score=90,
                    estimated_improvement=30,
                    metrics_data={"current_response_time": metrics.avg_response_time, "target_response_time": 2000}
                ))

            # Cost optimization insights
            if metrics.avg_cost_per_execution > 0.5:  # $0.50 per execution
                insights.append(self._create_insight(
                    agent_id=agent_id,
                    user_id=user_id,
                    insight_type="cost",
                    severity="medium",
                    title="成本优化机会",
                    description=f"每次执行成本为${metrics.avg_cost_per_execution:.3f}，存在优化空间",
                    recommendation="考虑使用更经济的AI模型处理简单任务，或优化提示词长度",
                    confidence_score=80,
                    estimated_savings=metrics.avg_cost_per_execution * 0.3 * metrics.execution_count,
                    metrics_data={"current_cost": metrics.avg_cost_per_execution, "potential_savings": 30}
                ))

            # Usage pattern insights
            if metrics.execution_count < 10 and (utc_now() - metrics.created_at).days > 7:
                insights.append(self._create_insight(
                    agent_id=agent_id,
                    user_id=user_id,
                    insight_type="usage",
                    severity="low",
                    title="Agent使用率偏低",
                    description=f"该Agent在过去{(utc_now() - metrics.created_at).days}天仅被使用{metrics.execution_count}次",
                    recommendation="考虑推广该Agent、添加使用示例或评估是否需要调整功能定位",
                    confidence_score=75,
                    estimated_improvement=200,  # 200% usage increase
                    metrics_data={"current_usage": metrics.execution_count, "days_active": (utc_now() - metrics.created_at).days}
                ))

            # Save insights to database
            for insight_data in insights:
                stmt = select(AgentInsight).where(
                    and_(
                        AgentInsight.agent_id == agent_id,
                        AgentInsight.user_id == user_id,
                        AgentInsight.title == insight_data['title'],
                        AgentInsight.is_dismissed == False
                    )
                )
                result = await self.db.execute(stmt)
                existing_insight = result.scalar_one_or_none()

                if not existing_insight:
                    insight = AgentInsight(**insight_data)
                    self.db.add(insight)

            await self.db.commit()

            # Return fresh insights from database
            stmt = select(AgentInsight).where(
                and_(
                    AgentInsight.agent_id == agent_id,
                    AgentInsight.user_id == user_id,
                    AgentInsight.is_dismissed == False
                )
            ).order_by(desc(AgentInsight.priority_score), desc(AgentInsight.created_at))
            result = await self.db.execute(stmt)
            return result.scalars().all()

        except Exception as e:
            logger.error(f"Error generating agent insights: {str(e)}")
            await self.db.rollback()
            return []

    async def generate_system_insights(self, user_id: int) -> List[AgentInsight]:
        """Generate system-wide insights for a user."""
        try:
            insights = []

            # Get user's agents and their metrics
            stmt = select(Agent).where(Agent.user_id == user_id)
            result = await self.db.execute(stmt)
            user_agents = result.scalars().all()
            if not user_agents:
                return insights

            # Calculate system-wide metrics
            total_executions = 0
            total_cost = 0
            avg_response_times = []
            success_rates = []

            for agent in user_agents:
                stmt = select(AgentMetrics).where(
                    and_(AgentMetrics.agent_id == agent.agent_id, AgentMetrics.user_id == user_id)
                )
                result = await self.db.execute(stmt)
                metrics = result.scalar_one_or_none()

                if metrics and metrics.execution_count > 0:
                    total_executions += metrics.execution_count
                    total_cost += metrics.total_cost
                    avg_response_times.append(metrics.avg_response_time)
                    success_rates.append((metrics.success_count / metrics.execution_count) * 100)

            if not avg_response_times:
                return insights

            # System performance insights
            system_avg_response = sum(avg_response_times) / len(avg_response_times)
            system_success_rate = sum(success_rates) / len(success_rates)

            if system_success_rate < 95:
                insights.append(self._create_insight(
                    agent_id=None,
                    user_id=user_id,
                    insight_type="performance",
                    severity="medium",
                    title="系统整体成功率需要改善",
                    description=f"您的Agent系统整体成功率为{system_success_rate:.1f}%，建议优化",
                    recommendation="检查表现较差的Agent，统一优化配置和错误处理机制",
                    confidence_score=88,
                    estimated_improvement=95 - system_success_rate,
                    metrics_data={"system_success_rate": system_success_rate, "total_agents": len(user_agents)}
                ))

            # Cost insights
            if total_cost > 100:  # $100 threshold
                insights.append(self._create_insight(
                    agent_id=None,
                    user_id=user_id,
                    insight_type="cost",
                    severity="medium",
                    title="总体成本较高",
                    description=f"您的Agent系统总成本为${total_cost:.2f}，存在优化空间",
                    recommendation="实施智能模型路由策略，将简单任务分配给更经济的模型",
                    confidence_score=85,
                    estimated_savings=total_cost * 0.25,
                    metrics_data={"total_cost": total_cost, "potential_savings_percent": 25}
                ))

            # Save and return insights
            for insight_data in insights:
                stmt = select(AgentInsight).where(
                    and_(
                        AgentInsight.agent_id.is_(None),
                        AgentInsight.user_id == user_id,
                        AgentInsight.title == insight_data['title'],
                        AgentInsight.is_dismissed == False
                    )
                )
                result = await self.db.execute(stmt)
                existing_insight = result.scalar_one_or_none()

                if not existing_insight:
                    insight = AgentInsight(**insight_data)
                    self.db.add(insight)

            await self.db.commit()

            stmt = select(AgentInsight).where(
                and_(
                    AgentInsight.agent_id.is_(None),
                    AgentInsight.user_id == user_id,
                    AgentInsight.is_dismissed == False
                )
            ).order_by(desc(AgentInsight.priority_score), desc(AgentInsight.created_at))
            result = await self.db.execute(stmt)
            return result.scalars().all()

        except Exception as e:
            logger.error(f"Error generating system insights: {str(e)}")
            await self.db.rollback()
            return []

    def _create_insight(self, agent_id: Optional[str], user_id: int, insight_type: str, 
                       severity: str, title: str, description: str, recommendation: str,
                       confidence_score: float, estimated_improvement: Optional[float] = None,
                       estimated_savings: Optional[float] = None, metrics_data: Optional[Dict] = None) -> Dict[str, Any]:
        """Create an insight dictionary."""
        
        # Calculate priority score based on severity and confidence
        severity_weights = {"low": 1, "medium": 2, "high": 3, "critical": 4}
        priority_score = severity_weights.get(severity, 1) * (confidence_score / 100) * 10

        return {
            "agent_id": agent_id,
            "user_id": user_id,
            "insight_type": insight_type,
            "severity": severity,
            "category": f"{insight_type}_{severity}",
            "title": title,
            "description": description,
            "recommendation": recommendation,
            "impact_description": f"预期改善: {estimated_improvement:.1f}%" if estimated_improvement else None,
            "confidence_score": confidence_score,
            "estimated_savings": estimated_savings,
            "estimated_improvement": estimated_improvement,
            "priority_score": int(priority_score),
            "is_actionable": True,
            "metrics_data": metrics_data or {},
            "analysis_data": {
                "generated_at": utc_now().isoformat(),
                "algorithm_version": "1.0",
                "data_points_analyzed": 1
            }
        }

    async def update_system_metrics(self) -> SystemMetrics:
        """Update system-wide metrics."""
        try:
            # Calculate system metrics
            stmt = select(func.count(Agent.id))
            result = await self.db.execute(stmt)
            total_agents = result.scalar()

            stmt = select(func.count(Agent.id)).where(Agent.status == "active")
            result = await self.db.execute(stmt)
            active_agents = result.scalar()

            # Get metrics for health calculation
            stmt = select(AgentMetrics)
            result = await self.db.execute(stmt)
            all_metrics = result.scalars().all()
            healthy_agents = 0
            warning_agents = 0
            critical_agents = 0
            
            total_executions = 0
            total_success = 0
            total_response_time = 0
            total_cost_24h = 0
            
            for metrics in all_metrics:
                if metrics.execution_count > 0:
                    success_rate = (metrics.success_count / metrics.execution_count) * 100
                    if success_rate >= 95 and metrics.avg_response_time < 3000:
                        healthy_agents += 1
                    elif success_rate >= 85:
                        warning_agents += 1
                    else:
                        critical_agents += 1
                    
                    total_executions += metrics.execution_count
                    total_success += metrics.success_count
                    total_response_time += metrics.avg_response_time
                    
                    # Calculate 24h cost (simplified)
                    if metrics.updated_at and (utc_now() - metrics.updated_at).days < 1:
                        total_cost_24h += metrics.total_cost * 0.1  # Estimate daily cost

            offline_agents = total_agents - active_agents
            
            # Calculate averages
            avg_response_time = total_response_time / len(all_metrics) if all_metrics else 0
            system_success_rate = (total_success / total_executions * 100) if total_executions > 0 else 100
            
            # Get user counts
            stmt = select(func.count(User.id))
            result = await self.db.execute(stmt)
            total_users = result.scalar()

            # Simplified active users calculation
            active_users_24h = total_users  # Simplified for now

            # Create or update system metrics
            system_metrics = SystemMetrics(
                total_agents=total_agents,
                active_agents=active_agents,
                healthy_agents=healthy_agents,
                warning_agents=warning_agents,
                critical_agents=critical_agents,
                offline_agents=offline_agents,
                total_executions=total_executions,
                total_users=total_users,
                active_users_24h=active_users_24h,
                active_users_7d=active_users_24h,  # Simplified
                active_users_30d=total_users,      # Simplified
                avg_response_time=avg_response_time,
                system_success_rate=system_success_rate,
                system_error_rate=100 - system_success_rate,
                system_load=min(100, (total_executions / 1000) * 100),  # Simplified load calculation
                total_cost_24h=total_cost_24h,
                total_cost_7d=total_cost_24h * 7,   # Simplified
                total_cost_30d=total_cost_24h * 30, # Simplified
                timestamp=normalize_datetime_for_db(utc_now())
            )

            self.db.add(system_metrics)
            await self.db.commit()

            return system_metrics

        except Exception as e:
            logger.error(f"Error updating system metrics: {str(e)}")
            await self.db.rollback()
            raise

    async def get_user_insights(self, user_id: int, limit: int = 20) -> List[AgentInsight]:
        """Get all insights for a user."""
        try:
            stmt = select(AgentInsight).where(
                and_(
                    AgentInsight.user_id == user_id,
                    AgentInsight.is_dismissed == False
                )
            ).order_by(
                desc(AgentInsight.priority_score),
                desc(AgentInsight.created_at)
            ).limit(limit)
            result = await self.db.execute(stmt)
            return result.scalars().all()

        except Exception as e:
            logger.error(f"Error getting user insights: {str(e)}")
            return []

    async def acknowledge_insight(self, insight_id: str, user_id: int) -> bool:
        """Acknowledge an insight."""
        try:
            stmt = select(AgentInsight).where(
                and_(
                    AgentInsight.insight_id == insight_id,
                    AgentInsight.user_id == user_id
                )
            )
            result = await self.db.execute(stmt)
            insight = result.scalar_one_or_none()

            if insight:
                insight.is_acknowledged = True
                insight.acknowledged_at = normalize_datetime_for_db(utc_now())
                await self.db.commit()
                return True
            return False

        except Exception as e:
            logger.error(f"Error acknowledging insight: {str(e)}")
            await self.db.rollback()
            return False

    async def dismiss_insight(self, insight_id: str, user_id: int) -> bool:
        """Dismiss an insight."""
        try:
            stmt = select(AgentInsight).where(
                and_(
                    AgentInsight.insight_id == insight_id,
                    AgentInsight.user_id == user_id
                )
            )
            result = await self.db.execute(stmt)
            insight = result.scalar_one_or_none()

            if insight:
                insight.is_dismissed = True
                insight.dismissed_at = normalize_datetime_for_db(utc_now())
                await self.db.commit()
                return True
            return False

        except Exception as e:
            logger.error(f"Error dismissing insight: {str(e)}")
            await self.db.rollback()
            return False
