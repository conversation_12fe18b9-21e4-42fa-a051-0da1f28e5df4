"""
WebSocket Service for Real-time Variable Tracking.

This service manages WebSocket connections and broadcasts real-time updates
for variable resolution during agent execution.
"""

import json
import asyncio
from typing import Dict, List, Any, Optional, Set
from datetime import datetime, timezone
from dataclasses import dataclass, asdict

from fastapi import WebSocket, WebSocketDisconnect
from app.core.logging import api_logger


@dataclass
class VariableUpdate:
    """Structure for variable update messages."""
    variable_name: str
    variable_value: str
    source_agent: str
    execution_step: int
    timestamp: str
    variable_type: str
    destination_agents: List[str]
    metadata: Dict[str, Any]


@dataclass
class ConnectionInfo:
    """Information about a WebSocket connection."""
    websocket: WebSocket
    user_id: int
    agent_id: str
    session_id: str
    connected_at: datetime


class WebSocketManager:
    """Manages WebSocket connections for real-time variable tracking."""
    
    def __init__(self):
        # Store active connections by session_id
        self.active_connections: Dict[str, ConnectionInfo] = {}
        # Store connections by agent_id for broadcasting
        self.agent_connections: Dict[str, Set[str]] = {}
        # Store connections by user_id for user-specific updates
        self.user_connections: Dict[int, Set[str]] = {}
        self.logger = api_logger
    
    async def connect(
        self, 
        websocket: WebSocket, 
        session_id: str, 
        user_id: int, 
        agent_id: str
    ) -> bool:
        """
        Accept a new WebSocket connection and register it.
        
        Args:
            websocket: The WebSocket connection
            session_id: Unique session identifier
            user_id: ID of the connected user
            agent_id: ID of the agent being tracked
            
        Returns:
            True if connection was successful, False otherwise
        """
        try:
            await websocket.accept()
            
            # Create connection info
            connection_info = ConnectionInfo(
                websocket=websocket,
                user_id=user_id,
                agent_id=agent_id,
                session_id=session_id,
                connected_at=datetime.now(timezone.utc)
            )
            
            # Store connection
            self.active_connections[session_id] = connection_info
            
            # Add to agent connections
            if agent_id not in self.agent_connections:
                self.agent_connections[agent_id] = set()
            self.agent_connections[agent_id].add(session_id)
            
            # Add to user connections
            if user_id not in self.user_connections:
                self.user_connections[user_id] = set()
            self.user_connections[user_id].add(session_id)
            
            self.logger.info(
                f"WebSocket connected: session={session_id}, user={user_id}, agent={agent_id}"
            )
            
            # Send connection confirmation
            await self.send_to_session(session_id, {
                "type": "connection_established",
                "session_id": session_id,
                "agent_id": agent_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "message": "WebSocket connection established for variable tracking"
            })
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to establish WebSocket connection: {str(e)}")
            return False
    
    async def disconnect(self, session_id: str):
        """
        Disconnect and clean up a WebSocket connection.
        
        Args:
            session_id: Session identifier to disconnect
        """
        if session_id in self.active_connections:
            connection_info = self.active_connections[session_id]
            
            # Remove from active connections
            del self.active_connections[session_id]
            
            # Remove from agent connections
            if connection_info.agent_id in self.agent_connections:
                self.agent_connections[connection_info.agent_id].discard(session_id)
                if not self.agent_connections[connection_info.agent_id]:
                    del self.agent_connections[connection_info.agent_id]
            
            # Remove from user connections
            if connection_info.user_id in self.user_connections:
                self.user_connections[connection_info.user_id].discard(session_id)
                if not self.user_connections[connection_info.user_id]:
                    del self.user_connections[connection_info.user_id]
            
            self.logger.info(
                f"WebSocket disconnected: session={session_id}, "
                f"user={connection_info.user_id}, agent={connection_info.agent_id}"
            )
    
    async def send_to_session(self, session_id: str, message: Dict[str, Any]) -> bool:
        """
        Send a message to a specific session.
        
        Args:
            session_id: Target session identifier
            message: Message to send
            
        Returns:
            True if message was sent successfully, False otherwise
        """
        if session_id not in self.active_connections:
            return False
        
        try:
            connection_info = self.active_connections[session_id]
            await connection_info.websocket.send_text(json.dumps(message))
            return True
        except Exception as e:
            self.logger.error(f"Failed to send message to session {session_id}: {str(e)}")
            # Clean up broken connection
            await self.disconnect(session_id)
            return False
    
    async def broadcast_to_agent(self, agent_id: str, message: Dict[str, Any]) -> int:
        """
        Broadcast a message to all connections tracking a specific agent.
        
        Args:
            agent_id: Target agent identifier
            message: Message to broadcast
            
        Returns:
            Number of successful sends
        """
        if agent_id not in self.agent_connections:
            return 0
        
        successful_sends = 0
        failed_sessions = []
        
        for session_id in self.agent_connections[agent_id].copy():
            if await self.send_to_session(session_id, message):
                successful_sends += 1
            else:
                failed_sessions.append(session_id)
        
        # Clean up failed sessions
        for session_id in failed_sessions:
            await self.disconnect(session_id)
        
        return successful_sends
    
    async def broadcast_variable_update(
        self, 
        agent_id: str, 
        variable_update: VariableUpdate
    ) -> int:
        """
        Broadcast a variable update to all relevant connections.
        
        Args:
            agent_id: Agent identifier
            variable_update: Variable update information
            
        Returns:
            Number of successful broadcasts
        """
        message = {
            "type": "variable_update",
            "agent_id": agent_id,
            "data": asdict(variable_update),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        return await self.broadcast_to_agent(agent_id, message)
    
    async def broadcast_execution_progress(
        self, 
        agent_id: str, 
        progress_data: Dict[str, Any]
    ) -> int:
        """
        Broadcast execution progress updates.
        
        Args:
            agent_id: Agent identifier
            progress_data: Progress information
            
        Returns:
            Number of successful broadcasts
        """
        message = {
            "type": "execution_progress",
            "agent_id": agent_id,
            "data": progress_data,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        return await self.broadcast_to_agent(agent_id, message)
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get statistics about current connections."""
        return {
            "total_connections": len(self.active_connections),
            "agents_being_tracked": len(self.agent_connections),
            "users_connected": len(self.user_connections),
            "connections_by_agent": {
                agent_id: len(sessions) 
                for agent_id, sessions in self.agent_connections.items()
            }
        }
    
    async def cleanup_stale_connections(self):
        """Clean up stale or broken connections."""
        stale_sessions = []
        
        for session_id, connection_info in self.active_connections.items():
            try:
                # Send a ping to check if connection is alive
                await connection_info.websocket.ping()
            except Exception:
                stale_sessions.append(session_id)
        
        # Clean up stale connections
        for session_id in stale_sessions:
            await self.disconnect(session_id)
        
        if stale_sessions:
            self.logger.info(f"Cleaned up {len(stale_sessions)} stale WebSocket connections")


# Global WebSocket manager instance
websocket_manager = WebSocketManager()


class VariableTracker:
    """Service for tracking and broadcasting variable updates during agent execution."""

    def __init__(self, websocket_manager: WebSocketManager):
        self.websocket_manager = websocket_manager
        self.logger = api_logger
        # Store variable data for database updates
        self._variable_storage = {}
    
    async def track_variable_resolution(
        self,
        agent_id: str,
        variable_name: str,
        variable_value: str,
        source_agent: str,
        execution_step: int,
        variable_type: str = "inter-agent",
        destination_agents: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        test_id: Optional[str] = None
    ):
        """
        Track and broadcast a variable resolution event.

        Args:
            agent_id: ID of the agent team
            variable_name: Name of the resolved variable
            variable_value: Value of the resolved variable
            source_agent: Agent that generated the variable
            execution_step: Current execution step
            variable_type: Type of variable (user-input, inter-agent, system, etc.)
            destination_agents: Agents that will consume this variable
            metadata: Additional metadata about the variable
            test_id: Optional test ID for database storage
        """
        try:
            variable_update = VariableUpdate(
                variable_name=variable_name,
                variable_value=variable_value,
                source_agent=source_agent,
                execution_step=execution_step,
                timestamp=datetime.now(timezone.utc).isoformat(),
                variable_type=variable_type,
                destination_agents=destination_agents or [],
                metadata=metadata or {}
            )

            # Broadcast the update via WebSocket
            sent_count = await self.websocket_manager.broadcast_variable_update(
                agent_id, variable_update
            )

            # Store variable data for database update if test_id is provided
            if test_id:
                await self._store_variable_data(test_id, variable_update, metadata)
                # Immediately write to database using the same data that was broadcasted
                await self._write_to_database_immediately(test_id, variable_update)

            self.logger.info(
                f"Variable update broadcasted: {variable_name} = {variable_value[:50]}... "
                f"(sent to {sent_count} connections)"
                + (f", stored and written to DB for test {test_id}" if test_id else "")
            )

        except Exception as e:
            self.logger.error(f"Failed to track variable resolution: {str(e)}")

    async def _store_variable_data(
        self,
        test_id: str,
        variable_update: VariableUpdate,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        Store variable data to database for test history.

        Args:
            test_id: Test execution identifier
            variable_update: Variable update data
            metadata: Additional metadata
        """
        try:
            # Initialize storage for this test if not exists
            if test_id not in self._variable_storage:
                self._variable_storage[test_id] = {
                    "context_placeholders_used": [],
                    "team_member_interactions": [],
                    "context_summary": {
                        "total_variables": 0,
                        "resolved_variables": 0,
                        "variable_types": {}
                    }
                }

            storage = self._variable_storage[test_id]

            # Add to context placeholders
            placeholder_data = {
                "variable_name": variable_update.variable_name,
                "variable_value": variable_update.variable_value,
                "source_agent": variable_update.source_agent,
                "execution_step": variable_update.execution_step,
                "timestamp": variable_update.timestamp,
                "variable_type": variable_update.variable_type,
                "destination_agents": variable_update.destination_agents
            }
            storage["context_placeholders_used"].append(placeholder_data)

            # Add to team member interactions if it's inter-agent communication
            if variable_update.variable_type == "inter-agent" and variable_update.destination_agents:
                interaction_data = {
                    "source_agent": variable_update.source_agent,
                    "destination_agents": variable_update.destination_agents,
                    "variable_name": variable_update.variable_name,
                    "execution_step": variable_update.execution_step,
                    "timestamp": variable_update.timestamp,
                    "data_type": "variable_resolution"
                }
                storage["team_member_interactions"].append(interaction_data)

            # Update summary
            storage["context_summary"]["total_variables"] = len(storage["context_placeholders_used"])
            storage["context_summary"]["resolved_variables"] = len([
                v for v in storage["context_placeholders_used"] if v["variable_value"]
            ])

            # Update variable type counts
            var_type = variable_update.variable_type
            if var_type not in storage["context_summary"]["variable_types"]:
                storage["context_summary"]["variable_types"][var_type] = 0
            storage["context_summary"]["variable_types"][var_type] += 1

            # Store data for later database update (will be handled by the calling function)
            self.logger.info(f"Variable data prepared for test {test_id}: {len(storage['context_placeholders_used'])} placeholders, {len(storage['team_member_interactions'])} interactions")

        except Exception as e:
            self.logger.error(f"Failed to store variable data for test {test_id}: {str(e)}")

    def get_stored_variables(self, test_id: str) -> Dict[str, Any]:
        """
        Get stored variable data for a test.

        Args:
            test_id: Test execution identifier

        Returns:
            Stored variable data or empty dict if not found
        """
        return self._variable_storage.get(test_id, {})

    async def _write_to_database_immediately(self, test_id: str, variable_update: VariableUpdate):
        """
        Immediately write variable data to database using the same data that was broadcasted.

        This ensures the database contains exactly the same data that the frontend receives.

        Args:
            test_id: Test execution identifier
            variable_update: The same VariableUpdate object that was broadcasted
        """
        try:
            from app.core.database import AsyncSessionLocal
            from app.core.timezone_utils import utc_now, normalize_datetime_for_db
            from sqlalchemy import text
            import json

            self.logger.info(f"Writing variable to database immediately: {variable_update.variable_name} for test {test_id}")

            # Create independent database session
            async with AsyncSessionLocal() as db:
                # Get existing data from database
                existing_query = "SELECT context_placeholders_used FROM test_history WHERE test_id = :test_id"
                result = await db.execute(text(existing_query), {"test_id": test_id})
                existing_record = result.fetchone()

                if not existing_record:
                    self.logger.error(f"No test record found for test_id {test_id}")
                    return

                # Parse existing placeholders
                existing_placeholders = []
                if existing_record.context_placeholders_used:
                    try:
                        existing_placeholders = json.loads(existing_record.context_placeholders_used)
                        if not isinstance(existing_placeholders, list):
                            existing_placeholders = []
                    except (json.JSONDecodeError, TypeError):
                        existing_placeholders = []

                # Convert VariableUpdate to database format (same as frontend receives)
                new_placeholder = {
                    "variable_name": variable_update.variable_name,
                    "variable_value": variable_update.variable_value,
                    "source_agent": variable_update.source_agent,
                    "execution_step": variable_update.execution_step,
                    "timestamp": variable_update.timestamp,
                    "variable_type": variable_update.variable_type,
                    "destination_agents": variable_update.destination_agents,
                    "metadata": variable_update.metadata
                }

                # Update or add the variable
                updated = False
                for i, existing in enumerate(existing_placeholders):
                    if (existing.get('variable_name') == variable_update.variable_name and
                        existing.get('source_agent') == variable_update.source_agent):
                        # Update existing variable with new resolved value
                        existing_placeholders[i] = new_placeholder
                        updated = True
                        self.logger.debug(f"Updated existing variable in DB: {variable_update.variable_name}")
                        break

                if not updated:
                    # Add new variable
                    existing_placeholders.append(new_placeholder)
                    self.logger.debug(f"Added new variable to DB: {variable_update.variable_name}")

                # Update database with the merged data
                update_data = {
                    "context_placeholders_used": json.dumps(existing_placeholders),
                    "updated_at": normalize_datetime_for_db(utc_now())
                }

                set_clauses = []
                params = {"test_id": test_id}

                for key, value in update_data.items():
                    set_clauses.append(f"{key} = :{key}")
                    params[key] = value

                query = f"UPDATE test_history SET {', '.join(set_clauses)} WHERE test_id = :test_id"
                result = await db.execute(text(query), params)
                await db.commit()

                self.logger.info(
                    f"Successfully wrote variable to database: {variable_update.variable_name} = {variable_update.variable_value[:50]}... "
                    f"for test {test_id} (affected rows: {result.rowcount})"
                )

        except Exception as e:
            self.logger.error(f"Failed to write variable to database immediately for test {test_id}: {str(e)}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")


# Global variable tracker instance
variable_tracker = VariableTracker(websocket_manager)
