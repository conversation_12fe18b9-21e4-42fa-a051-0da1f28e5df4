"""
AI团队生成分析和统计服务
"""

import json
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select, func

from app.core.logging import get_logger
from app.models.user import User
from app.models.base import BaseModel, UUIDMixin
from sqlmodel import Field, SQLModel, Column, JSON

logger = get_logger(__name__)


# AI团队生成记录模型
class AITeamGenerationRecord(BaseModel, UUIDMixin, table=True):
    """AI团队生成记录"""
    __tablename__ = "ai_team_generation_records"
    
    # 用户信息
    user_id: int = Field(foreign_key="users.id", nullable=False)
    
    # 生成参数
    user_description: str = Field(max_length=2000)
    provider: str = Field(max_length=50)
    model: str = Field(max_length=100)
    temperature: float = Field(default=0.7)
    max_tokens: int = Field(default=4000)
    
    # 生成结果
    success: bool = Field(default=False)
    generation_method: str = Field(max_length=50)  # ai_powered, template_fallback
    team_plan: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON))
    
    # 性能指标
    generation_time_seconds: Optional[float] = Field(default=None)
    tokens_used: Optional[int] = Field(default=None)
    estimated_cost: Optional[float] = Field(default=None)
    
    # 错误信息
    error_message: Optional[str] = Field(default=None, max_length=1000)
    error_type: Optional[str] = Field(default=None, max_length=100)
    
    # 质量评分
    quality_score: Optional[float] = Field(default=None, ge=0.0, le=10.0)
    user_rating: Optional[int] = Field(default=None, ge=1, le=5)
    user_feedback: Optional[str] = Field(default=None, max_length=1000)


class AITeamAnalyticsService:
    """AI团队生成分析服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def record_generation(
        self,
        user: User,
        user_description: str,
        provider: str,
        model: str,
        temperature: float,
        max_tokens: int,
        success: bool,
        generation_method: str,
        team_plan: Optional[Dict] = None,
        generation_time: Optional[float] = None,
        tokens_used: Optional[int] = None,
        estimated_cost: Optional[float] = None,
        error_message: Optional[str] = None,
        error_type: Optional[str] = None
    ) -> AITeamGenerationRecord:
        """记录AI团队生成"""
        
        # 计算质量评分
        quality_score = None
        if success and team_plan:
            quality_score = self._calculate_quality_score(team_plan)
        
        record = AITeamGenerationRecord(
            user_id=user.id,
            user_description=user_description,
            provider=provider,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            success=success,
            generation_method=generation_method,
            team_plan=team_plan,
            generation_time_seconds=generation_time,
            tokens_used=tokens_used,
            estimated_cost=estimated_cost,
            error_message=error_message,
            error_type=error_type,
            quality_score=quality_score
        )
        
        self.db.add(record)
        await self.db.commit()
        await self.db.refresh(record)
        
        logger.info(
            "AI team generation recorded",
            user_id=user.id,
            success=success,
            method=generation_method,
            quality_score=quality_score
        )
        
        return record
    
    def _calculate_quality_score(self, team_plan: Dict) -> float:
        """计算团队方案质量评分"""
        score = 0.0
        max_score = 10.0
        
        # 基础结构完整性 (3分)
        required_fields = ["team_name", "description", "team_members", "workflow"]
        structure_score = sum(2.0 for field in required_fields if field in team_plan and team_plan[field]) / len(required_fields) * 3.0
        score += structure_score
        
        # 团队成员质量 (3分)
        team_members = team_plan.get("team_members", [])
        if team_members:
            member_quality = 0.0
            for member in team_members:
                member_score = 0.0
                member_fields = ["name", "role", "description", "system_prompt", "capabilities"]
                member_score = sum(0.2 for field in member_fields if field in member and member[field])
                member_quality += member_score
            
            member_quality = (member_quality / len(team_members)) * 3.0
            score += member_quality
        
        # 工作流程质量 (2分)
        workflow = team_plan.get("workflow", {})
        steps = workflow.get("steps", [])
        if steps:
            workflow_score = min(len(steps) * 0.4, 2.0)  # 每个步骤0.4分，最多2分
            score += workflow_score
        
        # 描述详细程度 (2分)
        description = team_plan.get("description", "")
        if isinstance(description, str):
            if len(description) > 200:
                score += 2.0
            elif len(description) > 100:
                score += 1.5
            elif len(description) > 50:
                score += 1.0
            elif len(description) > 20:
                score += 0.5
        
        return min(score, max_score)
    
    async def get_user_statistics(
        self,
        user: User,
        days: int = 30
    ) -> Dict[str, Any]:
        """获取用户统计信息"""
        
        start_date = datetime.now(timezone.utc) - timedelta(days=days)
        
        # 基础统计
        total_query = select(func.count(AITeamGenerationRecord.id)).where(
            AITeamGenerationRecord.user_id == user.id,
            AITeamGenerationRecord.created_at >= start_date
        )
        total_result = await self.db.execute(total_query)
        total_generations = total_result.scalar() or 0
        
        # 成功统计
        success_query = select(func.count(AITeamGenerationRecord.id)).where(
            AITeamGenerationRecord.user_id == user.id,
            AITeamGenerationRecord.created_at >= start_date,
            AITeamGenerationRecord.success == True
        )
        success_result = await self.db.execute(success_query)
        successful_generations = success_result.scalar() or 0
        
        # 成本统计
        cost_query = select(func.sum(AITeamGenerationRecord.estimated_cost)).where(
            AITeamGenerationRecord.user_id == user.id,
            AITeamGenerationRecord.created_at >= start_date,
            AITeamGenerationRecord.estimated_cost.isnot(None)
        )
        cost_result = await self.db.execute(cost_query)
        total_cost = cost_result.scalar() or 0.0
        
        # 平均质量评分
        quality_query = select(func.avg(AITeamGenerationRecord.quality_score)).where(
            AITeamGenerationRecord.user_id == user.id,
            AITeamGenerationRecord.created_at >= start_date,
            AITeamGenerationRecord.quality_score.isnot(None)
        )
        quality_result = await self.db.execute(quality_query)
        avg_quality = quality_result.scalar() or 0.0
        
        # 生成方法分布
        method_query = select(
            AITeamGenerationRecord.generation_method,
            func.count(AITeamGenerationRecord.id)
        ).where(
            AITeamGenerationRecord.user_id == user.id,
            AITeamGenerationRecord.created_at >= start_date
        ).group_by(AITeamGenerationRecord.generation_method)
        
        method_result = await self.db.execute(method_query)
        method_distribution = {row[0]: row[1] for row in method_result.fetchall()}
        
        success_rate = (successful_generations / total_generations * 100) if total_generations > 0 else 0
        
        return {
            "period_days": days,
            "total_generations": total_generations,
            "successful_generations": successful_generations,
            "success_rate": round(success_rate, 2),
            "total_cost": round(total_cost, 4),
            "average_quality_score": round(avg_quality, 2),
            "generation_methods": method_distribution,
            "user_id": user.id
        }
    
    async def get_system_statistics(self, days: int = 30) -> Dict[str, Any]:
        """获取系统级统计信息"""
        
        start_date = datetime.now(timezone.utc) - timedelta(days=days)
        
        # 总体统计
        total_query = select(func.count(AITeamGenerationRecord.id)).where(
            AITeamGenerationRecord.created_at >= start_date
        )
        total_result = await self.db.execute(total_query)
        total_generations = total_result.scalar() or 0
        
        # 活跃用户数
        users_query = select(func.count(func.distinct(AITeamGenerationRecord.user_id))).where(
            AITeamGenerationRecord.created_at >= start_date
        )
        users_result = await self.db.execute(users_query)
        active_users = users_result.scalar() or 0
        
        # 提供商使用分布
        provider_query = select(
            AITeamGenerationRecord.provider,
            func.count(AITeamGenerationRecord.id)
        ).where(
            AITeamGenerationRecord.created_at >= start_date
        ).group_by(AITeamGenerationRecord.provider)
        
        provider_result = await self.db.execute(provider_query)
        provider_distribution = {row[0]: row[1] for row in provider_result.fetchall()}
        
        # 模型使用分布
        model_query = select(
            AITeamGenerationRecord.model,
            func.count(AITeamGenerationRecord.id)
        ).where(
            AITeamGenerationRecord.created_at >= start_date
        ).group_by(AITeamGenerationRecord.model)
        
        model_result = await self.db.execute(model_query)
        model_distribution = {row[0]: row[1] for row in model_result.fetchall()}
        
        return {
            "period_days": days,
            "total_generations": total_generations,
            "active_users": active_users,
            "avg_generations_per_user": round(total_generations / active_users, 2) if active_users > 0 else 0,
            "provider_distribution": provider_distribution,
            "model_distribution": model_distribution
        }
    
    async def update_user_feedback(
        self,
        record_id: str,
        user: User,
        rating: int,
        feedback: Optional[str] = None
    ) -> bool:
        """更新用户反馈"""
        
        query = select(AITeamGenerationRecord).where(
            AITeamGenerationRecord.uuid == record_id,
            AITeamGenerationRecord.user_id == user.id
        )
        result = await self.db.execute(query)
        record = result.scalar_one_or_none()
        
        if not record:
            return False
        
        record.user_rating = rating
        record.user_feedback = feedback
        
        await self.db.commit()
        
        logger.info(
            "User feedback updated",
            record_id=record_id,
            user_id=user.id,
            rating=rating
        )
        
        return True
