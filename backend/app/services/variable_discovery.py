"""
Variable Discovery Service for Agent Team Analysis.

This service analyzes agent team configurations to extract and categorize
variable placeholders used in agent prompts and workflows.
"""

import re
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass
from enum import Enum

from app.core.logging import api_logger


class VariableType(str, Enum):
    """Types of variables in agent workflows."""
    USER_INPUT = "user-input"
    INTER_AGENT = "inter-agent"
    SYSTEM = "system"
    OUTPUT = "output"
    CONTEXT = "context"


@dataclass
class VariableMetadata:
    """Metadata for a discovered variable."""
    name: str
    placeholder: str
    variable_type: VariableType
    source_agent: Optional[str]
    destination_agents: List[str]
    semantic_description: str
    workflow_step: Optional[int]
    dependencies: List[str]
    is_required: bool
    example_value: Optional[str] = None


class VariableDiscoveryService:
    """Service for discovering and analyzing variables in agent team configurations."""
    
    # Common variable patterns
    VARIABLE_PATTERN = re.compile(r'\{([^}]+)\}')
    USER_PATTERNS = ['user.', 'input.', 'request.', 'query.']
    SYSTEM_PATTERNS = ['system.', 'config.', 'settings.', 'env.']
    OUTPUT_PATTERNS = ['output.', 'result.', 'response.', 'final.']
    
    def __init__(self):
        self.logger = api_logger
    
    def discover_team_variables(self, team_plan: Dict[str, Any]) -> List[VariableMetadata]:
        """
        Discover all variables in a team configuration.
        
        Args:
            team_plan: The complete team plan configuration
            
        Returns:
            List of discovered variable metadata
        """
        try:
            variables = []
            team_members = team_plan.get("team_members", [])
            
            # Extract variables from each team member
            for i, member in enumerate(team_members):
                member_variables = self._extract_member_variables(member, i, team_members)
                variables.extend(member_variables)
            
            # Analyze variable dependencies and relationships
            variables = self._analyze_variable_relationships(variables, team_members)
            
            # Add common system and user variables
            variables.extend(self._get_common_variables(team_members))
            
            # Remove duplicates and sort
            variables = self._deduplicate_variables(variables)
            
            self.logger.info(
                f"Discovered {len(variables)} variables in team configuration",
                extra={"team_name": team_plan.get("team_name", "Unknown")}
            )
            
            return variables
            
        except Exception as e:
            self.logger.error(f"Error discovering team variables: {str(e)}")
            return []
    
    def _extract_member_variables(
        self, 
        member: Dict[str, Any], 
        member_index: int, 
        all_members: List[Dict[str, Any]]
    ) -> List[VariableMetadata]:
        """Extract variables from a single team member's configuration."""
        variables = []
        member_role = member.get("role", f"member_{member_index}")
        
        # Extract from system prompt
        system_prompt = member.get("system_prompt", "")
        if system_prompt:
            variables.extend(self._extract_variables_from_text(
                system_prompt, member_role, member_index, all_members
            ))
        
        # Extract from description
        description = member.get("description", "")
        if description:
            variables.extend(self._extract_variables_from_text(
                description, member_role, member_index, all_members
            ))
        
        # Extract from workflow steps if available
        workflow_steps = member.get("workflow_steps", [])
        for step_index, step in enumerate(workflow_steps):
            if isinstance(step, dict):
                step_text = step.get("description", "") + " " + step.get("prompt", "")
                variables.extend(self._extract_variables_from_text(
                    step_text, member_role, member_index, all_members, step_index
                ))
        
        return variables
    
    def _extract_variables_from_text(
        self, 
        text: str, 
        source_agent: str, 
        agent_index: int, 
        all_members: List[Dict[str, Any]],
        workflow_step: Optional[int] = None
    ) -> List[VariableMetadata]:
        """Extract variable placeholders from text content."""
        variables = []
        matches = self.VARIABLE_PATTERN.findall(text)
        
        for match in matches:
            variable_name = match.strip()
            placeholder = f"{{{variable_name}}}"
            
            # Determine variable type
            var_type = self._classify_variable_type(variable_name)
            
            # Determine destination agents
            destination_agents = self._get_destination_agents(
                variable_name, source_agent, agent_index, all_members
            )
            
            # Generate semantic description
            description = self._generate_semantic_description(variable_name, var_type, source_agent)
            
            # Determine dependencies
            dependencies = self._get_variable_dependencies(variable_name, var_type)
            
            variable = VariableMetadata(
                name=variable_name,
                placeholder=placeholder,
                variable_type=var_type,
                source_agent=source_agent if var_type != VariableType.USER_INPUT else None,
                destination_agents=destination_agents,
                semantic_description=description,
                workflow_step=workflow_step,
                dependencies=dependencies,
                is_required=True,  # Assume required by default
                example_value=self._generate_example_value(variable_name, var_type)
            )
            
            variables.append(variable)
        
        return variables
    
    def _classify_variable_type(self, variable_name: str) -> VariableType:
        """Classify the type of a variable based on its name."""
        variable_lower = variable_name.lower()
        
        # Check for user input patterns
        if any(pattern in variable_lower for pattern in self.USER_PATTERNS):
            return VariableType.USER_INPUT
        
        # Check for system patterns
        if any(pattern in variable_lower for pattern in self.SYSTEM_PATTERNS):
            return VariableType.SYSTEM
        
        # Check for output patterns
        if any(pattern in variable_lower for pattern in self.OUTPUT_PATTERNS):
            return VariableType.OUTPUT
        
        # Check for agent-specific patterns (agent.something)
        if '.' in variable_name:
            prefix = variable_name.split('.')[0].lower()
            # Common agent role names
            agent_roles = ['planner', 'analyst', 'executor', 'reviewer', 'coordinator', 'specialist']
            if any(role in prefix for role in agent_roles):
                return VariableType.INTER_AGENT
        
        # Default to inter-agent for most cases
        return VariableType.INTER_AGENT
    
    def _get_destination_agents(
        self, 
        variable_name: str, 
        source_agent: str, 
        agent_index: int, 
        all_members: List[Dict[str, Any]]
    ) -> List[str]:
        """Determine which agents will consume this variable."""
        destinations = []
        
        # For user input, typically goes to first agent
        if self._classify_variable_type(variable_name) == VariableType.USER_INPUT:
            if all_members:
                destinations.append(all_members[0].get("role", "first_agent"))
        
        # For inter-agent variables, goes to subsequent agents
        elif self._classify_variable_type(variable_name) == VariableType.INTER_AGENT:
            # Add agents that come after the source agent
            for i in range(agent_index + 1, len(all_members)):
                destinations.append(all_members[i].get("role", f"agent_{i}"))
        
        return destinations
    
    def _generate_semantic_description(self, variable_name: str, var_type: VariableType, source_agent: str) -> str:
        """Generate a human-readable description for the variable."""
        if var_type == VariableType.USER_INPUT:
            return f"用户输入的{variable_name.split('.')[-1] if '.' in variable_name else variable_name}"
        elif var_type == VariableType.SYSTEM:
            return f"系统配置变量: {variable_name}"
        elif var_type == VariableType.OUTPUT:
            return f"最终输出结果: {variable_name}"
        elif var_type == VariableType.INTER_AGENT:
            if '.' in variable_name:
                agent_part, var_part = variable_name.split('.', 1)
                return f"{agent_part}代理生成的{var_part}数据"
            return f"{source_agent}代理生成的变量数据"
        else:
            return f"上下文变量: {variable_name}"
    
    def _get_variable_dependencies(self, variable_name: str, var_type: VariableType) -> List[str]:
        """Determine what other variables this variable depends on."""
        dependencies = []
        
        # Most variables depend on user input
        if var_type != VariableType.USER_INPUT:
            dependencies.append("user.requirements")
        
        # Inter-agent variables may depend on previous agent outputs
        if var_type == VariableType.INTER_AGENT and '.' in variable_name:
            agent_part = variable_name.split('.')[0].lower()
            # Add common dependencies based on agent type
            if 'analyst' in agent_part:
                dependencies.append("planner.task_breakdown")
            elif 'executor' in agent_part:
                dependencies.extend(["planner.task_breakdown", "analyst.analysis_results"])
            elif 'reviewer' in agent_part:
                dependencies.append("executor.execution_output")
        
        return dependencies
    
    def _generate_example_value(self, variable_name: str, var_type: VariableType) -> Optional[str]:
        """Generate an example value for the variable."""
        if var_type == VariableType.USER_INPUT:
            return "用户输入的具体需求和任务描述..."
        elif var_type == VariableType.SYSTEM:
            return "系统配置值"
        elif var_type == VariableType.INTER_AGENT:
            if 'analysis' in variable_name.lower():
                return "详细的分析结果和建议..."
            elif 'plan' in variable_name.lower():
                return "任务分解和执行计划..."
            elif 'output' in variable_name.lower():
                return "执行结果和产出物..."
            return "代理间传递的数据..."
        return None
    
    def _analyze_variable_relationships(
        self, 
        variables: List[VariableMetadata], 
        team_members: List[Dict[str, Any]]
    ) -> List[VariableMetadata]:
        """Analyze and enhance variable relationships."""
        # Create a map of variables by name for easy lookup
        var_map = {var.name: var for var in variables}
        
        # Update destination agents based on actual usage
        for variable in variables:
            if variable.variable_type == VariableType.INTER_AGENT:
                # Find which agents actually reference this variable
                actual_destinations = []
                for member in team_members:
                    member_text = (member.get("system_prompt", "") + " " + 
                                 member.get("description", "")).lower()
                    if variable.placeholder.lower() in member_text:
                        actual_destinations.append(member.get("role", "unknown"))
                
                if actual_destinations:
                    variable.destination_agents = actual_destinations
        
        return variables
    
    def _get_common_variables(self, team_members: List[Dict[str, Any]]) -> List[VariableMetadata]:
        """Add common variables that should always be present."""
        common_vars = []
        
        # User requirements (always present)
        common_vars.append(VariableMetadata(
            name="user.requirements",
            placeholder="{user.requirements}",
            variable_type=VariableType.USER_INPUT,
            source_agent=None,
            destination_agents=[team_members[0].get("role", "first_agent")] if team_members else [],
            semantic_description="用户输入的需求和任务描述",
            workflow_step=0,
            dependencies=[],
            is_required=True,
            example_value="用户的具体需求和任务描述..."
        ))
        
        # System execution ID
        common_vars.append(VariableMetadata(
            name="system.execution_id",
            placeholder="{system.execution_id}",
            variable_type=VariableType.SYSTEM,
            source_agent=None,
            destination_agents=[],
            semantic_description="系统生成的执行标识符",
            workflow_step=0,
            dependencies=[],
            is_required=False,
            example_value="exec_1234567890"
        ))
        
        return common_vars
    
    def _deduplicate_variables(self, variables: List[VariableMetadata]) -> List[VariableMetadata]:
        """Remove duplicate variables and merge information."""
        seen = {}
        result = []
        
        for var in variables:
            if var.name in seen:
                # Merge destination agents
                existing = seen[var.name]
                existing.destination_agents = list(set(existing.destination_agents + var.destination_agents))
                # Keep the more specific source agent
                if var.source_agent and not existing.source_agent:
                    existing.source_agent = var.source_agent
            else:
                seen[var.name] = var
                result.append(var)
        
        # Sort by workflow step and variable type
        result.sort(key=lambda x: (x.workflow_step or 0, x.variable_type.value, x.name))
        
        return result
