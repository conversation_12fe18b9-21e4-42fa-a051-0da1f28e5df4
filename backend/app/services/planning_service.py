"""
Planning service for AI agent team generation.
"""

import json
import uuid
from datetime import datetime
from typing import Any, Dict, Optional

from app.core.exceptions import PlanningError
from app.core.logging import get_logger
from app.services.user_ai_service import UserAIService

logger = get_logger("planning_service")


class PlanningService:
    """Service for planning AI agent teams."""

    def __init__(self, user_ai_service: UserAIService):
        self.user_ai_service = user_ai_service
        self.planning_prompt_template = self._get_planning_prompt_template()
        self.validation_prompt_template = self._get_validation_prompt_template()

    async def create_team_plan(
        self,
        user_description: str,
        requirements: Optional[Dict[str, Any]] = None,
        provider: Optional[str] = None,
        model: str = "gpt-4",
        temperature: float = 0.7,
    ) -> Dict[str, Any]:
        """Create a team plan based on user description.

        Args:
            user_description: Description of what the user wants
            requirements: Optional additional requirements
            provider: Optional specific provider to use (defaults to user's first available)
            model: Model to use for planning
            temperature: Temperature for text generation
        """
        try:
            logger.info("Starting team planning", description=user_description[:100])
            
            # Prepare planning prompt
            planning_context = {
                "user_description": user_description,
                "requirements": requirements or {},
                "timestamp": datetime.utcnow().isoformat(),
            }
            
            planning_prompt = self._render_planning_prompt(planning_context)

            # Generate team plan using user's AI service
            response = await self.user_ai_service.generate_structured_output(
                prompt=planning_prompt,
                schema=self._get_team_plan_schema(),
                system_prompt=self._get_planning_system_prompt(),
                provider=provider,
                model=model,
                temperature=temperature,
            )

            # Validate and enhance the plan
            validated_plan = await self._validate_and_enhance_plan(response)
            
            logger.info("Team planning completed", team_name=validated_plan.get("team_name"))
            return validated_plan
            
        except Exception as e:
            logger.error(f"Team planning failed: {str(e)}")
            raise PlanningError(f"Failed to create team plan: {str(e)}", stage="planning")
    
    async def _validate_and_enhance_plan(
        self,
        plan: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate and enhance the generated plan."""
        try:
            # Basic validation
            required_fields = ["team_name", "description", "objective", "team_members", "workflow"]
            for field in required_fields:
                if field not in plan:
                    raise PlanningError(f"Missing required field: {field}", stage="validation")
            
            # Enhance team members with detailed prompts
            enhanced_members = []
            for member in plan["team_members"]:
                enhanced_member = await self._enhance_team_member(member, plan)
                enhanced_members.append(enhanced_member)

            plan["team_members"] = enhanced_members
            
            # Add metadata
            plan["plan_id"] = str(uuid.uuid4())
            plan["created_at"] = datetime.utcnow().isoformat()
            plan["validation_score"] = await self._calculate_validation_score(plan)
            
            return plan
            
        except Exception as e:
            logger.error(f"Plan validation failed: {str(e)}")
            raise PlanningError(f"Failed to validate plan: {str(e)}", stage="validation")
    
    async def _enhance_team_member(
        self,
        member: Dict[str, Any],
        team_plan: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Enhance team member with detailed prompts and configuration."""
        try:
            enhancement_prompt = f"""
Based on the team plan and member role, create detailed prompts and configuration:

Team: {team_plan['team_name']}
Objective: {team_plan['objective']}

Member: {member['name']} - {member['role']}
Description: {member['description']}

Please provide:
1. A detailed system prompt that defines the member's role, expertise, and behavior
2. A role-specific prompt that guides their interactions
3. Specific capabilities they should have
4. Tools they might need
5. Optimal AI model configuration

Respond in JSON format with these fields:
- system_prompt: string
- role_prompt: string  
- capabilities: array of strings
- tools: array of strings
- model_config: object with model, temperature, max_tokens
"""
            
            enhancement = await self.user_ai_service.generate_structured_output(
                prompt=enhancement_prompt,
                schema={
                    "type": "object",
                    "properties": {
                        "system_prompt": {"type": "string"},
                        "role_prompt": {"type": "string"},
                        "capabilities": {"type": "array", "items": {"type": "string"}},
                        "tools": {"type": "array", "items": {"type": "string"}},
                        "model_config": {
                            "type": "object",
                            "properties": {
                                "model": {"type": "string"},
                                "temperature": {"type": "number"},
                                "max_tokens": {"type": "integer"}
                            }
                        }
                    }
                }
            )
            
            # Merge enhancement with original member data
            enhanced_member = {**member, **enhancement}
            
            return enhanced_member
            
        except Exception as e:
            logger.error(f"Member enhancement failed: {str(e)}")
            # Return original member if enhancement fails
            return member
    
    async def _calculate_validation_score(self, plan: Dict[str, Any]) -> float:
        """Calculate validation score for the plan."""
        score = 0.0
        max_score = 100.0
        
        # Check team structure (30 points)
        if len(plan.get("team_members", [])) >= 2:
            score += 15
        if len(plan.get("team_members", [])) <= 6:
            score += 15
        
        # Check workflow completeness (25 points)
        workflow = plan.get("workflow", {})
        if "steps" in workflow and len(workflow["steps"]) >= 3:
            score += 25
        
        # Check member role diversity (20 points)
        roles = [member.get("role", "") for member in plan.get("team_members", [])]
        unique_roles = len(set(roles))
        if unique_roles >= 2:
            score += 20
        
        # Check prompt quality (25 points)
        members_with_prompts = sum(
            1 for member in plan.get("team_members", [])
            if member.get("system_prompt") and len(member.get("system_prompt", "")) > 50
        )
        if members_with_prompts == len(plan.get("team_members", [])):
            score += 25
        
        return min(score, max_score)
    
    def _get_planning_system_prompt(self) -> str:
        """Get system prompt for planning."""
        return """You are an expert AI team architect specializing in designing multi-agent systems. Your role is to analyze user requirements and create optimal team structures with specialized AI agents.

Key principles:
1. Create diverse teams with complementary skills
2. Define clear roles and responsibilities
3. Design efficient workflows
4. Ensure proper coordination between team members
5. Focus on practical, achievable objectives

Always consider:
- Team size (2-6 members optimal)
- Role specialization and expertise
- Communication patterns
- Task dependencies
- Quality assurance processes"""
    
    def _render_planning_prompt(self, context: Dict[str, Any]) -> str:
        """Render planning prompt with context."""
        return f"""
Please analyze the following user request and create a comprehensive AI agent team plan:

User Description: {context['user_description']}

Additional Requirements: {json.dumps(context.get('requirements', {}), indent=2)}

Create a team plan that includes:

1. **Team Overview**
   - Compelling team name
   - Clear description of the team's purpose
   - Specific, measurable objective

2. **Team Members** (2-6 members)
   - Name and role for each member
   - Detailed description of their expertise
   - How they contribute to the team objective

3. **Workflow Design**
   - Step-by-step process
   - Clear input/output for each step
   - Coordination mechanisms
   - Quality assurance steps

4. **Success Metrics**
   - How to measure team performance
   - Key performance indicators

Please ensure the team is practical, efficient, and well-coordinated.
"""
    
    def _get_team_plan_schema(self) -> Dict[str, Any]:
        """Get JSON schema for team plan."""
        return {
            "type": "object",
            "properties": {
                "team_name": {"type": "string"},
                "description": {"type": "string"},
                "objective": {"type": "string"},
                "team_members": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "name": {"type": "string"},
                            "role": {"type": "string"},
                            "description": {"type": "string"},
                            "expertise": {"type": "array", "items": {"type": "string"}},
                            "responsibilities": {"type": "array", "items": {"type": "string"}}
                        },
                        "required": ["name", "role", "description"]
                    }
                },
                "workflow": {
                    "type": "object",
                    "properties": {
                        "steps": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "name": {"type": "string"},
                                    "description": {"type": "string"},
                                    "assignee": {"type": "string"},
                                    "inputs": {"type": "array", "items": {"type": "string"}},
                                    "outputs": {"type": "array", "items": {"type": "string"}},
                                    "dependencies": {"type": "array", "items": {"type": "string"}}
                                },
                                "required": ["name", "description", "assignee"]
                            }
                        },
                        "coordination": {"type": "string"},
                        "quality_assurance": {"type": "string"}
                    },
                    "required": ["steps"]
                },
                "success_metrics": {
                    "type": "array",
                    "items": {"type": "string"}
                },
                "estimated_completion_time": {"type": "string"},
                "complexity_level": {"type": "string", "enum": ["low", "medium", "high"]}
            },
            "required": ["team_name", "description", "objective", "team_members", "workflow"]
        }
    
    def _get_planning_prompt_template(self) -> str:
        """Get planning prompt template."""
        return "Planning prompt template placeholder"
    
    def _get_validation_prompt_template(self) -> str:
        """Get validation prompt template."""
        return "Validation prompt template placeholder"


def get_planning_service(user_ai_service: Optional[UserAIService] = None) -> PlanningService:
    """Get planning service instance.

    Args:
        user_ai_service: Optional user's AI service instance

    Returns:
        PlanningService configured for the user

    Note:
        This is a temporary implementation. In the future, all planning operations
        should require user authentication and use user-specific API keys.
    """
    if user_ai_service is None:
        # Temporary fallback - this should be removed once all endpoints are updated
        # to require user authentication
        raise PlanningError("Planning service requires user authentication", stage="initialization")

    return PlanningService(user_ai_service)
