"""
AI规划师核心组件 - 负责分析用户需求并生成团队规划
"""

import json
from typing import Dict, Optional
from datetime import datetime, timezone

from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select

from app.core.logging import get_logger
from app.models.settings import SystemSettings
from app.models.user import User
from app.services.ai_providers import create_ai_provider, AIProviderError
from app.services.user_ai_service import UserAIService

logger = get_logger(__name__)


class AIPlanner:
    """AI规划师 - 负责分析用户需求并生成团队规划"""

    def __init__(self, db: Optional[AsyncSession] = None, user: Optional[User] = None):
        self.db = db
        self.user = user
        self.system_prompt = self._get_system_prompt()
        self._ai_generation_prompt = self._get_ai_generation_prompt()
    
    def _get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """你是一个专业的AI团队规划师，擅长分析用户需求并设计高效的AI Agent团队。

你的任务是：
1. 深入理解用户的需求和目标
2. 设计合适的团队结构和成员角色
3. 定义清晰的工作流程和协作方式
4. 生成可执行的团队规划方案

请始终以JSON格式输出规划结果，确保结构清晰、逻辑合理。"""

    def _get_ai_generation_prompt(self) -> str:
        """获取AI生成团队的提示词"""
        return """你是一个专业的AI团队规划师。根据用户的需求描述，生成一个完整的AI Agent团队方案。

请严格按照以下JSON格式输出：

{
  "team_name": "团队名称",
  "description": "团队描述",
  "objective": "团队目标",
  "domain": "领域类型(technical/creative/consulting/investigation/general)",
  "complexity": "复杂度(beginner/intermediate/advanced)",
  "team_members": [
    {
      "name": "成员名称",
      "role": "角色标识",
      "description": "成员描述",
      "system_prompt": "详细的系统提示词，定义该成员的角色、能力和行为方式。使用语义化占位符来引用其他团队成员的输出，格式为{agent_role.output_type}",
      "capabilities": ["能力1", "能力2", "能力3"],
      "tools": ["工具1", "工具2", "工具3"],
      "model": "gpt-4",
      "temperature": 0.7,
      "max_tokens": 2000,
      "context_placeholders": [
        {
          "placeholder_name": "{agent_role.output_type}",
          "source_agent_role": "前置团队成员的role字段",
          "semantic_description": "具体描述这个输出的语义含义和用途",
          "source_step": "生成此输出的工作流步骤名称"
        }
      ]
    }
  ],
  "workflow": {
    "steps": [
      {
        "name": "步骤名称",
        "description": "步骤描述",
        "assignee": "负责成员的role字段（英文标识符）或'team_collaboration'（团队协作）",
        "inputs": ["输入1", "输入2"],
        "outputs": ["输出1", "输出2"],
        "context_dependencies": ["前置步骤的输出标识"]
      }
    ]
  }
}

语义化占位符命名规范：
- 格式：{agent_role.output_type}
- agent_role：对应团队成员的role字段
- output_type：描述输出的语义类型

示例1 - 数据分析团队：
```json
{
  "team_members": [
    {
      "name": "数据收集专家",
      "role": "data_collector",
      "system_prompt": "你是数据收集专家，负责收集和验证数据。基于用户需求 {user.requirements} 收集相关数据。",
      "context_placeholders": [
        {
          "placeholder_name": "{user.requirements}",
          "source_agent_role": "user",
          "semantic_description": "用户提出的具体数据需求和分析目标",
          "source_step": "user_input"
        }
      ]
    },
    {
      "name": "数据分析师",
      "role": "data_analyst",
      "system_prompt": "你是数据分析师，分析 {data_collector.validated_dataset} 并生成洞察。参考 {user.requirements} 确保分析符合需求。",
      "context_placeholders": [
        {
          "placeholder_name": "{data_collector.validated_dataset}",
          "source_agent_role": "data_collector",
          "semantic_description": "经过清洗和验证的数据集，可直接用于分析",
          "source_step": "Data Collection"
        },
        {
          "placeholder_name": "{user.requirements}",
          "source_agent_role": "user",
          "semantic_description": "用户的原始分析需求",
          "source_step": "user_input"
        }
      ]
    }
  ]
}
```

示例2 - 内容创作团队：
```json
{
  "team_members": [
    {
      "name": "内容策划师",
      "role": "content_strategist",
      "system_prompt": "你是内容策划师，基于 {user.content_brief} 制定内容策略和大纲。",
      "context_placeholders": [
        {
          "placeholder_name": "{user.content_brief}",
          "source_agent_role": "user",
          "semantic_description": "用户提供的内容创作简介和要求",
          "source_step": "user_input"
        }
      ]
    },
    {
      "name": "内容编辑",
      "role": "content_editor",
      "system_prompt": "你是内容编辑，根据 {content_strategist.content_outline} 创作内容，确保符合 {user.content_brief} 的要求。",
      "context_placeholders": [
        {
          "placeholder_name": "{content_strategist.content_outline}",
          "source_agent_role": "content_strategist",
          "semantic_description": "详细的内容大纲和创作指导",
          "source_step": "Content Planning"
        },
        {
          "placeholder_name": "{user.content_brief}",
          "source_agent_role": "user",
          "semantic_description": "用户的内容创作需求",
          "source_step": "user_input"
        }
      ]
    }
  ]
}
```

要求：
1. 团队成员数量：2-5人
2. 每个成员都要有独特的角色和能力
3. 工作流程要逻辑清晰，步骤之间有明确的输入输出关系
4. system_prompt要详细具体，使用语义化占位符格式 {agent_role.output_type}
5. context_placeholders必须包含source_agent_role和semantic_description字段
6. 占位符名称要具体描述输出的语义含义，避免使用generic名称
7. workflow步骤要包含context_dependencies来明确依赖关系
8. **重要：workflow步骤中的assignee字段必须使用团队成员的role字段（英文标识符），不要使用name字段**
9. 确保JSON格式正确，可以被解析

用户需求："""





    async def get_system_settings(self) -> Optional[SystemSettings]:
        """获取系统设置"""
        if not self.db:
            return None

        try:
            statement = select(SystemSettings).where(SystemSettings.is_active == True)
            result = await self.db.execute(statement)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Failed to get system settings: {str(e)}")
            return None

    async def get_system_api_key_for_team_generation(self) -> Optional[str]:
        """获取系统级团队生成API密钥"""
        if not self.db:
            return None

        try:
            settings = await self.get_system_settings()
            if not settings or not settings.team_generation_api_key:
                return None

            # Decrypt the system API key
            from app.models.settings import decrypt_api_key
            return decrypt_api_key(settings.team_generation_api_key)

        except Exception as e:
            logger.error(f"Failed to get system API key for team generation: {str(e)}")
            return None

    async def get_user_api_key(self, provider: str) -> Optional[str]:
        """获取用户的API密钥（用于Agent执行，保留向后兼容性）"""
        if not self.db or not self.user:
            return None

        try:
            user_ai_service = UserAIService(self.db, self.user)
            api_keys = await user_ai_service.get_user_api_keys(provider=provider, active_only=True)

            if api_keys:
                # Return the first active API key for the provider
                from app.models.settings import decrypt_api_key
                return decrypt_api_key(api_keys[0].encrypted_key)

            return None
        except Exception as e:
            logger.error(f"Failed to get user API key for {provider}: {str(e)}")
            return None

    async def generate_ai_powered_team(self, user_description: str, record_analytics: bool = True, model_override: Optional[str] = None, temperature_override: Optional[float] = None) -> Dict:
        """使用AI生成团队规划"""
        logger.info(f"开始AI生成团队规划: {user_description[:100]}...")

        start_time = datetime.now(timezone.utc)
        analytics_data = {
            "user_description": user_description,
            "success": False,
            "generation_method": "ai_powered",
            "error_message": None,
            "error_type": None
        }

        try:
            # Get system settings
            settings = await self.get_system_settings()
            if not settings or not settings.enable_ai_team_generation:
                analytics_data["error_message"] = "AI team generation is not enabled"
                analytics_data["error_type"] = "configuration_error"
                raise ValueError("AI team generation is not enabled")

            analytics_data.update({
                "provider": settings.team_generation_provider,
                "model": settings.team_generation_model,
                "temperature": settings.team_generation_temperature,
                "max_tokens": settings.team_generation_max_tokens
            })

            # Get system API key for team generation
            api_key = await self.get_system_api_key_for_team_generation()
            if not api_key:
                analytics_data["error_message"] = f"No system API key configured for team generation with provider: {settings.team_generation_provider}"
                analytics_data["error_type"] = "configuration_error"
                raise ValueError(f"No system API key configured for team generation with provider: {settings.team_generation_provider}")

            # Use overrides if provided, otherwise use system settings
            model = model_override or settings.team_generation_model
            temperature = temperature_override if temperature_override is not None else settings.team_generation_temperature

            # Create AI provider instance with custom base URL if configured
            logger.info(f"Creating AI provider with base_url: {settings.team_generation_base_url}, model: {model}, temperature: {temperature}")
            ai_provider = create_ai_provider(
                provider_name=settings.team_generation_provider,
                api_key=api_key,
                base_url=settings.team_generation_base_url,  # Use custom base URL from settings
                model=model,
                temperature=temperature
            )

            # Generate team plan using AI
            prompt = f"{self._ai_generation_prompt}\n\n{user_description}"

            response = await ai_provider.generate_text(
                prompt=prompt,
                system_prompt=self.system_prompt,
                temperature=temperature,
                max_tokens=settings.team_generation_max_tokens
            )

            # Parse JSON response - handle markdown code blocks
            try:
                # Clean response by removing markdown code blocks if present
                cleaned_response = response.strip()
                if cleaned_response.startswith("```json"):
                    cleaned_response = cleaned_response[7:]  # Remove ```json
                if cleaned_response.endswith("```"):
                    cleaned_response = cleaned_response[:-3]  # Remove ```
                cleaned_response = cleaned_response.strip()

                team_plan = json.loads(cleaned_response)
            except json.JSONDecodeError as e:
                analytics_data["error_message"] = f"Failed to parse AI response as JSON: {str(e)}"
                analytics_data["error_type"] = "parsing_error"
                logger.error(f"Failed to parse AI response as JSON: {str(e)}")
                logger.error(f"AI response: {response}")
                raise ValueError("AI generated invalid JSON response")

            # Validate and enhance the generated plan
            team_plan = self._enhance_ai_generated_plan(team_plan, user_description, model, temperature)

            # Calculate generation time and estimate cost
            end_time = datetime.now(timezone.utc)
            generation_time = (end_time - start_time).total_seconds()

            # Estimate tokens used (rough approximation)
            estimated_tokens = len(prompt.split()) + len(response.split())
            estimated_cost = self._estimate_cost(settings.team_generation_provider, settings.team_generation_model, estimated_tokens)

            analytics_data.update({
                "success": True,
                "team_plan": team_plan,
                "generation_time": generation_time,
                "tokens_used": estimated_tokens,
                "estimated_cost": estimated_cost
            })

            logger.info("AI团队规划生成完成")
            return team_plan

        except AIProviderError as e:
            analytics_data["error_message"] = str(e)
            analytics_data["error_type"] = "ai_provider_error"
            logger.error(f"AI provider error: {str(e)}")
            raise ValueError(f"AI generation failed: {str(e)}")
        except Exception as e:
            if not analytics_data.get("error_message"):
                analytics_data["error_message"] = str(e)
                analytics_data["error_type"] = "unexpected_error"
            logger.error(f"Unexpected error in AI team generation: {str(e)}")
            raise ValueError(f"Team generation failed: {str(e)}")
        finally:
            # Record analytics if enabled and we have user context
            if record_analytics and self.db and self.user:
                try:
                    from app.services.ai_team_analytics import AITeamAnalyticsService
                    analytics_service = AITeamAnalyticsService(self.db)
                    await analytics_service.record_generation(
                        user=self.user,
                        user_description=analytics_data["user_description"],
                        provider=analytics_data.get("provider", "unknown"),
                        model=analytics_data.get("model", "unknown"),
                        temperature=analytics_data.get("temperature", 0.7),
                        max_tokens=analytics_data.get("max_tokens", 4000),
                        success=analytics_data["success"],
                        generation_method=analytics_data["generation_method"],
                        team_plan=analytics_data.get("team_plan"),
                        generation_time=analytics_data.get("generation_time"),
                        tokens_used=analytics_data.get("tokens_used"),
                        estimated_cost=analytics_data.get("estimated_cost"),
                        error_message=analytics_data.get("error_message"),
                        error_type=analytics_data.get("error_type")
                    )
                except Exception as analytics_error:
                    logger.warning(f"Failed to record analytics: {str(analytics_error)}")

    def _estimate_cost(self, provider: str, model: str, tokens: int) -> float:
        """估算生成成本"""
        # 简化的成本估算，实际应该根据具体的定价模型
        cost_per_1k_tokens = {
            "openai": {
                "gpt-4": 0.03,
                "gpt-4-turbo": 0.01,
                "gpt-3.5-turbo": 0.002
            },
            "anthropic": {
                "claude-3-opus": 0.015,
                "claude-3-sonnet": 0.003,
                "claude-3-haiku": 0.00025
            },
            "google": {
                "gemini-pro": 0.0005,
                "gemini-pro-vision": 0.0025
            }
        }

        provider_costs = cost_per_1k_tokens.get(provider.lower(), {})
        model_cost = provider_costs.get(model.lower(), 0.001)  # 默认成本

        return (tokens / 1000) * model_cost

    def _enhance_ai_generated_plan(self, team_plan: Dict, user_description: str, model: str, temperature: float) -> Dict:
        """增强AI生成的团队规划"""
        # Add metadata
        team_plan["created_at"] = datetime.now().isoformat()
        team_plan["generation_method"] = "ai_powered"
        team_plan["user_request"] = user_description

        # Add AI model configuration to the team plan
        team_plan["ai_model_config"] = {
            "provider": "openai",  # This could be made configurable
            "model": model,
            "temperature": temperature,
            "max_tokens": 2000  # Default value, could be made configurable
        }

        # Ensure required fields exist
        if "team_name" not in team_plan:
            team_plan["team_name"] = "AI Generated Team"

        if "description" not in team_plan:
            team_plan["description"] = f"AI generated team for: {user_description[:100]}"

        if "objective" not in team_plan:
            team_plan["objective"] = f"Address user requirements: {user_description}"

        # Validate team members
        if "team_members" not in team_plan or not team_plan["team_members"]:
            raise ValueError("AI generated plan must include team members")

        # Ensure each team member has required fields
        for i, member in enumerate(team_plan["team_members"]):
            if "name" not in member:
                member["name"] = f"Team Member {i+1}"
            if "role" not in member:
                member["role"] = f"member_{i+1}"
            if "description" not in member:
                member["description"] = "AI generated team member"
            if "system_prompt" not in member:
                member["system_prompt"] = "You are a helpful AI assistant."
            if "capabilities" not in member:
                member["capabilities"] = ["General assistance"]
            if "tools" not in member:
                member["tools"] = ["Analysis", "Communication"]
            if "model" not in member:
                member["model"] = "gpt-4"
            if "temperature" not in member:
                member["temperature"] = 0.7

            # Ensure context_placeholders field exists (new field for context-aware execution)
            if "context_placeholders" not in member:
                member["context_placeholders"] = []

            # Validate context_placeholders structure
            if not isinstance(member["context_placeholders"], list):
                member["context_placeholders"] = []

            # Ensure each placeholder has required fields for semantic naming
            for placeholder in member["context_placeholders"]:
                if not isinstance(placeholder, dict):
                    continue

                # Ensure placeholder_name exists
                if "placeholder_name" not in placeholder:
                    placeholder["placeholder_name"] = "{context.data}"

                # Ensure source_agent_role exists (new semantic field)
                if "source_agent_role" not in placeholder:
                    # Try to extract from placeholder_name if semantic format
                    placeholder_name = placeholder.get("placeholder_name", "")
                    if placeholder_name.startswith('{') and placeholder_name.endswith('}'):
                        content = placeholder_name[1:-1]
                        if '.' in content:
                            placeholder["source_agent_role"] = content.split('.')[0]
                        else:
                            placeholder["source_agent_role"] = "previous_member"
                    else:
                        placeholder["source_agent_role"] = "previous_member"

                # Ensure semantic_description exists (new semantic field)
                if "semantic_description" not in placeholder:
                    # Use legacy description if available, otherwise create default
                    legacy_desc = placeholder.get("description", "")
                    if legacy_desc and len(legacy_desc) >= 10:
                        placeholder["semantic_description"] = legacy_desc
                    else:
                        placeholder["semantic_description"] = "Context information from previous team member"

                # Ensure source_step exists
                if "source_step" not in placeholder:
                    placeholder["source_step"] = "previous_step"

                # Keep legacy description for backward compatibility
                if "description" not in placeholder:
                    placeholder["description"] = placeholder.get("semantic_description", "Dynamic context information")

        # Validate workflow
        if "workflow" not in team_plan:
            team_plan["workflow"] = {"steps": []}

        if "steps" not in team_plan["workflow"] or not team_plan["workflow"]["steps"]:
            # Generate basic workflow if missing
            # Use role instead of name for assignees
            first_member_role = team_plan["team_members"][0]["role"]
            second_member_role = team_plan["team_members"][1]["role"] if len(team_plan["team_members"]) > 1 else first_member_role

            team_plan["workflow"]["steps"] = [
                {
                    "name": "Analysis",
                    "description": "Analyze the user requirements",
                    "assignee": first_member_role,
                    "inputs": ["User requirements"],
                    "outputs": ["Analysis results"],
                    "context_dependencies": []
                },
                {
                    "name": "Solution",
                    "description": "Develop solution based on analysis",
                    "assignee": second_member_role,
                    "inputs": ["Analysis results"],
                    "outputs": ["Final solution"],
                    "context_dependencies": ["Analysis"]
                }
            ]

        # Ensure each workflow step has context_dependencies field
        for step in team_plan["workflow"]["steps"]:
            if "context_dependencies" not in step:
                step["context_dependencies"] = []
            if not isinstance(step["context_dependencies"], list):
                step["context_dependencies"] = []

        return team_plan

    async def generate_team_plan(self, user_description: str) -> Dict:
        """生成团队规划 - 仅使用AI生成"""
        logger.info(f"开始AI生成团队规划: {user_description[:100]}...")

        if not self.db or not self.user:
            raise ValueError("AI team generation requires database connection and user context")

        return await self.generate_ai_powered_team(user_description)



    def validate_team_plan(self, team_plan: Dict) -> Dict:
        """验证团队规划"""
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
            "suggestions": []
        }
        
        # 检查必要字段
        required_fields = ["team_name", "description", "objective", "team_members", "workflow"]
        for field in required_fields:
            if field not in team_plan:
                validation_result["errors"].append(f"缺少必要字段: {field}")
                validation_result["is_valid"] = False
        
        # 检查团队成员
        if "team_members" in team_plan:
            if len(team_plan["team_members"]) < 1:
                validation_result["errors"].append("团队至少需要一个成员")
                validation_result["is_valid"] = False
            elif len(team_plan["team_members"]) > 5:
                validation_result["warnings"].append("团队成员过多，可能影响协作效率")
        
        # 检查工作流程
        if "workflow" in team_plan and "steps" in team_plan["workflow"]:
            if len(team_plan["workflow"]["steps"]) < 1:
                validation_result["errors"].append("工作流程至少需要一个步骤")
                validation_result["is_valid"] = False
        
        return validation_result


def get_ai_planner(db: AsyncSession, user: User) -> AIPlanner:
    """获取AI规划师实例 - 需要数据库连接和用户上下文"""
    return AIPlanner(db=db, user=user)
