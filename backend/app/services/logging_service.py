"""
Centralized logging service for application events.
"""

import asyncio
import inspect
import traceback
import json
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List, Union
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, desc, func
from fastapi import Request

from app.core.database import AsyncSessionLocal
from app.core.timezone_utils import utc_now, normalize_datetime_for_db
from app.models.application_log import (
    ApplicationLog, ApplicationLogCreate, LogLevel, EventType,
    ApplicationLogResponse, ApplicationLogDetailResponse, ApplicationLogListResponse,
    LogFilterParams
)


def _serialize_json(data: Any) -> Optional[str]:
    """Serialize data to JSON string."""
    if data is None:
        return None
    try:
        return json.dumps(data)
    except (TypeError, ValueError):
        return str(data)


def _deserialize_json(data: Optional[str]) -> Any:
    """Deserialize JSON string to data."""
    if not data:
        return None
    try:
        return json.loads(data)
    except (json.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TypeError):
        return data


class ApplicationLoggingService:
    """Service for managing application logs."""
    
    def __init__(self, db: Optional[AsyncSession] = None):
        self.db = db
        self._should_close_db = db is None
    
    async def __aenter__(self):
        if self.db is None:
            self.db = AsyncSessionLocal()
            await self.db.__aenter__()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self._should_close_db and self.db:
            await self.db.close()
    
    async def log_event(
        self,
        level: LogLevel,
        event_type: EventType,
        message: str,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None,
        request_id: Optional[str] = None,
        agent_id: Optional[str] = None,
        test_id: Optional[str] = None,
        template_id: Optional[str] = None,
        api_key_id: Optional[int] = None,
        error_code: Optional[str] = None,
        error_type: Optional[str] = None,
        stack_trace: Optional[str] = None,
        execution_time_ms: Optional[float] = None,
        memory_usage_mb: Optional[float] = None,
        cpu_usage_percent: Optional[float] = None,
        metadata: Optional[Dict[str, Any]] = None,
        tags: Optional[List[str]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        request_method: Optional[str] = None,
        request_path: Optional[str] = None,
        request_headers: Optional[Dict[str, Any]] = None,
        request_body: Optional[Dict[str, Any]] = None,
        response_status: Optional[int] = None,
        response_headers: Optional[Dict[str, Any]] = None,
        response_body: Optional[Dict[str, Any]] = None,
        auto_capture_source: bool = True
    ) -> ApplicationLog:
        """Log an application event."""
        
        # Auto-capture source information if enabled
        source_module = None
        source_function = None
        source_file = None
        source_line = None
        
        if auto_capture_source:
            frame = inspect.currentframe()
            if frame and frame.f_back:
                caller_frame = frame.f_back
                source_file = caller_frame.f_code.co_filename
                source_function = caller_frame.f_code.co_name
                source_line = caller_frame.f_lineno
                source_module = inspect.getmodule(caller_frame).__name__ if inspect.getmodule(caller_frame) else None
        
        log_data = ApplicationLogCreate(
            level=level,
            event_type=event_type,
            message=message,
            user_id=user_id,
            session_id=session_id,
            request_id=request_id,
            source_module=source_module,
            source_function=source_function,
            source_file=source_file,
            source_line=source_line,
            request_method=request_method,
            request_path=request_path,
            request_headers=_serialize_json(request_headers),
            request_body=_serialize_json(request_body),
            response_status=response_status,
            response_headers=_serialize_json(response_headers),
            response_body=_serialize_json(response_body),
            execution_time_ms=execution_time_ms,
            memory_usage_mb=memory_usage_mb,
            cpu_usage_percent=cpu_usage_percent,
            agent_id=agent_id,
            test_id=test_id,
            template_id=template_id,
            api_key_id=api_key_id,
            error_code=error_code,
            error_type=error_type,
            stack_trace=stack_trace,
            log_metadata=_serialize_json(metadata),
            tags=_serialize_json(tags),
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        log_entry = ApplicationLog(**log_data.model_dump())
        
        if not self.db:
            async with self:
                self.db.add(log_entry)
                await self.db.commit()
                await self.db.refresh(log_entry)
        else:
            self.db.add(log_entry)
            await self.db.commit()
            await self.db.refresh(log_entry)
        
        return log_entry
    
    async def log_authentication_event(
        self,
        event_type: EventType,
        user_id: Optional[int],
        success: bool,
        message: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> ApplicationLog:
        """Log authentication-related events."""
        level = LogLevel.INFO if success else LogLevel.WARNING
        
        auth_metadata = {
            "success": success,
            **(metadata or {})
        }
        
        return await self.log_event(
            level=level,
            event_type=event_type,
            message=message,
            user_id=user_id,
            session_id=session_id,
            ip_address=ip_address,
            user_agent=user_agent,
            metadata=auth_metadata,
            tags=["authentication"]
        )
    
    async def log_agent_event(
        self,
        event_type: EventType,
        agent_id: str,
        user_id: Optional[int],
        message: str,
        execution_time_ms: Optional[float] = None,
        metadata: Optional[Dict[str, Any]] = None,
        error_code: Optional[str] = None,
        error_type: Optional[str] = None,
        stack_trace: Optional[str] = None
    ) -> ApplicationLog:
        """Log agent-related events."""
        level = LogLevel.ERROR if error_code else LogLevel.INFO
        
        return await self.log_event(
            level=level,
            event_type=event_type,
            message=message,
            user_id=user_id,
            agent_id=agent_id,
            execution_time_ms=execution_time_ms,
            metadata=metadata,
            error_code=error_code,
            error_type=error_type,
            stack_trace=stack_trace,
            tags=["agent"]
        )
    
    async def log_test_event(
        self,
        event_type: EventType,
        test_id: str,
        agent_id: str,
        user_id: Optional[int],
        message: str,
        execution_time_ms: Optional[float] = None,
        metadata: Optional[Dict[str, Any]] = None,
        error_code: Optional[str] = None,
        error_type: Optional[str] = None,
        stack_trace: Optional[str] = None
    ) -> ApplicationLog:
        """Log test execution events."""
        level = LogLevel.ERROR if error_code else LogLevel.INFO
        
        return await self.log_event(
            level=level,
            event_type=event_type,
            message=message,
            user_id=user_id,
            agent_id=agent_id,
            test_id=test_id,
            execution_time_ms=execution_time_ms,
            metadata=metadata,
            error_code=error_code,
            error_type=error_type,
            stack_trace=stack_trace,
            tags=["test"]
        )
    
    async def log_api_key_event(
        self,
        event_type: EventType,
        api_key_id: int,
        user_id: Optional[int],
        message: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> ApplicationLog:
        """Log API key management events."""
        return await self.log_event(
            level=LogLevel.INFO,
            event_type=event_type,
            message=message,
            user_id=user_id,
            api_key_id=api_key_id,
            metadata=metadata,
            tags=["api_key"]
        )
    
    async def log_system_event(
        self,
        event_type: EventType,
        message: str,
        level: LogLevel = LogLevel.INFO,
        metadata: Optional[Dict[str, Any]] = None,
        error_code: Optional[str] = None,
        error_type: Optional[str] = None,
        stack_trace: Optional[str] = None
    ) -> ApplicationLog:
        """Log system-level events."""
        return await self.log_event(
            level=level,
            event_type=event_type,
            message=message,
            metadata=metadata,
            error_code=error_code,
            error_type=error_type,
            stack_trace=stack_trace,
            tags=["system"]
        )
    
    async def log_performance_event(
        self,
        message: str,
        execution_time_ms: Optional[float] = None,
        memory_usage_mb: Optional[float] = None,
        cpu_usage_percent: Optional[float] = None,
        metadata: Optional[Dict[str, Any]] = None,
        user_id: Optional[int] = None,
        agent_id: Optional[str] = None,
        test_id: Optional[str] = None
    ) -> ApplicationLog:
        """Log performance metrics."""
        return await self.log_event(
            level=LogLevel.INFO,
            event_type=EventType.PERFORMANCE_METRIC,
            message=message,
            user_id=user_id,
            agent_id=agent_id,
            test_id=test_id,
            execution_time_ms=execution_time_ms,
            memory_usage_mb=memory_usage_mb,
            cpu_usage_percent=cpu_usage_percent,
            metadata=metadata,
            tags=["performance"]
        )
    
    async def log_error(
        self,
        message: str,
        error: Exception,
        user_id: Optional[int] = None,
        agent_id: Optional[str] = None,
        test_id: Optional[str] = None,
        request_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> ApplicationLog:
        """Log error events with exception details."""
        error_metadata = {
            "exception_type": type(error).__name__,
            "exception_message": str(error),
            **(metadata or {})
        }
        
        return await self.log_event(
            level=LogLevel.ERROR,
            event_type=EventType.SYSTEM_ERROR,
            message=message,
            user_id=user_id,
            agent_id=agent_id,
            test_id=test_id,
            request_id=request_id,
            error_type=type(error).__name__,
            stack_trace=traceback.format_exc(),
            metadata=error_metadata,
            tags=["error"]
        )

    async def get_logs(
        self,
        filters: LogFilterParams,
        page: int = 1,
        limit: int = 50,
        user_id: Optional[int] = None  # For user isolation
    ) -> ApplicationLogListResponse:
        """Get logs with filtering and pagination."""
        if not self.db:
            async with self:
                return await self._get_logs_impl(filters, page, limit, user_id)
        else:
            return await self._get_logs_impl(filters, page, limit, user_id)

    async def _get_logs_impl(
        self,
        filters: LogFilterParams,
        page: int,
        limit: int,
        user_id: Optional[int]
    ) -> ApplicationLogListResponse:
        """Implementation of get_logs."""
        # Build query conditions
        conditions = []

        # User isolation - users can only see their own logs
        if user_id is not None:
            conditions.append(ApplicationLog.user_id == user_id)

        # Apply filters
        if filters.level:
            conditions.append(ApplicationLog.level == filters.level)

        if filters.event_type:
            conditions.append(ApplicationLog.event_type == filters.event_type)

        if filters.agent_id:
            conditions.append(ApplicationLog.agent_id == filters.agent_id)

        if filters.test_id:
            conditions.append(ApplicationLog.test_id == filters.test_id)

        if filters.template_id:
            conditions.append(ApplicationLog.template_id == filters.template_id)

        if filters.source_module:
            conditions.append(ApplicationLog.source_module == filters.source_module)

        if filters.error_code:
            conditions.append(ApplicationLog.error_code == filters.error_code)

        if filters.ip_address:
            conditions.append(ApplicationLog.ip_address == filters.ip_address)

        if filters.start_date:
            conditions.append(ApplicationLog.timestamp >= filters.start_date)

        if filters.end_date:
            conditions.append(ApplicationLog.timestamp <= filters.end_date)

        if filters.search_query:
            search_conditions = [
                ApplicationLog.message.ilike(f"%{filters.search_query}%"),
                ApplicationLog.source_module.ilike(f"%{filters.search_query}%"),
                ApplicationLog.source_function.ilike(f"%{filters.search_query}%"),
                ApplicationLog.agent_id.ilike(f"%{filters.search_query}%"),
                ApplicationLog.test_id.ilike(f"%{filters.search_query}%"),
                ApplicationLog.error_code.ilike(f"%{filters.search_query}%"),
                ApplicationLog.error_type.ilike(f"%{filters.search_query}%")
            ]
            conditions.append(or_(*search_conditions))

        if filters.tags:
            # Check if any of the specified tags are in the log's tags array
            for tag in filters.tags:
                conditions.append(ApplicationLog.tags.contains([tag]))

        # Build the query
        query = select(ApplicationLog)
        if conditions:
            query = query.where(and_(*conditions))

        # Get total count
        count_query = select(func.count(ApplicationLog.id))
        if conditions:
            count_query = count_query.where(and_(*conditions))

        total_result = await self.db.execute(count_query)
        total = total_result.scalar()

        # Apply pagination and ordering
        offset = (page - 1) * limit
        query = query.order_by(desc(ApplicationLog.timestamp)).offset(offset).limit(limit)

        # Execute query
        result = await self.db.execute(query)
        logs = result.scalars().all()

        # Convert to response models
        log_responses = [
            ApplicationLogResponse(
                id=log.id,
                uuid=log.uuid,
                level=log.level,
                event_type=log.event_type,
                message=log.message,
                user_id=log.user_id,
                session_id=log.session_id,
                request_id=log.request_id,
                source_module=log.source_module,
                source_function=log.source_function,
                execution_time_ms=log.execution_time_ms,
                agent_id=log.agent_id,
                test_id=log.test_id,
                template_id=log.template_id,
                error_code=log.error_code,
                error_type=log.error_type,
                ip_address=log.ip_address,
                timestamp=log.timestamp,
                created_at=log.created_at
            )
            for log in logs
        ]

        return ApplicationLogListResponse(
            logs=log_responses,
            total=total,
            page=page,
            limit=limit,
            has_next=page * limit < total,
            has_prev=page > 1
        )

    async def get_log_detail(self, log_id: int, user_id: Optional[int] = None) -> Optional[ApplicationLogDetailResponse]:
        """Get detailed log information."""
        if not self.db:
            async with self:
                return await self._get_log_detail_impl(log_id, user_id)
        else:
            return await self._get_log_detail_impl(log_id, user_id)

    async def _get_log_detail_impl(self, log_id: int, user_id: Optional[int]) -> Optional[ApplicationLogDetailResponse]:
        """Implementation of get_log_detail."""
        query = select(ApplicationLog).where(ApplicationLog.id == log_id)

        # User isolation
        if user_id is not None:
            query = query.where(ApplicationLog.user_id == user_id)

        result = await self.db.execute(query)
        log = result.scalar_one_or_none()

        if not log:
            return None

        return ApplicationLogDetailResponse(
            id=log.id,
            uuid=log.uuid,
            level=log.level,
            event_type=log.event_type,
            message=log.message,
            user_id=log.user_id,
            session_id=log.session_id,
            request_id=log.request_id,
            source_module=log.source_module,
            source_function=log.source_function,
            source_file=log.source_file,
            source_line=log.source_line,
            request_method=log.request_method,
            request_path=log.request_path,
            request_headers=_deserialize_json(log.request_headers),
            request_body=_deserialize_json(log.request_body),
            response_status=log.response_status,
            response_headers=_deserialize_json(log.response_headers),
            response_body=_deserialize_json(log.response_body),
            execution_time_ms=log.execution_time_ms,
            memory_usage_mb=log.memory_usage_mb,
            cpu_usage_percent=log.cpu_usage_percent,
            agent_id=log.agent_id,
            test_id=log.test_id,
            template_id=log.template_id,
            api_key_id=log.api_key_id,
            error_code=log.error_code,
            error_type=log.error_type,
            stack_trace=log.stack_trace,
            log_metadata=_deserialize_json(log.log_metadata),
            tags=_deserialize_json(log.tags),
            ip_address=log.ip_address,
            user_agent=log.user_agent,
            timestamp=log.timestamp,
            created_at=log.created_at
        )

    async def get_statistics(self, user_id: Optional[int] = None) -> Dict[str, Any]:
        """Get log statistics for a user."""
        if not self.db:
            async with self:
                return await self._get_statistics_impl(user_id)
        else:
            return await self._get_statistics_impl(user_id)

    async def _get_statistics_impl(self, user_id: Optional[int]) -> Dict[str, Any]:
        """Implementation of get_statistics."""
        # Build base conditions for user isolation
        conditions = []
        if user_id is not None:
            conditions.append(ApplicationLog.user_id == user_id)

        # Count total logs for user
        total_query = select(func.count(ApplicationLog.id))
        if conditions:
            total_query = total_query.where(and_(*conditions))

        total_result = await self.db.execute(total_query)
        total_logs = total_result.scalar() or 0

        # Count by level
        level_query = select(ApplicationLog.level, func.count(ApplicationLog.id))
        if conditions:
            level_query = level_query.where(and_(*conditions))
        level_query = level_query.group_by(ApplicationLog.level)

        level_result = await self.db.execute(level_query)
        level_stats = level_result.fetchall()

        # Count by event type
        event_query = select(ApplicationLog.event_type, func.count(ApplicationLog.id))
        if conditions:
            event_query = event_query.where(and_(*conditions))
        event_query = event_query.group_by(ApplicationLog.event_type)

        event_result = await self.db.execute(event_query)
        event_stats = event_result.fetchall()

        # Count recent activity (last 24 hours)
        twenty_four_hours_ago = utc_now() - timedelta(hours=24)
        recent_conditions = conditions.copy()
        recent_conditions.append(ApplicationLog.timestamp >= twenty_four_hours_ago)

        recent_query = select(func.count(ApplicationLog.id))
        if recent_conditions:
            recent_query = recent_query.where(and_(*recent_conditions))

        recent_result = await self.db.execute(recent_query)
        recent_activity_24h = recent_result.scalar() or 0

        # Build response
        level_distribution = {str(row[0]): row[1] for row in level_stats}
        event_type_distribution = {str(row[0]): row[1] for row in event_stats}

        return {
            "user_id": user_id,
            "total_logs": total_logs,
            "level_distribution": level_distribution,
            "event_type_distribution": event_type_distribution,
            "recent_activity_24h": recent_activity_24h,
            "generated_at": utc_now().isoformat()
        }


# Global logging service instance
logging_service = ApplicationLoggingService()


# Convenience functions for common logging operations
async def log_authentication(
    event_type: EventType,
    user_id: Optional[int],
    success: bool,
    message: str,
    ip_address: Optional[str] = None,
    user_agent: Optional[str] = None,
    session_id: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None
) -> ApplicationLog:
    """Convenience function for logging authentication events."""
    async with logging_service:
        return await logging_service.log_authentication_event(
            event_type, user_id, success, message, ip_address, user_agent, session_id, metadata
        )


async def log_agent_operation(
    event_type: EventType,
    agent_id: str,
    user_id: Optional[int],
    message: str,
    execution_time_ms: Optional[float] = None,
    metadata: Optional[Dict[str, Any]] = None,
    error: Optional[Exception] = None
) -> ApplicationLog:
    """Convenience function for logging agent operations."""
    async with logging_service:
        return await logging_service.log_agent_event(
            event_type=event_type,
            agent_id=agent_id,
            user_id=user_id,
            message=message,
            execution_time_ms=execution_time_ms,
            metadata=metadata,
            error_code=type(error).__name__ if error else None,
            error_type=type(error).__name__ if error else None,
            stack_trace=traceback.format_exc() if error else None
        )


async def log_test_execution(
    event_type: EventType,
    test_id: str,
    agent_id: str,
    user_id: Optional[int],
    message: str,
    execution_time_ms: Optional[float] = None,
    metadata: Optional[Dict[str, Any]] = None,
    error: Optional[Exception] = None
) -> ApplicationLog:
    """Convenience function for logging test executions."""
    async with logging_service:
        return await logging_service.log_test_event(
            event_type=event_type,
            test_id=test_id,
            agent_id=agent_id,
            user_id=user_id,
            message=message,
            execution_time_ms=execution_time_ms,
            metadata=metadata,
            error_code=type(error).__name__ if error else None,
            error_type=type(error).__name__ if error else None,
            stack_trace=traceback.format_exc() if error else None
        )


async def log_system_error(
    message: str,
    error: Exception,
    user_id: Optional[int] = None,
    agent_id: Optional[str] = None,
    test_id: Optional[str] = None,
    request_id: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None
) -> ApplicationLog:
    """Convenience function for logging system errors."""
    async with logging_service:
        return await logging_service.log_error(
            message, error, user_id, agent_id, test_id, request_id, metadata
        )
