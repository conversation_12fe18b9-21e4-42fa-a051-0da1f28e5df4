"""
Context management service for dynamic context sharing between team members.
"""

import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.context import (
    RuntimeContext, 
    ContextManager, 
    ContextPlaceholder,
    ContextAwareTeamMember,
    ContextAwareWorkflowStep
)
from app.core.logging import get_logger
from app.core.timezone_utils import utc_now

logger = get_logger(__name__)


class ContextService:
    """Service for managing runtime context during team execution."""
    
    def __init__(self, db: Optional[AsyncSession] = None):
        self.db = db
        self.active_contexts: Dict[str, ContextManager] = {}
    
    def create_execution_context(self, execution_id: str) -> ContextManager:
        """Create a new execution context manager."""
        context_manager = ContextManager(execution_id=execution_id)
        self.active_contexts[execution_id] = context_manager
        
        logger.info(f"Created execution context: {execution_id}")
        return context_manager
    
    def get_execution_context(self, execution_id: str) -> Optional[ContextManager]:
        """Get an existing execution context manager."""
        return self.active_contexts.get(execution_id)
    
    def add_step_context(
        self, 
        execution_id: str, 
        step_name: str, 
        member_name: str, 
        context_data: Dict[str, Any],
        input_tokens: Optional[int] = None,
        output_tokens: Optional[int] = None,
        processing_time_ms: Optional[int] = None
    ) -> RuntimeContext:
        """Add context data for a completed step."""
        context_manager = self.get_execution_context(execution_id)
        if not context_manager:
            context_manager = self.create_execution_context(execution_id)
        
        runtime_context = RuntimeContext(
            execution_id=execution_id,
            step_name=step_name,
            member_name=member_name,
            context_data=context_data,
            timestamp=utc_now().isoformat(),
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            processing_time_ms=processing_time_ms
        )
        
        context_manager.add_context(runtime_context)
        
        logger.info(f"Added context for step '{step_name}' in execution {execution_id}")
        return runtime_context
    
    def resolve_member_prompt(
        self, 
        execution_id: str, 
        member: ContextAwareTeamMember,
        user_input: str = ""
    ) -> str:
        """Resolve placeholders in a team member's system prompt."""
        context_manager = self.get_execution_context(execution_id)
        if not context_manager:
            logger.warning(f"No context manager found for execution {execution_id}")
            return member.system_prompt
        
        # Add user input to context if not already present
        if user_input and "user_input" not in context_manager.contexts:
            self.add_step_context(
                execution_id=execution_id,
                step_name="user_input",
                member_name="system",
                context_data={"user_input": user_input, "output": user_input}
            )
        
        # Replace placeholders in the system prompt
        resolved_prompt = context_manager.replace_placeholders(
            member.system_prompt, 
            member.context_placeholders
        )
        
        logger.debug(f"Resolved prompt for member '{member.name}': {len(resolved_prompt)} characters")
        return resolved_prompt
    
    def get_step_dependencies_context(
        self, 
        execution_id: str, 
        step: ContextAwareWorkflowStep
    ) -> Dict[str, Any]:
        """Get context data from all dependency steps."""
        context_manager = self.get_execution_context(execution_id)
        if not context_manager:
            return {}
        
        dependencies_context = {}
        
        for dependency_step in step.context_dependencies:
            context = context_manager.get_context(dependency_step)
            if context:
                dependencies_context[dependency_step] = context.context_data
        
        return dependencies_context
    
    def build_step_prompt(
        self, 
        execution_id: str, 
        step: ContextAwareWorkflowStep, 
        member: ContextAwareTeamMember,
        user_input: str = ""
    ) -> str:
        """Build a complete prompt for a workflow step with context."""
        # Get resolved member prompt
        resolved_system_prompt = self.resolve_member_prompt(execution_id, member, user_input)
        
        # Get dependency context
        dependencies_context = self.get_step_dependencies_context(execution_id, step)
        
        # Build step-specific prompt
        step_prompt = f"""
工作流步骤: {step.name}
步骤描述: {step.description}
负责成员: {step.assignee}

成员系统提示: {resolved_system_prompt}

用户输入: {user_input}
"""
        
        # Add dependency context if available
        if dependencies_context:
            step_prompt += "\n前置步骤输出:\n"
            for dep_step, dep_context in dependencies_context.items():
                output = dep_context.get("output", "无输出")
                step_prompt += f"- {dep_step}: {output}\n"
        
        # Add step inputs/outputs guidance
        if step.inputs:
            step_prompt += f"\n期望输入: {', '.join(step.inputs)}"
        
        if step.outputs:
            step_prompt += f"\n期望输出: {', '.join(step.outputs)}"
        
        step_prompt += "\n\n请根据以上信息完成该步骤的任务。"
        
        return step_prompt

    def build_collaborative_step_prompt(
        self,
        execution_id: str,
        step: ContextAwareWorkflowStep,
        primary_member: ContextAwareTeamMember,
        team_members: List[Dict],
        user_input: str = "",
        collaborative_context: Dict = None
    ) -> str:
        """Build a prompt for collaborative team steps."""
        # Get dependency context
        dependencies_context = self.get_step_dependencies_context(execution_id, step)

        # Build team overview
        team_overview = "团队成员概览:\n"
        for i, member in enumerate(team_members, 1):
            team_overview += f"{i}. {member.get('name', 'Unknown')} ({member.get('role', 'Assistant')}): {member.get('description', '')}\n"

        # Build collaborative prompt
        collaborative_prompt = f"""
工作流步骤: {step.name} (团队协作)
步骤描述: {step.description}
协作方式: 团队成员共同完成此步骤

{team_overview}

协作指导原则:
- 综合运用各成员的专业能力和视角
- 确保输出质量和完整性
- 体现团队协作的优势和互补性
- 以用户需求为核心，提供最佳解决方案

用户输入: {user_input}
"""

        # Add dependency context if available
        if dependencies_context:
            collaborative_prompt += "\n前置步骤输出:\n"
            for dep_step, dep_context in dependencies_context.items():
                output = dep_context.get("output", "无输出")
                collaborative_prompt += f"- {dep_step}: {output}\n"

        # Add step inputs/outputs guidance
        if step.inputs:
            collaborative_prompt += f"\n期望输入: {', '.join(step.inputs)}"

        if step.outputs:
            collaborative_prompt += f"\n期望输出: {', '.join(step.outputs)}"

        collaborative_prompt += "\n\n请以团队协作的方式完成该步骤，体现各成员的专业贡献。"

        return collaborative_prompt

    def cleanup_execution_context(self, execution_id: str) -> None:
        """Clean up execution context after completion."""
        if execution_id in self.active_contexts:
            del self.active_contexts[execution_id]
            logger.info(f"Cleaned up execution context: {execution_id}")
    
    def get_execution_summary(self, execution_id: str) -> Dict[str, Any]:
        """Get a summary of the execution context."""
        context_manager = self.get_execution_context(execution_id)
        if not context_manager:
            return {}
        
        summary = {
            "execution_id": execution_id,
            "total_steps": len(context_manager.contexts),
            "steps": []
        }
        
        for step_name, context in context_manager.contexts.items():
            step_summary = {
                "step_name": step_name,
                "member_name": context.member_name,
                "timestamp": context.timestamp,
                "input_tokens": context.input_tokens,
                "output_tokens": context.output_tokens,
                "processing_time_ms": context.processing_time_ms,
                "has_output": bool(context.context_data.get("output"))
            }
            summary["steps"].append(step_summary)
        
        return summary
    
    def export_execution_context(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """Export execution context for storage or analysis."""
        context_manager = self.get_execution_context(execution_id)
        if not context_manager:
            return None
        
        export_data = {
            "execution_id": execution_id,
            "contexts": {},
            "placeholder_mappings": context_manager.placeholder_mappings,
            "exported_at": utc_now().isoformat()
        }
        
        for step_name, context in context_manager.contexts.items():
            export_data["contexts"][step_name] = {
                "execution_id": context.execution_id,
                "step_name": context.step_name,
                "member_name": context.member_name,
                "context_data": context.context_data,
                "timestamp": context.timestamp,
                "input_tokens": context.input_tokens,
                "output_tokens": context.output_tokens,
                "processing_time_ms": context.processing_time_ms
            }
        
        return export_data
    
    def import_execution_context(self, export_data: Dict[str, Any]) -> Optional[ContextManager]:
        """Import execution context from exported data."""
        try:
            execution_id = export_data["execution_id"]
            context_manager = ContextManager(
                execution_id=execution_id,
                placeholder_mappings=export_data.get("placeholder_mappings", {})
            )
            
            for step_name, context_data in export_data["contexts"].items():
                runtime_context = RuntimeContext(**context_data)
                context_manager.add_context(runtime_context)
            
            self.active_contexts[execution_id] = context_manager
            logger.info(f"Imported execution context: {execution_id}")
            return context_manager
            
        except Exception as e:
            logger.error(f"Failed to import execution context: {str(e)}")
            return None


# Global context service instance
_context_service = None

def get_context_service(db: Optional[AsyncSession] = None) -> ContextService:
    """Get the global context service instance."""
    global _context_service
    if _context_service is None:
        _context_service = ContextService(db)
    return _context_service
