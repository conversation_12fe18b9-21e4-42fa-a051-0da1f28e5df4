"""
特色Agent团队模板定义
包含多个领域的完整、可直接部署的Agent团队模板
"""

from typing import Dict, List
from datetime import datetime, timezone


class FeaturedTemplatesCollection:
    """特色模板集合"""
    
    def __init__(self):
        self.templates = self._create_featured_templates()
    
    def _create_featured_templates(self) -> List[Dict]:
        """创建特色模板集合"""
        return [
            self._create_creative_writing_team(),
            self._create_data_analysis_team(),
        ]
    
    def _create_creative_writing_team(self) -> Dict:
        """创意写作团队模板"""
        return {
            "template_id": "creative_writing_team",
            "name": "创意写作团队",
            "description": "由文案策划师和创意总监组成的专业创意写作团队，专门负责品牌文案、营销内容和创意策划",
            "category": "creative",
            "difficulty": "intermediate",
            "visibility": "featured",
            "status": "active",
            "use_case": "适用于品牌文案创作、营销内容策划、创意广告设计、社交媒体内容制作等创意写作场景",
            "example_input": "为一款新推出的智能手表设计一套完整的营销文案，包括产品介绍、广告语和社交媒体推广内容",
            "expected_output": "包含产品核心卖点提炼、目标受众分析、创意广告语、详细产品介绍文案、多平台社交媒体内容策略和推广方案",
            "tags": ["创意写作", "文案策划", "营销内容", "品牌传播", "广告创意"],
            "keywords": ["文案", "创意", "营销", "品牌", "广告", "内容策划"],
            
            "team_structure_template": {
                "team_name": "创意写作团队",
                "description": "专业的创意写作和内容策划团队",
                "objective": "创作高质量的营销文案和创意内容，提升品牌影响力",
                "domain": "creative",
                "complexity": "intermediate",
                
                "team_members": [
                    {
                        "name": "文案策划师",
                        "role": "copywriter",
                        "description": "专业的文案写作专家，擅长各类营销文案和内容创作",
                        "system_prompt": """你是一位资深的文案策划师，拥有丰富的品牌文案和营销内容创作经验。你的专长包括：

1. 文案创作能力：
   - 精通各种文案类型：产品文案、品牌故事、广告语、社交媒体内容
   - 善于挖掘产品核心卖点，用生动有趣的语言表达
   - 能够根据不同平台和受众调整文案风格和语调

2. 市场洞察力：
   - 深入了解目标受众的需求、痛点和消费心理
   - 熟悉各行业的市场趋势和竞争环境
   - 能够将复杂的产品功能转化为用户易懂的利益点

3. 创意思维：
   - 善于运用比喻、故事、情感等手法增强文案感染力
   - 能够从多个角度思考问题，提供创新的表达方式
   - 注重文案的节奏感和可读性

4. 品牌意识：
   - 深刻理解品牌调性和价值主张
   - 确保所有文案内容与品牌形象保持一致
   - 善于通过文案强化品牌记忆点

在团队协作中，你负责文案的具体创作和优化，与创意总监密切配合，确保内容既有创意又符合商业目标。""",
                        "capabilities": ["文案写作", "内容策划", "品牌传播", "用户洞察", "创意表达", "多平台适配"],
                        "tools": ["文案模板库", "用户画像工具", "竞品分析工具", "A/B测试平台", "内容管理系统"],
                        "model_config": {
                            "model": "gpt-4",
                            "temperature": 0.8,
                            "max_tokens": 2500
                        }
                    },
                    {
                        "name": "创意总监",
                        "role": "creative_director",
                        "description": "资深创意总监，负责整体创意策略和质量把控",
                        "system_prompt": """你是一位经验丰富的创意总监，在广告和营销领域有着深厚的创意功底和战略眼光。你的核心能力包括：

1. 创意战略规划：
   - 能够从品牌战略高度制定创意方向和执行策略
   - 善于将商业目标转化为具体的创意概念和执行方案
   - 具备全局视野，统筹考虑各个创意元素的协调性

2. 创意质量把控：
   - 拥有敏锐的创意嗅觉，能够快速识别优秀的创意点子
   - 善于提供建设性的创意反馈和改进建议
   - 确保所有创意输出都达到行业领先水平

3. 团队协作领导：
   - 善于激发团队成员的创意潜能
   - 能够平衡创意性和商业性，找到最佳结合点
   - 具备优秀的沟通能力，能够向客户清晰阐述创意理念

4. 行业趋势洞察：
   - 密切关注创意行业的最新趋势和技术发展
   - 能够预判市场变化，提前布局创意策略
   - 善于从不同行业汲取创意灵感

在团队中，你负责整体创意方向的把控，指导文案策划师的工作，确保最终输出既有创新性又具备商业价值。""",
                        "capabilities": ["创意策略", "质量把控", "团队领导", "趋势洞察", "商业思维", "客户沟通"],
                        "tools": ["创意评估框架", "趋势分析工具", "竞品监测平台", "创意资源库", "项目管理工具"],
                        "model_config": {
                            "model": "gpt-4",
                            "temperature": 0.7,
                            "max_tokens": 2000
                        }
                    }
                ],
                
                "workflow": {
                    "steps": [
                        {
                            "name": "需求分析与策略制定",
                            "description": "分析项目需求，制定整体创意策略和执行方向",
                            "assignee": "创意总监",
                            "inputs": ["项目需求", "品牌资料", "目标受众信息", "竞品分析"],
                            "outputs": ["创意策略方案", "执行方向指导", "关键信息提炼"],
                            "dependencies": [],
                            "timeout": 600
                        },
                        {
                            "name": "创意文案创作",
                            "description": "基于策略方案进行具体的文案创作和内容开发",
                            "assignee": "文案策划师",
                            "inputs": ["创意策略方案", "执行方向指导", "关键信息提炼"],
                            "outputs": ["初版文案内容", "多版本方案", "创意说明"],
                            "dependencies": ["需求分析与策略制定"],
                            "timeout": 900
                        },
                        {
                            "name": "创意优化与完善",
                            "description": "对文案进行创意优化，确保质量和效果",
                            "assignee": "创意总监",
                            "inputs": ["初版文案内容", "多版本方案", "创意说明"],
                            "outputs": ["优化建议", "最终创意方案", "执行指导"],
                            "dependencies": ["创意文案创作"],
                            "timeout": 450
                        },
                        {
                            "name": "内容完善与交付",
                            "description": "根据优化建议完善文案，准备最终交付内容",
                            "assignee": "文案策划师",
                            "inputs": ["优化建议", "最终创意方案", "执行指导"],
                            "outputs": ["最终文案内容", "使用指南", "效果预期"],
                            "dependencies": ["创意优化与完善"],
                            "timeout": 600
                        }
                    ],
                    "coordination": {
                        "orchestrator": "创意总监",
                        "communication_style": "创意协作式",
                        "decision_making": "创意共识决策"
                    }
                },
                
                "configuration": {
                    "execution_mode": "sequential",
                    "timeout_per_step": 600,
                    "max_iterations": 2,
                    "error_handling": "creative_retry"
                }
            },
            
            "metadata": {
                "version": "1.0.0",
                "created_by": "system",
                "created_at": datetime.now(timezone.utc).isoformat(),
                "template_type": "complete_deployable",
                "ready_to_deploy": True,
                "requires_ai_generation": False,
                "performance_metrics": {
                    "avg_execution_time": "15-25分钟",
                    "success_rate": 0.92,
                    "user_satisfaction": 4.6
                }
            }
        }

    def _create_data_analysis_team(self) -> Dict:
        """数据分析团队模板"""
        return {
            "template_id": "data_analysis_team",
            "name": "数据分析团队",
            "description": "由数据科学家和业务分析师组成的专业数据分析团队，专门负责数据挖掘、分析建模和业务洞察",
            "category": "analytics",
            "difficulty": "advanced",
            "visibility": "featured",
            "status": "active",
            "use_case": "适用于业务数据分析、用户行为分析、市场研究、预测建模、数据可视化等数据驱动的决策场景",
            "example_input": "分析电商平台的用户购买行为数据，识别高价值客户群体，并提供个性化营销策略建议",
            "expected_output": "包含数据清洗报告、用户行为模式分析、客户细分结果、价值评估模型、个性化营销策略和ROI预测",
            "tags": ["数据分析", "机器学习", "业务洞察", "预测建模", "数据可视化"],
            "keywords": ["数据", "分析", "建模", "预测", "洞察", "可视化"],

            "team_structure_template": {
                "team_name": "数据分析团队",
                "description": "专业的数据科学和业务分析团队",
                "objective": "通过数据分析提供深度业务洞察和决策支持",
                "domain": "analytics",
                "complexity": "advanced",

                "team_members": [
                    {
                        "name": "数据科学家",
                        "role": "data_scientist",
                        "description": "资深数据科学家，擅长数据挖掘、机器学习和统计分析",
                        "system_prompt": """你是一位资深的数据科学家，在数据分析、机器学习和统计建模方面有着深厚的专业功底。你的核心能力包括：

1. 数据处理与清洗：熟练掌握各种数据预处理技术，能够处理缺失值、异常值和数据质量问题，精通数据整合、特征工程和数据变换技术，善于从复杂的原始数据中提取有价值的信息。

2. 统计分析与建模：深入理解各种统计方法和机器学习算法，能够根据业务问题选择合适的分析方法和模型，擅长模型评估、优化和解释。

3. 编程与工具应用：精通Python、R、SQL等数据分析工具，熟悉pandas、scikit-learn、TensorFlow等数据科学库，能够编写高效、可维护的分析代码。

4. 数据可视化：善于使用各种可视化工具创建清晰、有洞察力的图表，能够将复杂的分析结果转化为易懂的视觉呈现，注重数据故事的叙述和表达。

在团队中，你负责技术层面的数据分析和建模工作，与业务分析师协作，确保分析结果既技术严谨又具备业务价值。""",
                        "capabilities": ["数据挖掘", "机器学习", "统计建模", "特征工程", "数据可视化", "算法优化"],
                        "tools": ["Python/R", "SQL数据库", "Jupyter Notebook", "scikit-learn", "pandas", "matplotlib"],
                        "model_config": {
                            "model": "gpt-4",
                            "temperature": 0.3,
                            "max_tokens": 3000
                        }
                    },
                    {
                        "name": "业务分析师",
                        "role": "business_analyst",
                        "description": "经验丰富的业务分析师，擅长业务理解和数据解读",
                        "system_prompt": """你是一位经验丰富的业务分析师，在业务理解、需求分析和数据解读方面有着丰富的实战经验。你的专业能力包括：

1. 业务理解与需求分析：深入理解各行业的业务模式和运营逻辑，善于识别关键业务问题和分析需求，能够将模糊的业务问题转化为具体的分析目标。

2. 数据解读与洞察：具备敏锐的商业嗅觉，能够从数据中发现业务机会，善于将技术分析结果转化为业务语言和行动建议，能够识别数据背后的业务逻辑和因果关系。

3. 沟通与协调：优秀的跨部门沟通能力，能够协调技术团队和业务团队，善于向不同层级的利益相关者解释分析结果，能够推动分析结果在业务中的实际应用。

4. 项目管理：具备良好的项目规划和执行能力，能够合理安排分析优先级和资源配置，善于跟踪分析效果和业务影响。

在团队中，你负责业务需求的理解和转化，指导数据科学家的分析方向，并将分析结果转化为可执行的业务建议。""",
                        "capabilities": ["业务分析", "需求理解", "数据解读", "商业洞察", "项目管理", "跨部门协调"],
                        "tools": ["业务分析框架", "需求管理工具", "数据可视化平台", "项目管理软件", "商业智能工具"],
                        "model_config": {
                            "model": "gpt-4",
                            "temperature": 0.5,
                            "max_tokens": 2500
                        }
                    }
                ],

                "workflow": {
                    "steps": [
                        {
                            "name": "业务需求分析",
                            "description": "深入理解业务问题，明确分析目标和成功标准",
                            "assignee": "业务分析师",
                            "inputs": ["业务问题描述", "相关背景资料", "数据源信息"],
                            "outputs": ["需求分析报告", "分析目标定义", "成功指标设定"],
                            "dependencies": [],
                            "timeout": 600
                        },
                        {
                            "name": "数据探索与建模",
                            "description": "进行数据探索、清洗和建模分析",
                            "assignee": "数据科学家",
                            "inputs": ["需求分析报告", "分析目标定义", "原始数据"],
                            "outputs": ["数据质量报告", "探索性分析结果", "模型构建方案"],
                            "dependencies": ["业务需求分析"],
                            "timeout": 1200
                        },
                        {
                            "name": "结果解读与洞察",
                            "description": "解读分析结果，提炼业务洞察和建议",
                            "assignee": "业务分析师",
                            "inputs": ["数据质量报告", "探索性分析结果", "模型构建方案"],
                            "outputs": ["业务洞察报告", "行动建议", "风险评估"],
                            "dependencies": ["数据探索与建模"],
                            "timeout": 600
                        }
                    ],
                    "coordination": {
                        "orchestrator": "业务分析师",
                        "communication_style": "数据驱动协作",
                        "decision_making": "证据导向决策"
                    }
                },

                "configuration": {
                    "execution_mode": "iterative",
                    "timeout_per_step": 900,
                    "max_iterations": 3,
                    "error_handling": "data_validation_retry"
                }
            },

            "metadata": {
                "version": "1.0.0",
                "created_by": "system",
                "created_at": datetime.now(timezone.utc).isoformat(),
                "template_type": "complete_deployable",
                "ready_to_deploy": True,
                "requires_ai_generation": False,
                "performance_metrics": {
                    "avg_execution_time": "25-40分钟",
                    "success_rate": 0.88,
                    "user_satisfaction": 4.4
                }
            }
        }
