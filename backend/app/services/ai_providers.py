"""
AI provider services for different LLM providers.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional

# No longer need global config imports - providers use user-specific API keys
from app.core.exceptions import AIProviderError
from app.core.logging import get_logger

logger = get_logger("ai_providers")


class AIProvider(ABC):
    """Abstract base class for AI providers."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.name = self.__class__.__name__.lower().replace("provider", "")
    
    @abstractmethod
    async def generate_text(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> str:
        """Generate text using the AI provider."""
        pass
    
    @abstractmethod
    async def generate_structured_output(
        self,
        prompt: str,
        schema: Dict[str, Any],
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate structured output using the AI provider."""
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """Check if the AI provider is healthy."""
        pass

    async def generate_text_stream(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        **kwargs
    ):
        """Generate text using streaming. Default implementation falls back to non-streaming."""
        # Default implementation for providers that don't support streaming
        result = await self.generate_text(
            prompt=prompt,
            system_prompt=system_prompt,
            temperature=temperature,
            max_tokens=max_tokens,
            **kwargs
        )
        # Yield the entire result as a single chunk
        yield result


class OpenAIProvider(AIProvider):
    """OpenAI provider implementation."""

    def __init__(self, api_key: str, base_url: Optional[str] = None, model: Optional[str] = None, temperature: Optional[float] = None):
        """Initialize OpenAI provider with user-specific configuration.

        Args:
            api_key: The OpenAI API key to use
            base_url: Optional custom base URL (for proxies or compatible APIs)
            model: Optional default model to use
            temperature: Optional default temperature
        """
        config = {
            "api_key": api_key,
            "model": model or "gpt-4o-mini",
            "temperature": temperature or 0.7,
            "base_url": base_url
        }
        super().__init__(config)

        if not api_key:
            raise AIProviderError("openai", "API key is required")

        try:
            from openai import AsyncOpenAI
            # Use custom base_url if provided, otherwise use default
            client_kwargs = {"api_key": api_key}
            if base_url:
                client_kwargs["base_url"] = base_url
                logger.info(f"OpenAI provider initialized with custom base_url: {base_url}")
            else:
                logger.info("OpenAI provider initialized with default base_url")
            self.client = AsyncOpenAI(**client_kwargs)
        except ImportError:
            raise AIProviderError("openai", "OpenAI library not installed")
    
    async def generate_text(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> str:
        """Generate text using OpenAI."""
        try:
            messages = []
            
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            
            messages.append({"role": "user", "content": prompt})
            
            response = await self.client.chat.completions.create(
                model=kwargs.get("model", self.config["model"]),
                messages=messages,
                temperature=temperature or self.config["temperature"],
                max_tokens=max_tokens,
                **{k: v for k, v in kwargs.items() if k not in ["model"]}
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"OpenAI generation failed: {str(e)}")
            raise AIProviderError("openai", str(e))
    
    async def generate_structured_output(
        self,
        prompt: str,
        schema: Dict[str, Any],
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate structured output using OpenAI."""
        try:
            import json
            
            # Add schema instruction to system prompt
            schema_instruction = f"\nPlease respond with valid JSON that matches this schema: {json.dumps(schema)}"
            full_system_prompt = (system_prompt or "") + schema_instruction
            
            response_text = await self.generate_text(
                prompt=prompt,
                system_prompt=full_system_prompt,
                **kwargs
            )
            
            # Parse JSON response
            try:
                return json.loads(response_text)
            except json.JSONDecodeError:
                # Try to extract JSON from response
                import re
                json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
                if json_match:
                    return json.loads(json_match.group())
                else:
                    raise AIProviderError("openai", "Failed to parse JSON response")
                    
        except AIProviderError:
            raise
        except Exception as e:
            logger.error(f"OpenAI structured generation failed: {str(e)}")
            raise AIProviderError("openai", str(e))
    
    async def health_check(self) -> bool:
        """Check OpenAI health."""
        try:
            # Try to list models first (lightweight operation)
            try:
                models_response = await self.client.models.list()
                if not models_response.data:
                    return False

                # Use the first available model for testing, or fallback to gpt-3.5-turbo
                available_models = [model.id for model in models_response.data]
                test_model = None

                # Prefer common models for testing
                preferred_models = ["gpt-4o-mini", "gpt-4o"]
                for model in preferred_models:
                    if model in available_models:
                        test_model = model
                        break

                # If no preferred model found, use the first available
                if not test_model and available_models:
                    test_model = available_models[0]

                if not test_model:
                    return False

            except Exception:
                # If models.list fails, try with default model
                test_model = "gpt-3.5-turbo"

            # Simple test request with minimal tokens
            await self.client.chat.completions.create(
                model=test_model,
                messages=[{"role": "user", "content": "Hi"}],
                max_tokens=1,
                timeout=10.0
            )
            return True
        except Exception as e:
            logger.debug(f"OpenAI health check failed: {str(e)}")
            return False

    async def generate_text_stream(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        **kwargs
    ):
        """Generate text using OpenAI streaming."""
        try:
            messages = []

            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})

            messages.append({"role": "user", "content": prompt})

            stream = await self.client.chat.completions.create(
                model=kwargs.get("model", self.config["model"]),
                messages=messages,
                temperature=temperature or self.config["temperature"],
                max_tokens=max_tokens,
                stream=True,
                **{k: v for k, v in kwargs.items() if k not in ["model"]}
            )

            async for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    yield chunk.choices[0].delta.content

        except Exception as e:
            logger.error(f"OpenAI streaming generation failed: {str(e)}")
            raise AIProviderError("openai", str(e))


class AnthropicProvider(AIProvider):
    """Anthropic provider implementation."""

    def __init__(self, api_key: str, base_url: Optional[str] = None, model: Optional[str] = None, temperature: Optional[float] = None):
        """Initialize Anthropic provider with user-specific configuration.

        Args:
            api_key: The Anthropic API key to use
            base_url: Optional custom base URL (for proxies or compatible APIs)
            model: Optional default model to use
            temperature: Optional default temperature
        """
        config = {
            "api_key": api_key,
            "model": model or "claude-3-sonnet-20240229",
            "temperature": temperature or 0.7,
            "base_url": base_url
        }
        super().__init__(config)

        if not api_key:
            raise AIProviderError("anthropic", "API key is required")

        try:
            from anthropic import AsyncAnthropic
            # Use custom base_url if provided, otherwise use default
            client_kwargs = {"api_key": api_key}
            if base_url:
                client_kwargs["base_url"] = base_url
            self.client = AsyncAnthropic(**client_kwargs)
        except ImportError:
            raise AIProviderError("anthropic", "Anthropic library not installed")
    
    async def generate_text(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> str:
        """Generate text using Anthropic."""
        try:
            response = await self.client.messages.create(
                model=kwargs.get("model", self.config["model"]),
                system=system_prompt or "",
                messages=[{"role": "user", "content": prompt}],
                temperature=temperature or self.config["temperature"],
                max_tokens=max_tokens or 4000,
                **{k: v for k, v in kwargs.items() if k not in ["model"]}
            )
            
            return response.content[0].text
            
        except Exception as e:
            logger.error(f"Anthropic generation failed: {str(e)}")
            raise AIProviderError("anthropic", str(e))
    
    async def generate_structured_output(
        self,
        prompt: str,
        schema: Dict[str, Any],
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate structured output using Anthropic."""
        try:
            import json
            
            # Add schema instruction to system prompt
            schema_instruction = f"\nPlease respond with valid JSON that matches this schema: {json.dumps(schema)}"
            full_system_prompt = (system_prompt or "") + schema_instruction
            
            response_text = await self.generate_text(
                prompt=prompt,
                system_prompt=full_system_prompt,
                **kwargs
            )
            
            # Parse JSON response
            try:
                return json.loads(response_text)
            except json.JSONDecodeError:
                # Try to extract JSON from response
                import re
                json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
                if json_match:
                    return json.loads(json_match.group())
                else:
                    raise AIProviderError("anthropic", "Failed to parse JSON response")
                    
        except AIProviderError:
            raise
        except Exception as e:
            logger.error(f"Anthropic structured generation failed: {str(e)}")
            raise AIProviderError("anthropic", str(e))
    
    async def health_check(self) -> bool:
        """Check Anthropic health."""
        try:
            # Try different Claude models in order of preference
            test_models = [
                "claude-3-haiku-20240307",  # Fastest and cheapest
                "claude-3-sonnet-20240229", # Good balance
                "claude-3-opus-20240229",   # Most capable
                "claude-2.1",               # Fallback
                "claude-2.0"                # Older fallback
            ]

            last_error = None
            for model in test_models:
                try:
                    # Simple test request with minimal tokens
                    await self.client.messages.create(
                        model=model,
                        messages=[{"role": "user", "content": "Hi"}],
                        max_tokens=1,
                        timeout=10.0
                    )
                    return True
                except Exception as e:
                    last_error = e
                    # If it's an authentication error, don't try other models
                    if "authentication" in str(e).lower() or "api_key" in str(e).lower():
                        break
                    continue

            logger.debug(f"Anthropic health check failed: {str(last_error)}")
            return False
        except Exception as e:
            logger.debug(f"Anthropic health check failed: {str(e)}")
            return False


class GoogleProvider(AIProvider):
    """Google provider implementation."""

    def __init__(self, api_key: str, base_url: Optional[str] = None, model: Optional[str] = None, temperature: Optional[float] = None):
        """Initialize Google provider with user-specific configuration.

        Args:
            api_key: The Google API key to use
            base_url: Optional custom base URL (for proxies or compatible APIs)
            model: Optional default model to use
            temperature: Optional default temperature
        """
        config = {
            "api_key": api_key,
            "model": model or "gemini-pro",
            "temperature": temperature or 0.7,
            "base_url": base_url
        }
        super().__init__(config)

        if not api_key:
            raise AIProviderError("google", "API key is required")

        # Google uses REST API, we'll implement basic HTTP client
        self.base_url = base_url or "https://generativelanguage.googleapis.com/v1"
        self.api_key = api_key

    async def generate_text(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> str:
        """Generate text using Google Gemini."""
        try:
            import httpx

            # Combine system and user prompts
            full_prompt = prompt
            if system_prompt:
                full_prompt = f"{system_prompt}\n\n{prompt}"

            url = f"{self.base_url}/models/gemini-pro:generateContent"
            headers = {"Content-Type": "application/json"}

            data = {
                "contents": [{"parts": [{"text": full_prompt}]}],
                "generationConfig": {
                    "temperature": temperature or self.config["temperature"],
                    "maxOutputTokens": max_tokens or 2048
                }
            }

            async with httpx.AsyncClient() as client:
                response = await client.post(
                    url,
                    json=data,
                    headers=headers,
                    params={"key": self.api_key},
                    timeout=30.0
                )
                response.raise_for_status()

                result = response.json()
                if "candidates" in result and result["candidates"]:
                    return result["candidates"][0]["content"]["parts"][0]["text"]
                else:
                    raise AIProviderError("google", "No response generated")

        except Exception as e:
            logger.error(f"Google generation failed: {str(e)}")
            raise AIProviderError("google", str(e))

    async def generate_structured_output(
        self,
        prompt: str,
        schema: Dict[str, Any],
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate structured output using Google Gemini."""
        try:
            import json

            # Add schema instruction to system prompt
            schema_instruction = f"\nPlease respond with valid JSON that matches this schema: {json.dumps(schema)}"
            full_system_prompt = (system_prompt or "") + schema_instruction

            response_text = await self.generate_text(
                prompt=prompt,
                system_prompt=full_system_prompt,
                **kwargs
            )

            # Parse JSON response
            try:
                return json.loads(response_text)
            except json.JSONDecodeError:
                # Try to extract JSON from response
                import re
                json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
                if json_match:
                    return json.loads(json_match.group())
                else:
                    raise AIProviderError("google", "Failed to parse JSON response")

        except AIProviderError:
            raise
        except Exception as e:
            logger.error(f"Google structured generation failed: {str(e)}")
            raise AIProviderError("google", str(e))

    async def health_check(self) -> bool:
        """Check Google Gemini health."""
        try:
            import httpx

            # Try different Gemini models in order of preference
            test_models = [
                "gemini-pro",           # Most common
                "gemini-1.5-pro",       # Latest version
                "gemini-1.5-flash",     # Faster version
                "gemini-pro-vision"     # Fallback
            ]

            headers = {"Content-Type": "application/json"}
            data = {
                "contents": [{"parts": [{"text": "Hi"}]}],
                "generationConfig": {"maxOutputTokens": 1}
            }

            last_error = None
            async with httpx.AsyncClient() as client:
                for model in test_models:
                    try:
                        url = f"{self.base_url}/models/{model}:generateContent"
                        response = await client.post(
                            url,
                            json=data,
                            headers=headers,
                            params={"key": self.api_key},
                            timeout=10.0
                        )
                        if response.status_code == 200:
                            return True
                        else:
                            last_error = f"HTTP {response.status_code}: {response.text}"
                            # If it's an authentication error, don't try other models
                            if response.status_code in [401, 403]:
                                break
                    except Exception as e:
                        last_error = str(e)
                        continue

            logger.debug(f"Google health check failed: {last_error}")
            return False
        except Exception as e:
            logger.debug(f"Google health check failed: {str(e)}")
            return False


class AIProviderManager:
    """Manager for AI providers."""

    def __init__(self):
        # No longer maintain global providers - all providers are created on-demand with user keys
        self.supported_providers = ["openai", "anthropic", "google", "custom"]
        logger.info("AI Provider Manager initialized - providers will be created with user-specific API keys")

    def get_supported_providers(self) -> List[str]:
        """Get list of supported provider names."""
        return self.supported_providers.copy()

    def is_provider_supported(self, provider_name: str) -> bool:
        """Check if a provider is supported."""
        return provider_name.lower() in self.supported_providers

    def create_provider_with_config(
        self,
        provider_name: str,
        api_key: str,
        base_url: Optional[str] = None,
        model: Optional[str] = None,
        temperature: Optional[float] = None
    ) -> AIProvider:
        """Create a provider instance with user-specific configuration.

        Args:
            provider_name: Name of the provider (openai, anthropic, google, custom)
                          'custom' is treated as OpenAI-compatible with custom base_url
            api_key: User's API key for the provider
            base_url: Optional custom base URL
            model: Optional default model to use
            temperature: Optional default temperature

        Returns:
            AIProvider instance configured with user's settings
        """
        provider_name = provider_name.lower()

        if provider_name == "openai" or provider_name == "custom":
            # 'custom' provider is treated as OpenAI-compatible with custom base_url
            return OpenAIProvider(
                api_key=api_key,
                base_url=base_url,
                model=model,
                temperature=temperature
            )
        elif provider_name == "anthropic":
            return AnthropicProvider(
                api_key=api_key,
                base_url=base_url,
                model=model,
                temperature=temperature
            )
        elif provider_name == "google":
            return GoogleProvider(
                api_key=api_key,
                base_url=base_url,
                model=model,
                temperature=temperature
            )
        else:
            raise AIProviderError(provider_name, f"Provider '{provider_name}' not supported")

    async def health_check_provider(self, provider_name: str, api_key: str, base_url: Optional[str] = None) -> bool:
        """Health check a specific provider with user's API key.

        Args:
            provider_name: Name of the provider to test
            api_key: User's API key
            base_url: Optional custom base URL

        Returns:
            True if provider is healthy, False otherwise
        """
        try:
            provider = self.create_provider_with_config(provider_name, api_key, base_url)
            return await provider.health_check()
        except Exception as e:
            logger.error(f"Health check failed for {provider_name}: {str(e)}")
            return False


# Global provider manager instance
provider_manager = AIProviderManager()


def create_ai_provider(provider_name: str, api_key: str, base_url: Optional[str] = None, model: Optional[str] = None, temperature: Optional[float] = None) -> AIProvider:
    """Create an AI provider instance with user-specific configuration.

    Args:
        provider_name: Name of the provider (openai, anthropic, google, custom)
                      'custom' is treated as OpenAI-compatible with custom base_url
        api_key: User's API key for the provider
        base_url: Optional custom base URL
        model: Optional default model to use
        temperature: Optional default temperature

    Returns:
        AIProvider instance configured with user's settings
    """
    return provider_manager.create_provider_with_config(
        provider_name=provider_name,
        api_key=api_key,
        base_url=base_url,
        model=model,
        temperature=temperature
    )
