"""
User AI service for managing user-specific AI providers and operations.
"""

from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.models.settings import APIKey, APIKeyStatus, decrypt_api_key
from app.models.user import User
from app.services.ai_providers import AIProvider, create_ai_provider
from app.core.exceptions import AIProviderError
from app.core.logging import get_logger

logger = get_logger("user_ai_service")


class UserAIService:
    """Service for managing user-specific AI operations."""
    
    def __init__(self, db: AsyncSession, user: User):
        self.db = db
        self.user = user
    
    async def get_user_api_keys(self, provider: Optional[str] = None, active_only: bool = True) -> List[APIKey]:
        """Get user's API keys, optionally filtered by provider.

        Args:
            provider: Optional provider name to filter by (ignored in simplified model)
            active_only: Whether to only return active keys

        Returns:
            List of user's API keys
        """
        query = select(APIKey).where(APIKey.user_id == self.user.id)

        # Note: provider filtering removed in simplified API key model
        # All API keys are generic and provider is determined at usage time

        if active_only:
            query = query.where(APIKey.status == APIKeyStatus.ACTIVE)

        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_api_key_by_id(self, key_id: int) -> Optional[APIKey]:
        """Get a specific API key by ID (must belong to user).
        
        Args:
            key_id: ID of the API key
            
        Returns:
            APIKey if found and belongs to user, None otherwise
        """
        result = await self.db.execute(
            select(APIKey).where(
                APIKey.id == key_id,
                APIKey.user_id == self.user.id
            )
        )
        return result.scalar_one_or_none()
    
    async def get_default_api_key(self, provider: str = None) -> Optional[APIKey]:
        """Get user's default API key.

        Args:
            provider: Provider name (ignored in simplified model)

        Returns:
            First active API key, or None
        """
        api_keys = await self.get_user_api_keys(active_only=True)
        return api_keys[0] if api_keys else None
    
    async def create_provider_for_key(self, api_key: APIKey, provider: str = "openai") -> AIProvider:
        """Create an AI provider instance using a specific API key.

        Args:
            api_key: The API key to use
            provider: Provider name to use (default: openai)

        Returns:
            Configured AIProvider instance

        Raises:
            AIProviderError: If provider creation fails
        """
        if not api_key.encrypted_key:
            raise AIProviderError(
                provider,
                "API key was created before encryption support. Please recreate the key."
            )

        # Decrypt the API key
        decrypted_key = decrypt_api_key(api_key.encrypted_key)
        if not decrypted_key:
            raise AIProviderError(
                provider,
                "Failed to decrypt API key"
            )

        # Create provider with user's configuration
        return create_ai_provider(
            provider_name=provider,
            api_key=decrypted_key,
            # Could add model and temperature from user preferences in the future
        )
    
    async def create_provider_for_provider_name(self, provider: str) -> AIProvider:
        """Create an AI provider instance using user's default API key for a provider.
        
        Args:
            provider: Provider name (openai, anthropic, google)
            
        Returns:
            Configured AIProvider instance
            
        Raises:
            AIProviderError: If no API key found or provider creation fails
        """
        api_key = await self.get_default_api_key(provider)
        if not api_key:
            raise AIProviderError(
                provider,
                f"No active API key found for {provider}. Please add an API key in settings."
            )
        
        return await self.create_provider_for_key(api_key, provider)
    
    async def get_available_providers(self) -> List[str]:
        """Get list of providers for which the user has active API keys.

        Returns:
            List of provider names (simplified to common providers)
        """
        api_keys = await self.get_user_api_keys(active_only=True)
        # In simplified model, we return common providers if user has any API keys
        if api_keys:
            return ["openai", "anthropic", "google"]  # Common providers
        return []
    
    async def test_api_key_connection(self, api_key: APIKey, provider: str = "openai") -> Dict[str, Any]:
        """Test connection for a specific API key.

        Args:
            api_key: The API key to test
            provider: Provider to test with (default: openai)

        Returns:
            Dictionary with test results
        """
        try:
            # In simplified model, we need to specify the provider for testing
            from app.services.ai_providers import create_ai_provider
            from app.models.settings import decrypt_api_key

            decrypted_key = decrypt_api_key(api_key.encrypted_key)
            ai_provider = create_ai_provider(
                provider_name=provider,
                api_key=decrypted_key
            )
            health_status = await ai_provider.health_check()

            return {
                "success": health_status,
                "provider": provider,
                "base_url": "default",
                "message": f"{provider.title()} API key is working correctly" if health_status
                          else f"{provider.title()} API key test failed - authentication or connection error"
            }
        except Exception as e:
            return {
                "success": False,
                "provider": provider,
                "base_url": "default",
                "message": f"API key test failed: {str(e)}",
                "error": str(e)
            }
    
    async def generate_text(
        self,
        prompt: str,
        provider: Optional[str] = None,
        api_key_id: Optional[int] = None,
        system_prompt: Optional[str] = None,
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        base_url: Optional[str] = None,
        **kwargs
    ) -> str:
        """Generate text using user's AI provider.

        Args:
            prompt: The text prompt
            provider: Optional provider name (if not specified, uses first available)
            api_key_id: Optional specific API key ID to use
            system_prompt: Optional system prompt
            model: Optional model override
            temperature: Optional temperature override
            max_tokens: Optional max tokens override
            base_url: Optional base URL override
            **kwargs: Additional provider-specific parameters

        Returns:
            Generated text

        Raises:
            AIProviderError: If no provider available or generation fails
        """
        # Determine which API key to use
        if api_key_id:
            api_key = await self.get_api_key_by_id(api_key_id)
            if not api_key:
                raise AIProviderError("user", f"API key {api_key_id} not found")
        elif provider:
            api_key = await self.get_default_api_key(provider)
            if not api_key:
                raise AIProviderError(provider, f"No API key found for {provider}")
        else:
            # Use first available provider
            available_providers = await self.get_available_providers()
            if not available_providers:
                raise AIProviderError("user", "No API keys configured. Please add an API key in settings.")
            api_key = await self.get_default_api_key(available_providers[0])

        # Determine provider to use (default to provider parameter or openai)
        provider_to_use = provider or "openai"

        # Create provider with custom configuration if provided
        if base_url or model or temperature is not None:
            # Use custom configuration - create provider with overrides
            from app.services.ai_providers import create_ai_provider
            decrypted_key = decrypt_api_key(api_key.encrypted_key)
            ai_provider = create_ai_provider(
                provider_name=provider_to_use,
                api_key=decrypted_key,
                base_url=base_url,
                model=model,
                temperature=temperature
            )
        else:
            # Use default configuration from API key
            ai_provider = await self.create_provider_for_key(api_key, provider_to_use)

        return await ai_provider.generate_text(
            prompt=prompt,
            system_prompt=system_prompt,
            temperature=temperature,
            max_tokens=max_tokens,
            **kwargs
        )
    
    async def generate_structured_output(
        self,
        prompt: str,
        schema: Dict[str, Any],
        provider: Optional[str] = None,
        api_key_id: Optional[int] = None,
        system_prompt: Optional[str] = None,
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        base_url: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate structured output using user's AI provider.

        Args:
            prompt: The text prompt
            schema: JSON schema for the expected output
            provider: Optional provider name
            api_key_id: Optional specific API key ID to use
            system_prompt: Optional system prompt
            model: Optional model override
            temperature: Optional temperature override
            max_tokens: Optional max tokens override
            base_url: Optional base URL override
            **kwargs: Additional provider-specific parameters

        Returns:
            Generated structured data

        Raises:
            AIProviderError: If no provider available or generation fails
        """
        # Determine which API key to use (same logic as generate_text)
        if api_key_id:
            api_key = await self.get_api_key_by_id(api_key_id)
            if not api_key:
                raise AIProviderError("user", f"API key {api_key_id} not found")
        elif provider:
            api_key = await self.get_default_api_key(provider)
            if not api_key:
                raise AIProviderError(provider, f"No API key found for {provider}")
        else:
            # Use first available provider
            available_providers = await self.get_available_providers()
            if not available_providers:
                raise AIProviderError("user", "No API keys configured. Please add an API key in settings.")
            api_key = await self.get_default_api_key(available_providers[0])

        # Determine provider to use (default to provider parameter or openai)
        provider_to_use = provider or "openai"

        # Create provider with custom configuration if provided
        if base_url or model or temperature is not None:
            # Use custom configuration - create provider with overrides
            from app.services.ai_providers import create_ai_provider
            decrypted_key = decrypt_api_key(api_key.encrypted_key)
            ai_provider = create_ai_provider(
                provider_name=provider_to_use,
                api_key=decrypted_key,
                base_url=base_url,
                model=model,
                temperature=temperature
            )
        else:
            # Use default configuration from API key
            ai_provider = await self.create_provider_for_key(api_key, provider_to_use)

        return await ai_provider.generate_structured_output(
            prompt=prompt,
            schema=schema,
            system_prompt=system_prompt,
            max_tokens=max_tokens,
            **kwargs
        )

    async def generate_with_custom_config(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        base_url: Optional[str] = None,
        provider_override: Optional[str] = None,
        api_key_id: Optional[str] = None,
        **kwargs
    ) -> str:
        """Generate text with custom configuration - used by ConfigDrivenAgent.

        Args:
            prompt: The text prompt
            system_prompt: Optional system prompt
            model: Model to use
            temperature: Temperature setting
            max_tokens: Maximum tokens
            base_url: Custom base URL
            provider_override: Provider to use
            api_key_id: Specific API key ID to use
            **kwargs: Additional parameters

        Returns:
            Generated text

        Raises:
            AIProviderError: If generation fails
        """
        return await self.generate_text(
            prompt=prompt,
            provider=provider_override,
            api_key_id=int(api_key_id) if api_key_id else None,
            system_prompt=system_prompt,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            base_url=base_url,
            **kwargs
        )

    async def generate_text_stream(
        self,
        prompt: str,
        provider: Optional[str] = None,
        api_key_id: Optional[int] = None,
        system_prompt: Optional[str] = None,
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        base_url: Optional[str] = None,
        **kwargs
    ):
        """Generate text using streaming with user's AI provider.

        Args:
            prompt: The text prompt
            provider: Optional provider name (if not specified, uses first available)
            api_key_id: Optional specific API key ID to use
            system_prompt: Optional system prompt
            model: Optional model override
            temperature: Optional temperature override
            max_tokens: Optional max tokens override
            base_url: Optional base URL override
            **kwargs: Additional provider-specific parameters

        Yields:
            Text chunks as they are generated
        """
        if not self.user:
            raise AIProviderError("user_ai_service", "No current user set")

        # Determine which API key to use (same logic as generate_text)
        if api_key_id:
            api_key = await self.get_api_key_by_id(api_key_id)
            if not api_key:
                raise AIProviderError("user", f"API key {api_key_id} not found")
        elif provider:
            api_key = await self.get_default_api_key(provider)
            if not api_key:
                raise AIProviderError(provider, f"No API key found for {provider}")
        else:
            # Use first available provider
            available_providers = await self.get_available_providers()
            if not available_providers:
                raise AIProviderError("user", "No API keys configured. Please add an API key in settings.")
            api_key = await self.get_default_api_key(available_providers[0])

        # Determine provider to use (default to provider parameter or openai)
        provider_to_use = provider or "openai"

        # Create provider with custom configuration if provided
        if base_url or model or temperature is not None:
            # Use custom configuration - create provider with overrides
            from app.services.ai_providers import create_ai_provider
            decrypted_key = decrypt_api_key(api_key.encrypted_key)
            ai_provider = create_ai_provider(
                provider_name=provider_to_use,
                api_key=decrypted_key,
                base_url=base_url,
                model=model,
                temperature=temperature
            )
        else:
            # Use default configuration from API key
            ai_provider = await self.create_provider_for_key(api_key, provider_to_use)

        async for chunk in ai_provider.generate_text_stream(
            prompt=prompt,
            system_prompt=system_prompt,
            temperature=temperature,
            max_tokens=max_tokens,
            **kwargs
        ):
            yield chunk
