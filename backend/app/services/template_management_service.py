"""
Template management service for business logic operations.
"""

import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from app.core.exceptions import NotFoundError, ValidationError
from app.core.logging import get_logger
from app.models.planning import (
    Template, TemplateCreate, TemplateUpdate, TemplateResponse,
    TemplateCategory, TemplateDifficulty, TemplateVisibility, TemplateStatus
)
from app.models.user import User
from app.models.agent import Agent

logger = get_logger(__name__)


class TemplateManagementService:
    """Service for managing template business logic."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def validate_template_data(self, template_data: TemplateCreate) -> None:
        """Validate template data before creation."""
        # Validate required fields
        if not template_data.name or len(template_data.name.strip()) < 3:
            raise ValidationError("Template name must be at least 3 characters long")
        
        if not template_data.description or len(template_data.description.strip()) < 10:
            raise ValidationError("Template description must be at least 10 characters long")
        
        if not template_data.prompt_template or len(template_data.prompt_template.strip()) < 20:
            raise ValidationError("Prompt template must be at least 20 characters long")
        
        # Validate team structure
        if not template_data.team_structure_template:
            raise ValidationError("Team structure template is required")
        
        # Validate team structure has required fields
        team_structure = template_data.team_structure_template
        required_fields = ["team_name", "team_members"]
        for field in required_fields:
            if field not in team_structure:
                raise ValidationError(f"Team structure must include '{field}' field")
        
        # Validate team members
        team_members = team_structure.get("team_members", [])
        if not team_members or len(team_members) == 0:
            raise ValidationError("Template must have at least one team member")
        
        for i, member in enumerate(team_members):
            if not isinstance(member, dict):
                raise ValidationError(f"Team member {i+1} must be an object")
            
            required_member_fields = ["name", "role", "description"]
            for field in required_member_fields:
                if field not in member or not member[field]:
                    raise ValidationError(f"Team member {i+1} must have '{field}' field")
        
        # Validate tags
        if template_data.tags:
            if len(template_data.tags) > 10:
                raise ValidationError("Template cannot have more than 10 tags")
            
            for tag in template_data.tags:
                if not tag or len(tag.strip()) < 2:
                    raise ValidationError("Each tag must be at least 2 characters long")
                if len(tag) > 50:
                    raise ValidationError("Each tag must be less than 50 characters long")
    
    async def check_template_name_uniqueness(
        self, 
        name: str, 
        user_id: int, 
        exclude_template_id: Optional[str] = None
    ) -> bool:
        """Check if template name is unique for the user."""
        query = """
            SELECT COUNT(*) as count FROM templates 
            WHERE name = :name AND user_id = :user_id AND status != 'archived'
        """
        params = {"name": name, "user_id": user_id}
        
        if exclude_template_id:
            query += " AND template_id != :exclude_template_id"
            params["exclude_template_id"] = exclude_template_id
        
        result = await self.db.execute(text(query), params)
        count = result.scalar()
        return count == 0
    
    async def create_template_from_agent(
        self,
        agent_id: str,
        template_data: Dict[str, Any],
        user: User
    ) -> Template:
        """Create a complete, deployable template from an existing agent."""
        # Validate agent exists and belongs to user
        agent_query = """
            SELECT * FROM agents
            WHERE agent_id = :agent_id AND user_id = :user_id
        """
        result = await self.db.execute(
            text(agent_query),
            {"agent_id": agent_id, "user_id": user.id}
        )
        agent = result.fetchone()

        if not agent:
            raise NotFoundError("Agent", agent_id)

        # Extract agent data
        agent_dict = {column: getattr(agent, column) for column in agent._fields}

        # Check name uniqueness
        if not await self.check_template_name_uniqueness(template_data["name"], user.id):
            raise ValidationError(f"Template name '{template_data['name']}' already exists")

        # Generate unique template ID
        template_id = f"template_{uuid.uuid4().hex[:12]}"

        # Extract and enhance team configuration for complete deployability
        complete_team_config = self._extract_complete_team_config(agent_dict, template_data)

        # Create template from agent data with complete configuration
        template = Template(
            template_id=template_id,
            name=template_data["name"],
            description=template_data["description"],
            category=template_data["category"],
            difficulty=template_data["difficulty"],
            visibility=template_data.get("visibility", TemplateVisibility.PRIVATE),
            status=TemplateStatus.ACTIVE,
            prompt_template=agent_dict.get("prompt_template", ""),
            team_structure_template=complete_team_config,
            default_config=self._extract_default_config(agent_dict),
            tags=template_data.get("tags", []),
            keywords=template_data.get("keywords", []),
            use_case=template_data.get("use_case"),
            example_input=template_data.get("example_input"),
            expected_output=template_data.get("expected_output"),
            source_agent_id=agent_id,
            template_metadata={
                "created_from_agent": True,
                "source_agent_name": agent_dict.get("team_name", ""),
                "creation_method": "agent_conversion",
                "template_type": "complete_deployable",
                "ready_to_deploy": True,
                "extracted_at": datetime.now(timezone.utc).isoformat(),
                "completeness_verified": True,
            },
            user_id=user.id,
            author_name=user.name,
            usage_count=0,
            rating_count=0,
        )

        self.db.add(template)
        await self.db.commit()
        await self.db.refresh(template)

        logger.info(
            "Complete template created from agent",
            template_id=template_id,
            source_agent_id=agent_id,
            user_id=user.id,
            team_members_count=len(complete_team_config.get("team_members", [])),
            workflow_steps_count=len(complete_team_config.get("workflow", {}).get("steps", [])),
        )

        return template

    def _extract_complete_team_config(self, agent_dict: Dict[str, Any], template_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract complete team configuration from agent data."""
        team_plan = agent_dict.get("team_plan", {})

        # Ensure we have a complete team configuration
        complete_config = {
            "team_name": agent_dict.get("team_name", template_data["name"]),
            "description": agent_dict.get("description", template_data["description"]),
            "objective": template_data.get("use_case", "解决复杂问题"),
            "domain": template_data.get("category", "general"),
            "complexity": template_data.get("difficulty", "intermediate"),

            # Extract team members with complete information
            "team_members": self._extract_complete_team_members(team_plan, agent_dict),

            # Extract or create workflow
            "workflow": self._extract_complete_workflow(team_plan, agent_dict),

            # Configuration
            "configuration": {
                "execution_mode": "sequential",
                "timeout_per_step": 300,
                "max_iterations": 3,
                "error_handling": "graceful_degradation"
            }
        }

        return complete_config

    def _extract_complete_team_members(self, team_plan: Dict[str, Any], agent_dict: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract complete team member definitions."""
        team_members = []

        # Get team members from various possible sources
        members_sources = [
            team_plan.get("team_members", []),
            team_plan.get("specialists", []),
            agent_dict.get("team_members", []),
        ]

        raw_members = []
        for source in members_sources:
            if source and isinstance(source, list):
                raw_members = source
                break

        # Enhance each member with complete information
        for i, member in enumerate(raw_members):
            if isinstance(member, dict):
                complete_member = {
                    "name": member.get("name", f"团队成员{i+1}"),
                    "role": member.get("role", "specialist"),
                    "description": member.get("description", f"{member.get('name', f'成员{i+1}')}的职责描述"),
                    "system_prompt": member.get("system_prompt", self._generate_default_system_prompt(member)),
                    "capabilities": member.get("capabilities", ["问题分析", "解决方案设计"]),
                    "tools": member.get("tools", ["分析工具", "协作工具"]),
                    "model_config": member.get("model_config", {
                        "model": "gpt-4",
                        "temperature": 0.7,
                        "max_tokens": 2000
                    })
                }
                team_members.append(complete_member)

        # If no members found, create a default member
        if not team_members:
            team_members.append({
                "name": "专家助手",
                "role": "expert",
                "description": "专业的问题解决专家",
                "system_prompt": "你是一位专业的问题解决专家，能够分析复杂问题并提供有效的解决方案。",
                "capabilities": ["问题分析", "解决方案设计", "专业咨询"],
                "tools": ["分析框架", "决策工具"],
                "model_config": {
                    "model": "gpt-4",
                    "temperature": 0.7,
                    "max_tokens": 2000
                }
            })

        return team_members

    def _extract_complete_workflow(self, team_plan: Dict[str, Any], agent_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Extract or create complete workflow definition."""
        existing_workflow = team_plan.get("workflow", {})

        # If workflow exists, enhance it
        if existing_workflow and existing_workflow.get("steps"):
            steps = []
            for step in existing_workflow["steps"]:
                complete_step = {
                    "name": step.get("name", "处理步骤"),
                    "description": step.get("description", "处理和分析任务"),
                    "assignee": step.get("assignee", "专家助手"),
                    "inputs": step.get("inputs", ["任务描述"]),
                    "outputs": step.get("outputs", ["处理结果"]),
                    "dependencies": step.get("dependencies", []),
                    "timeout": step.get("timeout", 300)
                }
                steps.append(complete_step)

            return {
                "steps": steps,
                "coordination": existing_workflow.get("coordination", {
                    "orchestrator": steps[0]["assignee"] if steps else "专家助手",
                    "communication_style": "协作式对话",
                    "decision_making": "共识决策"
                })
            }

        # Create default workflow
        return {
            "steps": [
                {
                    "name": "问题分析",
                    "description": "分析和理解用户的问题或需求",
                    "assignee": "专家助手",
                    "inputs": ["用户问题", "相关信息"],
                    "outputs": ["问题分析", "解决方案"],
                    "dependencies": [],
                    "timeout": 300
                }
            ],
            "coordination": {
                "orchestrator": "专家助手",
                "communication_style": "协作式对话",
                "decision_making": "专家决策"
            }
        }

    def _generate_default_system_prompt(self, member: Dict[str, Any]) -> str:
        """Generate a default system prompt for a team member."""
        name = member.get("name", "助手")
        role = member.get("role", "专家")
        description = member.get("description", "专业助手")

        return f"""你是{name}，担任{role}角色。{description}

你的主要职责：
1. 认真分析用户的问题和需求
2. 基于你的专业知识提供有价值的见解
3. 与团队其他成员协作，共同解决问题
4. 确保提供的解决方案实用且可行

请始终保持专业、友好的态度，并根据具体情况调整你的回应风格。"""

    def _extract_default_config(self, agent_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Extract default configuration from agent."""
        return {
            "model_settings": {
                "default_model": "gpt-4",
                "default_temperature": 0.7,
                "default_max_tokens": 2000
            },
            "execution_settings": {
                "timeout": 300,
                "retry_count": 1,
                "error_handling": "graceful"
            },
            "ui_settings": {
                "show_thinking": True,
                "show_workflow": True,
                "enable_feedback": True
            }
        }

    async def transform_template_for_agent_creation(
        self, 
        template_id: str, 
        user: User,
        customizations: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Transform template data for agent creation."""
        # Get template
        query = """
            SELECT * FROM templates 
            WHERE template_id = :template_id 
            AND (visibility IN ('public', 'featured') OR user_id = :user_id)
            AND status = 'active'
        """
        result = await self.db.execute(
            text(query), 
            {"template_id": template_id, "user_id": user.id}
        )
        template = result.fetchone()

        if not template:
            raise NotFoundError("Template", template_id)
        
        template_dict = {column: getattr(template, column) for column in template._fields}
        
        # Base agent configuration from template
        agent_config = {
            "team_name": template_dict["name"],
            "description": template_dict["description"],
            "prompt_template": template_dict["prompt_template"],
            "team_plan": template_dict["team_structure_template"],
            "metadata": {
                "created_from_template": True,
                "template_id": template_id,
                "template_name": template_dict["name"],
                "template_version": template_dict["version"],
            }
        }
        
        # Parse JSON fields if they are strings
        team_structure = agent_config["team_plan"]
        if isinstance(team_structure, str):
            import json
            try:
                team_structure = json.loads(team_structure)
                agent_config["team_plan"] = team_structure
            except (json.JSONDecodeError, TypeError):
                team_structure = {}
                agent_config["team_plan"] = team_structure

        # Validate template completeness for direct deployment
        if not self._validate_template_completeness(team_structure):
            logger.warning(f"Template {template_id} is not complete enough for direct deployment")
            # Still allow creation but mark as requiring AI generation
            agent_config["metadata"]["requires_ai_generation"] = True
            agent_config["metadata"]["ready_to_deploy"] = False
        else:
            # Template is complete - can be deployed directly
            agent_config["metadata"]["requires_ai_generation"] = False
            agent_config["metadata"]["ready_to_deploy"] = True
            agent_config["team_members"] = self._extract_team_members_for_agent(team_structure)
            agent_config["status"] = "active"  # Ready to use immediately

        # Apply customizations if provided
        if customizations:
            agent_config = self._apply_customizations(agent_config, customizations)
            agent_config["metadata"]["customizations"] = customizations

        # Update template usage count
        await self.db.execute(
            text("UPDATE templates SET usage_count = usage_count + 1 WHERE template_id = :template_id"),
            {"template_id": template_id}
        )
        await self.db.commit()

        logger.info(
            "Template transformed for agent creation",
            template_id=template_id,
            user_id=user.id,
            ready_to_deploy=agent_config["metadata"]["ready_to_deploy"],
            has_customizations=bool(customizations),
        )

        return agent_config

    def _validate_template_completeness(self, team_structure: Dict[str, Any]) -> bool:
        """Validate that template is complete enough for direct deployment."""
        required_fields = ["team_members"]

        for field in required_fields:
            if field not in team_structure:
                return False

        # Check team members completeness
        team_members = team_structure.get("team_members", [])
        if not team_members:
            return False

        for member in team_members:
            if not isinstance(member, dict):
                return False
            if not all(key in member for key in ["name", "role", "system_prompt"]):
                return False

        return True

    def _extract_team_members_for_agent(self, team_structure: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract team members in the format expected by Agent model."""
        team_members = team_structure.get("team_members", [])

        # Convert to Agent-compatible format
        agent_members = []
        for member in team_members:
            agent_member = {
                "name": member.get("name"),
                "role": member.get("role"),
                "description": member.get("description"),
                "system_prompt": member.get("system_prompt"),
            }
            # Add optional fields if present
            if "capabilities" in member:
                agent_member["capabilities"] = member["capabilities"]
            if "tools" in member:
                agent_member["tools"] = member["tools"]
            if "model_config" in member:
                agent_member["model_config"] = member["model_config"]

            agent_members.append(agent_member)

        return agent_members

    def _apply_customizations(self, agent_config: Dict[str, Any], customizations: Dict[str, Any]) -> Dict[str, Any]:
        """Apply user customizations to agent configuration."""
        # Apply basic customizations
        if "team_name" in customizations:
            agent_config["team_name"] = customizations["team_name"]

        if "description" in customizations:
            agent_config["description"] = customizations["description"]

        # Apply team member customizations
        if "team_members" in customizations:
            custom_members = customizations["team_members"]
            if isinstance(custom_members, list):
                for i, custom_member in enumerate(custom_members):
                    if i < len(agent_config.get("team_members", [])):
                        agent_config["team_members"][i].update(custom_member)

                        # Also update in team_plan
                        if "team_plan" in agent_config and "team_members" in agent_config["team_plan"]:
                            if i < len(agent_config["team_plan"]["team_members"]):
                                agent_config["team_plan"]["team_members"][i].update(custom_member)

        return agent_config

    async def validate_template_completeness(self, template_id: str, user: User) -> Dict[str, Any]:
        """验证模板的完整性和可部署性"""
        try:
            # Get template
            query = """
                SELECT * FROM templates
                WHERE template_id = :template_id
                AND (visibility IN ('public', 'featured') OR user_id = :user_id)
                AND status = 'active'
            """
            result = await self.db.execute(
                text(query),
                {"template_id": template_id, "user_id": user.id}
            )
            template = result.fetchone()

            if not template:
                raise NotFoundError("Template", template_id)

            template_dict = {column: getattr(template, column) for column in template._fields}

            # Parse team structure
            team_structure = template_dict["team_structure_template"]
            if isinstance(team_structure, str):
                import json
                try:
                    team_structure = json.loads(team_structure)
                except (json.JSONDecodeError, TypeError):
                    team_structure = {}

            # Perform comprehensive validation
            validation_result = {
                "template_id": template_id,
                "template_name": template_dict["name"],
                "is_complete": True,
                "is_deployable": True,
                "validation_score": 0.0,
                "issues": [],
                "warnings": [],
                "suggestions": [],
                "details": {}
            }

            # Check basic information
            basic_score = self._validate_basic_info(template_dict, validation_result)

            # Check team members
            members_score = self._validate_team_members(team_structure, validation_result)

            # Check workflow
            workflow_score = self._validate_workflow_completeness(team_structure, validation_result)

            # Check configuration
            config_score = self._validate_configuration_completeness(team_structure, validation_result)

            # Calculate overall score
            validation_result["validation_score"] = (basic_score + members_score + workflow_score + config_score) / 4

            # Determine if template is complete and deployable
            validation_result["is_complete"] = validation_result["validation_score"] >= 0.8
            validation_result["is_deployable"] = (
                validation_result["is_complete"] and
                len(validation_result["issues"]) == 0 and
                members_score >= 0.9
            )

            # Update template metadata if needed
            if validation_result["is_deployable"] != template_dict.get("template_metadata", {}).get("ready_to_deploy", False):
                await self._update_template_readiness(template_id, validation_result["is_deployable"])

            logger.info(
                "Template validation completed",
                template_id=template_id,
                validation_score=validation_result["validation_score"],
                is_deployable=validation_result["is_deployable"],
                issues_count=len(validation_result["issues"]),
            )

            return validation_result

        except NotFoundError:
            raise
        except Exception as e:
            logger.error(f"Template validation failed: {str(e)}")
            raise ValidationError(f"Template validation failed: {str(e)}")

    def _validate_basic_info(self, template_dict: Dict, validation_result: Dict) -> float:
        """验证基本信息"""
        score = 1.0

        if not template_dict.get("name", "").strip():
            validation_result["issues"].append("模板名称不能为空")
            score -= 0.3

        if not template_dict.get("description", "").strip():
            validation_result["issues"].append("模板描述不能为空")
            score -= 0.3
        elif len(template_dict["description"]) < 20:
            validation_result["warnings"].append("模板描述过短，建议至少20个字符")
            score -= 0.1

        if not template_dict.get("category", "").strip():
            validation_result["warnings"].append("建议设置模板分类")
            score -= 0.1

        validation_result["details"]["basic_info"] = {
            "score": score,
            "has_name": bool(template_dict.get("name", "").strip()),
            "has_description": bool(template_dict.get("description", "").strip()),
            "has_category": bool(template_dict.get("category", "").strip()),
        }

        return max(0.0, score)

    def _validate_team_members(self, team_structure: Dict, validation_result: Dict) -> float:
        """验证团队成员配置"""
        score = 1.0
        team_members = team_structure.get("team_members", [])

        if not team_members:
            validation_result["issues"].append("模板必须包含至少一个团队成员")
            return 0.0

        member_issues = []
        for i, member in enumerate(team_members):
            member_score = 1.0

            if not isinstance(member, dict):
                member_issues.append(f"成员 {i+1}: 格式错误")
                member_score = 0.0
                continue

            # Check required fields
            if not member.get("name", "").strip():
                member_issues.append(f"成员 {i+1}: 缺少名称")
                member_score -= 0.4

            if not member.get("role", "").strip():
                member_issues.append(f"成员 {i+1}: 缺少角色")
                member_score -= 0.3

            if not member.get("system_prompt", "").strip():
                member_issues.append(f"成员 {i+1}: 缺少系统提示词")
                member_score -= 0.3
            elif len(member["system_prompt"]) < 50:
                validation_result["warnings"].append(f"成员 {member.get('name', i+1)}: 系统提示词过短")
                member_score -= 0.1

            # Check optional but recommended fields
            if not member.get("capabilities"):
                validation_result["suggestions"].append(f"成员 {member.get('name', i+1)}: 建议定义能力列表")
                member_score -= 0.05

            if not member.get("tools"):
                validation_result["suggestions"].append(f"成员 {member.get('name', i+1)}: 建议定义工具列表")
                member_score -= 0.05

            score = min(score, member_score)

        validation_result["issues"].extend(member_issues)

        validation_result["details"]["team_members"] = {
            "score": score,
            "count": len(team_members),
            "all_have_names": all(m.get("name", "").strip() for m in team_members if isinstance(m, dict)),
            "all_have_roles": all(m.get("role", "").strip() for m in team_members if isinstance(m, dict)),
            "all_have_prompts": all(m.get("system_prompt", "").strip() for m in team_members if isinstance(m, dict)),
        }

        return max(0.0, score)

    def _validate_workflow_completeness(self, team_structure: Dict, validation_result: Dict) -> float:
        """验证工作流程完整性"""
        score = 1.0
        workflow = team_structure.get("workflow", {})

        if not workflow:
            validation_result["warnings"].append("建议定义工作流程以提高模板可用性")
            score = 0.5
        else:
            steps = workflow.get("steps", [])
            if not steps:
                validation_result["warnings"].append("工作流程中没有定义步骤")
                score = 0.6
            else:
                # Validate steps
                team_member_names = {m.get("name") for m in team_structure.get("team_members", []) if isinstance(m, dict)}

                for i, step in enumerate(steps):
                    if not isinstance(step, dict):
                        validation_result["warnings"].append(f"工作流程步骤 {i+1}: 格式错误")
                        score -= 0.1
                        continue

                    if not step.get("name", "").strip():
                        validation_result["warnings"].append(f"工作流程步骤 {i+1}: 缺少名称")
                        score -= 0.1

                    if not step.get("assignee", "").strip():
                        validation_result["warnings"].append(f"工作流程步骤 {i+1}: 缺少负责人")
                        score -= 0.1
                    elif step["assignee"] not in team_member_names:
                        validation_result["warnings"].append(f"工作流程步骤 {i+1}: 负责人不在团队成员中")
                        score -= 0.1

        validation_result["details"]["workflow"] = {
            "score": score,
            "has_workflow": bool(workflow),
            "has_steps": bool(workflow.get("steps")),
            "steps_count": len(workflow.get("steps", [])),
        }

        return max(0.0, score)

    def _validate_configuration_completeness(self, team_structure: Dict, validation_result: Dict) -> float:
        """验证配置完整性"""
        score = 1.0

        # Check if basic configuration exists
        config = team_structure.get("configuration", {})
        if not config:
            validation_result["suggestions"].append("建议添加执行配置以优化性能")
            score = 0.8

        validation_result["details"]["configuration"] = {
            "score": score,
            "has_config": bool(config),
        }

        return score

    async def _update_template_readiness(self, template_id: str, is_ready: bool):
        """更新模板的就绪状态"""
        try:
            # Get current metadata
            query = "SELECT template_metadata FROM templates WHERE template_id = :template_id"
            result = await self.db.execute(text(query), {"template_id": template_id})
            row = result.fetchone()

            if row:
                metadata = row.template_metadata or {}
                if isinstance(metadata, str):
                    import json
                    try:
                        metadata = json.loads(metadata)
                    except (json.JSONDecodeError, TypeError):
                        metadata = {}

                # Update readiness flags
                metadata.update({
                    "ready_to_deploy": is_ready,
                    "requires_ai_generation": not is_ready,
                    "last_validated": datetime.now(timezone.utc).isoformat(),
                    "template_type": "complete_deployable" if is_ready else "user_created"
                })

                # Update in database
                update_query = """
                    UPDATE templates
                    SET template_metadata = :metadata
                    WHERE template_id = :template_id
                """
                await self.db.execute(
                    text(update_query),
                    {"template_id": template_id, "metadata": json.dumps(metadata)}
                )
                await self.db.commit()

                logger.info(f"Updated template readiness: {template_id} -> {is_ready}")
        except Exception as e:
            logger.error(f"Failed to update template readiness: {str(e)}")

    async def get_template_recommendations(
        self, 
        user: User, 
        category: Optional[TemplateCategory] = None,
        difficulty: Optional[TemplateDifficulty] = None,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """Get template recommendations for a user."""
        # Build recommendation query based on user's usage patterns
        conditions = ["visibility IN ('public', 'featured')", "status = 'active'"]
        params = {"user_id": user.id, "limit": limit}
        
        if category:
            conditions.append("category = :category")
            params["category"] = category
        
        if difficulty:
            conditions.append("difficulty = :difficulty")
            params["difficulty"] = difficulty
        
        # Get user's template usage history for personalization
        user_categories_query = """
            SELECT category, COUNT(*) as usage_count
            FROM templates t
            JOIN agents a ON a.team_plan->>'template_id' = t.template_id
            WHERE a.user_id = :user_id
            GROUP BY category
            ORDER BY usage_count DESC
            LIMIT 3
        """
        
        result = await self.db.execute(text(user_categories_query), {"user_id": user.id})
        preferred_categories = [row.category for row in result.fetchall()]
        
        # Build recommendation query with personalization
        where_clause = " AND ".join(conditions)
        
        # Boost templates in user's preferred categories
        order_clause = "ORDER BY "
        if preferred_categories:
            category_cases = []
            for i, cat in enumerate(preferred_categories):
                category_cases.append(f"WHEN category = '{cat}' THEN {i}")
            
            order_clause += f"CASE {' '.join(category_cases)} ELSE 999 END, "
        
        order_clause += """
            CASE WHEN visibility = 'featured' THEN 0 ELSE 1 END,
            (COALESCE(rating, 0) * COALESCE(usage_count, 0)) DESC,
            created_at DESC
        """
        
        query = f"""
            SELECT template_id, name, description, category, difficulty, 
                   rating, usage_count, tags, use_case
            FROM templates 
            WHERE {where_clause}
            {order_clause}
            LIMIT :limit
        """
        
        result = await self.db.execute(text(query), params)
        recommendations = result.fetchall()
        
        return [
            {
                "template_id": rec.template_id,
                "name": rec.name,
                "description": rec.description,
                "category": rec.category,
                "difficulty": rec.difficulty,
                "rating": rec.rating,
                "usage_count": rec.usage_count,
                "tags": rec.tags,
                "use_case": rec.use_case,
            }
            for rec in recommendations
        ]
    
    async def validate_template_permissions(
        self, 
        template_id: str, 
        user: User, 
        action: str = "read"
    ) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """Validate user permissions for template operations."""
        query = """
            SELECT * FROM templates 
            WHERE template_id = :template_id
        """
        result = await self.db.execute(text(query), {"template_id": template_id})
        template = result.fetchone()

        if not template:
            return False, None
        
        template_dict = {column: getattr(template, column) for column in template._fields}
        
        # Check permissions based on action
        if action == "read":
            # Can read if public/featured or owned by user
            can_access = (
                template_dict["visibility"] in ["public", "featured"] or 
                template_dict["user_id"] == user.id
            )
        elif action in ["write", "update", "delete"]:
            # Can modify only if owned by user
            can_access = template_dict["user_id"] == user.id
        else:
            can_access = False
        
        return can_access, template_dict if can_access else None
