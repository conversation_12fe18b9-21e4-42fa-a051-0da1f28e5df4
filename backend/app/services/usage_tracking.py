"""
API Key usage tracking service.
"""

from datetime import datetime, date
from typing import Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.models.settings import APIKey, APIKeyStatus
from app.core.logging import get_logger

logger = get_logger("usage_tracking")


class UsageTracker:
    """Service for tracking API key usage statistics."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def record_usage(
        self, 
        api_key_id: int, 
        requests: int = 1, 
        cost: float = 0.0,
        provider_response_time: Optional[float] = None
    ) -> bool:
        """Record usage for an API key."""
        try:
            # Get the API key
            result = await self.db.execute(
                select(APIKey).where(APIKey.id == api_key_id)
            )
            api_key = result.scalar_one_or_none()
            
            if not api_key:
                logger.warning(f"API key {api_key_id} not found for usage recording")
                return False
            
            # Check if key is active
            if api_key.status != APIKeyStatus.ACTIVE:
                logger.warning(f"Attempted to record usage for inactive API key {api_key_id}")
                return False
            
            # Reset daily/monthly counters if needed
            await self._reset_counters_if_needed(api_key)
            
            # Update usage statistics
            api_key.usage_count += requests
            api_key.requests_today += requests
            api_key.requests_month += requests
            api_key.cost_today += cost
            api_key.cost_month += cost
            api_key.last_used = datetime.now()
            
            await self.db.commit()
            await self.db.refresh(api_key)
            
            logger.debug(f"Recorded usage for API key {api_key_id}: {requests} requests, ${cost:.4f} cost")
            return True
            
        except Exception as e:
            logger.error(f"Failed to record usage for API key {api_key_id}: {str(e)}")
            await self.db.rollback()
            return False
    
    async def _reset_counters_if_needed(self, api_key: APIKey) -> None:
        """Reset daily/monthly counters if needed."""
        now = datetime.now()
        today = now.date()
        current_month = (now.year, now.month)
        
        # Reset daily counters
        if api_key.last_daily_reset is None or api_key.last_daily_reset.date() != today:
            api_key.requests_today = 0
            api_key.cost_today = 0.0
            api_key.last_daily_reset = now
        
        # Reset monthly counters
        last_month = None
        if api_key.last_monthly_reset:
            last_month = (api_key.last_monthly_reset.year, api_key.last_monthly_reset.month)
        
        if last_month is None or last_month != current_month:
            api_key.requests_month = 0
            api_key.cost_month = 0.0
            api_key.last_monthly_reset = now
    
    async def get_usage_stats(self, api_key_id: int) -> Optional[Dict[str, Any]]:
        """Get usage statistics for an API key."""
        try:
            result = await self.db.execute(
                select(APIKey).where(APIKey.id == api_key_id)
            )
            api_key = result.scalar_one_or_none()
            
            if not api_key:
                return None
            
            # Ensure counters are up to date
            await self._reset_counters_if_needed(api_key)
            await self.db.commit()
            await self.db.refresh(api_key)
            
            return {
                "total_requests": api_key.usage_count,
                "requests_today": api_key.requests_today,
                "requests_month": api_key.requests_month,
                "cost_today": round(api_key.cost_today, 4),
                "cost_month": round(api_key.cost_month, 4),
                "last_used": api_key.last_used.isoformat() if api_key.last_used else None,
                "last_daily_reset": api_key.last_daily_reset.isoformat() if api_key.last_daily_reset else None,
                "last_monthly_reset": api_key.last_monthly_reset.isoformat() if api_key.last_monthly_reset else None
            }
            
        except Exception as e:
            logger.error(f"Failed to get usage stats for API key {api_key_id}: {str(e)}")
            return None
    
    async def get_user_usage_summary(self, user_id: int) -> Dict[str, Any]:
        """Get usage summary for all user's API keys."""
        try:
            result = await self.db.execute(
                select(APIKey).where(APIKey.user_id == user_id)
            )
            api_keys = result.scalars().all()
            
            total_requests = 0
            total_requests_today = 0
            total_requests_month = 0
            total_cost_today = 0.0
            total_cost_month = 0.0
            active_keys = 0
            
            for api_key in api_keys:
                # Reset counters if needed
                await self._reset_counters_if_needed(api_key)
                
                total_requests += api_key.usage_count
                total_requests_today += api_key.requests_today
                total_requests_month += api_key.requests_month
                total_cost_today += api_key.cost_today
                total_cost_month += api_key.cost_month
                
                if api_key.status == APIKeyStatus.ACTIVE:
                    active_keys += 1
            
            await self.db.commit()
            
            return {
                "total_keys": len(api_keys),
                "active_keys": active_keys,
                "total_requests": total_requests,
                "requests_today": total_requests_today,
                "requests_month": total_requests_month,
                "cost_today": round(total_cost_today, 4),
                "cost_month": round(total_cost_month, 4)
            }
            
        except Exception as e:
            logger.error(f"Failed to get usage summary for user {user_id}: {str(e)}")
            return {
                "total_keys": 0,
                "active_keys": 0,
                "total_requests": 0,
                "requests_today": 0,
                "requests_month": 0,
                "cost_today": 0.0,
                "cost_month": 0.0
            }


def estimate_cost(provider: str, model: str, input_tokens: int, output_tokens: int) -> float:
    """Estimate cost based on provider, model, and token usage."""
    # Simplified cost estimation - in production, this would be more sophisticated
    cost_per_1k_tokens = {
        "openai": {
            "gpt-4": {"input": 0.03, "output": 0.06},
            "gpt-4-turbo": {"input": 0.01, "output": 0.03},
            "gpt-3.5-turbo": {"input": 0.0015, "output": 0.002}
        },
        "anthropic": {
            "claude-3-opus": {"input": 0.015, "output": 0.075},
            "claude-3-sonnet": {"input": 0.003, "output": 0.015},
            "claude-3-haiku": {"input": 0.00025, "output": 0.00125}
        },
        "google": {
            "gemini-pro": {"input": 0.0005, "output": 0.0015},
            "gemini-pro-vision": {"input": 0.0005, "output": 0.0015}
        }
    }
    
    provider_costs = cost_per_1k_tokens.get(provider.lower(), {})
    model_costs = provider_costs.get(model.lower(), {"input": 0.001, "output": 0.002})
    
    input_cost = (input_tokens / 1000) * model_costs["input"]
    output_cost = (output_tokens / 1000) * model_costs["output"]
    
    return input_cost + output_cost
