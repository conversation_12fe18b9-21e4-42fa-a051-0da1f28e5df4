"""
动态加载器 - 支持基于配置的Agent部署和传统代码生成的Agent加载
重新设计：优先使用配置驱动的运行时，回退到代码生成方式
"""

import importlib
import importlib.util
import sys
import json
import os
import uuid
from typing import Dict, List, Optional, Any, Type
from pathlib import Path
from datetime import datetime

from app.core.logging import get_logger
from app.core.timezone_utils import utc_now
from app.services.context_service import get_context_service
from app.models.context import (
    ContextAwareTeamMember,
    ContextAwareWorkflowStep,
    ensure_context_compatibility
)

logger = get_logger(__name__)


class ConfigDrivenAgent:
    """基于配置的Agent运行时 - 无需代码生成"""

    def __init__(self, agent_config: Dict[str, Any]):
        self.config = agent_config
        self.team_plan = agent_config.get("team_plan", {})

        # Ensure context compatibility for existing team plans
        self.team_plan = ensure_context_compatibility(self.team_plan)

        self.team_members = self.team_plan.get("team_members", [])
        self.workflow = self.team_plan.get("workflow", {})
        self.agent_id = agent_config.get("agent_id")
        self.team_name = agent_config.get("team_name", "Unknown Team")

        # Migrate workflow assignees from name to role if needed
        self._migrate_workflow_assignees()

        # Initialize user context (will be set by the execution framework)
        self.current_user = None

        # Initialize context service
        self.context_service = get_context_service()
        self.execution_id = None  # Will be set during execution
        self.test_id = None  # For variable tracking integration

        # Use system default AI configuration for runtime execution
        self.ai_config = {
            "provider": "openai",
            "model": "gpt-4",
            "temperature": 0.7,
            "max_tokens": 2000,
            "base_url": None,
            "custom_provider_name": None
        }

        logger.info(f"配置驱动Agent初始化: {self.team_name} ({self.agent_id})")
        logger.info(f"AI配置: {self.ai_config['provider']}/{self.ai_config['model']} (temp: {self.ai_config['temperature']})")
        logger.info(f"Context-aware execution enabled: {len(self.team_members)} team members")

    def _migrate_workflow_assignees(self):
        """Migrate workflow assignees from name to role for unified architecture."""
        if not self.workflow or "steps" not in self.workflow:
            return

        # Create name to role mapping
        name_to_role = {}
        for member in self.team_members:
            name = member.get("name")
            role = member.get("role")
            if name and role:
                name_to_role[name] = role

        # Migrate each workflow step
        migrated_count = 0
        for step in self.workflow["steps"]:
            assignee = step.get("assignee")
            if assignee and assignee in name_to_role:
                old_assignee = assignee
                new_assignee = name_to_role[assignee]
                step["assignee"] = new_assignee
                migrated_count += 1
                logger.info(f"Migrated workflow assignee: '{old_assignee}' -> '{new_assignee}'")
            elif assignee == "团队协作":
                # Migrate Chinese team collaboration to English
                step["assignee"] = "team_collaboration"
                migrated_count += 1
                logger.info(f"Migrated team collaboration: '团队协作' -> 'team_collaboration'")

        if migrated_count > 0:
            logger.info(f"Workflow migration completed: {migrated_count} assignees migrated to role-based format")

    def set_test_id(self, test_id: str):
        """Set test ID for variable tracking integration."""
        self.test_id = test_id
        logger.debug(f"Test ID set for agent {self.agent_id}: {test_id}")

    async def execute(self, input_data: Dict, progress_callback=None) -> Dict:
        """执行基于配置的Agent工作流"""
        try:
            logger.info(f"开始执行配置驱动Agent: {self.team_name}")

            # Initialize execution context for this run
            self.execution_id = f"exec_{uuid.uuid4().hex[:12]}"
            context_manager = self.context_service.create_execution_context(self.execution_id)

            # Add user input to context
            user_input = input_data.get("input", "")
            self.context_service.add_step_context(
                execution_id=self.execution_id,
                step_name="user_input",
                member_name="system",
                context_data={"user_input": user_input, "output": user_input}
            )

            # Send initial progress update
            if progress_callback:
                await progress_callback({
                    "stage": "initializing",
                    "message": f"正在初始化 {self.team_name}...",
                    "progress": 0,
                    "total_steps": 0,
                    "execution_id": self.execution_id
                })

            # Apply AI overrides if provided
            effective_ai_config = self.ai_config.copy()
            ai_override = input_data.get("ai_override")
            if ai_override:
                logger.info(f"应用AI配置覆盖: {ai_override}")
                if ai_override.get("provider"):
                    effective_ai_config["provider"] = ai_override["provider"]
                if ai_override.get("model"):
                    effective_ai_config["model"] = ai_override["model"]
                if ai_override.get("temperature") is not None:
                    effective_ai_config["temperature"] = ai_override["temperature"]
                if ai_override.get("max_tokens"):
                    effective_ai_config["max_tokens"] = ai_override["max_tokens"]
                if ai_override.get("base_url"):
                    effective_ai_config["base_url"] = ai_override["base_url"]
                if ai_override.get("custom_provider_name"):
                    effective_ai_config["custom_provider_name"] = ai_override["custom_provider_name"]
                if ai_override.get("api_key_id"):
                    effective_ai_config["api_key_id"] = ai_override["api_key_id"]

            # Store the effective config for use in AI calls
            self.effective_ai_config = effective_ai_config
            logger.info(f"有效AI配置: {effective_ai_config['provider']}/{effective_ai_config['model']} (temp: {effective_ai_config['temperature']})")

            # 模拟团队协作执行
            results = []
            workflow_steps = self.workflow.get("steps", [])

            if not workflow_steps:
                # 如果没有定义工作流，使用默认的单步执行
                if progress_callback:
                    await progress_callback({
                        "stage": "executing",
                        "message": "执行默认工作流...",
                        "progress": 50,
                        "total_steps": 1
                    })
                return await self._execute_default_workflow(input_data)

            total_steps = len(workflow_steps)

            # Send progress update with total steps
            if progress_callback:
                await progress_callback({
                    "stage": "planning",
                    "message": f"工作流规划完成，共 {total_steps} 个步骤",
                    "progress": 10,
                    "total_steps": total_steps
                })

            # 按工作流步骤执行
            context = {"input": input_data, "results": []}

            for i, step in enumerate(workflow_steps):
                step_name = step.get("name", f"步骤 {i+1}")
                assignee = step.get("assignee", "团队成员")

                # Send progress update for current step
                if progress_callback:
                    progress_percent = 10 + (i / total_steps) * 80  # 10% for planning, 80% for execution, 10% for completion
                    await progress_callback({
                        "stage": "executing",
                        "message": f"正在执行: {step_name} (负责人: {assignee})",
                        "progress": int(progress_percent),
                        "current_step": i + 1,
                        "total_steps": total_steps,
                        "step_name": step_name,
                        "assignee": assignee
                    })

                # Add current step index to context for variable tracking
                context["current_step_index"] = i

                step_result = await self._execute_workflow_step(step, context, progress_callback)
                results.append(step_result)
                context["results"].append(step_result)

                # Send step completion update
                if progress_callback:
                    progress_percent = 10 + ((i + 1) / total_steps) * 80
                    await progress_callback({
                        "stage": "step_completed",
                        "message": f"完成: {step_name}",
                        "progress": int(progress_percent),
                        "current_step": i + 1,
                        "total_steps": total_steps,
                        "step_result": step_result.get("output", "步骤完成")
                    })

            # Send final completion update
            if progress_callback:
                await progress_callback({
                    "stage": "completed",
                    "message": f"{self.team_name} 执行完成！",
                    "progress": 100,
                    "total_steps": total_steps,
                    "final_output": results[-1].get("output", "执行完成") if results else "No output generated"
                })

            # Get execution context summary
            context_summary = self.context_service.get_execution_summary(self.execution_id)

            # Clean up execution context
            self.context_service.cleanup_execution_context(self.execution_id)

            return {
                "agent_id": self.agent_id,
                "team_name": self.team_name,
                "status": "success",
                "results": results,
                "final_output": results[-1] if results else "No output generated",
                "execution_method": "config_driven_context_aware",
                "ai_config_used": effective_ai_config,
                "execution_id": self.execution_id,
                "context_summary": context_summary,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"配置驱动Agent执行失败: {str(e)}")

            # Clean up execution context on error
            if hasattr(self, 'execution_id') and self.execution_id:
                self.context_service.cleanup_execution_context(self.execution_id)

            return {
                "agent_id": self.agent_id,
                "team_name": self.team_name,
                "status": "error",
                "error": str(e),
                "execution_method": "config_driven_context_aware",
                "execution_id": getattr(self, 'execution_id', None),
                "timestamp": datetime.now().isoformat()
            }

    async def execute_stream(self, input_data: Dict, progress_callback=None) -> Dict:
        """流式执行Agent（支持实时AI响应）"""
        try:
            logger.info(f"开始流式执行Agent: {self.team_name}")

            # Initialize execution context for this run
            self.execution_id = f"exec_{uuid.uuid4().hex[:12]}"
            context_manager = self.context_service.create_execution_context(self.execution_id)

            # Add user input to context
            user_input = input_data.get("input", "")
            self.context_service.add_step_context(
                execution_id=self.execution_id,
                step_name="user_input",
                member_name="system",
                context_data={"user_input": user_input, "output": user_input}
            )

            # Send initial progress update
            if progress_callback:
                await progress_callback({
                    "stage": "initializing",
                    "message": f"正在初始化 {self.team_name}...",
                    "progress": 0,
                    "total_steps": 0
                })

            # Apply AI overrides if provided
            effective_ai_config = self.ai_config.copy()
            ai_override = input_data.get("ai_override")
            if ai_override:
                logger.info(f"应用AI配置覆盖: {ai_override}")
                if ai_override.get("provider"):
                    effective_ai_config["provider"] = ai_override["provider"]
                if ai_override.get("model"):
                    effective_ai_config["model"] = ai_override["model"]
                if ai_override.get("temperature") is not None:
                    effective_ai_config["temperature"] = ai_override["temperature"]
                if ai_override.get("max_tokens"):
                    effective_ai_config["max_tokens"] = ai_override["max_tokens"]
                if ai_override.get("base_url"):
                    effective_ai_config["base_url"] = ai_override["base_url"]
                if ai_override.get("custom_provider_name"):
                    effective_ai_config["custom_provider_name"] = ai_override["custom_provider_name"]
                if ai_override.get("api_key_id"):
                    effective_ai_config["api_key_id"] = ai_override["api_key_id"]

            # Store the effective config for use in AI calls
            self.effective_ai_config = effective_ai_config
            logger.info(f"有效AI配置: {effective_ai_config['provider']}/{effective_ai_config['model']} (temp: {effective_ai_config['temperature']})")

            # 执行工作流
            workflow_steps = self.workflow.get("steps", [])

            if not workflow_steps:
                # 如果没有定义工作流，使用默认的单步执行
                if progress_callback:
                    await progress_callback({
                        "stage": "executing",
                        "message": "执行默认工作流...",
                        "progress": 50,
                        "total_steps": 1
                    })
                return await self._execute_default_workflow_stream(input_data, progress_callback)

            return await self._execute_workflow_stream(input_data, progress_callback)

        except Exception as e:
            logger.error(f"Agent流式执行失败: {str(e)}")
            if progress_callback:
                await progress_callback({
                    "stage": "error",
                    "message": f"执行失败: {str(e)}",
                    "progress": 0,
                    "error": str(e)
                })
            raise e

    async def _execute_workflow_step(self, step: Dict, context: Dict, progress_callback=None) -> Dict:
        """执行单个工作流步骤"""
        step_name = step.get("name", "Unknown Step")
        assignee = step.get("assignee", "Unknown Member")
        description = step.get("description", "")

        # 找到负责的团队成员
        member = self._find_team_member(assignee)
        if not member:
            return {
                "step": step_name,
                "assignee": assignee,
                "status": "error",
                "error": f"Team member '{assignee}' not found"
            }

        # 使用AI模型执行任务
        logger.info(f"执行步骤: {step_name} (负责人: {assignee})")

        try:
            # Convert to context-aware models for enhanced processing
            context_step = ContextAwareWorkflowStep(**step)
            context_member = ContextAwareTeamMember(**member)

            # Build context-aware prompt using context service
            input_data = context.get("input", {})
            user_input = input_data.get("input", "No input provided")

            step_prompt = self.context_service.build_step_prompt(
                execution_id=self.execution_id,
                step=context_step,
                member=context_member,
                user_input=user_input
            )

            # Use AI to process this step with effective configuration (including overrides)
            ai_response = await self._call_ai_with_agent_config(
                prompt=step_prompt,
                member=member
            )

            # Store step result in context for future steps
            self.context_service.add_step_context(
                execution_id=self.execution_id,
                step_name=step_name,
                member_name=assignee,
                context_data={
                    "input": user_input,
                    "output": ai_response,
                    "step_description": description,
                    "member_role": member.get("role", "specialist")
                }
            )

            # Track variable resolution for WebSocket broadcasting
            await self._track_step_variables(
                step_name=step_name,
                assignee=assignee,
                ai_response=ai_response,
                step_index=context.get("current_step_index", 0)
            )

            result = {
                "step": step_name,
                "assignee": assignee,
                "description": description,
                "status": "completed",
                "output": ai_response,
                "member_role": member.get("role", "specialist"),
                "capabilities_used": member.get("capabilities", []),
                "execution_method": "ai_powered_workflow_step",
                "ai_config_used": {
                    "provider": self.effective_ai_config["provider"],
                    "model": self.effective_ai_config["model"],
                    "temperature": self.effective_ai_config["temperature"]
                },
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"AI call failed for workflow step {step_name}: {str(e)}")
            # Fallback to simple response
            result = {
                "step": step_name,
                "assignee": assignee,
                "description": description,
                "status": "completed",
                "output": f"{assignee}完成了{step_name}：{description} (AI调用失败，使用默认响应)",
                "member_role": member.get("role", "specialist"),
                "capabilities_used": member.get("capabilities", []),
                "execution_method": "fallback_workflow_step",
                "error": f"AI call failed: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }

        return result

    async def _track_step_variables(self, step_name: str, assignee: str, ai_response: str, step_index: int):
        """Track and broadcast variable updates for a completed step."""
        try:
            # Import variable tracker
            from app.services.websocket_service import variable_tracker

            logger.info(f"Tracking variables for step '{step_name}' by {assignee}, test_id: {self.test_id}")

            # Only track variables that are actually defined in the team configuration
            # Get the discovered variables for this agent
            discovered_variables = await self._get_discovered_variables_for_agent(assignee)

            if not discovered_variables:
                logger.info(f"No discovered variables found for agent {assignee}, skipping variable tracking")
                return

            logger.info(f"Found {len(discovered_variables)} discovered variables for agent {assignee}")

            # Track each discovered variable that this agent should produce
            for variable in discovered_variables:
                # Only track if this agent is the source of this variable
                if variable.get("source_agent") == assignee:
                    variable_name = variable.get("placeholder", "")
                    if variable_name:
                        # Extract the actual variable value from the AI response
                        variable_value = self._extract_variable_value(variable_name, ai_response, variable)

                        await variable_tracker.track_variable_resolution(
                            agent_id=self.agent_id,
                            variable_name=variable_name,
                            variable_value=variable_value,
                            source_agent=assignee,
                            execution_step=step_index,
                            variable_type=variable.get("variable_type", "inter-agent"),
                            destination_agents=variable.get("destination_agents", []),
                            metadata={
                                "step_name": step_name,
                                "execution_id": self.execution_id,
                                "team_name": self.team_name,
                                "variable_description": variable.get("semantic_description", ""),
                                "is_complete": True,  # Mark as complete since step is finished
                                "extraction_method": "step_completion"
                            },
                            test_id=self.test_id  # Pass test_id for database storage
                        )

                        logger.info(f"Tracked discovered variable: {variable_name} = {variable_value[:100]}... from {assignee} at step {step_index}")

            # Note: Database updates are now handled immediately in variable_tracker.track_variable_resolution
            # This ensures the database contains exactly the same data that the frontend receives via WebSocket
            if self.test_id and discovered_variables:
                logger.info(f"Variables tracked and written to database for test {self.test_id}")

        except Exception as e:
            logger.error(f"Failed to track step variables: {str(e)}")

    def _extract_variable_value(self, variable_name: str, ai_response: str, variable_metadata: Dict[str, Any]) -> str:
        """
        Extract the actual variable value from AI response.

        This method attempts to intelligently extract the relevant part of the AI response
        that corresponds to the variable being tracked.

        Args:
            variable_name: The variable placeholder (e.g., "{planner.strategy}")
            ai_response: The full AI response text
            variable_metadata: Metadata about the variable

        Returns:
            Extracted variable value or the full response if extraction fails
        """
        try:
            # Remove braces from variable name for analysis
            clean_var_name = variable_name.strip('{}')

            # For semantic variables like "planner.strategy", extract the output type
            if '.' in clean_var_name:
                agent_role, output_type = clean_var_name.split('.', 1)

                # Try to extract based on output type patterns
                extracted_value = self._extract_by_output_type(ai_response, output_type, variable_metadata)
                if extracted_value:
                    logger.debug(f"Extracted variable value for {variable_name} using output type pattern: {extracted_value[:100]}...")
                    return extracted_value

            # Try to extract based on variable description
            description = variable_metadata.get("semantic_description", "")
            if description:
                extracted_value = self._extract_by_description(ai_response, description)
                if extracted_value:
                    logger.debug(f"Extracted variable value for {variable_name} using description pattern: {extracted_value[:100]}...")
                    return extracted_value

            # For now, use the full AI response as fallback
            # In the future, we could implement more sophisticated extraction
            logger.debug(f"Using full AI response as variable value for {variable_name}")
            return ai_response.strip()

        except Exception as e:
            logger.error(f"Error extracting variable value for {variable_name}: {str(e)}")
            return ai_response.strip()

    def _extract_by_output_type(self, ai_response: str, output_type: str, variable_metadata: Dict[str, Any]) -> str:
        """Extract variable value based on output type patterns."""
        # Common output type patterns
        patterns = {
            'strategy': ['strategy', 'plan', 'approach'],
            'analysis': ['analysis', 'assessment', 'evaluation'],
            'content': ['content', 'text', 'copy'],
            'summary': ['summary', 'overview', 'conclusion'],
            'recommendations': ['recommendations', 'suggestions', 'advice'],
            'results': ['results', 'findings', 'outcome'],
            'draft': ['draft', 'version', 'content'],
            'review': ['review', 'feedback', 'comments'],
            'final': ['final', 'completed', 'finished']
        }

        # Look for patterns in the output type
        for pattern_key, keywords in patterns.items():
            if any(keyword in output_type.lower() for keyword in keywords):
                # For now, return the full response
                # TODO: Implement more sophisticated pattern matching
                return ai_response.strip()

        return ai_response.strip()

    def _extract_by_description(self, ai_response: str, description: str) -> str:
        """Extract variable value based on semantic description."""
        # For now, return the full response
        # TODO: Implement description-based extraction using NLP
        return ai_response.strip()

    async def _update_variables_in_database(self):
        """
        Update the test_history database with variable data from VariableTracker.

        This method is called only when a step is completed and variables have been tracked.
        """
        try:
            from app.core.database import AsyncSessionLocal
            from app.services.websocket_service import variable_tracker
            from app.core.timezone_utils import utc_now, normalize_datetime_for_db
            from sqlalchemy import text
            import json

            logger.info(f"Starting database update for test {self.test_id}")

            # Get stored variable data from VariableTracker
            stored_data = variable_tracker.get_stored_variables(self.test_id)

            if not stored_data:
                logger.warning(f"No variable data found in VariableTracker for test {self.test_id}")
                return

            logger.info(f"Found variable data for test {self.test_id}: {len(stored_data.get('context_placeholders_used', []))} placeholders")

            # Create a new database session for this update
            async with AsyncSessionLocal() as db:
                # Get existing data from database
                existing_query = "SELECT context_placeholders_used, team_member_interactions, context_summary FROM test_history WHERE test_id = :test_id"
                result = await db.execute(text(existing_query), {"test_id": self.test_id})
                existing_record = result.fetchone()

                if not existing_record:
                    logger.error(f"No test record found for test_id {self.test_id}")
                    return

                # Merge variable data with existing data
                merged_placeholders = await self._merge_variable_data(
                    existing_record.context_placeholders_used,
                    stored_data.get('context_placeholders_used', [])
                )

                merged_interactions = await self._merge_interaction_data(
                    existing_record.team_member_interactions,
                    stored_data.get('team_member_interactions', [])
                )

                merged_summary = await self._merge_summary_data(
                    existing_record.context_summary,
                    stored_data.get('context_summary', {})
                )

                # Prepare update data
                update_data = {
                    "updated_at": normalize_datetime_for_db(utc_now()),
                    "context_placeholders_used": json.dumps(merged_placeholders),
                    "team_member_interactions": json.dumps(merged_interactions),
                    "context_summary": json.dumps(merged_summary)
                }

                # Build update query
                set_clauses = []
                params = {"test_id": self.test_id}

                for key, value in update_data.items():
                    set_clauses.append(f"{key} = :{key}")
                    params[key] = value

                query = f"UPDATE test_history SET {', '.join(set_clauses)} WHERE test_id = :test_id"
                logger.debug(f"Executing database update query for test {self.test_id}")

                result = await db.execute(text(query), params)
                await db.commit()

                logger.info(f"Successfully updated variable data in database for test {self.test_id} (affected rows: {result.rowcount})")
                logger.debug(f"Updated data: {len(merged_placeholders)} placeholders, {len(merged_interactions)} interactions")

        except Exception as e:
            logger.error(f"Failed to update variables in database for test {self.test_id}: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")

    async def _merge_variable_data(self, existing_json: str, new_data: List[Dict]) -> List[Dict]:
        """Merge new variable data with existing data, updating resolved variables."""
        try:
            import json

            # Parse existing data
            existing_data = []
            if existing_json:
                try:
                    existing_data = json.loads(existing_json) if isinstance(existing_json, str) else existing_json
                    if not isinstance(existing_data, list):
                        existing_data = []
                except (json.JSONDecodeError, TypeError):
                    existing_data = []

            # Create a map of existing variables by name for quick lookup
            existing_map = {}
            for item in existing_data:
                if isinstance(item, dict) and 'variable_name' in item:
                    existing_map[item['variable_name']] = item

            # Add or update variables from new data
            for new_item in new_data:
                if isinstance(new_item, dict) and 'variable_name' in new_item:
                    variable_name = new_item['variable_name']

                    # If variable exists and new one has a value, update it
                    if variable_name in existing_map:
                        existing_item = existing_map[variable_name]
                        # Update if new item has a non-empty value or is marked as complete
                        if (new_item.get('variable_value') and new_item['variable_value'].strip() and
                            new_item['variable_value'] != 'pending'):
                            existing_map[variable_name] = new_item
                            logger.debug(f"Updated existing variable: {variable_name}")
                        else:
                            logger.debug(f"Kept existing variable: {variable_name}")
                    else:
                        # Add new variable
                        existing_map[variable_name] = new_item
                        logger.debug(f"Added new variable: {variable_name}")

            # Convert back to list
            merged_data = list(existing_map.values())
            return merged_data

        except Exception as e:
            logger.error(f"Error merging variable data: {str(e)}")
            return new_data  # Fallback to new data

    async def _merge_interaction_data(self, existing_json: str, new_data: List[Dict]) -> List[Dict]:
        """Merge new interaction data with existing data."""
        try:
            import json

            # Parse existing data
            existing_data = []
            if existing_json:
                try:
                    existing_data = json.loads(existing_json) if isinstance(existing_json, str) else existing_json
                    if not isinstance(existing_data, list):
                        existing_data = []
                except (json.JSONDecodeError, TypeError):
                    existing_data = []

            # Simply append new interactions (they are typically unique by timestamp)
            merged_data = existing_data + new_data
            return merged_data

        except Exception as e:
            logger.error(f"Error merging interaction data: {str(e)}")
            return new_data  # Fallback to new data

    async def _merge_summary_data(self, existing_json: str, new_data: Dict) -> Dict:
        """Merge new summary data with existing data."""
        try:
            import json

            # Parse existing data
            existing_data = {}
            if existing_json:
                try:
                    existing_data = json.loads(existing_json) if isinstance(existing_json, str) else existing_json
                    if not isinstance(existing_data, dict):
                        existing_data = {}
                except (json.JSONDecodeError, TypeError):
                    existing_data = {}

            # Merge summary data
            merged_data = existing_data.copy()
            merged_data.update(new_data)

            return merged_data

        except Exception as e:
            logger.error(f"Error merging summary data: {str(e)}")
            return new_data  # Fallback to new data

    async def _get_discovered_variables_for_agent(self, assignee: str) -> List[Dict[str, Any]]:
        """Get discovered variables that this agent should produce."""
        try:
            # Import the variable discovery service
            from app.services.variable_discovery import VariableDiscoveryService

            # Get workflow steps from the correct location
            workflow_steps = self.workflow.get("steps", [])

            # Get team plan
            team_plan = {
                "team_name": self.team_name,
                "team_members": self.team_members,
                "workflow_steps": workflow_steps
            }

            logger.info(f"Discovering variables for agent {assignee} with {len(self.team_members)} team members and {len(workflow_steps)} workflow steps")

            # Discover variables
            discovery_service = VariableDiscoveryService()
            variables = discovery_service.discover_team_variables(team_plan)

            logger.info(f"Discovered {len(variables)} total variables, filtering for agent {assignee}")

            # Convert to dict format and filter for this agent
            # Now assignee should be the role directly (no mapping needed)
            agent_variables = []
            for var in variables:
                # Match by role (assignee is now the role)
                if var.source_agent == assignee:
                    agent_variables.append({
                        "name": var.name,
                        "placeholder": var.placeholder,
                        "variable_type": var.variable_type.value,
                        "source_agent": var.source_agent,
                        "destination_agents": var.destination_agents,
                        "semantic_description": var.semantic_description,
                        "workflow_step": var.workflow_step,
                        "dependencies": var.dependencies,
                        "is_required": var.is_required
                    })
                    logger.info(f"Matched variable {var.placeholder} for agent role '{assignee}'")

            logger.info(f"Found {len(agent_variables)} variables for agent {assignee}")
            return agent_variables

        except Exception as e:
            logger.error(f"Failed to get discovered variables for agent {assignee}: {str(e)}")
            return []



    async def _execute_workflow_step_stream(self, step: Dict, context: Dict, progress_callback=None) -> Dict:
        """执行单个工作流步骤（流式）"""
        step_name = step.get("name", "Unknown Step")
        assignee = step.get("assignee", "Unknown Member")
        description = step.get("description", "No description provided")

        # 查找对应的团队成员
        member = self._find_team_member(assignee)
        if not member:
            logger.warning(f"未找到团队成员: {assignee}")
            member = self.team_members[0] if self.team_members else {"name": assignee, "role": "Assistant"}

        # Check if this is a collaborative step
        is_collaborative = member.get("is_collaborative", False)
        if is_collaborative:
            logger.info(f"执行团队协作步骤: {step_name}")
        else:
            logger.info(f"执行个人步骤: {step_name} (负责人: {assignee})")

        try:
            # Convert to context-aware models for enhanced processing
            context_step = ContextAwareWorkflowStep(**step)
            context_member = ContextAwareTeamMember(**member)

            # Build context-aware prompt using context service
            input_data = context.get("input", {})
            user_input = input_data.get("input", "No input provided")

            # Enhance prompt for collaborative steps
            if is_collaborative:
                # Create a collaborative context that includes all team members
                collaborative_context = self._build_collaborative_context()
                step_prompt = self.context_service.build_collaborative_step_prompt(
                    execution_id=self.execution_id,
                    step=context_step,
                    primary_member=context_member,
                    team_members=self.team_members,
                    user_input=user_input,
                    collaborative_context=collaborative_context
                )
            else:
                step_prompt = self.context_service.build_step_prompt(
                    execution_id=self.execution_id,
                    step=context_step,
                    member=context_member,
                    user_input=user_input
                )

            # 收集流式响应
            ai_response_chunks = []
            async for chunk in self._call_ai_with_agent_config_stream(
                prompt=step_prompt,
                member=member
            ):
                ai_response_chunks.append(chunk)
                # 发送流式进度更新
                if progress_callback:
                    await progress_callback({
                        "stage": "streaming",
                        "message": f"正在生成: {step_name}",
                        "step_name": step_name,
                        "assignee": assignee,
                        "streaming_content": chunk,
                        "partial_output": "".join(ai_response_chunks)
                    })

            ai_response = "".join(ai_response_chunks)

            # Store step result in context for future steps
            self.context_service.add_step_context(
                execution_id=self.execution_id,
                step_name=step_name,
                member_name=assignee,
                context_data={
                    "input": user_input,
                    "output": ai_response,
                    "step_description": description,
                    "member_role": member.get("role", "specialist")
                }
            )

            # Track variable resolution for WebSocket broadcasting (stream version)
            await self._track_step_variables(
                step_name=step_name,
                assignee=assignee,
                ai_response=ai_response,
                step_index=context.get("current_step_index", 0)
            )

            result = {
                "step": step_name,
                "assignee": assignee,
                "description": description,
                "status": "completed",
                "output": ai_response,
                "member_role": member.get("role", "specialist"),
                "capabilities_used": member.get("capabilities", []),
                "execution_method": "ai_powered_workflow_step_stream",
                "ai_config_used": {
                    "provider": self.effective_ai_config["provider"],
                    "model": self.effective_ai_config["model"],
                    "temperature": self.effective_ai_config["temperature"]
                },
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"AI streaming call failed for workflow step {step_name}: {str(e)}")
            # Fallback to simple response
            result = {
                "step": step_name,
                "assignee": assignee,
                "description": description,
                "status": "completed",
                "output": f"{assignee}完成了{step_name}：{description} (AI流式调用失败，使用默认响应)",
                "member_role": member.get("role", "specialist"),
                "capabilities_used": member.get("capabilities", []),
                "execution_method": "fallback_workflow_step_stream",
                "error": f"AI streaming call failed: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }

        return result

    async def _execute_default_workflow(self, input_data: Dict) -> Dict:
        """执行默认工作流（当没有定义具体步骤时）"""
        if not self.team_members:
            return {
                "agent_id": self.agent_id,
                "status": "error",
                "error": "No team members defined"
            }

        # 使用第一个团队成员处理请求
        primary_member = self.team_members[0]

        try:
            # Use effective AI configuration for actual AI processing (including overrides)
            ai_response = await self._call_ai_with_agent_config(
                prompt=input_data.get('input', 'No input provided'),
                member=primary_member
            )

            result = {
                "agent_id": self.agent_id,
                "team_name": self.team_name,
                "status": "success",
                "output": ai_response,
                "member": primary_member.get("name"),
                "role": primary_member.get("role"),
                "execution_method": "config_driven_default_with_ai",
                "ai_config_used": {
                    "provider": self.effective_ai_config["provider"],
                    "model": self.effective_ai_config["model"],
                    "temperature": self.effective_ai_config["temperature"]
                },
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"AI call failed for agent {self.agent_id}: {str(e)}")
            # Fallback to simple response
            result = {
                "agent_id": self.agent_id,
                "team_name": self.team_name,
                "status": "success",
                "output": f"{primary_member.get('name', 'Team Member')}处理了请求：{input_data.get('input', 'No input provided')} (AI调用失败，使用默认响应)",
                "member": primary_member.get("name"),
                "role": primary_member.get("role"),
                "execution_method": "config_driven_fallback",
                "error": f"AI call failed: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }

        return result

    async def _execute_workflow_stream(self, input_data: Dict, progress_callback=None) -> Dict:
        """执行工作流（流式）"""
        results = []
        workflow_steps = self.workflow.get("steps", [])
        total_steps = len(workflow_steps)

        # Send progress update with total steps
        if progress_callback:
            await progress_callback({
                "stage": "planning",
                "message": f"工作流规划完成，共 {total_steps} 个步骤",
                "progress": 10,
                "total_steps": total_steps
            })

        # 按工作流步骤执行
        context = {"input": input_data, "results": []}

        for i, step in enumerate(workflow_steps):
            step_name = step.get("name", f"步骤 {i+1}")
            assignee = step.get("assignee", "团队成员")

            # Send progress update for current step
            if progress_callback:
                progress_percent = 10 + (i / total_steps) * 80  # 10% for planning, 80% for execution, 10% for completion
                await progress_callback({
                    "stage": "executing",
                    "message": f"正在执行: {step_name} (负责人: {assignee})",
                    "progress": int(progress_percent),
                    "current_step": i + 1,
                    "total_steps": total_steps,
                    "step_name": step_name,
                    "assignee": assignee
                })

            step_result = await self._execute_workflow_step_stream(step, context, progress_callback)
            results.append(step_result)
            context["results"].append(step_result)

            # Send step completion update
            if progress_callback:
                progress_percent = 10 + ((i + 1) / total_steps) * 80
                await progress_callback({
                    "stage": "step_completed",
                    "message": f"完成: {step_name}",
                    "progress": int(progress_percent),
                    "current_step": i + 1,
                    "total_steps": total_steps,
                    "step_result": step_result.get("output", "步骤完成")
                })

        # Send final completion update
        if progress_callback:
            await progress_callback({
                "stage": "completed",
                "message": f"{self.team_name} 执行完成！",
                "progress": 100,
                "total_steps": total_steps,
                "final_output": results[-1].get("output", "执行完成") if results else "No output generated"
            })

        # Clean up execution context
        if hasattr(self, 'execution_id') and self.execution_id:
            self.context_service.cleanup_execution_context(self.execution_id)

        return {
            "agent_id": self.agent_id,
            "team_name": self.team_name,
            "status": "success",
            "results": results,
            "final_output": results[-1] if results else "No output generated",
            "execution_method": "config_driven_stream",
            "execution_id": getattr(self, 'execution_id', None),
            "ai_config_used": self.effective_ai_config,
            "timestamp": datetime.now().isoformat()
        }

    async def _execute_default_workflow_stream(self, input_data: Dict, progress_callback=None) -> Dict:
        """执行默认工作流（流式）"""
        # 使用第一个团队成员处理请求
        primary_member = self.team_members[0]

        try:
            # 收集流式响应
            ai_response_chunks = []
            async for chunk in self._call_ai_with_agent_config_stream(
                prompt=input_data.get('input', 'No input provided'),
                member=primary_member
            ):
                ai_response_chunks.append(chunk)
                # 发送流式进度更新
                if progress_callback:
                    await progress_callback({
                        "stage": "streaming",
                        "message": f"正在生成响应...",
                        "streaming_content": chunk,
                        "partial_output": "".join(ai_response_chunks)
                    })

            ai_response = "".join(ai_response_chunks)

            result = {
                "agent_id": self.agent_id,
                "team_name": self.team_name,
                "status": "success",
                "output": ai_response,
                "member_role": primary_member.get("role", "primary"),
                "capabilities_used": primary_member.get("capabilities", []),
                "execution_method": "default_workflow_stream",
                "ai_config_used": {
                    "provider": self.effective_ai_config["provider"],
                    "model": self.effective_ai_config["model"],
                    "temperature": self.effective_ai_config["temperature"]
                },
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Default workflow streaming execution failed: {str(e)}")
            result = {
                "agent_id": self.agent_id,
                "team_name": self.team_name,
                "status": "error",
                "error": str(e),
                "execution_method": "default_workflow_stream",
                "timestamp": datetime.now().isoformat()
            }

        return result

    async def _call_ai_with_agent_config(self, prompt: str, member: Dict, system_prompt: str = None) -> str:
        """使用Agent的AI配置调用AI服务"""
        try:
            from app.services.user_ai_service import UserAIService
            from app.core.database import AsyncSessionLocal

            # Create a temporary AI service instance
            async with AsyncSessionLocal() as db:
                # Get user from context (should be set during agent execution)
                user = getattr(self, 'current_user', None)
                logger.info(f"User context check: user={user}, user_id={user.id if user else None}")
                if not user:
                    logger.error("User context not available for AI service - current_user not set")
                    raise ValueError("User context not available for AI service")

                ai_service = UserAIService(db, user)

                # Build system prompt based on team member role
                if not system_prompt:
                    member_name = member.get("name", "Team Member")
                    member_role = member.get("role", "Assistant")
                    member_description = member.get("description", "")

                    system_prompt = f"""你是{member_name}，角色是{member_role}。
{member_description}

请根据你的角色和专业知识来处理用户的请求。保持专业、准确和有帮助的回应。"""

                # Use effective AI configuration for the call (including overrides)
                effective_config = getattr(self, 'effective_ai_config', self.ai_config)

                # Prepare API call parameters
                call_params = {
                    "prompt": prompt,
                    "system_prompt": system_prompt,
                    "model": effective_config["model"],
                    "temperature": effective_config["temperature"],
                    "max_tokens": effective_config["max_tokens"],
                    "base_url": effective_config["base_url"],
                    "provider_override": effective_config["provider"]
                }

                # Add API key ID if provided
                if effective_config.get("api_key_id"):
                    call_params["api_key_id"] = effective_config["api_key_id"]

                response = await ai_service.generate_with_custom_config(**call_params)

                return response

        except Exception as e:
            logger.error(f"AI call failed: {str(e)}")
            raise e

    async def _call_ai_with_agent_config_stream(self, prompt: str, member: Dict, system_prompt: str = None):
        """使用Agent的AI配置调用AI服务（流式）"""
        try:
            from app.services.user_ai_service import UserAIService
            from app.core.database import AsyncSessionLocal

            # Create a temporary AI service instance
            async with AsyncSessionLocal() as db:
                # Get user from context (should be set during agent execution)
                user = getattr(self, 'current_user', None)
                logger.info(f"User context check for streaming: user={user}, user_id={user.id if user else None}")
                if not user:
                    logger.error("User context not available for AI service - current_user not set")
                    raise ValueError("User context not available for AI service")

                ai_service = UserAIService(db, user)

                # Build system prompt based on team member role
                if not system_prompt:
                    member_name = member.get("name", "Team Member")
                    member_role = member.get("role", "Assistant")
                    member_description = member.get("description", "")

                    system_prompt = f"""你是{member_name}，角色是{member_role}。
{member_description}

请根据你的角色和专业知识来处理用户的请求。保持专业、准确和有帮助的回应。"""

                # Use effective AI configuration for the call (including overrides)
                effective_config = getattr(self, 'effective_ai_config', self.ai_config)

                # Prepare API call parameters
                call_params = {
                    "prompt": prompt,
                    "system_prompt": system_prompt,
                    "model": effective_config["model"],
                    "temperature": effective_config["temperature"],
                    "max_tokens": effective_config["max_tokens"],
                    "base_url": effective_config["base_url"],
                    "provider": effective_config["provider"]
                }

                # Add API key ID if provided
                if effective_config.get("api_key_id"):
                    call_params["api_key_id"] = effective_config["api_key_id"]

                async for chunk in ai_service.generate_text_stream(**call_params):
                    yield chunk

        except Exception as e:
            logger.error(f"AI streaming call failed: {str(e)}")
            # Return a fallback response as a single chunk
            yield f"AI调用失败，使用默认响应: {prompt[:100]}..."

    def _find_team_member(self, role: str) -> Optional[Dict]:
        """查找团队成员 - 只支持通过role查找"""
        # Handle special case for team collaboration
        if role == "team_collaboration":
            # For collaborative steps, use the first team member as the primary executor
            # but mark it as a collaborative step
            if self.team_members:
                primary_member = self.team_members[0].copy()
                primary_member["is_collaborative"] = True
                primary_member["collaboration_type"] = "team_collaboration"
                return primary_member
            else:
                # Fallback if no team members exist
                return {
                    "name": "团队协作",
                    "role": "team_collaboration",
                    "description": "团队协作执行",
                    "is_collaborative": True,
                    "collaboration_type": "team_collaboration"
                }

        # Find by role only
        for member in self.team_members:
            if member.get("role") == role:
                return member

        # If not found, log error and return None
        logger.error(f"Team member with role '{role}' not found. Available roles: {[m.get('role') for m in self.team_members]}")
        return None

    def _build_collaborative_context(self) -> Dict[str, Any]:
        """构建团队协作上下文信息"""
        return {
            "team_size": len(self.team_members),
            "team_members": [
                {
                    "name": member.get("name", "Unknown"),
                    "role": member.get("role", "Assistant"),
                    "description": member.get("description", ""),
                    "capabilities": member.get("capabilities", [])
                }
                for member in self.team_members
            ],
            "collaboration_style": "综合各成员专长，协作完成任务",
            "decision_making": "基于团队共识和专业判断"
        }


class DynamicLoader:
    """动态加载器 - 支持配置驱动和代码生成两种方式"""

    def __init__(self):
        self.loaded_agents = {}  # agent_id -> agent_instance
        self.agent_classes = {}  # agent_id -> agent_class
        self.agent_configs = {}  # agent_id -> config
        self.generated_agents_dir = Path("generated_agents")
        self.generated_agents_dir.mkdir(exist_ok=True)

        # 将生成的代理目录添加到Python路径
        if str(self.generated_agents_dir) not in sys.path:
            sys.path.insert(0, str(self.generated_agents_dir))
    
    async def load_agent(self, agent_id: str, agent_config: Optional[Dict] = None, force_reload: bool = False) -> Optional[Any]:
        """加载Agent实例 - 支持配置驱动和代码生成两种方式"""
        logger.info(f"加载Agent: {agent_id}, force_reload={force_reload}")

        try:
            # 如果已经加载且不强制重新加载，直接返回
            if agent_id in self.loaded_agents and not force_reload:
                logger.info(f"Agent {agent_id} 已加载，直接返回")
                return self.loaded_agents[agent_id]

            # 优先使用传入的配置（来自数据库）
            config = agent_config
            if not config:
                # 回退到查找配置文件
                config = self._load_agent_config(agent_id)

            if not config:
                logger.error(f"未找到Agent配置: {agent_id}")
                return None

            # 检查是否为完整的配置驱动Agent
            if self._is_config_driven_agent(config):
                logger.info(f"创建配置驱动Agent: {agent_id}")
                agent_instance = ConfigDrivenAgent(config)

                # 缓存
                self.loaded_agents[agent_id] = agent_instance
                self.agent_configs[agent_id] = config

                logger.info(f"配置驱动Agent {agent_id} 创建成功")
                return agent_instance

            # 回退到传统的代码生成方式
            logger.info(f"回退到代码生成方式加载Agent: {agent_id}")
            return await self._load_code_generated_agent(agent_id, config)

        except Exception as e:
            logger.error(f"加载Agent失败 {agent_id}: {str(e)}")
            return None

    def _is_config_driven_agent(self, config: Dict) -> bool:
        """检查是否为配置驱动的Agent"""
        # 检查是否有完整的团队配置
        team_plan = config.get("team_plan", {})
        if not team_plan:
            return False

        # 检查是否有团队成员定义
        team_members = team_plan.get("team_members", [])
        if not team_members:
            return False

        # 检查团队成员是否有必要的字段
        for member in team_members:
            if not isinstance(member, dict):
                return False
            if not all(key in member for key in ["name", "role"]):
                return False

        # 检查元数据标记
        metadata = config.get("metadata", {})
        if metadata.get("ready_to_deploy") and not metadata.get("requires_ai_generation", True):
            return True

        # 如果有完整的团队配置，认为可以配置驱动
        return len(team_members) > 0

    async def _load_code_generated_agent(self, agent_id: str, config: Dict) -> Optional[Any]:
        """加载代码生成的Agent（传统方式）"""
        try:
            # 动态导入Agent类
            agent_class = await self._import_agent_class(config)
            if not agent_class:
                logger.error(f"无法导入Agent类: {agent_id}")
                return None

            # 创建Agent实例
            agent_instance = agent_class()

            # 缓存
            self.loaded_agents[agent_id] = agent_instance
            self.agent_classes[agent_id] = agent_class
            self.agent_configs[agent_id] = config

            logger.info(f"代码生成Agent {agent_id} 加载成功")
            return agent_instance

        except Exception as e:
            logger.error(f"加载代码生成Agent失败 {agent_id}: {str(e)}")
            return None
    
    def _load_agent_config(self, agent_id: str) -> Optional[Dict]:
        """加载Agent配置"""
        # 查找配置文件
        for config_file in self.generated_agents_dir.glob("*_config.json"):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                if config.get("agent_id") == agent_id:
                    return config
                    
            except Exception as e:
                logger.warning(f"读取配置文件失败 {config_file}: {str(e)}")
        
        return None
    
    async def _import_agent_class(self, config: Dict) -> Optional[Type]:
        """动态导入Agent类"""
        try:
            class_name = config.get("class_name")
            if not class_name:
                logger.error("配置中缺少class_name")
                return None
            
            # 查找对应的Python文件
            module_name = None
            for py_file in self.generated_agents_dir.glob("*.py"):
                if py_file.stem.endswith("_agent"):
                    # 检查文件内容是否包含目标类
                    try:
                        with open(py_file, 'r', encoding='utf-8') as f:
                            content = f.read()
                            if f"class {class_name}" in content:
                                module_name = py_file.stem
                                break
                    except Exception as e:
                        logger.warning(f"读取文件失败 {py_file}: {str(e)}")
            
            if not module_name:
                logger.error(f"未找到包含类 {class_name} 的模块")
                return None
            
            # 动态导入模块
            if module_name in sys.modules:
                # 如果模块已经导入，重新加载
                module = importlib.reload(sys.modules[module_name])
            else:
                # 首次导入
                module = importlib.import_module(module_name)
            
            # 获取类
            agent_class = getattr(module, class_name, None)
            if not agent_class:
                logger.error(f"模块 {module_name} 中未找到类 {class_name}")
                return None
            
            return agent_class
            
        except Exception as e:
            logger.error(f"动态导入失败: {str(e)}")
            return None
    
    async def execute_agent(self, agent_id: str, input_data: Dict, agent_config: Optional[Dict] = None, progress_callback=None, current_user=None, test_id: Optional[str] = None) -> Dict:
        """执行Agent - 支持配置驱动和代码生成两种方式"""
        logger.info(f"执行Agent: {agent_id}")

        try:
            # 加载Agent（传入配置以支持配置驱动）
            agent = await self.load_agent(agent_id, agent_config)
            if not agent:
                return {
                    "error": f"无法加载Agent: {agent_id}",
                    "status": "failed",
                    "agent_id": agent_id
                }

            # 设置用户上下文
            if current_user and hasattr(agent, 'current_user'):
                agent.current_user = current_user
                logger.info(f"User context set for agent {agent_id}: user_id={current_user.id}")
            elif current_user:
                logger.warning(f"Agent {agent_id} does not support user context")
            else:
                logger.warning(f"No current_user provided for agent {agent_id}")

            # 设置test_id到agent实例中，以便在变量跟踪中使用
            if test_id and hasattr(agent, 'set_test_id'):
                agent.set_test_id(test_id)
                logger.info(f"Test ID set for agent {agent_id}: {test_id}")

            # 执行Agent
            if hasattr(agent, 'execute') and callable(getattr(agent, 'execute')):
                # For ConfigDrivenAgent, pass progress_callback
                if hasattr(agent, 'team_name'):  # ConfigDrivenAgent has team_name
                    result = await agent.execute(input_data, progress_callback)
                else:
                    result = await agent.execute(input_data)
            else:
                result = await agent.execute(input_data)

            logger.info(f"Agent {agent_id} 执行完成")

            # 确保返回结果包含必要字段
            if isinstance(result, dict):
                result["agent_id"] = agent_id
                if "status" not in result:
                    result["status"] = "completed"
                return result
            else:
                return {
                    "agent_id": agent_id,
                "status": "completed",
                "result": result,
                "executed_at": utc_now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"执行Agent失败 {agent_id}: {str(e)}")
            return {
                "error": str(e),
                "agent_id": agent_id,
                "status": "failed",
                "failed_at": utc_now().isoformat()
            }

    async def execute_agent_stream(self, agent_id: str, input_data: Dict, agent_config: Optional[Dict] = None, progress_callback=None, current_user=None, test_id: Optional[str] = None) -> Dict:
        """流式执行Agent - 支持配置驱动和代码生成两种方式"""
        logger.info(f"流式执行Agent: {agent_id}")

        try:
            # 加载Agent（传入配置以支持配置驱动）
            agent = await self.load_agent(agent_id, agent_config)
            if not agent:
                return {
                    "error": f"无法加载Agent: {agent_id}",
                    "status": "failed",
                    "agent_id": agent_id
                }

            # 设置用户上下文
            if current_user and hasattr(agent, 'current_user'):
                agent.current_user = current_user
                logger.info(f"User context set for streaming agent {agent_id}: user_id={current_user.id}")
            elif current_user:
                logger.warning(f"Agent {agent_id} does not support user context")
            else:
                logger.warning(f"No current_user provided for streaming agent {agent_id}")

            # 设置test_id到agent实例中，以便在变量跟踪中使用
            if test_id and hasattr(agent, 'set_test_id'):
                agent.set_test_id(test_id)

            # 执行Agent（流式）
            if hasattr(agent, 'execute_stream') and callable(getattr(agent, 'execute_stream')):
                # 优先使用流式执行方法
                result = await agent.execute_stream(input_data, progress_callback)
            elif hasattr(agent, 'execute') and callable(getattr(agent, 'execute')):
                # 回退到普通执行方法
                if hasattr(agent, 'team_name'):  # ConfigDrivenAgent has team_name
                    result = await agent.execute(input_data, progress_callback)
                else:
                    result = await agent.execute(input_data)
            else:
                result = await agent.execute(input_data)

            logger.info(f"Agent {agent_id} 流式执行完成")

            # 确保返回结果包含必要字段
            if isinstance(result, dict):
                result["agent_id"] = agent_id
                result["status"] = result.get("status", "completed")
                result["execution_method"] = result.get("execution_method", "stream")
                return result
            else:
                return {
                    "agent_id": agent_id,
                    "status": "completed",
                    "result": result,
                    "execution_method": "stream",
                    "executed_at": utc_now().isoformat()
                }

        except Exception as e:
            logger.error(f"Agent {agent_id} 流式执行失败: {str(e)}")
            return {
                "error": str(e),
                "agent_id": agent_id,
                "status": "failed",
                "execution_method": "stream",
                "failed_at": utc_now().isoformat()
            }

    def get_agent_info(self, agent_id: str) -> Optional[Dict]:
        """获取Agent信息"""
        try:
            config = self.agent_configs.get(agent_id)
            if not config:
                config = self._load_agent_config(agent_id)
            
            if not config:
                return None
            
            # 基础信息
            info = {
                "agent_id": agent_id,
                "class_name": config.get("class_name"),
                "team_name": config.get("team_plan", {}).get("team_name"),
                "description": config.get("team_plan", {}).get("description"),
                "generated_at": config.get("generated_at"),
                "version": config.get("version"),
                "metadata": config.get("metadata", {}),
                "is_loaded": agent_id in self.loaded_agents,
                "status": "active" if agent_id in self.loaded_agents else "inactive"
            }
            
            # 如果Agent已加载，获取运行时信息
            if agent_id in self.loaded_agents:
                agent = self.loaded_agents[agent_id]
                if hasattr(agent, 'get_agent_info'):
                    runtime_info = agent.get_agent_info()
                    info.update(runtime_info)
            
            return info
            
        except Exception as e:
            logger.error(f"获取Agent信息失败 {agent_id}: {str(e)}")
            return None
    
    def list_available_agents(self) -> List[Dict]:
        """列出所有可用的Agent"""
        agents = []
        
        for config_file in self.generated_agents_dir.glob("*_config.json"):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                agent_id = config.get("agent_id")
                if agent_id:
                    info = {
                        "agent_id": agent_id,
                        "class_name": config.get("class_name"),
                        "team_name": config.get("team_plan", {}).get("team_name"),
                        "description": config.get("team_plan", {}).get("description"),
                        "generated_at": config.get("generated_at"),
                        "metadata": config.get("metadata", {}),
                        "is_loaded": agent_id in self.loaded_agents,
                        "status": "active" if agent_id in self.loaded_agents else "inactive"
                    }
                    agents.append(info)
                    
            except Exception as e:
                logger.warning(f"读取配置文件失败 {config_file}: {str(e)}")
        
        return sorted(agents, key=lambda x: x.get("generated_at", ""), reverse=True)
    
    def unload_agent(self, agent_id: str) -> bool:
        """卸载Agent"""
        logger.info(f"卸载Agent: {agent_id}")
        
        try:
            if agent_id in self.loaded_agents:
                del self.loaded_agents[agent_id]
            
            if agent_id in self.agent_classes:
                del self.agent_classes[agent_id]
            
            if agent_id in self.agent_configs:
                del self.agent_configs[agent_id]
            
            logger.info(f"Agent {agent_id} 卸载成功")
            return True
            
        except Exception as e:
            logger.error(f"卸载Agent失败 {agent_id}: {str(e)}")
            return False
    
    def reload_agent(self, agent_id: str) -> bool:
        """重新加载Agent"""
        logger.info(f"重新加载Agent: {agent_id}")
        
        try:
            # 先卸载
            self.unload_agent(agent_id)
            
            # 重新加载
            import asyncio
            agent = asyncio.run(self.load_agent(agent_id, force_reload=True))
            
            return agent is not None
            
        except Exception as e:
            logger.error(f"重新加载Agent失败 {agent_id}: {str(e)}")
            return False
    
    def get_agent_execution_history(self, agent_id: str, limit: int = 10) -> List[Dict]:
        """获取Agent执行历史"""
        try:
            if agent_id in self.loaded_agents:
                agent = self.loaded_agents[agent_id]
                if hasattr(agent, 'get_execution_history'):
                    return agent.get_execution_history(limit)
            
            return []
            
        except Exception as e:
            logger.error(f"获取执行历史失败 {agent_id}: {str(e)}")
            return []
    
    def validate_agent_code(self, agent_id: str) -> Dict:
        """验证Agent代码"""
        logger.info(f"验证Agent代码: {agent_id}")
        
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
            "suggestions": []
        }
        
        try:
            # 检查配置文件
            config = self._load_agent_config(agent_id)
            if not config:
                validation_result["errors"].append("未找到配置文件")
                validation_result["is_valid"] = False
                return validation_result
            
            # 检查Python文件
            class_name = config.get("class_name")
            if not class_name:
                validation_result["errors"].append("配置中缺少class_name")
                validation_result["is_valid"] = False
                return validation_result
            
            # 尝试导入类
            try:
                import asyncio
                agent_class = asyncio.run(self._import_agent_class(config))
                if not agent_class:
                    validation_result["errors"].append("无法导入Agent类")
                    validation_result["is_valid"] = False
                else:
                    validation_result["suggestions"].append("Agent类导入成功")
            except Exception as e:
                validation_result["errors"].append(f"导入失败: {str(e)}")
                validation_result["is_valid"] = False
            
            # 检查必要方法
            if validation_result["is_valid"]:
                required_methods = ["execute", "get_agent_info"]
                for method in required_methods:
                    if not hasattr(agent_class, method):
                        validation_result["warnings"].append(f"缺少推荐方法: {method}")
            
            return validation_result
            
        except Exception as e:
            validation_result["errors"].append(f"验证过程出错: {str(e)}")
            validation_result["is_valid"] = False
            return validation_result
    
    def cleanup_inactive_agents(self) -> Dict:
        """清理不活跃的Agent"""
        logger.info("开始清理不活跃的Agent")
        
        cleanup_result = {
            "cleaned_count": 0,
            "errors": [],
            "cleaned_agents": []
        }
        
        try:
            # 获取所有已加载的Agent
            loaded_agent_ids = list(self.loaded_agents.keys())
            
            for agent_id in loaded_agent_ids:
                try:
                    agent = self.loaded_agents[agent_id]
                    
                    # 检查Agent是否有执行历史
                    if hasattr(agent, 'execution_history'):
                        if not agent.execution_history:
                            # 没有执行历史，可以清理
                            self.unload_agent(agent_id)
                            cleanup_result["cleaned_agents"].append(agent_id)
                            cleanup_result["cleaned_count"] += 1
                    
                except Exception as e:
                    cleanup_result["errors"].append(f"清理Agent {agent_id} 时出错: {str(e)}")
            
            logger.info(f"清理完成，共清理 {cleanup_result['cleaned_count']} 个Agent")
            return cleanup_result
            
        except Exception as e:
            logger.error(f"清理过程出错: {str(e)}")
            cleanup_result["errors"].append(str(e))
            return cleanup_result
    
    def get_system_stats(self) -> Dict:
        """获取系统统计信息"""
        return {
            "total_agents": len(self.list_available_agents()),
            "loaded_agents": len(self.loaded_agents),
            "memory_usage": {
                "loaded_classes": len(self.agent_classes),
                "cached_configs": len(self.agent_configs)
            },
            "generated_agents_dir": str(self.generated_agents_dir),
            "python_path_added": str(self.generated_agents_dir) in sys.path,
            "last_updated": utc_now().isoformat()
        }


# 全局实例
_dynamic_loader_instance = None

def get_dynamic_loader() -> DynamicLoader:
    """获取动态加载器实例（单例模式）"""
    global _dynamic_loader_instance
    if _dynamic_loader_instance is None:
        _dynamic_loader_instance = DynamicLoader()
    return _dynamic_loader_instance
