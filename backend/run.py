#!/usr/bin/env python3
"""
Development server runner for Meta-Agent backend.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

import uvicorn
from app.core.config import settings
from app.core.database import init_db
from app.core.logging import setup_logging


async def startup():
    """Startup tasks."""
    print(f"🚀 Starting {settings.APP_NAME} v{settings.APP_VERSION}")
    print(f"Environment: {settings.ENVIRONMENT}")
    print(f"Debug mode: {settings.DEBUG}")
    
    # Setup logging
    setup_logging()
    
    # Initialize database
    await init_db()
    
    print("✅ Startup completed")


def main():
    """Main entry point."""
    # Run startup tasks
    asyncio.run(startup())
    
    # Start the server
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.RELOAD and settings.DEBUG,
        reload_dirs=settings.RELOAD_DIRS if settings.RELOAD else None,
        reload_includes=settings.RELOAD_INCLUDES if settings.RELOAD else None,
        reload_excludes=settings.RELOAD_EXCLUDES if settings.RELOAD else None,
        log_level=settings.LOG_LEVEL.lower(),
    )


if __name__ == "__main__":
    main()
