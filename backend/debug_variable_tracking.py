#!/usr/bin/env python3
"""
Debug script to trace variable tracking issues.

This script helps identify where the variable tracking chain breaks.
"""

import asyncio
import aiohttp
import json
import sys
import os
from datetime import datetime

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Test configuration
BASE_URL = "http://localhost:8000"
TEST_AGENT_ID = "agent_test"  # You may need to adjust this to a real agent
TEST_INPUT = "测试变量跟踪调试 - 请分析这个问题并提供解决方案"

async def debug_variable_tracking():
    """Debug the complete variable tracking chain."""
    print("🔍 Debugging variable tracking chain...\n")
    
    # Step 1: Start test execution
    print("📋 Step 1: Starting test execution...")
    test_id = await start_test_execution()
    if not test_id:
        print("❌ Cannot proceed without test_id")
        return False
    
    print(f"✅ Test started with ID: {test_id}\n")
    
    # Step 2: Check if agent exists
    print("🤖 Step 2: Checking if agent exists...")
    agent_exists = await check_agent_exists(TEST_AGENT_ID)
    if not agent_exists:
        print(f"❌ Agent {TEST_AGENT_ID} does not exist. Please use a valid agent ID.")
        print("Available agents can be found at: GET /api/v1/agents/")
        return False
    
    print(f"✅ Agent {TEST_AGENT_ID} exists\n")
    
    # Step 3: Execute agent with variable tracking
    print("⚡ Step 3: Executing agent with variable tracking...")
    execution_success = await execute_agent_with_tracking(TEST_AGENT_ID, test_id)
    
    if not execution_success:
        print("❌ Agent execution failed")
        return False
    
    print("✅ Agent execution completed\n")
    
    # Step 4: Check variable tracker storage
    print("📊 Step 4: Checking VariableTracker storage...")
    await check_variable_tracker_storage(test_id)
    print()
    
    # Step 5: Check database storage
    print("🗄️ Step 5: Checking database storage...")
    await check_database_storage(test_id)
    print()
    
    return True

async def start_test_execution():
    """Start a test execution and return test_id."""
    url = f"{BASE_URL}/api/v1/test-execution/start"
    headers = {"Content-Type": "application/json"}
    
    payload = {
        "agent_id": TEST_AGENT_ID,
        "input_text": TEST_INPUT,
        "ai_config_override": {
            "provider": "openai",
            "model": "gpt-4",
            "temperature": 0.7
        },
        "api_key_name": "调试测试密钥",
        "input_metadata": {
            "timestamp": datetime.now().isoformat(),
            "test_type": "debug_variable_tracking"
        }
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=payload, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    return result.get('test_id')
                else:
                    error_text = await response.text()
                    print(f"❌ Failed to start test execution: {response.status}")
                    print(f"   Error: {error_text}")
                    return None
    except Exception as e:
        print(f"❌ Exception during test start: {str(e)}")
        return None

async def check_agent_exists(agent_id: str):
    """Check if the agent exists."""
    url = f"{BASE_URL}/api/v1/agents/{agent_id}/info"
    headers = {"Content-Type": "application/json"}
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers) as response:
                return response.status == 200
    except Exception as e:
        print(f"❌ Exception checking agent: {str(e)}")
        return False

async def execute_agent_with_tracking(agent_id: str, test_id: str):
    """Execute agent with variable tracking enabled."""
    url = f"{BASE_URL}/api/v1/agents/{agent_id}/execute"
    headers = {"Content-Type": "application/json"}
    
    payload = {
        "input": TEST_INPUT,
        "test_id": test_id,  # This is crucial for variable tracking
        "options": {
            "stream": False  # Use sync execution for simpler debugging
        }
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=payload, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"   ✅ Agent execution successful")
                    print(f"   📊 Response status: {result.get('success', 'unknown')}")
                    return True
                else:
                    error_text = await response.text()
                    print(f"   ❌ Agent execution failed: {response.status}")
                    print(f"   Error: {error_text}")
                    return False
    except Exception as e:
        print(f"   ❌ Exception during agent execution: {str(e)}")
        return False

async def check_variable_tracker_storage(test_id: str):
    """Check if VariableTracker has stored data for the test."""
    try:
        # This requires importing the variable tracker directly
        from app.services.websocket_service import variable_tracker
        
        stored_data = variable_tracker.get_stored_variables(test_id)
        
        if stored_data:
            print(f"   ✅ VariableTracker has data for test {test_id}")
            print(f"   📊 Context placeholders: {len(stored_data.get('context_placeholders_used', []))}")
            print(f"   📊 Team interactions: {len(stored_data.get('team_member_interactions', []))}")
            print(f"   📊 Context summary: {stored_data.get('context_summary', {})}")
            
            # Show first few variables
            placeholders = stored_data.get('context_placeholders_used', [])
            if placeholders:
                print(f"   📝 First variable: {placeholders[0].get('variable_name', 'Unknown')}")
        else:
            print(f"   ❌ No data found in VariableTracker for test {test_id}")
            print(f"   🔍 Available test IDs in storage: {list(variable_tracker._variable_storage.keys())}")
            
    except Exception as e:
        print(f"   ❌ Error checking VariableTracker: {str(e)}")

async def check_database_storage(test_id: str):
    """Check if variable data was stored in the database."""
    url = f"{BASE_URL}/api/v1/test-history/{test_id}"
    headers = {"Content-Type": "application/json"}
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    
                    # Check for variable data fields
                    placeholders = result.get('context_placeholders_used')
                    interactions = result.get('team_member_interactions')
                    summary = result.get('context_summary')
                    
                    print(f"   📋 Test record found in database")
                    print(f"   📊 Status: {result.get('status', 'unknown')}")
                    
                    if placeholders:
                        if isinstance(placeholders, str):
                            try:
                                placeholders = json.loads(placeholders)
                            except:
                                pass
                        print(f"   ✅ Context placeholders: {len(placeholders) if isinstance(placeholders, list) else 'Invalid data'}")
                    else:
                        print("   ❌ No context placeholders in database")
                    
                    if interactions:
                        if isinstance(interactions, str):
                            try:
                                interactions = json.loads(interactions)
                            except:
                                pass
                        print(f"   ✅ Team interactions: {len(interactions) if isinstance(interactions, list) else 'Invalid data'}")
                    else:
                        print("   ❌ No team interactions in database")
                    
                    if summary:
                        if isinstance(summary, str):
                            try:
                                summary = json.loads(summary)
                            except:
                                pass
                        print(f"   ✅ Context summary: {summary}")
                    else:
                        print("   ❌ No context summary in database")
                    
                    return bool(placeholders or interactions or summary)
                    
                else:
                    error_text = await response.text()
                    print(f"   ❌ Failed to get test history: {response.status}")
                    print(f"   Error: {error_text}")
                    return False
                    
    except Exception as e:
        print(f"   ❌ Database check failed: {str(e)}")
        return False

async def main():
    """Run the debug process."""
    print("🔍 Variable Tracking Debug Tool\n")
    print(f"Testing with agent: {TEST_AGENT_ID}")
    print(f"Input: {TEST_INPUT}\n")
    
    success = await debug_variable_tracking()
    
    print("🎯 Debug Summary:")
    if success:
        print("✅ Debug process completed")
        print("📋 Check the logs above for detailed information")
    else:
        print("❌ Debug process failed")
    
    print("\n💡 Troubleshooting tips:")
    print("1. Make sure the backend server is running")
    print("2. Check that the agent ID exists")
    print("3. Look for error messages in the server logs")
    print("4. Verify that variable tracking is enabled in the agent configuration")

if __name__ == "__main__":
    asyncio.run(main())
