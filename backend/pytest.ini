[tool:pytest]
minversion = 6.0
addopts = 
    --strict-markers
    --strict-config
    --cov=app
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-fail-under=80
    --cov-branch
    -ra
    --tb=short
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow tests (may take several seconds)
    performance: Performance tests
    external: Tests that require external services
    database_operations: Tests that perform actual database operations
    index_conflict: Tests that may cause database index conflicts
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::pytest.PytestCollectionWarning
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function
