"""
Tests for template models.
"""

import pytest
from datetime import datetime, timezone
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.planning import (
    Template, TemplateCreate, TemplateUpdate, TemplateResponse,
    TemplateCategory, TemplateDifficulty, TemplateVisibility, TemplateStatus
)
from app.models.user import User


class TestTemplateModels:
    """Test template model functionality."""

    @pytest.fixture
    async def sample_user(self, db_session: AsyncSession) -> User:
        """Create a sample user for testing."""
        user = User(
            email="<EMAIL>",
            name="Test User",
            hashed_password="hashed_password",
            is_active=True,
            is_verified=True
        )
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    @pytest.fixture
    def sample_template_data(self) -> dict:
        """Sample template data for testing."""
        return {
            "template_id": "template_test123",
            "name": "Test Template",
            "description": "A test template for unit testing",
            "category": TemplateCategory.TECHNICAL,
            "difficulty": TemplateDifficulty.INTERMEDIATE,
            "visibility": TemplateVisibility.PRIVATE,
            "status": TemplateStatus.ACTIVE,
            "prompt_template": "This is a test prompt template with {variable}",
            "team_structure_template": {
                "team_name": "Test Team",
                "team_members": [
                    {
                        "name": "Test Agent",
                        "role": "analyst",
                        "description": "A test agent"
                    }
                ]
            },
            "default_config": {"temperature": 0.7},
            "tags": ["test", "unit-test"],
            "keywords": ["testing", "automation"],
            "use_case": "Testing template functionality",
            "example_input": "Test input example",
            "expected_output": "Test output example",
            "version": "1.0.0",
            "author_name": "Test Author",
            "usage_count": 0,
            "rating_count": 0,
            "metadata": {"test": True}
        }

    async def test_template_creation(self, db_session: AsyncSession, sample_user: User, sample_template_data: dict):
        """Test template creation."""
        template_data = sample_template_data.copy()
        template_data["user_id"] = sample_user.id
        
        template = Template(**template_data)
        db_session.add(template)
        await db_session.commit()
        await db_session.refresh(template)

        assert template.id is not None
        assert template.template_id == "template_test123"
        assert template.name == "Test Template"
        assert template.category == TemplateCategory.TECHNICAL
        assert template.difficulty == TemplateDifficulty.INTERMEDIATE
        assert template.visibility == TemplateVisibility.PRIVATE
        assert template.status == TemplateStatus.ACTIVE
        assert template.user_id == sample_user.id
        assert template.created_at is not None
        assert template.updated_at is None

    async def test_template_relationships(self, db_session: AsyncSession, sample_user: User, sample_template_data: dict):
        """Test template relationships with user."""
        template_data = sample_template_data.copy()
        template_data["user_id"] = sample_user.id
        
        template = Template(**template_data)
        db_session.add(template)
        await db_session.commit()
        await db_session.refresh(template)

        # Test user relationship
        await db_session.refresh(sample_user)
        assert len(sample_user.templates) == 1
        assert sample_user.templates[0].template_id == "template_test123"

    def test_template_create_model(self):
        """Test TemplateCreate model validation."""
        template_create = TemplateCreate(
            name="Test Template",
            description="A test template",
            category=TemplateCategory.BUSINESS,
            difficulty=TemplateDifficulty.BEGINNER,
            prompt_template="Test prompt",
            team_structure_template={"team_name": "Test"}
        )
        
        assert template_create.name == "Test Template"
        assert template_create.category == TemplateCategory.BUSINESS
        assert template_create.difficulty == TemplateDifficulty.BEGINNER
        assert template_create.visibility == TemplateVisibility.PRIVATE  # Default
        assert template_create.status == TemplateStatus.ACTIVE  # Default

    def test_template_update_model(self):
        """Test TemplateUpdate model."""
        template_update = TemplateUpdate(
            name="Updated Template",
            description="Updated description",
            visibility=TemplateVisibility.PUBLIC
        )
        
        assert template_update.name == "Updated Template"
        assert template_update.description == "Updated description"
        assert template_update.visibility == TemplateVisibility.PUBLIC
        assert template_update.category is None  # Not updated

    async def test_template_versioning(self, db_session: AsyncSession, sample_user: User, sample_template_data: dict):
        """Test template versioning functionality."""
        # Create parent template
        parent_data = sample_template_data.copy()
        parent_data["user_id"] = sample_user.id
        parent_template = Template(**parent_data)
        db_session.add(parent_template)
        await db_session.commit()
        await db_session.refresh(parent_template)

        # Create version template
        version_data = sample_template_data.copy()
        version_data["template_id"] = "template_test123_v2"
        version_data["user_id"] = sample_user.id
        version_data["parent_template_id"] = parent_template.template_id
        version_data["version"] = "1.0.1"
        
        version_template = Template(**version_data)
        db_session.add(version_template)
        await db_session.commit()
        await db_session.refresh(version_template)

        assert version_template.parent_template_id == parent_template.template_id
        assert version_template.version == "1.0.1"

    async def test_template_from_agent(self, db_session: AsyncSession, sample_user: User, sample_template_data: dict):
        """Test template created from agent."""
        template_data = sample_template_data.copy()
        template_data["user_id"] = sample_user.id
        template_data["source_agent_id"] = "agent_123"
        template_data["metadata"] = {
            "created_from_agent": True,
            "source_agent_name": "Test Agent"
        }
        
        template = Template(**template_data)
        db_session.add(template)
        await db_session.commit()
        await db_session.refresh(template)

        assert template.source_agent_id == "agent_123"
        assert template.metadata["created_from_agent"] is True
        assert template.metadata["source_agent_name"] == "Test Agent"

    def test_template_visibility_enum(self):
        """Test template visibility enumeration."""
        assert TemplateVisibility.PRIVATE == "private"
        assert TemplateVisibility.SHARED == "shared"
        assert TemplateVisibility.PUBLIC == "public"
        assert TemplateVisibility.FEATURED == "featured"

    def test_template_status_enum(self):
        """Test template status enumeration."""
        assert TemplateStatus.DRAFT == "draft"
        assert TemplateStatus.ACTIVE == "active"
        assert TemplateStatus.ARCHIVED == "archived"
        assert TemplateStatus.DEPRECATED == "deprecated"

    def test_template_category_enum(self):
        """Test template category enumeration."""
        assert TemplateCategory.BUSINESS == "business"
        assert TemplateCategory.TECHNICAL == "technical"
        assert TemplateCategory.CREATIVE == "creative"
        assert TemplateCategory.ANALYSIS == "analysis"

    def test_template_difficulty_enum(self):
        """Test template difficulty enumeration."""
        assert TemplateDifficulty.BEGINNER == "beginner"
        assert TemplateDifficulty.INTERMEDIATE == "intermediate"
        assert TemplateDifficulty.ADVANCED == "advanced"
        assert TemplateDifficulty.EXPERT == "expert"

    async def test_template_search_fields(self, db_session: AsyncSession, sample_user: User, sample_template_data: dict):
        """Test template search-related fields."""
        template_data = sample_template_data.copy()
        template_data["user_id"] = sample_user.id
        template_data["tags"] = ["python", "automation", "testing"]
        template_data["keywords"] = ["test", "unit", "pytest"]
        
        template = Template(**template_data)
        db_session.add(template)
        await db_session.commit()
        await db_session.refresh(template)

        assert "python" in template.tags
        assert "automation" in template.tags
        assert "testing" in template.tags
        assert "test" in template.keywords
        assert "unit" in template.keywords
        assert "pytest" in template.keywords

    async def test_template_usage_tracking(self, db_session: AsyncSession, sample_user: User, sample_template_data: dict):
        """Test template usage and rating tracking."""
        template_data = sample_template_data.copy()
        template_data["user_id"] = sample_user.id
        template_data["usage_count"] = 10
        template_data["rating"] = 4.5
        template_data["rating_count"] = 5
        
        template = Template(**template_data)
        db_session.add(template)
        await db_session.commit()
        await db_session.refresh(template)

        assert template.usage_count == 10
        assert template.rating == 4.5
        assert template.rating_count == 5

    def test_template_response_model(self, sample_template_data: dict):
        """Test TemplateResponse model."""
        response_data = sample_template_data.copy()
        response_data.update({
            "id": 1,
            "created_at": datetime.now(timezone.utc),
            "updated_at": None,
            "is_owner": True,
            "can_edit": True
        })
        
        template_response = TemplateResponse(**response_data)
        
        assert template_response.id == 1
        assert template_response.template_id == "template_test123"
        assert template_response.is_owner is True
        assert template_response.can_edit is True
        assert template_response.created_at is not None

    async def test_template_soft_delete(self, db_session: AsyncSession, sample_user: User, sample_template_data: dict):
        """Test template soft delete functionality."""
        template_data = sample_template_data.copy()
        template_data["user_id"] = sample_user.id
        
        template = Template(**template_data)
        db_session.add(template)
        await db_session.commit()
        await db_session.refresh(template)

        # Soft delete by changing status
        template.status = TemplateStatus.ARCHIVED
        template.updated_at = datetime.now(timezone.utc)
        await db_session.commit()
        await db_session.refresh(template)

        assert template.status == TemplateStatus.ARCHIVED
        assert template.updated_at is not None
