"""
Authentication test suite runner.
"""

import pytest
import sys
import os
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))


def run_auth_tests():
    """Run all authentication-related tests."""
    
    # Test paths for authentication
    auth_test_paths = [
        "tests/unit/models/test_user.py",
        "tests/unit/services/test_user_service.py", 
        "tests/unit/core/test_security.py",
        "tests/integration/api/test_auth_endpoints.py",
    ]
    
    # Test configuration
    pytest_args = [
        "-v",  # Verbose output
        "--tb=short",  # Short traceback format
        "--strict-markers",  # Strict marker checking
        "--disable-warnings",  # Disable warnings for cleaner output
        "-x",  # Stop on first failure
        "--cov=app.models.user",  # Coverage for user models
        "--cov=app.services.user_service",  # Coverage for user service
        "--cov=app.core.security",  # Coverage for security
        "--cov=app.api.v1.endpoints.auth",  # Coverage for auth endpoints
        "--cov-report=term-missing",  # Show missing lines
        "--cov-report=html:htmlcov/auth",  # HTML coverage report
        "--cov-fail-under=80",  # Fail if coverage below 80%
    ]
    
    # Add test paths
    pytest_args.extend(auth_test_paths)
    
    print("🔐 Running Authentication Test Suite")
    print("=" * 50)
    
    # Run tests
    exit_code = pytest.main(pytest_args)
    
    if exit_code == 0:
        print("\n✅ All authentication tests passed!")
        print("📊 Coverage report generated in htmlcov/auth/")
    else:
        print("\n❌ Some authentication tests failed!")
        print(f"Exit code: {exit_code}")
    
    return exit_code


def run_security_tests():
    """Run security-specific tests."""
    
    security_test_paths = [
        "tests/unit/core/test_security.py",
        "-k", "security or password or token or rate_limit"
    ]
    
    pytest_args = [
        "-v",
        "--tb=short",
        "--cov=app.core.security",
        "--cov-report=term-missing",
    ]
    
    pytest_args.extend(security_test_paths)
    
    print("🛡️  Running Security Test Suite")
    print("=" * 50)
    
    exit_code = pytest.main(pytest_args)
    
    if exit_code == 0:
        print("\n✅ All security tests passed!")
    else:
        print("\n❌ Some security tests failed!")
    
    return exit_code


def run_integration_tests():
    """Run integration tests only."""
    
    integration_test_paths = [
        "tests/integration/api/test_auth_endpoints.py",
    ]
    
    pytest_args = [
        "-v",
        "--tb=short", 
        "-m", "integration",  # Only integration tests
        "--cov=app.api.v1.endpoints.auth",
        "--cov-report=term-missing",
    ]
    
    pytest_args.extend(integration_test_paths)
    
    print("🔗 Running Integration Test Suite")
    print("=" * 50)
    
    exit_code = pytest.main(pytest_args)
    
    if exit_code == 0:
        print("\n✅ All integration tests passed!")
    else:
        print("\n❌ Some integration tests failed!")
    
    return exit_code


def run_unit_tests():
    """Run unit tests only."""
    
    unit_test_paths = [
        "tests/unit/models/test_user.py",
        "tests/unit/services/test_user_service.py",
        "tests/unit/core/test_security.py",
    ]
    
    pytest_args = [
        "-v",
        "--tb=short",
        "-m", "unit",  # Only unit tests
        "--cov=app.models.user",
        "--cov=app.services.user_service", 
        "--cov=app.core.security",
        "--cov-report=term-missing",
        "--cov-fail-under=85",  # Higher coverage requirement for unit tests
    ]
    
    pytest_args.extend(unit_test_paths)
    
    print("🧪 Running Unit Test Suite")
    print("=" * 50)
    
    exit_code = pytest.main(pytest_args)
    
    if exit_code == 0:
        print("\n✅ All unit tests passed!")
    else:
        print("\n❌ Some unit tests failed!")
    
    return exit_code


def run_performance_tests():
    """Run performance tests for authentication."""
    
    # This would include load testing for auth endpoints
    print("⚡ Performance tests not implemented yet")
    print("Consider adding:")
    print("- Login endpoint load testing")
    print("- Password hashing performance")
    print("- Token generation/verification speed")
    print("- Rate limiting effectiveness")
    
    return 0


def main():
    """Main test runner."""
    
    if len(sys.argv) > 1:
        test_type = sys.argv[1].lower()
        
        if test_type == "unit":
            return run_unit_tests()
        elif test_type == "integration":
            return run_integration_tests()
        elif test_type == "security":
            return run_security_tests()
        elif test_type == "performance":
            return run_performance_tests()
        elif test_type == "all":
            return run_auth_tests()
        else:
            print(f"Unknown test type: {test_type}")
            print("Available options: unit, integration, security, performance, all")
            return 1
    else:
        # Run all tests by default
        return run_auth_tests()


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
