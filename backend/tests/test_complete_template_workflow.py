"""
端到端测试：完整的模板工作流
验证从模板创建的Agent可以直接部署和使用，无需额外的AI生成步骤
"""

import pytest
import json
from datetime import datetime, timezone
from unittest.mock import AsyncMock, patch

from app.services.complete_templates import get_complete_templates_service
from app.services.template_management_service import TemplateManagementService
from app.services.dynamic_loader import DynamicLoader, ConfigDrivenAgent
from app.models.user import User
from app.models.agent import Agent, AgentStatus, AgentType


class TestCompleteTemplateWorkflow:
    """测试完整的模板工作流程"""

    @pytest.fixture
    def mock_user(self):
        """创建测试用户"""
        return User(
            id=1,
            name="Test User",
            email="<EMAIL>",
            is_active=True
        )

    @pytest.fixture
    def complete_templates_service(self):
        """获取完整模板服务"""
        return get_complete_templates_service()

    @pytest.fixture
    def sample_template_config(self):
        """示例模板配置"""
        return {
            "team_name": "测试侦探团队",
            "description": "专业的测试侦探团队",
            "objective": "解决测试案件",
            "domain": "testing",
            "complexity": "intermediate",
            
            "team_members": [
                {
                    "name": "测试分析师",
                    "role": "analyst",
                    "description": "负责分析测试案件",
                    "system_prompt": "你是一位专业的测试分析师，擅长分析复杂的测试案件。",
                    "capabilities": ["逻辑推理", "数据分析", "模式识别"],
                    "tools": ["分析工具", "测试框架"],
                    "model_config": {
                        "model": "gpt-4",
                        "temperature": 0.3,
                        "max_tokens": 2000
                    }
                },
                {
                    "name": "测试执行员",
                    "role": "executor",
                    "description": "负责执行测试任务",
                    "system_prompt": "你是一位经验丰富的测试执行员，负责执行各种测试任务。",
                    "capabilities": ["测试执行", "问题定位", "结果验证"],
                    "tools": ["测试工具", "验证工具"],
                    "model_config": {
                        "model": "gpt-4",
                        "temperature": 0.5,
                        "max_tokens": 1500
                    }
                }
            ],
            
            "workflow": {
                "steps": [
                    {
                        "name": "案件分析",
                        "description": "分析测试案件的关键信息",
                        "assignee": "测试分析师",
                        "inputs": ["案件描述", "测试数据"],
                        "outputs": ["分析报告", "测试计划"],
                        "dependencies": [],
                        "timeout": 300
                    },
                    {
                        "name": "测试执行",
                        "description": "执行测试计划",
                        "assignee": "测试执行员",
                        "inputs": ["分析报告", "测试计划"],
                        "outputs": ["测试结果", "问题报告"],
                        "dependencies": ["案件分析"],
                        "timeout": 600
                    }
                ],
                "coordination": {
                    "orchestrator": "测试分析师",
                    "communication_style": "协作式",
                    "decision_making": "共识决策"
                }
            },
            
            "configuration": {
                "execution_mode": "sequential",
                "timeout_per_step": 300,
                "max_iterations": 3,
                "error_handling": "graceful_degradation"
            }
        }

    def test_complete_templates_service_initialization(self, complete_templates_service):
        """测试完整模板服务初始化"""
        assert complete_templates_service is not None
        assert len(complete_templates_service.templates) > 0
        
        # 检查模板格式
        for template in complete_templates_service.templates:
            assert "id" in template
            assert "name" in template
            assert "team_config" in template
            assert "metadata" in template

    def test_get_template_by_id(self, complete_templates_service):
        """测试根据ID获取模板"""
        # 获取第一个模板
        first_template = complete_templates_service.templates[0]
        template_id = first_template["id"]
        
        # 通过ID获取模板
        retrieved_template = complete_templates_service.get_template_by_id(template_id)
        
        assert retrieved_template is not None
        assert retrieved_template["id"] == template_id
        assert retrieved_template["team_config"] is not None

    def test_get_template_config(self, complete_templates_service):
        """测试获取模板的团队配置"""
        # 获取第一个模板的配置
        first_template = complete_templates_service.templates[0]
        template_id = first_template["id"]
        
        config = complete_templates_service.get_template_config(template_id)
        
        assert config is not None
        assert "team_members" in config
        assert "workflow" in config
        assert len(config["team_members"]) > 0

    def test_config_driven_agent_initialization(self, sample_template_config):
        """测试配置驱动Agent的初始化"""
        agent_config = {
            "agent_id": "test_agent_123",
            "team_name": "测试团队",
            "team_plan": sample_template_config
        }
        
        agent = ConfigDrivenAgent(agent_config)
        
        assert agent.agent_id == "test_agent_123"
        assert agent.team_name == "测试团队"
        assert len(agent.team_members) == 2
        assert len(agent.workflow.get("steps", [])) == 2

    @pytest.mark.asyncio
    async def test_config_driven_agent_execution(self, sample_template_config):
        """测试配置驱动Agent的执行"""
        agent_config = {
            "agent_id": "test_agent_123",
            "team_name": "测试团队",
            "team_plan": sample_template_config
        }
        
        agent = ConfigDrivenAgent(agent_config)
        
        # 执行测试
        input_data = {
            "input": "测试案件：系统登录失败",
            "context": {"priority": "high"}
        }
        
        result = await agent.execute(input_data)
        
        assert result is not None
        assert result["status"] == "success"
        assert result["agent_id"] == "test_agent_123"
        assert "results" in result
        assert len(result["results"]) == 2  # 两个工作流步骤

    @pytest.mark.asyncio
    async def test_dynamic_loader_config_driven_detection(self, sample_template_config):
        """测试动态加载器对配置驱动Agent的检测"""
        loader = DynamicLoader()
        
        # 完整的配置（应该被识别为配置驱动）
        complete_config = {
            "agent_id": "test_agent_123",
            "team_plan": sample_template_config,
            "metadata": {
                "ready_to_deploy": True,
                "requires_ai_generation": False
            }
        }
        
        assert loader._is_config_driven_agent(complete_config) == True
        
        # 不完整的配置（应该回退到代码生成）
        incomplete_config = {
            "agent_id": "test_agent_456",
            "team_plan": {"team_name": "Incomplete Team"},
            "metadata": {
                "ready_to_deploy": False,
                "requires_ai_generation": True
            }
        }
        
        assert loader._is_config_driven_agent(incomplete_config) == False

    @pytest.mark.asyncio
    async def test_dynamic_loader_execution(self, sample_template_config):
        """测试动态加载器执行配置驱动Agent"""
        loader = DynamicLoader()
        
        agent_config = {
            "agent_id": "test_agent_123",
            "team_name": "测试团队",
            "team_plan": sample_template_config,
            "metadata": {
                "ready_to_deploy": True,
                "requires_ai_generation": False
            }
        }
        
        # 执行Agent
        input_data = {
            "input": "测试任务：验证用户登录功能",
            "user_data": {"user_id": "test_user"}
        }
        
        result = await loader.execute_agent("test_agent_123", input_data, agent_config)
        
        assert result is not None
        assert result["agent_id"] == "test_agent_123"
        assert result["status"] in ["success", "completed"]

    @pytest.mark.asyncio
    async def test_template_validation_complete_template(self, mock_user, sample_template_config):
        """测试完整模板的验证"""
        # 模拟数据库会话
        mock_db = AsyncMock()
        
        # 模拟模板数据
        mock_template = AsyncMock()
        mock_template._fields = ["template_id", "name", "description", "team_structure_template", "template_metadata"]
        mock_template.template_id = "test_template_123"
        mock_template.name = "测试模板"
        mock_template.description = "这是一个测试模板"
        mock_template.team_structure_template = json.dumps(sample_template_config)
        mock_template.template_metadata = json.dumps({
            "ready_to_deploy": True,
            "template_type": "complete_deployable"
        })
        
        # 模拟数据库查询
        mock_result = AsyncMock()
        mock_result.fetchone.return_value = mock_template
        mock_db.execute.return_value = mock_result
        
        # 创建模板管理服务
        template_service = TemplateManagementService(mock_db)
        
        # 执行验证
        validation_result = await template_service.validate_template_completeness("test_template_123", mock_user)
        
        assert validation_result is not None
        assert validation_result["is_complete"] == True
        assert validation_result["is_deployable"] == True
        assert validation_result["validation_score"] >= 0.8
        assert len(validation_result["issues"]) == 0

    @pytest.mark.asyncio
    async def test_end_to_end_template_to_agent_workflow(self, complete_templates_service, sample_template_config):
        """端到端测试：从模板到可执行Agent的完整工作流"""
        
        # 1. 获取模板配置
        template_id = "detective_team"  # 使用预置的侦探团队模板
        template_config = complete_templates_service.get_template_config(template_id)
        
        assert template_config is not None
        assert "team_members" in template_config
        
        # 2. 创建Agent配置
        agent_config = {
            "agent_id": "test_agent_from_template",
            "team_name": template_config["team_name"],
            "team_plan": template_config,
            "metadata": {
                "created_from_template": True,
                "template_id": template_id,
                "ready_to_deploy": True,
                "requires_ai_generation": False
            }
        }
        
        # 3. 使用动态加载器加载Agent
        loader = DynamicLoader()
        agent_instance = await loader.load_agent("test_agent_from_template", agent_config)
        
        assert agent_instance is not None
        assert isinstance(agent_instance, ConfigDrivenAgent)
        
        # 4. 执行Agent
        input_data = {
            "input": "调查案件：办公室电脑被盗",
            "context": {"location": "办公室", "time": "昨晚"}
        }
        
        result = await agent_instance.execute(input_data)
        
        # 5. 验证执行结果
        assert result is not None
        assert result["status"] == "success"
        assert result["agent_id"] == "test_agent_from_template"
        assert "results" in result
        assert result["execution_method"] == "config_driven"
        
        # 6. 验证工作流程执行
        if "results" in result and result["results"]:
            # 检查是否按工作流程执行了多个步骤
            assert len(result["results"]) > 0
            for step_result in result["results"]:
                assert "step" in step_result
                assert "assignee" in step_result
                assert "status" in step_result

    def test_template_readiness_indicators(self, complete_templates_service):
        """测试模板就绪状态指示器"""
        templates = complete_templates_service.get_all_templates()
        
        for template in templates:
            # 检查就绪状态字段
            assert "ready_to_deploy" in template
            assert isinstance(template["ready_to_deploy"], bool)
            
            # 如果标记为就绪，应该有完整的配置
            if template["ready_to_deploy"]:
                full_template = complete_templates_service.get_template_by_id(template["id"])
                team_config = full_template["team_config"]
                
                assert "team_members" in team_config
                assert len(team_config["team_members"]) > 0
                
                # 检查团队成员的完整性
                for member in team_config["team_members"]:
                    assert "name" in member
                    assert "role" in member
                    assert "system_prompt" in member
                    assert len(member["system_prompt"]) > 50  # 足够详细的提示词


