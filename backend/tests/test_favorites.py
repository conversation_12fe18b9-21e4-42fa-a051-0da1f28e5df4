"""
Tests for agent favorites functionality.
"""

import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.ext.asyncio import AsyncSession
from app.main import app
from app.models.user import User
from app.models.agent import Agent, AgentStatus, AgentType
from app.models.favorites import UserAgentFavorite
from tests.fixtures.factories import UserFactory, AgentFactory


class TestAgentFavorites:
    """Test agent favorites endpoints."""

    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)

    @pytest.fixture
    async def test_user(self, db_session: AsyncSession):
        """Create test user."""
        user = UserFactory.create_user(
            name="Test User",
            email="<EMAIL>",
            password="testpass123"
        )
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    @pytest.fixture
    async def test_agent(self, db_session: AsyncSession, test_user: User):
        """Create test agent."""
        agent = AgentFactory.create_agent(
            agent_id="test-agent-1",
            team_name="Test Agent",
            description="Test agent for favorites",
            user_id=test_user.id,
            status=AgentStatus.ACTIVE
        )
        db_session.add(agent)
        await db_session.commit()
        await db_session.refresh(agent)
        return agent

    @pytest.fixture
    def auth_headers(self, test_user: User):
        """Create authentication headers."""
        # This would normally use JWT token generation
        # For now, we'll mock the authentication
        return {"Authorization": f"Bearer test-token-{test_user.id}"}

    async def test_toggle_favorite_add(
        self, 
        client: TestClient, 
        test_agent: Agent, 
        auth_headers: dict,
        db_session: AsyncSession
    ):
        """Test adding an agent to favorites."""
        response = client.post(
            f"/api/v1/agents/{test_agent.agent_id}/favorite",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["is_favorite"] is True
        assert data["agent_id"] == test_agent.agent_id
        assert "favorite_id" in data

        # Verify in database
        favorite = await db_session.get(UserAgentFavorite, data["favorite_id"])
        assert favorite is not None
        assert favorite.agent_id == test_agent.agent_id

    async def test_toggle_favorite_remove(
        self, 
        client: TestClient, 
        test_agent: Agent, 
        test_user: User,
        auth_headers: dict,
        db_session: AsyncSession
    ):
        """Test removing an agent from favorites."""
        # First add to favorites
        favorite = UserAgentFavorite(
            user_id=test_user.id,
            agent_id=test_agent.agent_id
        )
        db_session.add(favorite)
        await db_session.commit()

        # Then remove
        response = client.post(
            f"/api/v1/agents/{test_agent.agent_id}/favorite",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["is_favorite"] is False
        assert data["agent_id"] == test_agent.agent_id

    async def test_get_favorites_empty(
        self, 
        client: TestClient, 
        auth_headers: dict
    ):
        """Test getting favorites when none exist."""
        response = client.get(
            "/api/v1/agents/favorites",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) == 0

    async def test_get_favorites_with_data(
        self, 
        client: TestClient, 
        test_agent: Agent,
        test_user: User,
        auth_headers: dict,
        db_session: AsyncSession
    ):
        """Test getting favorites with data."""
        # Add agent to favorites
        favorite = UserAgentFavorite(
            user_id=test_user.id,
            agent_id=test_agent.agent_id
        )
        db_session.add(favorite)
        await db_session.commit()

        response = client.get(
            "/api/v1/agents/favorites",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) == 1
        
        favorite_agent = data[0]
        assert favorite_agent["agent_id"] == test_agent.agent_id
        assert favorite_agent["name"] == test_agent.team_name
        assert favorite_agent["description"] == test_agent.description
        assert "favorite_id" in favorite_agent
        assert "favorited_at" in favorite_agent

    async def test_toggle_favorite_nonexistent_agent(
        self, 
        client: TestClient, 
        auth_headers: dict
    ):
        """Test toggling favorite for non-existent agent."""
        response = client.post(
            "/api/v1/agents/nonexistent-agent/favorite",
            headers=auth_headers
        )
        
        assert response.status_code == 404

    async def test_get_favorites_with_performance(
        self, 
        client: TestClient, 
        test_agent: Agent,
        test_user: User,
        auth_headers: dict,
        db_session: AsyncSession
    ):
        """Test getting favorites with performance metrics."""
        # Add agent to favorites
        favorite = UserAgentFavorite(
            user_id=test_user.id,
            agent_id=test_agent.agent_id
        )
        db_session.add(favorite)
        await db_session.commit()

        response = client.get(
            "/api/v1/agents/favorites?include_performance=true",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        
        favorite_agent = data[0]
        # Performance data might be None if no metrics exist
        assert "performance" in favorite_agent

    async def test_user_isolation(
        self, 
        client: TestClient, 
        test_agent: Agent,
        auth_headers: dict,
        db_session: AsyncSession
    ):
        """Test that users can only see their own favorites."""
        # Create another user
        other_user = UserFactory.create_user(
            name="Other User",
            email="<EMAIL>",
            password="otherpass123"
        )
        db_session.add(other_user)
        await db_session.commit()

        # Add agent to other user's favorites
        favorite = UserAgentFavorite(
            user_id=other_user.id,
            agent_id=test_agent.agent_id
        )
        db_session.add(favorite)
        await db_session.commit()

        # Current user should not see other user's favorites
        response = client.get(
            "/api/v1/agents/favorites",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 0

    async def test_favorite_duplicate_prevention(
        self, 
        client: TestClient, 
        test_agent: Agent,
        test_user: User,
        auth_headers: dict,
        db_session: AsyncSession
    ):
        """Test that duplicate favorites are handled correctly."""
        # Add to favorites twice
        response1 = client.post(
            f"/api/v1/agents/{test_agent.agent_id}/favorite",
            headers=auth_headers
        )
        response2 = client.post(
            f"/api/v1/agents/{test_agent.agent_id}/favorite",
            headers=auth_headers
        )
        
        assert response1.status_code == 200
        assert response2.status_code == 200
        
        data1 = response1.json()
        data2 = response2.json()
        
        assert data1["is_favorite"] is True
        assert data2["is_favorite"] is False  # Should toggle back to false

        # Verify only one favorite exists in database
        from sqlalchemy import text
        result = await db_session.execute(
            text("SELECT COUNT(*) FROM user_agent_favorites WHERE user_id = :user_id AND agent_id = :agent_id"),
            {"user_id": test_user.id, "agent_id": test_agent.agent_id}
        )
        count = result.scalar()
        assert count == 0  # Should be removed after second toggle
