"""
Tests for timezone handling utilities and consistency across the application.
"""

import pytest
from datetime import datetime, timezone, timedelta
from app.core.timezone_utils import (
    utc_now, to_utc, to_naive_utc, calculate_duration_ms,
    normalize_datetime_for_db, normalize_datetime_for_api,
    validate_datetime_consistency, format_duration
)


class TestTimezoneUtilities:
    """Test timezone utility functions."""
    
    def test_utc_now_returns_timezone_aware(self):
        """Test that utc_now returns timezone-aware datetime."""
        now = utc_now()
        assert now.tzinfo is not None
        assert now.tzinfo == timezone.utc
    
    def test_to_utc_with_naive_datetime(self):
        """Test converting naive datetime to UTC."""
        naive_dt = datetime(2025, 7, 17, 12, 0, 0)
        utc_dt = to_utc(naive_dt)
        
        assert utc_dt.tzinfo == timezone.utc
        assert utc_dt.replace(tzinfo=None) == naive_dt
    
    def test_to_utc_with_timezone_aware_datetime(self):
        """Test converting timezone-aware datetime to UTC."""
        # Create a datetime in a different timezone
        eastern = timezone(timedelta(hours=-5))
        eastern_dt = datetime(2025, 7, 17, 7, 0, 0, tzinfo=eastern)
        
        utc_dt = to_utc(eastern_dt)
        
        assert utc_dt.tzinfo == timezone.utc
        assert utc_dt.hour == 12  # 7 AM Eastern = 12 PM UTC
    
    def test_to_utc_with_iso_string(self):
        """Test converting ISO string to UTC datetime."""
        iso_string = "2025-07-17T12:00:00Z"
        utc_dt = to_utc(iso_string)
        
        assert utc_dt.tzinfo == timezone.utc
        assert utc_dt.year == 2025
        assert utc_dt.month == 7
        assert utc_dt.day == 17
        assert utc_dt.hour == 12
    
    def test_to_utc_with_iso_string_with_timezone(self):
        """Test converting ISO string with timezone to UTC."""
        iso_string = "2025-07-17T12:00:00+05:00"
        utc_dt = to_utc(iso_string)
        
        assert utc_dt.tzinfo == timezone.utc
        assert utc_dt.hour == 7  # 12 PM +5 = 7 AM UTC
    
    def test_to_utc_with_none(self):
        """Test that to_utc returns None for None input."""
        assert to_utc(None) is None
    
    def test_to_naive_utc(self):
        """Test converting to naive UTC datetime."""
        aware_dt = datetime(2025, 7, 17, 12, 0, 0, tzinfo=timezone.utc)
        naive_dt = to_naive_utc(aware_dt)
        
        assert naive_dt.tzinfo is None
        assert naive_dt.year == 2025
        assert naive_dt.hour == 12
    
    def test_calculate_duration_ms(self):
        """Test duration calculation in milliseconds."""
        start = datetime(2025, 7, 17, 12, 0, 0, tzinfo=timezone.utc)
        end = datetime(2025, 7, 17, 12, 1, 30, 500000, tzinfo=timezone.utc)  # 1.5 seconds later
        
        duration = calculate_duration_ms(start, end)
        assert duration == 90500  # 90.5 seconds = 90500 ms
    
    def test_calculate_duration_ms_with_strings(self):
        """Test duration calculation with ISO strings."""
        start_str = "2025-07-17T12:00:00Z"
        end_str = "2025-07-17T12:01:30.500Z"
        
        duration = calculate_duration_ms(start_str, end_str)
        assert duration == 90500
    
    def test_calculate_duration_ms_with_none(self):
        """Test duration calculation with None values."""
        start = datetime(2025, 7, 17, 12, 0, 0, tzinfo=timezone.utc)
        
        assert calculate_duration_ms(None, start) is None
        assert calculate_duration_ms(start, None) is None
        assert calculate_duration_ms(None, None) is None
    
    def test_normalize_datetime_for_db(self):
        """Test normalizing datetime for database storage."""
        aware_dt = datetime(2025, 7, 17, 12, 0, 0, tzinfo=timezone.utc)
        db_dt = normalize_datetime_for_db(aware_dt)
        
        assert db_dt.tzinfo is None
        assert db_dt.year == 2025
        assert db_dt.hour == 12
    
    def test_normalize_datetime_for_api(self):
        """Test normalizing datetime for API responses."""
        naive_dt = datetime(2025, 7, 17, 12, 0, 0)
        api_dt = normalize_datetime_for_api(naive_dt)

        assert api_dt.tzinfo == timezone.utc
        assert api_dt.year == 2025
        assert api_dt.hour == 12

    def test_normalize_datetime_for_api_with_string(self):
        """Test normalizing string datetime for API responses (SQLite scenario)."""
        # Test SQLite-style datetime string
        sqlite_dt_str = "2025-07-17 12:30:45.123456"
        api_dt = normalize_datetime_for_api(sqlite_dt_str)

        assert api_dt.tzinfo == timezone.utc
        assert api_dt.year == 2025
        assert api_dt.hour == 12
        assert api_dt.minute == 30
    
    def test_validate_datetime_consistency(self):
        """Test datetime consistency validation."""
        start = datetime(2025, 7, 17, 12, 0, 0, tzinfo=timezone.utc)
        end = datetime(2025, 7, 17, 12, 1, 0, tzinfo=timezone.utc)
        
        assert validate_datetime_consistency(start, end) is True
        assert validate_datetime_consistency(end, start) is False
        assert validate_datetime_consistency(None, end) is True
        assert validate_datetime_consistency(start, None) is True
    
    def test_format_duration(self):
        """Test duration formatting."""
        assert format_duration(500) == "500ms"
        assert format_duration(1500) == "1.5s"
        assert format_duration(90500) == "1m 30.5s"
        assert format_duration(None) == "未知"


class TestTimezoneEdgeCases:
    """Test edge cases and complex timezone scenarios."""
    
    def test_daylight_saving_time_transition(self):
        """Test handling of daylight saving time transitions."""
        # This test simulates a DST transition scenario
        # Note: This is a simplified test as actual DST handling depends on the timezone

        # Create timestamps around a typical DST transition
        before_dst = "2025-03-09T06:00:00-05:00"  # EST (11:00 UTC)
        after_dst = "2025-03-09T07:00:00-04:00"   # EDT (11:00 UTC) - same UTC time but different local time

        before_utc = to_utc(before_dst)
        after_utc = to_utc(after_dst)

        # Both should convert to the same UTC time during DST transition
        duration = calculate_duration_ms(before_utc, after_utc)
        assert duration == 0  # Same UTC time
    
    def test_cross_date_boundary(self):
        """Test calculations that cross date boundaries."""
        start = "2025-07-17T23:30:00Z"
        end = "2025-07-18T00:30:00Z"
        
        duration = calculate_duration_ms(start, end)
        assert duration == 3600000  # 1 hour = 3600000 ms
    
    def test_different_timezone_inputs(self):
        """Test handling inputs from different timezones."""
        tokyo_time = "2025-07-17T21:00:00+09:00"
        new_york_time = "2025-07-17T08:00:00-04:00"
        
        tokyo_utc = to_utc(tokyo_time)
        ny_utc = to_utc(new_york_time)
        
        # Both should convert to the same UTC time (12:00 UTC)
        assert tokyo_utc == ny_utc
    
    def test_microsecond_precision(self):
        """Test that microsecond precision is maintained."""
        start = datetime(2025, 7, 17, 12, 0, 0, 123456, tzinfo=timezone.utc)
        end = datetime(2025, 7, 17, 12, 0, 1, 654321, tzinfo=timezone.utc)
        
        duration = calculate_duration_ms(start, end)
        # 1 second + (654321 - 123456) microseconds = 1530.865 ms
        expected = 1000 + (654321 - 123456) // 1000
        assert duration == expected
    
    def test_invalid_datetime_string(self):
        """Test handling of invalid datetime strings."""
        with pytest.raises(ValueError):
            to_utc("invalid-datetime-string")
    
    def test_very_large_duration(self):
        """Test handling of very large durations."""
        start = datetime(2025, 1, 1, 0, 0, 0, tzinfo=timezone.utc)
        end = datetime(2025, 12, 31, 23, 59, 59, tzinfo=timezone.utc)
        
        duration = calculate_duration_ms(start, end)
        assert duration > 0
        assert isinstance(duration, int)
    
    def test_negative_duration_handling(self):
        """Test that negative durations are handled correctly."""
        start = datetime(2025, 7, 17, 12, 1, 0, tzinfo=timezone.utc)
        end = datetime(2025, 7, 17, 12, 0, 0, tzinfo=timezone.utc)
        
        duration = calculate_duration_ms(start, end)
        assert duration == -60000  # -1 minute


class TestDatabaseIntegration:
    """Test timezone handling in database operations."""
    
    def test_db_normalization_roundtrip(self):
        """Test that datetime survives DB normalization roundtrip."""
        original = utc_now()
        
        # Simulate storing in database
        db_format = normalize_datetime_for_db(original)
        
        # Simulate reading from database
        api_format = normalize_datetime_for_api(db_format)
        
        # Should be equivalent (within microsecond precision)
        assert abs((api_format - original).total_seconds()) < 0.001
    
    def test_iso_string_roundtrip(self):
        """Test that ISO strings survive conversion roundtrip."""
        iso_string = "2025-07-17T12:30:45.123456Z"
        
        # Convert to datetime and back
        dt = to_utc(iso_string)
        back_to_iso = dt.isoformat()
        
        # Should be equivalent
        assert back_to_iso.endswith("Z") or back_to_iso.endswith("+00:00")
