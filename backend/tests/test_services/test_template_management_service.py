"""
Tests for template management service.
"""

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.template_management_service import TemplateManagementService
from app.models.planning import (
    Template, TemplateCreate, TemplateCategory, TemplateDifficulty, 
    TemplateVisibility, TemplateStatus
)
from app.models.user import User
from app.models.agent import Agent
from app.core.exceptions import NotFoundError, ValidationError


class TestTemplateManagementService:
    """Test template management service functionality."""

    @pytest.fixture
    async def sample_user(self, db_session: AsyncSession) -> User:
        """Create a sample user for testing."""
        user = User(
            email="<EMAIL>",
            name="Test User",
            hashed_password="hashed_password",
            is_active=True,
            is_verified=True
        )
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    @pytest.fixture
    async def sample_agent(self, db_session: AsyncSession, sample_user: User) -> Agent:
        """Create a sample agent for testing."""
        agent = Agent(
            agent_id="agent_test123",
            team_name="Test Agent Team",
            description="A test agent team",
            prompt_template="Test agent prompt",
            team_plan={
                "team_name": "Test Agent Team",
                "team_members": [
                    {
                        "name": "Test Agent",
                        "role": "analyst",
                        "description": "A test agent"
                    }
                ]
            },
            user_id=sample_user.id
        )
        db_session.add(agent)
        await db_session.commit()
        await db_session.refresh(agent)
        return agent

    @pytest.fixture
    def template_service(self, db_session: AsyncSession) -> TemplateManagementService:
        """Create template management service instance."""
        return TemplateManagementService(db_session)

    @pytest.fixture
    def valid_template_data(self) -> TemplateCreate:
        """Create valid template data for testing."""
        return TemplateCreate(
            name="Test Template",
            description="A comprehensive test template for validation",
            category=TemplateCategory.TECHNICAL,
            difficulty=TemplateDifficulty.INTERMEDIATE,
            prompt_template="This is a detailed test prompt template with proper length",
            team_structure_template={
                "team_name": "Test Team",
                "team_members": [
                    {
                        "name": "Test Agent",
                        "role": "analyst",
                        "description": "A test agent for validation"
                    }
                ]
            },
            tags=["test", "validation"],
            use_case="Testing template validation functionality"
        )

    async def test_validate_template_data_success(
        self, 
        template_service: TemplateManagementService,
        valid_template_data: TemplateCreate
    ):
        """Test successful template data validation."""
        # Should not raise any exceptions
        await template_service.validate_template_data(valid_template_data)

    async def test_validate_template_data_short_name(
        self, 
        template_service: TemplateManagementService,
        valid_template_data: TemplateCreate
    ):
        """Test validation with short name."""
        valid_template_data.name = "AB"  # Too short
        
        with pytest.raises(ValidationError) as exc_info:
            await template_service.validate_template_data(valid_template_data)
        
        assert "at least 3 characters" in str(exc_info.value)

    async def test_validate_template_data_short_description(
        self, 
        template_service: TemplateManagementService,
        valid_template_data: TemplateCreate
    ):
        """Test validation with short description."""
        valid_template_data.description = "Short"  # Too short
        
        with pytest.raises(ValidationError) as exc_info:
            await template_service.validate_template_data(valid_template_data)
        
        assert "at least 10 characters" in str(exc_info.value)

    async def test_validate_template_data_short_prompt(
        self, 
        template_service: TemplateManagementService,
        valid_template_data: TemplateCreate
    ):
        """Test validation with short prompt template."""
        valid_template_data.prompt_template = "Short prompt"  # Too short
        
        with pytest.raises(ValidationError) as exc_info:
            await template_service.validate_template_data(valid_template_data)
        
        assert "at least 20 characters" in str(exc_info.value)

    async def test_validate_template_data_missing_team_structure(
        self, 
        template_service: TemplateManagementService,
        valid_template_data: TemplateCreate
    ):
        """Test validation with missing team structure."""
        valid_template_data.team_structure_template = {}
        
        with pytest.raises(ValidationError) as exc_info:
            await template_service.validate_template_data(valid_template_data)
        
        assert "team_name" in str(exc_info.value)

    async def test_validate_template_data_empty_team_members(
        self, 
        template_service: TemplateManagementService,
        valid_template_data: TemplateCreate
    ):
        """Test validation with empty team members."""
        valid_template_data.team_structure_template = {
            "team_name": "Test Team",
            "team_members": []
        }
        
        with pytest.raises(ValidationError) as exc_info:
            await template_service.validate_template_data(valid_template_data)
        
        assert "at least one team member" in str(exc_info.value)

    async def test_validate_template_data_too_many_tags(
        self, 
        template_service: TemplateManagementService,
        valid_template_data: TemplateCreate
    ):
        """Test validation with too many tags."""
        valid_template_data.tags = [f"tag{i}" for i in range(15)]  # Too many tags
        
        with pytest.raises(ValidationError) as exc_info:
            await template_service.validate_template_data(valid_template_data)
        
        assert "more than 10 tags" in str(exc_info.value)

    async def test_check_template_name_uniqueness_unique(
        self, 
        template_service: TemplateManagementService,
        sample_user: User
    ):
        """Test template name uniqueness check with unique name."""
        is_unique = await template_service.check_template_name_uniqueness(
            "Unique Template Name", 
            sample_user.id
        )
        assert is_unique is True

    async def test_check_template_name_uniqueness_duplicate(
        self, 
        template_service: TemplateManagementService,
        sample_user: User,
        db_session: AsyncSession
    ):
        """Test template name uniqueness check with duplicate name."""
        # Create existing template
        existing_template = Template(
            template_id="existing_123",
            name="Existing Template",
            description="An existing template",
            category=TemplateCategory.BUSINESS,
            difficulty=TemplateDifficulty.BEGINNER,
            visibility=TemplateVisibility.PRIVATE,
            status=TemplateStatus.ACTIVE,
            prompt_template="Existing prompt template",
            team_structure_template={"team_name": "Existing"},
            user_id=sample_user.id,
            author_name=sample_user.name
        )
        db_session.add(existing_template)
        await db_session.commit()
        
        is_unique = await template_service.check_template_name_uniqueness(
            "Existing Template", 
            sample_user.id
        )
        assert is_unique is False

    async def test_create_template_from_agent_success(
        self, 
        template_service: TemplateManagementService,
        sample_agent: Agent,
        sample_user: User
    ):
        """Test successful template creation from agent."""
        template_data = {
            "name": "Template from Agent",
            "description": "Created from test agent",
            "category": TemplateCategory.TECHNICAL,
            "difficulty": TemplateDifficulty.INTERMEDIATE,
            "tags": ["agent", "generated"]
        }
        
        template = await template_service.create_template_from_agent(
            sample_agent.agent_id,
            template_data,
            sample_user
        )
        
        assert template.name == "Template from Agent"
        assert template.source_agent_id == sample_agent.agent_id
        assert template.user_id == sample_user.id
        assert template.metadata["created_from_agent"] is True

    async def test_create_template_from_agent_not_found(
        self, 
        template_service: TemplateManagementService,
        sample_user: User
    ):
        """Test template creation from non-existent agent."""
        template_data = {
            "name": "Template from Agent",
            "description": "Created from test agent",
            "category": TemplateCategory.TECHNICAL,
            "difficulty": TemplateDifficulty.INTERMEDIATE
        }
        
        with pytest.raises(NotFoundError):
            await template_service.create_template_from_agent(
                "nonexistent_agent",
                template_data,
                sample_user
            )

    async def test_transform_template_for_agent_creation(
        self, 
        template_service: TemplateManagementService,
        sample_user: User,
        db_session: AsyncSession
    ):
        """Test transforming template for agent creation."""
        # Create template
        template = Template(
            template_id="template_transform",
            name="Transform Template",
            description="Template for transformation",
            category=TemplateCategory.CREATIVE,
            difficulty=TemplateDifficulty.ADVANCED,
            visibility=TemplateVisibility.PUBLIC,
            status=TemplateStatus.ACTIVE,
            prompt_template="Transform prompt template",
            team_structure_template={
                "team_name": "Transform Team",
                "team_members": [{"name": "Transform Agent", "role": "creator"}]
            },
            user_id=sample_user.id,
            author_name=sample_user.name,
            usage_count=0
        )
        db_session.add(template)
        await db_session.commit()
        await db_session.refresh(template)
        
        agent_config = await template_service.transform_template_for_agent_creation(
            template.template_id,
            sample_user
        )
        
        assert agent_config["team_name"] == "Transform Template"
        assert agent_config["description"] == "Template for transformation"
        assert agent_config["prompt_template"] == "Transform prompt template"
        assert agent_config["metadata"]["created_from_template"] is True
        assert agent_config["metadata"]["template_id"] == template.template_id

    async def test_get_template_recommendations(
        self, 
        template_service: TemplateManagementService,
        sample_user: User,
        db_session: AsyncSession
    ):
        """Test getting template recommendations."""
        # Create some public templates
        for i in range(3):
            template = Template(
                template_id=f"recommend_{i}",
                name=f"Recommendation Template {i}",
                description=f"Template for recommendation {i}",
                category=TemplateCategory.BUSINESS,
                difficulty=TemplateDifficulty.INTERMEDIATE,
                visibility=TemplateVisibility.PUBLIC,
                status=TemplateStatus.ACTIVE,
                prompt_template=f"Recommendation prompt {i}",
                team_structure_template={"team_name": f"Recommend Team {i}"},
                user_id=sample_user.id,
                author_name=sample_user.name,
                usage_count=i * 5,
                rating=4.0 + i * 0.1,
                rating_count=5
            )
            db_session.add(template)
        
        await db_session.commit()
        
        recommendations = await template_service.get_template_recommendations(
            sample_user,
            category=TemplateCategory.BUSINESS,
            limit=2
        )
        
        assert len(recommendations) <= 2
        assert all(rec["category"] == TemplateCategory.BUSINESS for rec in recommendations)

    async def test_validate_template_permissions_read_owner(
        self, 
        template_service: TemplateManagementService,
        sample_user: User,
        db_session: AsyncSession
    ):
        """Test template permission validation for owner read access."""
        template = Template(
            template_id="permission_test",
            name="Permission Template",
            description="Template for permission testing",
            category=TemplateCategory.TECHNICAL,
            difficulty=TemplateDifficulty.INTERMEDIATE,
            visibility=TemplateVisibility.PRIVATE,
            status=TemplateStatus.ACTIVE,
            prompt_template="Permission prompt template",
            team_structure_template={"team_name": "Permission Team"},
            user_id=sample_user.id,
            author_name=sample_user.name
        )
        db_session.add(template)
        await db_session.commit()
        
        can_access, template_data = await template_service.validate_template_permissions(
            template.template_id,
            sample_user,
            "read"
        )
        
        assert can_access is True
        assert template_data is not None
        assert template_data["template_id"] == template.template_id

    async def test_validate_template_permissions_write_owner(
        self, 
        template_service: TemplateManagementService,
        sample_user: User,
        db_session: AsyncSession
    ):
        """Test template permission validation for owner write access."""
        template = Template(
            template_id="write_permission_test",
            name="Write Permission Template",
            description="Template for write permission testing",
            category=TemplateCategory.TECHNICAL,
            difficulty=TemplateDifficulty.INTERMEDIATE,
            visibility=TemplateVisibility.PRIVATE,
            status=TemplateStatus.ACTIVE,
            prompt_template="Write permission prompt template",
            team_structure_template={"team_name": "Write Permission Team"},
            user_id=sample_user.id,
            author_name=sample_user.name
        )
        db_session.add(template)
        await db_session.commit()
        
        can_access, template_data = await template_service.validate_template_permissions(
            template.template_id,
            sample_user,
            "write"
        )
        
        assert can_access is True
        assert template_data is not None

    async def test_validate_template_permissions_nonexistent(
        self, 
        template_service: TemplateManagementService,
        sample_user: User
    ):
        """Test template permission validation for non-existent template."""
        can_access, template_data = await template_service.validate_template_permissions(
            "nonexistent_template",
            sample_user,
            "read"
        )
        
        assert can_access is False
        assert template_data is None
