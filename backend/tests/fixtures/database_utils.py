"""
Database utilities for testing.
"""

import asyncio
from typing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from sqlalchemy.ext.asyncio import <PERSON><PERSON><PERSON><PERSON><PERSON>, AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlmodel import SQLModel


class DatabaseTestManager:
    """Manages test database lifecycle."""

    def __init__(self, database_url: str = "sqlite+aiosqlite:///:memory:"):
        self.database_url = database_url
        self.engine: AsyncEngine = None
        self.session_factory = None
    
    async def setup(self) -> AsyncEngine:
        """Set up test database."""
        if self.engine is None:
            self.engine = create_async_engine(
                self.database_url,
                echo=False,
                future=True,
                # Important: Use a separate metadata instance for tests
                connect_args={"check_same_thread": False} if "sqlite" in self.database_url else {}
            )
            
            # Create session factory
            self.session_factory = sessionmaker(
                self.engine, class_=AsyncSession, expire_on_commit=False
            )
            
            # Create all tables with proper error handling
            await self._create_tables()
        
        return self.engine
    
    async def _create_tables(self):
        """Create database tables safely."""
        try:
            async with self.engine.begin() as conn:
                # Drop all tables first to ensure clean state
                await conn.run_sync(SQLModel.metadata.drop_all)
                # Then create all tables
                await conn.run_sync(SQLModel.metadata.create_all)
        except Exception as e:
            print(f"Warning: Error creating tables: {e}")
            # Try alternative approach
            await self._create_tables_alternative()
    
    async def _create_tables_alternative(self):
        """Alternative table creation method."""
        try:
            async with self.engine.begin() as conn:
                # Create tables one by one with error handling
                for table in SQLModel.metadata.sorted_tables:
                    try:
                        await conn.run_sync(table.create, checkfirst=True)
                    except Exception as table_error:
                        print(f"Warning: Could not create table {table.name}: {table_error}")
        except Exception as e:
            print(f"Warning: Alternative table creation failed: {e}")
    
    def get_session(self):
        """Get a test database session context manager."""
        return self._session_context()

    async def _session_context(self):
        """Internal session context manager."""
        if self.session_factory is None:
            await self.setup()

        async with self.session_factory() as session:
            # Use savepoint for better isolation
            async with session.begin():
                yield session
                # Session will be rolled back automatically
    
    async def cleanup(self):
        """Clean up test database."""
        if self.engine:
            try:
                async with self.engine.begin() as conn:
                    await conn.run_sync(SQLModel.metadata.drop_all)
            except Exception as e:
                print(f"Warning: Error during cleanup: {e}")
            finally:
                await self.engine.dispose()
                self.engine = None
                self.session_factory = None
    
    async def reset_database(self):
        """Reset database to clean state."""
        if self.engine:
            try:
                async with self.engine.begin() as conn:
                    # Clear all data but keep structure
                    for table in reversed(SQLModel.metadata.sorted_tables):
                        await conn.execute(table.delete())
                    await conn.commit()
            except Exception as e:
                print(f"Warning: Error resetting database: {e}")
                # Fallback: recreate tables
                await self._create_tables()


# Global test database manager
_test_db_manager = None


async def get_test_db_manager() -> DatabaseTestManager:
    """Get or create test database manager."""
    global _test_db_manager
    if _test_db_manager is None:
        _test_db_manager = DatabaseTestManager()
        await _test_db_manager.setup()
    return _test_db_manager


async def cleanup_test_db():
    """Clean up test database."""
    global _test_db_manager
    if _test_db_manager:
        await _test_db_manager.cleanup()
        _test_db_manager = None


def pytest_configure():
    """Configure pytest for database testing."""
    # Ensure clean state at start
    asyncio.run(cleanup_test_db())


def pytest_unconfigure():
    """Clean up after pytest."""
    # Ensure clean state at end
    asyncio.run(cleanup_test_db())


class IsolatedTestSession:
    """Context manager for isolated test sessions."""
    
    def __init__(self, db_manager: DatabaseTestManager):
        self.db_manager = db_manager
        self.session = None
        self.transaction = None
    
    async def __aenter__(self) -> AsyncSession:
        """Enter the context."""
        session_gen = self.db_manager.get_session()
        self.session = await session_gen.__anext__()
        return self.session
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Exit the context."""
        if self.session:
            try:
                await self.session.rollback()
            except Exception:
                pass
            finally:
                await self.session.close()


async def create_isolated_session() -> AsyncSession:
    """Create an isolated test session."""
    db_manager = await get_test_db_manager()
    return IsolatedTestSession(db_manager)
