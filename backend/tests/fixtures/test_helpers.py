"""
Test helper utilities and functions.
"""

import asyncio
import json
import tempfile
from pathlib import Path
from typing import Any, Dict, List, Optional
from unittest.mock import AsyncMock, Mock

import pytest


class AsyncContextManager:
    """Helper for creating async context managers in tests."""
    
    def __init__(self, return_value=None):
        self.return_value = return_value
    
    async def __aenter__(self):
        return self.return_value
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass


class MockAIProvider:
    """Mock AI provider for testing."""
    
    def __init__(self, responses: Optional[List[str]] = None):
        self.responses = responses or ["Mock AI response"]
        self.call_count = 0
        self.last_prompt = None
        self.last_system_prompt = None
    
    async def generate_text(
        self, 
        prompt: str, 
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> str:
        """Generate mock text response."""
        self.call_count += 1
        self.last_prompt = prompt
        self.last_system_prompt = system_prompt
        
        response_index = min(self.call_count - 1, len(self.responses) - 1)
        return self.responses[response_index]
    
    async def generate_structured_output(
        self,
        prompt: str,
        schema: Dict[str, Any],
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate mock structured output."""
        self.call_count += 1
        self.last_prompt = prompt
        self.last_system_prompt = system_prompt
        
        # Return a mock structure based on schema
        if "team_name" in str(schema):
            return {
                "team_name": "Mock Team",
                "description": "Mock team description",
                "team_members": [
                    {
                        "name": "Mock Member 1",
                        "role": "mock_role_1",
                        "description": "Mock member description"
                    }
                ],
                "workflow": {
                    "steps": [
                        {
                            "name": "Mock Step",
                            "description": "Mock step description",
                            "assignee": "Mock Member 1"
                        }
                    ]
                }
            }
        
        return {"mock": "response"}
    
    async def health_check(self) -> bool:
        """Mock health check."""
        return True


class TestFileManager:
    """Helper for managing test files."""
    
    def __init__(self):
        self.temp_dir = None
        self.created_files = []
    
    def __enter__(self):
        self.temp_dir = tempfile.mkdtemp()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        # Clean up created files
        for file_path in self.created_files:
            try:
                Path(file_path).unlink(missing_ok=True)
            except Exception:
                pass
        
        # Clean up temp directory
        if self.temp_dir:
            import shutil
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_file(self, filename: str, content: str = "") -> str:
        """Create a temporary file with content."""
        file_path = Path(self.temp_dir) / filename
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        self.created_files.append(str(file_path))
        return str(file_path)
    
    def create_json_file(self, filename: str, data: Dict[str, Any]) -> str:
        """Create a JSON file with data."""
        content = json.dumps(data, indent=2, ensure_ascii=False)
        return self.create_file(filename, content)


def assert_valid_uuid(uuid_string: str) -> None:
    """Assert that a string is a valid UUID."""
    import uuid
    try:
        uuid.UUID(uuid_string)
    except ValueError:
        raise AssertionError(f"'{uuid_string}' is not a valid UUID")


def assert_valid_timestamp(timestamp: Any) -> None:
    """Assert that a value is a valid timestamp."""
    from datetime import datetime
    
    if isinstance(timestamp, str):
        try:
            datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        except ValueError:
            pytest.fail(f"'{timestamp}' is not a valid ISO timestamp")
    elif isinstance(timestamp, (int, float)):
        if timestamp <= 0:
            pytest.fail(f"'{timestamp}' is not a valid Unix timestamp")
    elif isinstance(timestamp, datetime):
        # Already a datetime object, should be valid
        pass
    else:
        pytest.fail(f"'{timestamp}' is not a valid timestamp type")


def assert_valid_json(json_string: str) -> Dict[str, Any]:
    """Assert that a string is valid JSON and return parsed data."""
    try:
        return json.loads(json_string)
    except json.JSONDecodeError as e:
        pytest.fail(f"Invalid JSON: {e}")


def create_mock_response(status_code: int = 200, json_data: Optional[Dict] = None):
    """Create a mock HTTP response."""
    mock_response = Mock()
    mock_response.status_code = status_code
    mock_response.json.return_value = json_data or {}
    mock_response.text = json.dumps(json_data or {})
    mock_response.headers = {"content-type": "application/json"}
    return mock_response


async def wait_for_condition(
    condition_func, 
    timeout: float = 5.0, 
    interval: float = 0.1
) -> bool:
    """Wait for a condition to become true."""
    start_time = asyncio.get_event_loop().time()
    
    while True:
        if await condition_func() if asyncio.iscoroutinefunction(condition_func) else condition_func():
            return True
        
        if asyncio.get_event_loop().time() - start_time > timeout:
            return False
        
        await asyncio.sleep(interval)


class DatabaseTestHelper:
    """Helper for database testing."""
    
    @staticmethod
    async def count_records(session, model_class) -> int:
        """Count records in a table."""
        from sqlalchemy import text
        table_name = model_class.__tablename__
        result = await session.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
        return result.scalar()
    
    @staticmethod
    async def clear_table(session, model_class) -> None:
        """Clear all records from a table."""
        from sqlalchemy import text
        table_name = model_class.__tablename__
        await session.execute(text(f"DELETE FROM {table_name}"))
        await session.commit()
    
    @staticmethod
    async def record_exists(session, model_class, **filters) -> bool:
        """Check if a record exists with given filters."""
        from sqlalchemy import select
        
        query = select(model_class)
        for key, value in filters.items():
            query = query.where(getattr(model_class, key) == value)
        
        result = await session.execute(query)
        return result.first() is not None


class APITestHelper:
    """Helper for API testing."""
    
    @staticmethod
    def assert_error_response(response, expected_status: int, expected_message: Optional[str] = None):
        """Assert that response is an error with expected status and message."""
        assert response.status_code == expected_status
        
        if expected_message:
            data = response.json()
            assert "detail" in data
            assert expected_message in str(data["detail"])
    
    @staticmethod
    def assert_success_response(response, expected_keys: Optional[List[str]] = None):
        """Assert that response is successful with expected keys."""
        assert 200 <= response.status_code < 300
        
        if expected_keys:
            data = response.json()
            for key in expected_keys:
                assert key in data
    
    @staticmethod
    def extract_pagination_info(response) -> Dict[str, Any]:
        """Extract pagination information from response."""
        data = response.json()
        return {
            "total": data.get("total", 0),
            "page": data.get("page", 1),
            "size": data.get("size", 10),
            "pages": data.get("pages", 1)
        }


def skip_if_no_api_key(provider: str):
    """Skip test if API key is not available."""
    import os
    
    key_mapping = {
        "openai": "OPENAI_API_KEY",
        "anthropic": "ANTHROPIC_API_KEY",
        "google": "GOOGLE_API_KEY"
    }
    
    env_var = key_mapping.get(provider.lower())
    if not env_var:
        return pytest.skip(f"Unknown provider: {provider}")
    
    if not os.getenv(env_var) or os.getenv(env_var) in ["your-api-key", "test-api-key"]:
        return pytest.skip(f"No valid {provider} API key available")


def parametrize_providers():
    """Parametrize test with available AI providers."""
    providers = []
    
    import os
    if os.getenv("OPENAI_API_KEY") and os.getenv("OPENAI_API_KEY") not in ["your-openai-api-key", "test-openai-key"]:
        providers.append("openai")
    
    if os.getenv("ANTHROPIC_API_KEY") and os.getenv("ANTHROPIC_API_KEY") not in ["your-anthropic-api-key", "test-anthropic-key"]:
        providers.append("anthropic")
    
    if not providers:
        providers = ["mock"]  # Use mock provider if no real ones available
    
    return pytest.mark.parametrize("provider", providers)
