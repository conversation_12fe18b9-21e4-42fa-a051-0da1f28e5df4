"""
Test data factories for creating test objects.
"""

import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any

from app.models.agent import Agent, AgentStatus, AgentType
from app.models.planning import PlanningRequest, PlanningStatus
from app.models.user import (
    User, UserSession, UserToken, LoginHistory,
    UserRole, UserStatus, SessionStatus, TokenType
)
from app.core.security import get_password_hash


class AgentFactory:
    """Factory for creating Agent test objects."""
    
    @staticmethod
    def create_agent_data(
        agent_id: Optional[str] = None,
        team_name: Optional[str] = None,
        description: Optional[str] = None,
        agent_type: AgentType = AgentType.TEAM,
        status: AgentStatus = AgentStatus.ACTIVE,
        **kwargs
    ) -> Dict[str, Any]:
        """Create agent data dictionary."""
        return {
            "agent_id": agent_id or f"agent_{uuid.uuid4().hex[:12]}",
            "team_name": team_name or "测试团队",
            "description": description or "这是一个测试团队",
            "agent_type": agent_type,
            "status": status,
            "prompt_template": kwargs.get("prompt_template"),
            "system_prompt": kwargs.get("system_prompt"),
            "usage_count": kwargs.get("usage_count", 0),
            "last_used": kwargs.get("last_used"),
            "avg_response_time": kwargs.get("avg_response_time"),
            "success_rate": kwargs.get("success_rate"),
        }
    
    @staticmethod
    def create_agent(
        agent_id: Optional[str] = None,
        team_name: Optional[str] = None,
        description: Optional[str] = None,
        agent_type: AgentType = AgentType.TEAM,
        status: AgentStatus = AgentStatus.ACTIVE,
        **kwargs
    ) -> Agent:
        """Create Agent model instance."""
        data = AgentFactory.create_agent_data(
            agent_id=agent_id,
            team_name=team_name,
            description=description,
            agent_type=agent_type,
            status=status,
            **kwargs
        )
        return Agent(**data)


class PlanningRequestFactory:
    """Factory for creating PlanningRequest test objects."""
    
    @staticmethod
    def create_planning_request_data(
        request_id: Optional[str] = None,
        user_description: Optional[str] = None,
        model: str = "gpt-4",
        temperature: float = 0.7,
        status: PlanningStatus = PlanningStatus.PENDING,
        **kwargs
    ) -> Dict[str, Any]:
        """Create planning request data dictionary."""
        return {
            "request_id": request_id or f"req_{uuid.uuid4().hex[:12]}",
            "user_description": user_description or "我需要一个数据分析团队",
            "model": model,
            "temperature": temperature,
            "status": status,
            "team_plan_json": kwargs.get("team_plan_json"),
            "generated_code": kwargs.get("generated_code"),
            "started_at": kwargs.get("started_at"),
            "completed_at": kwargs.get("completed_at"),
            "duration_seconds": kwargs.get("duration_seconds"),
            "error_message": kwargs.get("error_message"),
        }
    
    @staticmethod
    def create_planning_request(
        request_id: Optional[str] = None,
        user_description: Optional[str] = None,
        model: str = "gpt-4",
        temperature: float = 0.7,
        status: PlanningStatus = PlanningStatus.PENDING,
        **kwargs
    ) -> PlanningRequest:
        """Create PlanningRequest model instance."""
        data = PlanningRequestFactory.create_planning_request_data(
            request_id=request_id,
            user_description=user_description,
            model=model,
            temperature=temperature,
            status=status,
            **kwargs
        )
        return PlanningRequest(**data)


class TeamPlanFactory:
    """Factory for creating team plan test data."""
    
    @staticmethod
    def create_team_plan(
        team_name: Optional[str] = None,
        description: Optional[str] = None,
        objective: Optional[str] = None,
        member_count: int = 2,
        **kwargs
    ) -> Dict[str, Any]:
        """Create team plan dictionary."""
        team_name = team_name or "测试分析团队"
        description = description or "专业的数据分析团队"
        objective = objective or "提供准确的数据分析服务"
        
        # Create team members
        members = []
        for i in range(member_count):
            member = {
                "name": f"成员{i+1}",
                "role": f"role_{i+1}",
                "description": f"负责任务{i+1}的专家",
                "expertise": [f"技能{i+1}", f"技能{i+2}"],
                "responsibilities": [f"职责{i+1}", f"职责{i+2}"]
            }
            members.append(member)
        
        # Create workflow
        workflow_steps = []
        for i in range(min(3, member_count)):
            step = {
                "name": f"步骤{i+1}",
                "description": f"执行步骤{i+1}",
                "assignee": f"成员{i+1}",
                "inputs": [f"输入{i+1}"],
                "outputs": [f"输出{i+1}"]
            }
            workflow_steps.append(step)
        
        return {
            "team_name": team_name,
            "description": description,
            "objective": objective,
            "team_members": members,
            "workflow": {
                "steps": workflow_steps
            },
            "success_metrics": [
                "准确率 > 90%",
                "响应时间 < 30秒",
                "用户满意度 > 4.0/5"
            ],
            "plan_id": str(uuid.uuid4()),
            "created_at": datetime.utcnow().isoformat(),
            "validation_score": kwargs.get("validation_score", 85.0)
        }


class MockResponseFactory:
    """Factory for creating mock API responses."""
    
    @staticmethod
    def create_openai_response(
        content: str = "Test response",
        model: str = "gpt-4",
        **kwargs
    ) -> Dict[str, Any]:
        """Create mock OpenAI API response."""
        return {
            "id": f"chatcmpl-{uuid.uuid4().hex[:12]}",
            "object": "chat.completion",
            "created": int(datetime.utcnow().timestamp()),
            "model": model,
            "choices": [
                {
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": content
                    },
                    "finish_reason": "stop"
                }
            ],
            "usage": {
                "prompt_tokens": kwargs.get("prompt_tokens", 100),
                "completion_tokens": kwargs.get("completion_tokens", 50),
                "total_tokens": kwargs.get("total_tokens", 150)
            }
        }
    
    @staticmethod
    def create_anthropic_response(
        content: str = "Test response",
        model: str = "claude-3-sonnet-20240229",
        **kwargs
    ) -> Dict[str, Any]:
        """Create mock Anthropic API response."""
        return {
            "id": f"msg_{uuid.uuid4().hex[:12]}",
            "type": "message",
            "role": "assistant",
            "content": [
                {
                    "type": "text",
                    "text": content
                }
            ],
            "model": model,
            "stop_reason": "end_turn",
            "stop_sequence": None,
            "usage": {
                "input_tokens": kwargs.get("input_tokens", 100),
                "output_tokens": kwargs.get("output_tokens", 50)
            }
        }


class FileFactory:
    """Factory for creating test files and directories."""
    
    @staticmethod
    def create_test_file_content(
        file_type: str = "python",
        content: Optional[str] = None
    ) -> str:
        """Create test file content."""
        if content:
            return content
        
        if file_type == "python":
            return '''"""
Test generated agent file.
"""

class TestAgent:
    def __init__(self):
        self.name = "Test Agent"
    
    async def execute(self, input_data):
        return {"result": "test output"}
'''
        elif file_type == "json":
            return '''
{
    "name": "Test Config",
    "version": "1.0.0",
    "settings": {
        "enabled": true,
        "timeout": 30
    }
}
'''
        else:
            return "Test file content"
    
    @staticmethod
    def create_agent_config(
        agent_id: Optional[str] = None,
        team_name: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Create agent configuration dictionary."""
        return {
            "agent_id": agent_id or f"agent_{uuid.uuid4().hex[:12]}",
            "team_name": team_name or "测试团队",
            "version": "1.0.0",
            "created_at": datetime.utcnow().isoformat(),
            "config": {
                "model": kwargs.get("model", "gpt-4"),
                "temperature": kwargs.get("temperature", 0.7),
                "max_tokens": kwargs.get("max_tokens", 2000)
            },
            "metadata": kwargs.get("metadata", {})
        }


class UserFactory:
    """Factory for creating User test objects."""

    @staticmethod
    def create_user_data(
        name: Optional[str] = None,
        email: Optional[str] = None,
        password: str = "testpassword123",
        role: UserRole = UserRole.USER,
        status: UserStatus = UserStatus.ACTIVE,
        **kwargs
    ) -> Dict[str, Any]:
        """Create user data dictionary."""
        return {
            "name": name or "Test User",
            "email": email or f"test{uuid.uuid4().hex[:8]}@example.com",
            "password_hash": get_password_hash(password),
            "role": role,
            "status": status,
            "avatar": kwargs.get("avatar"),
            "bio": kwargs.get("bio"),
            "timezone": kwargs.get("timezone", "UTC"),
            "language": kwargs.get("language", "en"),
            "is_email_verified": kwargs.get("is_email_verified", True),
            "login_count": kwargs.get("login_count", 0),
            "failed_login_attempts": kwargs.get("failed_login_attempts", 0),
            "preferences": kwargs.get("preferences", {}),
            "user_metadata": kwargs.get("user_metadata", {}),
        }

    @staticmethod
    def create_user(
        name: Optional[str] = None,
        email: Optional[str] = None,
        password: str = "testpassword123",
        role: UserRole = UserRole.USER,
        status: UserStatus = UserStatus.ACTIVE,
        **kwargs
    ) -> User:
        """Create User model instance."""
        data = UserFactory.create_user_data(
            name=name,
            email=email,
            password=password,
            role=role,
            status=status,
            **kwargs
        )
        return User(**data)

    @staticmethod
    def create_admin_user(**kwargs) -> User:
        """Create admin user."""
        return UserFactory.create_user(
            name="Admin User",
            email="<EMAIL>",
            role=UserRole.ADMIN,
            **kwargs
        )

    @staticmethod
    def create_pending_user(**kwargs) -> User:
        """Create pending verification user."""
        return UserFactory.create_user(
            status=UserStatus.PENDING_VERIFICATION,
            is_email_verified=False,
            **kwargs
        )


class UserSessionFactory:
    """Factory for creating UserSession test objects."""

    @staticmethod
    def create_session_data(
        user_id: int,
        session_token: Optional[str] = None,
        status: SessionStatus = SessionStatus.ACTIVE,
        **kwargs
    ) -> Dict[str, Any]:
        """Create session data dictionary."""
        from datetime import datetime, timedelta

        return {
            "user_id": user_id,
            "session_token": session_token or f"session_{uuid.uuid4().hex}",
            "status": status,
            "ip_address": kwargs.get("ip_address", "127.0.0.1"),
            "user_agent": kwargs.get("user_agent", "Test User Agent"),
            "device_info": kwargs.get("device_info", "Test Device"),
            "location": kwargs.get("location", "Test Location"),
            "expires_at": kwargs.get("expires_at", datetime.now() + timedelta(days=1)),
            "is_secure": kwargs.get("is_secure", True),
            "is_mobile": kwargs.get("is_mobile", False),
            "session_metadata": kwargs.get("session_metadata", {}),
        }

    @staticmethod
    def create_session(
        user_id: int,
        session_token: Optional[str] = None,
        status: SessionStatus = SessionStatus.ACTIVE,
        **kwargs
    ) -> UserSession:
        """Create UserSession model instance."""
        data = UserSessionFactory.create_session_data(
            user_id=user_id,
            session_token=session_token,
            status=status,
            **kwargs
        )
        return UserSession(**data)


class UserTokenFactory:
    """Factory for creating UserToken test objects."""

    @staticmethod
    def create_token_data(
        user_id: int,
        token_type: TokenType,
        token: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Create token data dictionary."""
        from datetime import datetime, timedelta

        return {
            "user_id": user_id,
            "token": token or f"token_{uuid.uuid4().hex}",
            "token_type": token_type,
            "data": kwargs.get("data", {}),
            "expires_at": kwargs.get("expires_at", datetime.now() + timedelta(hours=1)),
            "is_used": kwargs.get("is_used", False),
            "is_revoked": kwargs.get("is_revoked", False),
        }

    @staticmethod
    def create_token(
        user_id: int,
        token_type: TokenType,
        token: Optional[str] = None,
        **kwargs
    ) -> UserToken:
        """Create UserToken model instance."""
        data = UserTokenFactory.create_token_data(
            user_id=user_id,
            token_type=token_type,
            token=token,
            **kwargs
        )
        return UserToken(**data)

    @staticmethod
    def create_password_reset_token(user_id: int, **kwargs) -> UserToken:
        """Create password reset token."""
        return UserTokenFactory.create_token(
            user_id=user_id,
            token_type=TokenType.PASSWORD_RESET,
            **kwargs
        )

    @staticmethod
    def create_email_verification_token(user_id: int, **kwargs) -> UserToken:
        """Create email verification token."""
        return UserTokenFactory.create_token(
            user_id=user_id,
            token_type=TokenType.EMAIL_VERIFICATION,
            **kwargs
        )


class LoginHistoryFactory:
    """Factory for creating LoginHistory test objects."""

    @staticmethod
    def create_login_history_data(
        user_id: int,
        success: bool = True,
        **kwargs
    ) -> Dict[str, Any]:
        """Create login history data dictionary."""
        return {
            "user_id": user_id,
            "ip_address": kwargs.get("ip_address", "127.0.0.1"),
            "user_agent": kwargs.get("user_agent", "Test User Agent"),
            "device_info": kwargs.get("device_info", "Test Device"),
            "location": kwargs.get("location", "Test Location"),
            "success": success,
            "failure_reason": kwargs.get("failure_reason"),
            "is_suspicious": kwargs.get("is_suspicious", False),
            "risk_score": kwargs.get("risk_score"),
            "login_metadata": kwargs.get("login_metadata", {}),
        }

    @staticmethod
    def create_login_history(
        user_id: int,
        success: bool = True,
        **kwargs
    ) -> LoginHistory:
        """Create LoginHistory model instance."""
        data = LoginHistoryFactory.create_login_history_data(
            user_id=user_id,
            success=success,
            **kwargs
        )
        return LoginHistory(**data)

    @staticmethod
    def create_failed_login(user_id: int, reason: str = "Invalid password", **kwargs) -> LoginHistory:
        """Create failed login history."""
        return LoginHistoryFactory.create_login_history(
            user_id=user_id,
            success=False,
            failure_reason=reason,
            **kwargs
        )

    @staticmethod
    def create_suspicious_login(user_id: int, **kwargs) -> LoginHistory:
        """Create suspicious login history."""
        return LoginHistoryFactory.create_login_history(
            user_id=user_id,
            success=True,
            is_suspicious=True,
            risk_score=0.8,
            **kwargs
        )


class AuthTestDataFactory:
    """Factory for creating authentication test data."""

    @staticmethod
    def create_registration_data(
        name: Optional[str] = None,
        email: Optional[str] = None,
        password: str = "testpassword123",
        **kwargs
    ) -> Dict[str, Any]:
        """Create user registration data."""
        return {
            "name": name or "Test User",
            "email": email or f"test{uuid.uuid4().hex[:8]}@example.com",
            "password": password,
            "confirm_password": kwargs.get("confirm_password", password),
            "timezone": kwargs.get("timezone", "UTC"),
            "language": kwargs.get("language", "en"),
        }

    @staticmethod
    def create_login_data(
        email: str,
        password: str = "testpassword123",
        remember_me: bool = False
    ) -> Dict[str, Any]:
        """Create login data."""
        return {
            "email": email,
            "password": password,
            "remember_me": remember_me,
        }

    @staticmethod
    def create_change_password_data(
        current_password: str = "testpassword123",
        new_password: str = "newpassword123",
        **kwargs
    ) -> Dict[str, Any]:
        """Create change password data."""
        return {
            "current_password": current_password,
            "new_password": new_password,
            "confirm_password": kwargs.get("confirm_password", new_password),
        }

    @staticmethod
    def create_reset_password_data(email: str) -> Dict[str, Any]:
        """Create reset password data."""
        return {"email": email}

    @staticmethod
    def create_reset_password_confirm_data(
        token: str,
        new_password: str = "newpassword123",
        **kwargs
    ) -> Dict[str, Any]:
        """Create reset password confirmation data."""
        return {
            "token": token,
            "new_password": new_password,
            "confirm_password": kwargs.get("confirm_password", new_password),
        }

    @staticmethod
    def create_user_update_data(**kwargs) -> Dict[str, Any]:
        """Create user update data."""
        return {
            "name": kwargs.get("name"),
            "bio": kwargs.get("bio"),
            "timezone": kwargs.get("timezone"),
            "language": kwargs.get("language"),
            "preferences": kwargs.get("preferences"),
        }
