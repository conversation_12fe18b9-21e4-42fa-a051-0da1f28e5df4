# Meta-Agent Backend Test Suite

This directory contains a comprehensive test suite for the Meta-Agent backend application, designed to achieve 80%+ code coverage and ensure robust functionality.

## Test Structure

```
tests/
├── conftest.py                 # Pytest configuration and shared fixtures
├── test_basic.py              # Basic infrastructure tests
├── fixtures/
│   ├── factories.py           # Test data factories
│   └── test_helpers.py        # Test utility functions
├── unit/                      # Unit tests
│   ├── models/               # Model tests
│   │   ├── test_agent.py     # Agent model tests
│   │   └── test_planning.py  # Planning model tests
│   ├── services/             # Service tests
│   │   ├── test_ai_planner.py
│   │   ├── test_planning_service.py
│   │   └── test_ai_providers.py
│   └── core/                 # Core functionality tests
│       ├── test_config.py    # Configuration tests
│       └── test_database.py  # Database tests
├── integration/              # Integration tests
│   ├── api/                 # API endpoint tests
│   │   ├── test_health_endpoints.py
│   │   └── test_planning_endpoints.py
│   ├── database/            # Database integration tests
│   │   └── test_database_operations.py
│   └── services/            # Service integration tests
└── performance/             # Performance tests
    └── test_load_testing.py
```

## Test Categories

### Unit Tests (`tests/unit/`)
- **Models**: Test SQLModel classes, validation, serialization
- **Services**: Test business logic in isolation with mocked dependencies
- **Core**: Test configuration, database setup, utilities

### Integration Tests (`tests/integration/`)
- **API**: Test full request/response cycles for all endpoints
- **Database**: Test actual database operations and transactions
- **Services**: Test service interactions with real dependencies



## Test Features

### Comprehensive Fixtures
- **Database**: Isolated test database with automatic cleanup
- **HTTP Clients**: Both sync and async test clients
- **Mock Services**: AI provider mocks for testing without API calls
- **Test Data**: Factories for generating consistent test data

### Test Helpers
- **MockAIProvider**: Simulates AI service responses
- **TestFileManager**: Manages temporary files for tests
- **DatabaseTestHelper**: Database operation utilities
- **APITestHelper**: API response validation utilities

### Coverage Requirements
- Target: 80%+ code coverage
- Branch coverage enabled
- HTML and XML reports generated
- Coverage fails below threshold

## Running Tests

### Quick Commands
```bash
# Run all tests
make test

# Run specific test types
make test-unit
make test-integration
make test-performance

# Run with coverage
make test-coverage

# Run fast tests (exclude slow ones)
make test-fast
```

### Detailed Commands
```bash
# Run all tests with verbose output
python -m pytest tests/ -v

# Run specific test file
python -m pytest tests/unit/models/test_agent.py -v

# Run tests with coverage
python -m pytest tests/ --cov=app --cov-report=html

# Run tests by marker
python -m pytest tests/ -m "unit"
python -m pytest tests/ -m "integration"
python -m pytest tests/ -m "slow"

# Run tests in parallel
python -m pytest tests/ -n auto
```

### Test Script
```bash
# Use the test runner script
python scripts/run_tests.py --type unit --coverage --verbose
python scripts/run_tests.py --type integration --html-report
python scripts/run_tests.py --type performance
```

## Test Configuration

### Pytest Configuration (`pytest.ini`)
- Coverage settings and thresholds
- Test markers and filtering
- Async test configuration
- Warning filters

### Coverage Configuration (`.coveragerc`)
- Source code inclusion/exclusion
- Branch coverage settings
- Report formatting
- HTML/XML output configuration

## Test Data Management

### Factories (`tests/fixtures/factories.py`)
- **AgentFactory**: Creates Agent model instances
- **PlanningRequestFactory**: Creates PlanningRequest instances
- **TeamPlanFactory**: Creates team plan data structures
- **MockResponseFactory**: Creates mock API responses

### Test Helpers (`tests/fixtures/test_helpers.py`)
- **MockAIProvider**: AI service simulation
- **TestFileManager**: File system utilities
- **DatabaseTestHelper**: Database testing utilities
- **APITestHelper**: API testing utilities

## Test Markers

- `@pytest.mark.unit`: Unit tests
- `@pytest.mark.integration`: Integration tests
- `@pytest.mark.slow`: Slow tests (may take several seconds)
- `@pytest.mark.performance`: Performance benchmarks
- `@pytest.mark.external`: Tests requiring external services

## Current Test Coverage

### Implemented Tests
✅ **Basic Infrastructure**: Test setup and configuration
✅ **Model Tests**: Agent and Planning model validation
✅ **Service Tests**: AI providers and planning services
✅ **Core Tests**: Configuration and database setup
✅ **API Tests**: Health and planning endpoints
✅ **Performance Tests**: Load testing and benchmarks

### Test Statistics
- **Total Test Files**: 12+
- **Test Functions**: 100+
- **Test Coverage**: Targeting 80%+
- **Test Types**: Unit, Integration, Performance

## Known Issues

### Database Tests
- SQLModel table definition conflicts in some integration tests
- Workaround: Use `extend_existing=True` in table definitions
- Alternative: Run unit tests separately from integration tests

### AI Provider Tests
- Some tests require actual API keys for full integration testing
- Mock providers used for most tests to avoid external dependencies
- Real provider tests can be enabled with valid API keys

## Best Practices

### Writing Tests
1. Use descriptive test names that explain what is being tested
2. Follow AAA pattern: Arrange, Act, Assert
3. Use factories for test data generation
4. Mock external dependencies in unit tests
5. Test both success and failure scenarios

### Test Organization
1. Mirror source code structure in test directories
2. Group related tests in classes
3. Use appropriate test markers
4. Keep tests isolated and independent

### Performance Considerations
1. Use async tests for async code
2. Minimize database operations in unit tests
3. Use fixtures for expensive setup operations
4. Run slow tests separately when needed

## Continuous Integration

The test suite is designed to integrate with CI/CD pipelines:

```yaml
# Example CI configuration
- name: Run Tests
  run: |
    python -m pytest tests/ --cov=app --cov-report=xml --cov-fail-under=80
    
- name: Upload Coverage
  uses: codecov/codecov-action@v1
  with:
    file: ./coverage.xml
```

## Contributing

When adding new features:
1. Write tests first (TDD approach)
2. Ensure tests cover both success and error cases
3. Update test documentation
4. Maintain coverage above 80%
5. Run full test suite before submitting changes

## Troubleshooting

### Common Issues
1. **Import Errors**: Check PYTHONPATH and virtual environment
2. **Database Errors**: Ensure test database is properly isolated
3. **Async Errors**: Use proper async fixtures and test markers
4. **Coverage Issues**: Check file inclusion/exclusion patterns

### Debug Commands
```bash
# Run tests with debug output
python -m pytest tests/ -v -s --tb=long

# Run specific test with debugging
python -m pytest tests/unit/models/test_agent.py::TestAgentModel::test_agent_creation -v -s

# Check test collection
python -m pytest tests/ --collect-only
```
