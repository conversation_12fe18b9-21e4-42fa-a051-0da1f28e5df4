"""
Test user isolation functionality for agents.
"""

import pytest
from httpx import As<PERSON><PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.security import create_access_token
from app.models.agent import Agent, AgentStatus
from app.models.user import User, UserRole, UserStatus


@pytest.fixture
async def test_users(db_session: AsyncSession):
    """Create test users for isolation testing."""
    # Create user 1
    user1 = User(
        uuid="test-user-1",
        name="Test User 1",
        email="<EMAIL>",
        password_hash="dummy-hash",
        role=UserRole.USER,
        status=UserStatus.ACTIVE,
        is_email_verified=True,
        login_count=0,
        failed_login_attempts=0
    )
    db_session.add(user1)
    
    # Create user 2
    user2 = User(
        uuid="test-user-2",
        name="Test User 2",
        email="<EMAIL>",
        password_hash="dummy-hash",
        role=UserRole.USER,
        status=UserStatus.ACTIVE,
        is_email_verified=True,
        login_count=0,
        failed_login_attempts=0
    )
    db_session.add(user2)
    
    await db_session.commit()
    await db_session.refresh(user1)
    await db_session.refresh(user2)
    
    return user1, user2


@pytest.fixture
async def test_agents(db_session: AsyncSession, test_users):
    """Create test agents for isolation testing."""
    user1, user2 = test_users
    
    # Create agent for user 1
    agent1 = Agent(
        uuid="test-agent-1",
        agent_id="agent_test_1",
        team_name="User 1 Agent",
        description="Agent belonging to user 1",
        user_id=user1.id,
        status=AgentStatus.ACTIVE,
        usage_count=0
    )
    db_session.add(agent1)
    
    # Create agent for user 2
    agent2 = Agent(
        uuid="test-agent-2",
        agent_id="agent_test_2",
        team_name="User 2 Agent",
        description="Agent belonging to user 2",
        user_id=user2.id,
        status=AgentStatus.ACTIVE,
        usage_count=0
    )
    db_session.add(agent2)
    
    await db_session.commit()
    await db_session.refresh(agent1)
    await db_session.refresh(agent2)
    
    return agent1, agent2


@pytest.fixture
def user1_token(test_users):
    """Create JWT token for user 1."""
    user1, _ = test_users
    return create_access_token(subject=str(user1.id))


@pytest.fixture
def user2_token(test_users):
    """Create JWT token for user 2."""
    _, user2 = test_users
    return create_access_token(subject=str(user2.id))


class TestAgentUserIsolation:
    """Test agent user isolation functionality."""
    
    async def test_list_agents_user_isolation(
        self, 
        client: AsyncClient, 
        test_agents, 
        user1_token, 
        user2_token
    ):
        """Test that users can only see their own agents."""
        agent1, agent2 = test_agents
        
        # User 1 should only see their agent
        response = await client.get(
            "/api/v1/agents/",
            headers={"Authorization": f"Bearer {user1_token}"}
        )
        assert response.status_code == 200
        agents = response.json()
        assert len(agents) == 1
        assert agents[0]["agent_id"] == agent1.agent_id
        
        # User 2 should only see their agent
        response = await client.get(
            "/api/v1/agents/",
            headers={"Authorization": f"Bearer {user2_token}"}
        )
        assert response.status_code == 200
        agents = response.json()
        assert len(agents) == 1
        assert agents[0]["agent_id"] == agent2.agent_id
    
    async def test_get_agent_user_isolation(
        self, 
        client: AsyncClient, 
        test_agents, 
        user1_token, 
        user2_token
    ):
        """Test that users can only access their own agents."""
        agent1, agent2 = test_agents
        
        # User 1 can access their own agent
        response = await client.get(
            f"/api/v1/agents/{agent1.agent_id}",
            headers={"Authorization": f"Bearer {user1_token}"}
        )
        assert response.status_code == 200
        agent_data = response.json()
        assert agent_data["agent_id"] == agent1.agent_id
        
        # User 1 cannot access user 2's agent
        response = await client.get(
            f"/api/v1/agents/{agent2.agent_id}",
            headers={"Authorization": f"Bearer {user1_token}"}
        )
        assert response.status_code == 404
        
        # User 2 can access their own agent
        response = await client.get(
            f"/api/v1/agents/{agent2.agent_id}",
            headers={"Authorization": f"Bearer {user2_token}"}
        )
        assert response.status_code == 200
        agent_data = response.json()
        assert agent_data["agent_id"] == agent2.agent_id
        
        # User 2 cannot access user 1's agent
        response = await client.get(
            f"/api/v1/agents/{agent1.agent_id}",
            headers={"Authorization": f"Bearer {user2_token}"}
        )
        assert response.status_code == 404
    
    async def test_update_agent_user_isolation(
        self, 
        client: AsyncClient, 
        test_agents, 
        user1_token, 
        user2_token
    ):
        """Test that users can only update their own agents."""
        agent1, agent2 = test_agents
        
        update_data = {
            "team_name": "Updated Agent Name",
            "description": "Updated description"
        }
        
        # User 1 can update their own agent
        response = await client.put(
            f"/api/v1/agents/{agent1.agent_id}",
            json=update_data,
            headers={"Authorization": f"Bearer {user1_token}"}
        )
        assert response.status_code == 200
        
        # User 1 cannot update user 2's agent
        response = await client.put(
            f"/api/v1/agents/{agent2.agent_id}",
            json=update_data,
            headers={"Authorization": f"Bearer {user1_token}"}
        )
        assert response.status_code == 404
        
        # User 2 can update their own agent
        response = await client.put(
            f"/api/v1/agents/{agent2.agent_id}",
            json=update_data,
            headers={"Authorization": f"Bearer {user2_token}"}
        )
        assert response.status_code == 200
        
        # User 2 cannot update user 1's agent
        response = await client.put(
            f"/api/v1/agents/{agent1.agent_id}",
            json=update_data,
            headers={"Authorization": f"Bearer {user2_token}"}
        )
        assert response.status_code == 404
    
    async def test_delete_agent_user_isolation(
        self, 
        client: AsyncClient, 
        test_agents, 
        user1_token, 
        user2_token
    ):
        """Test that users can only delete their own agents."""
        agent1, agent2 = test_agents
        
        # User 1 cannot delete user 2's agent
        response = await client.delete(
            f"/api/v1/agents/{agent2.agent_id}",
            headers={"Authorization": f"Bearer {user1_token}"}
        )
        assert response.status_code == 404
        
        # User 2 cannot delete user 1's agent
        response = await client.delete(
            f"/api/v1/agents/{agent1.agent_id}",
            headers={"Authorization": f"Bearer {user2_token}"}
        )
        assert response.status_code == 404
        
        # User 1 can delete their own agent
        response = await client.delete(
            f"/api/v1/agents/{agent1.agent_id}",
            headers={"Authorization": f"Bearer {user1_token}"}
        )
        assert response.status_code == 200
    
    async def test_execute_agent_user_isolation(
        self, 
        client: AsyncClient, 
        test_agents, 
        user1_token, 
        user2_token
    ):
        """Test that users can only execute their own agents."""
        agent1, agent2 = test_agents
        
        execution_data = {
            "input": "Test input",
            "options": {}
        }
        
        # User 1 cannot execute user 2's agent
        response = await client.post(
            f"/api/v1/agents/{agent2.agent_id}/execute",
            json=execution_data,
            headers={"Authorization": f"Bearer {user1_token}"}
        )
        assert response.status_code == 404
        
        # User 2 cannot execute user 1's agent
        response = await client.post(
            f"/api/v1/agents/{agent1.agent_id}/execute",
            json=execution_data,
            headers={"Authorization": f"Bearer {user2_token}"}
        )
        assert response.status_code == 404
    
    async def test_agent_creation_associates_with_user(
        self, 
        client: AsyncClient, 
        user1_token,
        db_session: AsyncSession
    ):
        """Test that newly created agents are associated with the current user."""
        team_plan = {
            "team_name": "New Test Agent",
            "description": "Test agent creation",
            "team_members": [
                {
                    "name": "Test Member",
                    "role": "Assistant",
                    "description": "Test member",
                    "system_prompt": "You are a test assistant"
                }
            ]
        }
        
        response = await client.post(
            "/api/v1/agents/create",
            json=team_plan,
            headers={"Authorization": f"Bearer {user1_token}"}
        )
        assert response.status_code == 200
        
        agent_data = response.json()
        agent_id = agent_data["agent_id"]
        
        # Verify the agent is associated with user 1
        from sqlalchemy import text
        result = await db_session.execute(
            text("SELECT user_id FROM agents WHERE agent_id = :agent_id"),
            {"agent_id": agent_id}
        )
        agent_user_id = result.scalar()
        
        # Get user 1's ID from token
        from app.core.security import verify_token
        user_id = verify_token(user1_token)
        
        assert agent_user_id == int(user_id)
    
    async def test_unauthenticated_access_denied(
        self, 
        client: AsyncClient, 
        test_agents
    ):
        """Test that unauthenticated requests are denied."""
        agent1, _ = test_agents
        
        # List agents without authentication
        response = await client.get("/api/v1/agents/")
        assert response.status_code == 401
        
        # Get agent without authentication
        response = await client.get(f"/api/v1/agents/{agent1.agent_id}")
        assert response.status_code == 401
        
        # Update agent without authentication
        response = await client.put(
            f"/api/v1/agents/{agent1.agent_id}",
            json={"team_name": "Updated"}
        )
        assert response.status_code == 401
        
        # Delete agent without authentication
        response = await client.delete(f"/api/v1/agents/{agent1.agent_id}")
        assert response.status_code == 401
        
        # Execute agent without authentication
        response = await client.post(
            f"/api/v1/agents/{agent1.agent_id}/execute",
            json={"input": "test"}
        )
        assert response.status_code == 401
