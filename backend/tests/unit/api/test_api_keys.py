"""
Unit tests for API key management endpoints.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch
from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.v1.endpoints.api_keys import (
    create_api_key, list_api_keys, reveal_api_key, update_api_key,
    delete_api_key, get_api_key_usage, get_usage_summary
)
from app.models.settings import (
    APIKey, APIKeyCreate, APIKeyUpdate, APIKeyStatus, APIKeyProvider,
    encrypt_api_key, decrypt_api_key
)
from app.models.user import User


class TestAPIKeyEndpoints:
    """Test API key management endpoints."""
    
    @pytest.fixture
    def mock_user(self):
        """Create a mock user."""
        user = Mock(spec=User)
        user.id = 1
        user.email = "<EMAIL>"
        return user
    
    @pytest.fixture
    def mock_db(self):
        """Create a mock database session."""
        return Mock(spec=AsyncSession)
    
    @pytest.fixture
    def sample_api_key_create(self):
        """Create sample API key creation data."""
        return APIKeyCreate(
            name="Test OpenAI Key",
            description="Test key for OpenAI",
            provider=APIKeyProvider.OPENAI,
            key="sk-test123456789",
            base_url=None,
            expires_at=None,
            rate_limit_per_minute=100,
            rate_limit_per_day=10000
        )
    
    @pytest.fixture
    def sample_api_key(self):
        """Create a sample API key."""
        api_key = Mock(spec=APIKey)
        api_key.id = 1
        api_key.uuid = "test-uuid-123"
        api_key.user_id = 1
        api_key.name = "Test OpenAI Key"
        api_key.description = "Test key for OpenAI"
        api_key.provider = APIKeyProvider.OPENAI
        api_key.key_prefix = "sk-test1"
        api_key.encrypted_key = "encrypted_test_key"
        api_key.base_url = None
        api_key.status = APIKeyStatus.ACTIVE
        api_key.expires_at = None
        api_key.usage_count = 0
        api_key.last_used = None
        api_key.requests_today = 0
        api_key.requests_month = 0
        api_key.cost_today = 0.0
        api_key.cost_month = 0.0
        api_key.created_at = datetime.now()
        api_key.updated_at = None
        return api_key

    @pytest.mark.asyncio
    async def test_create_api_key_success(self, mock_user, mock_db, sample_api_key_create):
        """Test successful API key creation."""
        # Mock database operations
        mock_db.add = Mock()
        mock_db.commit = AsyncMock()
        mock_db.refresh = AsyncMock()
        
        # Mock the created API key
        created_key = Mock()
        created_key.id = 1
        created_key.uuid = "test-uuid-123"
        created_key.name = sample_api_key_create.name
        created_key.provider = sample_api_key_create.provider
        created_key.created_at = datetime.now()
        
        with patch('app.api.v1.endpoints.api_keys.encrypt_api_key') as mock_encrypt, \
             patch('app.api.v1.endpoints.api_keys.generate_key_prefix') as mock_prefix, \
             patch('app.api.v1.endpoints.api_keys.APIKey') as mock_api_key_class:
            
            mock_encrypt.return_value = "encrypted_key"
            mock_prefix.return_value = "sk-test1"
            mock_api_key_class.return_value = created_key
            
            result = await create_api_key(sample_api_key_create, mock_user, mock_db)
            
            assert result["success"] is True
            assert result["message"] == "API key created successfully"
            assert result["data"]["name"] == sample_api_key_create.name
            assert result["data"]["key"] == sample_api_key_create.key
            
            mock_encrypt.assert_called_once_with(sample_api_key_create.key)
            mock_prefix.assert_called_once_with(sample_api_key_create.key)
            mock_db.add.assert_called_once()
            mock_db.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_api_key_database_error(self, mock_user, mock_db, sample_api_key_create):
        """Test API key creation with database error."""
        mock_db.add = Mock()
        mock_db.commit = AsyncMock(side_effect=Exception("Database error"))
        mock_db.rollback = AsyncMock()
        
        with patch('app.api.v1.endpoints.api_keys.encrypt_api_key') as mock_encrypt, \
             patch('app.api.v1.endpoints.api_keys.generate_key_prefix') as mock_prefix, \
             patch('app.api.v1.endpoints.api_keys.APIKey'):
            
            mock_encrypt.return_value = "encrypted_key"
            mock_prefix.return_value = "sk-test1"
            
            with pytest.raises(HTTPException) as exc_info:
                await create_api_key(sample_api_key_create, mock_user, mock_db)
            
            assert exc_info.value.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
            assert "Failed to create API key" in str(exc_info.value.detail)
            mock_db.rollback.assert_called_once()

    @pytest.mark.asyncio
    async def test_list_api_keys_success(self, mock_user, mock_db, sample_api_key):
        """Test successful API keys listing."""
        # Mock database query
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = [sample_api_key]
        mock_db.execute = AsyncMock(return_value=mock_result)
        
        result = await list_api_keys(mock_user, mock_db)
        
        assert len(result) == 1
        assert result[0] == sample_api_key
        mock_db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_reveal_api_key_success(self, mock_user, mock_db, sample_api_key):
        """Test successful API key revelation."""
        # Mock database query
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_api_key
        mock_db.execute = AsyncMock(return_value=mock_result)
        
        with patch('app.api.v1.endpoints.api_keys.decrypt_api_key') as mock_decrypt:
            mock_decrypt.return_value = "sk-test123456789"
            
            result = await reveal_api_key(1, mock_user, mock_db)
            
            assert result["success"] is True
            assert result["data"]["key"] == "sk-test123456789"
            assert result["data"]["name"] == sample_api_key.name
            mock_decrypt.assert_called_once_with(sample_api_key.encrypted_key)

    @pytest.mark.asyncio
    async def test_reveal_api_key_not_found(self, mock_user, mock_db):
        """Test API key revelation when key not found."""
        # Mock database query returning None
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_db.execute = AsyncMock(return_value=mock_result)
        
        with pytest.raises(HTTPException) as exc_info:
            await reveal_api_key(999, mock_user, mock_db)
        
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "API key not found" in str(exc_info.value.detail)

    @pytest.mark.asyncio
    async def test_reveal_api_key_no_encrypted_key(self, mock_user, mock_db, sample_api_key):
        """Test API key revelation when encrypted key is missing."""
        sample_api_key.encrypted_key = None
        
        # Mock database query
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_api_key
        mock_db.execute = AsyncMock(return_value=mock_result)
        
        with pytest.raises(HTTPException) as exc_info:
            await reveal_api_key(1, mock_user, mock_db)
        
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "created before encryption support" in str(exc_info.value.detail)

    @pytest.mark.asyncio
    async def test_update_api_key_success(self, mock_user, mock_db, sample_api_key):
        """Test successful API key update."""
        # Mock database query
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_api_key
        mock_db.execute = AsyncMock(return_value=mock_result)
        mock_db.commit = AsyncMock()
        mock_db.refresh = AsyncMock()
        
        update_data = APIKeyUpdate(
            name="Updated Key Name",
            description="Updated description",
            status=APIKeyStatus.INACTIVE
        )
        
        result = await update_api_key(1, update_data, mock_user, mock_db)
        
        assert result == sample_api_key
        assert sample_api_key.updated_at is not None
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_api_key_invalid_status_transition(self, mock_user, mock_db, sample_api_key):
        """Test API key update with invalid status transition."""
        sample_api_key.status = APIKeyStatus.EXPIRED
        
        # Mock database query
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_api_key
        mock_db.execute = AsyncMock(return_value=mock_result)
        
        update_data = APIKeyUpdate(status=APIKeyStatus.ACTIVE)
        
        with pytest.raises(HTTPException) as exc_info:
            await update_api_key(1, update_data, mock_user, mock_db)
        
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "Cannot change status from" in str(exc_info.value.detail)

    @pytest.mark.asyncio
    async def test_delete_api_key_success(self, mock_user, mock_db, sample_api_key):
        """Test successful API key deletion."""
        # Mock database query
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_api_key
        mock_db.execute = AsyncMock(return_value=mock_result)
        mock_db.delete = AsyncMock()
        mock_db.commit = AsyncMock()
        
        result = await delete_api_key(1, mock_user, mock_db)
        
        assert result["success"] is True
        assert result["message"] == "API key deleted successfully"
        assert result["data"]["id"] == 1
        mock_db.delete.assert_called_once_with(sample_api_key)
        mock_db.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_delete_api_key_not_found(self, mock_user, mock_db):
        """Test API key deletion when key not found."""
        # Mock database query returning None
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_db.execute = AsyncMock(return_value=mock_result)
        
        with pytest.raises(HTTPException) as exc_info:
            await delete_api_key(999, mock_user, mock_db)
        
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "API key not found" in str(exc_info.value.detail)

    @pytest.mark.asyncio
    async def test_test_api_key_success(self, mock_user, mock_db, sample_api_key):
        """Test successful API key testing."""
        # Mock database query
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_api_key
        mock_db.execute = AsyncMock(return_value=mock_result)
        mock_db.commit = AsyncMock()

        # Mock provider creation and health check
        mock_provider = Mock()
        mock_provider.health_check = AsyncMock(return_value=True)

        with patch('app.api.v1.endpoints.api_keys.decrypt_api_key') as mock_decrypt, \
             patch('app.api.v1.endpoints.api_keys.provider_manager') as mock_manager:

            mock_decrypt.return_value = "sk-test123456789"
            mock_manager.create_provider_with_config.return_value = mock_provider

            from app.models.settings import APIKeyTest
            from app.api.v1.endpoints.api_keys import test_api_key as test_api_key_endpoint
            test_data = APIKeyTest()

            result = await test_api_key_endpoint(1, test_data, mock_user, mock_db)

            assert result["success"] is True
            assert "working correctly" in result["message"]
            assert result["data"]["status"] == "valid"

            # Verify usage was updated
            assert sample_api_key.usage_count == 1
            assert sample_api_key.last_used is not None

    @pytest.mark.asyncio
    async def test_get_api_key_usage_success(self, mock_user, mock_db, sample_api_key):
        """Test successful API key usage retrieval."""
        # Mock database query
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_api_key
        mock_db.execute = AsyncMock(return_value=mock_result)

        # Mock usage tracker
        mock_usage_stats = {
            "total_requests": 100,
            "requests_today": 10,
            "requests_month": 50,
            "cost_today": 1.23,
            "cost_month": 15.67,
            "last_used": "2025-07-02T10:00:00"
        }

        with patch('app.api.v1.endpoints.api_keys.UsageTracker') as mock_tracker_class:
            mock_tracker = Mock()
            mock_tracker.get_usage_stats = AsyncMock(return_value=mock_usage_stats)
            mock_tracker_class.return_value = mock_tracker

            result = await get_api_key_usage(1, mock_user, mock_db)

            assert result["success"] is True
            assert result["data"]["key_name"] == sample_api_key.name
            assert result["data"]["usage"] == mock_usage_stats

    @pytest.mark.asyncio
    async def test_get_usage_summary_success(self, mock_user, mock_db):
        """Test successful usage summary retrieval."""
        mock_summary = {
            "total_keys": 3,
            "active_keys": 2,
            "total_requests": 500,
            "requests_today": 50,
            "requests_month": 200,
            "cost_today": 5.67,
            "cost_month": 45.89
        }

        with patch('app.api.v1.endpoints.api_keys.UsageTracker') as mock_tracker_class:
            mock_tracker = Mock()
            mock_tracker.get_user_usage_summary = AsyncMock(return_value=mock_summary)
            mock_tracker_class.return_value = mock_tracker

            result = await get_usage_summary(mock_user, mock_db)

            assert result["success"] is True
            assert result["data"] == mock_summary
