"""
Unit tests for database configuration and session management.
"""

import pytest
from unittest.mock import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import (
    create_db_and_tables,
    get_session,
    init_db,
    close_db,
    engine,
    AsyncSessionLocal
)


class TestDatabaseConfiguration:
    """Test database configuration."""
    
    def test_engine_creation(self):
        """Test database engine creation."""
        assert engine is not None
        assert hasattr(engine, 'url')
        assert hasattr(engine, 'dialect')
    
    def test_session_factory_creation(self):
        """Test session factory creation."""
        assert AsyncSessionLocal is not None
        assert hasattr(AsyncSessionLocal, '__call__')
    
    @pytest.mark.asyncio
    async def test_get_session_generator(self):
        """Test get_session generator function."""
        # This test uses the actual test database session
        session_generator = get_session()
        
        # Should be an async generator
        assert hasattr(session_generator, '__aiter__')
        assert hasattr(session_generator, '__anext__')
    
    @pytest.mark.asyncio
    async def test_get_session_yields_session(self, test_session):
        """Test that get_session yields a valid session."""
        # Using the test session from conftest.py
        assert isinstance(test_session, AsyncSession)
        assert hasattr(test_session, 'execute')
        assert hasattr(test_session, 'commit')
        assert hasattr(test_session, 'rollback')
        assert hasattr(test_session, 'close')


class TestDatabaseOperations:
    """Test database operations."""
    
    @pytest.mark.asyncio
    async def test_create_db_and_tables(self):
        """Test database and tables creation."""
        with patch('app.core.database.engine') as mock_engine:
            mock_conn = AsyncMock()
            mock_engine.begin.return_value.__aenter__.return_value = mock_conn
            
            await create_db_and_tables()
            
            # Verify engine.begin was called
            mock_engine.begin.assert_called_once()
            # Verify run_sync was called on connection
            mock_conn.run_sync.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_init_db(self):
        """Test database initialization."""
        with patch('app.core.database.create_db_and_tables') as mock_create:
            mock_create.return_value = AsyncMock()
            
            await init_db()
            
            mock_create.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_close_db(self):
        """Test database connection closing."""
        with patch('app.core.database.engine') as mock_engine:
            mock_engine.dispose = AsyncMock()
            
            await close_db()
            
            mock_engine.dispose.assert_called_once()


class TestSessionManagement:
    """Test session management."""
    
    @pytest.mark.asyncio
    async def test_session_context_manager(self, test_session):
        """Test session as context manager."""
        # The test_session fixture already provides a session
        # Test that it has the expected methods
        assert hasattr(test_session, '__aenter__')
        assert hasattr(test_session, '__aexit__')
    
    @pytest.mark.asyncio
    async def test_session_transaction_commit(self, test_session):
        """Test session transaction commit."""
        # Test that we can call commit without errors
        await test_session.commit()
        # No exception should be raised
    
    @pytest.mark.asyncio
    async def test_session_transaction_rollback(self, test_session):
        """Test session transaction rollback."""
        # Test that we can call rollback without errors
        await test_session.rollback()
        # No exception should be raised
    
    @pytest.mark.asyncio
    async def test_session_close(self, test_session):
        """Test session closing."""
        # Test that we can call close without errors
        await test_session.close()
        # No exception should be raised


class TestDatabaseErrorHandling:
    """Test database error handling."""
    
    @pytest.mark.asyncio
    async def test_get_session_error_handling(self):
        """Test get_session error handling."""
        with patch('app.core.database.AsyncSessionLocal') as mock_session_factory:
            # Create a mock session that raises an exception
            mock_session = AsyncMock()
            mock_session.__aenter__.side_effect = Exception("Database connection error")
            mock_session_factory.return_value = mock_session
            
            # Test that the exception is properly handled
            session_gen = get_session()
            with pytest.raises(Exception, match="Database connection error"):
                async for session in session_gen:
                    pass
    
    @pytest.mark.asyncio
    async def test_session_rollback_on_error(self):
        """Test session rollback on error."""
        with patch('app.core.database.AsyncSessionLocal') as mock_session_factory:
            mock_session = AsyncMock()
            mock_session.rollback = AsyncMock()
            mock_session.close = AsyncMock()
            
            # Simulate an error during session usage
            async def mock_aenter():
                return mock_session
            
            async def mock_aexit(exc_type, exc_val, exc_tb):
                if exc_type:
                    await mock_session.rollback()
                await mock_session.close()
            
            mock_session.__aenter__ = mock_aenter
            mock_session.__aexit__ = mock_aexit
            mock_session_factory.return_value = mock_session
            
            # Simulate using the session with an error
            try:
                async for session in get_session():
                    raise ValueError("Simulated error")
            except ValueError:
                pass
            
            # Verify rollback was called
            mock_session.rollback.assert_called()
    
    @pytest.mark.asyncio
    async def test_create_db_and_tables_error(self):
        """Test create_db_and_tables error handling."""
        with patch('app.core.database.engine') as mock_engine:
            mock_engine.begin.side_effect = Exception("Database creation error")
            
            with pytest.raises(Exception, match="Database creation error"):
                await create_db_and_tables()
    
    @pytest.mark.asyncio
    async def test_init_db_error(self):
        """Test init_db error handling."""
        with patch('app.core.database.create_db_and_tables') as mock_create:
            mock_create.side_effect = Exception("Initialization error")
            
            with pytest.raises(Exception, match="Initialization error"):
                await init_db()
    
    @pytest.mark.asyncio
    async def test_close_db_error(self):
        """Test close_db error handling."""
        with patch('app.core.database.engine') as mock_engine:
            mock_engine.dispose.side_effect = Exception("Close error")
            
            with pytest.raises(Exception, match="Close error"):
                await close_db()


class TestDatabaseIntegration:
    """Test database integration scenarios."""
    
    @pytest.mark.asyncio
    async def test_database_lifecycle(self):
        """Test complete database lifecycle."""
        with patch('app.core.database.engine') as mock_engine:
            # Setup mocks
            mock_conn = AsyncMock()
            mock_engine.begin.return_value.__aenter__.return_value = mock_conn
            mock_engine.dispose = AsyncMock()
            
            # Test initialization
            await init_db()
            mock_engine.begin.assert_called()
            mock_conn.run_sync.assert_called()
            
            # Test cleanup
            await close_db()
            mock_engine.dispose.assert_called()
    
    @pytest.mark.asyncio
    async def test_multiple_sessions(self):
        """Test multiple concurrent sessions."""
        sessions = []
        
        # Create multiple sessions
        for _ in range(3):
            async for session in get_session():
                sessions.append(session)
                break  # Get one session from each generator
        
        # All sessions should be AsyncSession instances
        for session in sessions:
            assert isinstance(session, AsyncSession)
        
        # Clean up sessions
        for session in sessions:
            await session.close()
    
    @pytest.mark.asyncio
    async def test_session_isolation(self, test_session):
        """Test session isolation."""
        # This test verifies that sessions are properly isolated
        # The test_session fixture should provide a clean session
        
        # Execute a simple query to verify session works
        result = await test_session.execute("SELECT 1")
        assert result is not None
        
        # Rollback should not affect other sessions
        await test_session.rollback()
        
        # Session should still be usable
        result = await test_session.execute("SELECT 1")
        assert result is not None


class TestDatabaseConfiguration:
    """Test database configuration scenarios."""
    
    def test_engine_configuration(self):
        """Test engine configuration parameters."""
        # Test that engine has expected configuration
        assert hasattr(engine, 'url')
        assert hasattr(engine, 'pool')
        assert hasattr(engine, 'dialect')
        
        # For SQLite, should have appropriate settings
        if 'sqlite' in str(engine.url):
            assert engine.url.drivername in ['sqlite+aiosqlite']
    
    def test_session_factory_configuration(self):
        """Test session factory configuration."""
        # Test session factory parameters
        assert AsyncSessionLocal.class_ == AsyncSession
        assert AsyncSessionLocal.expire_on_commit is False
    
    @pytest.mark.asyncio
    async def test_database_url_handling(self):
        """Test database URL handling."""
        # Test with different database URL formats
        test_urls = [
            "sqlite+aiosqlite:///test.db",
            "sqlite+aiosqlite:///:memory:",
            "postgresql+asyncpg://user:pass@localhost/db"
        ]
        
        for url in test_urls:
            with patch('app.core.config.get_database_url', return_value=url):
                # Should not raise an exception
                from sqlalchemy.ext.asyncio import create_async_engine
                test_engine = create_async_engine(url, echo=False)
                assert test_engine is not None
                await test_engine.dispose()
