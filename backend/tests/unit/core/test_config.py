"""
Unit tests for configuration.
"""

import pytest
import os
from unittest.mock import patch

from app.core.config import Settings, get_database_url, get_cors_config


class TestSettings:
    """Test Settings configuration."""
    
    def test_default_settings(self):
        """Test settings can be created and have expected types."""
        settings = Settings()

        # Application settings
        assert isinstance(settings.APP_NAME, str)
        assert isinstance(settings.APP_VERSION, str)
        assert isinstance(settings.APP_DESCRIPTION, str)
        assert isinstance(settings.DEBUG, bool)
        assert isinstance(settings.ENVIRONMENT, str)

        # Server settings
        assert isinstance(settings.HOST, str)
        assert isinstance(settings.PORT, int)
        assert isinstance(settings.RELOAD, bool)

        # Database settings
        assert isinstance(settings.DATABASE_URL, str)
        assert isinstance(settings.DATABASE_ECHO, bool)

        # Security settings
        assert isinstance(settings.SECRET_KEY, str)
        assert len(settings.SECRET_KEY) > 0
        assert isinstance(settings.ACCESS_TOKEN_EXPIRE_MINUTES, int)
        assert isinstance(settings.ALGORITHM, str)
    
    def test_settings_with_environment_variables(self):
        """Test settings with environment variables."""
        env_vars = {
            "APP_NAME": "Test-Agent",
            "DEBUG": "true",
            "PORT": "9000",
            "DATABASE_URL": "postgresql://test:test@localhost/test",
            "SECRET_KEY": "test-secret-key",
            "OPENAI_API_KEY": "test-openai-key",
            "ANTHROPIC_API_KEY": "test-anthropic-key"
        }
        
        with patch.dict(os.environ, env_vars):
            settings = Settings()
            
            assert settings.APP_NAME == "Test-Agent"
            assert settings.DEBUG is True
            assert settings.PORT == 9000
            assert settings.DATABASE_URL == "postgresql://test:test@localhost/test"
            assert settings.SECRET_KEY == "test-secret-key"
            assert settings.OPENAI_API_KEY == "test-openai-key"
            assert settings.ANTHROPIC_API_KEY == "test-anthropic-key"
    
    def test_cors_settings(self):
        """Test CORS settings."""
        settings = Settings()

        assert isinstance(settings.BACKEND_CORS_ORIGINS, list)
        # CORS settings are handled by get_cors_config function, not as direct attributes
    
    def test_ai_provider_settings(self):
        """Test AI provider settings."""
        settings = Settings()

        # Should have API key fields (may be None or have values from .env)
        assert hasattr(settings, 'OPENAI_API_KEY')
        assert hasattr(settings, 'ANTHROPIC_API_KEY')
        assert hasattr(settings, 'GOOGLE_API_KEY')
        
        # LangSmith settings
        assert settings.LANGCHAIN_TRACING_V2 is False
        assert settings.LANGCHAIN_API_KEY is None
        assert settings.LANGCHAIN_PROJECT == "meta-agent"
    
    def test_logging_settings(self):
        """Test logging settings."""
        settings = Settings()
        
        assert settings.LOG_LEVEL == "INFO"
        assert settings.LOG_FORMAT == "json"
        assert "logs/" in settings.LOG_FILE
        assert settings.LOG_ROTATION == "1 day"
        assert settings.LOG_RETENTION == "30 days"
    
    def test_file_upload_settings(self):
        """Test file upload settings."""
        settings = Settings()
        
        assert settings.MAX_UPLOAD_SIZE == 10 * 1024 * 1024  # 10MB
        assert "uploads" in settings.UPLOAD_DIR
        assert "generated_agents" in settings.GENERATED_AGENTS_DIR
    
    def test_rate_limiting_settings(self):
        """Test rate limiting settings."""
        settings = Settings()
        
        assert settings.RATE_LIMIT_PER_MINUTE == 100
        assert settings.RATE_LIMIT_BURST == 20
    
    def test_redis_settings(self):
        """Test Redis settings."""
        settings = Settings()
        
        assert "redis://localhost:6379" in settings.REDIS_URL
    
    def test_settings_validation(self):
        """Test settings validation."""
        # Test with invalid values
        with patch.dict(os.environ, {"PORT": "invalid"}):
            with pytest.raises(ValueError):
                Settings()
        
        with patch.dict(os.environ, {"DEBUG": "invalid"}):
            with pytest.raises(ValueError):
                Settings()


class TestDatabaseURL:
    """Test database URL configuration."""
    
    def test_get_database_url_default(self):
        """Test getting default database URL."""
        url = get_database_url()
        assert "sqlite+aiosqlite" in url
        assert "meta_agent.db" in url
    
    def test_get_database_url_with_settings(self):
        """Test getting database URL with custom settings."""
        with patch.dict(os.environ, {"DATABASE_URL": "postgresql://user:pass@localhost/db"}):
            url = get_database_url()
            assert url == "postgresql://user:pass@localhost/db"
    
    def test_get_database_url_environment_variable(self):
        """Test getting database URL from environment variable."""
        test_url = "mysql://user:pass@localhost/testdb"
        with patch.dict(os.environ, {"DATABASE_URL": test_url}):
            url = get_database_url()
            assert url == test_url


class TestCORSConfig:
    """Test CORS configuration."""
    
    def test_get_cors_config_default(self):
        """Test getting default CORS configuration."""
        config = get_cors_config()
        
        assert isinstance(config, dict)
        assert "allow_origins" in config
        assert "allow_credentials" in config
        assert "allow_methods" in config
        assert "allow_headers" in config
        
        assert config["allow_credentials"] is True
        assert isinstance(config["allow_origins"], list)
        assert isinstance(config["allow_methods"], list)
        assert isinstance(config["allow_headers"], list)
    
    def test_get_cors_config_with_origins(self):
        """Test CORS configuration with custom origins."""
        origins_str = "http://localhost:3000,https://example.com"
        with patch.dict(os.environ, {"BACKEND_CORS_ORIGINS": origins_str}):
            config = get_cors_config()

            assert len(config["allow_origins"]) == 2
            assert "http://localhost:3000" in config["allow_origins"]
            assert "https://example.com" in config["allow_origins"]
    
    def test_get_cors_config_development(self):
        """Test CORS configuration in development mode."""
        with patch.dict(os.environ, {"DEBUG": "true", "ENVIRONMENT": "development"}):
            config = get_cors_config()

            # Should have CORS configuration
            assert isinstance(config["allow_origins"], list)

    def test_get_cors_config_production(self):
        """Test CORS configuration in production mode."""
        with patch.dict(os.environ, {"DEBUG": "false", "ENVIRONMENT": "production"}):
            config = get_cors_config()

            # In production, should be more restrictive
            assert isinstance(config["allow_origins"], list)
    
    def test_cors_methods(self):
        """Test CORS allowed methods."""
        config = get_cors_config()
        methods = config["allow_methods"]

        # The configuration uses ["*"] which allows all methods
        assert methods == ["*"]
    
    def test_cors_headers(self):
        """Test CORS allowed headers."""
        config = get_cors_config()
        headers = config["allow_headers"]

        # The configuration uses ["*"] which allows all headers
        assert headers == ["*"]


class TestSettingsIntegration:
    """Test settings integration scenarios."""
    
    def test_settings_for_testing(self):
        """Test settings configuration for testing."""
        test_env = {
            "ENVIRONMENT": "test",
            "DEBUG": "true",
            "DATABASE_URL": "sqlite+aiosqlite:///:memory:",
            "LOG_LEVEL": "DEBUG"
        }
        
        with patch.dict(os.environ, test_env):
            settings = Settings()
            
            assert settings.ENVIRONMENT == "test"
            assert settings.DEBUG is True
            assert ":memory:" in settings.DATABASE_URL
            assert settings.LOG_LEVEL == "DEBUG"
    
    def test_settings_for_development(self):
        """Test settings configuration for development."""
        dev_env = {
            "ENVIRONMENT": "development",
            "DEBUG": "true",
            "RELOAD": "true",
            "DATABASE_ECHO": "true"
        }
        
        with patch.dict(os.environ, dev_env):
            settings = Settings()
            
            assert settings.ENVIRONMENT == "development"
            assert settings.DEBUG is True
            assert settings.RELOAD is True
            assert settings.DATABASE_ECHO is True
    
    def test_settings_for_production(self):
        """Test settings configuration for production."""
        prod_env = {
            "ENVIRONMENT": "production",
            "DEBUG": "false",
            "SECRET_KEY": "production-secret-key",
            "DATABASE_URL": "******************************/metaagent"
        }
        
        with patch.dict(os.environ, prod_env):
            settings = Settings()
            
            assert settings.ENVIRONMENT == "production"
            assert settings.DEBUG is False
            assert settings.SECRET_KEY == "production-secret-key"
            assert "postgresql" in settings.DATABASE_URL
    
    def test_settings_validation_edge_cases(self):
        """Test settings validation edge cases."""
        # Test empty string values
        with patch.dict(os.environ, {"APP_NAME": ""}):
            settings = Settings()
            assert settings.APP_NAME == ""  # Should allow empty string
        
        # Test whitespace values
        with patch.dict(os.environ, {"APP_NAME": "   "}):
            settings = Settings()
            assert settings.APP_NAME == "   "
    
    def test_settings_type_conversion(self):
        """Test settings type conversion."""
        env_vars = {
            "PORT": "8080",
            "DEBUG": "True",
            "ACCESS_TOKEN_EXPIRE_MINUTES": "60",
            "MAX_UPLOAD_SIZE": "20971520"  # 20MB
        }
        
        with patch.dict(os.environ, env_vars):
            settings = Settings()
            
            assert isinstance(settings.PORT, int)
            assert settings.PORT == 8080
            assert isinstance(settings.DEBUG, bool)
            assert settings.DEBUG is True
            assert isinstance(settings.ACCESS_TOKEN_EXPIRE_MINUTES, int)
            assert settings.ACCESS_TOKEN_EXPIRE_MINUTES == 60
            assert isinstance(settings.MAX_UPLOAD_SIZE, int)
            assert settings.MAX_UPLOAD_SIZE == 20971520
