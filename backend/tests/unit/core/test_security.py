"""
Unit tests for security utilities.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import patch

from app.core.security import (
    create_access_token, verify_token, verify_password, get_password_hash,
    generate_api_key, validate_api_key, sanitize_input, validate_file_upload,
    rate_limit_key, RateLimiter
)


class TestPasswordSecurity:
    """Test password hashing and verification."""
    
    def test_password_hashing(self):
        """Test password hashing."""
        password = "test_password_123"
        hashed = get_password_hash(password)
        
        # Hash should be different from original password
        assert hashed != password
        assert len(hashed) > 50  # bcrypt hashes are long
        assert hashed.startswith("$2b$")  # bcrypt prefix
    
    def test_password_verification_success(self):
        """Test successful password verification."""
        password = "test_password_123"
        hashed = get_password_hash(password)
        
        assert verify_password(password, hashed) is True
    
    def test_password_verification_failure(self):
        """Test failed password verification."""
        password = "test_password_123"
        wrong_password = "wrong_password"
        hashed = get_password_hash(password)
        
        assert verify_password(wrong_password, hashed) is False
    
    def test_password_hash_uniqueness(self):
        """Test that same password produces different hashes."""
        password = "test_password_123"
        hash1 = get_password_hash(password)
        hash2 = get_password_hash(password)
        
        # Hashes should be different due to salt
        assert hash1 != hash2
        
        # But both should verify correctly
        assert verify_password(password, hash1) is True
        assert verify_password(password, hash2) is True


class TestJWTTokens:
    """Test JWT token creation and verification."""
    
    def test_create_access_token(self):
        """Test access token creation."""
        user_id = "123"
        token = create_access_token(subject=user_id)
        
        assert isinstance(token, str)
        assert len(token) > 50  # JWT tokens are long
        assert "." in token  # JWT format has dots
    
    def test_verify_token_success(self):
        """Test successful token verification."""
        user_id = "123"
        token = create_access_token(subject=user_id)
        
        verified_user_id = verify_token(token)
        assert verified_user_id == user_id
    
    def test_verify_token_invalid(self):
        """Test verification of invalid token."""
        invalid_token = "invalid.token.here"
        
        result = verify_token(invalid_token)
        assert result is None
    
    def test_verify_token_expired(self):
        """Test verification of expired token."""
        user_id = "123"
        # Create token that expires immediately
        expired_delta = timedelta(seconds=-1)
        token = create_access_token(subject=user_id, expires_delta=expired_delta)
        
        result = verify_token(token)
        assert result is None
    
    def test_create_token_with_custom_expiry(self):
        """Test token creation with custom expiry."""
        user_id = "123"
        custom_delta = timedelta(hours=2)
        token = create_access_token(subject=user_id, expires_delta=custom_delta)
        
        # Token should be valid
        verified_user_id = verify_token(token)
        assert verified_user_id == user_id


class TestAPIKeySecurity:
    """Test API key generation and validation."""
    
    def test_generate_api_key(self):
        """Test API key generation."""
        api_key = generate_api_key()
        
        assert isinstance(api_key, str)
        assert api_key.startswith("ma_")
        assert len(api_key) == 46  # ma_ + 43 characters
    
    def test_validate_api_key_valid(self):
        """Test validation of valid API key."""
        api_key = generate_api_key()
        
        assert validate_api_key(api_key) is True
    
    def test_validate_api_key_invalid_format(self):
        """Test validation of invalid API key format."""
        invalid_keys = [
            "invalid_key",
            "ma_short",
            "wrong_prefix_" + "a" * 43,
            "",
            None
        ]
        
        for invalid_key in invalid_keys:
            if invalid_key is not None:
                assert validate_api_key(invalid_key) is False
    
    def test_api_key_uniqueness(self):
        """Test that generated API keys are unique."""
        key1 = generate_api_key()
        key2 = generate_api_key()
        
        assert key1 != key2


class TestInputSanitization:
    """Test input sanitization."""
    
    def test_sanitize_basic_input(self):
        """Test basic input sanitization."""
        clean_input = "Hello, World!"
        result = sanitize_input(clean_input)
        
        assert result == clean_input
    
    def test_sanitize_html_entities(self):
        """Test HTML entity sanitization."""
        malicious_input = "<script>alert('xss')</script>"
        result = sanitize_input(malicious_input)
        
        # Should escape HTML
        assert "&lt;" in result
        assert "&gt;" in result
        assert "<script>" not in result
    
    def test_sanitize_javascript_injection(self):
        """Test JavaScript injection sanitization."""
        malicious_inputs = [
            "javascript:alert('xss')",
            "vbscript:msgbox('xss')",
            "onload=alert('xss')",
            "onerror=alert('xss')",
            "onclick=alert('xss')"
        ]
        
        for malicious_input in malicious_inputs:
            result = sanitize_input(malicious_input)
            
            # Dangerous patterns should be removed
            assert "javascript:" not in result.lower()
            assert "vbscript:" not in result.lower()
            assert "onload=" not in result.lower()
            assert "onerror=" not in result.lower()
            assert "onclick=" not in result.lower()
    
    def test_sanitize_script_tags(self):
        """Test script tag sanitization."""
        malicious_input = "Hello <script>alert('xss')</script> World"
        result = sanitize_input(malicious_input)
        
        # Script tags should be removed
        assert "<script>" not in result
        assert "</script>" not in result
        assert "Hello  World" in result or "Hello World" in result
    
    def test_sanitize_preserves_safe_content(self):
        """Test that sanitization preserves safe content."""
        safe_input = "Hello, this is a normal string with numbers 123 and symbols !@#"
        result = sanitize_input(safe_input)
        
        assert result == safe_input


class TestFileUploadValidation:
    """Test file upload validation."""
    
    def test_validate_allowed_file_types(self):
        """Test validation of allowed file types."""
        allowed_files = [
            ("test.txt", "text/plain"),
            ("config.json", "application/json"),
            ("data.yaml", "application/x-yaml"),
            ("script.py", "text/x-python"),
            ("readme.md", "text/markdown")
        ]
        
        for filename, content_type in allowed_files:
            assert validate_file_upload(filename, content_type) is True
    
    def test_validate_disallowed_file_types(self):
        """Test validation of disallowed file types."""
        disallowed_files = [
            ("malware.exe", "application/x-executable"),
            ("script.bat", "application/x-bat"),
            ("image.jpg", "image/jpeg"),
            ("document.pdf", "application/pdf"),
            ("archive.zip", "application/zip")
        ]
        
        for filename, content_type in disallowed_files:
            assert validate_file_upload(filename, content_type) is False
    
    def test_validate_file_extension_mismatch(self):
        """Test validation with mismatched extension and content type."""
        # Extension says text, but content type says image
        assert validate_file_upload("test.txt", "image/jpeg") is False
        
        # Extension says python, but content type says executable
        assert validate_file_upload("script.py", "application/x-executable") is False


class TestRateLimiting:
    """Test rate limiting functionality."""
    
    def test_rate_limit_key_generation(self):
        """Test rate limit key generation."""
        identifier = "***********"
        endpoint = "/api/v1/auth/login"
        
        key = rate_limit_key(identifier, endpoint)
        
        assert key == "rate_limit:***********:/api/v1/auth/login"
    
    @pytest.mark.asyncio
    async def test_rate_limiter_without_redis(self):
        """Test rate limiter without Redis (should allow all requests)."""
        limiter = RateLimiter(redis_client=None)
        
        # Should allow request when no Redis
        is_allowed = await limiter.is_allowed("test_ip", "test_endpoint", limit=5)
        assert is_allowed is True
        
        # Should return full limit when no Redis
        remaining = await limiter.get_remaining("test_ip", "test_endpoint", limit=5)
        assert remaining == 5
    
    @pytest.mark.asyncio
    async def test_rate_limiter_with_mock_redis(self):
        """Test rate limiter with mocked Redis."""
        from unittest.mock import AsyncMock
        
        mock_redis = AsyncMock()
        mock_redis.get.return_value = None  # No existing count
        mock_redis.setex = AsyncMock()
        mock_redis.incr = AsyncMock()
        
        limiter = RateLimiter(redis_client=mock_redis)
        
        # First request should be allowed
        is_allowed = await limiter.is_allowed("test_ip", "test_endpoint", limit=5)
        assert is_allowed is True
        
        # Should set initial count
        mock_redis.setex.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_rate_limiter_exceeds_limit(self):
        """Test rate limiter when limit is exceeded."""
        from unittest.mock import AsyncMock
        
        mock_redis = AsyncMock()
        mock_redis.get.return_value = "5"  # Already at limit
        
        limiter = RateLimiter(redis_client=mock_redis)
        
        # Should not allow request when at limit
        is_allowed = await limiter.is_allowed("test_ip", "test_endpoint", limit=5)
        assert is_allowed is False
    
    @pytest.mark.asyncio
    async def test_rate_limiter_redis_failure(self):
        """Test rate limiter when Redis fails."""
        from unittest.mock import AsyncMock
        
        mock_redis = AsyncMock()
        mock_redis.get.side_effect = Exception("Redis connection failed")
        
        limiter = RateLimiter(redis_client=mock_redis)
        
        # Should allow request when Redis fails (fail open)
        is_allowed = await limiter.is_allowed("test_ip", "test_endpoint", limit=5)
        assert is_allowed is True
