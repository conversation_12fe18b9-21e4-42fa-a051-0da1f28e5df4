"""
Unit tests for AI providers with custom base URL functionality.
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock

from app.services.ai_providers import (
    OpenAIProvider, AnthropicProvider, AIProviderManager, AIProviderError
)


class TestOpenAIProviderBaseURL:
    """Test OpenAI provider with custom base URL."""
    
    @patch('app.services.ai_providers.get_openai_config')
    @patch('app.services.ai_providers.AsyncOpenAI')
    def test_openai_provider_default_base_url(self, mock_openai_class, mock_config):
        """Test OpenAI provider initialization with default base URL."""
        mock_config.return_value = {"api_key": "test-key"}
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        
        provider = OpenAIProvider()
        
        # Should be called with only api_key (no base_url)
        mock_openai_class.assert_called_once_with(api_key="test-key")
        assert provider.client == mock_client
    
    @patch('app.services.ai_providers.get_openai_config')
    @patch('app.services.ai_providers.AsyncOpenAI')
    def test_openai_provider_custom_base_url(self, mock_openai_class, mock_config):
        """Test OpenAI provider initialization with custom base URL."""
        mock_config.return_value = {"api_key": "test-key"}
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        
        custom_url = "https://proxy.company.com/openai/v1"
        provider = OpenAIProvider(base_url=custom_url)
        
        # Should be called with both api_key and base_url
        mock_openai_class.assert_called_once_with(
            api_key="test-key",
            base_url=custom_url
        )
        assert provider.client == mock_client
    
    @patch('app.services.ai_providers.get_openai_config')
    def test_openai_provider_missing_api_key(self, mock_config):
        """Test OpenAI provider initialization fails without API key."""
        mock_config.return_value = {"api_key": None}
        
        with pytest.raises(AIProviderError) as exc_info:
            OpenAIProvider()
        
        assert "API key not configured" in str(exc_info.value)
    
    @patch('app.services.ai_providers.get_openai_config')
    def test_openai_provider_import_error(self, mock_config):
        """Test OpenAI provider initialization fails when library not installed."""
        mock_config.return_value = {"api_key": "test-key"}
        
        with patch('app.services.ai_providers.AsyncOpenAI', side_effect=ImportError):
            with pytest.raises(AIProviderError) as exc_info:
                OpenAIProvider()
            
            assert "OpenAI library not installed" in str(exc_info.value)


class TestAnthropicProviderBaseURL:
    """Test Anthropic provider with custom base URL."""
    
    @patch('app.services.ai_providers.get_anthropic_config')
    @patch('app.services.ai_providers.AsyncAnthropic')
    def test_anthropic_provider_default_base_url(self, mock_anthropic_class, mock_config):
        """Test Anthropic provider initialization with default base URL."""
        mock_config.return_value = {"api_key": "test-key"}
        mock_client = Mock()
        mock_anthropic_class.return_value = mock_client
        
        provider = AnthropicProvider()
        
        # Should be called with only api_key (no base_url)
        mock_anthropic_class.assert_called_once_with(api_key="test-key")
        assert provider.client == mock_client
    
    @patch('app.services.ai_providers.get_anthropic_config')
    @patch('app.services.ai_providers.AsyncAnthropic')
    def test_anthropic_provider_custom_base_url(self, mock_anthropic_class, mock_config):
        """Test Anthropic provider initialization with custom base URL."""
        mock_config.return_value = {"api_key": "test-key"}
        mock_client = Mock()
        mock_anthropic_class.return_value = mock_client
        
        custom_url = "https://proxy.company.com/anthropic"
        provider = AnthropicProvider(base_url=custom_url)
        
        # Should be called with both api_key and base_url
        mock_anthropic_class.assert_called_once_with(
            api_key="test-key",
            base_url=custom_url
        )
        assert provider.client == mock_client
    
    @patch('app.services.ai_providers.get_anthropic_config')
    def test_anthropic_provider_missing_api_key(self, mock_config):
        """Test Anthropic provider initialization fails without API key."""
        mock_config.return_value = {"api_key": None}
        
        with pytest.raises(AIProviderError) as exc_info:
            AnthropicProvider()
        
        assert "API key not configured" in str(exc_info.value)


class TestAIProviderManagerBaseURL:
    """Test AI provider manager with custom base URL functionality."""
    
    @patch('app.services.ai_providers.settings')
    @patch('app.services.ai_providers.OpenAIProvider')
    def test_create_provider_with_config_openai(self, mock_openai_provider, mock_settings):
        """Test creating OpenAI provider with custom configuration."""
        mock_settings.OPENAI_API_KEY = "original-key"
        mock_provider_instance = Mock()
        mock_openai_provider.return_value = mock_provider_instance
        
        manager = AIProviderManager()
        
        # Test creating provider with custom config
        result = manager.create_provider_with_config(
            "openai", 
            "custom-key", 
            "https://proxy.company.com/openai/v1"
        )
        
        # Should temporarily set the API key and create provider with base_url
        mock_openai_provider.assert_called_once_with(
            base_url="https://proxy.company.com/openai/v1"
        )
        assert result == mock_provider_instance
        
        # Original API key should be restored
        assert mock_settings.OPENAI_API_KEY == "original-key"
    
    @patch('app.services.ai_providers.settings')
    @patch('app.services.ai_providers.AnthropicProvider')
    def test_create_provider_with_config_anthropic(self, mock_anthropic_provider, mock_settings):
        """Test creating Anthropic provider with custom configuration."""
        mock_settings.ANTHROPIC_API_KEY = "original-key"
        mock_provider_instance = Mock()
        mock_anthropic_provider.return_value = mock_provider_instance
        
        manager = AIProviderManager()
        
        # Test creating provider with custom config
        result = manager.create_provider_with_config(
            "anthropic", 
            "custom-key", 
            "https://proxy.company.com/anthropic"
        )
        
        # Should temporarily set the API key and create provider with base_url
        mock_anthropic_provider.assert_called_once_with(
            base_url="https://proxy.company.com/anthropic"
        )
        assert result == mock_provider_instance
        
        # Original API key should be restored
        assert mock_settings.ANTHROPIC_API_KEY == "original-key"
    
    def test_create_provider_with_config_unsupported(self):
        """Test creating provider with unsupported provider name."""
        manager = AIProviderManager()
        
        with pytest.raises(AIProviderError) as exc_info:
            manager.create_provider_with_config("unsupported", "key", "url")
        
        assert "Provider 'unsupported' not supported" in str(exc_info.value)
    
    @patch('app.services.ai_providers.settings')
    @patch('app.services.ai_providers.OpenAIProvider')
    def test_create_provider_with_config_no_base_url(self, mock_openai_provider, mock_settings):
        """Test creating provider without custom base URL."""
        mock_settings.OPENAI_API_KEY = "original-key"
        mock_provider_instance = Mock()
        mock_openai_provider.return_value = mock_provider_instance
        
        manager = AIProviderManager()
        
        # Test creating provider without base_url
        result = manager.create_provider_with_config("openai", "custom-key")
        
        # Should create provider with base_url=None
        mock_openai_provider.assert_called_once_with(base_url=None)
        assert result == mock_provider_instance
    
    @patch('app.services.ai_providers.settings')
    @patch('app.services.ai_providers.OpenAIProvider')
    def test_create_provider_with_config_exception_handling(self, mock_openai_provider, mock_settings):
        """Test that original API key is restored even if provider creation fails."""
        mock_settings.OPENAI_API_KEY = "original-key"
        mock_openai_provider.side_effect = Exception("Provider creation failed")
        
        manager = AIProviderManager()
        
        # Test that exception is propagated but API key is restored
        with pytest.raises(Exception) as exc_info:
            manager.create_provider_with_config("openai", "custom-key", "https://test.com")
        
        assert "Provider creation failed" in str(exc_info.value)
        # Original API key should be restored even after exception
        assert mock_settings.OPENAI_API_KEY == "original-key"


class TestProviderIntegration:
    """Integration tests for provider functionality with base URLs."""
    
    @pytest.mark.asyncio
    @patch('app.services.ai_providers.get_openai_config')
    @patch('app.services.ai_providers.AsyncOpenAI')
    async def test_openai_provider_health_check_with_custom_url(self, mock_openai_class, mock_config):
        """Test OpenAI provider health check with custom base URL."""
        mock_config.return_value = {"api_key": "test-key"}
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        
        # Mock the health check to return True
        with patch.object(OpenAIProvider, 'health_check', return_value=True):
            provider = OpenAIProvider(base_url="https://custom.openai.com/v1")
            health_status = await provider.health_check()
            assert health_status is True
