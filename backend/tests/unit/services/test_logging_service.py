"""
Tests for application logging service.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timedelta

from app.services.logging_service import (
    ApplicationLoggingService, log_authentication, log_agent_operation,
    log_test_execution, log_system_error
)
from app.models.application_log import (
    ApplicationLog, LogLevel, EventType, ApplicationLogResponse,
    ApplicationLogDetailResponse, ApplicationLogListResponse, LogFilterParams
)


@pytest.fixture
def mock_db():
    """Mock database session."""
    db = AsyncMock()
    db.add = MagicMock()
    db.commit = AsyncMock()
    db.refresh = AsyncMock()
    db.execute = AsyncMock()
    db.close = AsyncMock()
    return db


@pytest.fixture
def logging_service(mock_db):
    """Create logging service with mock database."""
    return ApplicationLoggingService(mock_db)


class TestApplicationLoggingService:
    """Test ApplicationLoggingService class."""

    @pytest.mark.asyncio
    async def test_log_event_basic(self, logging_service, mock_db):
        """Test basic log event creation."""
        # Mock the log entry creation
        mock_log = ApplicationLog(
            id=1,
            uuid="test-uuid",
            level=LogLevel.INFO,
            event_type=EventType.USER_LOGIN,
            message="Test message",
            user_id=1,
            timestamp=datetime.utcnow(),
            created_at=datetime.utcnow()
        )
        mock_db.refresh.side_effect = lambda obj: setattr(obj, 'id', 1)

        result = await logging_service.log_event(
            level=LogLevel.INFO,
            event_type=EventType.USER_LOGIN,
            message="Test message",
            user_id=1
        )

        # Verify database operations
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()

        # Verify the log entry was created with correct data
        added_log = mock_db.add.call_args[0][0]
        assert added_log.level == LogLevel.INFO
        assert added_log.event_type == EventType.USER_LOGIN
        assert added_log.message == "Test message"
        assert added_log.user_id == 1

    @pytest.mark.asyncio
    async def test_log_event_with_metadata(self, logging_service, mock_db):
        """Test log event with metadata and additional fields."""
        metadata = {"key": "value", "count": 42}
        tags = ["test", "metadata"]

        await logging_service.log_event(
            level=LogLevel.WARNING,
            event_type=EventType.AGENT_EXECUTE,
            message="Agent execution warning",
            user_id=2,
            agent_id="agent_123",
            execution_time_ms=1500.0,
            metadata=metadata,
            tags=tags,
            ip_address="***********",
            error_code="timeout"
        )

        # Verify the log entry was created with all fields
        added_log = mock_db.add.call_args[0][0]
        assert added_log.level == LogLevel.WARNING
        assert added_log.event_type == EventType.AGENT_EXECUTE
        assert added_log.user_id == 2
        assert added_log.agent_id == "agent_123"
        assert added_log.execution_time_ms == 1500.0
        assert added_log.metadata == metadata
        assert added_log.tags == tags
        assert added_log.ip_address == "***********"
        assert added_log.error_code == "timeout"

    @pytest.mark.asyncio
    async def test_log_authentication_event_success(self, logging_service, mock_db):
        """Test logging successful authentication event."""
        await logging_service.log_authentication_event(
            event_type=EventType.USER_LOGIN,
            user_id=1,
            success=True,
            message="User logged in successfully",
            ip_address="********",
            user_agent="Mozilla/5.0",
            session_id="session_123",
            metadata={"email": "<EMAIL>"}
        )

        added_log = mock_db.add.call_args[0][0]
        assert added_log.level == LogLevel.INFO  # Success should be INFO level
        assert added_log.event_type == EventType.USER_LOGIN
        assert added_log.user_id == 1
        assert added_log.ip_address == "********"
        assert added_log.user_agent == "Mozilla/5.0"
        assert added_log.session_id == "session_123"
        assert added_log.metadata["success"] is True
        assert added_log.metadata["email"] == "<EMAIL>"
        assert "authentication" in added_log.tags

    @pytest.mark.asyncio
    async def test_log_authentication_event_failure(self, logging_service, mock_db):
        """Test logging failed authentication event."""
        await logging_service.log_authentication_event(
            event_type=EventType.USER_LOGIN,
            user_id=None,
            success=False,
            message="Login failed",
            ip_address="********",
            metadata={"reason": "invalid_password"}
        )

        added_log = mock_db.add.call_args[0][0]
        assert added_log.level == LogLevel.WARNING  # Failure should be WARNING level
        assert added_log.user_id is None
        assert added_log.metadata["success"] is False
        assert added_log.metadata["reason"] == "invalid_password"

    @pytest.mark.asyncio
    async def test_log_agent_event(self, logging_service, mock_db):
        """Test logging agent-related events."""
        await logging_service.log_agent_event(
            event_type=EventType.AGENT_CREATE,
            agent_id="agent_456",
            user_id=2,
            message="Agent created successfully",
            execution_time_ms=2000.0,
            metadata={"team_size": 3, "agent_type": "multi_agent"}
        )

        added_log = mock_db.add.call_args[0][0]
        assert added_log.level == LogLevel.INFO
        assert added_log.event_type == EventType.AGENT_CREATE
        assert added_log.agent_id == "agent_456"
        assert added_log.user_id == 2
        assert added_log.execution_time_ms == 2000.0
        assert added_log.metadata["team_size"] == 3
        assert "agent" in added_log.tags

    @pytest.mark.asyncio
    async def test_log_agent_event_with_error(self, logging_service, mock_db):
        """Test logging agent event with error."""
        await logging_service.log_agent_event(
            event_type=EventType.AGENT_EXECUTE,
            agent_id="agent_789",
            user_id=3,
            message="Agent execution failed",
            error_code="execution_error",
            error_type="RuntimeError",
            stack_trace="Traceback..."
        )

        added_log = mock_db.add.call_args[0][0]
        assert added_log.level == LogLevel.ERROR  # Should be ERROR level when error_code is present
        assert added_log.error_code == "execution_error"
        assert added_log.error_type == "RuntimeError"
        assert added_log.stack_trace == "Traceback..."

    @pytest.mark.asyncio
    async def test_log_test_event(self, logging_service, mock_db):
        """Test logging test execution events."""
        await logging_service.log_test_event(
            event_type=EventType.TEST_START,
            test_id="test_123",
            agent_id="agent_abc",
            user_id=4,
            message="Test execution started",
            metadata={"input_length": 100, "timeout": 30}
        )

        added_log = mock_db.add.call_args[0][0]
        assert added_log.event_type == EventType.TEST_START
        assert added_log.test_id == "test_123"
        assert added_log.agent_id == "agent_abc"
        assert added_log.user_id == 4
        assert added_log.metadata["input_length"] == 100
        assert "test" in added_log.tags

    @pytest.mark.asyncio
    async def test_log_api_key_event(self, logging_service, mock_db):
        """Test logging API key management events."""
        await logging_service.log_api_key_event(
            event_type=EventType.API_KEY_CREATE,
            api_key_id=5,
            user_id=6,
            message="API key created",
            metadata={"key_name": "OpenAI Key", "provider": "openai"}
        )

        added_log = mock_db.add.call_args[0][0]
        assert added_log.level == LogLevel.INFO
        assert added_log.event_type == EventType.API_KEY_CREATE
        assert added_log.api_key_id == 5
        assert added_log.user_id == 6
        assert added_log.metadata["key_name"] == "OpenAI Key"
        assert "api_key" in added_log.tags

    @pytest.mark.asyncio
    async def test_log_system_event(self, logging_service, mock_db):
        """Test logging system-level events."""
        await logging_service.log_system_event(
            event_type=EventType.SYSTEM_STARTUP,
            message="System started successfully",
            level=LogLevel.INFO,
            metadata={"version": "1.0.0", "environment": "production"}
        )

        added_log = mock_db.add.call_args[0][0]
        assert added_log.level == LogLevel.INFO
        assert added_log.event_type == EventType.SYSTEM_STARTUP
        assert added_log.user_id is None  # System events don't have user_id
        assert added_log.metadata["version"] == "1.0.0"
        assert "system" in added_log.tags

    @pytest.mark.asyncio
    async def test_log_performance_event(self, logging_service, mock_db):
        """Test logging performance metrics."""
        await logging_service.log_performance_event(
            message="High execution time detected",
            execution_time_ms=5000.0,
            memory_usage_mb=256.5,
            cpu_usage_percent=85.2,
            user_id=7,
            agent_id="agent_slow"
        )

        added_log = mock_db.add.call_args[0][0]
        assert added_log.level == LogLevel.INFO
        assert added_log.event_type == EventType.PERFORMANCE_METRIC
        assert added_log.execution_time_ms == 5000.0
        assert added_log.memory_usage_mb == 256.5
        assert added_log.cpu_usage_percent == 85.2
        assert added_log.user_id == 7
        assert added_log.agent_id == "agent_slow"
        assert "performance" in added_log.tags

    @pytest.mark.asyncio
    async def test_log_error(self, logging_service, mock_db):
        """Test logging error events with exception details."""
        test_exception = ValueError("Test error message")
        
        with patch('traceback.format_exc', return_value="Mocked traceback"):
            await logging_service.log_error(
                message="An error occurred",
                error=test_exception,
                user_id=8,
                agent_id="agent_error",
                metadata={"context": "test_context"}
            )

        added_log = mock_db.add.call_args[0][0]
        assert added_log.level == LogLevel.ERROR
        assert added_log.event_type == EventType.SYSTEM_ERROR
        assert added_log.user_id == 8
        assert added_log.agent_id == "agent_error"
        assert added_log.error_type == "ValueError"
        assert added_log.stack_trace == "Mocked traceback"
        assert added_log.metadata["exception_type"] == "ValueError"
        assert added_log.metadata["exception_message"] == "Test error message"
        assert added_log.metadata["context"] == "test_context"
        assert "error" in added_log.tags

    @pytest.mark.asyncio
    async def test_context_manager_with_db(self, mock_db):
        """Test using logging service as context manager with existing db."""
        service = ApplicationLoggingService(mock_db)
        
        async with service as s:
            assert s.db == mock_db
            await s.log_event(
                level=LogLevel.INFO,
                event_type=EventType.USER_LOGIN,
                message="Test message"
            )
        
        # Should not close the provided db
        mock_db.close.assert_not_called()

    @pytest.mark.asyncio
    async def test_context_manager_without_db(self):
        """Test using logging service as context manager without db."""
        service = ApplicationLoggingService()
        
        with patch('app.services.logging_service.get_db_session') as mock_get_db:
            mock_db = AsyncMock()
            mock_get_db.return_value = mock_db
            
            async with service as s:
                assert s.db == mock_db
            
            # Should close the created db
            mock_db.close.assert_called_once()


class TestConvenienceFunctions:
    """Test convenience functions for logging."""

    @pytest.mark.asyncio
    async def test_log_authentication_function(self):
        """Test log_authentication convenience function."""
        with patch('app.services.logging_service.logging_service') as mock_service:
            mock_service.__aenter__ = AsyncMock(return_value=mock_service)
            mock_service.__aexit__ = AsyncMock(return_value=None)
            mock_service.log_authentication_event = AsyncMock()

            await log_authentication(
                event_type=EventType.USER_LOGIN,
                user_id=1,
                success=True,
                message="Login successful"
            )

            mock_service.log_authentication_event.assert_called_once_with(
                EventType.USER_LOGIN, 1, True, "Login successful", None, None, None, None
            )

    @pytest.mark.asyncio
    async def test_log_agent_operation_function(self):
        """Test log_agent_operation convenience function."""
        with patch('app.services.logging_service.logging_service') as mock_service:
            mock_service.__aenter__ = AsyncMock(return_value=mock_service)
            mock_service.__aexit__ = AsyncMock(return_value=None)
            mock_service.log_agent_event = AsyncMock()

            await log_agent_operation(
                event_type=EventType.AGENT_CREATE,
                agent_id="agent_123",
                user_id=2,
                message="Agent created"
            )

            mock_service.log_agent_event.assert_called_once()

    @pytest.mark.asyncio
    async def test_log_test_execution_function(self):
        """Test log_test_execution convenience function."""
        with patch('app.services.logging_service.logging_service') as mock_service:
            mock_service.__aenter__ = AsyncMock(return_value=mock_service)
            mock_service.__aexit__ = AsyncMock(return_value=None)
            mock_service.log_test_event = AsyncMock()

            await log_test_execution(
                event_type=EventType.TEST_START,
                test_id="test_456",
                agent_id="agent_789",
                user_id=3,
                message="Test started"
            )

            mock_service.log_test_event.assert_called_once()

    @pytest.mark.asyncio
    async def test_log_system_error_function(self):
        """Test log_system_error convenience function."""
        with patch('app.services.logging_service.logging_service') as mock_service:
            mock_service.__aenter__ = AsyncMock(return_value=mock_service)
            mock_service.__aexit__ = AsyncMock(return_value=None)
            mock_service.log_error = AsyncMock()

            test_error = RuntimeError("Test error")
            await log_system_error(
                message="System error occurred",
                error=test_error,
                user_id=4
            )

            mock_service.log_error.assert_called_once_with(
                "System error occurred", test_error, 4, None, None, None, None
            )


class TestLoggingServiceQueries:
    """Test logging service query functionality."""

    @pytest.mark.asyncio
    async def test_get_logs_basic(self, logging_service, mock_db):
        """Test basic log retrieval."""
        # Mock database response
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [
            ApplicationLog(
                id=1, uuid="uuid1", level=LogLevel.INFO, event_type=EventType.USER_LOGIN,
                message="Test log 1", user_id=1, timestamp=datetime.utcnow(),
                created_at=datetime.utcnow()
            ),
            ApplicationLog(
                id=2, uuid="uuid2", level=LogLevel.ERROR, event_type=EventType.SYSTEM_ERROR,
                message="Test log 2", user_id=1, timestamp=datetime.utcnow(),
                created_at=datetime.utcnow()
            )
        ]

        mock_count_result = MagicMock()
        mock_count_result.scalar.return_value = 2

        mock_db.execute.side_effect = [mock_count_result, mock_result]

        filters = LogFilterParams()
        result = await logging_service.get_logs(filters, page=1, limit=10, user_id=1)

        assert isinstance(result, ApplicationLogListResponse)
        assert len(result.logs) == 2
        assert result.total == 2
        assert result.page == 1
        assert result.limit == 10
        assert result.has_next is False
        assert result.has_prev is False

    @pytest.mark.asyncio
    async def test_get_logs_with_filters(self, logging_service, mock_db):
        """Test log retrieval with filters."""
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = []
        mock_count_result = MagicMock()
        mock_count_result.scalar.return_value = 0
        mock_db.execute.side_effect = [mock_count_result, mock_result]

        filters = LogFilterParams(
            level=LogLevel.ERROR,
            event_type=EventType.SYSTEM_ERROR,
            agent_id="agent_123",
            search_query="error message",
            start_date=datetime(2023, 1, 1),
            end_date=datetime(2023, 12, 31)
        )

        result = await logging_service.get_logs(filters, page=1, limit=10, user_id=1)

        # Verify that execute was called twice (count + data query)
        assert mock_db.execute.call_count == 2

    @pytest.mark.asyncio
    async def test_get_logs_pagination(self, logging_service, mock_db):
        """Test log retrieval with pagination."""
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = []
        mock_count_result = MagicMock()
        mock_count_result.scalar.return_value = 100
        mock_db.execute.side_effect = [mock_count_result, mock_result]

        filters = LogFilterParams()
        result = await logging_service.get_logs(filters, page=2, limit=20, user_id=1)

        assert result.total == 100
        assert result.page == 2
        assert result.limit == 20
        assert result.has_next is True  # (2 * 20) < 100
        assert result.has_prev is True  # page > 1

    @pytest.mark.asyncio
    async def test_get_log_detail(self, logging_service, mock_db):
        """Test retrieving detailed log information."""
        mock_log = ApplicationLog(
            id=1, uuid="uuid1", level=LogLevel.INFO, event_type=EventType.USER_LOGIN,
            message="Detailed log", user_id=1, source_file="/app/test.py",
            source_line=42, request_method="POST", request_path="/api/login",
            metadata={"key": "value"}, tags=["auth"], timestamp=datetime.utcnow(),
            created_at=datetime.utcnow()
        )

        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = mock_log
        mock_db.execute.return_value = mock_result

        result = await logging_service.get_log_detail(log_id=1, user_id=1)

        assert isinstance(result, ApplicationLogDetailResponse)
        assert result.id == 1
        assert result.message == "Detailed log"
        assert result.source_file == "/app/test.py"
        assert result.source_line == 42
        assert result.request_method == "POST"
        assert result.metadata == {"key": "value"}
        assert result.tags == ["auth"]

    @pytest.mark.asyncio
    async def test_get_log_detail_not_found(self, logging_service, mock_db):
        """Test retrieving non-existent log detail."""
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_db.execute.return_value = mock_result

        result = await logging_service.get_log_detail(log_id=999, user_id=1)

        assert result is None

    @pytest.mark.asyncio
    async def test_user_isolation(self, logging_service, mock_db):
        """Test that users can only see their own logs."""
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = []
        mock_count_result = MagicMock()
        mock_count_result.scalar.return_value = 0
        mock_db.execute.side_effect = [mock_count_result, mock_result]

        filters = LogFilterParams()
        await logging_service.get_logs(filters, page=1, limit=10, user_id=123)

        # Verify that user_id filter was applied in the query
        # This is a simplified check - in a real test, you'd inspect the SQL query
        assert mock_db.execute.call_count == 2
