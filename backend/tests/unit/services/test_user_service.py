"""
Unit tests for UserService.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Async<PERSON>ock, MagicMock, patch
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.user_service import UserService
from app.models.user import (
    User, UserSession, UserToken, LoginHistory,
    UserRole, UserStatus, SessionStatus, TokenType,
    UserRegister, UserLogin, ChangePassword
)
from app.core.security import verify_password, get_password_hash
from tests.fixtures.factories import (
    UserFactory, UserSessionFactory, UserTokenFactory, 
    LoginHistoryFactory, AuthTestDataFactory
)


class TestUserServiceRegistration:
    """Test user registration functionality."""
    
    @pytest.mark.asyncio
    async def test_create_user_success(self):
        """Test successful user creation."""
        # Mock database session
        mock_db = AsyncMock(spec=AsyncSession)
        mock_db.executeute = AsyncMock()
        mock_db.commit = AsyncMock()
        mock_db.refresh = AsyncMock()

        # Mock query result (no existing user)
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_db.executeute.return_value = mock_result
        
        # Create service and test data
        service = UserService(mock_db)
        registration_data = UserRegister(
            name="John Doe",
            email="<EMAIL>",
            password="password123",
            confirm_password="password123"
        )
        
        # Mock the created user
        created_user = UserFactory.create_user(
            name="John Doe",
            email="<EMAIL>"
        )
        created_user.id = 1
        
        # Mock database operations
        def mock_add(user):
            user.id = 1
        
        mock_db.add = mock_add
        mock_db.refresh.side_effect = lambda user: setattr(user, 'id', 1)
        
        # Test user creation
        with patch('app.services.user_service.create_access_token') as mock_token:
            mock_token.return_value = "test_token_123"
            
            user, token = await service.create_user(registration_data)
            
            assert user.name == "John Doe"
            assert user.email == "<EMAIL>"
            assert user.status == UserStatus.PENDING_VERIFICATION
            assert verify_password("password123", user.password_hash)
            assert token == "test_token_123"
            
            # Verify database operations
            mock_db.commit.assert_called()
    
    @pytest.mark.asyncio
    async def test_create_user_duplicate_email(self):
        """Test user creation with duplicate email."""
        mock_db = AsyncMock(spec=AsyncSession)
        
        # Mock existing user
        existing_user = UserFactory.create_user(email="<EMAIL>")
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = existing_user
        mock_db.executeute.return_value = mock_result
        
        service = UserService(mock_db)
        registration_data = UserRegister(
            name="John Doe",
            email="<EMAIL>",
            password="password123",
            confirm_password="password123"
        )
        
        # Test duplicate email error
        with pytest.raises(ValueError, match="User already exists with this email"):
            await service.create_user(registration_data)
    
    @pytest.mark.asyncio
    async def test_create_user_password_mismatch(self):
        """Test user creation with password mismatch."""
        mock_db = AsyncMock(spec=AsyncSession)
        service = UserService(mock_db)
        
        registration_data = UserRegister(
            name="John Doe",
            email="<EMAIL>",
            password="password123",
            confirm_password="different_password"
        )
        
        # Test password mismatch error
        with pytest.raises(ValueError, match="Passwords do not match"):
            await service.create_user(registration_data)


class TestUserServiceAuthentication:
    """Test user authentication functionality."""
    
    @pytest.mark.asyncio
    async def test_authenticate_user_success(self):
        """Test successful user authentication."""
        mock_db = AsyncMock(spec=AsyncSession)
        
        # Create test user
        user = UserFactory.create_user(
            email="<EMAIL>",
            password="password123",
            status=UserStatus.ACTIVE
        )
        user.id = 1
        
        # Mock database query
        mock_result = MagicMock()
        mock_result.first.return_value = user
        mock_db.execute.return_value = mock_result
        mock_db.commit = AsyncMock()
        mock_db.refresh = AsyncMock()
        
        service = UserService(mock_db)
        login_data = UserLogin(
            email="<EMAIL>",
            password="password123",
            remember_me=False
        )
        
        with patch('app.services.user_service.create_access_token') as mock_token:
            mock_token.return_value = "test_token_123"
            
            authenticated_user, token = await service.authenticate_user(login_data)
            
            assert authenticated_user is not None
            assert authenticated_user.email == "<EMAIL>"
            assert token == "test_token_123"
            assert authenticated_user.failed_login_attempts == 0
            assert authenticated_user.locked_until is None
            assert authenticated_user.login_count == 1
    
    @pytest.mark.asyncio
    async def test_authenticate_user_not_found(self):
        """Test authentication with non-existent user."""
        mock_db = AsyncMock(spec=AsyncSession)
        
        # Mock no user found
        mock_result = MagicMock()
        mock_result.first.return_value = None
        mock_db.execute.return_value = mock_result
        
        service = UserService(mock_db)
        login_data = UserLogin(
            email="<EMAIL>",
            password="password123"
        )
        
        user, token = await service.authenticate_user(login_data)
        
        assert user is None
        assert token is None
    
    @pytest.mark.asyncio
    async def test_authenticate_user_wrong_password(self):
        """Test authentication with wrong password."""
        mock_db = AsyncMock(spec=AsyncSession)
        
        # Create test user
        user = UserFactory.create_user(
            email="<EMAIL>",
            password="correct_password"
        )
        user.id = 1
        
        # Mock database query
        mock_result = MagicMock()
        mock_result.first.return_value = user
        mock_db.execute.return_value = mock_result
        mock_db.commit = AsyncMock()
        
        service = UserService(mock_db)
        login_data = UserLogin(
            email="<EMAIL>",
            password="wrong_password"
        )
        
        authenticated_user, token = await service.authenticate_user(login_data)
        
        assert authenticated_user is None
        assert token is None
        assert user.failed_login_attempts == 1
    
    @pytest.mark.asyncio
    async def test_authenticate_user_account_locked(self):
        """Test authentication with locked account."""
        mock_db = AsyncMock(spec=AsyncSession)
        
        # Create locked user
        user = UserFactory.create_user(
            email="<EMAIL>",
            password="password123",
            locked_until=datetime.now() + timedelta(minutes=30)
        )
        
        # Mock database query
        mock_result = MagicMock()
        mock_result.first.return_value = user
        mock_db.execute.return_value = mock_result
        
        service = UserService(mock_db)
        login_data = UserLogin(
            email="<EMAIL>",
            password="password123"
        )
        
        with pytest.raises(ValueError, match="Account is temporarily locked"):
            await service.authenticate_user(login_data)
    
    @pytest.mark.asyncio
    async def test_authenticate_user_inactive_status(self):
        """Test authentication with inactive user."""
        mock_db = AsyncMock(spec=AsyncSession)
        
        # Create inactive user
        user = UserFactory.create_user(
            email="<EMAIL>",
            password="password123",
            status=UserStatus.SUSPENDED
        )
        
        # Mock database query
        mock_result = MagicMock()
        mock_result.first.return_value = user
        mock_db.execute.return_value = mock_result
        
        service = UserService(mock_db)
        login_data = UserLogin(
            email="<EMAIL>",
            password="password123"
        )
        
        with pytest.raises(ValueError, match="Account is suspended"):
            await service.authenticate_user(login_data)


class TestUserServicePasswordManagement:
    """Test password management functionality."""
    
    @pytest.mark.asyncio
    async def test_change_password_success(self):
        """Test successful password change."""
        mock_db = AsyncMock(spec=AsyncSession)
        mock_db.commit = AsyncMock()
        
        # Create test user
        user = UserFactory.create_user(password="old_password")
        user.id = 1
        
        service = UserService(mock_db)
        password_data = ChangePassword(
            current_password="old_password",
            new_password="new_password123",
            confirm_password="new_password123"
        )
        
        with patch.object(service, '_invalidate_user_sessions') as mock_invalidate:
            result = await service.change_password(user, password_data)
            
            assert result is True
            assert verify_password("new_password123", user.password_hash)
            assert user.password_changed_at is not None
            mock_invalidate.assert_called_once()
            mock_db.commit.assert_called()
    
    @pytest.mark.asyncio
    async def test_change_password_wrong_current(self):
        """Test password change with wrong current password."""
        mock_db = AsyncMock(spec=AsyncSession)
        
        user = UserFactory.create_user(password="correct_password")
        service = UserService(mock_db)
        
        password_data = ChangePassword(
            current_password="wrong_password",
            new_password="new_password123",
            confirm_password="new_password123"
        )
        
        with pytest.raises(ValueError, match="Current password is incorrect"):
            await service.change_password(user, password_data)
    
    @pytest.mark.asyncio
    async def test_change_password_mismatch(self):
        """Test password change with new password mismatch."""
        mock_db = AsyncMock(spec=AsyncSession)
        
        user = UserFactory.create_user(password="old_password")
        service = UserService(mock_db)
        
        password_data = ChangePassword(
            current_password="old_password",
            new_password="new_password123",
            confirm_password="different_password"
        )
        
        with pytest.raises(ValueError, match="New passwords do not match"):
            await service.change_password(user, password_data)
