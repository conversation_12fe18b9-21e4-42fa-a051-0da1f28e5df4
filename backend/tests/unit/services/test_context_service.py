"""
Unit tests for context management service.
"""

import pytest
import uuid
from datetime import datetime
from unittest.mock import Mock, AsyncMock

from app.services.context_service import ContextService, get_context_service
from app.models.context import (
    RuntimeContext,
    ContextManager,
    ContextPlaceholder,
    ContextAwareTeamMember,
    ContextAwareWorkflowStep
)


class TestContextService:
    """Test cases for ContextService."""

    @pytest.fixture
    def context_service(self):
        """Create a context service instance for testing."""
        return ContextService()

    @pytest.fixture
    def sample_execution_id(self):
        """Generate a sample execution ID."""
        return f"exec_{uuid.uuid4().hex[:12]}"

    @pytest.fixture
    def sample_context_placeholder(self):
        """Create a sample context placeholder with semantic naming."""
        return ContextPlaceholder(
            placeholder_name="{data_analyst.insights}",
            source_agent_role="data_analyst",
            semantic_description="Analysis insights and findings from the data analyst",
            source_step="analysis_step",
            description="Previous analysis results"  # Legacy field for compatibility
        )

    @pytest.fixture
    def sample_team_member(self, sample_context_placeholder):
        """Create a sample context-aware team member."""
        return ContextAwareTeamMember(
            name="Data Analyst",
            role="analyst",
            description="Analyzes data and provides insights",
            system_prompt="You are a data analyst. Use {data_analyst.insights} to inform your work.",
            capabilities=["data_analysis", "visualization"],
            tools=["python", "sql"],
            context_placeholders=[sample_context_placeholder]
        )

    @pytest.fixture
    def sample_workflow_step(self):
        """Create a sample context-aware workflow step."""
        return ContextAwareWorkflowStep(
            name="Data Analysis",
            description="Analyze the provided data",
            assignee="Data Analyst",
            inputs=["raw_data"],
            outputs=["analysis_results"],
            context_dependencies=["data_collection"]
        )

    def test_create_execution_context(self, context_service, sample_execution_id):
        """Test creating a new execution context."""
        context_manager = context_service.create_execution_context(sample_execution_id)
        
        assert isinstance(context_manager, ContextManager)
        assert context_manager.execution_id == sample_execution_id
        assert len(context_manager.contexts) == 0
        assert sample_execution_id in context_service.active_contexts

    def test_get_execution_context(self, context_service, sample_execution_id):
        """Test retrieving an existing execution context."""
        # Create context first
        original_context = context_service.create_execution_context(sample_execution_id)
        
        # Retrieve it
        retrieved_context = context_service.get_execution_context(sample_execution_id)
        
        assert retrieved_context is original_context
        assert retrieved_context.execution_id == sample_execution_id

    def test_get_nonexistent_execution_context(self, context_service):
        """Test retrieving a non-existent execution context."""
        result = context_service.get_execution_context("nonexistent_id")
        assert result is None

    def test_add_step_context(self, context_service, sample_execution_id):
        """Test adding context data for a step."""
        context_service.create_execution_context(sample_execution_id)
        
        context_data = {
            "input": "test input",
            "output": "test output",
            "metadata": {"tokens": 100}
        }
        
        runtime_context = context_service.add_step_context(
            execution_id=sample_execution_id,
            step_name="test_step",
            member_name="test_member",
            context_data=context_data,
            input_tokens=50,
            output_tokens=50,
            processing_time_ms=1000
        )
        
        assert isinstance(runtime_context, RuntimeContext)
        assert runtime_context.execution_id == sample_execution_id
        assert runtime_context.step_name == "test_step"
        assert runtime_context.member_name == "test_member"
        assert runtime_context.context_data == context_data
        assert runtime_context.input_tokens == 50
        assert runtime_context.output_tokens == 50
        assert runtime_context.processing_time_ms == 1000

    def test_add_step_context_creates_manager_if_missing(self, context_service):
        """Test that adding step context creates a context manager if it doesn't exist."""
        execution_id = "new_execution_id"
        
        # Ensure no context manager exists
        assert context_service.get_execution_context(execution_id) is None
        
        # Add step context
        context_service.add_step_context(
            execution_id=execution_id,
            step_name="test_step",
            member_name="test_member",
            context_data={"test": "data"}
        )
        
        # Verify context manager was created
        context_manager = context_service.get_execution_context(execution_id)
        assert context_manager is not None
        assert "test_step" in context_manager.contexts

    def test_resolve_member_prompt_with_placeholders(self, context_service, sample_execution_id, sample_team_member):
        """Test resolving placeholders in a team member's prompt."""
        # Create context and add some data
        context_service.create_execution_context(sample_execution_id)
        context_service.add_step_context(
            execution_id=sample_execution_id,
            step_name="analysis_step",
            member_name="previous_member",
            context_data={"output": "Previous analysis shows positive trends"}
        )
        
        # Resolve the prompt
        resolved_prompt = context_service.resolve_member_prompt(
            execution_id=sample_execution_id,
            member=sample_team_member,
            user_input="Analyze the data"
        )
        
        assert "Previous analysis shows positive trends" in resolved_prompt
        assert "{previous_analysis}" not in resolved_prompt

    def test_resolve_member_prompt_without_context(self, context_service, sample_execution_id, sample_team_member):
        """Test resolving prompts when no context is available."""
        # Create context manager but don't add the required step context
        context_service.create_execution_context(sample_execution_id)

        resolved_prompt = context_service.resolve_member_prompt(
            execution_id=sample_execution_id,
            member=sample_team_member,
            user_input="Analyze the data"
        )

        # Should contain semantic placeholder replacement message
        assert "[Analysis insights and findings from the data analyst - no data available from data_analyst]" in resolved_prompt

    def test_get_step_dependencies_context(self, context_service, sample_execution_id, sample_workflow_step):
        """Test getting context from dependency steps."""
        # Create context and add dependency data
        context_service.create_execution_context(sample_execution_id)
        context_service.add_step_context(
            execution_id=sample_execution_id,
            step_name="data_collection",
            member_name="collector",
            context_data={"output": "Collected 1000 records", "status": "complete"}
        )
        
        # Get dependencies context
        dependencies = context_service.get_step_dependencies_context(
            execution_id=sample_execution_id,
            step=sample_workflow_step
        )
        
        assert "data_collection" in dependencies
        assert dependencies["data_collection"]["output"] == "Collected 1000 records"
        assert dependencies["data_collection"]["status"] == "complete"

    def test_build_step_prompt(self, context_service, sample_execution_id, sample_workflow_step, sample_team_member):
        """Test building a complete step prompt with context."""
        # Create context and add dependency data
        context_service.create_execution_context(sample_execution_id)
        context_service.add_step_context(
            execution_id=sample_execution_id,
            step_name="data_collection",
            member_name="collector",
            context_data={"output": "Collected 1000 records"}
        )
        context_service.add_step_context(
            execution_id=sample_execution_id,
            step_name="analysis_step",
            member_name="previous_analyst",
            context_data={"output": "Initial analysis complete"}
        )
        
        # Build step prompt
        step_prompt = context_service.build_step_prompt(
            execution_id=sample_execution_id,
            step=sample_workflow_step,
            member=sample_team_member,
            user_input="Please analyze the collected data"
        )
        
        assert "Data Analysis" in step_prompt
        assert "Analyze the provided data" in step_prompt
        assert "Data Analyst" in step_prompt
        assert "Please analyze the collected data" in step_prompt
        assert "Collected 1000 records" in step_prompt
        assert "Initial analysis complete" in step_prompt

    def test_cleanup_execution_context(self, context_service, sample_execution_id):
        """Test cleaning up execution context."""
        # Create context
        context_service.create_execution_context(sample_execution_id)
        assert sample_execution_id in context_service.active_contexts
        
        # Clean up
        context_service.cleanup_execution_context(sample_execution_id)
        assert sample_execution_id not in context_service.active_contexts

    def test_get_execution_summary(self, context_service, sample_execution_id):
        """Test getting execution summary."""
        # Create context and add some steps
        context_service.create_execution_context(sample_execution_id)
        context_service.add_step_context(
            execution_id=sample_execution_id,
            step_name="step1",
            member_name="member1",
            context_data={"output": "result1"},
            input_tokens=50,
            output_tokens=75,
            processing_time_ms=1000
        )
        context_service.add_step_context(
            execution_id=sample_execution_id,
            step_name="step2",
            member_name="member2",
            context_data={"output": "result2"},
            input_tokens=60,
            output_tokens=80,
            processing_time_ms=1200
        )
        
        # Get summary
        summary = context_service.get_execution_summary(sample_execution_id)
        
        assert summary["execution_id"] == sample_execution_id
        assert summary["total_steps"] == 2
        assert len(summary["steps"]) == 2
        
        step1_summary = next(s for s in summary["steps"] if s["step_name"] == "step1")
        assert step1_summary["member_name"] == "member1"
        assert step1_summary["input_tokens"] == 50
        assert step1_summary["output_tokens"] == 75
        assert step1_summary["processing_time_ms"] == 1000
        assert step1_summary["has_output"] is True

    def test_export_execution_context(self, context_service, sample_execution_id):
        """Test exporting execution context."""
        # Create context and add data
        context_service.create_execution_context(sample_execution_id)
        context_service.add_step_context(
            execution_id=sample_execution_id,
            step_name="test_step",
            member_name="test_member",
            context_data={"output": "test_output"}
        )
        
        # Export context
        export_data = context_service.export_execution_context(sample_execution_id)
        
        assert export_data is not None
        assert export_data["execution_id"] == sample_execution_id
        assert "contexts" in export_data
        assert "test_step" in export_data["contexts"]
        assert export_data["contexts"]["test_step"]["context_data"]["output"] == "test_output"
        assert "exported_at" in export_data

    def test_import_execution_context(self, context_service):
        """Test importing execution context."""
        # Create export data
        execution_id = "imported_execution"
        export_data = {
            "execution_id": execution_id,
            "contexts": {
                "step1": {
                    "execution_id": execution_id,
                    "step_name": "step1",
                    "member_name": "member1",
                    "context_data": {"output": "imported_output"},
                    "timestamp": datetime.utcnow().isoformat(),
                    "input_tokens": 50,
                    "output_tokens": 75,
                    "processing_time_ms": 1000
                }
            },
            "placeholder_mappings": {},
            "exported_at": datetime.utcnow().isoformat()
        }
        
        # Import context
        context_manager = context_service.import_execution_context(export_data)
        
        assert context_manager is not None
        assert context_manager.execution_id == execution_id
        assert "step1" in context_manager.contexts
        assert context_manager.contexts["step1"].context_data["output"] == "imported_output"
        assert execution_id in context_service.active_contexts


class TestGlobalContextService:
    """Test cases for global context service functions."""

    def test_get_context_service_singleton(self):
        """Test that get_context_service returns the same instance."""
        service1 = get_context_service()
        service2 = get_context_service()
        
        assert service1 is service2
        assert isinstance(service1, ContextService)

    def test_get_context_service_with_db(self):
        """Test getting context service with database session."""
        mock_db = Mock()
        # Create a new service instance directly since the global one is a singleton
        service = ContextService(mock_db)

        assert isinstance(service, ContextService)
        assert service.db is mock_db
