"""
Unit tests for AI Providers.
"""

import pytest
from unittest.mock import As<PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch
import json

from app.services.ai_providers import (
    OpenAIProvider,
    Anthropic<PERSON><PERSON>ider,
    AIProviderManager,
    create_ai_provider
)
from app.core.exceptions import AIProviderError
from app.core.config import settings
from tests.fixtures.factories import MockResponseFactory


class TestOpenAIProvider:
    """Test OpenAI provider."""
    
    @pytest.fixture
    def mock_openai_client(self):
        """Create mock OpenAI client."""
        client = Mock()
        client.chat.completions.create = AsyncMock()
        return client
    
    @pytest.fixture
    def openai_provider(self, mock_openai_client):
        """Create OpenAI provider with mock client."""
        with patch('app.services.ai_providers.AsyncOpenAI', return_value=mock_openai_client):
            provider = OpenAIProvider()
            provider.client = mock_openai_client
            return provider
    
    @pytest.mark.asyncio
    async def test_generate_text_success(self, openai_provider, mock_openai_client):
        """Test successful text generation."""
        # Setup
        expected_response = "Generated text response"
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = expected_response
        mock_openai_client.chat.completions.create.return_value = mock_response
        
        # Execute
        result = await openai_provider.generate_text(
            prompt="Test prompt",
            system_prompt="Test system prompt"
        )
        
        # Verify
        assert result == expected_response
        mock_openai_client.chat.completions.create.assert_called_once()
        
        # Verify call arguments
        call_args = mock_openai_client.chat.completions.create.call_args
        assert call_args.kwargs["model"] == "gpt-4"
        assert len(call_args.kwargs["messages"]) == 2  # system + user
    
    @pytest.mark.asyncio
    async def test_generate_text_with_custom_params(self, openai_provider, mock_openai_client):
        """Test text generation with custom parameters."""
        # Setup
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "Response"
        mock_openai_client.chat.completions.create.return_value = mock_response
        
        # Execute
        await openai_provider.generate_text(
            prompt="Test prompt",
            temperature=0.5,
            max_tokens=1000,
            model="gpt-3.5-turbo"
        )
        
        # Verify parameters
        call_args = mock_openai_client.chat.completions.create.call_args
        assert call_args.kwargs["temperature"] == 0.5
        assert call_args.kwargs["max_tokens"] == 1000
        assert call_args.kwargs["model"] == "gpt-3.5-turbo"
    
    @pytest.mark.asyncio
    async def test_generate_text_error(self, openai_provider, mock_openai_client):
        """Test text generation error handling."""
        # Setup
        mock_openai_client.chat.completions.create.side_effect = Exception("API Error")
        
        # Execute & Verify
        with pytest.raises(AIProviderError) as exc_info:
            await openai_provider.generate_text("Test prompt")
        
        assert exc_info.value.provider == "openai"
        assert "API Error" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_generate_structured_output(self, openai_provider, mock_openai_client):
        """Test structured output generation."""
        # Setup
        expected_data = {"key": "value", "number": 42}
        json_response = json.dumps(expected_data)
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = json_response
        mock_openai_client.chat.completions.create.return_value = mock_response
        
        schema = {
            "type": "object",
            "properties": {
                "key": {"type": "string"},
                "number": {"type": "integer"}
            }
        }
        
        # Execute
        result = await openai_provider.generate_structured_output(
            prompt="Generate JSON",
            schema=schema
        )
        
        # Verify
        assert result == expected_data
    
    @pytest.mark.asyncio
    async def test_generate_structured_output_invalid_json(self, openai_provider, mock_openai_client):
        """Test structured output with invalid JSON."""
        # Setup
        invalid_json = "This is not JSON"
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = invalid_json
        mock_openai_client.chat.completions.create.return_value = mock_response
        
        schema = {"type": "object"}
        
        # Execute & Verify
        with pytest.raises(AIProviderError):
            await openai_provider.generate_structured_output(
                prompt="Generate JSON",
                schema=schema
            )
    
    @pytest.mark.asyncio
    async def test_health_check_success(self, openai_provider, mock_openai_client):
        """Test successful health check."""
        # Setup
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "OK"
        mock_openai_client.chat.completions.create.return_value = mock_response
        
        # Execute
        result = await openai_provider.health_check()
        
        # Verify
        assert result is True
    
    @pytest.mark.asyncio
    async def test_health_check_failure(self, openai_provider, mock_openai_client):
        """Test health check failure."""
        # Setup
        mock_openai_client.chat.completions.create.side_effect = Exception("Connection error")
        
        # Execute
        result = await openai_provider.health_check()
        
        # Verify
        assert result is False


class TestAnthropicProvider:
    """Test Anthropic provider."""
    
    @pytest.fixture
    def mock_anthropic_client(self):
        """Create mock Anthropic client."""
        client = Mock()
        client.messages.create = AsyncMock()
        return client
    
    @pytest.fixture
    def anthropic_provider(self, mock_anthropic_client):
        """Create Anthropic provider with mock client."""
        with patch('app.services.ai_providers.AsyncAnthropic', return_value=mock_anthropic_client):
            provider = AnthropicProvider()
            provider.client = mock_anthropic_client
            return provider
    
    @pytest.mark.asyncio
    async def test_generate_text_success(self, anthropic_provider, mock_anthropic_client):
        """Test successful text generation."""
        # Setup
        expected_response = "Generated text response"
        mock_response = Mock()
        mock_response.content = [Mock()]
        mock_response.content[0].text = expected_response
        mock_anthropic_client.messages.create.return_value = mock_response
        
        # Execute
        result = await anthropic_provider.generate_text(
            prompt="Test prompt",
            system_prompt="Test system prompt"
        )
        
        # Verify
        assert result == expected_response
        mock_anthropic_client.messages.create.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_generate_text_error(self, anthropic_provider, mock_anthropic_client):
        """Test text generation error handling."""
        # Setup
        mock_anthropic_client.messages.create.side_effect = Exception("API Error")
        
        # Execute & Verify
        with pytest.raises(AIProviderError) as exc_info:
            await anthropic_provider.generate_text("Test prompt")
        
        assert exc_info.value.provider == "anthropic"


class TestAIProviderManager:
    """Test AI Provider Manager."""
    
    @pytest.fixture
    def provider_manager(self):
        """Create provider manager."""
        return AIProviderManager()
    
    def test_initialization_with_api_keys(self, provider_manager):
        """Test provider manager initialization."""
        # The actual initialization depends on environment variables
        # In tests, we might not have real API keys
        assert isinstance(provider_manager.providers, dict)
    
    def test_get_provider_existing(self, provider_manager):
        """Test getting existing provider."""
        # Mock a provider
        mock_provider = Mock()
        provider_manager.providers["test"] = mock_provider
        
        result = provider_manager.get_provider("test")
        assert result == mock_provider
    
    def test_get_provider_nonexistent(self, provider_manager):
        """Test getting non-existent provider."""
        with pytest.raises(AIProviderError) as exc_info:
            provider_manager.get_provider("nonexistent")
        
        assert "not available" in str(exc_info.value)
    
    def test_get_default_provider_with_openai(self, provider_manager):
        """Test getting default provider when OpenAI is available."""
        mock_openai = Mock()
        provider_manager.providers["openai"] = mock_openai
        
        result = provider_manager.get_default_provider()
        assert result == mock_openai
    
    def test_get_default_provider_with_anthropic(self, provider_manager):
        """Test getting default provider when only Anthropic is available."""
        mock_anthropic = Mock()
        provider_manager.providers["anthropic"] = mock_anthropic
        
        result = provider_manager.get_default_provider()
        assert result == mock_anthropic
    
    def test_get_default_provider_none_available(self, provider_manager):
        """Test getting default provider when none are available."""
        provider_manager.providers.clear()
        
        with pytest.raises(AIProviderError) as exc_info:
            provider_manager.get_default_provider()
        
        assert "No AI providers available" in str(exc_info.value)
    
    def test_list_providers(self, provider_manager):
        """Test listing providers."""
        mock_providers = {"openai": Mock(), "anthropic": Mock()}
        provider_manager.providers = mock_providers
        
        result = provider_manager.list_providers()
        assert result == ["openai", "anthropic"]
    
    @pytest.mark.asyncio
    async def test_health_check_all(self, provider_manager):
        """Test health check for all providers."""
        # Setup mock providers
        mock_openai = Mock()
        mock_openai.health_check = AsyncMock(return_value=True)
        mock_anthropic = Mock()
        mock_anthropic.health_check = AsyncMock(return_value=False)
        
        provider_manager.providers = {
            "openai": mock_openai,
            "anthropic": mock_anthropic
        }
        
        # Execute
        results = await provider_manager.health_check_all()
        
        # Verify
        assert results["openai"] is True
        assert results["anthropic"] is False


class TestCreateAIProvider:
    """Test create_ai_provider function."""

    @patch('app.services.ai_providers.provider_manager')
    def test_create_ai_provider_with_config(self, mock_manager):
        """Test creating provider with configuration."""
        mock_provider = Mock()
        mock_manager.create_provider_with_config.return_value = mock_provider

        result = create_ai_provider("openai", "test-key", model="gpt-4")

        assert result == mock_provider
        mock_manager.create_provider_with_config.assert_called_once_with(
            provider_name="openai",
            api_key="test-key",
            base_url=None,
            model="gpt-4",
            temperature=None
        )

    @patch('app.services.ai_providers.provider_manager')
    def test_create_ai_provider_with_custom_base_url(self, mock_manager):
        """Test creating provider with custom base URL."""
        mock_provider = Mock()
        mock_manager.create_provider_with_config.return_value = mock_provider

        result = create_ai_provider("custom", "test-key", base_url="https://api.example.com")

        assert result == mock_provider
        mock_manager.create_provider_with_config.assert_called_once_with(
            provider_name="custom",
            api_key="test-key",
            base_url="https://api.example.com",
            model=None,
            temperature=None
        )
