"""
Unit tests for usage tracking service.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock

from app.services.usage_tracking import UsageTracker, estimate_cost
from app.models.settings import API<PERSON>ey, APIKeyStatus, APIKeyProvider


class TestUsageTracker:
    """Test usage tracking service."""
    
    @pytest.fixture
    def mock_db(self):
        """Create a mock database session."""
        return Mock()
    
    @pytest.fixture
    def usage_tracker(self, mock_db):
        """Create a usage tracker instance."""
        return UsageTracker(mock_db)
    
    @pytest.fixture
    def sample_api_key(self):
        """Create a sample API key."""
        api_key = Mock(spec=APIKey)
        api_key.id = 1
        api_key.user_id = 1
        api_key.name = "Test Key"
        api_key.provider = APIKeyProvider.OPENAI
        api_key.status = APIKeyStatus.ACTIVE
        api_key.usage_count = 0
        api_key.requests_today = 0
        api_key.requests_month = 0
        api_key.cost_today = 0.0
        api_key.cost_month = 0.0
        api_key.last_used = None
        api_key.last_daily_reset = None
        api_key.last_monthly_reset = None
        return api_key

    @pytest.mark.asyncio
    async def test_record_usage_success(self, usage_tracker, mock_db, sample_api_key):
        """Test successful usage recording."""
        # Mock database query
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_api_key
        mock_db.execute = AsyncMock(return_value=mock_result)
        mock_db.commit = AsyncMock()
        mock_db.refresh = AsyncMock()
        
        result = await usage_tracker.record_usage(1, requests=5, cost=1.23)
        
        assert result is True
        assert sample_api_key.usage_count == 5
        assert sample_api_key.requests_today == 5
        assert sample_api_key.requests_month == 5
        assert sample_api_key.cost_today == 1.23
        assert sample_api_key.cost_month == 1.23
        assert sample_api_key.last_used is not None
        
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()

    @pytest.mark.asyncio
    async def test_record_usage_api_key_not_found(self, usage_tracker, mock_db):
        """Test usage recording when API key not found."""
        # Mock database query returning None
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_db.execute = AsyncMock(return_value=mock_result)
        
        result = await usage_tracker.record_usage(999, requests=1, cost=0.1)
        
        assert result is False

    @pytest.mark.asyncio
    async def test_record_usage_inactive_key(self, usage_tracker, mock_db, sample_api_key):
        """Test usage recording for inactive API key."""
        sample_api_key.status = APIKeyStatus.INACTIVE
        
        # Mock database query
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_api_key
        mock_db.execute = AsyncMock(return_value=mock_result)
        
        result = await usage_tracker.record_usage(1, requests=1, cost=0.1)
        
        assert result is False

    @pytest.mark.asyncio
    async def test_reset_daily_counters(self, usage_tracker, mock_db, sample_api_key):
        """Test daily counter reset."""
        # Set up API key with old daily reset date
        sample_api_key.last_daily_reset = datetime.now() - timedelta(days=1)
        sample_api_key.requests_today = 100
        sample_api_key.cost_today = 10.0
        
        # Mock database query
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_api_key
        mock_db.execute = AsyncMock(return_value=mock_result)
        mock_db.commit = AsyncMock()
        mock_db.refresh = AsyncMock()
        
        await usage_tracker.record_usage(1, requests=1, cost=0.1)
        
        # Verify daily counters were reset
        assert sample_api_key.requests_today == 1  # Only the new request
        assert sample_api_key.cost_today == 0.1    # Only the new cost

    @pytest.mark.asyncio
    async def test_reset_monthly_counters(self, usage_tracker, mock_db, sample_api_key):
        """Test monthly counter reset."""
        # Set up API key with old monthly reset date
        sample_api_key.last_monthly_reset = datetime.now() - timedelta(days=32)
        sample_api_key.requests_month = 1000
        sample_api_key.cost_month = 100.0
        
        # Mock database query
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_api_key
        mock_db.execute = AsyncMock(return_value=mock_result)
        mock_db.commit = AsyncMock()
        mock_db.refresh = AsyncMock()
        
        await usage_tracker.record_usage(1, requests=1, cost=0.1)
        
        # Verify monthly counters were reset
        assert sample_api_key.requests_month == 1  # Only the new request
        assert sample_api_key.cost_month == 0.1    # Only the new cost

    @pytest.mark.asyncio
    async def test_get_usage_stats_success(self, usage_tracker, mock_db, sample_api_key):
        """Test successful usage statistics retrieval."""
        sample_api_key.usage_count = 100
        sample_api_key.requests_today = 10
        sample_api_key.requests_month = 50
        sample_api_key.cost_today = 1.23
        sample_api_key.cost_month = 15.67
        sample_api_key.last_used = datetime.now()
        # Set reset dates to current time to prevent counter reset
        sample_api_key.last_daily_reset = datetime.now()
        sample_api_key.last_monthly_reset = datetime.now()

        # Mock database query
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_api_key
        mock_db.execute = AsyncMock(return_value=mock_result)
        mock_db.commit = AsyncMock()
        mock_db.refresh = AsyncMock()

        stats = await usage_tracker.get_usage_stats(1)

        assert stats is not None
        assert stats["total_requests"] == 100
        assert stats["requests_today"] == 10
        assert stats["requests_month"] == 50
        assert stats["cost_today"] == 1.23
        assert stats["cost_month"] == 15.67
        assert stats["last_used"] is not None

    @pytest.mark.asyncio
    async def test_get_usage_stats_not_found(self, usage_tracker, mock_db):
        """Test usage statistics retrieval when API key not found."""
        # Mock database query returning None
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_db.execute = AsyncMock(return_value=mock_result)
        
        stats = await usage_tracker.get_usage_stats(999)
        
        assert stats is None

    @pytest.mark.asyncio
    async def test_get_user_usage_summary_success(self, usage_tracker, mock_db):
        """Test successful user usage summary retrieval."""
        # Create multiple API keys for the user
        api_key1 = Mock(spec=APIKey)
        api_key1.status = APIKeyStatus.ACTIVE
        api_key1.usage_count = 100
        api_key1.requests_today = 10
        api_key1.requests_month = 50
        api_key1.cost_today = 1.0
        api_key1.cost_month = 10.0
        api_key1.last_daily_reset = datetime.now()
        api_key1.last_monthly_reset = datetime.now()
        
        api_key2 = Mock(spec=APIKey)
        api_key2.status = APIKeyStatus.INACTIVE
        api_key2.usage_count = 50
        api_key2.requests_today = 5
        api_key2.requests_month = 25
        api_key2.cost_today = 0.5
        api_key2.cost_month = 5.0
        api_key2.last_daily_reset = datetime.now()
        api_key2.last_monthly_reset = datetime.now()
        
        # Mock database query
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = [api_key1, api_key2]
        mock_db.execute = AsyncMock(return_value=mock_result)
        mock_db.commit = AsyncMock()
        
        summary = await usage_tracker.get_user_usage_summary(1)
        
        assert summary["total_keys"] == 2
        assert summary["active_keys"] == 1
        assert summary["total_requests"] == 150
        assert summary["requests_today"] == 15
        assert summary["requests_month"] == 75
        assert summary["cost_today"] == 1.5
        assert summary["cost_month"] == 15.0

    @pytest.mark.asyncio
    async def test_record_usage_database_error(self, usage_tracker, mock_db, sample_api_key):
        """Test usage recording with database error."""
        # Mock database query
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_api_key
        mock_db.execute = AsyncMock(return_value=mock_result)
        mock_db.commit = AsyncMock(side_effect=Exception("Database error"))
        mock_db.rollback = AsyncMock()
        
        result = await usage_tracker.record_usage(1, requests=1, cost=0.1)
        
        assert result is False
        mock_db.rollback.assert_called_once()


class TestCostEstimation:
    """Test cost estimation functions."""
    
    def test_estimate_cost_openai_gpt4(self):
        """Test cost estimation for OpenAI GPT-4."""
        cost = estimate_cost("openai", "gpt-4", 1000, 500)
        expected = (1000 / 1000) * 0.03 + (500 / 1000) * 0.06
        assert cost == expected

    def test_estimate_cost_anthropic_claude3_sonnet(self):
        """Test cost estimation for Anthropic Claude-3 Sonnet."""
        cost = estimate_cost("anthropic", "claude-3-sonnet", 1000, 500)
        expected = (1000 / 1000) * 0.003 + (500 / 1000) * 0.015
        assert cost == expected

    def test_estimate_cost_google_gemini_pro(self):
        """Test cost estimation for Google Gemini Pro."""
        cost = estimate_cost("google", "gemini-pro", 1000, 500)
        expected = (1000 / 1000) * 0.0005 + (500 / 1000) * 0.0015
        assert cost == expected

    def test_estimate_cost_unknown_provider(self):
        """Test cost estimation for unknown provider."""
        cost = estimate_cost("unknown", "unknown-model", 1000, 500)
        expected = (1000 / 1000) * 0.001 + (500 / 1000) * 0.002
        assert cost == expected

    def test_estimate_cost_unknown_model(self):
        """Test cost estimation for unknown model."""
        cost = estimate_cost("openai", "unknown-model", 1000, 500)
        expected = (1000 / 1000) * 0.001 + (500 / 1000) * 0.002
        assert cost == expected
