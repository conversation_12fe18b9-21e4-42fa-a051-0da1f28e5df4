"""
Unit tests for Planning Service.
"""

import pytest
from unittest.mock import As<PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch
import json

from app.services.planning_service import PlanningService
from app.core.exceptions import PlanningError
from tests.fixtures.factories import TeamPlanFactory, MockResponseFactory


class TestPlanningService:
    """Test PlanningService."""
    
    @pytest.fixture
    def planning_service(self):
        """Create PlanningService instance."""
        return PlanningService()
    
    @pytest.fixture
    def mock_ai_provider(self):
        """Create mock AI provider."""
        provider = Mock()
        provider.generate_structured_output = AsyncMock()
        provider.generate_text = AsyncMock()
        return provider
    
    @pytest.mark.asyncio
    async def test_create_team_plan_success(self, planning_service, mock_ai_provider):
        """Test successful team plan creation."""
        # Setup
        user_description = "我需要一个数据分析团队"
        team_plan = TeamPlanFactory.create_team_plan()
        
        mock_ai_provider.generate_structured_output.return_value = team_plan
        
        with patch('app.services.planning_service.get_ai_provider', return_value=mock_ai_provider):
            # Execute
            result = await planning_service.create_team_plan(user_description)
            
            # Verify
            assert isinstance(result, dict)
            assert "team_name" in result
            assert "team_members" in result
            assert "workflow" in result
            assert "plan_id" in result
            assert "created_at" in result
            assert "validation_score" in result
            
            # Verify AI provider was called
            mock_ai_provider.generate_structured_output.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_team_plan_with_requirements(self, planning_service, mock_ai_provider):
        """Test team plan creation with custom requirements."""
        # Setup
        user_description = "我需要一个股票分析团队"
        requirements = {
            "team_size": 3,
            "expertise": "金融分析",
            "real_time": True
        }
        team_plan = TeamPlanFactory.create_team_plan(member_count=3)
        
        mock_ai_provider.generate_structured_output.return_value = team_plan
        
        with patch('app.services.planning_service.get_ai_provider', return_value=mock_ai_provider):
            # Execute
            result = await planning_service.create_team_plan(
                user_description, 
                requirements=requirements
            )
            
            # Verify
            assert isinstance(result, dict)
            assert len(result["team_members"]) == 3
            
            # Verify requirements were passed to AI provider
            call_args = mock_ai_provider.generate_structured_output.call_args
            assert requirements in str(call_args)
    
    @pytest.mark.asyncio
    async def test_create_team_plan_with_model_params(self, planning_service, mock_ai_provider):
        """Test team plan creation with custom model parameters."""
        # Setup
        user_description = "我需要一个客服团队"
        team_plan = TeamPlanFactory.create_team_plan()
        
        mock_ai_provider.generate_structured_output.return_value = team_plan
        
        with patch('app.services.planning_service.get_ai_provider', return_value=mock_ai_provider):
            # Execute
            result = await planning_service.create_team_plan(
                user_description,
                model="gpt-3.5-turbo",
                temperature=0.5
            )
            
            # Verify
            assert isinstance(result, dict)
            
            # Verify model parameters were passed
            call_args = mock_ai_provider.generate_structured_output.call_args
            assert call_args.kwargs["model"] == "gpt-3.5-turbo"
            assert call_args.kwargs["temperature"] == 0.5
    
    @pytest.mark.asyncio
    async def test_create_team_plan_ai_provider_error(self, planning_service, mock_ai_provider):
        """Test team plan creation with AI provider error."""
        # Setup
        user_description = "我需要一个团队"
        mock_ai_provider.generate_structured_output.side_effect = Exception("API rate limit exceeded")
        
        with patch('app.services.planning_service.get_ai_provider', return_value=mock_ai_provider):
            # Execute & Verify
            with pytest.raises(PlanningError) as exc_info:
                await planning_service.create_team_plan(user_description)
            
            assert "Failed to create team plan" in str(exc_info.value)
            assert exc_info.value.stage == "planning"
    
    @pytest.mark.asyncio
    async def test_validate_and_enhance_plan(self, planning_service, mock_ai_provider):
        """Test plan validation and enhancement."""
        # Setup
        team_plan = TeamPlanFactory.create_team_plan()
        mock_ai_provider.generate_text.return_value = "Enhanced prompt for team member"
        
        # Execute
        enhanced_plan = await planning_service._validate_and_enhance_plan(team_plan, mock_ai_provider)
        
        # Verify
        assert isinstance(enhanced_plan, dict)
        assert "plan_id" in enhanced_plan
        assert "created_at" in enhanced_plan
        assert "validation_score" in enhanced_plan
        assert enhanced_plan["validation_score"] > 0
        
        # Verify team members were enhanced
        for member in enhanced_plan["team_members"]:
            assert "enhanced_prompt" in member
            assert "detailed_config" in member
    
    @pytest.mark.asyncio
    async def test_enhance_team_member(self, planning_service, mock_ai_provider):
        """Test team member enhancement."""
        # Setup
        team_plan = TeamPlanFactory.create_team_plan()
        member = team_plan["team_members"][0]
        enhanced_prompt = "You are a professional data analyst..."
        
        mock_ai_provider.generate_text.return_value = enhanced_prompt
        
        # Execute
        enhanced_member = await planning_service._enhance_team_member(
            member, team_plan, mock_ai_provider
        )
        
        # Verify
        assert isinstance(enhanced_member, dict)
        assert enhanced_member["name"] == member["name"]
        assert enhanced_member["role"] == member["role"]
        assert "enhanced_prompt" in enhanced_member
        assert "detailed_config" in enhanced_member
        assert enhanced_member["enhanced_prompt"] == enhanced_prompt
    
    def test_calculate_validation_score(self, planning_service):
        """Test validation score calculation."""
        # Test with good plan
        good_plan = TeamPlanFactory.create_team_plan(member_count=3)
        good_plan["workflow"]["steps"] = [
            {"name": "step1"}, {"name": "step2"}, {"name": "step3"}
        ]
        
        score = planning_service._calculate_validation_score(good_plan)
        assert score > 50  # Should get points for team size and workflow
        
        # Test with poor plan
        poor_plan = {
            "team_members": [{"role": "member1"}],  # Only 1 member
            "workflow": {"steps": [{"name": "step1"}]}  # Only 1 step
        }
        
        score = planning_service._calculate_validation_score(poor_plan)
        assert score < 50  # Should get fewer points
    
    def test_get_planning_system_prompt(self, planning_service):
        """Test planning system prompt."""
        system_prompt = planning_service._get_planning_system_prompt()
        
        assert isinstance(system_prompt, str)
        assert len(system_prompt) > 0
        assert "AI agent team planner" in system_prompt
        assert "diverse teams" in system_prompt
        assert "complementary skills" in system_prompt
    
    def test_render_planning_prompt(self, planning_service):
        """Test planning prompt rendering."""
        context = {
            "user_description": "我需要一个数据分析团队",
            "requirements": {"team_size": 3}
        }
        
        prompt = planning_service._render_planning_prompt(context)
        
        assert isinstance(prompt, str)
        assert context["user_description"] in prompt
        assert "team_size" in prompt
        assert "Team Overview" in prompt
        assert "Team Members" in prompt
        assert "Workflow Design" in prompt
    
    def test_get_team_plan_schema(self, planning_service):
        """Test team plan schema."""
        schema = planning_service._get_team_plan_schema()
        
        assert isinstance(schema, dict)
        assert "type" in schema
        assert "properties" in schema
        assert "team_name" in schema["properties"]
        assert "team_members" in schema["properties"]
        assert "workflow" in schema["properties"]
    
    @pytest.mark.asyncio
    async def test_create_team_plan_validation_error(self, planning_service, mock_ai_provider):
        """Test team plan creation with validation error."""
        # Setup - return invalid plan
        invalid_plan = {"invalid": "plan"}
        mock_ai_provider.generate_structured_output.return_value = invalid_plan
        
        with patch('app.services.planning_service.get_ai_provider', return_value=mock_ai_provider):
            # Execute & Verify
            with pytest.raises(PlanningError) as exc_info:
                await planning_service.create_team_plan("test description")
            
            assert exc_info.value.stage == "validation"
    
    @pytest.mark.asyncio
    async def test_create_team_plan_empty_description(self, planning_service):
        """Test team plan creation with empty description."""
        with pytest.raises(PlanningError):
            await planning_service.create_team_plan("")
    
    @pytest.mark.asyncio
    async def test_create_team_plan_none_description(self, planning_service):
        """Test team plan creation with None description."""
        with pytest.raises((PlanningError, TypeError)):
            await planning_service.create_team_plan(None)
    
    def test_planning_service_initialization(self, planning_service):
        """Test PlanningService initialization."""
        assert planning_service is not None
        assert hasattr(planning_service, 'create_team_plan')
        assert hasattr(planning_service, '_validate_and_enhance_plan')
        assert hasattr(planning_service, '_calculate_validation_score')
