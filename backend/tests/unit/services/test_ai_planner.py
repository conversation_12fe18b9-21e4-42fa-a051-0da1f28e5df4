"""
Unit tests for AI Planner service.
"""

import json
import pytest
from unittest.mock import As<PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

from app.services.ai_planner import AI<PERSON>lanner, get_ai_planner
from app.models.settings import SystemSettings
from app.models.user import User


class TestAIPlanner:
    """Test AIPlanner service."""
    
    @pytest.fixture
    def mock_db(self):
        """Create mock database session."""
        return AsyncMock()

    @pytest.fixture
    def mock_user(self):
        """Create mock user."""
        user = Mock(spec=User)
        user.id = 1
        user.email = "<EMAIL>"
        return user

    @pytest.fixture
    def ai_planner(self, mock_db, mock_user):
        """Create AIPlanner instance with user context."""
        return AIPlanner(db=mock_db, user=mock_user)

    def test_ai_planner_initialization(self, ai_planner):
        """Test AIPlanner initialization."""
        assert ai_planner.system_prompt is not None
        assert ai_planner._ai_generation_prompt is not None
        assert ai_planner.db is not None
        assert ai_planner.user is not None
    


    @pytest.mark.asyncio
    async def test_get_system_settings(self, ai_planner):
        """Test system settings retrieval."""
        # Mock the database query
        mock_settings = Mock(spec=SystemSettings)
        mock_settings.is_active = True
        mock_settings.enable_ai_team_generation = True
        mock_settings.team_generation_provider = "openai"
        mock_settings.team_generation_model = "gpt-4"

        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = mock_settings
        ai_planner.db.execute = AsyncMock(return_value=mock_result)

        settings = await ai_planner.get_system_settings()

        assert settings is not None
        assert settings.enable_ai_team_generation is True
        assert settings.team_generation_provider == "openai"
        assert settings.team_generation_model == "gpt-4"

    @pytest.mark.asyncio
    async def test_get_system_api_key_for_team_generation(self, ai_planner):
        """Test system API key retrieval for team generation."""
        # Mock the system settings
        mock_settings = Mock(spec=SystemSettings)
        mock_settings.team_generation_api_key = "encrypted_key"

        with patch.object(ai_planner, 'get_system_settings', return_value=mock_settings):
            with patch('app.models.settings.decrypt_api_key', return_value="decrypted_key"):
                api_key = await ai_planner.get_system_api_key_for_team_generation()

                assert api_key == "decrypted_key"
    
    @pytest.mark.asyncio
    async def test_generate_team_plan_ai_only(self, ai_planner):
        """Test AI-only team plan generation."""
        description = "我需要一个技术咨询团队"

        # Mock the AI generation method
        with patch.object(ai_planner, 'generate_ai_powered_team') as mock_ai_gen:
            mock_team_plan = {
                "team_name": "AI Generated Tech Team",
                "description": "AI generated technical consulting team",
                "team_members": [{"name": "Expert 1", "role": "consultant"}],
                "workflow": {"steps": [{"name": "Analysis", "description": "Analyze requirements"}]},
                "generation_method": "ai_powered"
            }
            mock_ai_gen.return_value = mock_team_plan

            team_plan = await ai_planner.generate_team_plan(description)

            assert isinstance(team_plan, dict)
            assert team_plan["generation_method"] == "ai_powered"
            assert "team_name" in team_plan
            assert "team_members" in team_plan
            mock_ai_gen.assert_called_once_with(description)

    @pytest.mark.asyncio
    async def test_generate_team_plan_requires_user_context(self):
        """Test that team plan generation requires user context."""
        # Create AIPlanner without user context
        ai_planner_no_user = AIPlanner()

        description = "我需要一个团队"

        with pytest.raises(ValueError, match="AI team generation requires database connection and user context"):
            await ai_planner_no_user.generate_team_plan(description)
    
    def test_system_prompt(self, ai_planner):
        """Test system prompt."""
        system_prompt = ai_planner._get_system_prompt()

        assert isinstance(system_prompt, str)
        assert len(system_prompt) > 0
        assert "AI团队规划师" in system_prompt
        assert "JSON格式" in system_prompt

    def test_get_ai_generation_prompt(self, ai_planner):
        """Test AI generation prompt."""
        prompt = ai_planner._get_ai_generation_prompt()

        assert isinstance(prompt, str)
        assert "JSON格式" in prompt
        assert "team_members" in prompt
        assert "workflow" in prompt
        assert "system_prompt" in prompt

    @pytest.mark.asyncio
    async def test_generate_ai_powered_team_success(self, ai_planner):
        """Test successful AI-powered team generation."""
        description = "我需要一个技术咨询团队"

        # Mock system settings
        mock_settings = Mock(spec=SystemSettings)
        mock_settings.enable_ai_team_generation = True
        mock_settings.team_generation_provider = "openai"
        mock_settings.team_generation_model = "gpt-4"
        mock_settings.team_generation_temperature = 0.7
        mock_settings.team_generation_max_tokens = 4000

        # Mock AI provider response
        mock_ai_response = json.dumps({
            "team_name": "AI Generated Tech Team",
            "description": "Professional technical consulting team",
            "objective": "Provide technical consulting services",
            "domain": "technical",
            "complexity": "advanced",
            "team_members": [
                {
                    "name": "Senior Architect",
                    "role": "architect",
                    "description": "System architecture expert",
                    "system_prompt": "You are a senior system architect...",
                    "capabilities": ["Architecture Design", "Technical Leadership"],
                    "tools": ["Design Tools", "Analysis Tools"],
                    "model": "gpt-4",
                    "temperature": 0.7
                }
            ],
            "workflow": {
                "steps": [
                    {
                        "name": "Requirements Analysis",
                        "description": "Analyze client requirements",
                        "assignee": "Senior Architect",
                        "inputs": ["Client Requirements"],
                        "outputs": ["Analysis Report"]
                    }
                ]
            }
        })

        with patch.object(ai_planner, 'get_system_settings', return_value=mock_settings):
            with patch.object(ai_planner, 'get_system_api_key_for_team_generation', return_value="test_api_key"):
                with patch('app.services.ai_planner.create_ai_provider') as mock_create_provider:
                    mock_provider = AsyncMock()
                    mock_provider.generate_text = AsyncMock(return_value=mock_ai_response)
                    mock_create_provider.return_value = mock_provider

                    team_plan = await ai_planner.generate_ai_powered_team(description)

                    assert isinstance(team_plan, dict)
                    assert team_plan["team_name"] == "AI Generated Tech Team"
                    assert team_plan["generation_method"] == "ai_powered"
                    assert "team_members" in team_plan
                    assert len(team_plan["team_members"]) > 0
                    assert "workflow" in team_plan

    @pytest.mark.asyncio
    async def test_generate_ai_powered_team_disabled(self, ai_planner):
        """Test AI team generation when disabled."""
        description = "我需要一个团队"

        # Mock settings with AI generation disabled
        mock_settings = Mock(spec=SystemSettings)
        mock_settings.enable_ai_team_generation = False

        with patch.object(ai_planner, 'get_system_settings', return_value=mock_settings):
            with pytest.raises(ValueError, match="AI team generation is not enabled"):
                await ai_planner.generate_ai_powered_team(description)


class TestGetAIPlanner:
    """Test get_ai_planner function."""

    def test_get_ai_planner_with_context(self):
        """Test get_ai_planner with database and user context."""
        mock_db = AsyncMock()
        mock_user = Mock(spec=User)
        mock_user.id = 1

        planner = get_ai_planner(db=mock_db, user=mock_user)

        assert isinstance(planner, AIPlanner)
        assert planner.db == mock_db
        assert planner.user == mock_user


class TestGetAIPlanner:
    """Test get_ai_planner function."""

    def test_get_ai_planner_with_context(self):
        """Test getting AI planner with database and user context."""
        mock_db = AsyncMock()
        mock_user = Mock(spec=User)
        mock_user.id = 1

        planner = get_ai_planner(db=mock_db, user=mock_user)

        assert isinstance(planner, AIPlanner)
        assert planner.db == mock_db
        assert planner.user == mock_user
