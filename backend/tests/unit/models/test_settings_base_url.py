"""
Unit tests for base URL functionality in settings models.
"""

import pytest
from pydantic import ValidationError

from app.models.settings import (
    APIKeyCreate, APIKeyUpdate, APIKeyProvider,
    validate_base_url, get_default_base_url, is_common_proxy_pattern,
    get_provider_examples
)


class TestBaseURLValidation:
    """Test base URL validation functionality."""
    
    def test_validate_base_url_empty(self):
        """Test that empty URLs are valid (optional field)."""
        assert validate_base_url("") is True
        assert validate_base_url(None) is True
    
    def test_validate_base_url_valid_https(self):
        """Test valid HTTPS URLs."""
        valid_urls = [
            "https://api.openai.com/v1",
            "https://api.anthropic.com",
            "https://your-resource.openai.azure.com",
            "https://localhost:8080",
            "https://127.0.0.1:3000",
            "https://proxy.company.com/api/v1",
            "https://gateway.example.org"
        ]
        
        for url in valid_urls:
            assert validate_base_url(url) is True, f"URL should be valid: {url}"
    
    def test_validate_base_url_valid_http(self):
        """Test valid HTTP URLs (for development)."""
        valid_urls = [
            "http://localhost:8080",
            "http://127.0.0.1:3000",
            "http://dev.company.internal"
        ]
        
        for url in valid_urls:
            assert validate_base_url(url) is True, f"URL should be valid: {url}"
    
    def test_validate_base_url_invalid(self):
        """Test invalid URLs."""
        invalid_urls = [
            "ftp://example.com",  # Wrong protocol
            "https://",  # No domain
            "not-a-url",  # Not a URL
            "https://",  # Incomplete
            "javascript:alert('xss')",  # Security risk
            "file:///etc/passwd",  # Local file
            "data:text/html,<script>alert('xss')</script>"  # Data URL
        ]
        
        for url in invalid_urls:
            assert validate_base_url(url) is False, f"URL should be invalid: {url}"
    
    def test_validate_base_url_with_whitespace(self):
        """Test URL validation with whitespace."""
        assert validate_base_url("  https://api.openai.com/v1  ") is True
        assert validate_base_url("\nhttps://api.openai.com/v1\n") is True


class TestAPIKeyCreateValidation:
    """Test API key creation with base URL validation."""
    
    def test_create_api_key_valid_base_url(self):
        """Test creating API key with valid base URL."""
        key_data = APIKeyCreate(
            name="Test Key",
            provider=APIKeyProvider.OPENAI,
            key="sk-test123",
            base_url="https://api.openai.com/v1"
        )
        assert key_data.base_url == "https://api.openai.com/v1"
    
    def test_create_api_key_empty_base_url(self):
        """Test creating API key with empty base URL."""
        key_data = APIKeyCreate(
            name="Test Key",
            provider=APIKeyProvider.OPENAI,
            key="sk-test123",
            base_url=""
        )
        assert key_data.base_url == ""
    
    def test_create_api_key_none_base_url(self):
        """Test creating API key with None base URL."""
        key_data = APIKeyCreate(
            name="Test Key",
            provider=APIKeyProvider.OPENAI,
            key="sk-test123",
            base_url=None
        )
        assert key_data.base_url is None
    
    def test_create_api_key_invalid_base_url(self):
        """Test creating API key with invalid base URL raises validation error."""
        with pytest.raises(ValidationError) as exc_info:
            APIKeyCreate(
                name="Test Key",
                provider=APIKeyProvider.OPENAI,
                key="sk-test123",
                base_url="not-a-valid-url"
            )
        
        assert "Invalid base URL format" in str(exc_info.value)


class TestAPIKeyUpdateValidation:
    """Test API key update with base URL validation."""
    
    def test_update_api_key_valid_base_url(self):
        """Test updating API key with valid base URL."""
        update_data = APIKeyUpdate(
            base_url="https://proxy.company.com/openai/v1"
        )
        assert update_data.base_url == "https://proxy.company.com/openai/v1"
    
    def test_update_api_key_invalid_base_url(self):
        """Test updating API key with invalid base URL raises validation error."""
        with pytest.raises(ValidationError) as exc_info:
            APIKeyUpdate(
                base_url="invalid-url"
            )
        
        assert "Invalid base URL format" in str(exc_info.value)


class TestProviderDefaults:
    """Test provider default URL functionality."""
    
    def test_get_default_base_url_openai(self):
        """Test getting default base URL for OpenAI."""
        url = get_default_base_url(APIKeyProvider.OPENAI)
        assert url == "https://api.openai.com/v1"
    
    def test_get_default_base_url_anthropic(self):
        """Test getting default base URL for Anthropic."""
        url = get_default_base_url(APIKeyProvider.ANTHROPIC)
        assert url == "https://api.anthropic.com"
    
    def test_get_default_base_url_azure(self):
        """Test getting default base URL for Azure (should be None)."""
        url = get_default_base_url(APIKeyProvider.AZURE)
        assert url is None
    
    def test_get_default_base_url_custom(self):
        """Test getting default base URL for custom provider (should be None)."""
        url = get_default_base_url(APIKeyProvider.CUSTOM)
        assert url is None


class TestProxyPatterns:
    """Test common proxy pattern detection."""
    
    def test_is_common_proxy_pattern_azure(self):
        """Test Azure OpenAI pattern detection."""
        assert is_common_proxy_pattern("https://myresource.openai.azure.com") is True
        assert is_common_proxy_pattern("https://test.openai.azure.com/openai/deployments/gpt-4") is True
    
    def test_is_common_proxy_pattern_corporate(self):
        """Test corporate proxy pattern detection."""
        assert is_common_proxy_pattern("https://api-proxy.company.com") is True
        assert is_common_proxy_pattern("https://gateway.enterprise.com") is True
    
    def test_is_common_proxy_pattern_cloud(self):
        """Test cloud service pattern detection."""
        assert is_common_proxy_pattern("https://api.amazonaws.com") is True
        assert is_common_proxy_pattern("https://worker.cloudflare.com") is True
    
    def test_is_common_proxy_pattern_not_proxy(self):
        """Test that regular URLs are not detected as proxy patterns."""
        assert is_common_proxy_pattern("https://api.openai.com/v1") is False
        assert is_common_proxy_pattern("https://api.anthropic.com") is False
    
    def test_is_common_proxy_pattern_empty(self):
        """Test empty URL handling."""
        assert is_common_proxy_pattern("") is False
        assert is_common_proxy_pattern(None) is False


class TestProviderExamples:
    """Test provider example URL functionality."""
    
    def test_get_provider_examples_openai(self):
        """Test getting example URLs for OpenAI."""
        examples = get_provider_examples(APIKeyProvider.OPENAI)
        assert "default" in examples
        assert "azure" in examples
        assert "proxy" in examples
        assert examples["default"] == "https://api.openai.com/v1"
    
    def test_get_provider_examples_azure(self):
        """Test getting example URLs for Azure."""
        examples = get_provider_examples(APIKeyProvider.AZURE)
        assert "required" in examples
        assert "your-resource.openai.azure.com" in examples["required"]
    
    def test_get_provider_examples_unknown(self):
        """Test getting examples for unknown provider returns empty dict."""
        # This would require adding a new provider type for testing
        examples = get_provider_examples(APIKeyProvider.CUSTOM)
        assert isinstance(examples, dict)
