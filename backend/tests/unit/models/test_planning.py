"""
Unit tests for Planning models.
"""

import pytest
import json
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.planning import (
    PlanningRequest,
    PlanningRequestCreate,
    PlanningStatus,
    PlanningRequestResponse
)
from tests.fixtures.factories import PlanningRequestFactory, TeamPlanFactory


class TestPlanningRequestModel:
    """Test PlanningRequest model."""
    
    def test_planning_request_creation(self):
        """Test planning request creation with valid data."""
        request_data = PlanningRequestFactory.create_planning_request_data()
        planning_request = PlanningRequest(**request_data)
        
        assert planning_request.request_id == request_data["request_id"]
        assert planning_request.user_description == request_data["user_description"]
        assert planning_request.model == request_data["model"]
        assert planning_request.temperature == request_data["temperature"]
        assert planning_request.status == request_data["status"]
    
    def test_planning_request_defaults(self):
        """Test planning request default values."""
        request_data = {
            "request_id": "test_req_001",
            "user_description": "测试需求描述"
        }
        planning_request = PlanningRequest(**request_data)
        
        assert planning_request.model == "gpt-4"
        assert planning_request.temperature == 0.7
        assert planning_request.status == PlanningStatus.PENDING
        assert planning_request.team_plan_json is None
        assert planning_request.generated_code is None
        assert planning_request.started_at is None
        assert planning_request.completed_at is None
        assert planning_request.duration_seconds is None
        assert planning_request.error_message is None
    
    def test_planning_status_enum(self):
        """Test planning status enumeration."""
        assert PlanningStatus.PENDING == "PENDING"
        assert PlanningStatus.ANALYZING == "ANALYZING"
        assert PlanningStatus.PLANNING == "PLANNING"
        assert PlanningStatus.COMPLETED == "COMPLETED"
        assert PlanningStatus.FAILED == "FAILED"
    
    def test_planning_request_with_results(self):
        """Test planning request with results."""
        team_plan = TeamPlanFactory.create_team_plan()
        team_plan_json = json.dumps(team_plan, ensure_ascii=False)
        
        planning_request = PlanningRequestFactory.create_planning_request(
            status=PlanningStatus.COMPLETED,
            team_plan_json=team_plan_json,
            generated_code="# Generated code here",
            started_at=datetime.utcnow(),
            completed_at=datetime.utcnow(),
            duration_seconds=15.5
        )
        
        assert planning_request.status == PlanningStatus.COMPLETED
        assert planning_request.team_plan_json == team_plan_json
        assert planning_request.generated_code == "# Generated code here"
        assert planning_request.started_at is not None
        assert planning_request.completed_at is not None
        assert planning_request.duration_seconds == 15.5
    
    def test_planning_request_with_error(self):
        """Test planning request with error."""
        planning_request = PlanningRequestFactory.create_planning_request(
            status=PlanningStatus.FAILED,
            error_message="AI provider error: Rate limit exceeded"
        )
        
        assert planning_request.status == PlanningStatus.FAILED
        assert planning_request.error_message == "AI provider error: Rate limit exceeded"
    
    def test_temperature_validation(self):
        """Test temperature field validation."""
        # Valid temperature values
        valid_temps = [0.0, 0.5, 1.0, 1.5, 2.0]
        for temp in valid_temps:
            request = PlanningRequestFactory.create_planning_request(temperature=temp)
            assert request.temperature == temp
        
        # Temperature should be between 0.0 and 2.0 (Pydantic validation)
        # Note: The actual validation depends on the Field constraints in the model
    
    @pytest.mark.asyncio
    @pytest.mark.database_operations
    async def test_planning_request_database_operations(self, test_session: AsyncSession):
        """Test planning request database operations."""
        # Create planning request
        planning_request = PlanningRequestFactory.create_planning_request()
        test_session.add(planning_request)
        await test_session.commit()
        await test_session.refresh(planning_request)
        
        # Verify creation
        assert planning_request.id is not None
        
        # Update status
        planning_request.status = PlanningStatus.ANALYZING
        planning_request.started_at = datetime.utcnow()
        await test_session.commit()
        await test_session.refresh(planning_request)
        
        assert planning_request.status == PlanningStatus.ANALYZING
        assert planning_request.started_at is not None
        
        # Complete the request
        team_plan = TeamPlanFactory.create_team_plan()
        planning_request.status = PlanningStatus.COMPLETED
        planning_request.team_plan_json = json.dumps(team_plan, ensure_ascii=False)
        planning_request.completed_at = datetime.utcnow()
        planning_request.duration_seconds = 20.0
        await test_session.commit()
        await test_session.refresh(planning_request)
        
        assert planning_request.status == PlanningStatus.COMPLETED
        assert planning_request.team_plan_json is not None
        assert planning_request.completed_at is not None
        assert planning_request.duration_seconds == 20.0
    
    def test_planning_request_create_model(self):
        """Test PlanningRequestCreate model."""
        create_data = {
            "user_description": "我需要一个客服团队",
            "model": "gpt-3.5-turbo",
            "temperature": 0.5
        }
        
        request_create = PlanningRequestCreate(**create_data)
        assert request_create.user_description == "我需要一个客服团队"
        assert request_create.model == "gpt-3.5-turbo"
        assert request_create.temperature == 0.5
    
    def test_planning_request_response_model(self):
        """Test PlanningRequestResponse model."""
        team_plan = TeamPlanFactory.create_team_plan()

        response_data = {
            "id": 1,
            "request_id": "req_123",
            "user_description": "测试描述",
            "model": "gpt-4",
            "temperature": 0.7,
            "status": PlanningStatus.COMPLETED,
            "team_plan_json": json.dumps(team_plan, ensure_ascii=False),
            "generated_code": None,
            "started_at": None,
            "completed_at": datetime.utcnow(),
            "duration_seconds": 25.0,
            "error_message": None,
            "created_at": datetime.utcnow(),
            "updated_at": None
        }

        response = PlanningRequestResponse(**response_data)
        assert response.request_id == "req_123"
        assert response.status == PlanningStatus.COMPLETED
        assert response.duration_seconds == 25.0
    
    def test_json_serialization(self):
        """Test JSON serialization of team plan."""
        team_plan = TeamPlanFactory.create_team_plan()
        team_plan_json = json.dumps(team_plan, ensure_ascii=False)
        
        planning_request = PlanningRequestFactory.create_planning_request(
            team_plan_json=team_plan_json
        )
        
        # Should be able to parse back to dict
        parsed_plan = json.loads(planning_request.team_plan_json)
        assert parsed_plan["team_name"] == team_plan["team_name"]
        assert len(parsed_plan["team_members"]) == len(team_plan["team_members"])
    
    def test_planning_request_validation(self):
        """Test planning request validation."""
        # Test that model can be created with minimal data
        planning_request = PlanningRequest(
            request_id="test_req",
            user_description="Test description"
        )
        assert planning_request.request_id == "test_req"
        assert planning_request.user_description == "Test description"

        # Test valid request with factory
        request_data = PlanningRequestFactory.create_planning_request_data()
        planning_request = PlanningRequest(**request_data)
        assert planning_request.request_id is not None
        assert planning_request.user_description is not None
    
    def test_planning_request_unique_id(self):
        """Test planning request unique ID generation."""
        request1 = PlanningRequestFactory.create_planning_request()
        request2 = PlanningRequestFactory.create_planning_request()
        
        assert request1.request_id != request2.request_id
        assert request1.request_id.startswith("req_")
        assert request2.request_id.startswith("req_")
    
    def test_planning_request_timing(self):
        """Test planning request timing calculations."""
        start_time = datetime.utcnow()
        end_time = datetime.utcnow()
        duration = (end_time - start_time).total_seconds()
        
        planning_request = PlanningRequestFactory.create_planning_request(
            started_at=start_time,
            completed_at=end_time,
            duration_seconds=duration
        )
        
        assert planning_request.started_at == start_time
        assert planning_request.completed_at == end_time
        assert planning_request.duration_seconds == duration
    
    def test_planning_request_error_handling(self):
        """Test planning request error handling."""
        error_message = "Connection timeout after 30 seconds"
        
        planning_request = PlanningRequestFactory.create_planning_request(
            status=PlanningStatus.FAILED,
            error_message=error_message
        )
        
        assert planning_request.status == PlanningStatus.FAILED
        assert planning_request.error_message == error_message
        assert planning_request.team_plan_json is None
        assert planning_request.generated_code is None
