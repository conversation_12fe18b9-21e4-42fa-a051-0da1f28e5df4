"""
Unit tests for Agent models.
"""

import pytest
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.agent import Agent, AgentStatus, AgentType, AgentCreate, AgentUpdate
from tests.fixtures.factories import AgentFactory


class TestAgentModel:
    """Test Agent model."""
    
    def test_agent_creation(self):
        """Test agent creation with valid data."""
        agent_data = AgentFactory.create_agent_data()
        agent = Agent(**agent_data)
        
        assert agent.agent_id == agent_data["agent_id"]
        assert agent.team_name == agent_data["team_name"]
        assert agent.description == agent_data["description"]
        assert agent.agent_type == agent_data["agent_type"]
        assert agent.status == agent_data["status"]
        assert agent.usage_count == 0
        assert agent.last_used is None
    
    def test_agent_with_custom_values(self):
        """Test agent creation with custom values."""
        agent = AgentFactory.create_agent(
            team_name="自定义团队",
            description="自定义描述",
            agent_type=AgentType.SINGLE,
            status=AgentStatus.INACTIVE,
            usage_count=10
        )
        
        assert agent.team_name == "自定义团队"
        assert agent.description == "自定义描述"
        assert agent.agent_type == AgentType.SINGLE
        assert agent.status == AgentStatus.INACTIVE
        assert agent.usage_count == 10
    
    def test_agent_status_enum(self):
        """Test agent status enumeration."""
        assert AgentStatus.CREATING == "creating"
        assert AgentStatus.ACTIVE == "active"
        assert AgentStatus.INACTIVE == "inactive"
        assert AgentStatus.ERROR == "error"
        assert AgentStatus.DELETED == "deleted"
    
    def test_agent_type_enum(self):
        """Test agent type enumeration."""
        assert AgentType.SINGLE == "single"
        assert AgentType.TEAM == "team"
        assert AgentType.WORKFLOW == "workflow"
    
    def test_agent_defaults(self):
        """Test agent default values."""
        agent_data = {
            "agent_id": "test_agent",
            "team_name": "测试团队",
            "description": "测试描述"
        }
        agent = Agent(**agent_data)
        
        assert agent.agent_type == AgentType.TEAM
        assert agent.status == AgentStatus.CREATING
        assert agent.usage_count == 0
        assert agent.last_used is None
        assert agent.avg_response_time is None
        assert agent.success_rate is None
    
    @pytest.mark.asyncio
    async def test_agent_database_operations(self, test_session: AsyncSession):
        """Test agent database operations."""
        # Create agent
        agent = AgentFactory.create_agent()
        test_session.add(agent)
        await test_session.commit()
        await test_session.refresh(agent)
        
        # Verify creation
        assert agent.id is not None
        assert agent.created_at is not None
        assert agent.uuid is not None
        
        # Update agent
        agent.status = AgentStatus.ACTIVE
        agent.usage_count = 5
        await test_session.commit()
        await test_session.refresh(agent)
        
        assert agent.status == AgentStatus.ACTIVE
        assert agent.usage_count == 5
        assert agent.updated_at is not None
    
    def test_agent_create_model(self):
        """Test AgentCreate model."""
        create_data = {
            "agent_id": "new_agent",
            "team_name": "新团队",
            "description": "新描述",
            "agent_type": AgentType.WORKFLOW,
            "status": AgentStatus.CREATING
        }
        
        agent_create = AgentCreate(**create_data)
        assert agent_create.agent_id == "new_agent"
        assert agent_create.team_name == "新团队"
        assert agent_create.agent_type == AgentType.WORKFLOW
    
    def test_agent_update_model(self):
        """Test AgentUpdate model."""
        update_data = {
            "team_name": "更新的团队",
            "status": AgentStatus.ACTIVE,
            "prompt_template": "新的提示模板"
        }
        
        agent_update = AgentUpdate(**update_data)
        assert agent_update.team_name == "更新的团队"
        assert agent_update.status == AgentStatus.ACTIVE
        assert agent_update.prompt_template == "新的提示模板"
        assert agent_update.description is None  # Not provided
    
    def test_agent_partial_update(self):
        """Test partial agent update."""
        # Only update specific fields
        update_data = {"status": AgentStatus.INACTIVE}
        agent_update = AgentUpdate(**update_data)
        
        assert agent_update.status == AgentStatus.INACTIVE
        assert agent_update.team_name is None
        assert agent_update.description is None
    
    def test_agent_validation(self):
        """Test agent data validation."""
        # Test required fields
        with pytest.raises(ValueError):
            Agent()  # Missing required fields
        
        # Test valid agent
        agent_data = AgentFactory.create_agent_data()
        agent = Agent(**agent_data)
        assert agent.agent_id is not None
        assert agent.team_name is not None
        assert agent.description is not None
    
    def test_agent_string_representation(self):
        """Test agent string representation."""
        agent = AgentFactory.create_agent(
            team_name="测试团队",
            agent_id="test_123"
        )
        
        # The model should have a meaningful string representation
        agent_str = str(agent)
        assert "test_123" in agent_str or "测试团队" in agent_str
    
    def test_agent_with_timestamps(self):
        """Test agent with timestamp fields."""
        now = datetime.utcnow()
        agent = AgentFactory.create_agent(last_used=now)
        
        assert agent.last_used == now
        # created_at should be set automatically when saved to DB
        assert hasattr(agent, 'created_at')
    

    
    def test_agent_prompts(self):
        """Test agent prompt templates."""
        prompt_template = "你是一个{role}，专门负责{task}"
        system_prompt = "你是一个专业的AI助手"
        
        agent = AgentFactory.create_agent(
            prompt_template=prompt_template,
            system_prompt=system_prompt
        )
        
        assert agent.prompt_template == prompt_template
        assert agent.system_prompt == system_prompt
