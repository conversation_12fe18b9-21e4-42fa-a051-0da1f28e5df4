"""
Unit tests for User models.
"""

import pytest
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import (
    User, UserSession, UserToken, LoginHistory,
    UserRole, UserStatus, SessionStatus, TokenType
)
from app.core.security import get_password_hash, verify_password
from tests.fixtures.factories import (
    UserFactory, UserSessionFactory, UserTokenFactory, LoginHistoryFactory
)


class TestUserModel:
    """Test User model."""
    
    def test_user_creation(self):
        """Test user creation with valid data."""
        user_data = UserFactory.create_user_data(
            name="<PERSON>",
            email="<EMAIL>",
            password="password123"
        )
        user = User(**user_data)
        
        assert user.name == "<PERSON>"
        assert user.email == "<EMAIL>"
        assert user.role == UserRole.USER
        assert user.status == UserStatus.ACTIVE
        assert user.language == "en"
        assert user.login_count == 0
        assert user.failed_login_attempts == 0
        assert user.is_email_verified is True
        assert isinstance(user.preferences, dict)
        assert isinstance(user.user_metadata, dict)
    
    def test_user_password_hash(self):
        """Test password hashing."""
        password = "testpassword123"
        user_data = UserFactory.create_user_data(password=password)
        user = User(**user_data)
        
        # Password should be hashed
        assert user.password_hash != password
        assert verify_password(password, user.password_hash)
    
    def test_user_roles(self):
        """Test user roles."""
        # Test regular user
        user = UserFactory.create_user(role=UserRole.USER)
        assert user.role == UserRole.USER
        
        # Test admin user
        admin = UserFactory.create_admin_user()
        assert admin.role == UserRole.ADMIN
        
        # Test moderator
        moderator = UserFactory.create_user(role=UserRole.MODERATOR)
        assert moderator.role == UserRole.MODERATOR
    
    def test_user_status(self):
        """Test user status."""
        # Test active user
        active_user = UserFactory.create_user(status=UserStatus.ACTIVE)
        assert active_user.status == UserStatus.ACTIVE
        
        # Test pending user
        pending_user = UserFactory.create_pending_user()
        assert pending_user.status == UserStatus.PENDING_VERIFICATION
        assert pending_user.is_email_verified is False
        
        # Test suspended user
        suspended_user = UserFactory.create_user(status=UserStatus.SUSPENDED)
        assert suspended_user.status == UserStatus.SUSPENDED
    
    def test_user_defaults(self):
        """Test user default values."""
        user = UserFactory.create_user()
        
        assert user.timezone == "UTC"
        assert user.language == "en"
        assert user.login_count == 0
        assert user.failed_login_attempts == 0
        assert user.preferences == {}
        assert user.user_metadata == {}
        assert user.is_email_verified is True
    
    def test_user_optional_fields(self):
        """Test user optional fields."""
        user = UserFactory.create_user(
            bio="Test bio",
            avatar="/avatars/test.jpg",
            timezone="America/New_York"
        )
        
        assert user.bio == "Test bio"
        assert user.avatar == "/avatars/test.jpg"
        assert user.timezone == "America/New_York"


class TestUserSessionModel:
    """Test UserSession model."""
    
    def test_session_creation(self):
        """Test session creation."""
        user = UserFactory.create_user()
        session_data = UserSessionFactory.create_session_data(
            user_id=1,
            session_token="test_token_123",
            ip_address="***********"
        )
        session = UserSession(**session_data)
        
        assert session.user_id == 1
        assert session.session_token == "test_token_123"
        assert session.status == SessionStatus.ACTIVE
        assert session.ip_address == "***********"
        assert session.is_secure is True
        assert session.is_mobile is False
    
    def test_session_expiry(self):
        """Test session expiry."""
        future_time = datetime.now() + timedelta(days=1)
        session = UserSessionFactory.create_session(
            user_id=1,
            expires_at=future_time
        )
        
        assert session.expires_at == future_time
        assert session.expires_at > datetime.now()
    
    def test_session_status(self):
        """Test session status."""
        # Active session
        active_session = UserSessionFactory.create_session(
            user_id=1,
            status=SessionStatus.ACTIVE
        )
        assert active_session.status == SessionStatus.ACTIVE
        
        # Expired session
        expired_session = UserSessionFactory.create_session(
            user_id=1,
            status=SessionStatus.EXPIRED
        )
        assert expired_session.status == SessionStatus.EXPIRED
        
        # Revoked session
        revoked_session = UserSessionFactory.create_session(
            user_id=1,
            status=SessionStatus.REVOKED
        )
        assert revoked_session.status == SessionStatus.REVOKED


class TestUserTokenModel:
    """Test UserToken model."""
    
    def test_token_creation(self):
        """Test token creation."""
        token_data = UserTokenFactory.create_token_data(
            user_id=1,
            token_type=TokenType.PASSWORD_RESET,
            token="reset_token_123"
        )
        token = UserToken(**token_data)
        
        assert token.user_id == 1
        assert token.token == "reset_token_123"
        assert token.token_type == TokenType.PASSWORD_RESET
        assert token.is_used is False
        assert token.is_revoked is False
    
    def test_password_reset_token(self):
        """Test password reset token."""
        token = UserTokenFactory.create_password_reset_token(user_id=1)
        
        assert token.token_type == TokenType.PASSWORD_RESET
        assert token.is_used is False
        assert token.is_revoked is False
        assert token.expires_at > datetime.now()
    
    def test_email_verification_token(self):
        """Test email verification token."""
        token = UserTokenFactory.create_email_verification_token(user_id=1)
        
        assert token.token_type == TokenType.EMAIL_VERIFICATION
        assert token.is_used is False
        assert token.is_revoked is False
    
    def test_token_expiry(self):
        """Test token expiry."""
        past_time = datetime.now() - timedelta(hours=1)
        expired_token = UserTokenFactory.create_token(
            user_id=1,
            token_type=TokenType.PASSWORD_RESET,
            expires_at=past_time
        )
        
        assert expired_token.expires_at < datetime.now()


class TestLoginHistoryModel:
    """Test LoginHistory model."""
    
    def test_successful_login_history(self):
        """Test successful login history."""
        history = LoginHistoryFactory.create_login_history(
            user_id=1,
            success=True,
            ip_address="***********"
        )
        
        assert history.user_id == 1
        assert history.success is True
        assert history.failure_reason is None
        assert history.ip_address == "***********"
        assert history.is_suspicious is False
    
    def test_failed_login_history(self):
        """Test failed login history."""
        history = LoginHistoryFactory.create_failed_login(
            user_id=1,
            reason="Invalid password"
        )
        
        assert history.user_id == 1
        assert history.success is False
        assert history.failure_reason == "Invalid password"
        assert history.is_suspicious is False
    
    def test_suspicious_login_history(self):
        """Test suspicious login history."""
        history = LoginHistoryFactory.create_suspicious_login(
            user_id=1
        )

        assert history.user_id == 1
        assert history.success is True
        assert history.is_suspicious is True
        assert history.risk_score == 0.8
    
    def test_login_history_metadata(self):
        """Test login history with metadata."""
        login_metadata = {"browser": "Chrome", "os": "Windows"}
        history = LoginHistoryFactory.create_login_history(
            user_id=1,
            login_metadata=login_metadata
        )

        assert history.login_metadata == login_metadata


class TestUserModelValidation:
    """Test User model validation."""
    
    def test_email_uniqueness_constraint(self):
        """Test email uniqueness (would be enforced by database)."""
        email = "<EMAIL>"
        user1 = UserFactory.create_user(email=email)
        user2 = UserFactory.create_user(email=email)
        
        # Both users can be created in memory, but database would enforce uniqueness
        assert user1.email == email
        assert user2.email == email
    
    def test_required_fields(self):
        """Test required fields."""
        # Test that required fields are present
        user_data = UserFactory.create_user_data()
        user = User(**user_data)
        
        assert user.name is not None
        assert user.email is not None
        assert user.password_hash is not None
        assert user.role is not None
        assert user.status is not None
    
    def test_field_lengths(self):
        """Test field length constraints."""
        # Test long name
        long_name = "A" * 300  # Exceeds max_length=255
        user = UserFactory.create_user(name=long_name)
        assert len(user.name) == 300  # Model doesn't enforce, database would
        
        # Test long email
        long_email = "a" * 250 + "@example.com"  # Exceeds max_length=255
        user = UserFactory.create_user(email=long_email)
        assert len(user.email) > 255  # Model doesn't enforce, database would
