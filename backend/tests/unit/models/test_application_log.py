"""
Tests for application log models.
"""

import pytest
from datetime import datetime
from typing import Dict, Any

from app.models.application_log import (
    ApplicationLog, ApplicationLogCreate, LogLevel, EventType,
    ApplicationLogResponse, ApplicationLogDetailResponse, LogFilterParams
)
from app.core.timezone_utils import utc_now


class TestApplicationLogModels:
    """Test application log models."""

    def test_log_level_enum(self):
        """Test LogLevel enum values."""
        assert LogLevel.DEBUG == "debug"
        assert LogLevel.INFO == "info"
        assert LogLevel.WARNING == "warning"
        assert LogLevel.ERROR == "error"
        assert LogLevel.CRITICAL == "critical"

    def test_event_type_enum(self):
        """Test EventType enum values."""
        assert EventType.USER_LOGIN == "user_login"
        assert EventType.USER_LOGOUT == "user_logout"
        assert EventType.AGENT_CREATE == "agent_create"
        assert EventType.AGENT_EXECUTE == "agent_execute"
        assert EventType.TEST_START == "test_start"
        assert EventType.TEST_COMPLETE == "test_complete"
        assert EventType.SYSTEM_ERROR == "system_error"

    def test_application_log_create_model(self):
        """Test ApplicationLogCreate model."""
        log_data = ApplicationLogCreate(
            level=LogLevel.INFO,
            event_type=EventType.USER_LOGIN,
            message="User logged in successfully",
            user_id=1,
            session_id="session_123",
            request_id="req_456",
            source_module="auth",
            source_function="login_user",
            agent_id="agent_789",
            execution_time_ms=150.5,
            metadata={"email": "<EMAIL>"},
            tags=["authentication", "success"],
            ip_address="***********",
            user_agent="Mozilla/5.0"
        )

        assert log_data.level == LogLevel.INFO
        assert log_data.event_type == EventType.USER_LOGIN
        assert log_data.message == "User logged in successfully"
        assert log_data.user_id == 1
        assert log_data.session_id == "session_123"
        assert log_data.request_id == "req_456"
        assert log_data.source_module == "auth"
        assert log_data.source_function == "login_user"
        assert log_data.agent_id == "agent_789"
        assert log_data.execution_time_ms == 150.5
        assert log_data.metadata == {"email": "<EMAIL>"}
        assert log_data.tags == ["authentication", "success"]
        assert log_data.ip_address == "***********"
        assert log_data.user_agent == "Mozilla/5.0"

    def test_application_log_create_minimal(self):
        """Test ApplicationLogCreate with minimal required fields."""
        log_data = ApplicationLogCreate(
            level=LogLevel.ERROR,
            event_type=EventType.SYSTEM_ERROR,
            message="System error occurred"
        )

        assert log_data.level == LogLevel.ERROR
        assert log_data.event_type == EventType.SYSTEM_ERROR
        assert log_data.message == "System error occurred"
        assert log_data.user_id is None
        assert log_data.session_id is None
        assert log_data.metadata is None

    def test_application_log_response_model(self):
        """Test ApplicationLogResponse model."""
        response = ApplicationLogResponse(
            id=1,
            uuid="log_uuid_123",
            level=LogLevel.WARNING,
            event_type=EventType.AGENT_EXECUTE,
            message="Agent execution warning",
            user_id=2,
            session_id="session_456",
            request_id="req_789",
            source_module="agents",
            source_function="execute_agent",
            execution_time_ms=2500.0,
            agent_id="agent_abc",
            test_id="test_def",
            template_id="template_ghi",
            error_code="timeout",
            error_type="TimeoutError",
            ip_address="********",
            timestamp=utc_now(),
            created_at=utc_now()
        )

        assert response.id == 1
        assert response.uuid == "log_uuid_123"
        assert response.level == LogLevel.WARNING
        assert response.event_type == EventType.AGENT_EXECUTE
        assert response.message == "Agent execution warning"
        assert response.user_id == 2
        assert response.agent_id == "agent_abc"
        assert response.test_id == "test_def"
        assert response.template_id == "template_ghi"
        assert response.error_code == "timeout"
        assert response.error_type == "TimeoutError"

    def test_application_log_detail_response_model(self):
        """Test ApplicationLogDetailResponse model."""
        detail_response = ApplicationLogDetailResponse(
            id=1,
            uuid="log_uuid_123",
            level=LogLevel.ERROR,
            event_type=EventType.SYSTEM_ERROR,
            message="Detailed error information",
            user_id=3,
            session_id="session_789",
            request_id="req_abc",
            source_module="system",
            source_function="process_request",
            source_file="/app/system/processor.py",
            source_line=42,
            request_method="POST",
            request_path="/api/v1/test",
            request_headers={"Content-Type": "application/json"},
            request_body={"test": "data"},
            response_status=500,
            response_headers={"Content-Type": "application/json"},
            response_body={"error": "Internal server error"},
            execution_time_ms=1000.0,
            memory_usage_mb=128.5,
            cpu_usage_percent=75.2,
            agent_id="agent_xyz",
            test_id="test_123",
            template_id="template_456",
            api_key_id=5,
            error_code="internal_error",
            error_type="InternalServerError",
            stack_trace="Traceback (most recent call last):\n  File...",
            metadata={"additional": "info"},
            tags=["error", "system"],
            ip_address="**********",
            user_agent="TestClient/1.0",
            timestamp=utc_now(),
            created_at=utc_now()
        )

        assert detail_response.source_file == "/app/system/processor.py"
        assert detail_response.source_line == 42
        assert detail_response.request_method == "POST"
        assert detail_response.request_path == "/api/v1/test"
        assert detail_response.request_headers == {"Content-Type": "application/json"}
        assert detail_response.response_status == 500
        assert detail_response.memory_usage_mb == 128.5
        assert detail_response.cpu_usage_percent == 75.2
        assert detail_response.api_key_id == 5
        assert detail_response.stack_trace.startswith("Traceback")
        assert detail_response.metadata == {"additional": "info"}
        assert detail_response.tags == ["error", "system"]

    def test_log_filter_params_model(self):
        """Test LogFilterParams model."""
        filters = LogFilterParams(
            level=LogLevel.INFO,
            event_type=EventType.USER_LOGIN,
            user_id=1,
            agent_id="agent_123",
            test_id="test_456",
            template_id="template_789",
            source_module="auth",
            error_code="invalid_credentials",
            start_date=datetime(2023, 1, 1),
            end_date=datetime(2023, 12, 31),
            search_query="login failed",
            tags=["authentication", "failure"],
            ip_address="***********00"
        )

        assert filters.level == LogLevel.INFO
        assert filters.event_type == EventType.USER_LOGIN
        assert filters.user_id == 1
        assert filters.agent_id == "agent_123"
        assert filters.test_id == "test_456"
        assert filters.template_id == "template_789"
        assert filters.source_module == "auth"
        assert filters.error_code == "invalid_credentials"
        assert filters.search_query == "login failed"
        assert filters.tags == ["authentication", "failure"]
        assert filters.ip_address == "***********00"

    def test_log_filter_params_empty(self):
        """Test LogFilterParams with no filters."""
        filters = LogFilterParams()

        assert filters.level is None
        assert filters.event_type is None
        assert filters.user_id is None
        assert filters.agent_id is None
        assert filters.search_query is None
        assert filters.tags is None

    def test_application_log_model_validation(self):
        """Test ApplicationLog model field validation."""
        # Test that required fields are enforced
        with pytest.raises(ValueError):
            ApplicationLogCreate()  # Missing required fields

        # Test that enum values are validated
        with pytest.raises(ValueError):
            ApplicationLogCreate(
                level="invalid_level",  # Invalid enum value
                event_type=EventType.USER_LOGIN,
                message="Test message"
            )

        with pytest.raises(ValueError):
            ApplicationLogCreate(
                level=LogLevel.INFO,
                event_type="invalid_event",  # Invalid enum value
                message="Test message"
            )

    def test_model_serialization(self):
        """Test model serialization to dict."""
        log_data = ApplicationLogCreate(
            level=LogLevel.INFO,
            event_type=EventType.AGENT_CREATE,
            message="Agent created",
            user_id=1,
            metadata={"key": "value"},
            tags=["agent", "creation"]
        )

        data_dict = log_data.model_dump()
        
        assert data_dict["level"] == "info"
        assert data_dict["event_type"] == "agent_create"
        assert data_dict["message"] == "Agent created"
        assert data_dict["user_id"] == 1
        assert data_dict["metadata"] == {"key": "value"}
        assert data_dict["tags"] == ["agent", "creation"]

    def test_model_deserialization(self):
        """Test model deserialization from dict."""
        data_dict = {
            "level": "error",
            "event_type": "system_error",
            "message": "System failure",
            "user_id": 2,
            "error_code": "sys_fail",
            "metadata": {"error_details": "Database connection failed"}
        }

        log_data = ApplicationLogCreate(**data_dict)
        
        assert log_data.level == LogLevel.ERROR
        assert log_data.event_type == EventType.SYSTEM_ERROR
        assert log_data.message == "System failure"
        assert log_data.user_id == 2
        assert log_data.error_code == "sys_fail"
        assert log_data.metadata == {"error_details": "Database connection failed"}
