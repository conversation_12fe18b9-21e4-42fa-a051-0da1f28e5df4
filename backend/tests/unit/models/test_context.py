"""
Unit tests for context models.
"""

import pytest
from pydantic import ValidationError

from app.models.context import (
    ContextPlaceholder,
    ContextAwareTeamMember,
    ContextAwareWorkflowStep,
    ContextAwareWorkflow,
    ContextAwareTeamPlan,
    RuntimeContext,
    ContextManager,
    validate_team_plan_context_structure,
    ensure_context_compatibility
)


class TestContextPlaceholder:
    """Test cases for ContextPlaceholder model."""

    def test_valid_semantic_placeholder(self):
        """Test creating a valid semantic context placeholder."""
        placeholder = ContextPlaceholder(
            placeholder_name="{data_collector.validated_dataset}",
            source_agent_role="data_collector",
            semantic_description="Cleaned and validated dataset ready for analysis",
            source_step="data_collection"
        )

        assert placeholder.placeholder_name == "{data_collector.validated_dataset}"
        assert placeholder.source_agent_role == "data_collector"
        assert placeholder.semantic_description == "Cleaned and validated dataset ready for analysis"
        assert placeholder.source_step == "data_collection"
        assert placeholder.is_semantic_format() is True
        assert placeholder.get_agent_role() == "data_collector"
        assert placeholder.get_output_type() == "validated_dataset"

    def test_valid_legacy_placeholder(self):
        """Test creating a valid legacy context placeholder for backward compatibility."""
        placeholder = ContextPlaceholder(
            placeholder_name="{user_input}",
            source_agent_role="user",
            semantic_description="User's original input and requirements",
            source_step="user_input",
            description="User's original input"  # Legacy field
        )

        assert placeholder.placeholder_name == "{user_input}"
        assert placeholder.source_agent_role == "user"
        assert placeholder.semantic_description == "User's original input and requirements"
        assert placeholder.source_step == "user_input"
        assert placeholder.description == "User's original input"
        assert placeholder.is_semantic_format() is False

    def test_invalid_placeholder_name_without_braces(self):
        """Test that placeholder name must have curly braces."""
        with pytest.raises(ValidationError) as exc_info:
            ContextPlaceholder(
                placeholder_name="user_input",
                source_agent_role="user",
                semantic_description="User's original input",
                source_step="user_input"
            )

        assert "must be wrapped in curly braces" in str(exc_info.value)

    def test_invalid_placeholder_name_partial_braces(self):
        """Test that placeholder name must have both opening and closing braces."""
        with pytest.raises(ValidationError):
            ContextPlaceholder(
                placeholder_name="{user_input",
                source_agent_role="user",
                semantic_description="User's original input",
                source_step="user_input"
            )

        with pytest.raises(ValidationError):
            ContextPlaceholder(
                placeholder_name="user_input}",
                source_agent_role="user",
                semantic_description="User's original input",
                source_step="user_input"
            )

    def test_invalid_semantic_placeholder_format(self):
        """Test validation of semantic placeholder format."""
        with pytest.raises(ValidationError) as exc_info:
            ContextPlaceholder(
                placeholder_name="{agent.role.extra.part}",
                source_agent_role="agent",
                semantic_description="Invalid format with too many parts",
                source_step="test_step"
            )

        assert "should be {agent_role.output_type}" in str(exc_info.value)

    def test_invalid_semantic_description_too_short(self):
        """Test that semantic description must be meaningful."""
        with pytest.raises(ValidationError) as exc_info:
            ContextPlaceholder(
                placeholder_name="{agent.data}",
                source_agent_role="agent",
                semantic_description="hi",  # Too short (2 characters)
                source_step="test_step"
            )

        assert "at least 5 characters long" in str(exc_info.value)

    def test_semantic_placeholder_methods(self):
        """Test semantic placeholder helper methods."""
        # Test semantic format
        semantic_placeholder = ContextPlaceholder(
            placeholder_name="{data_analyst.insights}",
            source_agent_role="data_analyst",
            semantic_description="Analysis insights and findings",
            source_step="analysis"
        )

        assert semantic_placeholder.is_semantic_format() is True
        assert semantic_placeholder.get_agent_role() == "data_analyst"
        assert semantic_placeholder.get_output_type() == "insights"

        # Test legacy format
        legacy_placeholder = ContextPlaceholder(
            placeholder_name="{user_input}",
            source_agent_role="user",
            semantic_description="User's original input",
            source_step="user_input"
        )

        assert legacy_placeholder.is_semantic_format() is False
        assert legacy_placeholder.get_agent_role() is None
        assert legacy_placeholder.get_output_type() is None


class TestContextAwareTeamMember:
    """Test cases for ContextAwareTeamMember model."""

    def test_valid_team_member(self):
        """Test creating a valid context-aware team member."""
        placeholder = ContextPlaceholder(
            placeholder_name="{data_analyst.insights}",
            source_agent_role="data_analyst",
            semantic_description="Analysis insights and findings from data analyst",
            source_step="analysis",
            description="Previous analysis results"  # Legacy field
        )
        
        member = ContextAwareTeamMember(
            name="Data Scientist",
            role="scientist",
            description="Analyzes data and builds models",
            system_prompt="You are a data scientist. Use {previous_analysis} for context.",
            capabilities=["machine_learning", "statistics"],
            tools=["python", "r", "sql"],
            model="gpt-4",
            temperature=0.7,
            max_tokens=2000,
            context_placeholders=[placeholder]
        )
        
        assert member.name == "Data Scientist"
        assert member.role == "scientist"
        assert len(member.context_placeholders) == 1
        assert member.context_placeholders[0].placeholder_name == "{data_analyst.insights}"

    def test_team_member_with_defaults(self):
        """Test creating team member with default values."""
        member = ContextAwareTeamMember(
            name="Analyst",
            role="analyst",
            description="Data analyst",
            system_prompt="You are an analyst."
        )
        
        assert member.capabilities == []
        assert member.tools == []
        assert member.model == "gpt-4"
        assert member.temperature == 0.7
        assert member.max_tokens == 2000
        assert member.context_placeholders == []

    def test_invalid_temperature_range(self):
        """Test that temperature must be within valid range."""
        with pytest.raises(ValidationError):
            ContextAwareTeamMember(
                name="Analyst",
                role="analyst",
                description="Data analyst",
                system_prompt="You are an analyst.",
                temperature=3.0  # Invalid: > 2.0
            )
        
        with pytest.raises(ValidationError):
            ContextAwareTeamMember(
                name="Analyst",
                role="analyst",
                description="Data analyst",
                system_prompt="You are an analyst.",
                temperature=-0.1  # Invalid: < 0.0
            )

    def test_invalid_max_tokens(self):
        """Test that max_tokens must be positive."""
        with pytest.raises(ValidationError):
            ContextAwareTeamMember(
                name="Analyst",
                role="analyst",
                description="Data analyst",
                system_prompt="You are an analyst.",
                max_tokens=0  # Invalid: must be > 0
            )


class TestContextAwareWorkflowStep:
    """Test cases for ContextAwareWorkflowStep model."""

    def test_valid_workflow_step(self):
        """Test creating a valid context-aware workflow step."""
        step = ContextAwareWorkflowStep(
            name="Data Analysis",
            description="Analyze the collected data",
            assignee="Data Analyst",
            inputs=["raw_data", "requirements"],
            outputs=["analysis_results", "insights"],
            context_dependencies=["data_collection", "preprocessing"]
        )
        
        assert step.name == "Data Analysis"
        assert step.description == "Analyze the collected data"
        assert step.assignee == "Data Analyst"
        assert len(step.inputs) == 2
        assert len(step.outputs) == 2
        assert len(step.context_dependencies) == 2

    def test_workflow_step_with_defaults(self):
        """Test creating workflow step with default values."""
        step = ContextAwareWorkflowStep(
            name="Simple Step",
            description="A simple step",
            assignee="Worker"
        )
        
        assert step.inputs == []
        assert step.outputs == []
        assert step.context_dependencies == []


class TestContextAwareWorkflow:
    """Test cases for ContextAwareWorkflow model."""

    def test_valid_workflow(self):
        """Test creating a valid context-aware workflow."""
        step1 = ContextAwareWorkflowStep(
            name="Step 1",
            description="First step",
            assignee="Worker 1"
        )
        step2 = ContextAwareWorkflowStep(
            name="Step 2",
            description="Second step",
            assignee="Worker 2",
            context_dependencies=["Step 1"]
        )
        
        workflow = ContextAwareWorkflow(steps=[step1, step2])
        
        assert len(workflow.steps) == 2
        assert workflow.steps[0].name == "Step 1"
        assert workflow.steps[1].context_dependencies == ["Step 1"]

    def test_empty_workflow(self):
        """Test creating an empty workflow."""
        workflow = ContextAwareWorkflow()
        assert workflow.steps == []


class TestContextAwareTeamPlan:
    """Test cases for ContextAwareTeamPlan model."""

    def test_valid_team_plan(self):
        """Test creating a valid context-aware team plan."""
        member = ContextAwareTeamMember(
            name="Analyst",
            role="analyst",
            description="Data analyst",
            system_prompt="You are an analyst."
        )
        
        step = ContextAwareWorkflowStep(
            name="Analysis",
            description="Analyze data",
            assignee="Analyst"
        )
        
        workflow = ContextAwareWorkflow(steps=[step])
        
        plan = ContextAwareTeamPlan(
            team_name="Analysis Team",
            description="Team for data analysis",
            objective="Analyze business data",
            domain="technical",
            complexity="intermediate",
            team_members=[member],
            workflow=workflow
        )
        
        assert plan.team_name == "Analysis Team"
        assert plan.domain == "technical"
        assert plan.complexity == "intermediate"
        assert len(plan.team_members) == 1
        assert len(plan.workflow.steps) == 1

    def test_team_plan_with_defaults(self):
        """Test creating team plan with default values."""
        member = ContextAwareTeamMember(
            name="Worker",
            role="worker",
            description="Generic worker",
            system_prompt="You are a worker."
        )
        
        workflow = ContextAwareWorkflow()
        
        plan = ContextAwareTeamPlan(
            team_name="Default Team",
            description="Default team",
            objective="Default objective",
            team_members=[member],
            workflow=workflow
        )
        
        assert plan.domain == "general"
        assert plan.complexity == "intermediate"
        assert plan.created_at is None
        assert plan.generation_method is None


class TestRuntimeContext:
    """Test cases for RuntimeContext model."""

    def test_valid_runtime_context(self):
        """Test creating a valid runtime context."""
        context = RuntimeContext(
            execution_id="exec_123",
            step_name="analysis",
            member_name="analyst",
            context_data={"input": "data", "output": "results"},
            timestamp="2025-07-17T02:00:00Z",
            input_tokens=100,
            output_tokens=150,
            processing_time_ms=2000
        )
        
        assert context.execution_id == "exec_123"
        assert context.step_name == "analysis"
        assert context.member_name == "analyst"
        assert context.context_data["output"] == "results"
        assert context.input_tokens == 100
        assert context.output_tokens == 150
        assert context.processing_time_ms == 2000


class TestContextManager:
    """Test cases for ContextManager model."""

    def test_context_manager_creation(self):
        """Test creating a context manager."""
        manager = ContextManager(execution_id="exec_123")
        
        assert manager.execution_id == "exec_123"
        assert manager.contexts == {}
        assert manager.placeholder_mappings == {}

    def test_add_context(self):
        """Test adding context to manager."""
        manager = ContextManager(execution_id="exec_123")
        
        context = RuntimeContext(
            execution_id="exec_123",
            step_name="step1",
            member_name="member1",
            context_data={"output": "result1"},
            timestamp="2025-07-17T02:00:00Z"
        )
        
        manager.add_context(context)
        
        assert "step1" in manager.contexts
        assert manager.contexts["step1"] is context

    def test_get_context(self):
        """Test getting context from manager."""
        manager = ContextManager(execution_id="exec_123")
        
        context = RuntimeContext(
            execution_id="exec_123",
            step_name="step1",
            member_name="member1",
            context_data={"output": "result1"},
            timestamp="2025-07-17T02:00:00Z"
        )
        
        manager.add_context(context)
        
        retrieved = manager.get_context("step1")
        assert retrieved is context
        
        missing = manager.get_context("nonexistent")
        assert missing is None

    def test_resolve_placeholder_semantic(self):
        """Test resolving semantic placeholders."""
        manager = ContextManager(execution_id="exec_123")

        # Create context with semantic data
        context = RuntimeContext(
            execution_id="exec_123",
            step_name="data_analysis",
            member_name="data_analyst",
            context_data={
                "output": "Analysis complete",
                "insights": "Data shows positive trends",
                "validated_data": "Cleaned dataset with 1000 records",
                "user_input": "Analyze sales data"
            },
            timestamp="2025-07-17T02:00:00Z"
        )

        manager.add_context(context)

        # Test semantic placeholder resolution
        assert manager.resolve_placeholder("{data_analyst.insights}", "data_analysis", "data_analyst") == "Data shows positive trends"
        assert manager.resolve_placeholder("{data_analyst.validated_dataset}", "data_analysis", "data_analyst") == "Cleaned dataset with 1000 records"
        assert manager.resolve_placeholder("{user.requirements}", "data_analysis", "user") == "Analyze sales data"

        # Test legacy placeholder resolution (backward compatibility)
        assert manager.resolve_placeholder("{previous_analysis}", "data_analysis") == "Analysis complete"
        assert manager.resolve_placeholder("{user_input}", "data_analysis") == "Analyze sales data"

        # Test fallback behavior
        assert manager.resolve_placeholder("{unknown.output}", "data_analysis") == "Analysis complete"
        assert manager.resolve_placeholder("{any.data}", "nonexistent") is None

    def test_resolve_placeholder_legacy(self):
        """Test resolving legacy placeholders for backward compatibility."""
        manager = ContextManager(execution_id="exec_123")

        context = RuntimeContext(
            execution_id="exec_123",
            step_name="analysis",
            member_name="analyst",
            context_data={"output": "Analysis complete", "user_input": "Analyze this data"},
            timestamp="2025-07-17T02:00:00Z"
        )

        manager.add_context(context)

        # Test legacy placeholder types
        assert manager.resolve_placeholder("{previous_analysis}", "analysis") == "Analysis complete"
        assert manager.resolve_placeholder("{user_requirements}", "analysis") == "Analyze this data"
        assert manager.resolve_placeholder("{user_input}", "analysis") == "Analyze this data"
        assert manager.resolve_placeholder("{results}", "analysis") == "Analysis complete"
        assert manager.resolve_placeholder("{unknown}", "analysis") == "Analysis complete"  # Falls back to output
        assert manager.resolve_placeholder("{any}", "nonexistent") is None

    def test_replace_placeholders_semantic(self):
        """Test replacing semantic placeholders in text."""
        manager = ContextManager(execution_id="exec_123")

        context = RuntimeContext(
            execution_id="exec_123",
            step_name="data_analysis",
            member_name="data_analyst",
            context_data={
                "output": "Analysis complete",
                "insights": "Data shows positive trends",
                "validated_data": "Cleaned dataset"
            },
            timestamp="2025-07-17T02:00:00Z"
        )

        manager.add_context(context)

        placeholders = [
            ContextPlaceholder(
                placeholder_name="{data_analyst.insights}",
                source_agent_role="data_analyst",
                semantic_description="Analysis insights from data analyst",
                source_step="data_analysis"
            ),
            ContextPlaceholder(
                placeholder_name="{data_analyst.validated_data}",
                source_agent_role="data_analyst",
                semantic_description="Validated dataset from data analyst",
                source_step="data_analysis"
            )
        ]

        text = "Based on {data_analyst.insights} from {data_analyst.validated_data}, we can conclude..."
        result = manager.replace_placeholders(text, placeholders)

        assert result == "Based on Data shows positive trends from Cleaned dataset, we can conclude..."

    def test_replace_placeholders_legacy(self):
        """Test replacing legacy placeholders for backward compatibility."""
        manager = ContextManager(execution_id="exec_123")

        context = RuntimeContext(
            execution_id="exec_123",
            step_name="analysis",
            member_name="analyst",
            context_data={"output": "Data shows positive trends"},
            timestamp="2025-07-17T02:00:00Z"
        )

        manager.add_context(context)

        placeholders = [
            ContextPlaceholder(
                placeholder_name="{previous_analysis}",
                source_agent_role="analyst",
                semantic_description="Previous analysis results",
                source_step="analysis",
                description="Previous analysis"  # Legacy field
            )
        ]

        text = "Based on {previous_analysis}, we can conclude..."
        result = manager.replace_placeholders(text, placeholders)

        assert result == "Based on Data shows positive trends, we can conclude..."

    def test_replace_placeholders_missing_data(self):
        """Test replacing placeholders when data is missing."""
        manager = ContextManager(execution_id="exec_123")

        placeholders = [
            ContextPlaceholder(
                placeholder_name="{missing_agent.data}",
                source_agent_role="missing_agent",
                semantic_description="Missing data from nonexistent agent",
                source_step="nonexistent"
            )
        ]

        text = "Based on {missing_agent.data}, we can conclude..."
        result = manager.replace_placeholders(text, placeholders)

        assert "[Missing data from nonexistent agent - no data available from missing_agent]" in result


class TestUtilityFunctions:
    """Test cases for utility functions."""

    def test_validate_team_plan_context_structure_valid(self):
        """Test validating a valid team plan structure."""
        team_plan = {
            "team_name": "Test Team",
            "description": "Test description",
            "objective": "Test objective",
            "domain": "technical",
            "complexity": "intermediate",
            "team_members": [
                {
                    "name": "Member 1",
                    "role": "role1",
                    "description": "Description",
                    "system_prompt": "Prompt",
                    "context_placeholders": []
                }
            ],
            "workflow": {
                "steps": [
                    {
                        "name": "Step 1",
                        "description": "Description",
                        "assignee": "Member 1",
                        "context_dependencies": []
                    }
                ]
            }
        }
        
        assert validate_team_plan_context_structure(team_plan) is True

    def test_validate_team_plan_context_structure_invalid(self):
        """Test validating an invalid team plan structure."""
        invalid_plan = {
            "team_name": "Test Team"
            # Missing required fields
        }
        
        assert validate_team_plan_context_structure(invalid_plan) is False

    def test_ensure_context_compatibility(self):
        """Test ensuring context compatibility for existing team plans."""
        team_plan = {
            "team_members": [
                {
                    "name": "Member 1",
                    "role": "role1"
                    # Missing context_placeholders
                }
            ],
            "workflow": {
                "steps": [
                    {
                        "name": "Step 1",
                        "description": "Description"
                        # Missing context_dependencies
                    }
                ]
            }
        }
        
        result = ensure_context_compatibility(team_plan)
        
        assert "context_placeholders" in result["team_members"][0]
        assert result["team_members"][0]["context_placeholders"] == []
        assert "context_dependencies" in result["workflow"]["steps"][0]
        assert result["workflow"]["steps"][0]["context_dependencies"] == []

    def test_ensure_context_compatibility_preserves_existing(self):
        """Test that existing context fields are preserved."""
        team_plan = {
            "team_members": [
                {
                    "name": "Member 1",
                    "role": "role1",
                    "context_placeholders": [{"placeholder_name": "{test}"}]
                }
            ],
            "workflow": {
                "steps": [
                    {
                        "name": "Step 1",
                        "description": "Description",
                        "context_dependencies": ["previous_step"]
                    }
                ]
            }
        }
        
        result = ensure_context_compatibility(team_plan)
        
        assert len(result["team_members"][0]["context_placeholders"]) == 1
        assert result["team_members"][0]["context_placeholders"][0]["placeholder_name"] == "{test}"
        assert result["workflow"]["steps"][0]["context_dependencies"] == ["previous_step"]
