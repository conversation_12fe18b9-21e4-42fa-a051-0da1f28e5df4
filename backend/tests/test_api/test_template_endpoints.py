"""
Tests for template API endpoints.
"""

import pytest
from httpx import As<PERSON><PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.planning import (
    Template, TemplateCategory, TemplateDifficulty, 
    TemplateVisibility, TemplateStatus
)
from app.models.user import User


class TestTemplateEndpoints:
    """Test template API endpoints."""

    @pytest.fixture
    async def sample_user(self, db_session: AsyncSession) -> User:
        """Create a sample user for testing."""
        user = User(
            email="<EMAIL>",
            name="Test User",
            hashed_password="hashed_password",
            is_active=True,
            is_verified=True
        )
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    @pytest.fixture
    async def sample_template(self, db_session: AsyncSession, sample_user: User) -> Template:
        """Create a sample template for testing."""
        template = Template(
            template_id="template_test123",
            name="Test Template",
            description="A test template",
            category=TemplateCategory.TECHNICAL,
            difficulty=TemplateDifficulty.INTERMEDIATE,
            visibility=TemplateVisibility.PRIVATE,
            status=TemplateStatus.ACTIVE,
            prompt_template="Test prompt template",
            team_structure_template={
                "team_name": "Test Team",
                "team_members": [{"name": "Agent", "role": "analyst"}]
            },
            user_id=sample_user.id,
            author_name=sample_user.name,
            tags=["test", "api"],
            usage_count=5,
            rating=4.0,
            rating_count=2
        )
        db_session.add(template)
        await db_session.commit()
        await db_session.refresh(template)
        return template

    @pytest.fixture
    async def public_template(self, db_session: AsyncSession, sample_user: User) -> Template:
        """Create a public template for testing."""
        template = Template(
            template_id="template_public123",
            name="Public Test Template",
            description="A public test template",
            category=TemplateCategory.BUSINESS,
            difficulty=TemplateDifficulty.BEGINNER,
            visibility=TemplateVisibility.PUBLIC,
            status=TemplateStatus.ACTIVE,
            prompt_template="Public test prompt template",
            team_structure_template={
                "team_name": "Public Test Team",
                "team_members": [{"name": "Public Agent", "role": "assistant"}]
            },
            user_id=sample_user.id,
            author_name=sample_user.name,
            tags=["public", "test"],
            usage_count=10,
            rating=4.5,
            rating_count=5
        )
        db_session.add(template)
        await db_session.commit()
        await db_session.refresh(template)
        return template

    async def test_list_templates(
        self, 
        client: AsyncClient, 
        sample_user: User, 
        sample_template: Template,
        public_template: Template,
        auth_headers: dict
    ):
        """Test listing templates."""
        response = await client.get("/api/v1/templates", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        
        # Should include user's private template and public templates
        template_ids = [t["template_id"] for t in data]
        assert sample_template.template_id in template_ids
        assert public_template.template_id in template_ids

    async def test_list_templates_with_filters(
        self, 
        client: AsyncClient, 
        sample_template: Template,
        auth_headers: dict
    ):
        """Test listing templates with filters."""
        # Filter by category
        response = await client.get(
            f"/api/v1/templates?category={TemplateCategory.TECHNICAL}",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        for template in data:
            assert template["category"] == TemplateCategory.TECHNICAL

    async def test_get_template(
        self, 
        client: AsyncClient, 
        sample_template: Template,
        auth_headers: dict
    ):
        """Test getting a specific template."""
        response = await client.get(
            f"/api/v1/templates/{sample_template.template_id}",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["template_id"] == sample_template.template_id
        assert data["name"] == sample_template.name
        assert data["is_owner"] is True
        assert data["can_edit"] is True

    async def test_get_nonexistent_template(
        self, 
        client: AsyncClient,
        auth_headers: dict
    ):
        """Test getting a non-existent template."""
        response = await client.get(
            "/api/v1/templates/nonexistent",
            headers=auth_headers
        )
        
        assert response.status_code == 404

    async def test_create_template(
        self, 
        client: AsyncClient,
        auth_headers: dict
    ):
        """Test creating a new template."""
        template_data = {
            "name": "New Test Template",
            "description": "A new test template",
            "category": TemplateCategory.CREATIVE,
            "difficulty": TemplateDifficulty.ADVANCED,
            "prompt_template": "New test prompt template",
            "team_structure_template": {
                "team_name": "New Test Team",
                "team_members": [{"name": "New Agent", "role": "creator"}]
            },
            "tags": ["new", "test"],
            "use_case": "Testing new template creation"
        }
        
        response = await client.post(
            "/api/v1/templates",
            json=template_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == template_data["name"]
        assert data["category"] == template_data["category"]
        assert data["is_owner"] is True
        assert data["can_edit"] is True

    async def test_create_template_validation_error(
        self, 
        client: AsyncClient,
        auth_headers: dict
    ):
        """Test creating template with validation errors."""
        template_data = {
            "name": "",  # Empty name should fail
            "description": "A test template",
            "category": TemplateCategory.TECHNICAL,
            "difficulty": TemplateDifficulty.INTERMEDIATE,
            "prompt_template": "Test prompt",
            "team_structure_template": {}
        }
        
        response = await client.post(
            "/api/v1/templates",
            json=template_data,
            headers=auth_headers
        )
        
        assert response.status_code == 422

    async def test_update_template(
        self, 
        client: AsyncClient, 
        sample_template: Template,
        auth_headers: dict
    ):
        """Test updating a template."""
        update_data = {
            "name": "Updated Test Template",
            "description": "Updated description",
            "visibility": TemplateVisibility.PUBLIC
        }
        
        response = await client.put(
            f"/api/v1/templates/{sample_template.template_id}",
            json=update_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == update_data["name"]
        assert data["description"] == update_data["description"]
        assert data["visibility"] == update_data["visibility"]

    async def test_update_nonexistent_template(
        self, 
        client: AsyncClient,
        auth_headers: dict
    ):
        """Test updating a non-existent template."""
        update_data = {"name": "Updated Name"}
        
        response = await client.put(
            "/api/v1/templates/nonexistent",
            json=update_data,
            headers=auth_headers
        )
        
        assert response.status_code == 404

    async def test_delete_template(
        self, 
        client: AsyncClient, 
        sample_template: Template,
        auth_headers: dict
    ):
        """Test deleting a template (soft delete)."""
        response = await client.delete(
            f"/api/v1/templates/{sample_template.template_id}",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "deleted successfully" in data["message"]

    async def test_duplicate_template(
        self, 
        client: AsyncClient, 
        public_template: Template,
        auth_headers: dict
    ):
        """Test duplicating a template."""
        response = await client.post(
            f"/api/v1/templates/{public_template.template_id}/duplicate",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == f"{public_template.name} (Copy)"
        assert data["parent_template_id"] == public_template.template_id
        assert data["is_owner"] is True

    async def test_search_templates(
        self, 
        client: AsyncClient, 
        sample_template: Template,
        auth_headers: dict
    ):
        """Test searching templates."""
        response = await client.get(
            "/api/v1/templates/search?q=test",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        
        # Should find templates with "test" in name or description
        found_template = next((t for t in data if t["template_id"] == sample_template.template_id), None)
        assert found_template is not None

    async def test_get_template_categories(
        self, 
        client: AsyncClient,
        auth_headers: dict
    ):
        """Test getting template categories."""
        response = await client.get(
            "/api/v1/templates/categories/list",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) > 0
        
        # Check structure
        for category in data:
            assert "value" in category
            assert "label" in category

    async def test_get_template_difficulties(
        self, 
        client: AsyncClient,
        auth_headers: dict
    ):
        """Test getting template difficulties."""
        response = await client.get(
            "/api/v1/templates/difficulties/list",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) == 4  # beginner, intermediate, advanced, expert

    async def test_get_popular_tags(
        self, 
        client: AsyncClient,
        auth_headers: dict
    ):
        """Test getting popular tags."""
        response = await client.get(
            "/api/v1/templates/tags/popular",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    async def test_get_template_stats(
        self, 
        client: AsyncClient,
        auth_headers: dict
    ):
        """Test getting template statistics."""
        response = await client.get(
            "/api/v1/templates/stats",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "overview" in data
        assert "categories" in data
        assert "difficulties" in data

    async def test_share_template(
        self, 
        client: AsyncClient, 
        sample_template: Template,
        auth_headers: dict
    ):
        """Test sharing a template."""
        response = await client.post(
            f"/api/v1/templates/{sample_template.template_id}/share?visibility=public",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "sharing updated" in data["message"]

    async def test_get_featured_templates(
        self, 
        client: AsyncClient,
        auth_headers: dict
    ):
        """Test getting featured templates."""
        response = await client.get(
            "/api/v1/templates/public/featured",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    async def test_get_community_templates(
        self, 
        client: AsyncClient,
        auth_headers: dict
    ):
        """Test getting community templates."""
        response = await client.get(
            "/api/v1/templates/community",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    async def test_unauthorized_access(self, client: AsyncClient):
        """Test unauthorized access to template endpoints."""
        response = await client.get("/api/v1/templates")
        assert response.status_code == 401

    async def test_template_permissions(
        self, 
        client: AsyncClient, 
        sample_template: Template,
        auth_headers: dict
    ):
        """Test template permission checks."""
        # Try to access another user's private template
        # This would require creating another user and template
        # For now, just test that the endpoint respects ownership
        
        response = await client.get(
            f"/api/v1/templates/{sample_template.template_id}",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["is_owner"] is True

    async def test_create_template_from_agent(
        self,
        client: AsyncClient,
        auth_headers: dict
    ):
        """Test creating template from agent."""
        # This would require an existing agent
        # For now, test the endpoint structure
        template_data = {
            "agent_id": "agent_123",
            "name": "Template from Agent",
            "description": "Created from an agent",
            "category": TemplateCategory.TECHNICAL,
            "difficulty": TemplateDifficulty.INTERMEDIATE,
            "tags": ["agent", "generated"]
        }

        response = await client.post(
            "/api/v1/templates/from-agent",
            json=template_data,
            headers=auth_headers
        )

        # This might fail if agent doesn't exist, but tests the endpoint
        assert response.status_code in [200, 404]
