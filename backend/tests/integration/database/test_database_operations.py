"""
Integration tests for database operations.
"""

import pytest
import json
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from app.models.agent import Agent, AgentStatus, AgentType
from app.models.planning import PlanningRequest, PlanningStatus
from tests.fixtures.factories import AgentFactory, PlanningRequestFactory, TeamPlanFactory


@pytest.mark.integration
class TestDatabaseOperations:
    """Test database operations with real database."""
    
    @pytest.mark.asyncio
    async def test_agent_crud_operations(self, test_session: AsyncSession):
        """Test Agent CRUD operations."""
        # Create
        agent_data = AgentFactory.create_agent_data(
            team_name="测试团队",
            description="这是一个测试团队"
        )
        agent = Agent(**agent_data)
        test_session.add(agent)
        await test_session.commit()
        await test_session.refresh(agent)
        
        # Verify creation
        assert agent.id is not None
        assert agent.created_at is not None
        assert agent.uuid is not None
        assert agent.team_name == "测试团队"
        
        # Read
        result = await test_session.execute(
            text("SELECT * FROM agents WHERE id = :id"),
            {"id": agent.id}
        )
        row = result.fetchone()
        assert row is not None
        assert row.team_name == "测试团队"
        
        # Update
        agent.status = AgentStatus.ACTIVE
        agent.usage_count = 10
        await test_session.commit()
        await test_session.refresh(agent)
        
        assert agent.status == AgentStatus.ACTIVE
        assert agent.usage_count == 10
        assert agent.updated_at is not None
        
        # Delete
        await test_session.delete(agent)
        await test_session.commit()
        
        # Verify deletion
        result = await test_session.execute(
            text("SELECT * FROM agents WHERE id = :id"),
            {"id": agent.id}
        )
        row = result.fetchone()
        assert row is None
    
    @pytest.mark.asyncio
    async def test_planning_request_crud_operations(self, test_session: AsyncSession):
        """Test PlanningRequest CRUD operations."""
        # Create
        team_plan = TeamPlanFactory.create_team_plan()
        request_data = PlanningRequestFactory.create_planning_request_data(
            user_description="我需要一个数据分析团队",
            status=PlanningStatus.COMPLETED,
            team_plan_json=json.dumps(team_plan, ensure_ascii=False)
        )
        planning_request = PlanningRequest(**request_data)
        test_session.add(planning_request)
        await test_session.commit()
        await test_session.refresh(planning_request)
        
        # Verify creation
        assert planning_request.id is not None
        assert planning_request.request_id is not None
        assert planning_request.user_description == "我需要一个数据分析团队"
        assert planning_request.status == PlanningStatus.COMPLETED
        
        # Verify JSON serialization
        parsed_plan = json.loads(planning_request.team_plan_json)
        assert parsed_plan["team_name"] == team_plan["team_name"]
        
        # Update
        planning_request.status = PlanningStatus.PROCESSING
        planning_request.started_at = datetime.utcnow()
        await test_session.commit()
        await test_session.refresh(planning_request)
        
        assert planning_request.status == PlanningStatus.PROCESSING
        assert planning_request.started_at is not None
        
        # Delete
        await test_session.delete(planning_request)
        await test_session.commit()
    
    @pytest.mark.asyncio
    async def test_database_transactions(self, test_session: AsyncSession):
        """Test database transaction handling."""
        # Test successful transaction
        agent1 = AgentFactory.create_agent(team_name="团队1")
        agent2 = AgentFactory.create_agent(team_name="团队2")
        
        test_session.add(agent1)
        test_session.add(agent2)
        await test_session.commit()
        
        # Verify both agents were created
        result = await test_session.execute(
            text("SELECT COUNT(*) as count FROM agents")
        )
        count = result.scalar()
        assert count >= 2
        
        # Test transaction rollback
        agent3 = AgentFactory.create_agent(team_name="团队3")
        test_session.add(agent3)
        
        # Rollback before commit
        await test_session.rollback()
        
        # Verify agent3 was not persisted
        result = await test_session.execute(
            text("SELECT * FROM agents WHERE team_name = '团队3'")
        )
        row = result.fetchone()
        assert row is None
    
    @pytest.mark.asyncio
    async def test_database_constraints(self, test_session: AsyncSession):
        """Test database constraints."""
        # Test unique constraint on agent_id
        agent1 = AgentFactory.create_agent(agent_id="unique_agent_001")
        test_session.add(agent1)
        await test_session.commit()
        
        # Try to create another agent with same agent_id
        agent2 = AgentFactory.create_agent(agent_id="unique_agent_001")
        test_session.add(agent2)
        
        with pytest.raises(Exception):  # Should raise integrity error
            await test_session.commit()
        
        await test_session.rollback()
    
    @pytest.mark.asyncio
    async def test_database_relationships(self, test_session: AsyncSession):
        """Test database relationships and foreign keys."""
        # Create related records
        agent = AgentFactory.create_agent()
        planning_request = PlanningRequestFactory.create_planning_request()
        
        test_session.add(agent)
        test_session.add(planning_request)
        await test_session.commit()
        
        # Verify records exist
        agent_result = await test_session.execute(
            text("SELECT COUNT(*) FROM agents")
        )
        planning_result = await test_session.execute(
            text("SELECT COUNT(*) FROM planning_requests")
        )
        
        assert agent_result.scalar() >= 1
        assert planning_result.scalar() >= 1
    
    @pytest.mark.asyncio
    async def test_database_indexes(self, test_session: AsyncSession):
        """Test database indexes for performance."""
        # Create multiple agents
        agents = []
        for i in range(10):
            agent = AgentFactory.create_agent(
                agent_id=f"agent_{i:03d}",
                team_name=f"团队_{i}"
            )
            agents.append(agent)
            test_session.add(agent)
        
        await test_session.commit()
        
        # Test index on agent_id (should be fast)
        import time
        start_time = time.time()
        
        result = await test_session.execute(
            text("SELECT * FROM agents WHERE agent_id = 'agent_005'")
        )
        row = result.fetchone()
        
        end_time = time.time()
        
        assert row is not None
        assert row.agent_id == "agent_005"
        # Query should be fast (less than 0.1 seconds)
        assert (end_time - start_time) < 0.1
    
    @pytest.mark.asyncio
    async def test_database_concurrent_access(self, test_engine):
        """Test concurrent database access."""
        from sqlalchemy.orm import sessionmaker
        from sqlalchemy.ext.asyncio import AsyncSession
        import asyncio
        
        AsyncSessionLocal = sessionmaker(
            test_engine, class_=AsyncSession, expire_on_commit=False
        )
        
        async def create_agent(session_factory, agent_id):
            async with session_factory() as session:
                agent = AgentFactory.create_agent(agent_id=agent_id)
                session.add(agent)
                await session.commit()
                return agent.id
        
        # Create multiple agents concurrently
        tasks = [
            create_agent(AsyncSessionLocal, f"concurrent_agent_{i}")
            for i in range(5)
        ]
        
        agent_ids = await asyncio.gather(*tasks)
        
        # Verify all agents were created
        assert len(agent_ids) == 5
        assert all(agent_id is not None for agent_id in agent_ids)
    
    @pytest.mark.asyncio
    async def test_database_data_types(self, test_session: AsyncSession):
        """Test various data types in database."""
        # Test with different data types
        now = datetime.utcnow()
        
        agent = AgentFactory.create_agent(
            usage_count=42,
            last_used=now,
            prompt_template="测试模板 with unicode: 🤖",
            system_prompt="System prompt with newlines\nand special chars: @#$%"
        )
        
        test_session.add(agent)
        await test_session.commit()
        await test_session.refresh(agent)
        
        # Verify data types are preserved
        assert isinstance(agent.usage_count, int)
        assert isinstance(agent.last_used, datetime)
        assert "🤖" in agent.prompt_template
        assert "\n" in agent.system_prompt
    
    @pytest.mark.asyncio
    async def test_database_json_fields(self, test_session: AsyncSession):
        """Test JSON field handling."""
        team_plan = TeamPlanFactory.create_team_plan()
        team_plan_json = json.dumps(team_plan, ensure_ascii=False)
        
        planning_request = PlanningRequestFactory.create_planning_request(
            team_plan_json=team_plan_json
        )
        
        test_session.add(planning_request)
        await test_session.commit()
        await test_session.refresh(planning_request)
        
        # Verify JSON is properly stored and retrieved
        assert planning_request.team_plan_json is not None
        parsed_plan = json.loads(planning_request.team_plan_json)
        
        assert parsed_plan["team_name"] == team_plan["team_name"]
        assert len(parsed_plan["team_members"]) == len(team_plan["team_members"])
        assert parsed_plan["workflow"]["steps"] == team_plan["workflow"]["steps"]
    
    @pytest.mark.asyncio
    async def test_database_performance(self, test_session: AsyncSession):
        """Test database performance with bulk operations."""
        import time
        
        # Test bulk insert performance
        agents = []
        for i in range(100):
            agent = AgentFactory.create_agent(
                agent_id=f"perf_agent_{i:03d}",
                team_name=f"性能测试团队_{i}"
            )
            agents.append(agent)
        
        start_time = time.time()
        
        # Add all agents
        for agent in agents:
            test_session.add(agent)
        
        await test_session.commit()
        
        end_time = time.time()
        
        # Bulk insert should be reasonably fast
        duration = end_time - start_time
        assert duration < 5.0  # Should complete within 5 seconds
        
        # Verify all agents were created
        result = await test_session.execute(
            text("SELECT COUNT(*) FROM agents WHERE agent_id LIKE 'perf_agent_%'")
        )
        count = result.scalar()
        assert count == 100
    
    @pytest.mark.asyncio
    async def test_database_error_handling(self, test_session: AsyncSession):
        """Test database error handling."""
        # Test handling of invalid data
        try:
            # Try to insert invalid enum value
            result = await test_session.execute(
                text("INSERT INTO agents (agent_id, team_name, description, agent_type, status) VALUES (:agent_id, :team_name, :description, :agent_type, :status)"),
                {
                    "agent_id": "invalid_agent",
                    "team_name": "测试",
                    "description": "测试",
                    "agent_type": "invalid_type",  # Invalid enum value
                    "status": "active"
                }
            )
            await test_session.commit()
            assert False, "Should have raised an error"
        except Exception:
            await test_session.rollback()
            # Error should be handled gracefully
    
    @pytest.mark.asyncio
    async def test_database_cleanup(self, test_session: AsyncSession):
        """Test database cleanup operations."""
        # Create test data
        agents = []
        for i in range(5):
            agent = AgentFactory.create_agent(
                agent_id=f"cleanup_agent_{i}",
                status=AgentStatus.DELETED if i % 2 == 0 else AgentStatus.ACTIVE
            )
            agents.append(agent)
            test_session.add(agent)
        
        await test_session.commit()
        
        # Test cleanup of deleted agents
        result = await test_session.execute(
            text("DELETE FROM agents WHERE status = 'deleted' AND agent_id LIKE 'cleanup_agent_%'")
        )
        await test_session.commit()
        
        # Verify cleanup
        result = await test_session.execute(
            text("SELECT COUNT(*) FROM agents WHERE agent_id LIKE 'cleanup_agent_%'")
        )
        remaining_count = result.scalar()
        assert remaining_count == 3  # Only active agents should remain
