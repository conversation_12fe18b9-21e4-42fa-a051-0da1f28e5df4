"""
Integration tests for context-aware agent execution.
"""

import pytest
import json
import uuid
from unittest.mock import Mock, AsyncMock, patch

from app.services.dynamic_loader import Config<PERSON>rivenAgent
from app.services.context_service import ContextService
from app.models.context import ensure_context_compatibility


class TestContextAwareExecution:
    """Integration tests for context-aware agent execution."""

    @pytest.fixture
    def sample_team_plan(self):
        """Create a sample team plan with context-aware features."""
        return {
            "team_name": "Context-Aware Analysis Team",
            "description": "Team that demonstrates context sharing",
            "objective": "Analyze data with context sharing between members",
            "domain": "technical",
            "complexity": "intermediate",
            "team_members": [
                {
                    "name": "Data Collector",
                    "role": "collector",
                    "description": "Collects and validates data",
                    "system_prompt": "You are a data collector. Collect and validate data based on user requirements: {user_requirements}",
                    "capabilities": ["data_collection", "validation"],
                    "tools": ["sql", "apis"],
                    "model": "gpt-4",
                    "temperature": 0.3,
                    "max_tokens": 1500,
                    "context_placeholders": [
                        {
                            "placeholder_name": "{user_requirements}",
                            "description": "User's data requirements",
                            "source_step": "user_input"
                        }
                    ]
                },
                {
                    "name": "Data Analyst",
                    "role": "analyst",
                    "description": "Analyzes collected data",
                    "system_prompt": "You are a data analyst. Analyze the data collected: {collected_data}. Consider user requirements: {user_requirements}",
                    "capabilities": ["analysis", "statistics"],
                    "tools": ["python", "r"],
                    "model": "gpt-4",
                    "temperature": 0.5,
                    "max_tokens": 2000,
                    "context_placeholders": [
                        {
                            "placeholder_name": "{collected_data}",
                            "description": "Data collected by the collector",
                            "source_step": "Data Collection"
                        },
                        {
                            "placeholder_name": "{user_requirements}",
                            "description": "User's analysis requirements",
                            "source_step": "user_input"
                        }
                    ]
                },
                {
                    "name": "Report Generator",
                    "role": "reporter",
                    "description": "Generates final reports",
                    "system_prompt": "You are a report generator. Create a comprehensive report based on: {analysis_results}. Original requirements: {user_requirements}",
                    "capabilities": ["reporting", "visualization"],
                    "tools": ["markdown", "charts"],
                    "model": "gpt-4",
                    "temperature": 0.7,
                    "max_tokens": 2500,
                    "context_placeholders": [
                        {
                            "placeholder_name": "{analysis_results}",
                            "description": "Results from data analysis",
                            "source_step": "Data Analysis"
                        },
                        {
                            "placeholder_name": "{user_requirements}",
                            "description": "User's original requirements",
                            "source_step": "user_input"
                        }
                    ]
                }
            ],
            "workflow": {
                "steps": [
                    {
                        "name": "Data Collection",
                        "description": "Collect and validate required data",
                        "assignee": "Data Collector",
                        "inputs": ["user_requirements"],
                        "outputs": ["collected_data", "validation_report"],
                        "context_dependencies": []
                    },
                    {
                        "name": "Data Analysis",
                        "description": "Analyze the collected data",
                        "assignee": "Data Analyst",
                        "inputs": ["collected_data"],
                        "outputs": ["analysis_results", "insights"],
                        "context_dependencies": ["Data Collection"]
                    },
                    {
                        "name": "Report Generation",
                        "description": "Generate comprehensive report",
                        "assignee": "Report Generator",
                        "inputs": ["analysis_results"],
                        "outputs": ["final_report"],
                        "context_dependencies": ["Data Analysis"]
                    }
                ]
            }
        }

    @pytest.fixture
    def agent_config(self, sample_team_plan):
        """Create agent configuration with context-aware team plan."""
        return {
            "agent_id": f"test_agent_{uuid.uuid4().hex[:8]}",
            "team_name": sample_team_plan["team_name"],
            "description": sample_team_plan["description"],
            "team_plan": sample_team_plan,
            "status": "active"
        }

    @pytest.fixture
    def mock_ai_service(self):
        """Mock AI service for testing."""
        mock_service = AsyncMock()
        mock_service.generate_text.return_value = "Mocked AI response"
        return mock_service

    @pytest.mark.asyncio
    async def test_context_aware_agent_initialization(self, agent_config):
        """Test that ConfigDrivenAgent initializes with context support."""
        agent = ConfigDrivenAgent(agent_config)
        
        assert agent.agent_id == agent_config["agent_id"]
        assert agent.team_name == agent_config["team_name"]
        assert hasattr(agent, 'context_service')
        assert isinstance(agent.context_service, ContextService)
        assert agent.execution_id is None  # Not set until execution
        
        # Verify context compatibility was ensured
        for member in agent.team_members:
            assert "context_placeholders" in member
        
        for step in agent.workflow.get("steps", []):
            assert "context_dependencies" in step

    @pytest.mark.asyncio
    async def test_context_compatibility_for_legacy_plans(self):
        """Test that legacy team plans are made context-compatible."""
        legacy_plan = {
            "team_name": "Legacy Team",
            "team_members": [
                {
                    "name": "Worker",
                    "role": "worker",
                    "description": "Does work",
                    "system_prompt": "You are a worker."
                    # Missing context_placeholders
                }
            ],
            "workflow": {
                "steps": [
                    {
                        "name": "Work Step",
                        "description": "Do some work",
                        "assignee": "Worker"
                        # Missing context_dependencies
                    }
                ]
            }
        }
        
        agent_config = {
            "agent_id": "legacy_agent",
            "team_name": "Legacy Team",
            "team_plan": legacy_plan
        }
        
        agent = ConfigDrivenAgent(agent_config)
        
        # Verify context fields were added
        assert "context_placeholders" in agent.team_members[0]
        assert agent.team_members[0]["context_placeholders"] == []
        assert "context_dependencies" in agent.workflow["steps"][0]
        assert agent.workflow["steps"][0]["context_dependencies"] == []

    @pytest.mark.asyncio
    async def test_execution_context_lifecycle(self, agent_config, mock_ai_service):
        """Test the complete execution context lifecycle."""
        agent = ConfigDrivenAgent(agent_config)
        
        # Mock the AI service call
        with patch.object(agent, '_call_ai_with_agent_config', return_value="Mocked response"):
            input_data = {
                "input": "Please analyze sales data for Q1 2025"
            }
            
            # Execute the agent
            result = await agent.execute(input_data)
            
            # Verify execution completed successfully
            assert result["status"] == "success"
            assert result["execution_method"] == "config_driven_context_aware"
            assert "execution_id" in result
            assert "context_summary" in result
            
            # Verify context summary contains expected information
            context_summary = result["context_summary"]
            assert "execution_id" in context_summary
            assert "total_steps" in context_summary
            assert "steps" in context_summary
            
            # Should have user_input plus workflow steps
            expected_steps = len(agent.workflow.get("steps", [])) + 1  # +1 for user_input
            assert context_summary["total_steps"] >= expected_steps

    @pytest.mark.asyncio
    async def test_context_sharing_between_steps(self, agent_config):
        """Test that context is properly shared between workflow steps."""
        agent = ConfigDrivenAgent(agent_config)
        
        # Mock AI responses for each step
        ai_responses = {
            "Data Collection": "Collected 1000 sales records from Q1 2025",
            "Data Analysis": "Analysis shows 15% growth in sales compared to Q4 2024",
            "Report Generation": "Generated comprehensive sales report with visualizations"
        }
        
        async def mock_ai_call(prompt, member):
            # Determine which step this is based on the member
            member_name = member.get("name", "")
            if "Collector" in member_name:
                return ai_responses["Data Collection"]
            elif "Analyst" in member_name:
                # Verify that the prompt contains context from previous step
                assert "Collected 1000 sales records" in prompt
                return ai_responses["Data Analysis"]
            elif "Generator" in member_name:
                # Verify that the prompt contains context from analysis step
                assert "15% growth in sales" in prompt
                return ai_responses["Report Generation"]
            return "Default response"
        
        with patch.object(agent, '_call_ai_with_agent_config', side_effect=mock_ai_call):
            input_data = {
                "input": "Please analyze sales data for Q1 2025"
            }
            
            result = await agent.execute(input_data)
            
            assert result["status"] == "success"
            assert len(result["results"]) == 3  # Three workflow steps

    @pytest.mark.asyncio
    async def test_placeholder_resolution_in_prompts(self, agent_config):
        """Test that placeholders are correctly resolved in member prompts."""
        agent = ConfigDrivenAgent(agent_config)
        
        # Track the prompts that were sent to AI
        captured_prompts = []
        
        async def capture_ai_call(prompt, member):
            captured_prompts.append(prompt)
            return f"Response from {member.get('name', 'Unknown')}"
        
        with patch.object(agent, '_call_ai_with_agent_config', side_effect=capture_ai_call):
            input_data = {
                "input": "Analyze customer satisfaction data"
            }
            
            await agent.execute(input_data)
            
            # Verify that prompts contain resolved placeholders
            assert len(captured_prompts) >= 3
            
            # First step should have user requirements
            first_prompt = captured_prompts[0]
            assert "Analyze customer satisfaction data" in first_prompt
            
            # Later steps should have context from previous steps
            if len(captured_prompts) > 1:
                later_prompts = captured_prompts[1:]
                for prompt in later_prompts:
                    # Should contain some context from previous steps
                    assert any(keyword in prompt.lower() for keyword in ["response", "output", "result"])

    @pytest.mark.asyncio
    async def test_error_handling_with_context_cleanup(self, agent_config):
        """Test that context is properly cleaned up and fallback responses are used when AI calls fail."""
        agent = ConfigDrivenAgent(agent_config)

        # Mock AI service to raise an error
        with patch.object(agent, '_call_ai_with_agent_config', side_effect=Exception("AI service error")):
            input_data = {
                "input": "Test input"
            }

            result = await agent.execute(input_data)

            # Verify execution completed with fallback responses (resilient behavior)
            assert result["status"] == "success"
            assert result["execution_method"] == "config_driven_context_aware"

            # Verify execution_id was set and context was cleaned up
            assert "execution_id" in result
            assert "context_summary" in result

            # Verify that fallback responses were used
            assert len(result["results"]) > 0
            for step_result in result["results"]:
                # Each step should have completed with fallback
                assert step_result["status"] == "completed"
                assert step_result["execution_method"] == "fallback_workflow_step"
                assert "AI调用失败，使用默认响应" in step_result["output"]

    @pytest.mark.asyncio
    async def test_context_service_integration(self, agent_config):
        """Test integration with context service."""
        agent = ConfigDrivenAgent(agent_config)
        
        # Verify context service is properly initialized
        assert agent.context_service is not None
        
        # Mock successful execution
        with patch.object(agent, '_call_ai_with_agent_config', return_value="Test response"):
            input_data = {
                "input": "Test input for context service"
            }
            
            result = await agent.execute(input_data)
            
            # Verify context service was used
            assert result["status"] == "success"
            assert "context_summary" in result
            
            # Verify execution context was created and cleaned up
            execution_id = result["execution_id"]
            assert execution_id is not None
            
            # Context should be cleaned up after execution
            context_manager = agent.context_service.get_execution_context(execution_id)
            assert context_manager is None  # Should be cleaned up

    @pytest.mark.asyncio
    async def test_backward_compatibility(self):
        """Test that the enhanced agent maintains backward compatibility."""
        # Create a simple agent config without context features
        simple_config = {
            "agent_id": "simple_agent",
            "team_name": "Simple Team",
            "team_plan": {
                "team_name": "Simple Team",
                "team_members": [
                    {
                        "name": "Simple Worker",
                        "role": "worker",
                        "description": "Does simple work",
                        "system_prompt": "You are a simple worker."
                    }
                ],
                "workflow": {
                    "steps": [
                        {
                            "name": "Simple Step",
                            "description": "Do simple work",
                            "assignee": "Simple Worker"
                        }
                    ]
                }
            }
        }
        
        agent = ConfigDrivenAgent(simple_config)
        
        with patch.object(agent, '_call_ai_with_agent_config', return_value="Simple response"):
            input_data = {
                "input": "Do some simple work"
            }
            
            result = await agent.execute(input_data)
            
            # Should work without context features
            assert result["status"] == "success"
            assert result["execution_method"] == "config_driven_context_aware"
            
            # Context fields should be added automatically
            assert "context_placeholders" in agent.team_members[0]
            assert "context_dependencies" in agent.workflow["steps"][0]

    @pytest.mark.asyncio
    async def test_context_export_and_import(self, agent_config):
        """Test context export and import functionality."""
        agent = ConfigDrivenAgent(agent_config)
        
        with patch.object(agent, '_call_ai_with_agent_config', return_value="Test response"):
            input_data = {
                "input": "Test for context export"
            }
            
            # Execute to create context
            result = await agent.execute(input_data)
            execution_id = result["execution_id"]
            
            # Export context before cleanup
            context_manager = agent.context_service.get_execution_context(execution_id)
            if context_manager:
                export_data = agent.context_service.export_execution_context(execution_id)
                
                assert export_data is not None
                assert export_data["execution_id"] == execution_id
                assert "contexts" in export_data
                assert "exported_at" in export_data
                
                # Test import
                new_execution_id = "imported_execution"
                export_data["execution_id"] = new_execution_id
                
                imported_manager = agent.context_service.import_execution_context(export_data)
                assert imported_manager is not None
                assert imported_manager.execution_id == new_execution_id
