"""
Integration tests for logs API endpoints.
"""

import pytest
import json
from datetime import datetime, timedelta
from unittest.mock import patch, AsyncMock
from fastapi.testclient import TestClient

from app.main import app
from app.models.application_log import LogLevel, EventType
from app.services.logging_service import ApplicationLoggingService


@pytest.fixture
def client():
    """Create test client."""
    return TestClient(app)


@pytest.fixture
def auth_headers():
    """Mock authentication headers."""
    return {"Authorization": "Bearer test-token"}


@pytest.fixture
def mock_current_user():
    """Mock current user."""
    from app.models.user import User
    return User(
        id=1,
        email="<EMAIL>",
        username="testuser",
        is_active=True,
        is_verified=True
    )


class TestLogsAPI:
    """Test logs API endpoints."""

    @patch('app.api.v1.endpoints.logs.get_current_active_user')
    @patch('app.api.v1.endpoints.logs.ApplicationLoggingService')
    def test_get_logs_success(self, mock_service_class, mock_get_user, client, mock_current_user):
        """Test successful log retrieval."""
        mock_get_user.return_value = mock_current_user
        
        # Mock service response
        mock_service = AsyncMock()
        mock_service_class.return_value = mock_service
        mock_service.get_logs.return_value = {
            "logs": [
                {
                    "id": 1,
                    "uuid": "log-uuid-1",
                    "level": "info",
                    "event_type": "user_login",
                    "message": "User logged in",
                    "user_id": 1,
                    "timestamp": "2023-01-01T12:00:00Z",
                    "created_at": "2023-01-01T12:00:00Z"
                }
            ],
            "total": 1,
            "page": 1,
            "limit": 50,
            "has_next": False,
            "has_prev": False
        }

        response = client.get("/api/v1/logs", headers={"Authorization": "Bearer test-token"})

        assert response.status_code == 200
        data = response.json()
        assert "logs" in data
        assert len(data["logs"]) == 1
        assert data["total"] == 1

    @patch('app.api.v1.endpoints.logs.get_current_active_user')
    @patch('app.api.v1.endpoints.logs.ApplicationLoggingService')
    def test_get_logs_with_filters(self, mock_service_class, mock_get_user, client, mock_current_user):
        """Test log retrieval with filters."""
        mock_get_user.return_value = mock_current_user
        
        mock_service = AsyncMock()
        mock_service_class.return_value = mock_service
        mock_service.get_logs.return_value = {
            "logs": [],
            "total": 0,
            "page": 1,
            "limit": 50,
            "has_next": False,
            "has_prev": False
        }

        params = {
            "level": "error",
            "event_type": "system_error",
            "agent_id": "agent_123",
            "search_query": "error message",
            "page": 1,
            "limit": 25
        }

        response = client.get("/api/v1/logs", params=params, headers={"Authorization": "Bearer test-token"})

        assert response.status_code == 200
        # Verify that the service was called with correct filters
        mock_service.get_logs.assert_called_once()
        call_args = mock_service.get_logs.call_args
        filters = call_args[1]["filters"]
        assert filters.level == LogLevel.ERROR
        assert filters.event_type == EventType.SYSTEM_ERROR
        assert filters.agent_id == "agent_123"
        assert filters.search_query == "error message"

    @patch('app.api.v1.endpoints.logs.get_current_active_user')
    @patch('app.api.v1.endpoints.logs.ApplicationLoggingService')
    def test_get_log_detail_success(self, mock_service_class, mock_get_user, client, mock_current_user):
        """Test successful log detail retrieval."""
        mock_get_user.return_value = mock_current_user
        
        mock_service = AsyncMock()
        mock_service_class.return_value = mock_service
        mock_service.get_log_detail.return_value = {
            "id": 1,
            "uuid": "log-uuid-1",
            "level": "error",
            "event_type": "system_error",
            "message": "Detailed error message",
            "user_id": 1,
            "source_file": "/app/test.py",
            "source_line": 42,
            "stack_trace": "Traceback...",
            "metadata": {"key": "value"},
            "timestamp": "2023-01-01T12:00:00Z",
            "created_at": "2023-01-01T12:00:00Z"
        }

        response = client.get("/api/v1/logs/1", headers={"Authorization": "Bearer test-token"})

        assert response.status_code == 200
        data = response.json()
        assert data["id"] == 1
        assert data["level"] == "error"
        assert data["source_file"] == "/app/test.py"
        assert data["stack_trace"] == "Traceback..."

    @patch('app.api.v1.endpoints.logs.get_current_active_user')
    @patch('app.api.v1.endpoints.logs.ApplicationLoggingService')
    def test_get_log_detail_not_found(self, mock_service_class, mock_get_user, client, mock_current_user):
        """Test log detail retrieval for non-existent log."""
        mock_get_user.return_value = mock_current_user
        
        mock_service = AsyncMock()
        mock_service_class.return_value = mock_service
        mock_service.get_log_detail.return_value = None

        response = client.get("/api/v1/logs/999", headers={"Authorization": "Bearer test-token"})

        assert response.status_code == 404
        assert "not found" in response.json()["detail"].lower()

    @patch('app.api.v1.endpoints.logs.get_current_active_user')
    @patch('app.api.v1.endpoints.logs.ApplicationLoggingService')
    def test_export_logs_json(self, mock_service_class, mock_get_user, client, mock_current_user):
        """Test log export in JSON format."""
        mock_get_user.return_value = mock_current_user
        
        mock_service = AsyncMock()
        mock_service_class.return_value = mock_service
        mock_service.get_logs.return_value = {
            "logs": [
                {
                    "id": 1,
                    "uuid": "log-uuid-1",
                    "level": "info",
                    "event_type": "user_login",
                    "message": "User logged in",
                    "user_id": 1,
                    "timestamp": "2023-01-01T12:00:00Z",
                    "created_at": "2023-01-01T12:00:00Z"
                }
            ],
            "total": 1,
            "page": 1,
            "limit": 10000,
            "has_next": False,
            "has_prev": False
        }

        response = client.get(
            "/api/v1/logs/export?format=json",
            headers={"Authorization": "Bearer test-token"}
        )

        assert response.status_code == 200
        assert response.headers["content-type"] == "application/json"
        assert "attachment" in response.headers["content-disposition"]

    @patch('app.api.v1.endpoints.logs.get_current_active_user')
    @patch('app.api.v1.endpoints.logs.ApplicationLoggingService')
    def test_export_logs_csv(self, mock_service_class, mock_get_user, client, mock_current_user):
        """Test log export in CSV format."""
        mock_get_user.return_value = mock_current_user
        
        mock_service = AsyncMock()
        mock_service_class.return_value = mock_service
        mock_service.get_logs.return_value = {
            "logs": [
                {
                    "id": 1,
                    "uuid": "log-uuid-1",
                    "level": "info",
                    "event_type": "user_login",
                    "message": "User logged in",
                    "user_id": 1,
                    "agent_id": "agent_123",
                    "timestamp": "2023-01-01T12:00:00Z",
                    "created_at": "2023-01-01T12:00:00Z"
                }
            ],
            "total": 1,
            "page": 1,
            "limit": 10000,
            "has_next": False,
            "has_prev": False
        }

        response = client.get(
            "/api/v1/logs/export?format=csv",
            headers={"Authorization": "Bearer test-token"}
        )

        assert response.status_code == 200
        assert response.headers["content-type"] == "text/csv; charset=utf-8"
        assert "attachment" in response.headers["content-disposition"]

    @patch('app.api.v1.endpoints.logs.get_current_active_user')
    def test_get_log_statistics(self, mock_get_user, client, mock_current_user):
        """Test log statistics retrieval."""
        mock_get_user.return_value = mock_current_user
        
        with patch('app.api.v1.endpoints.logs.text') as mock_text:
            with patch('app.api.v1.endpoints.logs.get_db') as mock_get_db:
                mock_db = AsyncMock()
                mock_get_db.return_value = mock_db
                
                # Mock query results
                mock_level_result = AsyncMock()
                mock_level_result.__iter__ = lambda self: iter([
                    type('Row', (), {'level': 'info', 'count': 10}),
                    type('Row', (), {'level': 'error', 'count': 2})
                ])
                
                mock_event_result = AsyncMock()
                mock_event_result.__iter__ = lambda self: iter([
                    type('Row', (), {'event_type': 'user_login', 'count': 5}),
                    type('Row', (), {'event_type': 'system_error', 'count': 1})
                ])
                
                mock_recent_result = AsyncMock()
                mock_recent_result.scalar.return_value = 3
                
                mock_db.execute.side_effect = [
                    mock_level_result,
                    mock_event_result,
                    mock_recent_result
                ]

                response = client.get("/api/v1/logs/stats", headers={"Authorization": "Bearer test-token"})

                assert response.status_code == 200
                data = response.json()
                assert "user_id" in data
                assert "level_distribution" in data
                assert "event_type_distribution" in data
                assert "recent_activity_24h" in data

    def test_get_logs_unauthorized(self, client):
        """Test log retrieval without authentication."""
        response = client.get("/api/v1/logs")
        assert response.status_code == 401

    def test_get_log_detail_unauthorized(self, client):
        """Test log detail retrieval without authentication."""
        response = client.get("/api/v1/logs/1")
        assert response.status_code == 401

    def test_export_logs_unauthorized(self, client):
        """Test log export without authentication."""
        response = client.get("/api/v1/logs/export")
        assert response.status_code == 401

    @patch('app.api.v1.endpoints.logs.get_current_active_user')
    @patch('app.api.v1.endpoints.logs.ApplicationLoggingService')
    def test_get_logs_pagination_params(self, mock_service_class, mock_get_user, client, mock_current_user):
        """Test log retrieval with pagination parameters."""
        mock_get_user.return_value = mock_current_user
        
        mock_service = AsyncMock()
        mock_service_class.return_value = mock_service
        mock_service.get_logs.return_value = {
            "logs": [],
            "total": 100,
            "page": 2,
            "limit": 25,
            "has_next": True,
            "has_prev": True
        }

        response = client.get(
            "/api/v1/logs?page=2&limit=25",
            headers={"Authorization": "Bearer test-token"}
        )

        assert response.status_code == 200
        data = response.json()
        assert data["page"] == 2
        assert data["limit"] == 25
        assert data["has_next"] is True
        assert data["has_prev"] is True

    @patch('app.api.v1.endpoints.logs.get_current_active_user')
    @patch('app.api.v1.endpoints.logs.ApplicationLoggingService')
    def test_get_logs_error_handling(self, mock_service_class, mock_get_user, client, mock_current_user):
        """Test error handling in log retrieval."""
        mock_get_user.return_value = mock_current_user
        
        mock_service = AsyncMock()
        mock_service_class.return_value = mock_service
        mock_service.get_logs.side_effect = Exception("Database error")

        response = client.get("/api/v1/logs", headers={"Authorization": "Bearer test-token"})

        assert response.status_code == 500
        assert "Failed to retrieve logs" in response.json()["detail"]

    def test_logs_stats_route_ordering(self, client):
        """Test that /stats route is matched before /{log_id} route to prevent 422 errors."""
        # Test without authentication to ensure we get 401, not 422
        response = client.get("/api/v1/logs/stats")

        # Should get 401 (unauthorized) not 422 (unprocessable entity)
        # 422 would indicate route matching issue where "stats" is parsed as log_id
        assert response.status_code == 401
        assert response.json()["error"]["message"] == "Authentication required"

        # Test that numeric log_id still works
        response = client.get("/api/v1/logs/123")
        assert response.status_code == 401  # Still unauthorized, but route matched correctly
