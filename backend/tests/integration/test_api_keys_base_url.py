"""
Integration tests for API key management with base URL functionality.
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.main import app
from app.models.settings import <PERSON><PERSON><PERSON>, APIKeyProvider, APIKeyStatus
from app.models.user import User, UserRole


@pytest.fixture
def test_user(db_session: AsyncSession):
    """Create a test user for API key tests."""
    user = User(
        username="testuser",
        email="<EMAIL>",
        hashed_password="hashed_password",
        role=UserRole.USER,
        is_active=True
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def auth_headers(test_user):
    """Create authentication headers for test user."""
    # In a real test, you'd generate a proper JWT token
    # For now, we'll mock the authentication
    return {"Authorization": "Bearer test-token"}


class TestAPIKeyCreationWithBaseURL:
    """Test API key creation with base URL functionality."""
    
    def test_create_api_key_with_base_url(self, client: TestClient, auth_headers, test_user):
        """Test creating an API key with custom base URL."""
        api_key_data = {
            "name": "Test OpenAI Key",
            "provider": "openai",
            "key": "sk-test123456789",
            "base_url": "https://proxy.company.com/openai/v1",
            "description": "Test key with custom base URL"
        }
        
        response = client.post(
            "/api/v1/api-keys",
            json=api_key_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        result = response.json()
        assert result["success"] is True
        assert "API key created successfully" in result["message"]
        assert "data" in result
    
    def test_create_api_key_without_base_url(self, client: TestClient, auth_headers, test_user):
        """Test creating an API key without base URL (should use default)."""
        api_key_data = {
            "name": "Test OpenAI Key Default",
            "provider": "openai",
            "key": "sk-test123456789"
        }
        
        response = client.post(
            "/api/v1/api-keys",
            json=api_key_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        result = response.json()
        assert result["success"] is True
    
    def test_create_api_key_invalid_base_url(self, client: TestClient, auth_headers, test_user):
        """Test creating an API key with invalid base URL."""
        api_key_data = {
            "name": "Test Invalid URL",
            "provider": "openai",
            "key": "sk-test123456789",
            "base_url": "not-a-valid-url"
        }
        
        response = client.post(
            "/api/v1/api-keys",
            json=api_key_data,
            headers=auth_headers
        )
        
        assert response.status_code == 422  # Validation error
        result = response.json()
        assert "Invalid base URL format" in str(result)
    
    def test_create_api_key_azure_with_required_base_url(self, client: TestClient, auth_headers, test_user):
        """Test creating Azure API key with required base URL."""
        api_key_data = {
            "name": "Test Azure Key",
            "provider": "azure",
            "key": "azure-key-123",
            "base_url": "https://myresource.openai.azure.com"
        }
        
        response = client.post(
            "/api/v1/api-keys",
            json=api_key_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        result = response.json()
        assert result["success"] is True


class TestAPIKeyUpdateWithBaseURL:
    """Test API key update with base URL functionality."""
    
    @pytest.fixture
    def existing_api_key(self, db_session: AsyncSession, test_user):
        """Create an existing API key for update tests."""
        api_key = APIKey(
            user_id=test_user.id,
            name="Existing Key",
            provider=APIKeyProvider.OPENAI,
            key_prefix="sk-test",
            key_hash="hashed_key",
            status=APIKeyStatus.ACTIVE
        )
        db_session.add(api_key)
        db_session.commit()
        db_session.refresh(api_key)
        return api_key
    
    def test_update_api_key_base_url(self, client: TestClient, auth_headers, existing_api_key):
        """Test updating an API key's base URL."""
        update_data = {
            "base_url": "https://new-proxy.company.com/openai/v1"
        }
        
        response = client.put(
            f"/api/v1/api-keys/{existing_api_key.id}",
            json=update_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        result = response.json()
        assert result["base_url"] == "https://new-proxy.company.com/openai/v1"
    
    def test_update_api_key_invalid_base_url(self, client: TestClient, auth_headers, existing_api_key):
        """Test updating an API key with invalid base URL."""
        update_data = {
            "base_url": "invalid-url"
        }
        
        response = client.put(
            f"/api/v1/api-keys/{existing_api_key.id}",
            json=update_data,
            headers=auth_headers
        )
        
        assert response.status_code == 422  # Validation error
    
    def test_update_api_key_clear_base_url(self, client: TestClient, auth_headers, existing_api_key):
        """Test clearing an API key's base URL (revert to default)."""
        update_data = {
            "base_url": None
        }
        
        response = client.put(
            f"/api/v1/api-keys/{existing_api_key.id}",
            json=update_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        result = response.json()
        assert result["base_url"] is None


class TestAPIKeyTestingWithBaseURL:
    """Test API key testing functionality with base URLs."""
    
    @pytest.fixture
    def api_key_with_base_url(self, db_session: AsyncSession, test_user):
        """Create an API key with custom base URL for testing."""
        api_key = APIKey(
            user_id=test_user.id,
            name="Test Key with Base URL",
            provider=APIKeyProvider.OPENAI,
            key_prefix="sk-test",
            key_hash="hashed_key",
            base_url="https://proxy.company.com/openai/v1",
            status=APIKeyStatus.ACTIVE
        )
        db_session.add(api_key)
        db_session.commit()
        db_session.refresh(api_key)
        return api_key
    
    @pytest.mark.asyncio
    async def test_test_api_key_with_custom_base_url(self, client: TestClient, auth_headers, api_key_with_base_url):
        """Test API key testing with custom base URL."""
        response = client.post(
            f"/api/v1/api-keys/{api_key_with_base_url.id}/test",
            json={},
            headers=auth_headers
        )
        
        # The test might fail due to invalid API key, but should include base URL info
        result = response.json()
        assert "base_url" in result.get("data", {})
        assert result["data"]["base_url"] == "https://proxy.company.com/openai/v1"
    
    @pytest.fixture
    def api_key_without_base_url(self, db_session: AsyncSession, test_user):
        """Create an API key without custom base URL for testing."""
        api_key = APIKey(
            user_id=test_user.id,
            name="Test Key Default",
            provider=APIKeyProvider.OPENAI,
            key_prefix="sk-test",
            key_hash="hashed_key",
            base_url=None,
            status=APIKeyStatus.ACTIVE
        )
        db_session.add(api_key)
        db_session.commit()
        db_session.refresh(api_key)
        return api_key
    
    @pytest.mark.asyncio
    async def test_test_api_key_with_default_base_url(self, client: TestClient, auth_headers, api_key_without_base_url):
        """Test API key testing with default base URL."""
        response = client.post(
            f"/api/v1/api-keys/{api_key_without_base_url.id}/test",
            json={},
            headers=auth_headers
        )
        
        result = response.json()
        assert "base_url" in result.get("data", {})
        assert result["data"]["base_url"] == "default"


class TestAPIKeyListingWithBaseURL:
    """Test API key listing includes base URL information."""
    
    @pytest.fixture
    def multiple_api_keys(self, db_session: AsyncSession, test_user):
        """Create multiple API keys with different base URL configurations."""
        keys = [
            APIKey(
                user_id=test_user.id,
                name="OpenAI Default",
                provider=APIKeyProvider.OPENAI,
                key_prefix="sk-def",
                key_hash="hashed_key_1",
                base_url=None,
                status=APIKeyStatus.ACTIVE
            ),
            APIKey(
                user_id=test_user.id,
                name="OpenAI Custom",
                provider=APIKeyProvider.OPENAI,
                key_prefix="sk-cus",
                key_hash="hashed_key_2",
                base_url="https://proxy.company.com/openai/v1",
                status=APIKeyStatus.ACTIVE
            ),
            APIKey(
                user_id=test_user.id,
                name="Azure OpenAI",
                provider=APIKeyProvider.AZURE,
                key_prefix="az-key",
                key_hash="hashed_key_3",
                base_url="https://myresource.openai.azure.com",
                status=APIKeyStatus.ACTIVE
            )
        ]
        
        for key in keys:
            db_session.add(key)
        db_session.commit()
        
        for key in keys:
            db_session.refresh(key)
        
        return keys
    
    def test_list_api_keys_includes_base_url(self, client: TestClient, auth_headers, multiple_api_keys):
        """Test that listing API keys includes base URL information."""
        response = client.get(
            "/api/v1/api-keys",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        keys = response.json()
        
        # Should have 3 keys
        assert len(keys) == 3
        
        # Check that base_url field is included
        for key in keys:
            assert "base_url" in key
        
        # Find specific keys and check their base URLs
        openai_default = next(k for k in keys if k["name"] == "OpenAI Default")
        assert openai_default["base_url"] is None
        
        openai_custom = next(k for k in keys if k["name"] == "OpenAI Custom")
        assert openai_custom["base_url"] == "https://proxy.company.com/openai/v1"
        
        azure_key = next(k for k in keys if k["name"] == "Azure OpenAI")
        assert azure_key["base_url"] == "https://myresource.openai.azure.com"
