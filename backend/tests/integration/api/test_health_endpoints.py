"""
Integration tests for health endpoints.
"""

import pytest
from fastapi.testclient import Test<PERSON>lient
from httpx import AsyncClient


@pytest.mark.integration
class TestHealthEndpoints:
    """Test health check endpoints."""
    
    def test_health_check_sync(self, client: TestClient):
        """Test basic health check endpoint (sync)."""
        response = client.get("/api/v1/health/")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "status" in data
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "app_name" in data
        assert "version" in data
        assert isinstance(data["timestamp"], (int, float))
    
    @pytest.mark.asyncio
    async def test_health_check_async(self, async_client: AsyncClient):
        """Test basic health check endpoint (async)."""
        response = await async_client.get("/api/v1/health/")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "app_name" in data
        assert "version" in data
    
    def test_health_check_response_format(self, client: TestClient):
        """Test health check response format."""
        response = client.get("/api/v1/health/")
        
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/json"
        
        data = response.json()
        
        # Verify required fields
        required_fields = ["status", "timestamp", "app_name", "version"]
        for field in required_fields:
            assert field in data
        
        # Verify data types
        assert isinstance(data["status"], str)
        assert isinstance(data["timestamp"], (int, float))
        assert isinstance(data["app_name"], str)
        assert isinstance(data["version"], str)
    
    def test_health_check_values(self, client: TestClient):
        """Test health check response values."""
        response = client.get("/api/v1/health/")
        data = response.json()
        
        assert data["status"] == "healthy"
        assert data["app_name"] == "Meta-Agent"
        assert data["version"] == "0.1.0"
        assert data["timestamp"] > 0
    
    def test_health_check_multiple_requests(self, client: TestClient):
        """Test multiple health check requests."""
        responses = []
        
        # Make multiple requests
        for _ in range(5):
            response = client.get("/api/v1/health/")
            responses.append(response)
        
        # All should be successful
        for response in responses:
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "healthy"
        
        # Timestamps should be different (or very close)
        timestamps = [r.json()["timestamp"] for r in responses]
        assert len(set(timestamps)) >= 1  # At least one unique timestamp
    
    @pytest.mark.asyncio
    async def test_health_check_concurrent_requests(self, async_client: AsyncClient):
        """Test concurrent health check requests."""
        import asyncio
        
        # Make concurrent requests
        tasks = [
            async_client.get("/api/v1/health/")
            for _ in range(10)
        ]
        
        responses = await asyncio.gather(*tasks)
        
        # All should be successful
        for response in responses:
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "healthy"
    
    def test_health_check_headers(self, client: TestClient):
        """Test health check response headers."""
        response = client.get("/api/v1/health/")
        
        assert response.status_code == 200
        
        # Check content type
        assert "application/json" in response.headers.get("content-type", "")
        
        # Check for security headers (if configured)
        # These might be added by middleware
        headers = response.headers
        assert "content-length" in headers
    
    def test_health_check_method_not_allowed(self, client: TestClient):
        """Test health check with unsupported HTTP methods."""
        # POST should not be allowed
        response = client.post("/api/v1/health/")
        assert response.status_code == 405
        
        # PUT should not be allowed
        response = client.put("/api/v1/health/")
        assert response.status_code == 405
        
        # DELETE should not be allowed
        response = client.delete("/api/v1/health/")
        assert response.status_code == 405
    
    def test_health_check_with_query_parameters(self, client: TestClient):
        """Test health check with query parameters."""
        # Should ignore query parameters
        response = client.get("/api/v1/health/?param=value&other=123")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
    
    def test_health_check_with_headers(self, client: TestClient):
        """Test health check with custom headers."""
        headers = {
            "User-Agent": "Test-Client/1.0",
            "Accept": "application/json",
            "Custom-Header": "test-value"
        }
        
        response = client.get("/api/v1/health/", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
    
    @pytest.mark.asyncio
    async def test_health_check_performance(self, async_client: AsyncClient):
        """Test health check performance."""
        import time
        
        start_time = time.time()
        response = await async_client.get("/api/v1/health/")
        end_time = time.time()
        
        assert response.status_code == 200
        
        # Health check should be fast (less than 1 second)
        duration = end_time - start_time
        assert duration < 1.0
    
    def test_health_check_content_encoding(self, client: TestClient):
        """Test health check with different content encodings."""
        # Test with gzip encoding request
        headers = {"Accept-Encoding": "gzip, deflate"}
        response = client.get("/api/v1/health/", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
    
    def test_health_check_json_structure(self, client: TestClient):
        """Test health check JSON structure."""
        response = client.get("/api/v1/health/")
        
        assert response.status_code == 200
        data = response.json()
        
        # Should be a flat dictionary
        assert isinstance(data, dict)
        
        # Should not have nested objects (for basic health check)
        for value in data.values():
            assert not isinstance(value, (dict, list))
    
    def test_health_check_timestamp_format(self, client: TestClient):
        """Test health check timestamp format."""
        import time
        
        before_request = time.time()
        response = client.get("/api/v1/health/")
        after_request = time.time()
        
        assert response.status_code == 200
        data = response.json()
        
        timestamp = data["timestamp"]
        
        # Timestamp should be within reasonable range
        assert before_request <= timestamp <= after_request
        
        # Should be Unix timestamp (positive number)
        assert timestamp > 0
    
    def test_health_check_idempotency(self, client: TestClient):
        """Test health check idempotency."""
        # Multiple identical requests should return consistent results
        responses = []
        
        for _ in range(3):
            response = client.get("/api/v1/health/")
            responses.append(response.json())
        
        # Status should be consistent
        statuses = [r["status"] for r in responses]
        assert all(status == "healthy" for status in statuses)
        
        # App name and version should be consistent
        app_names = [r["app_name"] for r in responses]
        versions = [r["version"] for r in responses]
        
        assert len(set(app_names)) == 1
        assert len(set(versions)) == 1
