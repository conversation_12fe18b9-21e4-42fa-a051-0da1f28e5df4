"""
Integration tests for planning endpoints.
"""

import pytest
import json
from unittest.mock import patch, AsyncMock
from fastapi.testclient import <PERSON><PERSON>lient
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from tests.fixtures.factories import TeamPlanFactory, PlanningRequestFactory


@pytest.mark.integration
class TestPlanningEndpoints:
    """Test planning endpoints."""
    
    @pytest.fixture
    def mock_planning_service(self):
        """Mock planning service."""
        with patch('app.api.v1.endpoints.planning.get_planning_service') as mock:
            service = AsyncMock()
            mock.return_value = service
            yield service
    
    @pytest.mark.asyncio
    async def test_create_planning_request_success(
        self, 
        async_client: AsyncClient, 
        mock_planning_service,
        test_session: AsyncSession
    ):
        """Test successful planning request creation."""
        # Setup
        team_plan = TeamPlanFactory.create_team_plan()
        mock_planning_service.create_team_plan.return_value = team_plan
        
        request_data = {
            "user_description": "我需要一个数据分析团队",
            "model": "gpt-4",
            "temperature": 0.7
        }
        
        # Execute
        response = await async_client.post(
            "/api/v1/planning/create",
            json=request_data
        )
        
        # Verify
        assert response.status_code == 200
        data = response.json()
        
        assert "request_id" in data
        assert "status" in data
        assert data["status"] == "completed"
        assert "team_plan" in data
        assert data["team_plan"]["team_name"] == team_plan["team_name"]
        
        # Verify service was called
        mock_planning_service.create_team_plan.assert_called_once()
    
    def test_create_planning_request_sync(
        self, 
        client: TestClient, 
        mock_planning_service
    ):
        """Test planning request creation (sync client)."""
        # Setup
        team_plan = TeamPlanFactory.create_team_plan()
        mock_planning_service.create_team_plan.return_value = team_plan
        
        request_data = {
            "user_description": "我需要一个股票分析团队"
        }
        
        # Execute
        response = client.post(
            "/api/v1/planning/create",
            json=request_data
        )
        
        # Verify
        assert response.status_code == 200
        data = response.json()
        assert "request_id" in data
        assert "team_plan" in data
    
    @pytest.mark.asyncio
    async def test_create_planning_request_with_custom_params(
        self, 
        async_client: AsyncClient, 
        mock_planning_service
    ):
        """Test planning request with custom parameters."""
        # Setup
        team_plan = TeamPlanFactory.create_team_plan()
        mock_planning_service.create_team_plan.return_value = team_plan
        
        request_data = {
            "user_description": "我需要一个客服团队",
            "model": "gpt-3.5-turbo",
            "temperature": 0.5,
            "requirements": {
                "team_size": 3,
                "expertise": "客户服务"
            }
        }
        
        # Execute
        response = await async_client.post(
            "/api/v1/planning/create",
            json=request_data
        )
        
        # Verify
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "completed"
        
        # Verify service was called with correct parameters
        call_args = mock_planning_service.create_team_plan.call_args
        assert call_args.kwargs["model"] == "gpt-3.5-turbo"
        assert call_args.kwargs["temperature"] == 0.5
    
    @pytest.mark.asyncio
    async def test_create_planning_request_validation_error(
        self, 
        async_client: AsyncClient
    ):
        """Test planning request with validation errors."""
        # Missing required field
        request_data = {
            "model": "gpt-4"
            # Missing user_description
        }
        
        response = await async_client.post(
            "/api/v1/planning/create",
            json=request_data
        )
        
        assert response.status_code == 422  # Validation error
        data = response.json()
        assert "detail" in data
    
    @pytest.mark.asyncio
    async def test_create_planning_request_invalid_temperature(
        self, 
        async_client: AsyncClient
    ):
        """Test planning request with invalid temperature."""
        request_data = {
            "user_description": "测试描述",
            "temperature": 3.0  # Invalid: should be <= 2.0
        }
        
        response = await async_client.post(
            "/api/v1/planning/create",
            json=request_data
        )
        
        assert response.status_code == 422
    
    @pytest.mark.asyncio
    async def test_create_planning_request_service_error(
        self, 
        async_client: AsyncClient, 
        mock_planning_service
    ):
        """Test planning request with service error."""
        # Setup service to raise error
        from app.core.exceptions import PlanningError
        mock_planning_service.create_team_plan.side_effect = PlanningError(
            "AI provider error", stage="planning"
        )
        
        request_data = {
            "user_description": "测试描述"
        }
        
        # Execute
        response = await async_client.post(
            "/api/v1/planning/create",
            json=request_data
        )
        
        # Verify error response
        assert response.status_code == 500
        data = response.json()
        assert "detail" in data
        assert "AI provider error" in data["detail"]
    
    @pytest.mark.asyncio
    async def test_get_planning_request_success(
        self, 
        async_client: AsyncClient,
        test_session: AsyncSession
    ):
        """Test getting planning request by ID."""
        # Create a planning request in database
        planning_request = PlanningRequestFactory.create_planning_request(
            status="completed",
            team_plan_json=json.dumps(TeamPlanFactory.create_team_plan())
        )
        test_session.add(planning_request)
        await test_session.commit()
        await test_session.refresh(planning_request)
        
        # Execute
        response = await async_client.get(
            f"/api/v1/planning/{planning_request.request_id}"
        )
        
        # Verify
        assert response.status_code == 200
        data = response.json()
        assert data["request_id"] == planning_request.request_id
        assert data["status"] == "completed"
        assert "team_plan" in data
    
    @pytest.mark.asyncio
    async def test_get_planning_request_not_found(
        self, 
        async_client: AsyncClient
    ):
        """Test getting non-existent planning request."""
        response = await async_client.get(
            "/api/v1/planning/nonexistent_id"
        )
        
        assert response.status_code == 404
        data = response.json()
        assert "detail" in data
    
    @pytest.mark.asyncio
    async def test_list_planning_requests(
        self, 
        async_client: AsyncClient,
        test_session: AsyncSession
    ):
        """Test listing planning requests."""
        # Create multiple planning requests
        requests = []
        for i in range(3):
            req = PlanningRequestFactory.create_planning_request(
                user_description=f"测试描述 {i}"
            )
            requests.append(req)
            test_session.add(req)
        
        await test_session.commit()
        
        # Execute
        response = await async_client.get("/api/v1/planning/")
        
        # Verify
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 3
        
        # Verify structure
        for item in data:
            assert "request_id" in item
            assert "user_description" in item
            assert "status" in item
    
    @pytest.mark.asyncio
    async def test_list_planning_requests_with_pagination(
        self, 
        async_client: AsyncClient,
        test_session: AsyncSession
    ):
        """Test listing planning requests with pagination."""
        # Create multiple planning requests
        for i in range(10):
            req = PlanningRequestFactory.create_planning_request(
                user_description=f"测试描述 {i}"
            )
            test_session.add(req)
        
        await test_session.commit()
        
        # Execute with pagination
        response = await async_client.get(
            "/api/v1/planning/?skip=0&limit=5"
        )
        
        # Verify
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) <= 5
    
    @pytest.mark.asyncio
    async def test_delete_planning_request(
        self, 
        async_client: AsyncClient,
        test_session: AsyncSession
    ):
        """Test deleting planning request."""
        # Create planning request
        planning_request = PlanningRequestFactory.create_planning_request()
        test_session.add(planning_request)
        await test_session.commit()
        await test_session.refresh(planning_request)
        
        # Execute
        response = await async_client.delete(
            f"/api/v1/planning/{planning_request.request_id}"
        )
        
        # Verify
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        
        # Verify deletion
        get_response = await async_client.get(
            f"/api/v1/planning/{planning_request.request_id}"
        )
        assert get_response.status_code == 404
    
    @pytest.mark.asyncio
    async def test_planning_request_content_type(
        self, 
        async_client: AsyncClient, 
        mock_planning_service
    ):
        """Test planning request content type handling."""
        team_plan = TeamPlanFactory.create_team_plan()
        mock_planning_service.create_team_plan.return_value = team_plan
        
        request_data = {
            "user_description": "测试描述"
        }
        
        # Test with correct content type
        response = await async_client.post(
            "/api/v1/planning/create",
            json=request_data,
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code == 200
    
    @pytest.mark.asyncio
    async def test_planning_request_large_description(
        self, 
        async_client: AsyncClient, 
        mock_planning_service
    ):
        """Test planning request with large description."""
        team_plan = TeamPlanFactory.create_team_plan()
        mock_planning_service.create_team_plan.return_value = team_plan
        
        # Create large description
        large_description = "测试描述 " * 1000  # Very long description
        
        request_data = {
            "user_description": large_description
        }
        
        response = await async_client.post(
            "/api/v1/planning/create",
            json=request_data
        )
        
        # Should handle large descriptions
        assert response.status_code == 200
    
    @pytest.mark.asyncio
    async def test_planning_request_unicode_handling(
        self, 
        async_client: AsyncClient, 
        mock_planning_service
    ):
        """Test planning request with Unicode characters."""
        team_plan = TeamPlanFactory.create_team_plan()
        mock_planning_service.create_team_plan.return_value = team_plan
        
        request_data = {
            "user_description": "我需要一个包含特殊字符的团队：🤖💡📊🔍"
        }
        
        response = await async_client.post(
            "/api/v1/planning/create",
            json=request_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "request_id" in data
