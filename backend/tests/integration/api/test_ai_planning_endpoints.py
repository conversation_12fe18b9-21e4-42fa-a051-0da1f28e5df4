"""
Integration tests for AI planning endpoints.
"""

import json
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi.testclient import TestClient

from app.main import app
from app.models.settings import SystemSettings
from app.models.user import User


class TestAIPlanningEndpoints:
    """Test AI planning API endpoints."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    @pytest.fixture
    def mock_user(self):
        """Create mock authenticated user."""
        user = MagicMock(spec=User)
        user.id = 1
        user.email = "<EMAIL>"
        user.name = "Test User"
        return user
    
    @pytest.fixture
    def auth_headers(self, mock_user):
        """Create authentication headers."""
        with patch('app.api.dependencies.auth.get_current_active_user', return_value=mock_user):
            return {"Authorization": "Bearer test_token"}
    
    def test_ai_generate_no_auth(self, client):
        """Test AI generation without authentication."""
        response = client.post(
            "/api/v1/planning/ai-generate",
            json={"user_description": "test description"}
        )
        assert response.status_code == 401
    
    def test_ai_generate_missing_description(self, client, auth_headers):
        """Test AI generation with missing description."""
        with patch('app.api.dependencies.auth.get_current_active_user'):
            response = client.post(
                "/api/v1/planning/ai-generate",
                json={},
                headers=auth_headers
            )
            assert response.status_code == 400
            assert "user_description is required" in response.json()["detail"]
    
    def test_ai_generate_success(self, client, auth_headers, mock_user):
        """Test successful AI generation."""
        mock_team_plan = {
            "team_name": "Test Team",
            "description": "A test team",
            "team_members": [
                {"name": "Member 1", "role": "role1"}
            ],
            "workflow": {"steps": []},
            "generation_method": "ai_powered"
        }
        
        with patch('app.api.dependencies.auth.get_current_active_user', return_value=mock_user):
            with patch('app.services.ai_planner.get_ai_planner') as mock_get_planner:
                mock_planner = MagicMock()
                mock_planner.generate_ai_powered_team = AsyncMock(return_value=mock_team_plan)
                mock_get_planner.return_value = mock_planner
                
                response = client.post(
                    "/api/v1/planning/ai-generate",
                    json={"user_description": "test description"},
                    headers=auth_headers
                )
                
                assert response.status_code == 200
                data = response.json()
                assert data["success"] is True
                assert "team_plan" in data
                assert data["team_plan"]["team_name"] == "Test Team"
                assert data["generation_method"] == "ai_powered"
    
    def test_ai_generate_with_fallback_success(self, client, auth_headers, mock_user):
        """Test AI generation with fallback."""
        mock_team_plan = {
            "team_name": "Test Team",
            "description": "A test team",
            "team_members": [
                {"name": "Member 1", "role": "role1"}
            ],
            "workflow": {"steps": []}
        }
        
        with patch('app.api.dependencies.auth.get_current_active_user', return_value=mock_user):
            with patch('app.services.ai_planner.get_ai_planner') as mock_get_planner:
                mock_planner = MagicMock()
                mock_planner.generate_team_plan = AsyncMock(return_value=mock_team_plan)
                mock_get_planner.return_value = mock_planner
                
                response = client.post(
                    "/api/v1/planning/ai-generate-with-fallback",
                    json={
                        "user_description": "test description",
                        "template_id": "detective_team"
                    },
                    headers=auth_headers
                )
                
                assert response.status_code == 200
                data = response.json()
                assert data["success"] is True
                assert "team_plan" in data
    
    def test_ai_generate_error(self, client, auth_headers, mock_user):
        """Test AI generation with error."""
        with patch('app.api.dependencies.auth.get_current_active_user', return_value=mock_user):
            with patch('app.services.ai_planner.get_ai_planner') as mock_get_planner:
                mock_planner = MagicMock()
                mock_planner.generate_ai_powered_team = AsyncMock(
                    side_effect=ValueError("AI generation failed")
                )
                mock_get_planner.return_value = mock_planner
                
                response = client.post(
                    "/api/v1/planning/ai-generate",
                    json={"user_description": "test description"},
                    headers=auth_headers
                )
                
                assert response.status_code == 400
                assert "AI generation failed" in response.json()["detail"]
    
    def test_get_ai_settings_no_auth(self, client):
        """Test getting AI settings without authentication."""
        response = client.get("/api/v1/planning/ai-settings")
        assert response.status_code == 401
    
    def test_get_ai_settings_success(self, client, auth_headers, mock_user):
        """Test getting AI settings successfully."""
        mock_settings = MagicMock(spec=SystemSettings)
        mock_settings.enable_ai_team_generation = True
        mock_settings.team_generation_provider = "openai"
        mock_settings.team_generation_model = "gpt-4"
        mock_settings.team_generation_temperature = 0.7
        mock_settings.team_generation_max_tokens = 4000
        mock_settings.team_generation_api_key = "encrypted_system_key"  # System API key

        with patch('app.api.dependencies.auth.get_current_active_user', return_value=mock_user):
            with patch('app.api.dependencies.database.get_db') as mock_get_db:
                mock_db = MagicMock()
                mock_get_db.return_value = mock_db

                # Mock database query
                mock_result = MagicMock()
                mock_result.scalar_one_or_none.return_value = mock_settings
                mock_db.execute = AsyncMock(return_value=mock_result)

                response = client.get(
                    "/api/v1/planning/ai-settings",
                    headers=auth_headers
                )

                assert response.status_code == 200
                data = response.json()
                assert data["enabled"] is True
                assert data["provider"] == "openai"
                assert data["model"] == "gpt-4"
                assert data["has_api_key"] is True  # System has API key configured
    
    def test_get_ai_settings_no_settings(self, client, auth_headers, mock_user):
        """Test getting AI settings when no settings exist."""
        with patch('app.api.dependencies.auth.get_current_active_user', return_value=mock_user):
            with patch('app.api.dependencies.database.get_db') as mock_get_db:
                mock_db = MagicMock()
                mock_get_db.return_value = mock_db
                
                # Mock no settings found
                mock_result = MagicMock()
                mock_result.scalar_one_or_none.return_value = None
                mock_db.execute = AsyncMock(return_value=mock_result)
                
                response = client.get(
                    "/api/v1/planning/ai-settings",
                    headers=auth_headers
                )
                
                assert response.status_code == 404
                assert "System settings not found" in response.json()["detail"]
    
    def test_existing_endpoints_still_work(self, client):
        """Test that existing endpoints still work."""
        # Test templates endpoint
        response = client.get("/api/v1/planning/templates")
        assert response.status_code == 200
        templates = response.json()
        assert isinstance(templates, list)
        assert len(templates) > 0
        
        # Test analyze endpoint
        response = client.post(
            "/api/v1/planning/analyze",
            json={"user_description": "test description"}
        )
        assert response.status_code == 200
        data = response.json()
        assert "analysis" in data
        assert "suggestions" in data
    
    def test_validate_endpoint(self, client):
        """Test team plan validation endpoint."""
        valid_plan = {
            "team_name": "Test Team",
            "description": "A test team",
            "objective": "Test objective",
            "team_members": [
                {"name": "Member 1", "role": "role1"},
                {"name": "Member 2", "role": "role2"}
            ],
            "workflow": {
                "steps": [
                    {"name": "Step 1", "description": "First step"}
                ]
            }
        }
        
        response = client.post(
            "/api/v1/planning/validate",
            json=valid_plan
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "valid" in data
        assert "score" in data
        assert "errors" in data
        assert "warnings" in data
        assert "suggestions" in data


class TestAIPlanningEndpointsErrorHandling:
    """Test error handling in AI planning endpoints."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    @pytest.fixture
    def mock_user(self):
        """Create mock authenticated user."""
        user = MagicMock(spec=User)
        user.id = 1
        user.email = "<EMAIL>"
        return user
    
    def test_ai_generate_internal_error(self, client, mock_user):
        """Test AI generation with internal server error."""
        with patch('app.api.dependencies.auth.get_current_active_user', return_value=mock_user):
            with patch('app.services.ai_planner.get_ai_planner') as mock_get_planner:
                mock_planner = MagicMock()
                mock_planner.generate_ai_powered_team = AsyncMock(
                    side_effect=Exception("Internal error")
                )
                mock_get_planner.return_value = mock_planner
                
                response = client.post(
                    "/api/v1/planning/ai-generate",
                    json={"user_description": "test description"},
                    headers={"Authorization": "Bearer test_token"}
                )
                
                assert response.status_code == 500
                assert "Failed to generate AI team" in response.json()["detail"]
    
    def test_ai_settings_internal_error(self, client, mock_user):
        """Test AI settings with internal server error."""
        with patch('app.api.dependencies.auth.get_current_active_user', return_value=mock_user):
            with patch('app.api.dependencies.database.get_db') as mock_get_db:
                mock_db = MagicMock()
                mock_get_db.return_value = mock_db
                mock_db.execute = AsyncMock(side_effect=Exception("Database error"))
                
                response = client.get(
                    "/api/v1/planning/ai-settings",
                    headers={"Authorization": "Bearer test_token"}
                )
                
                assert response.status_code == 500
                assert "Failed to get AI settings" in response.json()["detail"]
