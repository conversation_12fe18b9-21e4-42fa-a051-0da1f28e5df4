"""
Integration tests for authentication endpoints.
"""

import pytest
from fastapi.testclient import Test<PERSON><PERSON>
from httpx import Async<PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import User, User<PERSON>tatus, UserRole
from app.core.security import create_access_token
from tests.fixtures.factories import UserFactory, AuthTestDataFactory


class TestAuthRegistrationEndpoint:
    """Test user registration endpoint."""
    
    @pytest.mark.integration
    def test_register_user_success(self, client: TestClient):
        """Test successful user registration."""
        registration_data = AuthTestDataFactory.create_registration_data(
            name="<PERSON>",
            email="<EMAIL>",
            password="password123"
        )
        
        response = client.post("/api/v1/auth/register", json=registration_data)
        
        assert response.status_code == 201
        data = response.json()
        
        # Check response structure
        assert "user" in data
        assert "tokens" in data
        assert "session_id" in data
        
        # Check user data
        user_data = data["user"]
        assert user_data["name"] == "<PERSON>"
        assert user_data["email"] == "<EMAIL>"
        assert user_data["role"] == "user"
        assert user_data["status"] == "pending_verification"
        assert user_data["is_email_verified"] is False
        
        # Check tokens
        tokens = data["tokens"]
        assert "access_token" in tokens
        assert tokens["token_type"] == "bearer"
        assert "expires_in" in tokens
    
    def test_register_user_duplicate_email(self, client: TestClient, test_session: AsyncSession):
        """Test registration with duplicate email."""
        # Create existing user
        existing_user = UserFactory.create_user(email="<EMAIL>")
        test_session.add(existing_user)
        test_session.commit()
        
        registration_data = AuthTestDataFactory.create_registration_data(
            email="<EMAIL>"
        )
        
        response = client.post("/api/v1/auth/register", json=registration_data)
        
        assert response.status_code == 400
        assert "User already exists" in response.json()["detail"]
    
    def test_register_user_password_mismatch(self, client: TestClient):
        """Test registration with password mismatch."""
        registration_data = AuthTestDataFactory.create_registration_data(
            password="password123",
            confirm_password="different_password"
        )
        
        response = client.post("/api/v1/auth/register", json=registration_data)
        
        assert response.status_code == 400
        assert "Passwords do not match" in response.json()["detail"]
    
    def test_register_user_invalid_data(self, client: TestClient):
        """Test registration with invalid data."""
        # Missing required fields
        response = client.post("/api/v1/auth/register", json={})
        assert response.status_code == 422
        
        # Invalid email format
        invalid_data = AuthTestDataFactory.create_registration_data(
            email="invalid-email"
        )
        response = client.post("/api/v1/auth/register", json=invalid_data)
        assert response.status_code == 422


class TestAuthLoginEndpoint:
    """Test user login endpoint."""
    
    @pytest.mark.integration
    def test_login_user_success(self, client: TestClient, test_session: AsyncSession):
        """Test successful user login."""
        # Create test user
        user = UserFactory.create_user(
            email="<EMAIL>",
            password="password123",
            status=UserStatus.ACTIVE
        )
        test_session.add(user)
        test_session.commit()
        
        login_data = AuthTestDataFactory.create_login_data(
            email="<EMAIL>",
            password="password123"
        )
        
        response = client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == 200
        data = response.json()
        
        # Check response structure
        assert "user" in data
        assert "tokens" in data
        assert "session_id" in data
        
        # Check user data
        user_data = data["user"]
        assert user_data["email"] == "<EMAIL>"
        assert user_data["status"] == "active"
        
        # Check tokens
        tokens = data["tokens"]
        assert "access_token" in tokens
        assert tokens["token_type"] == "bearer"
    
    def test_login_user_invalid_credentials(self, client: TestClient, test_session: AsyncSession):
        """Test login with invalid credentials."""
        # Create test user
        user = UserFactory.create_user(
            email="<EMAIL>",
            password="correct_password"
        )
        test_session.add(user)
        test_session.commit()
        
        # Wrong password
        login_data = AuthTestDataFactory.create_login_data(
            email="<EMAIL>",
            password="wrong_password"
        )
        
        response = client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == 401
        assert "Invalid email or password" in response.json()["detail"]
    
    def test_login_user_not_found(self, client: TestClient):
        """Test login with non-existent user."""
        login_data = AuthTestDataFactory.create_login_data(
            email="<EMAIL>",
            password="password123"
        )
        
        response = client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == 401
        assert "Invalid email or password" in response.json()["detail"]
    
    def test_login_remember_me(self, client: TestClient, test_session: AsyncSession):
        """Test login with remember me option."""
        user = UserFactory.create_user(
            email="<EMAIL>",
            password="password123",
            status=UserStatus.ACTIVE
        )
        test_session.add(user)
        test_session.commit()
        
        login_data = AuthTestDataFactory.create_login_data(
            email="<EMAIL>",
            password="password123",
            remember_me=True
        )
        
        response = client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == 200
        data = response.json()
        
        # Should have longer expiry time
        tokens = data["tokens"]
        assert tokens["expires_in"] == 30 * 24 * 60 * 60  # 30 days


class TestAuthProfileEndpoints:
    """Test user profile endpoints."""
    
    def _get_auth_headers(self, user_id: int) -> dict:
        """Get authentication headers for user."""
        token = create_access_token(subject=str(user_id))
        return {"Authorization": f"Bearer {token}"}
    
    @pytest.mark.integration
    def test_get_current_user_profile(self, client: TestClient, test_session: AsyncSession):
        """Test getting current user profile."""
        user = UserFactory.create_user(
            name="John Doe",
            email="<EMAIL>",
            status=UserStatus.ACTIVE
        )
        test_session.add(user)
        test_session.commit()
        test_session.refresh(user)
        
        headers = self._get_auth_headers(user.id)
        response = client.get("/api/v1/auth/me", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["name"] == "John Doe"
        assert data["email"] == "<EMAIL>"
        assert data["status"] == "active"
        assert "preferences" in data
        assert "created_at" in data
    
    def test_get_profile_unauthorized(self, client: TestClient):
        """Test getting profile without authentication."""
        response = client.get("/api/v1/auth/me")
        
        assert response.status_code == 401
        assert "Authentication required" in response.json()["detail"]
    
    @pytest.mark.integration
    def test_update_user_profile(self, client: TestClient, test_session: AsyncSession):
        """Test updating user profile."""
        user = UserFactory.create_user(
            name="John Doe",
            email="<EMAIL>",
            status=UserStatus.ACTIVE
        )
        test_session.add(user)
        test_session.commit()
        test_session.refresh(user)
        
        update_data = AuthTestDataFactory.create_user_update_data(
            name="Jane Doe",
            bio="Updated bio",
            timezone="America/New_York"
        )
        
        headers = self._get_auth_headers(user.id)
        response = client.put("/api/v1/auth/me", json=update_data, headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["name"] == "Jane Doe"
        assert data["bio"] == "Updated bio"
        assert data["timezone"] == "America/New_York"


class TestAuthPasswordEndpoints:
    """Test password management endpoints."""
    
    def _get_auth_headers(self, user_id: int) -> dict:
        """Get authentication headers for user."""
        token = create_access_token(subject=str(user_id))
        return {"Authorization": f"Bearer {token}"}
    
    @pytest.mark.integration
    def test_change_password_success(self, client: TestClient, test_session: AsyncSession):
        """Test successful password change."""
        user = UserFactory.create_user(
            email="<EMAIL>",
            password="old_password",
            status=UserStatus.ACTIVE
        )
        test_session.add(user)
        test_session.commit()
        test_session.refresh(user)
        
        password_data = AuthTestDataFactory.create_change_password_data(
            current_password="old_password",
            new_password="new_password123"
        )
        
        headers = self._get_auth_headers(user.id)
        response = client.put("/api/v1/auth/change-password", json=password_data, headers=headers)
        
        assert response.status_code == 200
        assert "Password changed successfully" in response.json()["message"]
    
    def test_change_password_wrong_current(self, client: TestClient, test_session: AsyncSession):
        """Test password change with wrong current password."""
        user = UserFactory.create_user(
            email="<EMAIL>",
            password="correct_password",
            status=UserStatus.ACTIVE
        )
        test_session.add(user)
        test_session.commit()
        test_session.refresh(user)
        
        password_data = AuthTestDataFactory.create_change_password_data(
            current_password="wrong_password",
            new_password="new_password123"
        )
        
        headers = self._get_auth_headers(user.id)
        response = client.put("/api/v1/auth/change-password", json=password_data, headers=headers)
        
        assert response.status_code == 400
        assert "Current password is incorrect" in response.json()["detail"]
    
    def test_reset_password_request(self, client: TestClient, test_session: AsyncSession):
        """Test password reset request."""
        user = UserFactory.create_user(email="<EMAIL>")
        test_session.add(user)
        test_session.commit()
        
        reset_data = AuthTestDataFactory.create_reset_password_data(
            email="<EMAIL>"
        )
        
        response = client.post("/api/v1/auth/reset-password", json=reset_data)
        
        assert response.status_code == 200
        assert "password reset link has been sent" in response.json()["message"]
    
    def test_reset_password_nonexistent_email(self, client: TestClient):
        """Test password reset with non-existent email."""
        reset_data = AuthTestDataFactory.create_reset_password_data(
            email="<EMAIL>"
        )
        
        response = client.post("/api/v1/auth/reset-password", json=reset_data)
        
        # Should still return success to prevent email enumeration
        assert response.status_code == 200
        assert "password reset link has been sent" in response.json()["message"]
