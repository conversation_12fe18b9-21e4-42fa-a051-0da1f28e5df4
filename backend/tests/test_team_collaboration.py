"""
Tests for team collaboration functionality in dynamic loader.
"""

import pytest
from app.services.dynamic_loader import ConfigDrivenAgent
from app.services.context_service import ContextService
from app.models.context import ContextAwareWorkflowStep, ContextAwareTeamMember


class TestTeamCollaboration:
    """Test team collaboration features."""
    
    @pytest.fixture
    def sample_collaborative_agent_config(self):
        """Sample agent config with collaborative steps."""
        return {
            "agent_id": "test_collab_agent",
            "team_name": "协作测试团队",
            "team_plan": {
                "team_name": "协作测试团队",
                "description": "测试团队协作功能的团队",
                "objective": "验证团队协作步骤能正常执行",
                "team_members": [
                    {
                        "name": "分析师",
                        "role": "analyst",
                        "description": "负责数据分析",
                        "system_prompt": "你是一位专业的数据分析师。",
                        "capabilities": ["数据分析", "统计建模"],
                        "tools": ["分析工具", "统计软件"]
                    },
                    {
                        "name": "策略师",
                        "role": "strategist", 
                        "description": "负责策略制定",
                        "system_prompt": "你是一位经验丰富的策略师。",
                        "capabilities": ["策略规划", "决策分析"],
                        "tools": ["策略框架", "决策工具"]
                    }
                ],
                "workflow": {
                    "steps": [
                        {
                            "name": "数据分析",
                            "description": "分析相关数据",
                            "assignee": "分析师",
                            "inputs": ["原始数据"],
                            "outputs": ["分析报告"],
                            "context_dependencies": []
                        },
                        {
                            "name": "团队讨论",
                            "description": "团队协作讨论解决方案",
                            "assignee": "团队协作",
                            "inputs": ["分析报告"],
                            "outputs": ["讨论结果", "初步方案"],
                            "context_dependencies": ["数据分析"]
                        },
                        {
                            "name": "策略制定",
                            "description": "制定最终策略",
                            "assignee": "策略师",
                            "inputs": ["讨论结果"],
                            "outputs": ["最终策略"],
                            "context_dependencies": ["团队讨论"]
                        }
                    ]
                }
            }
        }
    
    def test_find_regular_team_member(self, sample_collaborative_agent_config):
        """Test finding regular team members."""
        agent = ConfigDrivenAgent(sample_collaborative_agent_config)
        
        # Test finding existing team members
        analyst = agent._find_team_member("分析师")
        assert analyst is not None
        assert analyst["name"] == "分析师"
        assert analyst["role"] == "analyst"
        assert not analyst.get("is_collaborative", False)
        
        strategist = agent._find_team_member("策略师")
        assert strategist is not None
        assert strategist["name"] == "策略师"
        assert strategist["role"] == "strategist"
        assert not strategist.get("is_collaborative", False)
    
    def test_find_team_collaboration_member(self, sample_collaborative_agent_config):
        """Test finding team collaboration assignee."""
        agent = ConfigDrivenAgent(sample_collaborative_agent_config)
        
        # Test finding team collaboration
        collaboration = agent._find_team_member("团队协作")
        assert collaboration is not None
        assert collaboration.get("is_collaborative", False) is True
        assert collaboration.get("collaboration_type") == "team_collaboration"
        # Should use the first team member as the base
        assert collaboration["name"] == "分析师"
    
    def test_find_team_collaboration_english(self, sample_collaborative_agent_config):
        """Test finding team collaboration with English name."""
        agent = ConfigDrivenAgent(sample_collaborative_agent_config)
        
        # Test finding team collaboration with English name
        collaboration = agent._find_team_member("Team Collaboration")
        assert collaboration is not None
        assert collaboration.get("is_collaborative", False) is True
        assert collaboration.get("collaboration_type") == "team_collaboration"
    
    def test_find_nonexistent_member(self, sample_collaborative_agent_config):
        """Test finding non-existent team member."""
        agent = ConfigDrivenAgent(sample_collaborative_agent_config)
        
        # Test finding non-existent member
        nonexistent = agent._find_team_member("不存在的成员")
        assert nonexistent is None
    
    def test_build_collaborative_context(self, sample_collaborative_agent_config):
        """Test building collaborative context."""
        agent = ConfigDrivenAgent(sample_collaborative_agent_config)
        
        context = agent._build_collaborative_context()
        
        assert context["team_size"] == 2
        assert len(context["team_members"]) == 2
        assert context["collaboration_style"] == "综合各成员专长，协作完成任务"
        assert context["decision_making"] == "基于团队共识和专业判断"
        
        # Check team member details
        member_names = [m["name"] for m in context["team_members"]]
        assert "分析师" in member_names
        assert "策略师" in member_names
    
    def test_collaborative_context_with_empty_team(self):
        """Test collaborative context with empty team."""
        agent_config = {
            "agent_id": "empty_team_agent",
            "team_name": "空团队",
            "team_plan": {
                "team_name": "空团队",
                "description": "没有成员的团队",
                "objective": "测试空团队情况",
                "team_members": [],
                "workflow": {"steps": []}
            }
        }
        
        agent = ConfigDrivenAgent(agent_config)
        
        # Test team collaboration with empty team
        collaboration = agent._find_team_member("团队协作")
        assert collaboration is not None
        assert collaboration.get("is_collaborative", False) is True
        assert collaboration["name"] == "团队协作"
        assert collaboration["role"] == "collaborative_assistant"
        
        # Test collaborative context
        context = agent._build_collaborative_context()
        assert context["team_size"] == 0
        assert len(context["team_members"]) == 0
    
    def test_workflow_step_types(self, sample_collaborative_agent_config):
        """Test identifying different workflow step types."""
        agent = ConfigDrivenAgent(sample_collaborative_agent_config)
        
        workflow_steps = agent.workflow.get("steps", [])
        assert len(workflow_steps) == 3
        
        # Test step 1: Individual step
        step1 = workflow_steps[0]
        member1 = agent._find_team_member(step1["assignee"])
        assert member1 is not None
        assert not member1.get("is_collaborative", False)
        
        # Test step 2: Collaborative step
        step2 = workflow_steps[1]
        member2 = agent._find_team_member(step2["assignee"])
        assert member2 is not None
        assert member2.get("is_collaborative", False) is True
        
        # Test step 3: Individual step
        step3 = workflow_steps[2]
        member3 = agent._find_team_member(step3["assignee"])
        assert member3 is not None
        assert not member3.get("is_collaborative", False)
    
    def test_collaborative_member_properties(self, sample_collaborative_agent_config):
        """Test properties of collaborative team member."""
        agent = ConfigDrivenAgent(sample_collaborative_agent_config)
        
        collaboration = agent._find_team_member("团队协作")
        
        # Should inherit properties from the base member
        assert collaboration["role"] == "analyst"  # From first team member
        assert collaboration["description"] == "负责数据分析"
        
        # Should have collaborative properties
        assert collaboration["is_collaborative"] is True
        assert collaboration["collaboration_type"] == "team_collaboration"
        
        # Should maintain original capabilities
        assert "capabilities" in collaboration
        assert "tools" in collaboration


class TestTeamCollaborationEdgeCases:
    """Test edge cases for team collaboration."""
    
    def test_case_sensitivity(self):
        """Test case sensitivity in team collaboration detection."""
        agent_config = {
            "agent_id": "case_test_agent",
            "team_name": "大小写测试团队",
            "team_plan": {
                "team_name": "大小写测试团队",
                "description": "测试大小写敏感性",
                "objective": "验证大小写处理",
                "team_members": [
                    {
                        "name": "测试员",
                        "role": "tester",
                        "description": "负责测试",
                        "system_prompt": "你是测试员。"
                    }
                ],
                "workflow": {"steps": []}
            }
        }
        
        agent = ConfigDrivenAgent(agent_config)
        
        # Test exact match
        assert agent._find_team_member("团队协作") is not None
        assert agent._find_team_member("Team Collaboration") is not None
        
        # Test case variations (should not match)
        assert agent._find_team_member("团队协作") is not None  # Exact Chinese
        assert agent._find_team_member("team collaboration") is None  # Lowercase English
        assert agent._find_team_member("TEAM COLLABORATION") is None  # Uppercase English
    
    def test_special_characters_in_names(self):
        """Test handling of special characters in team member names."""
        agent_config = {
            "agent_id": "special_char_agent",
            "team_name": "特殊字符团队",
            "team_plan": {
                "team_name": "特殊字符团队",
                "description": "测试特殊字符处理",
                "objective": "验证特殊字符",
                "team_members": [
                    {
                        "name": "专家-A",
                        "role": "expert_a",
                        "description": "专家A",
                        "system_prompt": "你是专家A。"
                    },
                    {
                        "name": "顾问(高级)",
                        "role": "senior_consultant",
                        "description": "高级顾问",
                        "system_prompt": "你是高级顾问。"
                    }
                ],
                "workflow": {"steps": []}
            }
        }
        
        agent = ConfigDrivenAgent(agent_config)
        
        # Test finding members with special characters
        expert_a = agent._find_team_member("专家-A")
        assert expert_a is not None
        assert expert_a["name"] == "专家-A"
        
        consultant = agent._find_team_member("顾问(高级)")
        assert consultant is not None
        assert consultant["name"] == "顾问(高级)"
        
        # Team collaboration should still work
        collaboration = agent._find_team_member("团队协作")
        assert collaboration is not None
        assert collaboration.get("is_collaborative") is True


class TestCollaborativePromptBuilding:
    """Test collaborative prompt building functionality."""

    def test_build_collaborative_step_prompt(self):
        """Test building collaborative step prompts."""
        context_service = ContextService()

        # Create test data
        step = ContextAwareWorkflowStep(
            name="团队讨论",
            description="团队协作讨论解决方案",
            assignee="团队协作",
            inputs=["分析报告"],
            outputs=["讨论结果", "初步方案"],
            context_dependencies=["数据分析"]
        )

        primary_member = ContextAwareTeamMember(
            name="分析师",
            role="analyst",
            description="负责数据分析",
            system_prompt="你是一位专业的数据分析师。"
        )

        team_members = [
            {
                "name": "分析师",
                "role": "analyst",
                "description": "负责数据分析",
                "capabilities": ["数据分析", "统计建模"]
            },
            {
                "name": "策略师",
                "role": "strategist",
                "description": "负责策略制定",
                "capabilities": ["策略规划", "决策分析"]
            }
        ]

        collaborative_context = {
            "team_size": 2,
            "team_members": team_members,
            "collaboration_style": "综合各成员专长，协作完成任务",
            "decision_making": "基于团队共识和专业判断"
        }

        # Build collaborative prompt
        prompt = context_service.build_collaborative_step_prompt(
            execution_id="test_exec_123",
            step=step,
            primary_member=primary_member,
            team_members=team_members,
            user_input="如何提高团队效率？",
            collaborative_context=collaborative_context
        )

        # Verify prompt content
        assert "团队讨论 (团队协作)" in prompt
        assert "团队协作讨论解决方案" in prompt
        assert "团队成员概览:" in prompt
        assert "分析师 (analyst)" in prompt
        assert "策略师 (strategist)" in prompt
        assert "协作指导原则:" in prompt
        assert "综合运用各成员的专业能力和视角" in prompt
        assert "如何提高团队效率？" in prompt
        assert "期望输入: 分析报告" in prompt
        assert "期望输出: 讨论结果, 初步方案" in prompt
        assert "请以团队协作的方式完成该步骤" in prompt
