"""
Simple test to verify the index conflict fix.
"""

import pytest
import tempfile
import os
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlmodel import SQLModel


@pytest.mark.asyncio
@pytest.mark.index_conflict
async def test_simple_database_creation():
    """Test simple database creation without conflicts."""
    # Create a temporary database file
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp:
        db_path = tmp.name
    
    try:
        # Create engine with unique database
        database_url = f"sqlite+aiosqlite:///{db_path}"
        engine = create_async_engine(database_url, echo=False)
        
        # Create tables with checkfirst=True
        async with engine.begin() as conn:
            await conn.run_sync(SQLModel.metadata.create_all, checkfirst=True)
        
        # Create session factory
        AsyncSessionLocal = sessionmaker(
            engine, class_=AsyncSession, expire_on_commit=False
        )
        
        # Test session creation
        from sqlalchemy import text
        async with AsyncSessionLocal() as session:
            result = await session.execute(text("SELECT 1"))
            assert result.scalar() == 1
        
        # Clean up
        await engine.dispose()
        print("✅ Simple database creation works")
        
    finally:
        # Clean up temp file
        if os.path.exists(db_path):
            os.unlink(db_path)


@pytest.mark.asyncio
@pytest.mark.index_conflict
async def test_memory_database():
    """Test in-memory database creation."""
    # Use in-memory database
    database_url = "sqlite+aiosqlite:///:memory:"
    engine = create_async_engine(database_url, echo=False)
    
    try:
        # Create tables
        async with engine.begin() as conn:
            await conn.run_sync(SQLModel.metadata.create_all, checkfirst=True)
        
        # Create session
        AsyncSessionLocal = sessionmaker(
            engine, class_=AsyncSession, expire_on_commit=False
        )
        
        from sqlalchemy import text
        async with AsyncSessionLocal() as session:
            result = await session.execute(text("SELECT 1"))
            assert result.scalar() == 1
        
        print("✅ Memory database works")
        
    finally:
        await engine.dispose()


def test_basic_imports():
    """Test that basic imports work without conflicts."""
    try:
        from app.models.agent import Agent, AgentStatus, AgentType
        from app.models.simple_models import PlanningStatus
        from app.models.planning import PlanningRequest
        
        # Test enum values
        assert AgentStatus.ACTIVE == "active"
        assert PlanningStatus.PENDING == "pending"
        
        print("✅ Model imports work without conflicts")
        
    except Exception as e:
        pytest.fail(f"Import failed: {e}")


@pytest.mark.asyncio
async def test_model_creation():
    """Test model creation without database."""
    from app.models.agent import Agent, AgentStatus, AgentType
    from app.models.simple_models import PlanningStatus
    from app.models.planning import PlanningRequest
    
    # Test Agent creation
    agent = Agent(
        agent_id="test_agent",
        team_name="Test Team",
        description="Test Description",
        agent_type=AgentType.TEAM,
        status=AgentStatus.ACTIVE
    )
    
    assert agent.agent_id == "test_agent"
    assert agent.team_name == "Test Team"
    assert agent.status == AgentStatus.ACTIVE
    
    # Test PlanningRequest creation
    request = PlanningRequest(
        request_id="test_request",
        user_description="Test Description"
    )
    
    assert request.request_id == "test_request"
    assert request.user_description == "Test Description"
    assert request.status == PlanningStatus.PENDING
    
    print("✅ Model creation works without database")


if __name__ == "__main__":
    import asyncio
    
    # Run tests directly
    asyncio.run(test_simple_database_creation())
    asyncio.run(test_memory_database())
    test_basic_imports()
    asyncio.run(test_model_creation())
    print("🎉 All simple fix tests passed!")
