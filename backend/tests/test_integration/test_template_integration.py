"""
Integration tests for template functionality.
"""

import pytest
from httpx import As<PERSON><PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.planning import (
    Template, TemplateCategory, TemplateDifficulty, 
    TemplateVisibility, TemplateStatus
)
from app.models.user import User
from app.models.agent import Agent


class TestTemplateIntegration:
    """Test template functionality end-to-end."""

    @pytest.fixture
    async def sample_user(self, db_session: AsyncSession) -> User:
        """Create a sample user for testing."""
        user = User(
            email="<EMAIL>",
            name="Integration User",
            hashed_password="hashed_password",
            is_active=True,
            is_verified=True
        )
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    @pytest.fixture
    async def sample_agent(self, db_session: AsyncSession, sample_user: User) -> Agent:
        """Create a sample agent for testing."""
        agent = Agent(
            agent_id="integration_agent_123",
            team_name="Integration Test Agent",
            description="An agent for integration testing",
            prompt_template="Integration test prompt template",
            team_plan={
                "team_name": "Integration Test Team",
                "team_members": [
                    {
                        "name": "Integration Agent",
                        "role": "analyst",
                        "description": "An integration test agent"
                    }
                ]
            },
            user_id=sample_user.id
        )
        db_session.add(agent)
        await db_session.commit()
        await db_session.refresh(agent)
        return agent

    async def test_complete_template_workflow(
        self, 
        client: AsyncClient, 
        sample_user: User,
        auth_headers: dict
    ):
        """Test complete template workflow from creation to deletion."""
        # 1. Create a template
        template_data = {
            "name": "Integration Test Template",
            "description": "A template for integration testing workflow",
            "category": TemplateCategory.TECHNICAL,
            "difficulty": TemplateDifficulty.INTERMEDIATE,
            "prompt_template": "Integration test prompt template for workflow testing",
            "team_structure_template": {
                "team_name": "Integration Test Team",
                "team_members": [
                    {
                        "name": "Integration Agent",
                        "role": "analyst",
                        "description": "An integration test agent"
                    }
                ]
            },
            "tags": ["integration", "test", "workflow"],
            "use_case": "Testing complete template workflow"
        }
        
        create_response = await client.post(
            "/api/v1/templates",
            json=template_data,
            headers=auth_headers
        )
        
        assert create_response.status_code == 200
        created_template = create_response.json()
        template_id = created_template["template_id"]
        
        # 2. Get the created template
        get_response = await client.get(
            f"/api/v1/templates/{template_id}",
            headers=auth_headers
        )
        
        assert get_response.status_code == 200
        retrieved_template = get_response.json()
        assert retrieved_template["name"] == template_data["name"]
        assert retrieved_template["is_owner"] is True
        
        # 3. Update the template
        update_data = {
            "name": "Updated Integration Test Template",
            "description": "Updated description for integration testing",
            "visibility": TemplateVisibility.PUBLIC
        }
        
        update_response = await client.put(
            f"/api/v1/templates/{template_id}",
            json=update_data,
            headers=auth_headers
        )
        
        assert update_response.status_code == 200
        updated_template = update_response.json()
        assert updated_template["name"] == update_data["name"]
        assert updated_template["visibility"] == update_data["visibility"]
        
        # 4. Search for the template
        search_response = await client.get(
            "/api/v1/templates/search?q=integration",
            headers=auth_headers
        )
        
        assert search_response.status_code == 200
        search_results = search_response.json()
        found_template = next((t for t in search_results if t["template_id"] == template_id), None)
        assert found_template is not None
        
        # 5. Duplicate the template
        duplicate_response = await client.post(
            f"/api/v1/templates/{template_id}/duplicate",
            headers=auth_headers
        )
        
        assert duplicate_response.status_code == 200
        duplicated_template = duplicate_response.json()
        assert duplicated_template["name"] == f"{updated_template['name']} (Copy)"
        assert duplicated_template["parent_template_id"] == template_id
        
        # 6. List templates (should include both original and duplicate)
        list_response = await client.get(
            "/api/v1/templates",
            headers=auth_headers
        )
        
        assert list_response.status_code == 200
        templates = list_response.json()
        template_ids = [t["template_id"] for t in templates]
        assert template_id in template_ids
        assert duplicated_template["template_id"] in template_ids
        
        # 7. Delete the duplicate template
        delete_response = await client.delete(
            f"/api/v1/templates/{duplicated_template['template_id']}",
            headers=auth_headers
        )
        
        assert delete_response.status_code == 200
        
        # 8. Verify deletion (template should be archived, not physically deleted)
        deleted_get_response = await client.get(
            f"/api/v1/templates/{duplicated_template['template_id']}",
            headers=auth_headers
        )
        
        # Should return 404 since archived templates are not accessible
        assert deleted_get_response.status_code == 404

    async def test_template_from_agent_workflow(
        self, 
        client: AsyncClient, 
        sample_agent: Agent,
        auth_headers: dict
    ):
        """Test creating template from agent workflow."""
        # 1. Create template from agent
        template_data = {
            "agent_id": sample_agent.agent_id,
            "name": "Template from Integration Agent",
            "description": "Created from integration test agent",
            "category": TemplateCategory.TECHNICAL,
            "difficulty": TemplateDifficulty.INTERMEDIATE,
            "tags": ["agent", "generated", "integration"]
        }
        
        create_response = await client.post(
            "/api/v1/templates/from-agent",
            json=template_data,
            headers=auth_headers
        )
        
        assert create_response.status_code == 200
        created_template = create_response.json()
        
        # 2. Verify template was created with agent data
        assert created_template["name"] == template_data["name"]
        assert created_template["source_agent_id"] == sample_agent.agent_id
        assert created_template["metadata"]["created_from_agent"] is True
        
        # 3. Verify team structure was copied from agent
        team_structure = created_template["team_structure_template"]
        assert team_structure["team_name"] == sample_agent.team_plan["team_name"]
        assert len(team_structure["team_members"]) == len(sample_agent.team_plan["team_members"])

    async def test_template_sharing_workflow(
        self, 
        client: AsyncClient, 
        sample_user: User,
        auth_headers: dict
    ):
        """Test template sharing workflow."""
        # 1. Create a private template
        template_data = {
            "name": "Sharing Test Template",
            "description": "A template for testing sharing functionality",
            "category": TemplateCategory.BUSINESS,
            "difficulty": TemplateDifficulty.BEGINNER,
            "prompt_template": "Sharing test prompt template for testing",
            "team_structure_template": {
                "team_name": "Sharing Test Team",
                "team_members": [{"name": "Share Agent", "role": "assistant"}]
            },
            "visibility": TemplateVisibility.PRIVATE
        }
        
        create_response = await client.post(
            "/api/v1/templates",
            json=template_data,
            headers=auth_headers
        )
        
        assert create_response.status_code == 200
        template = create_response.json()
        template_id = template["template_id"]
        
        # 2. Verify template is private initially
        assert template["visibility"] == TemplateVisibility.PRIVATE
        
        # 3. Make template public
        share_response = await client.post(
            f"/api/v1/templates/{template_id}/share?visibility=public",
            headers=auth_headers
        )
        
        assert share_response.status_code == 200
        
        # 4. Verify template is now public
        get_response = await client.get(
            f"/api/v1/templates/{template_id}",
            headers=auth_headers
        )
        
        assert get_response.status_code == 200
        updated_template = get_response.json()
        assert updated_template["visibility"] == TemplateVisibility.PUBLIC
        
        # 5. Check if template appears in community templates
        community_response = await client.get(
            "/api/v1/templates/community",
            headers=auth_headers
        )
        
        assert community_response.status_code == 200
        community_templates = community_response.json()
        found_in_community = any(t["template_id"] == template_id for t in community_templates)
        assert found_in_community is True

    async def test_template_versioning_workflow(
        self, 
        client: AsyncClient,
        auth_headers: dict
    ):
        """Test template versioning workflow."""
        # 1. Create original template
        template_data = {
            "name": "Versioning Test Template",
            "description": "Original version for testing versioning",
            "category": TemplateCategory.CREATIVE,
            "difficulty": TemplateDifficulty.ADVANCED,
            "prompt_template": "Original prompt template for versioning test",
            "team_structure_template": {
                "team_name": "Versioning Test Team",
                "team_members": [{"name": "Version Agent", "role": "creator"}]
            },
            "version": "1.0.0"
        }
        
        create_response = await client.post(
            "/api/v1/templates",
            json=template_data,
            headers=auth_headers
        )
        
        assert create_response.status_code == 200
        original_template = create_response.json()
        template_id = original_template["template_id"]
        
        # 2. Create a new version
        version_data = {
            "name": "Updated Versioning Test Template",
            "description": "Updated version for testing versioning",
            "prompt_template": "Updated prompt template for versioning test",
            "version": "1.0.1"
        }
        
        version_response = await client.post(
            f"/api/v1/templates/{template_id}/create-version",
            json=version_data,
            headers=auth_headers
        )
        
        assert version_response.status_code == 200
        new_version = version_response.json()
        
        # 3. Verify new version was created
        assert new_version["version"] == "1.0.1"
        assert new_version["parent_template_id"] == template_id
        assert new_version["name"] == version_data["name"]
        
        # 4. Get version history
        versions_response = await client.get(
            f"/api/v1/templates/{template_id}/versions",
            headers=auth_headers
        )
        
        assert versions_response.status_code == 200
        versions = versions_response.json()
        assert len(versions) >= 2  # Original + new version
        
        version_ids = [v["template_id"] for v in versions]
        assert template_id in version_ids
        assert new_version["template_id"] in version_ids
        
        # 5. Rollback to original version
        rollback_response = await client.post(
            f"/api/v1/templates/{template_id}/rollback/{template_id}",
            headers=auth_headers
        )
        
        assert rollback_response.status_code == 200
        rolled_back = rollback_response.json()
        
        # 6. Verify rollback
        assert rolled_back["version"] == "1.0.0"
        assert rolled_back["name"] == template_data["name"]

    async def test_template_statistics_and_metadata(
        self, 
        client: AsyncClient,
        auth_headers: dict
    ):
        """Test template statistics and metadata endpoints."""
        # 1. Get template categories
        categories_response = await client.get(
            "/api/v1/templates/categories/list",
            headers=auth_headers
        )
        
        assert categories_response.status_code == 200
        categories = categories_response.json()
        assert len(categories) > 0
        assert all("value" in cat and "label" in cat for cat in categories)
        
        # 2. Get template difficulties
        difficulties_response = await client.get(
            "/api/v1/templates/difficulties/list",
            headers=auth_headers
        )
        
        assert difficulties_response.status_code == 200
        difficulties = difficulties_response.json()
        assert len(difficulties) == 4  # beginner, intermediate, advanced, expert
        
        # 3. Get popular tags
        tags_response = await client.get(
            "/api/v1/templates/tags/popular",
            headers=auth_headers
        )
        
        assert tags_response.status_code == 200
        tags = tags_response.json()
        assert isinstance(tags, list)
        
        # 4. Get template statistics
        stats_response = await client.get(
            "/api/v1/templates/stats",
            headers=auth_headers
        )
        
        assert stats_response.status_code == 200
        stats = stats_response.json()
        assert "overview" in stats
        assert "categories" in stats
        assert "difficulties" in stats
        
        # 5. Get featured templates
        featured_response = await client.get(
            "/api/v1/templates/public/featured",
            headers=auth_headers
        )
        
        assert featured_response.status_code == 200
        featured = featured_response.json()
        assert isinstance(featured, list)

    async def test_template_error_handling(
        self, 
        client: AsyncClient,
        auth_headers: dict
    ):
        """Test template error handling scenarios."""
        # 1. Try to get non-existent template
        get_response = await client.get(
            "/api/v1/templates/nonexistent_template",
            headers=auth_headers
        )
        
        assert get_response.status_code == 404
        
        # 2. Try to create template with invalid data
        invalid_data = {
            "name": "",  # Empty name
            "description": "Test",
            "category": "invalid_category",
            "difficulty": "invalid_difficulty"
        }
        
        create_response = await client.post(
            "/api/v1/templates",
            json=invalid_data,
            headers=auth_headers
        )
        
        assert create_response.status_code == 422
        
        # 3. Try to update non-existent template
        update_response = await client.put(
            "/api/v1/templates/nonexistent_template",
            json={"name": "Updated Name"},
            headers=auth_headers
        )
        
        assert update_response.status_code == 404
        
        # 4. Try to delete non-existent template
        delete_response = await client.delete(
            "/api/v1/templates/nonexistent_template",
            headers=auth_headers
        )
        
        assert delete_response.status_code == 404
        
        # 5. Try to create template from non-existent agent
        agent_template_data = {
            "agent_id": "nonexistent_agent",
            "name": "Test Template",
            "description": "Test description",
            "category": TemplateCategory.TECHNICAL,
            "difficulty": TemplateDifficulty.INTERMEDIATE
        }
        
        agent_response = await client.post(
            "/api/v1/templates/from-agent",
            json=agent_template_data,
            headers=auth_headers
        )
        
        assert agent_response.status_code == 404
