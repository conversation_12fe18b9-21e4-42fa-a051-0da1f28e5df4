"""
Pytest configuration and shared fixtures.
"""

import asyncio
import os
import tempfile
from typing import As<PERSON><PERSON>enerator, Generator
from unittest.mock import AsyncMock, MagicMock

import pytest
import pytest_asyncio
from fastapi.testclient import TestClient
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlmodel import SQLModel

from app.core.config import Settings, get_database_url
from app.core.database import get_session
from app.main import app


# Test settings
@pytest.fixture(scope="session")
def test_settings() -> Settings:
    """Test settings with overrides."""
    return Settings(
        DEBUG=True,
        ENVIRONMENT="test",
        DATABASE_URL="sqlite+aiosqlite:///:memory:",
        DATABASE_ECHO=False,
        SECRET_KEY="test-secret-key",
        OPENAI_API_KEY="test-openai-key",
        ANTHROPIC_API_KEY="test-anthropic-key",
        LOG_LEVEL="DEBUG",
        UPLOAD_DIR="test_uploads",
        GENERATED_AGENTS_DIR="test_generated_agents",
    )


# Database fixtures
@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture(scope="session")
async def test_db_manager(test_settings):
    """Create test database manager."""
    from tests.fixtures.database_utils import DatabaseTestManager

    manager = DatabaseTestManager(test_settings.DATABASE_URL)
    await manager.setup()
    yield manager
    await manager.cleanup()


@pytest_asyncio.fixture(scope="session")
async def test_engine(test_db_manager):
    """Create test database engine."""
    return test_db_manager.engine


@pytest_asyncio.fixture
async def test_session(test_db_manager) -> AsyncGenerator[AsyncSession, None]:
    """Create test database session."""
    async with test_db_manager.get_session() as session:
        yield session


@pytest.fixture
def override_get_session(test_session):
    """Override database session dependency."""
    async def _get_test_session():
        yield test_session
    
    app.dependency_overrides[get_session] = _get_test_session
    yield
    app.dependency_overrides.clear()


# HTTP client fixtures
@pytest.fixture
def client(override_get_session) -> TestClient:
    """Create test client."""
    return TestClient(app)


@pytest_asyncio.fixture
async def async_client(override_get_session) -> AsyncGenerator[AsyncClient, None]:
    """Create async test client."""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac


# Mock fixtures for external services
@pytest.fixture
def mock_openai_client():
    """Mock OpenAI client."""
    mock = MagicMock()
    mock.chat.completions.create = AsyncMock()
    return mock


@pytest.fixture
def mock_anthropic_client():
    """Mock Anthropic client."""
    mock = MagicMock()
    mock.messages.create = AsyncMock()
    return mock


# File system fixtures
@pytest.fixture
def temp_dir():
    """Create temporary directory for tests."""
    with tempfile.TemporaryDirectory() as tmpdir:
        yield tmpdir


@pytest.fixture
def test_upload_dir(temp_dir):
    """Create test upload directory."""
    upload_dir = os.path.join(temp_dir, "uploads")
    os.makedirs(upload_dir, exist_ok=True)
    return upload_dir


@pytest.fixture
def test_generated_agents_dir(temp_dir):
    """Create test generated agents directory."""
    agents_dir = os.path.join(temp_dir, "generated_agents")
    os.makedirs(agents_dir, exist_ok=True)
    return agents_dir


# Test data fixtures
@pytest.fixture
def sample_user_description():
    """Sample user description for testing."""
    return "我需要一个能够帮助我进行股票分析的专业团队"


@pytest.fixture
def sample_team_plan():
    """Sample team plan for testing."""
    return {
        "team_name": "股票分析专家团队",
        "description": "专业的股票分析和投资建议团队",
        "objective": "提供准确的股票分析和投资建议",
        "team_members": [
            {
                "name": "数据分析师",
                "role": "data_analyst",
                "description": "负责收集和分析股票数据",
                "expertise": ["数据分析", "统计学", "Python"],
                "responsibilities": ["数据收集", "技术分析", "趋势识别"]
            },
            {
                "name": "投资顾问",
                "role": "investment_advisor",
                "description": "提供投资建议和风险评估",
                "expertise": ["投资策略", "风险管理", "市场分析"],
                "responsibilities": ["投资建议", "风险评估", "组合优化"]
            }
        ],
        "workflow": {
            "steps": [
                {
                    "name": "数据收集",
                    "description": "收集相关股票数据",
                    "assignee": "数据分析师",
                    "inputs": ["股票代码"],
                    "outputs": ["股票数据"]
                },
                {
                    "name": "技术分析",
                    "description": "进行技术分析",
                    "assignee": "数据分析师",
                    "inputs": ["股票数据"],
                    "outputs": ["技术分析报告"]
                },
                {
                    "name": "投资建议",
                    "description": "基于分析提供投资建议",
                    "assignee": "投资顾问",
                    "inputs": ["技术分析报告"],
                    "outputs": ["投资建议"]
                }
            ]
        },
        "success_metrics": [
            "分析准确率 > 80%",
            "响应时间 < 30秒",
            "用户满意度 > 4.5/5"
        ]
    }


@pytest.fixture
def sample_agent_data():
    """Sample agent data for testing."""
    return {
        "agent_id": "test_agent_001",
        "team_name": "测试团队",
        "description": "这是一个测试团队",
        "agent_type": "team",
        "status": "active"
    }


@pytest.fixture
def sample_planning_request_data():
    """Sample planning request data for testing."""
    return {
        "user_description": "我需要一个数据分析团队",
        "model": "gpt-4",
        "temperature": 0.7
    }


# Cleanup fixtures
@pytest.fixture(autouse=True)
def cleanup_test_files():
    """Clean up test files after each test."""
    yield
    
    # Clean up any test files
    test_dirs = ["test_uploads", "test_generated_agents", "logs"]
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            import shutil
            shutil.rmtree(test_dir, ignore_errors=True)


# Pytest configuration
def pytest_configure(config):
    """Configure pytest."""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "unit: marks tests as unit tests"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection."""
    for item in items:
        # Add unit marker to all tests by default
        if not any(marker.name in ["integration", "slow"] for marker in item.iter_markers()):
            item.add_marker(pytest.mark.unit)


# Logging-specific fixtures
@pytest.fixture
def sample_log_entry():
    """Create a sample log entry for testing."""
    from app.models.application_log import ApplicationLog, LogLevel, EventType
    from datetime import datetime

    return ApplicationLog(
        id=1,
        uuid="test-log-uuid",
        level=LogLevel.INFO,
        event_type=EventType.USER_LOGIN,
        message="Test log message",
        user_id=1,
        session_id="test-session",
        request_id="test-request",
        source_module="test_module",
        source_function="test_function",
        agent_id="test-agent",
        execution_time_ms=100.0,
        metadata={"test": "data"},
        tags=["test", "sample"],
        ip_address="127.0.0.1",
        timestamp=datetime.utcnow(),
        created_at=datetime.utcnow()
    )


@pytest.fixture
def sample_error_log():
    """Create a sample error log entry for testing."""
    from app.models.application_log import ApplicationLog, LogLevel, EventType
    from datetime import datetime

    return ApplicationLog(
        id=2,
        uuid="test-error-uuid",
        level=LogLevel.ERROR,
        event_type=EventType.SYSTEM_ERROR,
        message="Test error message",
        user_id=1,
        error_code="test_error",
        error_type="TestError",
        stack_trace="Traceback (most recent call last):\n  Test error",
        metadata={"error_context": "test"},
        tags=["error", "test"],
        timestamp=datetime.utcnow(),
        created_at=datetime.utcnow()
    )


@pytest.fixture
def mock_logging_service():
    """Mock logging service for testing."""
    service = AsyncMock()
    service.log_event = AsyncMock()
    service.log_authentication_event = AsyncMock()
    service.log_agent_event = AsyncMock()
    service.log_test_event = AsyncMock()
    service.log_api_key_event = AsyncMock()
    service.log_system_event = AsyncMock()
    service.log_performance_event = AsyncMock()
    service.log_error = AsyncMock()
    service.get_logs = AsyncMock()
    service.get_log_detail = AsyncMock()
    return service


@pytest.fixture
def sample_log_data():
    """Sample log data for creating test logs."""
    from app.models.application_log import LogLevel, EventType

    return {
        "level": LogLevel.INFO,
        "event_type": EventType.USER_LOGIN,
        "message": "User login successful",
        "user_id": 1,
        "session_id": "session_123",
        "request_id": "req_456",
        "source_module": "auth",
        "source_function": "login",
        "execution_time_ms": 150.0,
        "metadata": {"email": "<EMAIL>"},
        "tags": ["authentication", "success"],
        "ip_address": "********"
    }
