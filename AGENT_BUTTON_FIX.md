# Agent Status Button Fix

## Problem
The "Activate" and "Deactivate" buttons in the agent list page were not triggering backend API calls when clicked.

## Root Cause
The issue was that the frontend was configured to use the real backend API (`NEXT_PUBLIC_MOCK_API=false`), but the backend server was not running. This caused all API calls to fail silently.

## Solution

### 1. Environment Configuration Fix
- **File**: `frontend/.env.local`
- **Change**: Set `NEXT_PUBLIC_MOCK_API=true` to enable mock API mode
- **Reason**: This allows the buttons to work immediately without requiring the backend server

### 2. Enhanced Error Handling
- **File**: `frontend/src/app/manage/page.tsx`
- **Changes**:
  - Added proper API response validation
  - Enhanced console logging for debugging
  - Added error handling for failed API calls

### 3. User Feedback Improvements
- **Files**: 
  - `frontend/src/app/manage/page.tsx`
  - `frontend/src/components/features/agent-management/agent-list.tsx`
- **Changes**:
  - Added loading state for individual agent actions
  - Buttons show "处理中..." (Processing...) with spinner during API calls
  - Buttons are disabled during processing to prevent double-clicks

## Code Changes

### Environment Configuration
```bash
# frontend/.env.local
NEXT_PUBLIC_MOCK_API=true  # Changed from false
```

### Button Loading State
```tsx
<Button
  variant="outline"
  size="sm"
  disabled={processingAgents.has(agent.agent_id)}
  onClick={() => onAgentAction(agent.agent_id, "toggle_status")}
>
  {processingAgents.has(agent.agent_id) ? (
    <>
      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-current mr-1"></div>
      处理中...
    </>
  ) : (
    agent.status === "active" ? "停用" : "激活"
  )}
</Button>
```

### Enhanced Error Handling
```tsx
const response = await api.agents.updateStatus(agentId, newStatus);
if (!response.success) {
  throw new Error(response.error?.message || "Failed to update agent status");
}
```

## Testing

### Manual Testing
1. Open http://localhost:3000/manage
2. Click any "激活" or "停用" button
3. Verify:
   - Button shows loading state ("处理中...")
   - Button is disabled during processing
   - Agent status updates after completion
   - Console shows debug logs

### Automated Testing
Run the test script in browser console:
```javascript
// Check environment
checkEnvironment();

// Test API functionality
testMockAPI();

// Test button interactions
testButtonClicks();
```

## Alternative Solutions

### For Production Use
To use the real backend API instead of mock:

1. **Fix bcrypt dependency issue** (if encountered):
   ```bash
   cd backend
   pip uninstall bcrypt
   pip install bcrypt==4.0.1
   ```

2. **Start the backend server**:
   ```bash
   cd backend
   python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

3. **Update environment**:
   ```bash
   # frontend/.env.local
   NEXT_PUBLIC_MOCK_API=false
   NEXT_PUBLIC_API_URL=http://localhost:8000
   ```

4. **Ensure backend endpoints work**:
   ```bash
   curl -X PATCH http://localhost:8000/api/v1/agents/{agent_id} \
     -H "Content-Type: application/json" \
     -d '{"status": "active"}'
   ```

### Backend Troubleshooting

If you encounter the bcrypt error:
```
AttributeError: module 'bcrypt' has no attribute '__about__'
```

**Solution**:
```bash
cd backend
source venv/bin/activate  # if using virtual environment
pip uninstall bcrypt passlib
pip install bcrypt==4.0.1 passlib[bcrypt]==1.7.4
```

**Alternative approach**:
```bash
cd backend
pip install --upgrade bcrypt passlib
```

## Files Modified
- `frontend/.env.local` - Environment configuration
- `frontend/src/app/manage/page.tsx` - Main page logic
- `frontend/src/components/features/agent-management/agent-list.tsx` - Button component
- `test-mock-api.js` - Testing utilities (new)
- `AGENT_BUTTON_FIX.md` - This documentation (new)

## Status
✅ **FIXED** - Agent activate/deactivate buttons now work correctly with proper user feedback and error handling.
