#!/usr/bin/env python3
"""
测试统一架构：全部使用role而不是name
"""

def test_unified_role_architecture():
    """测试统一使用role的架构"""
    print("🏗️ 测试统一role架构...")
    
    # 模拟新的team_plan结构（统一使用role）
    team_plan = {
        "team_name": "内容创作团队",
        "team_members": [
            {
                "name": "内容策略师",  # 显示名称（中文）
                "role": "content_strategist",  # 系统标识符（英文）
                "description": "负责内容策略规划",
                "system_prompt": "你是内容策略师，根据{user.requirements}制定{content_strategist.strategy}。"
            },
            {
                "name": "内容撰写专家",
                "role": "content_writer", 
                "description": "负责内容撰写",
                "system_prompt": "你是撰写专家，基于{content_strategist.strategy}撰写{content_writer.content}。"
            },
            {
                "name": "社交媒体专家",
                "role": "social_media_specialist",
                "description": "负责社交媒体优化", 
                "system_prompt": "你是社交媒体专家，基于{content_writer.content}优化{social_media_specialist.social_content}。"
            }
        ],
        "workflow_steps": [
            {
                "name": "内容策略制定",
                "assignee": "content_strategist",  # 使用role而不是name
                "description": "制定内容策略"
            },
            {
                "name": "内容撰写",
                "assignee": "content_writer",  # 使用role而不是name
                "description": "撰写内容"
            },
            {
                "name": "社交媒体优化",
                "assignee": "social_media_specialist",  # 使用role而不是name
                "description": "优化社交媒体内容"
            }
        ]
    }
    
    print(f"📊 统一架构数据:")
    print(f"  - 团队成员: {len(team_plan['team_members'])} 个")
    print(f"  - 工作流步骤: {len(team_plan['workflow_steps'])} 个")
    
    # 验证数据一致性
    print(f"\n🔍 验证数据一致性:")
    
    # 1. 检查所有team_members都有role字段
    for i, member in enumerate(team_plan["team_members"]):
        role = member.get("role")
        name = member.get("name")
        print(f"  - 成员{i+1}: name='{name}', role='{role}'")
        assert role, f"成员{i+1}缺少role字段"
    
    # 2. 检查所有workflow_steps的assignee都是有效的role
    valid_roles = {member["role"] for member in team_plan["team_members"]}
    print(f"\n  - 有效roles: {valid_roles}")
    
    for i, step in enumerate(team_plan["workflow_steps"]):
        assignee = step.get("assignee")
        step_name = step.get("name")
        print(f"  - 步骤{i+1}: '{step_name}' -> assignee='{assignee}'")
        assert assignee in valid_roles or assignee == "team_collaboration", f"步骤{i+1}的assignee '{assignee}' 不是有效的role"
    
    # 3. 检查变量占位符使用role作为前缀
    print(f"\n  - 变量占位符检查:")
    import re
    VARIABLE_PATTERN = re.compile(r'\{([^}]+)\}')
    
    all_variables = set()
    for member in team_plan["team_members"]:
        system_prompt = member.get("system_prompt", "")
        matches = VARIABLE_PATTERN.findall(system_prompt)
        for match in matches:
            variable_name = match.strip()
            all_variables.add(f"{{{variable_name}}}")
            
            # 检查变量格式
            if '.' in variable_name and not variable_name.startswith('user.'):
                prefix = variable_name.split('.')[0]
                if prefix in valid_roles:
                    print(f"    ✅ {{{variable_name}}}: 使用有效role '{prefix}'")
                else:
                    print(f"    ❌ {{{variable_name}}}: 使用无效role '{prefix}'")
    
    print(f"\n📝 发现的变量: {len(all_variables)} 个")
    for var in sorted(all_variables):
        print(f"  - {var}")
    
    return True

def test_variable_matching_flow():
    """测试变量匹配流程"""
    print(f"\n🔄 测试变量匹配流程...")
    
    # 模拟变量发现结果
    discovered_variables = [
        {"placeholder": "{user.requirements}", "source_agent": None, "variable_type": "USER_INPUT"},
        {"placeholder": "{content_strategist.strategy}", "source_agent": "content_strategist", "variable_type": "INTER_AGENT"},
        {"placeholder": "{content_writer.content}", "source_agent": "content_writer", "variable_type": "INTER_AGENT"},
        {"placeholder": "{social_media_specialist.social_content}", "source_agent": "social_media_specialist", "variable_type": "INTER_AGENT"},
    ]
    
    # 模拟工作流执行
    workflow_steps = [
        {"assignee": "content_strategist"},
        {"assignee": "content_writer"},
        {"assignee": "social_media_specialist"}
    ]
    
    print(f"📝 变量发现结果: {len(discovered_variables)} 个")
    for var in discovered_variables:
        print(f"  - {var['placeholder']}: 来源={var['source_agent']}")
    
    print(f"\n🎯 测试变量匹配:")
    for step in workflow_steps:
        assignee = step["assignee"]  # 现在直接是role
        
        # 查找该role应该产生的变量（无需映射）
        agent_variables = [var for var in discovered_variables if var["source_agent"] == assignee]
        
        print(f"  - assignee '{assignee}': {len(agent_variables)} 个变量")
        for var in agent_variables:
            print(f"    - {var['placeholder']}")
    
    return True

def test_backward_compatibility():
    """测试向后兼容性"""
    print(f"\n🔄 测试向后兼容性...")
    
    # 模拟_find_team_member方法的新逻辑
    team_members = [
        {"name": "内容策略师", "role": "content_strategist"},
        {"name": "内容撰写专家", "role": "content_writer"}
    ]
    
    def find_team_member(identifier: str, team_members: list) -> dict:
        """模拟新的_find_team_member方法"""
        # 优先通过role查找
        for member in team_members:
            if member.get("role") == identifier:
                return member
        
        # 向后兼容：通过name查找
        for member in team_members:
            if member.get("name") == identifier:
                return member
        
        return None
    
    # 测试通过role查找（新方式）
    test_cases = [
        ("content_strategist", "通过role查找"),
        ("content_writer", "通过role查找"),
        ("内容策略师", "通过name查找（向后兼容）"),
        ("内容撰写专家", "通过name查找（向后兼容）"),
        ("nonexistent", "不存在的标识符")
    ]
    
    print(f"🔍 测试团队成员查找:")
    for identifier, description in test_cases:
        member = find_team_member(identifier, team_members)
        if member:
            print(f"  ✅ '{identifier}' ({description}): 找到 {member['name']} ({member['role']})")
        else:
            print(f"  ❌ '{identifier}' ({description}): 未找到")
    
    return True

def main():
    """主测试函数"""
    print("🚀 开始测试统一role架构\n")
    
    # 测试1: 统一架构
    test1_result = test_unified_role_architecture()
    
    # 测试2: 变量匹配流程
    test2_result = test_variable_matching_flow()
    
    # 测试3: 向后兼容性
    test3_result = test_backward_compatibility()
    
    print(f"\n📊 测试结果:")
    print(f"✅ 统一role架构: {'通过' if test1_result else '失败'}")
    print(f"✅ 变量匹配流程: {'通过' if test2_result else '失败'}")
    print(f"✅ 向后兼容性: {'通过' if test3_result else '失败'}")
    
    if test1_result and test2_result and test3_result:
        print(f"\n🎉 统一架构验证成功！")
        print(f"🔧 架构优势:")
        print(f"  - 消除了name到role的映射步骤")
        print(f"  - workflow steps直接使用role作为assignee")
        print(f"  - 变量发现和匹配逻辑简化")
        print(f"  - 保持向后兼容性")
        print(f"  - 数据流一致性更好")
    else:
        print(f"\n❌ 统一架构验证失败，需要进一步调试")

if __name__ == "__main__":
    main()
