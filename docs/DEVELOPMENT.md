# Meta-Agent 开发指南

## 🛠️ 开发环境配置

### 系统要求

- **Node.js**: 18.0+
- **Python**: 3.11+
- **Git**: 2.30+
- **IDE**: VS Code (推荐) 或其他支持TypeScript和Python的IDE

### 开发工具安装

```bash
# 安装Node.js (使用nvm)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18

# 安装Python (使用pyenv)
curl https://pyenv.run | bash
pyenv install 3.11.0
pyenv global 3.11.0

# 安装开发工具
npm install -g typescript @types/node
pip install black flake8 mypy
```

### 项目克隆和设置

```bash
# 克隆项目
git clone <repository-url>
cd meta-agent

# 设置Git hooks
cp .githooks/pre-commit .git/hooks/
chmod +x .git/hooks/pre-commit

# 创建开发分支
git checkout -b feature/your-feature-name
```

## 📁 项目结构详解

```
meta-agent/
├── frontend/                    # 前端项目 (Next.js)
│   ├── src/
│   │   ├── app/                # App Router页面
│   │   │   ├── create/         # Agent创建页面
│   │   │   ├── manage/         # Agent管理页面
│   │   │   ├── test/           # Agent测试页面
│   │   │   └── page.tsx        # 首页
│   │   ├── components/         # React组件
│   │   │   ├── common/         # 通用组件
│   │   │   ├── features/       # 功能组件
│   │   │   ├── layout/         # 布局组件
│   │   │   └── ui/             # UI基础组件
│   │   ├── lib/                # 工具库
│   │   │   ├── api.ts          # API客户端
│   │   │   ├── types.ts        # TypeScript类型定义
│   │   │   └── utils.ts        # 工具函数
│   │   └── styles/             # 样式文件
│   ├── package.json
│   ├── next.config.js
│   ├── tailwind.config.js
│   └── tsconfig.json
├── backend/                     # 后端项目 (FastAPI)
│   ├── app/
│   │   ├── api/                # API路由
│   │   │   └── v1/             # API v1版本
│   │   │       ├── endpoints/  # API端点
│   │   │       └── __init__.py
│   │   ├── core/               # 核心功能
│   │   │   ├── ai_planner.py   # AI规划师
│   │   │   ├── code_generator.py # 代码生成器
│   │   │   ├── dynamic_loader.py # 动态加载器
│   │   │   └── prompt_engineer.py # Prompt工程
│   │   ├── models/             # 数据模型
│   │   ├── services/           # 业务服务
│   │   ├── templates/          # 代码模板
│   │   └── utils/              # 工具函数
│   ├── generated_agents/       # 生成的Agent代码
│   ├── requirements.txt
│   ├── simple_run.py          # 简单启动脚本
│   └── .env.example           # 环境变量示例
├── docs/                       # 项目文档
├── test_system.py             # 系统测试脚本
└── README.md
```

## 🔧 开发环境启动

### 后端开发

```bash
cd backend

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt

# 安装开发依赖
pip install pytest pytest-asyncio black flake8 mypy

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件

# 启动开发服务器
python simple_run.py
```

### 前端开发

```bash
cd frontend

# 安装依赖
npm install

# 安装开发工具
npm install -D @types/react @types/node eslint prettier

# 启动开发服务器
npm run dev
```

## 🏗️ 架构设计原则

### 前端架构

**组件设计原则**:
- **单一职责**: 每个组件只负责一个功能
- **可复用性**: 设计通用的可复用组件
- **类型安全**: 使用TypeScript确保类型安全
- **性能优化**: 合理使用React.memo和useMemo

**目录组织**:
```
components/
├── common/          # 通用组件 (Loading, Error, Modal等)
├── features/        # 功能组件 (按功能模块组织)
│   ├── agent-creation/
│   ├── agent-management/
│   └── agent-testing/
├── layout/          # 布局组件
└── ui/              # 基础UI组件 (Button, Input等)
```

### 后端架构

**分层架构**:
- **API层**: 处理HTTP请求和响应
- **服务层**: 业务逻辑处理
- **核心层**: 核心功能实现
- **数据层**: 数据存储和访问

**模块设计**:
```python
# 服务层示例
class AgentService:
    def __init__(self, planner: AIPlanner, generator: CodeGenerator):
        self.planner = planner
        self.generator = generator
    
    async def create_agent(self, request: CreateAgentRequest) -> Agent:
        # 业务逻辑实现
        pass
```

## 🧪 测试策略

### 前端测试

**测试框架**: Jest + React Testing Library

```bash
# 安装测试依赖
npm install -D jest @testing-library/react @testing-library/jest-dom

# 运行测试
npm test

# 生成覆盖率报告
npm run test:coverage
```

**测试示例**:
```typescript
// components/__tests__/AgentCard.test.tsx
import { render, screen } from '@testing-library/react'
import { AgentCard } from '../AgentCard'

describe('AgentCard', () => {
  it('renders agent information correctly', () => {
    const agent = {
      agent_id: 'test-001',
      team_name: 'Test Team',
      status: 'active'
    }
    
    render(<AgentCard agent={agent} />)
    
    expect(screen.getByText('Test Team')).toBeInTheDocument()
    expect(screen.getByText('active')).toBeInTheDocument()
  })
})
```

### 后端测试

**测试框架**: pytest + pytest-asyncio

```bash
# 运行测试
pytest

# 生成覆盖率报告
pytest --cov=app tests/

# 运行特定测试
pytest tests/test_ai_planner.py -v
```

**测试示例**:
```python
# tests/test_ai_planner.py
import pytest
from app.core.ai_planner import AIPlanner

@pytest.fixture
def ai_planner():
    return AIPlanner()

@pytest.mark.asyncio
async def test_analyze_requirements(ai_planner):
    description = "我需要一个股票分析团队"
    result = await ai_planner.analyze_requirements(description)
    
    assert result.domain in ["finance", "investigation"]
    assert result.complexity in ["basic", "intermediate", "advanced"]
    assert len(result.key_requirements) > 0
```

## 🔄 开发工作流

### Git工作流

```bash
# 1. 创建功能分支
git checkout -b feature/new-feature

# 2. 开发和提交
git add .
git commit -m "feat: add new feature"

# 3. 推送分支
git push origin feature/new-feature

# 4. 创建Pull Request
# 在GitHub/GitLab上创建PR

# 5. 代码审查和合并
# 经过审查后合并到main分支
```

### 提交规范

使用Conventional Commits规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**类型说明**:
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例**:
```
feat(api): add agent creation endpoint
fix(ui): resolve button alignment issue
docs: update API documentation
```

### 代码审查清单

**前端代码审查**:
- [ ] 组件是否遵循单一职责原则
- [ ] 是否正确使用TypeScript类型
- [ ] 是否有适当的错误处理
- [ ] 是否有必要的测试覆盖
- [ ] 是否遵循代码风格规范

**后端代码审查**:
- [ ] API设计是否RESTful
- [ ] 是否有适当的错误处理
- [ ] 是否有输入验证
- [ ] 是否有必要的日志记录
- [ ] 是否有性能考虑

## 🎨 代码风格规范

### 前端代码风格

**TypeScript/React规范**:
```typescript
// 使用函数组件和Hooks
export const AgentCard: React.FC<AgentCardProps> = ({ agent, onSelect }) => {
  const [isSelected, setIsSelected] = useState(false)
  
  // 事件处理函数使用handle前缀
  const handleClick = useCallback(() => {
    setIsSelected(!isSelected)
    onSelect?.(agent.agent_id)
  }, [isSelected, onSelect, agent.agent_id])
  
  return (
    <div className="agent-card" onClick={handleClick}>
      {/* JSX内容 */}
    </div>
  )
}

// 类型定义
interface AgentCardProps {
  agent: Agent
  onSelect?: (agentId: string) => void
}
```

**CSS/Tailwind规范**:
```tsx
// 使用Tailwind CSS类名
<div className="flex items-center justify-between p-4 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow">
  <h3 className="text-lg font-semibold text-gray-900">{agent.team_name}</h3>
  <Badge variant={agent.status === 'active' ? 'success' : 'secondary'}>
    {agent.status}
  </Badge>
</div>
```

### 后端代码风格

**Python规范** (遵循PEP 8):
```python
from typing import List, Optional
from pydantic import BaseModel

class AgentService:
    """Agent管理服务类"""
    
    def __init__(self, planner: AIPlanner, generator: CodeGenerator) -> None:
        self.planner = planner
        self.generator = generator
    
    async def create_agent(
        self, 
        request: CreateAgentRequest
    ) -> CreateAgentResponse:
        """创建新的Agent
        
        Args:
            request: Agent创建请求
            
        Returns:
            Agent创建响应
            
        Raises:
            ValidationError: 输入验证失败
            GenerationError: 代码生成失败
        """
        try:
            # 业务逻辑实现
            team_plan = await self.planner.create_plan(request.description)
            agent_code = await self.generator.generate(team_plan)
            
            return CreateAgentResponse(
                agent_id=agent_code.agent_id,
                status="created",
                message="Agent created successfully"
            )
        except Exception as e:
            logger.error(f"Failed to create agent: {e}")
            raise
```

## 🔧 开发工具配置

### VS Code配置

创建 `.vscode/settings.json`:
```json
{
  "python.defaultInterpreterPath": "./backend/venv/bin/python",
  "python.linting.enabled": true,
  "python.linting.flake8Enabled": true,
  "python.formatting.provider": "black",
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": true
  }
}
```

创建 `.vscode/extensions.json`:
```json
{
  "recommendations": [
    "ms-python.python",
    "ms-python.black-formatter",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next"
  ]
}
```

### 代码格式化配置

**Prettier配置** (`.prettierrc`):
```json
{
  "semi": false,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "es5",
  "printWidth": 100
}
```

**Black配置** (`pyproject.toml`):
```toml
[tool.black]
line-length = 100
target-version = ['py311']
include = '\.pyi?$'
```

## 🚀 性能优化

### 前端性能优化

**代码分割**:
```typescript
// 使用动态导入进行代码分割
const AgentCreationPage = lazy(() => import('./pages/AgentCreationPage'))

// 使用Suspense包装
<Suspense fallback={<Loading />}>
  <AgentCreationPage />
</Suspense>
```

**状态管理优化**:
```typescript
// 使用useMemo缓存计算结果
const filteredAgents = useMemo(() => {
  return agents.filter(agent => 
    agent.status === selectedStatus &&
    agent.team_name.toLowerCase().includes(searchTerm.toLowerCase())
  )
}, [agents, selectedStatus, searchTerm])

// 使用useCallback缓存函数
const handleAgentSelect = useCallback((agentId: string) => {
  setSelectedAgents(prev => 
    prev.includes(agentId) 
      ? prev.filter(id => id !== agentId)
      : [...prev, agentId]
  )
}, [])
```

### 后端性能优化

**异步处理**:
```python
import asyncio
from concurrent.futures import ThreadPoolExecutor

class AgentService:
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=4)
    
    async def process_multiple_agents(self, requests: List[CreateAgentRequest]):
        """并行处理多个Agent创建请求"""
        tasks = [
            self.create_agent(request) 
            for request in requests
        ]
        return await asyncio.gather(*tasks, return_exceptions=True)
```

**缓存策略**:
```python
from functools import lru_cache
import redis

class CacheService:
    def __init__(self):
        self.redis_client = redis.Redis(host='localhost', port=6379, db=0)
    
    @lru_cache(maxsize=128)
    def get_template(self, template_id: str):
        """缓存模板数据"""
        return self._load_template(template_id)
    
    async def cache_agent_result(self, agent_id: str, result: dict):
        """缓存Agent执行结果"""
        await self.redis_client.setex(
            f"agent_result:{agent_id}", 
            3600,  # 1小时过期
            json.dumps(result)
        )
```

## 🐛 调试技巧

### 前端调试

**React DevTools**:
- 安装React DevTools浏览器扩展
- 使用Profiler分析性能问题
- 检查组件状态和props

**浏览器调试**:
```typescript
// 使用console.log进行调试
console.log('Agent data:', agent)

// 使用debugger断点
const handleSubmit = (data: FormData) => {
  debugger; // 浏览器会在此处暂停
  processFormData(data)
}

// 使用React DevTools Profiler
import { Profiler } from 'react'

<Profiler id="AgentList" onRender={onRenderCallback}>
  <AgentList agents={agents} />
</Profiler>
```

### 后端调试

**Python调试**:
```python
import pdb
import logging

# 设置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

async def create_agent(request: CreateAgentRequest):
    logger.debug(f"Creating agent with request: {request}")
    
    # 使用pdb断点
    pdb.set_trace()
    
    try:
        result = await self.process_request(request)
        logger.info(f"Agent created successfully: {result.agent_id}")
        return result
    except Exception as e:
        logger.error(f"Failed to create agent: {e}", exc_info=True)
        raise
```

**API调试**:
```bash
# 使用curl测试API
curl -X POST http://localhost:8000/api/v1/agents/create \
  -H "Content-Type: application/json" \
  -d '{"team_name": "Test Team"}' \
  -v  # 显示详细信息

# 使用httpie (更友好的HTTP客户端)
http POST localhost:8000/api/v1/agents/create team_name="Test Team"
```

## 📚 学习资源

### 技术文档

**前端技术栈**:
- [Next.js文档](https://nextjs.org/docs)
- [React文档](https://react.dev/)
- [TypeScript文档](https://www.typescriptlang.org/docs/)
- [Tailwind CSS文档](https://tailwindcss.com/docs)

**后端技术栈**:
- [FastAPI文档](https://fastapi.tiangolo.com/)
- [Pydantic文档](https://docs.pydantic.dev/)
- [Python异步编程](https://docs.python.org/3/library/asyncio.html)

### 最佳实践

**代码质量**:
- [Clean Code原则](https://github.com/ryanmcdermott/clean-code-javascript)
- [React最佳实践](https://react.dev/learn/thinking-in-react)
- [Python代码风格指南](https://pep8.org/)

**架构设计**:
- [微服务架构模式](https://microservices.io/)
- [RESTful API设计](https://restfulapi.net/)
- [前端架构模式](https://frontendmastery.com/)

这个开发指南为Meta-Agent项目的开发者提供了完整的开发环境配置、代码规范、测试策略和最佳实践，确保项目的代码质量和开发效率。
