# Meta-Agent API 文档

## 📋 API 概览

Meta-Agent提供完整的RESTful API接口，支持Agent的创建、管理、执行和监控。所有API都遵循OpenAPI 3.0规范。

**Base URL**: `http://localhost:8000`
**API版本**: v1
**Content-Type**: `application/json`

## 🔗 API 端点总览

| 分类 | 端点 | 方法 | 描述 |
|------|------|------|------|
| 系统 | `/health` | GET | 健康检查 |
| 规划 | `/api/v1/planning/templates` | GET | 获取模板列表 |
| 规划 | `/api/v1/planning/analyze` | POST | 需求分析 |
| 规划 | `/api/v1/planning/create` | POST | 创建团队规划 |
| Agent | `/api/v1/agents/` | GET | 获取Agent列表 |
| Agent | `/api/v1/agents/create` | POST | 创建Agent |
| Agent | `/api/v1/agents/{id}/info` | GET | 获取Agent信息 |
| Agent | `/api/v1/agents/{id}/execute` | POST | 执行Agent |
| Agent | `/api/v1/agents/{id}/reload` | POST | 重新加载Agent |
| Agent | `/api/v1/agents/{id}/history` | GET | 获取执行历史 |
| Agent | `/api/v1/agents/{id}` | DELETE | 删除Agent |

## 🏥 系统健康检查

### GET /health

检查系统健康状态。

**响应示例**:
```json
{
  "status": "healthy",
  "timestamp": "2025-06-30T13:24:15.571985"
}
```

## 📋 规划相关API

### GET /api/v1/planning/templates

获取所有可用的团队模板。

**响应示例**:
```json
[
  {
    "id": "detective_team",
    "name": "侦探二人组",
    "description": "由禅意僧侣和街头老兵组成的侦探团队",
    "category": "investigation",
    "difficulty": "intermediate",
    "example_prompt": "我需要一个能够解决复杂案件的侦探团队"
  },
  {
    "id": "tech_consultant",
    "name": "技术咨询团队",
    "description": "专业的技术咨询和架构评估团队",
    "category": "technology",
    "difficulty": "advanced",
    "example_prompt": "我需要技术专家来评估我的项目架构"
  }
]
```

### POST /api/v1/planning/analyze

分析用户需求，识别领域和推荐模板。

**请求体**:
```json
{
  "user_description": "我需要一个能够分析股票投资的专业团队"
}
```

**响应示例**:
```json
{
  "analysis": {
    "domain": "investigation",
    "complexity": "advanced",
    "key_requirements": ["数据分析", "投资建议", "风险评估"],
    "suggested_templates": ["detective_team"],
    "custom_needs": []
  },
  "suggestions": {
    "recommended_templates": ["detective_team"],
    "complexity_level": "advanced",
    "domain": "investigation",
    "key_requirements": ["数据分析", "投资建议", "风险评估"]
  }
}
```

### POST /api/v1/planning/create

根据用户需求创建团队规划。

**请求体**:
```json
{
  "user_description": "我需要一个技术咨询团队来评估我的项目架构",
  "template_id": "tech_consultant"  // 可选
}
```

**响应示例**:
```json
{
  "request_id": "plan_892cc5cbe57a",
  "status": "completed",
  "message": "Planning completed successfully",
  "team_plan": {
    "team_name": "技术咨询专家团队",
    "description": "专业的技术咨询和架构评估团队",
    "objective": "为用户提供专业的技术咨询和架构评估服务",
    "domain": "technology",
    "complexity": "advanced",
    "template_id": "tech_consultant",
    "team_members": [
      {
        "name": "架构师",
        "role": "architect",
        "description": "负责系统架构设计和评估",
        "system_prompt": "你是一位资深的系统架构师...",
        "capabilities": ["架构设计", "技术选型", "性能优化"],
        "tools": ["架构图设计", "技术评估", "性能分析"]
      }
    ],
    "workflow": {
      "steps": [
        {
          "name": "需求分析",
          "description": "分析用户的技术需求",
          "assignee": "架构师",
          "inputs": ["需求描述"],
          "outputs": ["需求分析报告"]
        }
      ]
    },
    "created_at": "2025-06-30T13:24:15.571985"
  }
}
```

## 🤖 Agent管理API

### GET /api/v1/agents/

获取所有Agent列表。

**查询参数**:
- `status` (可选): 过滤状态 (active, inactive, error)
- `search` (可选): 搜索关键词
- `page` (可选): 页码，默认1
- `size` (可选): 每页大小，默认10

**响应示例**:
```json
[
  {
    "agent_id": "agent_b4a4f7b20c01",
    "class_name": "股票分析专家团队Agent",
    "team_name": "股票分析专家团队",
    "description": "专业的股票投资分析团队",
    "generated_at": "2025-06-30T13:24:39.991780",
    "metadata": {
      "team_size": 2,
      "workflow_steps": 3,
      "domain": "finance",
      "complexity": "advanced"
    },
    "is_loaded": true,
    "status": "active"
  }
]
```

### POST /api/v1/agents/create

根据团队规划创建新的Agent。

**请求体**: 完整的TeamPlan对象
```json
{
  "team_name": "股票分析专家团队",
  "description": "专业的股票投资分析团队",
  "objective": "为用户提供专业的股票投资分析和建议",
  "team_members": [...],
  "workflow": {...}
}
```

**响应示例**:
```json
{
  "agent_id": "agent_b4a4f7b20c01",
  "status": "active",
  "message": "Agent created and loaded successfully",
  "api_endpoint": "http://localhost:8000/api/v1/agents/agent_b4a4f7b20c01/execute",
  "generation_info": {
    "class_name": "股票分析专家团队Agent",
    "file_path": "generated_agents/股票分析专家团队_agent.py",
    "generated_at": "2025-06-30T13:24:39.992000"
  }
}
```

### GET /api/v1/agents/{agent_id}/info

获取指定Agent的详细信息。

**路径参数**:
- `agent_id`: Agent的唯一标识符

**响应示例**:
```json
{
  "agent_id": "agent_b4a4f7b20c01",
  "class_name": "股票分析专家团队Agent",
  "team_name": "股票分析专家团队",
  "description": "专业的股票投资分析团队",
  "status": "active",
  "created_at": "2025-06-30T13:24:39.991780",
  "metadata": {
    "team_size": 2,
    "workflow_steps": 3,
    "domain": "finance",
    "complexity": "advanced"
  },
  "is_loaded": true,
  "total_executions": 5,
  "success_rate": 100.0,
  "avg_response_time": 2.3
}
```

### POST /api/v1/agents/{agent_id}/execute

执行指定的Agent。

**路径参数**:
- `agent_id`: Agent的唯一标识符

**请求体**:
```json
{
  "input": "请帮我分析一下苹果公司(AAPL)的股票，我想了解是否适合现在买入"
}
```

**响应示例**:
```json
{
  "agent_id": "agent_b4a4f7b20c01",
  "status": "completed",
  "result": {
    "final_result": {
      "技术分析": {
        "step_type": "individual",
        "assignee": "技术分析师",
        "response": "基于技术指标分析，发现了以下关键点...",
        "confidence": 0.85
      },
      "基本面分析": {
        "step_type": "individual",
        "assignee": "基本面分析师",
        "response": "基于财务分析，发现了以下关键点...",
        "confidence": 0.85
      }
    },
    "execution_summary": {
      "total_steps": 3,
      "completed_steps": 3,
      "team_members_involved": 2,
      "execution_quality": "high",
      "recommendations": ["团队协作效果良好", "工作流程执行顺利"]
    },
    "team_collaboration": {
      "collaboration_score": 0.9,
      "communication_quality": "excellent",
      "efficiency_rating": "high"
    }
  },
  "executed_at": "2025-06-30T13:25:32.575106"
}
```

### POST /api/v1/agents/{agent_id}/reload

重新加载指定的Agent。

**路径参数**:
- `agent_id`: Agent的唯一标识符

**响应示例**:
```json
{
  "agent_id": "agent_b4a4f7b20c01",
  "status": "reloaded",
  "message": "Agent reloaded successfully",
  "reloaded_at": "2025-06-30T13:30:00.000000"
}
```

### GET /api/v1/agents/{agent_id}/history

获取Agent的执行历史。

**路径参数**:
- `agent_id`: Agent的唯一标识符

**查询参数**:
- `limit` (可选): 返回记录数量限制，默认10
- `offset` (可选): 偏移量，默认0

**响应示例**:
```json
{
  "agent_id": "agent_b4a4f7b20c01",
  "total_executions": 5,
  "history": [
    {
      "execution_id": "exec_001",
      "input": "请分析苹果股票",
      "status": "completed",
      "executed_at": "2025-06-30T13:25:32.575106",
      "duration": 2.3,
      "success": true
    }
  ]
}
```

### DELETE /api/v1/agents/{agent_id}

删除指定的Agent。

**路径参数**:
- `agent_id`: Agent的唯一标识符

**响应示例**:
```json
{
  "agent_id": "agent_b4a4f7b20c01",
  "status": "deleted",
  "message": "Agent deleted successfully",
  "deleted_at": "2025-06-30T13:35:00.000000"
}
```


```

## 🚨 错误处理

所有API都遵循统一的错误响应格式：

```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": {
      "field": "Additional error details"
    }
  },
  "timestamp": "2025-06-30T13:24:15.571985"
}
```

### 常见错误码

| 错误码 | HTTP状态码 | 描述 |
|--------|------------|------|
| `AGENT_NOT_FOUND` | 404 | Agent不存在 |
| `INVALID_INPUT` | 400 | 输入参数无效 |
| `GENERATION_FAILED` | 500 | 代码生成失败 |
| `EXECUTION_FAILED` | 500 | Agent执行失败 |
| `LOADING_FAILED` | 500 | Agent加载失败 |

## 🔧 API使用示例

### 完整的Agent创建和执行流程

```bash
# 1. 分析需求
curl -X POST http://localhost:8000/api/v1/planning/analyze \
  -H "Content-Type: application/json" \
  -d '{"user_description": "我需要一个股票分析团队"}'

# 2. 创建规划
curl -X POST http://localhost:8000/api/v1/planning/create \
  -H "Content-Type: application/json" \
  -d '{"user_description": "我需要一个股票分析团队", "template_id": "detective_team"}'

# 3. 创建Agent
curl -X POST http://localhost:8000/api/v1/agents/create \
  -H "Content-Type: application/json" \
  -d @team_plan.json

# 4. 执行Agent
curl -X POST http://localhost:8000/api/v1/agents/{agent_id}/execute \
  -H "Content-Type: application/json" \
  -d '{"input": "请分析苹果股票"}'

# 5. 查看结果
curl http://localhost:8000/api/v1/agents/{agent_id}/history
```

## 📚 API文档访问

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

这些接口提供了完整的API交互界面和详细的参数说明。

## 🔐 认证和授权

当前版本的API暂不需要认证，但在生产环境中建议实施以下安全措施：

- **API Key认证**: 为每个客户端分配唯一的API密钥
- **JWT Token**: 使用JSON Web Token进行用户身份验证
- **Rate Limiting**: 实施请求频率限制
- **HTTPS**: 强制使用HTTPS加密传输

## 📝 API版本控制

API采用URL路径版本控制策略：
- 当前版本: `/api/v1/`
- 未来版本: `/api/v2/`, `/api/v3/`

版本兼容性策略：
- 向后兼容的更改不会增加版本号
- 破坏性更改会发布新的API版本
- 旧版本API将维护至少6个月的支持期

## 🚀 性能优化建议

1. **批量操作**: 尽可能使用批量API减少请求次数
2. **缓存**: 合理使用HTTP缓存头
3. **分页**: 大量数据查询时使用分页参数
4. **压缩**: 启用gzip压缩减少传输大小
5. **连接复用**: 使用HTTP/1.1的keep-alive或HTTP/2

## 📞 技术支持

如有API使用问题，请通过以下方式获取支持：
- 查看在线文档: http://localhost:8000/docs
- 提交Issue到项目仓库
- 联系技术支持团队
