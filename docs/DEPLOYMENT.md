# Meta-Agent 部署指南

## 🚀 部署概览

本指南详细介绍了如何在不同环境中部署Meta-Agent系统，包括开发环境、测试环境和生产环境。

## 📋 系统要求

### 最低配置
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 10GB可用空间
- **操作系统**: Linux (Ubuntu 20.04+), macOS, Windows 10+

### 推荐配置
- **CPU**: 4核心或更多
- **内存**: 8GB RAM或更多
- **存储**: 50GB SSD
- **操作系统**: Linux (Ubuntu 22.04 LTS)

### 软件依赖
- **Node.js**: 18.0+
- **Python**: 3.11+
- **npm**: 9.0+ 或 yarn 1.22+
- **Git**: 2.30+

## 🛠️ 开发环境部署

### 1. 环境准备

```bash
# 安装Node.js (使用nvm)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18

# 安装Python (使用pyenv)
curl https://pyenv.run | bash
pyenv install 3.11.0
pyenv global 3.11.0

# 验证安装
node --version  # 应该显示 v18.x.x
python --version  # 应该显示 Python 3.11.x
```

### 2. 项目克隆和配置

```bash
# 克隆项目
git clone <repository-url>
cd meta-agent

# 创建环境配置文件
cp backend/.env.example backend/.env
cp frontend/.env.local.example frontend/.env.local
```

### 3. 后端部署

```bash
cd backend

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt

# 启动后端服务
python simple_run.py
```

后端服务将在 http://localhost:8000 启动

### 4. 前端部署

```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

前端服务将在 http://localhost:3000 启动

### 5. 验证部署

```bash
# 运行系统测试
python test_system.py
```

## 🧪 测试环境部署

### 1. 使用Docker部署

创建 `docker-compose.yml`:

```yaml
version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=testing
      - DEBUG=false
    volumes:
      - ./backend/generated_agents:/app/generated_agents
    depends_on:
      - redis

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://backend:8000
      - NEXT_PUBLIC_MOCK_API=false
    depends_on:
      - backend

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
```

### 2. 创建Dockerfile

**后端Dockerfile** (`backend/Dockerfile`):
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建生成代码目录
RUN mkdir -p generated_agents

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "simple_run.py"]
```

**前端Dockerfile** (`frontend/Dockerfile`):
```dockerfile
FROM node:18-alpine AS builder

WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 生产镜像
FROM node:18-alpine AS runner

WORKDIR /app

# 创建非root用户
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# 复制构建产物
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000

CMD ["node", "server.js"]
```

### 3. 部署命令

```bash
# 构建和启动服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 🏭 生产环境部署

### 1. 服务器配置

#### 系统优化
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必要工具
sudo apt install -y curl wget git htop nginx certbot

# 配置防火墙
sudo ufw allow ssh
sudo ufw allow http
sudo ufw allow https
sudo ufw enable

# 优化系统参数
echo "net.core.somaxconn = 65535" | sudo tee -a /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog = 65535" | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

#### 创建应用用户
```bash
# 创建应用用户
sudo useradd -m -s /bin/bash metaagent
sudo usermod -aG sudo metaagent

# 切换到应用用户
sudo su - metaagent
```

### 2. 应用部署

#### 部署后端
```bash
# 克隆代码
git clone <repository-url> /home/<USER>/meta-agent
cd /home/<USER>/meta-agent/backend

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，设置生产环境配置
```

#### 创建systemd服务
创建 `/etc/systemd/system/metaagent-backend.service`:
```ini
[Unit]
Description=Meta-Agent Backend
After=network.target

[Service]
Type=simple
User=metaagent
WorkingDirectory=/home/<USER>/meta-agent/backend
Environment=PATH=/home/<USER>/meta-agent/backend/venv/bin
ExecStart=/home/<USER>/meta-agent/backend/venv/bin/python simple_run.py
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

#### 启动后端服务
```bash
sudo systemctl daemon-reload
sudo systemctl enable metaagent-backend
sudo systemctl start metaagent-backend
sudo systemctl status metaagent-backend
```

#### 部署前端
```bash
cd /home/<USER>/meta-agent/frontend

# 安装依赖
npm ci --only=production

# 构建生产版本
npm run build

# 使用PM2管理进程
npm install -g pm2
pm2 start npm --name "metaagent-frontend" -- start
pm2 save
pm2 startup
```

### 3. Nginx配置

创建 `/etc/nginx/sites-available/metaagent`:
```nginx
upstream backend {
    server 127.0.0.1:8000;
}

upstream frontend {
    server 127.0.0.1:3000;
}

server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;

    # SSL配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    # API代理
    location /api/ {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 健康检查
    location /health {
        proxy_pass http://backend;
        proxy_set_header Host $host;
    }

    # 前端应用
    location / {
        proxy_pass http://frontend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

启用配置：
```bash
sudo ln -s /etc/nginx/sites-available/metaagent /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 4. SSL证书配置

```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 设置自动续期
sudo crontab -e
# 添加以下行：
# 0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 监控和日志

### 1. 日志配置

#### 应用日志
```bash
# 查看后端日志
sudo journalctl -u metaagent-backend -f

# 查看前端日志
pm2 logs metaagent-frontend

# 查看Nginx日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

#### 日志轮转
创建 `/etc/logrotate.d/metaagent`:
```
/home/<USER>/meta-agent/backend/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 metaagent metaagent
}
```

### 2. 服务配置

#### 健康检查
```bash
# 创建健康检查脚本
cat > /home/<USER>/health_check.sh << 'EOF'
#!/bin/bash
BACKEND_URL="http://localhost:8000/health"
FRONTEND_URL="http://localhost:3000"

# 检查后端
if curl -f -s $BACKEND_URL > /dev/null; then
    echo "Backend: OK"
else
    echo "Backend: FAILED"
    # 重启服务
    sudo systemctl restart metaagent-backend
fi

# 检查前端
if curl -f -s $FRONTEND_URL > /dev/null; then
    echo "Frontend: OK"
else
    echo "Frontend: FAILED"
    # 重启服务
    pm2 restart metaagent-frontend
fi
EOF

chmod +x /home/<USER>/health_check.sh

# 添加到crontab
crontab -e
# 添加：*/5 * * * * /home/<USER>/health_check.sh
```

## 🔧 维护和更新

### 1. 应用更新

```bash
# 更新脚本
cat > /home/<USER>/update.sh << 'EOF'
#!/bin/bash
cd /home/<USER>/meta-agent

# 备份当前版本
cp -r . ../meta-agent-backup-$(date +%Y%m%d-%H%M%S)

# 拉取最新代码
git pull origin main

# 更新后端
cd backend
source venv/bin/activate
pip install -r requirements.txt
sudo systemctl restart metaagent-backend

# 更新前端
cd ../frontend
npm ci --only=production
npm run build
pm2 restart metaagent-frontend

echo "Update completed!"
EOF

chmod +x /home/<USER>/update.sh
```

### 2. 数据备份

```bash
# 备份脚本
cat > /home/<USER>/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/home/<USER>/backups"
DATE=$(date +%Y%m%d-%H%M%S)

mkdir -p $BACKUP_DIR

# 备份生成的Agent代码
tar -czf $BACKUP_DIR/agents-$DATE.tar.gz /home/<USER>/meta-agent/backend/generated_agents

# 备份配置文件
tar -czf $BACKUP_DIR/config-$DATE.tar.gz /home/<USER>/meta-agent/backend/.env /home/<USER>/meta-agent/frontend/.env.local

# 清理旧备份（保留30天）
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

echo "Backup completed: $DATE"
EOF

chmod +x /home/<USER>/backup.sh

# 添加到crontab（每日备份）
crontab -e
# 添加：0 2 * * * /home/<USER>/backup.sh
```

## 🚨 故障排除

### 常见问题

1. **后端服务无法启动**
   ```bash
   # 检查日志
   sudo journalctl -u metaagent-backend -n 50
   
   # 检查端口占用
   sudo netstat -tlnp | grep :8000
   
   # 手动启动测试
   cd /home/<USER>/meta-agent/backend
   source venv/bin/activate
   python simple_run.py
   ```

2. **前端构建失败**
   ```bash
   # 清理缓存
   cd /home/<USER>/meta-agent/frontend
   rm -rf .next node_modules
   npm install
   npm run build
   ```

3. **Nginx配置错误**
   ```bash
   # 测试配置
   sudo nginx -t
   
   # 重新加载配置
   sudo systemctl reload nginx
   ```

### 系统优化

1. **数据库优化**
   - 定期清理过期数据
   - 添加适当的索引

2. **缓存优化**
   - 启用Redis缓存
   - 配置CDN
   - 优化静态资源缓存

3. **服务器优化**
   - 调整系统参数
   - 定期更新系统

这个部署指南提供了从开发到生产的完整部署流程，确保Meta-Agent系统能够稳定、安全地运行在各种环境中。
