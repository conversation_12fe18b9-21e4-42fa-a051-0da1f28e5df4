# Meta-Agent 自动生成和管理平台

## 🚀 项目简介

Meta-Agent是一个革命性的AI Agent自动生成和管理平台，能够根据用户的自然语言需求描述，自动设计、生成、部署和管理专业的AI Agent团队。这是一个真正意义上的"AI Agent工厂"，让AI Agent的创建变得像描述需求一样简单。

## ✨ 核心特性

### 🧠 智能需求分析
- **自动领域识别**：智能分析用户需求，识别所属领域（调查、技术、创意等）
- **复杂度评估**：自动评估任务复杂度，推荐合适的团队规模
- **模板推荐**：基于需求特征推荐最佳团队模板

### 👥 自动团队规划
- **团队成员设计**：根据需求自动设计团队成员角色、能力和职责
- **工作流程生成**：创建详细的团队协作工作流程
- **个性化定制**：支持基于模板的个性化调整和优化

### ⚙️ 代码自动生成
- **完整Agent类**：生成包含团队协作逻辑的完整Python类
- **配置文件**：自动生成Agent配置和元数据
- **文档生成**：自动生成README和使用说明

### 🔄 动态加载与执行
- **热加载**：支持运行时动态加载新生成的Agent
- **实例管理**：智能管理Agent实例的生命周期
- **执行引擎**：支持复杂的团队协作工作流程

### 📊 完整的管理界面
- **Agent列表**：查看所有Agent的状态和统计信息
- **实时监控**：监控Agent的执行状态和性能
- **测试工具**：内置的Agent测试和调试工具

## 🏗️ 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    前端 (Next.js + shadcn/ui)                │
├─────────────────────────────────────────────────────────────┤
│  Agent创建页面  │  Agent管理页面  │  Agent测试页面  │  监控页面  │
└─────────────────────────────────────────────────────────────┘
                              │
                         HTTP/REST API
                              │
┌─────────────────────────────────────────────────────────────┐
│                   后端 (FastAPI + Python)                   │
├─────────────────────────────────────────────────────────────┤
│  AI规划师  │  代码生成器  │  动态加载器  │  Agent管理器  │  监控系统  │
└─────────────────────────────────────────────────────────────┘
                              │
                    ┌─────────┴─────────┐
                    │                   │
            ┌───────▼────────┐  ┌──────▼──────┐
            │  生成的Agent代码  │  │   系统数据库   │
            └────────────────┘  └─────────────┘
```

## 🛠️ 技术栈

### 前端
- **Next.js 15**: React框架，支持SSR和现代化开发
- **TypeScript**: 类型安全的JavaScript
- **Tailwind CSS**: 实用优先的CSS框架
- **shadcn/ui**: 现代化的UI组件库
- **Axios**: HTTP客户端库

### 后端
- **FastAPI**: 现代化的Python Web框架
- **Python 3.11+**: 主要编程语言
- **Pydantic**: 数据验证和序列化
- **Jinja2**: 模板引擎，用于代码生成
- **SQLite**: 轻量级数据库

### AI技术
- **Prompt Engineering**: 高级提示词工程技术
- **JSON解析**: 智能JSON提取和修复
- **代码生成**: 基于模板的动态代码生成
- **动态加载**: Python动态模块加载技术

## 📁 项目结构

```
meta-agent/
├── frontend/                 # 前端项目
│   ├── src/
│   │   ├── app/             # Next.js应用页面
│   │   ├── components/      # React组件
│   │   ├── lib/            # 工具库和API
│   │   └── styles/         # 样式文件
│   ├── package.json
│   └── next.config.js
├── backend/                  # 后端项目
│   ├── app/
│   │   ├── api/            # API路由
│   │   ├── core/           # 核心功能
│   │   ├── models/         # 数据模型
│   │   ├── services/       # 业务逻辑
│   │   └── templates/      # 代码模板
│   ├── generated_agents/    # 生成的Agent代码
│   ├── requirements.txt
│   └── simple_run.py
├── docs/                    # 项目文档
│   ├── README.md           # 项目概述
│   ├── ARCHITECTURE.md     # 架构设计
│   ├── API.md             # API文档
│   ├── DEPLOYMENT.md      # 部署指南
│   └── USER_GUIDE.md      # 用户指南
└── test_system.py          # 系统测试脚本
```

## 🚀 快速开始

### 环境要求
- Node.js 18+
- Python 3.11+
- npm 或 yarn

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd meta-agent
```

2. **启动后端**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
python simple_run.py
```

3. **启动前端**
```bash
cd frontend
npm install
npm run dev
```

4. **访问应用**
- 前端界面: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

## 📖 使用指南

### 创建Agent团队

1. **访问创建页面**: 在浏览器中打开 http://localhost:3000/create
2. **描述需求**: 用自然语言描述你的需求，例如：
   - "我需要一个能够分析股票投资的专业团队"
   - "帮我创建一个技术咨询团队来评估项目架构"
   - "我想要一个创意写作团队来帮助我写小说"
3. **确认规划**: 系统会自动分析需求并生成团队规划
4. **生成Agent**: 确认规划后，系统会自动生成Agent代码并部署

### 管理Agent

1. **访问管理页面**: http://localhost:3000/manage
2. **查看Agent列表**: 查看所有已创建的Agent及其状态
3. **监控性能**: 查看Agent的使用统计和性能指标
4. **管理操作**: 启用/禁用、删除Agent

### 测试Agent

1. **访问测试页面**: http://localhost:3000/test
2. **选择Agent**: 从列表中选择要测试的Agent
3. **输入测试数据**: 输入测试问题或任务
4. **查看结果**: 查看Agent的执行结果和性能数据

## 🧪 系统测试

项目包含完整的端到端测试脚本：

```bash
python test_system.py
```

测试覆盖：
- 后端API健康检查
- 规划模板加载
- 需求分析功能
- 团队规划生成
- Agent创建和执行
- 前端页面可访问性

## 📚 文档导航

- [架构设计](./ARCHITECTURE.md) - 详细的系统架构说明
- [API文档](./API.md) - 完整的API接口文档
- [部署指南](./DEPLOYMENT.md) - 生产环境部署指南
- [用户指南](./USER_GUIDE.md) - 详细的使用说明
- [开发指南](./DEVELOPMENT.md) - 开发环境配置和贡献指南

## 🤝 贡献

欢迎贡献代码、报告问题或提出改进建议！

## 📄 许可证

本项目采用 MIT 许可证。

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！

---

**Meta-Agent - 让AI Agent的创建变得像描述需求一样简单！** 🚀
