# Meta-Agent 文档中心

欢迎来到Meta-Agent文档中心！这里包含了使用、开发和部署Meta-Agent系统所需的所有文档。

## 📚 文档导航

### 🚀 快速开始
- **[项目概述](./README.md)** - 了解Meta-Agent是什么，核心特性和技术栈
- **[用户指南](./USER_GUIDE.md)** - 详细的使用说明，从入门到高级功能

### 🔧 开发相关
- **[开发指南](./DEVELOPMENT.md)** - 开发环境配置、代码规范和最佳实践
- **[架构设计](./ARCHITECTURE.md)** - 系统架构、设计原则和技术选型
- **[API文档](./API.md)** - 完整的REST API接口文档

### 🚀 部署运维
- **[部署指南](./DEPLOYMENT.md)** - 从开发到生产的完整部署流程
- **[更新日志](./CHANGELOG.md)** - 版本更新记录和功能变更

## 🎯 按角色分类

### 👤 最终用户
如果您是Meta-Agent的使用者，建议按以下顺序阅读：

1. **[项目概述](./README.md)** - 了解基本概念
2. **[用户指南](./USER_GUIDE.md)** - 学习如何使用系统
3. **[API文档](./API.md)** - 如需API集成

**核心功能**:
- ✨ 自然语言描述需求，自动生成AI Agent团队
- 🎯 智能需求分析和模板推荐
- 👥 可视化团队管理和监控
- 🧪 交互式Agent测试和调试

### 👨‍💻 开发者
如果您要参与Meta-Agent的开发，建议按以下顺序阅读：

1. **[项目概述](./README.md)** - 了解项目背景
2. **[架构设计](./ARCHITECTURE.md)** - 理解系统架构
3. **[开发指南](./DEVELOPMENT.md)** - 配置开发环境
4. **[API文档](./API.md)** - 了解API设计

**技术栈**:
- 🎨 **前端**: Next.js + TypeScript + Tailwind CSS + shadcn/ui
- ⚙️ **后端**: FastAPI + Python + Pydantic + Jinja2
- 🤖 **AI技术**: Prompt Engineering + 代码生成 + 动态加载

### 🔧 运维人员
如果您负责Meta-Agent的部署和运维，建议按以下顺序阅读：

1. **[项目概述](./README.md)** - 了解系统要求
2. **[部署指南](./DEPLOYMENT.md)** - 学习部署流程
3. **[架构设计](./ARCHITECTURE.md)** - 理解系统架构
4. **[更新日志](./CHANGELOG.md)** - 了解版本变更

**部署选项**:
- 🖥️ **本地部署**: 开发和测试环境
- 🐳 **Docker部署**: 容器化部署方案
- ☁️ **云平台部署**: 生产环境部署
- 📊 **监控运维**: 系统监控和维护

## 🔍 按主题分类

### 🏗️ 系统架构
- **[架构设计](./ARCHITECTURE.md#整体架构)** - 系统整体架构图
- **[核心组件](./ARCHITECTURE.md#核心组件详解)** - AI规划师、代码生成器等
- **[数据流设计](./ARCHITECTURE.md#数据流设计)** - 数据处理流程
- **[技术选型](./ARCHITECTURE.md#技术选型理由)** - 技术栈选择理由

### 🤖 AI功能
- **[AI规划师](./ARCHITECTURE.md#ai规划师-ai-planner)** - 智能需求分析和团队规划
- **[代码生成器](./ARCHITECTURE.md#代码生成器-code-generator)** - 自动代码生成技术
- **[动态加载器](./ARCHITECTURE.md#动态加载器-dynamic-loader)** - 运行时代码加载
- **[Prompt工程](./DEVELOPMENT.md#ai技术)** - 提示词工程最佳实践

### 🎨 用户界面
- **[创建流程](./USER_GUIDE.md#创建agent团队)** - Agent创建完整流程
- **[管理功能](./USER_GUIDE.md#管理agent团队)** - Agent管理和监控
- **[测试工具](./USER_GUIDE.md#测试agent功能)** - Agent测试和调试
- **[界面设计](./DEVELOPMENT.md#前端架构)** - UI/UX设计原则

### 🔌 API集成
- **[REST API](./API.md#api-概览)** - 完整的API接口列表
- **[认证授权](./API.md#认证和授权)** - API安全机制
- **[错误处理](./API.md#错误处理)** - 错误码和处理方式
- **[使用示例](./API.md#api使用示例)** - 实际集成示例

### 🚀 部署运维
- **[环境配置](./DEPLOYMENT.md#开发环境部署)** - 开发环境搭建
- **[生产部署](./DEPLOYMENT.md#生产环境部署)** - 生产环境部署
- **[监控维护](./DEPLOYMENT.md#监控和日志)** - 系统监控和维护
- **[故障排除](./DEPLOYMENT.md#故障排除)** - 常见问题解决

## 📖 学习路径

### 🎓 初学者路径 (2-4小时)
1. 阅读 **[项目概述](./README.md)** (30分钟)
2. 跟随 **[快速开始](./README.md#快速开始)** 搭建环境 (1小时)
3. 阅读 **[用户指南](./USER_GUIDE.md)** 前半部分 (1小时)
4. 实际操作创建第一个Agent (30分钟)

### 🔧 开发者路径 (1-2天)
1. 完成初学者路径
2. 深入阅读 **[架构设计](./ARCHITECTURE.md)** (2小时)
3. 配置 **[开发环境](./DEVELOPMENT.md#开发环境配置)** (2小时)
4. 阅读 **[代码规范](./DEVELOPMENT.md#代码风格规范)** (1小时)
5. 运行测试和开发第一个功能 (4小时)

### 🚀 运维路径 (4-8小时)
1. 阅读 **[项目概述](./README.md)** 和 **[架构设计](./ARCHITECTURE.md)** (2小时)
2. 学习 **[部署指南](./DEPLOYMENT.md)** (3小时)
3. 实际部署测试环境 (2小时)
4. 配置监控和维护流程 (1小时)

## 🔗 外部资源

### 技术文档
- **[Next.js官方文档](https://nextjs.org/docs)** - 前端框架文档
- **[FastAPI官方文档](https://fastapi.tiangolo.com/)** - 后端框架文档
- **[Tailwind CSS文档](https://tailwindcss.com/docs)** - CSS框架文档
- **[shadcn/ui组件库](https://ui.shadcn.com/)** - UI组件文档

### 学习资源
- **[React官方教程](https://react.dev/learn)** - React学习资源
- **[Python异步编程](https://docs.python.org/3/library/asyncio.html)** - Python异步编程
- **[TypeScript手册](https://www.typescriptlang.org/docs/)** - TypeScript学习
- **[Docker官方文档](https://docs.docker.com/)** - 容器化部署

### 社区资源
- **[GitHub仓库](https://github.com/your-org/meta-agent)** - 源代码和Issue
- **[讨论社区](https://github.com/your-org/meta-agent/discussions)** - 用户讨论
- **[示例项目](https://github.com/your-org/meta-agent-examples)** - 使用示例
- **[博客文章](https://blog.your-domain.com/meta-agent)** - 技术博客

## 🆘 获取帮助

### 常见问题
在提问之前，请先查看：
- **[用户指南](./USER_GUIDE.md#故障排除)** 中的故障排除部分
- **[部署指南](./DEPLOYMENT.md#故障排除)** 中的常见问题
- **[GitHub Issues](https://github.com/your-org/meta-agent/issues)** 中的已知问题

### 提交问题
如果您遇到问题，请：
1. 搜索现有的Issues，避免重复提交
2. 提供详细的错误信息和复现步骤
3. 包含系统环境信息（操作系统、版本等）
4. 使用合适的标签分类问题

### 贡献文档
我们欢迎您为文档做出贡献：
- 修正错误和改进表达
- 添加使用示例和最佳实践
- 翻译文档到其他语言
- 提供反馈和改进建议

## 📝 文档维护

### 更新频率
- **用户指南**: 随功能更新同步更新
- **API文档**: 自动从代码生成，实时更新
- **架构设计**: 重大架构变更时更新
- **部署指南**: 随部署方式变化更新

### 版本管理
- 文档版本与软件版本保持同步
- 重要变更会在 **[更新日志](./CHANGELOG.md)** 中记录
- 旧版本文档会保留在对应的Git分支中

### 反馈渠道
- **GitHub Issues**: 报告文档问题
- **Pull Requests**: 直接提交文档改进
- **邮件反馈**: 发送到文档维护团队
- **用户调研**: 定期收集用户反馈

---

**感谢您使用Meta-Agent！如果这些文档对您有帮助，请给我们一个⭐️！**

## 📊 文档统计

| 文档 | 字数 | 更新时间 | 状态 |
|------|------|----------|------|
| README.md | ~3,000 | 2025-06-30 | ✅ 完整 |
| USER_GUIDE.md | ~5,000 | 2025-06-30 | ✅ 完整 |
| DEVELOPMENT.md | ~8,000 | 2025-06-30 | ✅ 完整 |
| ARCHITECTURE.md | ~6,000 | 2025-06-30 | ✅ 完整 |
| API.md | ~4,000 | 2025-06-30 | ✅ 完整 |
| DEPLOYMENT.md | ~7,000 | 2025-06-30 | ✅ 完整 |
| CHANGELOG.md | ~2,000 | 2025-06-30 | ✅ 完整 |

**总计**: ~35,000字的完整文档体系 📚
