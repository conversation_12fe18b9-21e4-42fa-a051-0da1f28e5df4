#!/usr/bin/env python3
"""
Template Population Script
Populates the template database with high-quality, ready-to-use agent team templates
covering multiple domains with complete configurations.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent / "backend"
sys.path.insert(0, str(backend_dir))

from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import get_async_session
from app.models.planning import Template, TemplateCategory, TemplateDifficulty, TemplateVisibility, TemplateStatus
import uuid


class TemplatePopulator:
    """Handles population of template database with high-quality templates."""
    
    def __init__(self):
        self.templates = []
        self._prepare_templates()
    
    def _prepare_templates(self):
        """Prepare all template definitions."""
        # Creative Writing Templates
        self.templates.extend(self._create_creative_writing_templates())
        
        # Data Analysis Templates  
        self.templates.extend(self._create_data_analysis_templates())
        
        # Product Development Templates
        self.templates.extend(self._create_product_development_templates())
        
        # Education Templates
        self.templates.extend(self._create_education_templates())
        
        # Business Strategy Templates
        self.templates.extend(self._create_business_strategy_templates())
        
        # Technical Documentation Templates
        self.templates.extend(self._create_technical_documentation_templates())
    
    def _create_creative_writing_templates(self):
        """Create creative writing domain templates."""
        return [
            {
                "name": "Content Creation Team",
                "description": "A comprehensive content creation team specializing in blog posts, articles, and digital content. Features specialized roles for research, writing, editing, and SEO optimization to produce high-quality, engaging content that drives audience engagement and meets business objectives.",
                "category": TemplateCategory.CREATIVE,
                "difficulty": TemplateDifficulty.INTERMEDIATE,
                "visibility": TemplateVisibility.FEATURED,
                "prompt_template": "Create engaging, high-quality content for {topic} targeting {audience}. Focus on {content_type} with {tone} tone. Include SEO optimization and ensure content aligns with brand guidelines and marketing objectives.",
                "team_structure_template": {
                    "team_name": "Content Creation Team",
                    "description": "Professional content creation team for digital marketing and publishing",
                    "objective": "Produce high-quality, engaging content that drives audience engagement and meets business goals",
                    "domain": "creative_writing",
                    "complexity": "intermediate",
                    "team_members": [
                        {
                            "name": "Content Strategist",
                            "role": "strategist",
                            "description": "Develops content strategy, identifies target audiences, and ensures content aligns with business objectives",
                            "system_prompt": "You are an expert Content Strategist with deep knowledge of digital marketing, audience analysis, and content planning. Your role is to develop comprehensive content strategies that align with business goals, identify target audiences, analyze market trends, and create content calendars. You excel at understanding brand voice, competitive analysis, and measuring content performance metrics to optimize strategy.",
                            "capabilities": ["strategy_development", "audience_analysis", "competitive_research", "content_planning"],
                            "tools": ["analytics", "research", "planning"],
                            "model_config": {
                                "model": "gpt-4",
                                "temperature": 0.7,
                                "max_tokens": 2000
                            },
                            "workflow_position": 1
                        },
                        {
                            "name": "Content Writer",
                            "role": "writer",
                            "description": "Creates compelling, well-researched content across various formats and platforms",
                            "system_prompt": "You are a skilled Content Writer specializing in creating engaging, informative, and persuasive content across multiple formats including blog posts, articles, social media content, and marketing copy. You excel at adapting writing style to different audiences, conducting thorough research, and incorporating SEO best practices while maintaining readability and engagement. Your writing is clear, compelling, and aligned with brand voice.",
                            "capabilities": ["content_writing", "research", "seo_optimization", "storytelling"],
                            "tools": ["writing", "research", "seo_tools"],
                            "model_config": {
                                "model": "gpt-4",
                                "temperature": 0.8,
                                "max_tokens": 3000
                            },
                            "workflow_position": 2
                        },
                        {
                            "name": "Content Editor",
                            "role": "editor",
                            "description": "Reviews, refines, and optimizes content for quality, consistency, and brand alignment",
                            "system_prompt": "You are a meticulous Content Editor with expertise in proofreading, copy editing, and content optimization. Your role involves reviewing content for grammar, style, consistency, and brand alignment. You ensure content meets quality standards, is optimized for readability, and effectively communicates the intended message. You provide constructive feedback and suggestions for improvement while maintaining the author's voice and intent.",
                            "capabilities": ["editing", "proofreading", "quality_assurance", "brand_consistency"],
                            "tools": ["editing_tools", "style_guides", "quality_checkers"],
                            "model_config": {
                                "model": "gpt-4",
                                "temperature": 0.5,
                                "max_tokens": 2000
                            },
                            "workflow_position": 3
                        }
                    ],
                    "workflow": {
                        "steps": [
                            {
                                "name": "Content Strategy Development",
                                "description": "Analyze requirements, develop content strategy, and create content brief",
                                "assignee": "Content Strategist",
                                "inputs": ["project_requirements", "target_audience", "business_goals"],
                                "outputs": ["content_strategy", "content_brief", "success_metrics"],
                                "dependencies": [],
                                "timeout": 300
                            },
                            {
                                "name": "Content Creation",
                                "description": "Research and write content based on strategy and brief",
                                "assignee": "Content Writer", 
                                "inputs": ["content_brief", "research_materials", "brand_guidelines"],
                                "outputs": ["draft_content", "supporting_materials"],
                                "dependencies": ["Content Strategy Development"],
                                "timeout": 600
                            },
                            {
                                "name": "Content Review and Optimization",
                                "description": "Edit, proofread, and optimize content for publication",
                                "assignee": "Content Editor",
                                "inputs": ["draft_content", "style_guide", "quality_standards"],
                                "outputs": ["final_content", "optimization_report"],
                                "dependencies": ["Content Creation"],
                                "timeout": 300
                            }
                        ],
                        "coordination": {
                            "orchestrator": "Content Strategist",
                            "communication_style": "collaborative",
                            "decision_making": "consensus"
                        },
                        "execution_mode": "sequential"
                    }
                },
                "tags": ["content", "writing", "marketing", "seo", "digital"],
                "keywords": ["content creation", "blog writing", "digital marketing", "seo"],
                "use_case": "Creating high-quality blog posts, articles, and digital content for marketing and engagement",
                "example_input": "Create a comprehensive blog post about sustainable business practices for small business owners",
                "expected_output": "A well-researched, engaging blog post with SEO optimization, clear structure, and actionable insights"
            },
            {
                "name": "Creative Storytelling Team",
                "description": "A specialized creative team focused on narrative development, character creation, and storytelling across various media formats. Combines creative writing expertise, narrative structure knowledge, and audience engagement strategies to create compelling stories that resonate with target audiences and achieve creative objectives.",
                "category": TemplateCategory.CREATIVE,
                "difficulty": TemplateDifficulty.ADVANCED,
                "visibility": TemplateVisibility.FEATURED,
                "prompt_template": "Develop a compelling story for {story_type} targeting {target_audience}. Include character development, plot structure, and narrative elements that align with {creative_goals}. Focus on {storytelling_style} and ensure emotional engagement and thematic coherence.",
                "team_structure_template": {
                    "team_name": "Creative Storytelling Team",
                    "description": "Expert creative team for narrative development and storytelling across media formats",
                    "objective": "Create compelling stories that resonate with audiences and achieve creative and commercial objectives",
                    "domain": "creative_writing",
                    "complexity": "advanced",
                    "team_members": [
                        {
                            "name": "Story Developer",
                            "role": "story_developer",
                            "description": "Develops narrative concepts, plot structures, and story arcs for various media formats",
                            "system_prompt": "You are an expert Story Developer with deep knowledge of narrative structure, plot development, and storytelling techniques across various media formats. You excel at creating compelling story concepts, developing engaging plot structures, and crafting narrative arcs that maintain audience interest. You understand genre conventions, pacing, conflict development, and how to create stories that resonate emotionally with target audiences while achieving creative and commercial objectives.",
                            "capabilities": ["narrative_development", "plot_structure", "story_concepts", "genre_expertise"],
                            "tools": ["story_planning", "narrative_frameworks", "plot_development", "creative_software"],
                            "model_config": {
                                "model": "gpt-4",
                                "temperature": 0.8,
                                "max_tokens": 3000
                            },
                            "workflow_position": 1
                        },
                        {
                            "name": "Character Designer",
                            "role": "character_designer",
                            "description": "Creates compelling characters with depth, motivation, and authentic development arcs",
                            "system_prompt": "You are a skilled Character Designer specializing in creating multi-dimensional, compelling characters that drive narrative engagement. You excel at developing character backgrounds, motivations, personality traits, and growth arcs that feel authentic and serve the story's themes. You understand character archetypes, relationship dynamics, and how to create characters that audiences connect with emotionally. Your characters are well-developed, consistent, and integral to the narrative structure.",
                            "capabilities": ["character_development", "personality_design", "character_arcs", "relationship_dynamics"],
                            "tools": ["character_development", "psychology_frameworks", "creative_tools", "reference_materials"],
                            "model_config": {
                                "model": "gpt-4",
                                "temperature": 0.7,
                                "max_tokens": 2500
                            },
                            "workflow_position": 2
                        },
                        {
                            "name": "Narrative Writer",
                            "role": "narrative_writer",
                            "description": "Crafts the final narrative with compelling prose, dialogue, and descriptive elements",
                            "system_prompt": "You are an accomplished Narrative Writer with expertise in crafting engaging prose, authentic dialogue, and vivid descriptive writing. You excel at bringing stories to life through compelling language, maintaining consistent voice and tone, and creating immersive reading experiences. You understand the nuances of different writing styles, can adapt to various genres and audiences, and know how to balance narrative elements to create cohesive, engaging stories that achieve their intended impact.",
                            "capabilities": ["prose_writing", "dialogue_creation", "descriptive_writing", "voice_development"],
                            "tools": ["writing_software", "editing_tools", "style_guides", "reference_resources"],
                            "model_config": {
                                "model": "gpt-4",
                                "temperature": 0.8,
                                "max_tokens": 3500
                            },
                            "workflow_position": 3
                        }
                    ],
                    "workflow": {
                        "steps": [
                            {
                                "name": "Story Concept and Structure Development",
                                "description": "Develop story concept, plot structure, and narrative framework",
                                "assignee": "Story Developer",
                                "inputs": ["creative_brief", "target_audience", "genre_requirements"],
                                "outputs": ["story_concept", "plot_outline", "narrative_structure"],
                                "dependencies": [],
                                "timeout": 450
                            },
                            {
                                "name": "Character Creation and Development",
                                "description": "Design and develop characters with depth and authentic motivations",
                                "assignee": "Character Designer",
                                "inputs": ["story_concept", "plot_outline", "character_requirements"],
                                "outputs": ["character_profiles", "character_arcs", "relationship_dynamics"],
                                "dependencies": ["Story Concept and Structure Development"],
                                "timeout": 400
                            },
                            {
                                "name": "Narrative Writing and Refinement",
                                "description": "Write the complete narrative with compelling prose and dialogue",
                                "assignee": "Narrative Writer",
                                "inputs": ["plot_outline", "character_profiles", "style_guidelines"],
                                "outputs": ["complete_narrative", "polished_prose", "final_story"],
                                "dependencies": ["Character Creation and Development"],
                                "timeout": 600
                            }
                        ],
                        "coordination": {
                            "orchestrator": "Story Developer",
                            "communication_style": "creative_collaborative",
                            "decision_making": "creative_consensus"
                        },
                        "execution_mode": "sequential"
                    }
                },
                "tags": ["creative", "storytelling", "narrative", "character development", "fiction"],
                "keywords": ["storytelling", "narrative development", "creative writing", "character design"],
                "use_case": "Creating compelling narratives and stories for various media formats and audiences",
                "example_input": "Develop a science fiction short story about AI consciousness for young adult readers",
                "expected_output": "Complete narrative with well-developed characters, engaging plot, and thematic depth"
            }
        ]
    
    def _create_data_analysis_templates(self):
        """Create data analysis domain templates."""
        return [
            {
                "name": "Business Intelligence Team",
                "description": "A specialized business intelligence team focused on data analysis, reporting, and insights generation. Combines data engineering, statistical analysis, and business intelligence expertise to transform raw data into actionable business insights and strategic recommendations.",
                "category": TemplateCategory.ANALYSIS,
                "difficulty": TemplateDifficulty.ADVANCED,
                "visibility": TemplateVisibility.FEATURED,
                "prompt_template": "Analyze {data_source} to generate insights about {business_question}. Focus on {analysis_type} and provide actionable recommendations for {stakeholder_group}. Include data visualization and statistical validation.",
                "team_structure_template": {
                    "team_name": "Business Intelligence Team",
                    "description": "Expert team for comprehensive data analysis and business intelligence",
                    "objective": "Transform raw data into actionable business insights and strategic recommendations",
                    "domain": "data_analysis",
                    "complexity": "advanced",
                    "team_members": [
                        {
                            "name": "Data Analyst",
                            "role": "analyst",
                            "description": "Performs statistical analysis, data exploration, and generates insights from complex datasets",
                            "system_prompt": "You are an expert Data Analyst with strong skills in statistical analysis, data exploration, and pattern recognition. You excel at working with large datasets, performing exploratory data analysis, identifying trends and anomalies, and generating meaningful insights. You are proficient in statistical methods, data visualization, and translating complex data findings into clear, actionable business insights. Your analysis is thorough, accurate, and focused on business value.",
                            "capabilities": ["statistical_analysis", "data_exploration", "pattern_recognition", "trend_analysis"],
                            "tools": ["python", "sql", "excel", "statistical_software"],
                            "model_config": {
                                "model": "gpt-4",
                                "temperature": 0.3,
                                "max_tokens": 2500
                            },
                            "workflow_position": 1
                        },
                        {
                            "name": "Business Intelligence Specialist",
                            "role": "bi_specialist",
                            "description": "Creates dashboards, reports, and visualizations to communicate data insights effectively",
                            "system_prompt": "You are a Business Intelligence Specialist expert in creating compelling data visualizations, interactive dashboards, and comprehensive reports. You excel at translating complex analytical findings into clear, visually appealing presentations that drive business decisions. You understand stakeholder needs, design principles for data visualization, and best practices for business reporting. Your work makes data accessible and actionable for all organizational levels.",
                            "capabilities": ["data_visualization", "dashboard_creation", "report_design", "stakeholder_communication"],
                            "tools": ["tableau", "power_bi", "excel", "presentation_tools"],
                            "model_config": {
                                "model": "gpt-4",
                                "temperature": 0.4,
                                "max_tokens": 2000
                            },
                            "workflow_position": 2
                        },
                        {
                            "name": "Data Strategist",
                            "role": "strategist",
                            "description": "Provides strategic recommendations and ensures analysis aligns with business objectives",
                            "system_prompt": "You are a Data Strategist with deep business acumen and expertise in translating data insights into strategic recommendations. You understand market dynamics, business operations, and organizational goals. Your role is to ensure data analysis drives meaningful business outcomes, identify strategic opportunities, and provide actionable recommendations that align with company objectives. You excel at connecting data insights to business value and ROI.",
                            "capabilities": ["strategic_thinking", "business_analysis", "recommendation_development", "roi_analysis"],
                            "tools": ["business_analysis", "strategic_planning", "financial_modeling"],
                            "model_config": {
                                "model": "gpt-4",
                                "temperature": 0.6,
                                "max_tokens": 2000
                            },
                            "workflow_position": 3
                        }
                    ],
                    "workflow": {
                        "steps": [
                            {
                                "name": "Data Analysis and Exploration",
                                "description": "Perform comprehensive statistical analysis and data exploration",
                                "assignee": "Data Analyst",
                                "inputs": ["raw_data", "business_questions", "analysis_requirements"],
                                "outputs": ["statistical_analysis", "data_insights", "findings_summary"],
                                "dependencies": [],
                                "timeout": 600
                            },
                            {
                                "name": "Visualization and Reporting",
                                "description": "Create visualizations, dashboards, and comprehensive reports",
                                "assignee": "Business Intelligence Specialist",
                                "inputs": ["statistical_analysis", "data_insights", "stakeholder_requirements"],
                                "outputs": ["dashboards", "visualizations", "detailed_reports"],
                                "dependencies": ["Data Analysis and Exploration"],
                                "timeout": 450
                            },
                            {
                                "name": "Strategic Recommendations",
                                "description": "Develop strategic recommendations and action plans based on insights",
                                "assignee": "Data Strategist",
                                "inputs": ["data_insights", "business_context", "organizational_goals"],
                                "outputs": ["strategic_recommendations", "action_plans", "roi_projections"],
                                "dependencies": ["Visualization and Reporting"],
                                "timeout": 300
                            }
                        ],
                        "coordination": {
                            "orchestrator": "Data Strategist",
                            "communication_style": "data_driven",
                            "decision_making": "evidence_based"
                        },
                        "execution_mode": "sequential"
                    }
                },
                "tags": ["data", "analysis", "business intelligence", "insights", "reporting"],
                "keywords": ["data analysis", "business intelligence", "reporting", "insights"],
                "use_case": "Analyzing business data to generate insights and recommendations for strategic decision-making",
                "example_input": "Analyze sales data to identify trends and opportunities for revenue growth",
                "expected_output": "Comprehensive analysis report with insights, visualizations, and actionable recommendations"
            },
            {
                "name": "Research Analytics Team",
                "description": "A specialized research team focused on comprehensive data research, statistical analysis, and insights generation. Combines research methodology expertise, advanced analytics capabilities, and report writing skills to conduct thorough investigations and produce actionable research findings for academic, business, or policy applications.",
                "category": TemplateCategory.RESEARCH,
                "difficulty": TemplateDifficulty.EXPERT,
                "visibility": TemplateVisibility.FEATURED,
                "prompt_template": "Conduct comprehensive research on {research_topic} using {research_methodology}. Include literature review, data collection, statistical analysis, and evidence-based conclusions. Focus on {research_objectives} and ensure methodological rigor and validity.",
                "team_structure_template": {
                    "team_name": "Research Analytics Team",
                    "description": "Expert research team for comprehensive data research and analytical investigations",
                    "objective": "Conduct thorough research investigations and produce actionable, evidence-based findings",
                    "domain": "research_analytics",
                    "complexity": "expert",
                    "team_members": [
                        {
                            "name": "Research Methodologist",
                            "role": "research_methodologist",
                            "description": "Designs research methodology, ensures scientific rigor, and validates research approaches",
                            "system_prompt": "You are an expert Research Methodologist with extensive knowledge of research design, statistical methods, and scientific methodology. You excel at designing robust research studies, selecting appropriate methodologies, ensuring validity and reliability, and maintaining scientific rigor throughout the research process. You understand various research paradigms, sampling techniques, and how to address potential biases and limitations. Your approach is systematic, evidence-based, and methodologically sound.",
                            "capabilities": ["research_design", "methodology_selection", "validity_assessment", "bias_mitigation"],
                            "tools": ["statistical_software", "research_databases", "methodology_frameworks", "validation_tools"],
                            "model_config": {
                                "model": "gpt-4",
                                "temperature": 0.3,
                                "max_tokens": 2500
                            },
                            "workflow_position": 1
                        },
                        {
                            "name": "Data Research Analyst",
                            "role": "data_researcher",
                            "description": "Conducts data collection, performs statistical analysis, and generates research insights",
                            "system_prompt": "You are a skilled Data Research Analyst specializing in comprehensive data collection, advanced statistical analysis, and research insight generation. You excel at gathering data from multiple sources, applying appropriate analytical techniques, and identifying meaningful patterns and relationships in complex datasets. You understand research ethics, data quality assessment, and how to draw valid conclusions from empirical evidence. Your analysis is thorough, objective, and scientifically rigorous.",
                            "capabilities": ["data_collection", "statistical_analysis", "pattern_recognition", "empirical_research"],
                            "tools": ["data_collection_tools", "statistical_packages", "analysis_software", "visualization_tools"],
                            "model_config": {
                                "model": "gpt-4",
                                "temperature": 0.4,
                                "max_tokens": 3000
                            },
                            "workflow_position": 2
                        },
                        {
                            "name": "Research Writer",
                            "role": "research_writer",
                            "description": "Synthesizes findings and creates comprehensive research reports and publications",
                            "system_prompt": "You are an accomplished Research Writer with expertise in academic and professional research communication. You excel at synthesizing complex research findings, creating clear and compelling research reports, and communicating scientific results to diverse audiences. You understand academic writing standards, citation practices, and how to present research findings in a logical, persuasive manner. Your writing is precise, well-structured, and effectively communicates the significance and implications of research results.",
                            "capabilities": ["research_writing", "synthesis", "academic_communication", "report_development"],
                            "tools": ["writing_software", "citation_management", "formatting_tools", "collaboration_platforms"],
                            "model_config": {
                                "model": "gpt-4",
                                "temperature": 0.5,
                                "max_tokens": 3000
                            },
                            "workflow_position": 3
                        }
                    ],
                    "workflow": {
                        "steps": [
                            {
                                "name": "Research Design and Methodology",
                                "description": "Design research approach, methodology, and data collection strategy",
                                "assignee": "Research Methodologist",
                                "inputs": ["research_questions", "objectives", "constraints"],
                                "outputs": ["research_design", "methodology_plan", "data_collection_strategy"],
                                "dependencies": [],
                                "timeout": 400
                            },
                            {
                                "name": "Data Collection and Analysis",
                                "description": "Collect data and perform comprehensive statistical analysis",
                                "assignee": "Data Research Analyst",
                                "inputs": ["methodology_plan", "data_sources", "analysis_requirements"],
                                "outputs": ["collected_data", "statistical_analysis", "preliminary_findings"],
                                "dependencies": ["Research Design and Methodology"],
                                "timeout": 700
                            },
                            {
                                "name": "Research Synthesis and Reporting",
                                "description": "Synthesize findings and create comprehensive research report",
                                "assignee": "Research Writer",
                                "inputs": ["statistical_analysis", "preliminary_findings", "research_context"],
                                "outputs": ["research_report", "executive_summary", "recommendations"],
                                "dependencies": ["Data Collection and Analysis"],
                                "timeout": 500
                            }
                        ],
                        "coordination": {
                            "orchestrator": "Research Methodologist",
                            "communication_style": "scientific",
                            "decision_making": "evidence_based"
                        },
                        "execution_mode": "sequential"
                    }
                },
                "tags": ["research", "analytics", "data analysis", "methodology", "insights"],
                "keywords": ["research methodology", "data analysis", "statistical analysis", "research insights"],
                "use_case": "Conducting comprehensive research investigations with rigorous methodology and analysis",
                "example_input": "Research the impact of remote work on employee productivity and job satisfaction",
                "expected_output": "Comprehensive research report with methodology, findings, analysis, and evidence-based recommendations"
            }
        ]

    def _create_product_development_templates(self):
        """Create product development domain templates."""
        return [
            {
                "name": "Product Strategy Team",
                "description": "A comprehensive product development team specializing in feature planning, user research, and technical specifications. Combines product management expertise, user experience research, and technical architecture to deliver successful product initiatives that meet user needs and business objectives.",
                "category": TemplateCategory.PRODUCT,
                "difficulty": TemplateDifficulty.ADVANCED,
                "visibility": TemplateVisibility.FEATURED,
                "prompt_template": "Develop a comprehensive product strategy for {product_feature} targeting {user_segment}. Include user research insights, technical requirements, and implementation roadmap. Focus on {success_metrics} and ensure alignment with business goals.",
                "team_structure_template": {
                    "team_name": "Product Strategy Team",
                    "description": "Expert product development team for strategic planning and execution",
                    "objective": "Deliver successful product initiatives through comprehensive planning, user research, and technical excellence",
                    "domain": "product_development",
                    "complexity": "advanced",
                    "team_members": [
                        {
                            "name": "Product Manager",
                            "role": "product_manager",
                            "description": "Leads product strategy, defines requirements, and coordinates cross-functional execution",
                            "system_prompt": "You are an experienced Product Manager with expertise in product strategy, roadmap planning, and cross-functional team leadership. You excel at understanding market needs, defining product requirements, prioritizing features, and ensuring successful product delivery. You have strong analytical skills, understand user experience principles, and can balance business objectives with technical constraints. Your approach is data-driven, user-focused, and results-oriented.",
                            "capabilities": ["product_strategy", "roadmap_planning", "requirement_definition", "stakeholder_management"],
                            "tools": ["product_management", "analytics", "project_management", "user_research"],
                            "model_config": {
                                "model": "gpt-4",
                                "temperature": 0.6,
                                "max_tokens": 2500
                            },
                            "workflow_position": 1
                        },
                        {
                            "name": "UX Researcher",
                            "role": "ux_researcher",
                            "description": "Conducts user research, analyzes user behavior, and provides insights for product decisions",
                            "system_prompt": "You are a skilled UX Researcher specializing in user behavior analysis, usability testing, and research methodology. You excel at designing and conducting user research studies, analyzing qualitative and quantitative data, and translating research findings into actionable product insights. You understand various research methods, can identify user pain points and opportunities, and effectively communicate research findings to product teams and stakeholders.",
                            "capabilities": ["user_research", "usability_testing", "data_analysis", "insight_generation"],
                            "tools": ["research_tools", "analytics", "survey_platforms", "testing_tools"],
                            "model_config": {
                                "model": "gpt-4",
                                "temperature": 0.5,
                                "max_tokens": 2000
                            },
                            "workflow_position": 2
                        },
                        {
                            "name": "Technical Architect",
                            "role": "technical_architect",
                            "description": "Defines technical architecture, evaluates implementation approaches, and ensures technical feasibility",
                            "system_prompt": "You are a Technical Architect with deep expertise in software architecture, system design, and technology evaluation. You excel at designing scalable, maintainable technical solutions, evaluating different implementation approaches, and ensuring technical feasibility of product requirements. You understand modern development practices, can assess technical risks, and provide guidance on technology choices and architectural decisions that support product goals.",
                            "capabilities": ["system_architecture", "technical_design", "technology_evaluation", "risk_assessment"],
                            "tools": ["architecture_tools", "development_frameworks", "design_patterns", "technical_documentation"],
                            "model_config": {
                                "model": "gpt-4",
                                "temperature": 0.4,
                                "max_tokens": 2500
                            },
                            "workflow_position": 3
                        }
                    ],
                    "workflow": {
                        "steps": [
                            {
                                "name": "Product Strategy and Planning",
                                "description": "Define product strategy, requirements, and success metrics",
                                "assignee": "Product Manager",
                                "inputs": ["business_objectives", "market_analysis", "user_feedback"],
                                "outputs": ["product_strategy", "feature_requirements", "success_metrics"],
                                "dependencies": [],
                                "timeout": 450
                            },
                            {
                                "name": "User Research and Validation",
                                "description": "Conduct user research to validate assumptions and gather insights",
                                "assignee": "UX Researcher",
                                "inputs": ["product_strategy", "user_segments", "research_questions"],
                                "outputs": ["research_findings", "user_insights", "validation_results"],
                                "dependencies": ["Product Strategy and Planning"],
                                "timeout": 600
                            },
                            {
                                "name": "Technical Architecture and Planning",
                                "description": "Design technical architecture and implementation approach",
                                "assignee": "Technical Architect",
                                "inputs": ["feature_requirements", "technical_constraints", "scalability_needs"],
                                "outputs": ["technical_architecture", "implementation_plan", "risk_assessment"],
                                "dependencies": ["User Research and Validation"],
                                "timeout": 450
                            }
                        ],
                        "coordination": {
                            "orchestrator": "Product Manager",
                            "communication_style": "collaborative",
                            "decision_making": "consensus"
                        },
                        "execution_mode": "sequential"
                    }
                },
                "tags": ["product", "development", "strategy", "user research", "technical"],
                "keywords": ["product management", "user research", "technical architecture", "product strategy"],
                "use_case": "Developing comprehensive product strategies with user research and technical planning",
                "example_input": "Plan a new mobile app feature for user onboarding improvement",
                "expected_output": "Complete product strategy with user research insights, technical architecture, and implementation roadmap"
            }
        ]

    def _create_education_templates(self):
        """Create education domain templates."""
        return [
            {
                "name": "Curriculum Design Team",
                "description": "A specialized education team focused on curriculum development, instructional design, and learning assessment. Combines pedagogical expertise, content development skills, and assessment design to create effective, engaging educational experiences that achieve learning objectives and support student success.",
                "category": TemplateCategory.EDUCATION,
                "difficulty": TemplateDifficulty.INTERMEDIATE,
                "visibility": TemplateVisibility.FEATURED,
                "prompt_template": "Design a comprehensive curriculum for {subject_area} targeting {learner_level}. Include learning objectives, instructional strategies, assessment methods, and engagement techniques. Focus on {learning_outcomes} and ensure accessibility and inclusivity.",
                "team_structure_template": {
                    "team_name": "Curriculum Design Team",
                    "description": "Expert education team for comprehensive curriculum development and instructional design",
                    "objective": "Create effective, engaging educational experiences that achieve learning objectives and support student success",
                    "domain": "education",
                    "complexity": "intermediate",
                    "team_members": [
                        {
                            "name": "Instructional Designer",
                            "role": "instructional_designer",
                            "description": "Designs learning experiences, develops instructional strategies, and ensures pedagogical effectiveness",
                            "system_prompt": "You are an expert Instructional Designer with deep knowledge of learning theories, pedagogical approaches, and educational technology. You excel at designing engaging, effective learning experiences that accommodate different learning styles and achieve specific learning objectives. You understand curriculum development, assessment design, and how to create inclusive, accessible educational content. Your approach is learner-centered, evidence-based, and focused on measurable learning outcomes.",
                            "capabilities": ["curriculum_design", "learning_theory", "instructional_strategies", "assessment_design"],
                            "tools": ["learning_management_systems", "educational_technology", "assessment_tools", "design_software"],
                            "model_config": {
                                "model": "gpt-4",
                                "temperature": 0.6,
                                "max_tokens": 2500
                            },
                            "workflow_position": 1
                        },
                        {
                            "name": "Subject Matter Expert",
                            "role": "subject_expert",
                            "description": "Provides domain expertise, validates content accuracy, and ensures curriculum relevance",
                            "system_prompt": "You are a Subject Matter Expert with deep knowledge in your field and experience in educational content development. You excel at ensuring content accuracy, relevance, and alignment with industry standards. You understand how to translate complex subject matter into accessible learning materials and can identify key concepts, skills, and competencies that learners need to master. Your expertise ensures curriculum quality and real-world applicability.",
                            "capabilities": ["domain_expertise", "content_validation", "industry_alignment", "competency_mapping"],
                            "tools": ["research_databases", "industry_resources", "professional_networks", "validation_tools"],
                            "model_config": {
                                "model": "gpt-4",
                                "temperature": 0.4,
                                "max_tokens": 2000
                            },
                            "workflow_position": 2
                        }
                    ],
                    "workflow": {
                        "steps": [
                            {
                                "name": "Curriculum Planning and Design",
                                "description": "Design comprehensive curriculum structure and learning framework",
                                "assignee": "Instructional Designer",
                                "inputs": ["learning_objectives", "target_audience", "subject_requirements"],
                                "outputs": ["curriculum_framework", "learning_modules", "assessment_strategy"],
                                "dependencies": [],
                                "timeout": 450
                            },
                            {
                                "name": "Content Development and Validation",
                                "description": "Develop educational content and validate accuracy and relevance",
                                "assignee": "Subject Matter Expert",
                                "inputs": ["curriculum_framework", "industry_standards", "competency_requirements"],
                                "outputs": ["educational_content", "learning_materials", "competency_mapping"],
                                "dependencies": ["Curriculum Planning and Design"],
                                "timeout": 500
                            },
                            {
                                "name": "Curriculum Integration and Finalization",
                                "description": "Integrate content into final curriculum with assessments and activities",
                                "assignee": "Instructional Designer",
                                "inputs": ["educational_content", "learning_materials", "assessment_requirements"],
                                "outputs": ["complete_curriculum", "instructor_guides", "student_resources"],
                                "dependencies": ["Content Development and Validation"],
                                "timeout": 350
                            }
                        ],
                        "coordination": {
                            "orchestrator": "Instructional Designer",
                            "communication_style": "educational",
                            "decision_making": "pedagogical_best_practices"
                        },
                        "execution_mode": "sequential"
                    }
                },
                "tags": ["education", "curriculum", "instructional design", "learning", "assessment"],
                "keywords": ["curriculum design", "instructional design", "education", "learning"],
                "use_case": "Designing comprehensive educational curricula and learning experiences",
                "example_input": "Create a curriculum for teaching data science fundamentals to business professionals",
                "expected_output": "Complete curriculum with learning objectives, modules, activities, and assessment strategies"
            }
        ]

    def _create_business_strategy_templates(self):
        """Create business strategy domain templates."""
        return [
            {
                "name": "Strategic Consulting Team",
                "description": "A comprehensive business strategy team specializing in market analysis, strategic planning, and business consulting. Combines strategic thinking, market research expertise, and business analysis to deliver actionable strategic recommendations that drive business growth and competitive advantage.",
                "category": TemplateCategory.BUSINESS,
                "difficulty": TemplateDifficulty.EXPERT,
                "visibility": TemplateVisibility.FEATURED,
                "prompt_template": "Develop a comprehensive business strategy for {business_challenge} in {industry_context}. Include market analysis, competitive assessment, strategic recommendations, and implementation roadmap. Focus on {strategic_objectives} and ensure alignment with organizational capabilities.",
                "team_structure_template": {
                    "team_name": "Strategic Consulting Team",
                    "description": "Expert business strategy team for comprehensive strategic planning and consulting",
                    "objective": "Deliver actionable strategic recommendations that drive business growth and competitive advantage",
                    "domain": "business_strategy",
                    "complexity": "expert",
                    "team_members": [
                        {
                            "name": "Strategy Consultant",
                            "role": "strategy_consultant",
                            "description": "Leads strategic analysis, develops recommendations, and guides strategic planning processes",
                            "system_prompt": "You are a senior Strategy Consultant with extensive experience in business strategy, strategic planning, and organizational transformation. You excel at analyzing complex business challenges, identifying strategic opportunities, and developing comprehensive strategic recommendations. You understand market dynamics, competitive analysis, and strategic frameworks. Your approach is analytical, data-driven, and focused on delivering actionable insights that create sustainable competitive advantage.",
                            "capabilities": ["strategic_analysis", "strategic_planning", "business_transformation", "competitive_strategy"],
                            "tools": ["strategic_frameworks", "analysis_tools", "planning_software", "presentation_tools"],
                            "model_config": {
                                "model": "gpt-4",
                                "temperature": 0.6,
                                "max_tokens": 3000
                            },
                            "workflow_position": 1
                        },
                        {
                            "name": "Market Research Analyst",
                            "role": "market_analyst",
                            "description": "Conducts comprehensive market research, analyzes industry trends, and provides market insights",
                            "system_prompt": "You are an expert Market Research Analyst specializing in industry analysis, market trends, and competitive intelligence. You excel at gathering and analyzing market data, identifying emerging trends, and providing insights that inform strategic decisions. You understand research methodologies, data analysis techniques, and how to translate market intelligence into actionable business insights. Your analysis is thorough, objective, and strategically relevant.",
                            "capabilities": ["market_research", "industry_analysis", "competitive_intelligence", "trend_analysis"],
                            "tools": ["research_databases", "analytics_platforms", "survey_tools", "data_visualization"],
                            "model_config": {
                                "model": "gpt-4",
                                "temperature": 0.4,
                                "max_tokens": 2500
                            },
                            "workflow_position": 2
                        },
                        {
                            "name": "Business Analyst",
                            "role": "business_analyst",
                            "description": "Analyzes business operations, financial performance, and organizational capabilities",
                            "system_prompt": "You are a skilled Business Analyst with expertise in business process analysis, financial modeling, and organizational assessment. You excel at analyzing business operations, identifying improvement opportunities, and evaluating strategic options from a financial and operational perspective. You understand business metrics, financial analysis, and how to assess organizational readiness for strategic initiatives. Your analysis provides the foundation for informed strategic decision-making.",
                            "capabilities": ["business_analysis", "financial_modeling", "process_analysis", "organizational_assessment"],
                            "tools": ["financial_modeling", "process_mapping", "analytics_software", "business_intelligence"],
                            "model_config": {
                                "model": "gpt-4",
                                "temperature": 0.3,
                                "max_tokens": 2500
                            },
                            "workflow_position": 3
                        }
                    ],
                    "workflow": {
                        "steps": [
                            {
                                "name": "Strategic Assessment and Planning",
                                "description": "Conduct strategic assessment and develop strategic framework",
                                "assignee": "Strategy Consultant",
                                "inputs": ["business_challenge", "organizational_context", "strategic_objectives"],
                                "outputs": ["strategic_framework", "assessment_plan", "key_questions"],
                                "dependencies": [],
                                "timeout": 450
                            },
                            {
                                "name": "Market Research and Analysis",
                                "description": "Conduct comprehensive market research and competitive analysis",
                                "assignee": "Market Research Analyst",
                                "inputs": ["strategic_framework", "industry_context", "competitive_landscape"],
                                "outputs": ["market_analysis", "competitive_assessment", "industry_insights"],
                                "dependencies": ["Strategic Assessment and Planning"],
                                "timeout": 600
                            },
                            {
                                "name": "Business Analysis and Recommendations",
                                "description": "Analyze business capabilities and develop strategic recommendations",
                                "assignee": "Business Analyst",
                                "inputs": ["market_analysis", "organizational_data", "financial_information"],
                                "outputs": ["business_analysis", "strategic_recommendations", "implementation_roadmap"],
                                "dependencies": ["Market Research and Analysis"],
                                "timeout": 450
                            }
                        ],
                        "coordination": {
                            "orchestrator": "Strategy Consultant",
                            "communication_style": "analytical",
                            "decision_making": "evidence_based"
                        },
                        "execution_mode": "sequential"
                    }
                },
                "tags": ["business", "strategy", "consulting", "market analysis", "planning"],
                "keywords": ["business strategy", "strategic planning", "market analysis", "consulting"],
                "use_case": "Developing comprehensive business strategies and strategic recommendations",
                "example_input": "Develop a market expansion strategy for a SaaS company entering the European market",
                "expected_output": "Complete strategic plan with market analysis, competitive assessment, and implementation roadmap"
            }
        ]

    def _create_technical_documentation_templates(self):
        """Create technical documentation domain templates."""
        return [
            {
                "name": "Technical Documentation Team",
                "description": "A specialized technical documentation team focused on creating comprehensive, user-friendly technical documentation. Combines technical writing expertise, user experience design, and information architecture to produce clear, accurate, and accessible documentation that serves both technical and non-technical audiences effectively.",
                "category": TemplateCategory.TECHNICAL,
                "difficulty": TemplateDifficulty.INTERMEDIATE,
                "visibility": TemplateVisibility.FEATURED,
                "prompt_template": "Create comprehensive technical documentation for {technical_subject} targeting {audience_type}. Include {documentation_type} with clear explanations, examples, and best practices. Focus on usability, accuracy, and accessibility for {user_goals}.",
                "team_structure_template": {
                    "team_name": "Technical Documentation Team",
                    "description": "Expert team for comprehensive technical documentation and information design",
                    "objective": "Produce clear, accurate, and accessible technical documentation that serves both technical and non-technical audiences effectively",
                    "domain": "technical_documentation",
                    "complexity": "intermediate",
                    "team_members": [
                        {
                            "name": "Technical Writer",
                            "role": "technical_writer",
                            "description": "Creates clear, comprehensive technical documentation and user guides",
                            "system_prompt": "You are an expert Technical Writer specializing in creating clear, comprehensive, and user-friendly technical documentation. You excel at translating complex technical concepts into accessible language, organizing information logically, and creating documentation that serves diverse audiences. You understand documentation best practices, information architecture, and how to balance technical accuracy with usability. Your writing is precise, well-structured, and focused on user needs.",
                            "capabilities": ["technical_writing", "information_design", "user_experience", "content_organization"],
                            "tools": ["documentation_platforms", "writing_tools", "version_control", "collaboration_tools"],
                            "model_config": {
                                "model": "gpt-4",
                                "temperature": 0.5,
                                "max_tokens": 3000
                            },
                            "workflow_position": 1
                        },
                        {
                            "name": "Information Architect",
                            "role": "information_architect",
                            "description": "Designs information structure, navigation, and user experience for documentation",
                            "system_prompt": "You are an Information Architect specializing in documentation structure, user experience design, and information organization. You excel at creating logical information hierarchies, designing intuitive navigation systems, and ensuring documentation is accessible and user-friendly. You understand user research, information design principles, and how to organize complex technical information in ways that serve diverse user needs and use cases effectively.",
                            "capabilities": ["information_architecture", "user_experience", "navigation_design", "content_organization"],
                            "tools": ["wireframing_tools", "user_research", "information_design", "usability_testing"],
                            "model_config": {
                                "model": "gpt-4",
                                "temperature": 0.4,
                                "max_tokens": 2000
                            },
                            "workflow_position": 2
                        },
                        {
                            "name": "Documentation Editor",
                            "role": "documentation_editor",
                            "description": "Reviews, edits, and optimizes documentation for clarity, accuracy, and consistency",
                            "system_prompt": "You are a Documentation Editor with expertise in technical editing, quality assurance, and documentation standards. You excel at reviewing technical content for accuracy, clarity, and consistency, ensuring documentation meets quality standards and serves user needs effectively. You understand technical communication principles, style guides, and how to maintain consistency across large documentation sets while preserving technical accuracy and usability.",
                            "capabilities": ["technical_editing", "quality_assurance", "consistency_review", "standards_compliance"],
                            "tools": ["editing_software", "style_checkers", "quality_tools", "collaboration_platforms"],
                            "model_config": {
                                "model": "gpt-4",
                                "temperature": 0.3,
                                "max_tokens": 2000
                            },
                            "workflow_position": 3
                        }
                    ],
                    "workflow": {
                        "steps": [
                            {
                                "name": "Documentation Planning and Architecture",
                                "description": "Plan documentation structure and information architecture",
                                "assignee": "Information Architect",
                                "inputs": ["technical_requirements", "user_needs", "content_scope"],
                                "outputs": ["documentation_architecture", "content_structure", "navigation_design"],
                                "dependencies": [],
                                "timeout": 300
                            },
                            {
                                "name": "Content Creation and Writing",
                                "description": "Create comprehensive technical content and documentation",
                                "assignee": "Technical Writer",
                                "inputs": ["documentation_architecture", "technical_specifications", "user_scenarios"],
                                "outputs": ["technical_content", "user_guides", "reference_materials"],
                                "dependencies": ["Documentation Planning and Architecture"],
                                "timeout": 600
                            },
                            {
                                "name": "Review and Quality Assurance",
                                "description": "Review, edit, and optimize documentation for quality and consistency",
                                "assignee": "Documentation Editor",
                                "inputs": ["technical_content", "quality_standards", "style_guidelines"],
                                "outputs": ["final_documentation", "quality_report", "publication_ready_content"],
                                "dependencies": ["Content Creation and Writing"],
                                "timeout": 300
                            }
                        ],
                        "coordination": {
                            "orchestrator": "Technical Writer",
                            "communication_style": "technical_collaborative",
                            "decision_making": "user_centered"
                        },
                        "execution_mode": "sequential"
                    }
                },
                "tags": ["technical", "documentation", "writing", "user guides", "api"],
                "keywords": ["technical writing", "documentation", "user guides", "api documentation"],
                "use_case": "Creating comprehensive technical documentation and user guides",
                "example_input": "Create API documentation for a REST API with authentication and data management endpoints",
                "expected_output": "Complete API documentation with clear explanations, examples, and integration guides"
            }
        ]

    async def populate_database(self):
        """Populate the database with all prepared templates."""
        async for session in get_async_session():
            try:
                print(f"Starting template population with {len(self.templates)} templates...")
                
                for template_data in self.templates:
                    await self._create_template(session, template_data)
                
                await session.commit()
                print(f"Successfully populated {len(self.templates)} templates!")
                
            except Exception as e:
                await session.rollback()
                print(f"Error populating templates: {e}")
                raise
            finally:
                await session.close()
    
    async def _create_template(self, session: AsyncSession, template_data: dict):
        """Create a single template in the database."""
        template_id = f"template_{uuid.uuid4().hex[:12]}"
        
        template = Template(
            template_id=template_id,
            name=template_data["name"],
            description=template_data["description"],
            category=template_data["category"],
            difficulty=template_data["difficulty"],
            visibility=template_data["visibility"],
            status=TemplateStatus.ACTIVE,
            prompt_template=template_data["prompt_template"],
            team_structure_template=template_data["team_structure_template"],
            default_config={},
            tags=template_data.get("tags", []),
            keywords=template_data.get("keywords", []),
            use_case=template_data.get("use_case"),
            example_input=template_data.get("example_input"),
            expected_output=template_data.get("expected_output"),
            user_id=None,  # System templates
            author_name="System",
            usage_count=0,
            rating_count=0,
            version="1.0.0",
            metadata={
                "system_template": True,
                "created_by_script": True,
                "quality_verified": True
            }
        )
        
        session.add(template)
        print(f"Created template: {template.name}")


async def main():
    """Main function to run template population."""
    populator = TemplatePopulator()
    await populator.populate_database()


if __name__ == "__main__":
    asyncio.run(main())
