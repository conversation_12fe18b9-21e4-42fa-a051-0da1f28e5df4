#!/bin/bash

# Meta-Agent 开发服务启动脚本

set -e

echo "🚀 启动 Meta-Agent 开发服务..."

# 检查环境配置
if [ ! -f ".env" ]; then
    echo "❌ 未找到 .env 文件，请先运行 ./scripts/setup-dev.sh"
    exit 1
fi

# 函数：启动前端
start_frontend() {
    echo "🎨 启动前端服务..."
    cd frontend
    if [ ! -d "node_modules" ]; then
        echo "📦 安装前端依赖..."
        npm install
    fi
    npm run dev &
    FRONTEND_PID=$!
    cd ..
    echo "✅ 前端服务已启动 (PID: $FRONTEND_PID)"
}

# 函数：启动后端
start_backend() {
    echo "🔧 启动后端服务..."
    cd backend
    if [ ! -d "venv" ]; then
        echo "❌ 未找到Python虚拟环境，请先运行 ./scripts/setup-dev.sh"
        exit 1
    fi
    source venv/bin/activate
    uvicorn app.main:app --reload --host 0.0.0.0 --port 8000 &
    BACKEND_PID=$!
    cd ..
    echo "✅ 后端服务已启动 (PID: $BACKEND_PID)"
}

# 函数：清理进程
cleanup() {
    echo "🛑 正在停止服务..."
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
    fi
    echo "✅ 服务已停止"
    exit 0
}

# 捕获中断信号
trap cleanup SIGINT SIGTERM

# 检查参数
case "${1:-all}" in
    "frontend")
        start_frontend
        ;;
    "backend")
        start_backend
        ;;
    "all")
        start_frontend
        start_backend
        ;;
    *)
        echo "用法: $0 [frontend|backend|all]"
        exit 1
        ;;
esac

echo ""
echo "🎉 服务启动完成！"
echo ""
echo "🔗 访问地址："
echo "- 前端: http://localhost:3000"
echo "- 后端API: http://localhost:8000"
echo "- API文档: http://localhost:8000/docs"
echo ""
echo "按 Ctrl+C 停止所有服务"

# 等待进程
wait
