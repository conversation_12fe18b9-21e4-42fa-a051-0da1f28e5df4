#!/usr/bin/env python3
"""
Simple Template Population Script
Creates high-quality templates directly in the database using raw SQL.
"""

import sqlite3
import json
import uuid
from datetime import datetime, timezone

# Database path
DB_PATH = "/home/<USER>/workdir/vscode/meta-agent/backend/data/meta_agent.db"

def create_templates():
    """Create high-quality templates in the database."""
    
    templates = [
        {
            "template_id": f"template_{uuid.uuid4().hex[:12]}",
            "name": "Content Creation Team",
            "description": "A comprehensive content creation team specializing in blog posts, articles, and digital content. Features specialized roles for research, writing, editing, and SEO optimization to produce high-quality, engaging content that drives audience engagement and meets business objectives.",
            "category": "creative",
            "difficulty": "intermediate",
            "visibility": "featured",
            "status": "active",
            "prompt_template": "Create engaging, high-quality content for {topic} targeting {audience}. Focus on {content_type} with {tone} tone. Include SEO optimization and ensure content aligns with brand guidelines and marketing objectives.",
            "team_structure_template": {
                "team_name": "Content Creation Team",
                "description": "Professional content creation team for digital marketing and publishing",
                "objective": "Produce high-quality, engaging content that drives audience engagement and meets business goals",
                "domain": "creative_writing",
                "complexity": "intermediate",
                "team_members": [
                    {
                        "name": "Content Strategist",
                        "role": "strategist",
                        "description": "Develops content strategy, identifies target audiences, and ensures content aligns with business objectives",
                        "system_prompt": "You are an expert Content Strategist with deep knowledge of digital marketing, audience analysis, and content planning. Your role is to develop comprehensive content strategies that align with business goals, identify target audiences, analyze market trends, and create content calendars. You excel at understanding brand voice, competitive analysis, and measuring content performance metrics to optimize strategy.",
                        "capabilities": ["strategy_development", "audience_analysis", "competitive_research", "content_planning"],
                        "tools": ["analytics", "research", "planning"],
                        "model_config": {
                            "model": "gpt-4",
                            "temperature": 0.7,
                            "max_tokens": 2000
                        },
                        "workflow_position": 1
                    },
                    {
                        "name": "Content Writer",
                        "role": "writer",
                        "description": "Creates compelling, well-researched content across various formats and platforms",
                        "system_prompt": "You are a skilled Content Writer specializing in creating engaging, informative, and persuasive content across multiple formats including blog posts, articles, social media content, and marketing copy. You excel at adapting writing style to different audiences, conducting thorough research, and incorporating SEO best practices while maintaining readability and engagement. Your writing is clear, compelling, and aligned with brand voice.",
                        "capabilities": ["content_writing", "research", "seo_optimization", "storytelling"],
                        "tools": ["writing", "research", "seo_tools"],
                        "model_config": {
                            "model": "gpt-4",
                            "temperature": 0.8,
                            "max_tokens": 3000
                        },
                        "workflow_position": 2
                    },
                    {
                        "name": "Content Editor",
                        "role": "editor",
                        "description": "Reviews, refines, and optimizes content for quality, consistency, and brand alignment",
                        "system_prompt": "You are a meticulous Content Editor with expertise in proofreading, copy editing, and content optimization. Your role involves reviewing content for grammar, style, consistency, and brand alignment. You ensure content meets quality standards, is optimized for readability, and effectively communicates the intended message. You provide constructive feedback and suggestions for improvement while maintaining the author's voice and intent.",
                        "capabilities": ["editing", "proofreading", "quality_assurance", "brand_consistency"],
                        "tools": ["editing_tools", "style_guides", "quality_checkers"],
                        "model_config": {
                            "model": "gpt-4",
                            "temperature": 0.5,
                            "max_tokens": 2000
                        },
                        "workflow_position": 3
                    }
                ],
                "workflow": {
                    "steps": [
                        {
                            "name": "Content Strategy Development",
                            "description": "Analyze requirements, develop content strategy, and create content brief",
                            "assignee": "Content Strategist",
                            "inputs": ["project_requirements", "target_audience", "business_goals"],
                            "outputs": ["content_strategy", "content_brief", "success_metrics"],
                            "dependencies": [],
                            "timeout": 300
                        },
                        {
                            "name": "Content Creation",
                            "description": "Research and write content based on strategy and brief",
                            "assignee": "Content Writer",
                            "inputs": ["content_brief", "research_materials", "brand_guidelines"],
                            "outputs": ["draft_content", "supporting_materials"],
                            "dependencies": ["Content Strategy Development"],
                            "timeout": 600
                        },
                        {
                            "name": "Content Review and Optimization",
                            "description": "Edit, proofread, and optimize content for publication",
                            "assignee": "Content Editor",
                            "inputs": ["draft_content", "style_guide", "quality_standards"],
                            "outputs": ["final_content", "optimization_report"],
                            "dependencies": ["Content Creation"],
                            "timeout": 300
                        }
                    ],
                    "coordination": {
                        "orchestrator": "Content Strategist",
                        "communication_style": "collaborative",
                        "decision_making": "consensus"
                    },
                    "execution_mode": "sequential"
                }
            },
            "tags": ["content", "writing", "marketing", "seo", "digital"],
            "keywords": ["content creation", "blog writing", "digital marketing", "seo"],
            "use_case": "Creating high-quality blog posts, articles, and digital content for marketing and engagement",
            "example_input": "Create a comprehensive blog post about sustainable business practices for small business owners",
            "expected_output": "A well-researched, engaging blog post with SEO optimization, clear structure, and actionable insights"
        },
        {
            "template_id": f"template_{uuid.uuid4().hex[:12]}",
            "name": "Business Intelligence Team",
            "description": "A specialized business intelligence team focused on data analysis, reporting, and insights generation. Combines data engineering, statistical analysis, and business intelligence expertise to transform raw data into actionable business insights and strategic recommendations.",
            "category": "analysis",
            "difficulty": "advanced",
            "visibility": "featured",
            "status": "active",
            "prompt_template": "Analyze {data_source} to generate insights about {business_question}. Focus on {analysis_type} and provide actionable recommendations for {stakeholder_group}. Include data visualization and statistical validation.",
            "team_structure_template": {
                "team_name": "Business Intelligence Team",
                "description": "Expert team for comprehensive data analysis and business intelligence",
                "objective": "Transform raw data into actionable business insights and strategic recommendations",
                "domain": "data_analysis",
                "complexity": "advanced",
                "team_members": [
                    {
                        "name": "Data Analyst",
                        "role": "analyst",
                        "description": "Performs statistical analysis, data exploration, and generates insights from complex datasets",
                        "system_prompt": "You are an expert Data Analyst with strong skills in statistical analysis, data exploration, and pattern recognition. You excel at working with large datasets, performing exploratory data analysis, identifying trends and anomalies, and generating meaningful insights. You are proficient in statistical methods, data visualization, and translating complex data findings into clear, actionable business insights. Your analysis is thorough, accurate, and focused on business value.",
                        "capabilities": ["statistical_analysis", "data_exploration", "pattern_recognition", "trend_analysis"],
                        "tools": ["python", "sql", "excel", "statistical_software"],
                        "model_config": {
                            "model": "gpt-4",
                            "temperature": 0.3,
                            "max_tokens": 2500
                        },
                        "workflow_position": 1
                    },
                    {
                        "name": "Business Intelligence Specialist",
                        "role": "bi_specialist",
                        "description": "Creates dashboards, reports, and visualizations to communicate data insights effectively",
                        "system_prompt": "You are a Business Intelligence Specialist expert in creating compelling data visualizations, interactive dashboards, and comprehensive reports. You excel at translating complex analytical findings into clear, visually appealing presentations that drive business decisions. You understand stakeholder needs, design principles for data visualization, and best practices for business reporting. Your work makes data accessible and actionable for all organizational levels.",
                        "capabilities": ["data_visualization", "dashboard_creation", "report_design", "stakeholder_communication"],
                        "tools": ["tableau", "power_bi", "excel", "presentation_tools"],
                        "model_config": {
                            "model": "gpt-4",
                            "temperature": 0.4,
                            "max_tokens": 2000
                        },
                        "workflow_position": 2
                    }
                ],
                "workflow": {
                    "steps": [
                        {
                            "name": "Data Analysis and Exploration",
                            "description": "Perform comprehensive statistical analysis and data exploration",
                            "assignee": "Data Analyst",
                            "inputs": ["raw_data", "business_questions", "analysis_requirements"],
                            "outputs": ["statistical_analysis", "data_insights", "findings_summary"],
                            "dependencies": [],
                            "timeout": 600
                        },
                        {
                            "name": "Visualization and Reporting",
                            "description": "Create visualizations, dashboards, and comprehensive reports",
                            "assignee": "Business Intelligence Specialist",
                            "inputs": ["statistical_analysis", "data_insights", "stakeholder_requirements"],
                            "outputs": ["dashboards", "visualizations", "detailed_reports"],
                            "dependencies": ["Data Analysis and Exploration"],
                            "timeout": 450
                        }
                    ],
                    "coordination": {
                        "orchestrator": "Data Analyst",
                        "communication_style": "data_driven",
                        "decision_making": "evidence_based"
                    },
                    "execution_mode": "sequential"
                }
            },
            "tags": ["data", "analysis", "business intelligence", "insights", "reporting"],
            "keywords": ["data analysis", "business intelligence", "reporting", "insights"],
            "use_case": "Analyzing business data to generate insights and recommendations for strategic decision-making",
            "example_input": "Analyze sales data to identify trends and opportunities for revenue growth",
            "expected_output": "Comprehensive analysis report with insights, visualizations, and actionable recommendations"
        },
        {
            "template_id": f"template_{uuid.uuid4().hex[:12]}",
            "name": "Product Strategy Team",
            "description": "A comprehensive product development team specializing in feature planning, user research, and technical specifications. Combines product management expertise, user experience research, and technical architecture to deliver successful product initiatives that meet user needs and business objectives.",
            "category": "business",
            "difficulty": "advanced",
            "visibility": "featured",
            "status": "active",
            "prompt_template": "Develop a comprehensive product strategy for {product_feature} targeting {user_segment}. Include user research insights, technical requirements, and implementation roadmap. Focus on {success_metrics} and ensure alignment with business goals.",
            "team_structure_template": {
                "team_name": "Product Strategy Team",
                "description": "Expert product development team for strategic planning and execution",
                "objective": "Deliver successful product initiatives through comprehensive planning, user research, and technical excellence",
                "domain": "product_development",
                "complexity": "advanced",
                "team_members": [
                    {
                        "name": "Product Manager",
                        "role": "product_manager",
                        "description": "Leads product strategy, defines requirements, and coordinates cross-functional execution",
                        "system_prompt": "You are an experienced Product Manager with expertise in product strategy, roadmap planning, and cross-functional team leadership. You excel at understanding market needs, defining product requirements, prioritizing features, and ensuring successful product delivery. You have strong analytical skills, understand user experience principles, and can balance business objectives with technical constraints. Your approach is data-driven, user-focused, and results-oriented.",
                        "capabilities": ["product_strategy", "roadmap_planning", "requirement_definition", "stakeholder_management"],
                        "tools": ["product_management", "analytics", "project_management", "user_research"],
                        "model_config": {
                            "model": "gpt-4",
                            "temperature": 0.6,
                            "max_tokens": 2500
                        },
                        "workflow_position": 1
                    },
                    {
                        "name": "UX Researcher",
                        "role": "ux_researcher",
                        "description": "Conducts user research, analyzes user behavior, and provides insights for product decisions",
                        "system_prompt": "You are a skilled UX Researcher specializing in user behavior analysis, usability testing, and research methodology. You excel at designing and conducting user research studies, analyzing qualitative and quantitative data, and translating research findings into actionable product insights. You understand various research methods, can identify user pain points and opportunities, and effectively communicate research findings to product teams and stakeholders.",
                        "capabilities": ["user_research", "usability_testing", "data_analysis", "insight_generation"],
                        "tools": ["research_tools", "analytics", "survey_platforms", "testing_tools"],
                        "model_config": {
                            "model": "gpt-4",
                            "temperature": 0.5,
                            "max_tokens": 2000
                        },
                        "workflow_position": 2
                    },
                    {
                        "name": "Technical Architect",
                        "role": "technical_architect",
                        "description": "Defines technical architecture, evaluates implementation approaches, and ensures technical feasibility",
                        "system_prompt": "You are a Technical Architect with deep expertise in software architecture, system design, and technology evaluation. You excel at designing scalable, maintainable technical solutions, evaluating different implementation approaches, and ensuring technical feasibility of product requirements. You understand modern development practices, can assess technical risks, and provide guidance on technology choices and architectural decisions that support product goals.",
                        "capabilities": ["system_architecture", "technical_design", "technology_evaluation", "risk_assessment"],
                        "tools": ["architecture_tools", "development_frameworks", "design_patterns", "technical_documentation"],
                        "model_config": {
                            "model": "gpt-4",
                            "temperature": 0.4,
                            "max_tokens": 2500
                        },
                        "workflow_position": 3
                    }
                ],
                "workflow": {
                    "steps": [
                        {
                            "name": "Product Strategy and Planning",
                            "description": "Define product strategy, requirements, and success metrics",
                            "assignee": "Product Manager",
                            "inputs": ["business_objectives", "market_analysis", "user_feedback"],
                            "outputs": ["product_strategy", "feature_requirements", "success_metrics"],
                            "dependencies": [],
                            "timeout": 450
                        },
                        {
                            "name": "User Research and Validation",
                            "description": "Conduct user research to validate assumptions and gather insights",
                            "assignee": "UX Researcher",
                            "inputs": ["product_strategy", "user_segments", "research_questions"],
                            "outputs": ["research_findings", "user_insights", "validation_results"],
                            "dependencies": ["Product Strategy and Planning"],
                            "timeout": 600
                        },
                        {
                            "name": "Technical Architecture and Planning",
                            "description": "Design technical architecture and implementation approach",
                            "assignee": "Technical Architect",
                            "inputs": ["feature_requirements", "technical_constraints", "scalability_needs"],
                            "outputs": ["technical_architecture", "implementation_plan", "risk_assessment"],
                            "dependencies": ["User Research and Validation"],
                            "timeout": 450
                        }
                    ],
                    "coordination": {
                        "orchestrator": "Product Manager",
                        "communication_style": "collaborative",
                        "decision_making": "consensus"
                    },
                    "execution_mode": "sequential"
                }
            },
            "tags": ["product", "development", "strategy", "user research", "technical"],
            "keywords": ["product management", "user research", "technical architecture", "product strategy"],
            "use_case": "Developing comprehensive product strategies with user research and technical planning",
            "example_input": "Plan a new mobile app feature for user onboarding improvement",
            "expected_output": "Complete product strategy with user research insights, technical architecture, and implementation roadmap"
        },
        {
            "template_id": f"template_{uuid.uuid4().hex[:12]}",
            "name": "Curriculum Design Team",
            "description": "A specialized education team focused on curriculum development, instructional design, and learning assessment. Combines pedagogical expertise, content development skills, and assessment design to create effective, engaging educational experiences that achieve learning objectives and support student success.",
            "category": "education",
            "difficulty": "intermediate",
            "visibility": "featured",
            "status": "active",
            "prompt_template": "Design a comprehensive curriculum for {subject_area} targeting {learner_level}. Include learning objectives, instructional strategies, assessment methods, and engagement techniques. Focus on {learning_outcomes} and ensure accessibility and inclusivity.",
            "team_structure_template": {
                "team_name": "Curriculum Design Team",
                "description": "Expert education team for comprehensive curriculum development and instructional design",
                "objective": "Create effective, engaging educational experiences that achieve learning objectives and support student success",
                "domain": "education",
                "complexity": "intermediate",
                "team_members": [
                    {
                        "name": "Instructional Designer",
                        "role": "instructional_designer",
                        "description": "Designs learning experiences, develops instructional strategies, and ensures pedagogical effectiveness",
                        "system_prompt": "You are an expert Instructional Designer with deep knowledge of learning theories, pedagogical approaches, and educational technology. You excel at designing engaging, effective learning experiences that accommodate different learning styles and achieve specific learning objectives. You understand curriculum development, assessment design, and how to create inclusive, accessible educational content. Your approach is learner-centered, evidence-based, and focused on measurable learning outcomes.",
                        "capabilities": ["curriculum_design", "learning_theory", "instructional_strategies", "assessment_design"],
                        "tools": ["learning_management_systems", "educational_technology", "assessment_tools", "design_software"],
                        "model_config": {
                            "model": "gpt-4",
                            "temperature": 0.6,
                            "max_tokens": 2500
                        },
                        "workflow_position": 1
                    },
                    {
                        "name": "Subject Matter Expert",
                        "role": "subject_expert",
                        "description": "Provides domain expertise, validates content accuracy, and ensures curriculum relevance",
                        "system_prompt": "You are a Subject Matter Expert with deep knowledge in your field and experience in educational content development. You excel at ensuring content accuracy, relevance, and alignment with industry standards. You understand how to translate complex subject matter into accessible learning materials and can identify key concepts, skills, and competencies that learners need to master. Your expertise ensures curriculum quality and real-world applicability.",
                        "capabilities": ["domain_expertise", "content_validation", "industry_alignment", "competency_mapping"],
                        "tools": ["research_databases", "industry_resources", "professional_networks", "validation_tools"],
                        "model_config": {
                            "model": "gpt-4",
                            "temperature": 0.4,
                            "max_tokens": 2000
                        },
                        "workflow_position": 2
                    }
                ],
                "workflow": {
                    "steps": [
                        {
                            "name": "Curriculum Planning and Design",
                            "description": "Design comprehensive curriculum structure and learning framework",
                            "assignee": "Instructional Designer",
                            "inputs": ["learning_objectives", "target_audience", "subject_requirements"],
                            "outputs": ["curriculum_framework", "learning_modules", "assessment_strategy"],
                            "dependencies": [],
                            "timeout": 450
                        },
                        {
                            "name": "Content Development and Validation",
                            "description": "Develop educational content and validate accuracy and relevance",
                            "assignee": "Subject Matter Expert",
                            "inputs": ["curriculum_framework", "industry_standards", "competency_requirements"],
                            "outputs": ["educational_content", "learning_materials", "competency_mapping"],
                            "dependencies": ["Curriculum Planning and Design"],
                            "timeout": 500
                        },
                        {
                            "name": "Curriculum Integration and Finalization",
                            "description": "Integrate content into final curriculum with assessments and activities",
                            "assignee": "Instructional Designer",
                            "inputs": ["educational_content", "learning_materials", "assessment_requirements"],
                            "outputs": ["complete_curriculum", "instructor_guides", "student_resources"],
                            "dependencies": ["Content Development and Validation"],
                            "timeout": 350
                        }
                    ],
                    "coordination": {
                        "orchestrator": "Instructional Designer",
                        "communication_style": "educational",
                        "decision_making": "pedagogical_best_practices"
                    },
                    "execution_mode": "sequential"
                }
            },
            "tags": ["education", "curriculum", "instructional design", "learning", "assessment"],
            "keywords": ["curriculum design", "instructional design", "education", "learning"],
            "use_case": "Designing comprehensive educational curricula and learning experiences",
            "example_input": "Create a curriculum for teaching data science fundamentals to business professionals",
            "expected_output": "Complete curriculum with learning objectives, modules, activities, and assessment strategies"
        },
        {
            "template_id": f"template_{uuid.uuid4().hex[:12]}",
            "name": "Strategic Consulting Team",
            "description": "A comprehensive business strategy team specializing in market analysis, strategic planning, and business consulting. Combines strategic thinking, market research expertise, and business analysis to deliver actionable strategic recommendations that drive business growth and competitive advantage.",
            "category": "business",
            "difficulty": "expert",
            "visibility": "featured",
            "status": "active",
            "prompt_template": "Develop a comprehensive business strategy for {business_challenge} in {industry_context}. Include market analysis, competitive assessment, strategic recommendations, and implementation roadmap. Focus on {strategic_objectives} and ensure alignment with organizational capabilities.",
            "team_structure_template": {
                "team_name": "Strategic Consulting Team",
                "description": "Expert business strategy team for comprehensive strategic planning and consulting",
                "objective": "Deliver actionable strategic recommendations that drive business growth and competitive advantage",
                "domain": "business_strategy",
                "complexity": "expert",
                "team_members": [
                    {
                        "name": "Strategy Consultant",
                        "role": "strategy_consultant",
                        "description": "Leads strategic analysis, develops recommendations, and guides strategic planning processes",
                        "system_prompt": "You are a senior Strategy Consultant with extensive experience in business strategy, strategic planning, and organizational transformation. You excel at analyzing complex business challenges, identifying strategic opportunities, and developing comprehensive strategic recommendations. You understand market dynamics, competitive analysis, and strategic frameworks. Your approach is analytical, data-driven, and focused on delivering actionable insights that create sustainable competitive advantage.",
                        "capabilities": ["strategic_analysis", "strategic_planning", "business_transformation", "competitive_strategy"],
                        "tools": ["strategic_frameworks", "analysis_tools", "planning_software", "presentation_tools"],
                        "model_config": {
                            "model": "gpt-4",
                            "temperature": 0.6,
                            "max_tokens": 3000
                        },
                        "workflow_position": 1
                    },
                    {
                        "name": "Market Research Analyst",
                        "role": "market_analyst",
                        "description": "Conducts comprehensive market research, analyzes industry trends, and provides market insights",
                        "system_prompt": "You are an expert Market Research Analyst specializing in industry analysis, market trends, and competitive intelligence. You excel at gathering and analyzing market data, identifying emerging trends, and providing insights that inform strategic decisions. You understand research methodologies, data analysis techniques, and how to translate market intelligence into actionable business insights. Your analysis is thorough, objective, and strategically relevant.",
                        "capabilities": ["market_research", "industry_analysis", "competitive_intelligence", "trend_analysis"],
                        "tools": ["research_databases", "analytics_platforms", "survey_tools", "data_visualization"],
                        "model_config": {
                            "model": "gpt-4",
                            "temperature": 0.4,
                            "max_tokens": 2500
                        },
                        "workflow_position": 2
                    },
                    {
                        "name": "Business Analyst",
                        "role": "business_analyst",
                        "description": "Analyzes business operations, financial performance, and organizational capabilities",
                        "system_prompt": "You are a skilled Business Analyst with expertise in business process analysis, financial modeling, and organizational assessment. You excel at analyzing business operations, identifying improvement opportunities, and evaluating strategic options from a financial and operational perspective. You understand business metrics, financial analysis, and how to assess organizational readiness for strategic initiatives. Your analysis provides the foundation for informed strategic decision-making.",
                        "capabilities": ["business_analysis", "financial_modeling", "process_analysis", "organizational_assessment"],
                        "tools": ["financial_modeling", "process_mapping", "analytics_software", "business_intelligence"],
                        "model_config": {
                            "model": "gpt-4",
                            "temperature": 0.3,
                            "max_tokens": 2500
                        },
                        "workflow_position": 3
                    }
                ],
                "workflow": {
                    "steps": [
                        {
                            "name": "Strategic Assessment and Planning",
                            "description": "Conduct strategic assessment and develop strategic framework",
                            "assignee": "Strategy Consultant",
                            "inputs": ["business_challenge", "organizational_context", "strategic_objectives"],
                            "outputs": ["strategic_framework", "assessment_plan", "key_questions"],
                            "dependencies": [],
                            "timeout": 450
                        },
                        {
                            "name": "Market Research and Analysis",
                            "description": "Conduct comprehensive market research and competitive analysis",
                            "assignee": "Market Research Analyst",
                            "inputs": ["strategic_framework", "industry_context", "competitive_landscape"],
                            "outputs": ["market_analysis", "competitive_assessment", "industry_insights"],
                            "dependencies": ["Strategic Assessment and Planning"],
                            "timeout": 600
                        },
                        {
                            "name": "Business Analysis and Recommendations",
                            "description": "Analyze business capabilities and develop strategic recommendations",
                            "assignee": "Business Analyst",
                            "inputs": ["market_analysis", "organizational_data", "financial_information"],
                            "outputs": ["business_analysis", "strategic_recommendations", "implementation_roadmap"],
                            "dependencies": ["Market Research and Analysis"],
                            "timeout": 450
                        }
                    ],
                    "coordination": {
                        "orchestrator": "Strategy Consultant",
                        "communication_style": "analytical",
                        "decision_making": "evidence_based"
                    },
                    "execution_mode": "sequential"
                }
            },
            "tags": ["business", "strategy", "consulting", "market analysis", "planning"],
            "keywords": ["business strategy", "strategic planning", "market analysis", "consulting"],
            "use_case": "Developing comprehensive business strategies and strategic recommendations",
            "example_input": "Develop a market expansion strategy for a SaaS company entering the European market",
            "expected_output": "Complete strategic plan with market analysis, competitive assessment, and implementation roadmap"
        },
        {
            "template_id": f"template_{uuid.uuid4().hex[:12]}",
            "name": "Technical Documentation Team",
            "description": "A specialized technical documentation team focused on creating comprehensive, user-friendly technical documentation. Combines technical writing expertise, user experience design, and information architecture to produce clear, accurate, and accessible documentation that serves both technical and non-technical audiences effectively.",
            "category": "technical",
            "difficulty": "intermediate",
            "visibility": "featured",
            "status": "active",
            "prompt_template": "Create comprehensive technical documentation for {technical_subject} targeting {audience_type}. Include {documentation_type} with clear explanations, examples, and best practices. Focus on usability, accuracy, and accessibility for {user_goals}.",
            "team_structure_template": {
                "team_name": "Technical Documentation Team",
                "description": "Expert team for comprehensive technical documentation and information design",
                "objective": "Produce clear, accurate, and accessible technical documentation that serves both technical and non-technical audiences effectively",
                "domain": "technical_documentation",
                "complexity": "intermediate",
                "team_members": [
                    {
                        "name": "Technical Writer",
                        "role": "technical_writer",
                        "description": "Creates clear, comprehensive technical documentation and user guides",
                        "system_prompt": "You are an expert Technical Writer specializing in creating clear, comprehensive, and user-friendly technical documentation. You excel at translating complex technical concepts into accessible language, organizing information logically, and creating documentation that serves diverse audiences. You understand documentation best practices, information architecture, and how to balance technical accuracy with usability. Your writing is precise, well-structured, and focused on user needs.",
                        "capabilities": ["technical_writing", "information_design", "user_experience", "content_organization"],
                        "tools": ["documentation_platforms", "writing_tools", "version_control", "collaboration_tools"],
                        "model_config": {
                            "model": "gpt-4",
                            "temperature": 0.5,
                            "max_tokens": 3000
                        },
                        "workflow_position": 1
                    },
                    {
                        "name": "Information Architect",
                        "role": "information_architect",
                        "description": "Designs information structure, navigation, and user experience for documentation",
                        "system_prompt": "You are an Information Architect specializing in documentation structure, user experience design, and information organization. You excel at creating logical information hierarchies, designing intuitive navigation systems, and ensuring documentation is accessible and user-friendly. You understand user research, information design principles, and how to organize complex technical information in ways that serve diverse user needs and use cases effectively.",
                        "capabilities": ["information_architecture", "user_experience", "navigation_design", "content_organization"],
                        "tools": ["wireframing_tools", "user_research", "information_design", "usability_testing"],
                        "model_config": {
                            "model": "gpt-4",
                            "temperature": 0.4,
                            "max_tokens": 2000
                        },
                        "workflow_position": 2
                    }
                ],
                "workflow": {
                    "steps": [
                        {
                            "name": "Documentation Planning and Architecture",
                            "description": "Plan documentation structure and information architecture",
                            "assignee": "Information Architect",
                            "inputs": ["technical_requirements", "user_needs", "content_scope"],
                            "outputs": ["documentation_architecture", "content_structure", "navigation_design"],
                            "dependencies": [],
                            "timeout": 300
                        },
                        {
                            "name": "Content Creation and Writing",
                            "description": "Create comprehensive technical content and documentation",
                            "assignee": "Technical Writer",
                            "inputs": ["documentation_architecture", "technical_specifications", "user_scenarios"],
                            "outputs": ["technical_content", "user_guides", "reference_materials"],
                            "dependencies": ["Documentation Planning and Architecture"],
                            "timeout": 600
                        },
                        {
                            "name": "Review and Quality Assurance",
                            "description": "Review, edit, and optimize documentation for quality and consistency",
                            "assignee": "Technical Writer",
                            "inputs": ["technical_content", "quality_standards", "style_guidelines"],
                            "outputs": ["final_documentation", "quality_report", "publication_ready_content"],
                            "dependencies": ["Content Creation and Writing"],
                            "timeout": 300
                        }
                    ],
                    "coordination": {
                        "orchestrator": "Technical Writer",
                        "communication_style": "technical_collaborative",
                        "decision_making": "user_centered"
                    },
                    "execution_mode": "sequential"
                }
            },
            "tags": ["technical", "documentation", "writing", "user guides", "api"],
            "keywords": ["technical writing", "documentation", "user guides", "api documentation"],
            "use_case": "Creating comprehensive technical documentation and user guides",
            "example_input": "Create API documentation for a REST API with authentication and data management endpoints",
            "expected_output": "Complete API documentation with clear explanations, examples, and integration guides"
        }
    ]
    
    # Connect to database and insert templates
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    try:
        success_count = 0
        for template in templates:
            # Check if template already exists
            cursor.execute("SELECT template_id FROM templates WHERE name = ?", (template["name"],))
            if cursor.fetchone():
                print(f"⚠️ Template already exists, skipping: {template['name']}")
                continue
            
            # Insert template
            cursor.execute("""
                INSERT INTO templates (
                    uuid, created_at, updated_at, template_id, name, description, category, difficulty,
                    visibility, status, prompt_template, team_structure_template, default_config,
                    usage_count, rating, rating_count, tags, keywords, version, author_name,
                    use_case, example_input, expected_output, is_active, is_featured, template_metadata
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                str(uuid.uuid4()),
                datetime.now(timezone.utc),
                datetime.now(timezone.utc),
                template["template_id"],
                template["name"],
                template["description"],
                template["category"],
                template["difficulty"],
                template["visibility"],
                template["status"],
                template["prompt_template"],
                json.dumps(template["team_structure_template"]),
                json.dumps({}),
                0,  # usage_count
                None,  # rating
                0,  # rating_count
                json.dumps(template["tags"]),
                json.dumps(template["keywords"]),
                "1.0.0",  # version
                "System",  # author_name
                template["use_case"],
                template["example_input"],
                template["expected_output"],
                True,  # is_active
                True,  # is_featured
                json.dumps({"system_template": True, "quality_verified": True})
            ))
            
            success_count += 1
            print(f"✅ Created template: {template['name']}")
        
        conn.commit()
        print(f"\n🎉 Successfully created {success_count} templates!")
        
    except Exception as e:
        conn.rollback()
        print(f"❌ Error creating templates: {e}")
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    print("🚀 Starting template population...")
    create_templates()
    print("✅ Template population completed!")
