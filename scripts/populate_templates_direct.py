#!/usr/bin/env python3
"""
Direct Template Population Script
Populates the template database directly using SQLAlchemy models.
"""

import os
import sys
import asyncio
import uuid
from pathlib import Path

# Set environment variables before importing
os.environ['DATABASE_URL'] = 'sqlite:///./meta_agent.db'
os.environ['SECRET_KEY'] = 'dev-secret-key-for-template-population'
os.environ['JWT_SECRET_KEY'] = 'dev-jwt-secret-key'

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent / "backend"
sys.path.insert(0, str(backend_dir))

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from app.models.planning import Template, TemplateCategory, TemplateDifficulty, TemplateVisibility, TemplateStatus

# Database setup
DATABASE_URL = "sqlite+aiosqlite:///./meta_agent.db"
engine = create_async_engine(DATABASE_URL)
AsyncSessionLocal = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

class DirectTemplatePopulator:
    """Handles direct database population of templates."""
    
    def __init__(self):
        self.templates = self._prepare_templates()
    
    def _prepare_templates(self):
        """Prepare template definitions."""
        return [
            {
                "name": "Content Creation Team",
                "description": "A comprehensive content creation team specializing in blog posts, articles, and digital content. Features specialized roles for research, writing, editing, and SEO optimization to produce high-quality, engaging content that drives audience engagement and meets business objectives.",
                "category": TemplateCategory.CREATIVE,
                "difficulty": TemplateDifficulty.INTERMEDIATE,
                "visibility": TemplateVisibility.FEATURED,
                "prompt_template": "Create engaging, high-quality content for {topic} targeting {audience}. Focus on {content_type} with {tone} tone. Include SEO optimization and ensure content aligns with brand guidelines and marketing objectives.",
                "team_structure_template": {
                    "team_name": "Content Creation Team",
                    "description": "Professional content creation team for digital marketing and publishing",
                    "objective": "Produce high-quality, engaging content that drives audience engagement and meets business goals",
                    "domain": "creative_writing",
                    "complexity": "intermediate",
                    "team_members": [
                        {
                            "name": "Content Strategist",
                            "role": "strategist",
                            "description": "Develops content strategy, identifies target audiences, and ensures content aligns with business objectives",
                            "system_prompt": "You are an expert Content Strategist with deep knowledge of digital marketing, audience analysis, and content planning. Your role is to develop comprehensive content strategies that align with business goals, identify target audiences, analyze market trends, and create content calendars. You excel at understanding brand voice, competitive analysis, and measuring content performance metrics to optimize strategy.",
                            "capabilities": ["strategy_development", "audience_analysis", "competitive_research", "content_planning"],
                            "tools": ["analytics", "research", "planning"],
                            "model_config": {
                                "model": "gpt-4",
                                "temperature": 0.7,
                                "max_tokens": 2000
                            },
                            "workflow_position": 1
                        },
                        {
                            "name": "Content Writer",
                            "role": "writer",
                            "description": "Creates compelling, well-researched content across various formats and platforms",
                            "system_prompt": "You are a skilled Content Writer specializing in creating engaging, informative, and persuasive content across multiple formats including blog posts, articles, social media content, and marketing copy. You excel at adapting writing style to different audiences, conducting thorough research, and incorporating SEO best practices while maintaining readability and engagement. Your writing is clear, compelling, and aligned with brand voice.",
                            "capabilities": ["content_writing", "research", "seo_optimization", "storytelling"],
                            "tools": ["writing", "research", "seo_tools"],
                            "model_config": {
                                "model": "gpt-4",
                                "temperature": 0.8,
                                "max_tokens": 3000
                            },
                            "workflow_position": 2
                        },
                        {
                            "name": "Content Editor",
                            "role": "editor",
                            "description": "Reviews, refines, and optimizes content for quality, consistency, and brand alignment",
                            "system_prompt": "You are a meticulous Content Editor with expertise in proofreading, copy editing, and content optimization. Your role involves reviewing content for grammar, style, consistency, and brand alignment. You ensure content meets quality standards, is optimized for readability, and effectively communicates the intended message. You provide constructive feedback and suggestions for improvement while maintaining the author's voice and intent.",
                            "capabilities": ["editing", "proofreading", "quality_assurance", "brand_consistency"],
                            "tools": ["editing_tools", "style_guides", "quality_checkers"],
                            "model_config": {
                                "model": "gpt-4",
                                "temperature": 0.5,
                                "max_tokens": 2000
                            },
                            "workflow_position": 3
                        }
                    ],
                    "workflow": {
                        "steps": [
                            {
                                "name": "Content Strategy Development",
                                "description": "Analyze requirements, develop content strategy, and create content brief",
                                "assignee": "Content Strategist",
                                "inputs": ["project_requirements", "target_audience", "business_goals"],
                                "outputs": ["content_strategy", "content_brief", "success_metrics"],
                                "dependencies": [],
                                "timeout": 300
                            },
                            {
                                "name": "Content Creation",
                                "description": "Research and write content based on strategy and brief",
                                "assignee": "Content Writer",
                                "inputs": ["content_brief", "research_materials", "brand_guidelines"],
                                "outputs": ["draft_content", "supporting_materials"],
                                "dependencies": ["Content Strategy Development"],
                                "timeout": 600
                            },
                            {
                                "name": "Content Review and Optimization",
                                "description": "Edit, proofread, and optimize content for publication",
                                "assignee": "Content Editor",
                                "inputs": ["draft_content", "style_guide", "quality_standards"],
                                "outputs": ["final_content", "optimization_report"],
                                "dependencies": ["Content Creation"],
                                "timeout": 300
                            }
                        ],
                        "coordination": {
                            "orchestrator": "Content Strategist",
                            "communication_style": "collaborative",
                            "decision_making": "consensus"
                        },
                        "execution_mode": "sequential"
                    }
                },
                "tags": ["content", "writing", "marketing", "seo", "digital"],
                "keywords": ["content creation", "blog writing", "digital marketing", "seo"],
                "use_case": "Creating high-quality blog posts, articles, and digital content for marketing and engagement",
                "example_input": "Create a comprehensive blog post about sustainable business practices for small business owners",
                "expected_output": "A well-researched, engaging blog post with SEO optimization, clear structure, and actionable insights"
            },
            {
                "name": "Business Intelligence Team",
                "description": "A specialized business intelligence team focused on data analysis, reporting, and insights generation. Combines data engineering, statistical analysis, and business intelligence expertise to transform raw data into actionable business insights and strategic recommendations.",
                "category": TemplateCategory.ANALYSIS,
                "difficulty": TemplateDifficulty.ADVANCED,
                "visibility": TemplateVisibility.FEATURED,
                "prompt_template": "Analyze {data_source} to generate insights about {business_question}. Focus on {analysis_type} and provide actionable recommendations for {stakeholder_group}. Include data visualization and statistical validation.",
                "team_structure_template": {
                    "team_name": "Business Intelligence Team",
                    "description": "Expert team for comprehensive data analysis and business intelligence",
                    "objective": "Transform raw data into actionable business insights and strategic recommendations",
                    "domain": "data_analysis",
                    "complexity": "advanced",
                    "team_members": [
                        {
                            "name": "Data Analyst",
                            "role": "analyst",
                            "description": "Performs statistical analysis, data exploration, and generates insights from complex datasets",
                            "system_prompt": "You are an expert Data Analyst with strong skills in statistical analysis, data exploration, and pattern recognition. You excel at working with large datasets, performing exploratory data analysis, identifying trends and anomalies, and generating meaningful insights. You are proficient in statistical methods, data visualization, and translating complex data findings into clear, actionable business insights. Your analysis is thorough, accurate, and focused on business value.",
                            "capabilities": ["statistical_analysis", "data_exploration", "pattern_recognition", "trend_analysis"],
                            "tools": ["python", "sql", "excel", "statistical_software"],
                            "model_config": {
                                "model": "gpt-4",
                                "temperature": 0.3,
                                "max_tokens": 2500
                            },
                            "workflow_position": 1
                        }
                    ],
                    "workflow": {
                        "steps": [
                            {
                                "name": "Data Analysis and Exploration",
                                "description": "Perform comprehensive statistical analysis and data exploration",
                                "assignee": "Data Analyst",
                                "inputs": ["raw_data", "business_questions", "analysis_requirements"],
                                "outputs": ["statistical_analysis", "data_insights", "findings_summary"],
                                "dependencies": [],
                                "timeout": 600
                            }
                        ],
                        "coordination": {
                            "orchestrator": "Data Analyst",
                            "communication_style": "data_driven",
                            "decision_making": "evidence_based"
                        },
                        "execution_mode": "sequential"
                    }
                },
                "tags": ["data", "analysis", "business intelligence", "insights", "reporting"],
                "keywords": ["data analysis", "business intelligence", "reporting", "insights"],
                "use_case": "Analyzing business data to generate insights and recommendations for strategic decision-making",
                "example_input": "Analyze sales data to identify trends and opportunities for revenue growth",
                "expected_output": "Comprehensive analysis report with insights, visualizations, and actionable recommendations"
            }
        ]
    
    async def create_template(self, session: AsyncSession, template_data: dict):
        """Create a single template in the database."""
        template_id = f"template_{uuid.uuid4().hex[:12]}"
        
        template = Template(
            template_id=template_id,
            name=template_data["name"],
            description=template_data["description"],
            category=template_data["category"],
            difficulty=template_data["difficulty"],
            visibility=template_data["visibility"],
            status=TemplateStatus.ACTIVE,
            prompt_template=template_data["prompt_template"],
            team_structure_template=template_data["team_structure_template"],
            default_config={},
            tags=template_data.get("tags", []),
            keywords=template_data.get("keywords", []),
            use_case=template_data.get("use_case"),
            example_input=template_data.get("example_input"),
            expected_output=template_data.get("expected_output"),
            user_id=None,  # System templates
            author_name="System",
            usage_count=0,
            rating_count=0,
            version="1.0.0",
            metadata={
                "system_template": True,
                "created_by_script": True,
                "quality_verified": True
            }
        )
        
        session.add(template)
        print(f"✓ Created template: {template.name}")
    
    async def populate_database(self):
        """Populate the database with all prepared templates."""
        async with AsyncSessionLocal() as session:
            try:
                print(f"Starting template population with {len(self.templates)} templates...")
                
                for template_data in self.templates:
                    await self.create_template(session, template_data)
                
                await session.commit()
                print(f"Successfully populated {len(self.templates)} templates!")
                
            except Exception as e:
                await session.rollback()
                print(f"Error populating templates: {e}")
                raise

async def main():
    """Main function to run template population."""
    populator = DirectTemplatePopulator()
    await populator.populate_database()

if __name__ == "__main__":
    asyncio.run(main())
