#!/bin/bash

# Meta-Agent 开发环境设置脚本

set -e

echo "🚀 开始设置 Meta-Agent 开发环境..."

# 检查必要工具
check_tool() {
    if ! command -v $1 &> /dev/null; then
        echo "❌ $1 未安装，请先安装 $1"
        exit 1
    else
        echo "✅ $1 已安装"
    fi
}

echo "📋 检查必要工具..."
check_tool "node"
check_tool "npm"
check_tool "python3"
check_tool "pip"

# 检查Node.js版本
NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js 版本需要 >= 18，当前版本: $(node --version)"
    exit 1
else
    echo "✅ Node.js 版本符合要求: $(node --version)"
fi

# 检查Python版本
PYTHON_VERSION=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
if [ "$(echo "$PYTHON_VERSION < 3.8" | bc)" -eq 1 ]; then
    echo "❌ Python 版本需要 >= 3.8，当前版本: $(python3 --version)"
    exit 1
else
    echo "✅ Python 版本符合要求: $(python3 --version)"
fi

# 创建环境配置文件
if [ ! -f ".env" ]; then
    echo "📝 创建环境配置文件..."
    cp .env.example .env
    echo "⚠️  请编辑 .env 文件，填入必要的API密钥"
else
    echo "✅ 环境配置文件已存在"
fi

# 设置前端
echo "🎨 设置前端环境..."
if [ ! -d "frontend/node_modules" ]; then
    echo "📦 安装前端依赖..."
    cd frontend
    npm install
    cd ..
else
    echo "✅ 前端依赖已安装"
fi

# 设置后端
echo "🔧 设置后端环境..."
if [ ! -d "backend/venv" ]; then
    echo "🐍 创建Python虚拟环境..."
    cd backend
    python3 -m venv venv
    source venv/bin/activate
    pip install --upgrade pip
    if [ -f "requirements.txt" ]; then
        pip install -r requirements.txt
    fi
    cd ..
else
    echo "✅ Python虚拟环境已存在"
fi

echo "🎉 开发环境设置完成！"
echo ""
echo "📚 下一步："
echo "1. 编辑 .env 文件，填入API密钥"
echo "2. 启动前端: cd frontend && npm run dev"
echo "3. 启动后端: cd backend && source venv/bin/activate && uvicorn app.main:app --reload"
echo "4. 或使用 Docker: docker-compose up --build"
echo ""
echo "🔗 访问地址："
echo "- 前端: http://localhost:3000"
echo "- 后端API: http://localhost:8000"
echo "- API文档: http://localhost:8000/docs"
