#!/usr/bin/env python3
"""
Template Population Script using API
Populates the template database with high-quality templates via HTTP API calls.
"""

import requests
import json
import sys
from typing import Dict, List, Any

# API Configuration
API_BASE_URL = "http://localhost:8000/api/v1"
TEMPLATES_ENDPOINT = f"{API_BASE_URL}/templates"

# Mock authentication - you'll need to replace this with actual auth
AUTH_HEADERS = {
    "Content-Type": "application/json",
    # Add authentication headers here when available
}

class TemplateAPIPopulator:
    """Handles population of templates via API calls."""
    
    def __init__(self):
        self.templates = self._prepare_templates()
    
    def _prepare_templates(self) -> List[Dict[str, Any]]:
        """Prepare all template definitions."""
        templates = []
        
        # Creative Writing Templates
        templates.append({
            "name": "Content Creation Team",
            "description": "A comprehensive content creation team specializing in blog posts, articles, and digital content. Features specialized roles for research, writing, editing, and SEO optimization to produce high-quality, engaging content that drives audience engagement and meets business objectives.",
            "category": "creative",
            "difficulty": "intermediate",
            "visibility": "featured",
            "prompt_template": "Create engaging, high-quality content for {topic} targeting {audience}. Focus on {content_type} with {tone} tone. Include SEO optimization and ensure content aligns with brand guidelines and marketing objectives.",
            "team_structure_template": {
                "team_name": "Content Creation Team",
                "description": "Professional content creation team for digital marketing and publishing",
                "objective": "Produce high-quality, engaging content that drives audience engagement and meets business goals",
                "domain": "creative_writing",
                "complexity": "intermediate",
                "team_members": [
                    {
                        "name": "Content Strategist",
                        "role": "strategist",
                        "description": "Develops content strategy, identifies target audiences, and ensures content aligns with business objectives",
                        "system_prompt": "You are an expert Content Strategist with deep knowledge of digital marketing, audience analysis, and content planning. Your role is to develop comprehensive content strategies that align with business goals, identify target audiences, analyze market trends, and create content calendars. You excel at understanding brand voice, competitive analysis, and measuring content performance metrics to optimize strategy.",
                        "capabilities": ["strategy_development", "audience_analysis", "competitive_research", "content_planning"],
                        "tools": ["analytics", "research", "planning"],
                        "model_config": {
                            "model": "gpt-4",
                            "temperature": 0.7,
                            "max_tokens": 2000
                        },
                        "workflow_position": 1
                    },
                    {
                        "name": "Content Writer",
                        "role": "writer",
                        "description": "Creates compelling, well-researched content across various formats and platforms",
                        "system_prompt": "You are a skilled Content Writer specializing in creating engaging, informative, and persuasive content across multiple formats including blog posts, articles, social media content, and marketing copy. You excel at adapting writing style to different audiences, conducting thorough research, and incorporating SEO best practices while maintaining readability and engagement. Your writing is clear, compelling, and aligned with brand voice.",
                        "capabilities": ["content_writing", "research", "seo_optimization", "storytelling"],
                        "tools": ["writing", "research", "seo_tools"],
                        "model_config": {
                            "model": "gpt-4",
                            "temperature": 0.8,
                            "max_tokens": 3000
                        },
                        "workflow_position": 2
                    },
                    {
                        "name": "Content Editor",
                        "role": "editor",
                        "description": "Reviews, refines, and optimizes content for quality, consistency, and brand alignment",
                        "system_prompt": "You are a meticulous Content Editor with expertise in proofreading, copy editing, and content optimization. Your role involves reviewing content for grammar, style, consistency, and brand alignment. You ensure content meets quality standards, is optimized for readability, and effectively communicates the intended message. You provide constructive feedback and suggestions for improvement while maintaining the author's voice and intent.",
                        "capabilities": ["editing", "proofreading", "quality_assurance", "brand_consistency"],
                        "tools": ["editing_tools", "style_guides", "quality_checkers"],
                        "model_config": {
                            "model": "gpt-4",
                            "temperature": 0.5,
                            "max_tokens": 2000
                        },
                        "workflow_position": 3
                    }
                ],
                "workflow": {
                    "steps": [
                        {
                            "name": "Content Strategy Development",
                            "description": "Analyze requirements, develop content strategy, and create content brief",
                            "assignee": "Content Strategist",
                            "inputs": ["project_requirements", "target_audience", "business_goals"],
                            "outputs": ["content_strategy", "content_brief", "success_metrics"],
                            "dependencies": [],
                            "timeout": 300
                        },
                        {
                            "name": "Content Creation",
                            "description": "Research and write content based on strategy and brief",
                            "assignee": "Content Writer",
                            "inputs": ["content_brief", "research_materials", "brand_guidelines"],
                            "outputs": ["draft_content", "supporting_materials"],
                            "dependencies": ["Content Strategy Development"],
                            "timeout": 600
                        },
                        {
                            "name": "Content Review and Optimization",
                            "description": "Edit, proofread, and optimize content for publication",
                            "assignee": "Content Editor",
                            "inputs": ["draft_content", "style_guide", "quality_standards"],
                            "outputs": ["final_content", "optimization_report"],
                            "dependencies": ["Content Creation"],
                            "timeout": 300
                        }
                    ],
                    "coordination": {
                        "orchestrator": "Content Strategist",
                        "communication_style": "collaborative",
                        "decision_making": "consensus"
                    },
                    "execution_mode": "sequential"
                }
            },
            "tags": ["content", "writing", "marketing", "seo", "digital"],
            "keywords": ["content creation", "blog writing", "digital marketing", "seo"],
            "use_case": "Creating high-quality blog posts, articles, and digital content for marketing and engagement",
            "example_input": "Create a comprehensive blog post about sustainable business practices for small business owners",
            "expected_output": "A well-researched, engaging blog post with SEO optimization, clear structure, and actionable insights"
        })
        
        # Data Analysis Template
        templates.append({
            "name": "Business Intelligence Team",
            "description": "A specialized business intelligence team focused on data analysis, reporting, and insights generation. Combines data engineering, statistical analysis, and business intelligence expertise to transform raw data into actionable business insights and strategic recommendations.",
            "category": "analysis",
            "difficulty": "advanced",
            "visibility": "featured",
            "prompt_template": "Analyze {data_source} to generate insights about {business_question}. Focus on {analysis_type} and provide actionable recommendations for {stakeholder_group}. Include data visualization and statistical validation.",
            "team_structure_template": {
                "team_name": "Business Intelligence Team",
                "description": "Expert team for comprehensive data analysis and business intelligence",
                "objective": "Transform raw data into actionable business insights and strategic recommendations",
                "domain": "data_analysis",
                "complexity": "advanced",
                "team_members": [
                    {
                        "name": "Data Analyst",
                        "role": "analyst",
                        "description": "Performs statistical analysis, data exploration, and generates insights from complex datasets",
                        "system_prompt": "You are an expert Data Analyst with strong skills in statistical analysis, data exploration, and pattern recognition. You excel at working with large datasets, performing exploratory data analysis, identifying trends and anomalies, and generating meaningful insights. You are proficient in statistical methods, data visualization, and translating complex data findings into clear, actionable business insights. Your analysis is thorough, accurate, and focused on business value.",
                        "capabilities": ["statistical_analysis", "data_exploration", "pattern_recognition", "trend_analysis"],
                        "tools": ["python", "sql", "excel", "statistical_software"],
                        "model_config": {
                            "model": "gpt-4",
                            "temperature": 0.3,
                            "max_tokens": 2500
                        },
                        "workflow_position": 1
                    }
                ],
                "workflow": {
                    "steps": [
                        {
                            "name": "Data Analysis and Exploration",
                            "description": "Perform comprehensive statistical analysis and data exploration",
                            "assignee": "Data Analyst",
                            "inputs": ["raw_data", "business_questions", "analysis_requirements"],
                            "outputs": ["statistical_analysis", "data_insights", "findings_summary"],
                            "dependencies": [],
                            "timeout": 600
                        }
                    ],
                    "coordination": {
                        "orchestrator": "Data Analyst",
                        "communication_style": "data_driven",
                        "decision_making": "evidence_based"
                    },
                    "execution_mode": "sequential"
                }
            },
            "tags": ["data", "analysis", "business intelligence", "insights", "reporting"],
            "keywords": ["data analysis", "business intelligence", "reporting", "insights"],
            "use_case": "Analyzing business data to generate insights and recommendations for strategic decision-making",
            "example_input": "Analyze sales data to identify trends and opportunities for revenue growth",
            "expected_output": "Comprehensive analysis report with insights, visualizations, and actionable recommendations"
        })
        
        return templates
    
    def create_template(self, template_data: Dict[str, Any]) -> bool:
        """Create a single template via API."""
        try:
            response = requests.post(
                TEMPLATES_ENDPOINT,
                headers=AUTH_HEADERS,
                json=template_data,
                timeout=30
            )
            
            if response.status_code == 200:
                print(f"✓ Created template: {template_data['name']}")
                return True
            else:
                print(f"✗ Failed to create template: {template_data['name']}")
                print(f"  Status: {response.status_code}")
                print(f"  Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"✗ Error creating template {template_data['name']}: {str(e)}")
            return False
    
    def populate_all_templates(self) -> None:
        """Populate all templates via API."""
        print(f"Starting template population with {len(self.templates)} templates...")
        
        success_count = 0
        for template_data in self.templates:
            if self.create_template(template_data):
                success_count += 1
        
        print(f"\nCompleted! Successfully created {success_count}/{len(self.templates)} templates.")


def main():
    """Main function to run template population."""
    populator = TemplateAPIPopulator()
    populator.populate_all_templates()


if __name__ == "__main__":
    main()
