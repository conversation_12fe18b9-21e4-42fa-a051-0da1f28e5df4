# Meta-Agent 自动生成和管理平台

🚀 **一个革命性的AI Agent自动生成和管理平台，让AI Agent的创建变得像描述需求一样简单！**

## 🎯 项目概述

Meta-Agent是一个完整的AI Agent工厂，能够根据用户的自然语言需求描述，自动设计、生成、部署和管理专业的AI Agent团队。这是一个真正意义上的"AI Agent自动化平台"，将Agent开发周期从数天缩短到数分钟。

### ✨ 核心功能
- 🧠 **智能需求分析**: 自动识别领域、评估复杂度、推荐最佳模板
- 👥 **自动团队规划**: 根据需求设计团队成员、角色和工作流程
- ⚙️ **代码自动生成**: 生成完整的Agent类代码和配置文件
- 🔄 **动态加载执行**: 运行时热加载Agent，支持实时更新
- 📊 **完整管理界面**: 可视化的Agent创建、管理、测试和监控

### 🏆 技术亮点
- **AI驱动**: 使用先进的Prompt工程和智能分析技术
- **全栈解决方案**: 前后端完整集成的生产级应用
- **现代化架构**: 微服务架构，支持水平扩展
- **用户体验优先**: 直观易用的现代化界面设计

## 🏗️ 项目结构

```
meta-agent/
├── frontend/               # Next.js前端项目
│   ├── src/app/           # 页面和路由
│   ├── src/components/    # React组件
│   └── src/lib/           # 工具库和API
├── backend/               # FastAPI后端项目
│   ├── app/core/          # 核心功能 (AI规划师、代码生成器等)
│   ├── app/api/           # API路由
│   └── generated_agents/  # 生成的Agent代码
├── docs/                  # 完整项目文档
├── test_system.py         # 端到端测试脚本
└── README.md
```

## 🚀 快速开始

### 环境要求
- Node.js 18+
- Python 3.11+
- npm 或 yarn

### 1. 启动后端服务
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
python simple_run.py
```

### 2. 启动前端服务
```bash
cd frontend
npm install
npm run dev
```

### 3. 访问应用
- 前端界面: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

## 📚 完整文档

我们提供了完整的文档体系，涵盖使用、开发、部署的各个方面：

### 📖 用户文档
- **[📚 文档中心](./docs/INDEX.md)** - 文档导航和学习路径
- **[🚀 项目概述](./docs/README.md)** - 详细的项目介绍和特性说明
- **[👤 用户指南](./docs/USER_GUIDE.md)** - 完整的使用说明和最佳实践

### 🔧 开发文档
- **[🏗️ 架构设计](./docs/ARCHITECTURE.md)** - 系统架构和设计原则
- **[💻 开发指南](./docs/DEVELOPMENT.md)** - 开发环境配置和代码规范
- **[🔌 API文档](./docs/API.md)** - 完整的REST API接口文档

### 🚀 运维文档
- **[🚀 部署指南](./docs/DEPLOYMENT.md)** - 从开发到生产的部署流程
- **[📝 更新日志](./docs/CHANGELOG.md)** - 版本更新记录和功能变更

## 🛠️ 技术栈

### 前端技术
- **Next.js 15** + **TypeScript** - 现代化的React框架
- **Tailwind CSS** + **shadcn/ui** - 美观的UI设计系统
- **Axios** - HTTP客户端库

### 后端技术
- **FastAPI** + **Python 3.11+** - 高性能Web框架
- **Pydantic** - 数据验证和序列化
- **Jinja2** - 模板引擎，用于代码生成

### AI技术
- **Prompt Engineering** - 高级提示词工程
- **动态代码生成** - 基于模板的智能代码生成
- **智能JSON解析** - 容错的结构化数据提取

## 🎯 项目状态

### ✅ 已完成功能
- **完整的前端界面**: 现代化的用户界面，支持Agent创建、管理、测试
- **AI规划师**: 智能需求分析和团队规划生成
- **代码生成器**: 自动生成完整的Agent代码
- **动态加载器**: 运行时热加载Agent实例
- **API接口**: 完整的RESTful API和OpenAPI文档
- **系统测试**: 端到端测试覆盖所有核心功能

### 🚀 核心特性
- **🎯 智能化**: 从需求到部署的全自动化流程
- **⚡ 高性能**: 平均5秒内完成Agent创建和部署
- **🔄 实时性**: 支持热加载和动态更新
- **📊 可视化**: 完整的管理界面和监控工具
- **🧪 可测试**: 内置测试工具和性能分析

## 🧪 系统测试

运行完整的端到端测试：

```bash
python test_system.py
```

测试覆盖：
- ✅ 后端API健康检查
- ✅ 规划模板和需求分析
- ✅ Agent创建和执行
- ✅ 前端页面可访问性
- ✅ 系统性能和稳定性

## 🤝 贡献指南

我们欢迎所有形式的贡献！请查看 **[开发指南](./docs/DEVELOPMENT.md)** 了解：

- 开发环境配置
- 代码规范和最佳实践
- 测试策略和质量保证
- 提交流程和代码审查

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢所有为Meta-Agent项目做出贡献的开发者、测试者和用户！

---

**🚀 Meta-Agent - 让AI Agent的创建变得像描述需求一样简单！**

如果这个项目对您有帮助，请给我们一个 ⭐️ ！
