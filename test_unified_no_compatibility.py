#!/usr/bin/env python3
"""
测试统一架构：完全使用role，无向后兼容
"""

def test_migration_logic():
    """测试迁移逻辑"""
    print("🔄 测试迁移逻辑...")
    
    # 模拟旧的team_plan（使用name作为assignee）
    old_team_plan = {
        "team_members": [
            {"name": "内容策略师", "role": "content_strategist"},
            {"name": "内容撰写专家", "role": "content_writer"},
            {"name": "社交媒体专家", "role": "social_media_specialist"}
        ],
        "workflow": {
            "steps": [
                {"name": "策略制定", "assignee": "内容策略师"},  # 旧格式：使用name
                {"name": "内容撰写", "assignee": "内容撰写专家"},  # 旧格式：使用name
                {"name": "社交媒体优化", "assignee": "社交媒体专家"},  # 旧格式：使用name
                {"name": "团队协作", "assignee": "团队协作"}  # 旧格式：中文团队协作
            ]
        }
    }
    
    print(f"📊 迁移前:")
    for step in old_team_plan["workflow"]["steps"]:
        print(f"  - {step['name']}: assignee='{step['assignee']}'")
    
    # 模拟迁移逻辑
    def migrate_workflow_assignees(team_plan):
        """模拟_migrate_workflow_assignees方法"""
        team_members = team_plan.get("team_members", [])
        workflow = team_plan.get("workflow", {})
        
        if not workflow or "steps" not in workflow:
            return
        
        # 创建name到role的映射
        name_to_role = {}
        for member in team_members:
            name = member.get("name")
            role = member.get("role")
            if name and role:
                name_to_role[name] = role
        
        print(f"📝 Name到Role映射: {name_to_role}")
        
        # 迁移每个工作流步骤
        migrated_count = 0
        for step in workflow["steps"]:
            assignee = step.get("assignee")
            if assignee and assignee in name_to_role:
                old_assignee = assignee
                new_assignee = name_to_role[assignee]
                step["assignee"] = new_assignee
                migrated_count += 1
                print(f"  ✅ 迁移: '{old_assignee}' -> '{new_assignee}'")
            elif assignee == "团队协作":
                step["assignee"] = "team_collaboration"
                migrated_count += 1
                print(f"  ✅ 迁移团队协作: '团队协作' -> 'team_collaboration'")
        
        return migrated_count
    
    # 执行迁移
    migrated_count = migrate_workflow_assignees(old_team_plan)
    
    print(f"\n📊 迁移后:")
    for step in old_team_plan["workflow"]["steps"]:
        print(f"  - {step['name']}: assignee='{step['assignee']}'")
    
    print(f"\n✅ 迁移完成: {migrated_count} 个assignee已迁移")
    
    return migrated_count > 0

def test_role_only_lookup():
    """测试只支持role查找的逻辑"""
    print(f"\n🔍 测试只支持role查找...")
    
    team_members = [
        {"name": "内容策略师", "role": "content_strategist"},
        {"name": "内容撰写专家", "role": "content_writer"}
    ]
    
    def find_team_member_role_only(role: str, team_members: list):
        """模拟新的_find_team_member方法（只支持role）"""
        if role == "team_collaboration":
            return {
                "name": "团队协作",
                "role": "team_collaboration",
                "is_collaborative": True
            }
        
        # 只通过role查找
        for member in team_members:
            if member.get("role") == role:
                return member
        
        # 如果找不到，返回None并记录错误
        available_roles = [m.get('role') for m in team_members]
        print(f"❌ 找不到role '{role}'，可用roles: {available_roles}")
        return None
    
    # 测试用例
    test_cases = [
        ("content_strategist", "有效role"),
        ("content_writer", "有效role"),
        ("team_collaboration", "团队协作"),
        ("内容策略师", "无效：使用name而不是role"),
        ("nonexistent_role", "无效：不存在的role")
    ]
    
    print(f"🧪 测试查找:")
    success_count = 0
    for role, description in test_cases:
        member = find_team_member_role_only(role, team_members)
        if member:
            print(f"  ✅ '{role}' ({description}): 找到 {member.get('name', 'N/A')} ({member.get('role', 'N/A')})")
            success_count += 1
        else:
            print(f"  ❌ '{role}' ({description}): 未找到")
    
    print(f"\n📊 查找结果: {success_count}/{len(test_cases)} 成功")
    
    return success_count >= 3  # 至少3个有效的应该成功

def test_variable_matching_unified():
    """测试统一架构下的变量匹配"""
    print(f"\n🎯 测试统一架构下的变量匹配...")
    
    # 模拟迁移后的workflow（使用role作为assignee）
    workflow_steps = [
        {"assignee": "content_strategist"},
        {"assignee": "content_writer"},
        {"assignee": "social_media_specialist"}
    ]
    
    # 模拟变量发现结果（使用role作为source_agent）
    discovered_variables = [
        {"placeholder": "{user.requirements}", "source_agent": None},
        {"placeholder": "{content_strategist.strategy}", "source_agent": "content_strategist"},
        {"placeholder": "{content_writer.content}", "source_agent": "content_writer"},
        {"placeholder": "{social_media_specialist.social_content}", "source_agent": "social_media_specialist"}
    ]
    
    print(f"📝 工作流步骤:")
    for i, step in enumerate(workflow_steps):
        print(f"  - 步骤{i+1}: assignee='{step['assignee']}'")
    
    print(f"\n📝 发现的变量:")
    for var in discovered_variables:
        print(f"  - {var['placeholder']}: source_agent='{var['source_agent']}'")
    
    print(f"\n🔄 变量匹配测试:")
    total_matches = 0
    for step in workflow_steps:
        assignee = step["assignee"]  # 现在直接是role
        
        # 直接匹配（无需映射）
        agent_variables = [var for var in discovered_variables if var["source_agent"] == assignee]
        
        print(f"  - assignee '{assignee}': {len(agent_variables)} 个变量")
        for var in agent_variables:
            print(f"    - {var['placeholder']}")
            total_matches += 1
    
    print(f"\n✅ 总匹配数: {total_matches}")
    
    return total_matches > 0

def main():
    """主测试函数"""
    print("🚀 开始测试统一架构（无向后兼容）\n")
    
    # 测试1: 迁移逻辑
    test1_result = test_migration_logic()
    
    # 测试2: 只支持role查找
    test2_result = test_role_only_lookup()
    
    # 测试3: 统一变量匹配
    test3_result = test_variable_matching_unified()
    
    print(f"\n📊 测试结果:")
    print(f"✅ 迁移逻辑: {'通过' if test1_result else '失败'}")
    print(f"✅ Role查找: {'通过' if test2_result else '失败'}")
    print(f"✅ 变量匹配: {'通过' if test3_result else '失败'}")
    
    if test1_result and test2_result and test3_result:
        print(f"\n🎉 统一架构测试成功！")
        print(f"🔧 架构特点:")
        print(f"  - 完全使用英文role作为标识符")
        print(f"  - 自动迁移旧配置到新格式")
        print(f"  - 移除向后兼容性，强制统一标准")
        print(f"  - 简化变量匹配逻辑")
        print(f"  - 消除name/role混用问题")
    else:
        print(f"\n❌ 统一架构测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
