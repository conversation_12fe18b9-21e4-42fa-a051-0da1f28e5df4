#!/usr/bin/env python3
"""
测试模板API修复
验证所有模板相关的API端点是否能正确处理JSON字段序列化问题
"""

import asyncio
import aiohttp
import json

BASE_URL = "http://localhost:8000"

async def test_template_api():
    """测试模板API端点"""
    print("🧪 测试模板API修复...")
    
    # 模拟登录获取token（这里简化处理）
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer test_token"  # 实际应用中需要真实token
    }
    
    async with aiohttp.ClientSession() as session:
        
        # 测试1: 获取模板列表
        print("\n1. 测试获取模板列表...")
        try:
            async with session.get(f"{BASE_URL}/api/v1/templates", headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"   ✅ 模板列表获取成功，共 {len(data.get('templates', []))} 个模板")
                    
                    # 检查第一个模板的数据结构
                    if data.get('templates'):
                        template = data['templates'][0]
                        print(f"   - 模板名称: {template.get('name')}")
                        print(f"   - 分类: {template.get('category')}")
                        print(f"   - 标签类型: {type(template.get('tags'))}")
                        print(f"   - 标签内容: {template.get('tags')}")
                else:
                    print(f"   ❌ 模板列表获取失败: {response.status}")
                    error_text = await response.text()
                    print(f"   错误信息: {error_text}")
        except Exception as e:
            print(f"   ❌ 请求失败: {e}")
        
        # 测试2: 获取特定模板详情
        print("\n2. 测试获取模板详情...")
        try:
            # 先获取一个模板ID
            async with session.get(f"{BASE_URL}/api/v1/templates", headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('templates'):
                        template_id = data['templates'][0]['template_id']
                        
                        # 获取模板详情
                        async with session.get(f"{BASE_URL}/api/v1/templates/{template_id}", headers=headers) as detail_response:
                            if detail_response.status == 200:
                                template_data = await detail_response.json()
                                print(f"   ✅ 模板详情获取成功: {template_data.get('name')}")
                                print(f"   - team_structure_template类型: {type(template_data.get('team_structure_template'))}")
                                print(f"   - default_config类型: {type(template_data.get('default_config'))}")
                                print(f"   - tags类型: {type(template_data.get('tags'))}")
                                
                                # 验证JSON字段是否正确解析
                                if isinstance(template_data.get('team_structure_template'), dict):
                                    print("   ✅ team_structure_template正确解析为字典")
                                else:
                                    print("   ❌ team_structure_template未正确解析")
                                    
                                if isinstance(template_data.get('tags'), list):
                                    print("   ✅ tags正确解析为列表")
                                else:
                                    print("   ❌ tags未正确解析")
                            else:
                                print(f"   ❌ 模板详情获取失败: {detail_response.status}")
                                error_text = await detail_response.text()
                                print(f"   错误信息: {error_text}")
                    else:
                        print("   ⚠️  没有可用的模板进行测试")
                else:
                    print("   ❌ 无法获取模板列表进行详情测试")
        except Exception as e:
            print(f"   ❌ 请求失败: {e}")
        
        # 测试3: 搜索模板
        print("\n3. 测试搜索模板...")
        try:
            async with session.get(f"{BASE_URL}/api/v1/templates/search?q=test", headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"   ✅ 模板搜索成功，找到 {len(data.get('templates', []))} 个结果")
                else:
                    print(f"   ❌ 模板搜索失败: {response.status}")
                    error_text = await response.text()
                    print(f"   错误信息: {error_text}")
        except Exception as e:
            print(f"   ❌ 请求失败: {e}")
        
        # 测试4: 获取推荐模板
        print("\n4. 测试获取推荐模板...")
        try:
            async with session.get(f"{BASE_URL}/api/v1/templates/featured", headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"   ✅ 推荐模板获取成功，共 {len(data.get('templates', []))} 个模板")
                else:
                    print(f"   ❌ 推荐模板获取失败: {response.status}")
                    error_text = await response.text()
                    print(f"   错误信息: {error_text}")
        except Exception as e:
            print(f"   ❌ 请求失败: {e}")

def test_json_parsing_comprehensive():
    """全面测试JSON解析功能"""
    print("\n🧪 全面测试JSON解析功能...")
    
    # 导入我们的解析函数
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
    
    try:
        from app.api.v1.endpoints.templates import parse_template_json_fields
        
        # 测试各种复杂的JSON情况
        test_cases = [
            {
                "name": "Unicode JSON",
                "data": {
                    "team_structure_template": '{"team_name": "\\u4fa6\\u5bdf\\u56e2\\u961f", "description": "\\u8fd9\\u662f\\u4e00\\u4e2a\\u6d4b\\u8bd5"}',
                    "category": "BUSINESS",
                    "tags": '["\\u6d4b\\u8bd5", "\\u6a21\\u677f"]'
                }
            },
            {
                "name": "Mixed content",
                "data": {
                    "team_structure_template": '{"name": "test", "chinese": "\\u4e2d\\u6587"}',
                    "difficulty": "INTERMEDIATE",
                    "tags": '["test", "\\u6d4b\\u8bd5"]'
                }
            },
            {
                "name": "Complex nested",
                "data": {
                    "team_structure_template": '{"team": {"members": [{"name": "\\u6210\\u5458", "role": "\\u89d2\\u8272"}]}}',
                    "visibility": "PRIVATE",
                    "keywords": '["keyword1", "\\u5173\\u952e\\u8bcd"]'
                }
            }
        ]
        
        all_passed = True
        for test_case in test_cases:
            print(f"\n   测试用例: {test_case['name']}")
            try:
                result = parse_template_json_fields(test_case['data'].copy())
                
                # 验证JSON字段解析
                if isinstance(result.get('team_structure_template'), dict):
                    print("   ✅ team_structure_template解析正确")
                else:
                    print("   ❌ team_structure_template解析失败")
                    all_passed = False
                
                # 验证枚举字段转换
                enum_fields = ['category', 'difficulty', 'visibility', 'status']
                for field in enum_fields:
                    if field in result and isinstance(result[field], str):
                        if result[field].islower():
                            print(f"   ✅ {field}正确转换为小写")
                        else:
                            print(f"   ❌ {field}未转换为小写")
                            all_passed = False
                
                # 验证列表字段解析
                list_fields = ['tags', 'keywords']
                for field in list_fields:
                    if field in result:
                        if isinstance(result[field], list):
                            print(f"   ✅ {field}正确解析为列表")
                        else:
                            print(f"   ❌ {field}未正确解析为列表")
                            all_passed = False
                            
            except Exception as e:
                print(f"   ❌ 解析失败: {e}")
                all_passed = False
        
        if all_passed:
            print("\n   🎉 所有JSON解析测试通过!")
        else:
            print("\n   ❌ 部分JSON解析测试失败")
            
        return all_passed
        
    except ImportError as e:
        print(f"   ❌ 无法导入解析函数: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始测试模板API修复...")
    print("=" * 60)
    
    # 测试JSON解析功能
    json_test_passed = test_json_parsing_comprehensive()
    
    # 测试API端点
    await test_template_api()
    
    print("\n" + "=" * 60)
    print("📋 测试总结:")
    print(f"- JSON解析功能: {'✅ 通过' if json_test_passed else '❌ 失败'}")
    print("- API端点测试: 请查看上述输出")
    
    print("\n🎯 修复内容:")
    print("1. ✅ 创建了通用的JSON字段解析函数")
    print("2. ✅ 修复了所有TemplateResponse创建点")
    print("3. ✅ 处理Unicode转义序列")
    print("4. ✅ 枚举字段大小写转换")
    print("5. ✅ 边界情况处理")
    
    print("\n现在所有模板API应该能够:")
    print("- 正确解析JSON字段为字典/列表类型")
    print("- 处理包含Unicode字符的JSON数据")
    print("- 自动转换枚举字段为小写")
    print("- 返回正确格式的TemplateResponse")

if __name__ == "__main__":
    asyncio.run(main())
