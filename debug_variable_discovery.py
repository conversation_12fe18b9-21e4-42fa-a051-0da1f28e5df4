#!/usr/bin/env python3
"""
调试变量发现流程的脚本
"""

import sys
import os
import re
from typing import Dict, List, Any

# 添加backend路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

def debug_variable_discovery():
    """调试变量发现流程"""
    print("🔍 调试变量发现流程...")
    
    # 模拟真实的team_plan数据结构
    team_plan = {
        "team_name": "测试团队",
        "team_members": [
            {
                "role": "planner",
                "name": "规划师",
                "system_prompt": "你是一个规划师。根据{user.requirements}制定详细的任务分解计划，输出为{planner.task_breakdown}。",
                "description": "负责任务规划和分解"
            },
            {
                "role": "analyst", 
                "name": "分析师",
                "system_prompt": "你是一个分析师。基于{user.requirements}和{planner.task_breakdown}进行深度分析，输出为{analyst.analysis_results}。",
                "description": "负责需求分析"
            },
            {
                "role": "executor",
                "name": "执行者", 
                "system_prompt": "你是一个执行者。根据{planner.task_breakdown}和{analyst.analysis_results}执行具体任务，输出为{executor.execution_output}。",
                "description": "负责任务执行"
            }
        ],
        "workflow_steps": [
            {"name": "任务规划", "assignee": "planner", "description": "制定任务计划"},
            {"name": "需求分析", "assignee": "analyst", "description": "分析用户需求"},
            {"name": "任务执行", "assignee": "executor", "description": "执行具体任务"}
        ]
    }
    
    print(f"📊 Team Plan结构:")
    print(f"  - 团队名称: {team_plan['team_name']}")
    print(f"  - 团队成员数量: {len(team_plan['team_members'])}")
    print(f"  - 工作流步骤数量: {len(team_plan['workflow_steps'])}")
    
    # 手动提取变量
    print("\n🔍 手动提取变量...")
    VARIABLE_PATTERN = re.compile(r'\{([^}]+)\}')
    
    all_variables = set()
    for i, member in enumerate(team_plan["team_members"]):
        role = member.get("role", f"member_{i}")
        system_prompt = member.get("system_prompt", "")
        description = member.get("description", "")
        
        print(f"\n👤 分析成员: {role}")
        print(f"  - 系统提示: {system_prompt[:100]}...")
        
        # 从系统提示中提取变量
        matches = VARIABLE_PATTERN.findall(system_prompt)
        print(f"  - 找到变量: {matches}")
        
        for match in matches:
            variable_name = match.strip()
            placeholder = f"{{{variable_name}}}"
            all_variables.add(placeholder)
            
            # 分析变量类型
            if variable_name.startswith('user.'):
                var_type = "USER_INPUT"
                source_agent = None
            elif '.' in variable_name:
                prefix = variable_name.split('.')[0].lower()
                if prefix == role:
                    var_type = "OUTPUT"
                    source_agent = role
                else:
                    var_type = "INTER_AGENT"
                    source_agent = prefix
            else:
                var_type = "UNKNOWN"
                source_agent = None
            
            print(f"    - {placeholder}: 类型={var_type}, 来源={source_agent}")
    
    print(f"\n📝 总共发现 {len(all_variables)} 个唯一变量:")
    for var in sorted(all_variables):
        print(f"  - {var}")
    
    # 测试变量发现服务
    print("\n🧪 测试变量发现服务...")
    try:
        from app.services.variable_discovery import VariableDiscoveryService
        
        discovery_service = VariableDiscoveryService()
        variables = discovery_service.discover_team_variables(team_plan)
        
        print(f"✅ 变量发现服务返回 {len(variables)} 个变量:")
        for var in variables:
            print(f"  - {var.placeholder}: 类型={var.variable_type.value}, 来源={var.source_agent}")
        
        # 分析为什么某些agent找不到变量
        print("\n🔍 分析agent变量匹配...")
        for test_agent in ["planner", "analyst", "executor"]:
            agent_vars = [v for v in variables if v.source_agent == test_agent]
            print(f"  - {test_agent}: {len(agent_vars)} 个变量")
            for var in agent_vars:
                print(f"    - {var.placeholder}")
        
        return variables
        
    except Exception as e:
        print(f"❌ 变量发现服务测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

def debug_agent_matching():
    """调试agent匹配逻辑"""
    print("\n🎯 调试agent匹配逻辑...")
    
    # 测试不同的agent名称格式
    test_cases = [
        ("planner", "planner"),
        ("规划师", "planner"),
        ("Planner", "planner"),
        ("任务规划师", "planner"),
        ("内容策略师", "planner"),
        ("社交媒体专家", "executor"),
    ]
    
    for agent_name, expected_role in test_cases:
        # 模拟匹配逻辑
        normalized_name = agent_name.lower()
        
        # 检查是否包含关键词
        role_keywords = {
            "planner": ["plan", "规划", "策略"],
            "analyst": ["analy", "分析", "研究"],
            "executor": ["execut", "执行", "实施", "专家", "制作"]
        }
        
        matched_role = None
        for role, keywords in role_keywords.items():
            if any(keyword in normalized_name for keyword in keywords):
                matched_role = role
                break
        
        print(f"  - '{agent_name}' -> 期望: {expected_role}, 匹配: {matched_role}")

def main():
    """主函数"""
    print("🚀 开始调试变量发现流程\n")
    
    # 调试1: 变量发现
    variables = debug_variable_discovery()
    
    # 调试2: agent匹配
    debug_agent_matching()
    
    print(f"\n📊 调试总结:")
    print(f"✅ 手动提取变量成功")
    print(f"✅ 变量发现服务测试完成")
    print(f"✅ agent匹配逻辑分析完成")
    
    if len(variables) == 0:
        print("\n❌ 问题分析:")
        print("  - 变量发现服务返回0个变量")
        print("  - 可能的原因:")
        print("    1. team_plan数据结构不匹配")
        print("    2. 正则表达式匹配失败")
        print("    3. 变量分类逻辑错误")
        print("    4. source_agent匹配逻辑错误")

if __name__ == "__main__":
    main()
