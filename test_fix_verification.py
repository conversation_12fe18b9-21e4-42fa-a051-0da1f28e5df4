#!/usr/bin/env python3
"""
验证变量跟踪修复的简单测试
"""

import asyncio
import sys
import os

# 添加backend路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

async def test_config_driven_agent():
    """测试ConfigDrivenAgent的变量发现功能"""
    print("🧪 测试ConfigDrivenAgent变量发现...")
    
    try:
        from app.services.dynamic_loader import ConfigDrivenAgent
        
        # 模拟agent配置
        agent_config = {
            "agent_id": "test-agent",
            "team_name": "测试团队",
            "team_plan": {
                "team_name": "测试团队",
                "team_members": [
                    {
                        "role": "planner",
                        "name": "规划师",
                        "system_prompt": "你是规划师，根据{user.requirements}制定计划，输出{planner.task_breakdown}。",
                        "description": "负责任务规划"
                    },
                    {
                        "role": "analyst", 
                        "name": "分析师",
                        "system_prompt": "你是分析师，基于{planner.task_breakdown}分析，输出{analyst.analysis_results}。",
                        "description": "负责需求分析"
                    }
                ],
                "workflow": {
                    "steps": [
                        {"name": "任务规划", "assignee": "planner", "description": "制定计划"},
                        {"name": "需求分析", "assignee": "analyst", "description": "分析需求"}
                    ]
                }
            }
        }
        
        # 创建ConfigDrivenAgent实例
        agent = ConfigDrivenAgent(agent_config)
        
        print(f"✅ 创建agent成功: {agent.team_name}")
        print(f"📊 团队成员数量: {len(agent.team_members)}")
        print(f"🔄 工作流步骤数量: {len(agent.workflow.get('steps', []))}")
        
        # 测试变量发现
        print("\n🔍 测试变量发现...")
        
        # 测试planner的变量
        planner_vars = await agent._get_discovered_variables_for_agent("planner")
        print(f"📝 planner应产生的变量: {len(planner_vars)}")
        for var in planner_vars:
            print(f"  - {var['placeholder']}: {var['semantic_description']}")
        
        # 测试analyst的变量
        analyst_vars = await agent._get_discovered_variables_for_agent("analyst")
        print(f"📝 analyst应产生的变量: {len(analyst_vars)}")
        for var in analyst_vars:
            print(f"  - {var['placeholder']}: {var['semantic_description']}")
        
        print("✅ 变量发现测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_variable_tracking_flow():
    """测试完整的变量跟踪流程"""
    print("\n🔄 测试变量跟踪流程...")
    
    try:
        from app.services.dynamic_loader import ConfigDrivenAgent
        
        # 创建agent
        agent_config = {
            "agent_id": "test-agent",
            "team_name": "测试团队",
            "team_plan": {
                "team_name": "测试团队",
                "team_members": [
                    {
                        "role": "planner",
                        "name": "规划师",
                        "system_prompt": "根据{user.requirements}制定{planner.task_breakdown}。",
                        "description": "负责任务规划"
                    }
                ],
                "workflow": {
                    "steps": [
                        {"name": "任务规划", "assignee": "planner", "description": "制定计划"}
                    ]
                }
            }
        }
        
        agent = ConfigDrivenAgent(agent_config)
        
        # 模拟步骤完成
        step_name = "任务规划"
        assignee = "planner"
        ai_response = "这是完整的任务分解计划：\n1. 需求分析\n2. 方案设计\n3. 实施执行\n4. 结果验证"
        step_index = 0
        
        print(f"🎯 模拟步骤完成: {step_name} by {assignee}")
        print(f"📝 AI响应长度: {len(ai_response)} 字符")
        
        # 测试变量跟踪（不实际发送WebSocket）
        await agent._track_step_variables(step_name, assignee, ai_response, step_index)
        
        print("✅ 变量跟踪流程测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 流程测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🚀 开始验证变量跟踪修复\n")
    
    # 测试1: ConfigDrivenAgent变量发现
    test1_result = await test_config_driven_agent()
    
    # 测试2: 变量跟踪流程
    test2_result = await test_variable_tracking_flow()
    
    print(f"\n📊 测试结果:")
    print(f"✅ 变量发现: {'通过' if test1_result else '失败'}")
    print(f"✅ 跟踪流程: {'通过' if test2_result else '失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！修复成功！")
        print("🔧 修复内容:")
        print("  - 修复了workflow_steps属性访问问题")
        print("  - 只跟踪prompt中定义的变量")
        print("  - 只在步骤完成后发送完整内容")
        print("  - 移除了频繁的中间状态广播")
    else:
        print("\n❌ 部分测试失败，需要进一步调试")

if __name__ == "__main__":
    asyncio.run(main())
