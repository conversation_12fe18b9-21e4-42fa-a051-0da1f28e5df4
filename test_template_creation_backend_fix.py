#!/usr/bin/env python3
"""
测试模板创建后端修复
验证从Agent创建模板时的JSON字段序列化问题是否已解决
"""

import asyncio
import json
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from app.models.planning import Template, TemplateResponse
from app.api.v1.endpoints.templates import parse_template_json_fields

async def test_json_field_parsing():
    """测试JSON字段解析函数"""
    print("🧪 测试JSON字段解析函数...")
    
    # 模拟从数据库获取的数据（JSON字段被序列化为字符串）
    template_dict = {
        "id": 1,
        "template_id": "test_template_123",
        "name": "测试模板",
        "description": "这是一个测试模板",
        "category": "business",
        "difficulty": "intermediate",
        "visibility": "private",
        "status": "active",
        "prompt_template": "这是一个测试提示词模板",
        "team_structure_template": '{"team_name": "测试团队", "description": "测试描述"}',  # JSON字符串
        "default_config": '{"model": "gpt-4", "temperature": 0.7}',  # JSON字符串
        "template_metadata": '{"created_from_agent": true}',  # JSON字符串
        "tags": '["测试", "模板"]',  # JSON字符串
        "keywords": '["test", "template"]',  # JSON字符串
        "usage_count": 0,
        "rating_count": 0,
        "user_id": 1,
        "author_name": "测试用户",
        "use_case": "测试用例",
        "created_at": "2025-01-08T05:00:00",
        "updated_at": None
    }
    
    print("修复前的数据类型:")
    for key, value in template_dict.items():
        if key in ["team_structure_template", "default_config", "template_metadata", "tags", "keywords"]:
            print(f"  {key}: {type(value)} = {value}")
    
    # 应用JSON字段解析
    parsed_dict = parse_template_json_fields(template_dict.copy())
    
    print("\n修复后的数据类型:")
    for key, value in parsed_dict.items():
        if key in ["team_structure_template", "default_config", "template_metadata", "tags", "keywords"]:
            print(f"  {key}: {type(value)} = {value}")
    
    # 验证解析结果
    assert isinstance(parsed_dict["team_structure_template"], dict), "team_structure_template应该是字典类型"
    assert isinstance(parsed_dict["default_config"], dict), "default_config应该是字典类型"
    assert isinstance(parsed_dict["template_metadata"], dict), "template_metadata应该是字典类型"
    assert isinstance(parsed_dict["tags"], list), "tags应该是列表类型"
    assert isinstance(parsed_dict["keywords"], list), "keywords应该是列表类型"
    
    print("✅ JSON字段解析测试通过!")
    
    # 测试创建TemplateResponse
    try:
        parsed_dict["is_owner"] = True
        parsed_dict["can_edit"] = True
        response = TemplateResponse(**parsed_dict)
        print("✅ TemplateResponse创建成功!")
        print(f"   模板名称: {response.name}")
        print(f"   团队结构类型: {type(response.team_structure_template)}")
        print(f"   标签类型: {type(response.tags)}")
        return True
    except Exception as e:
        print(f"❌ TemplateResponse创建失败: {e}")
        return False

def test_edge_cases():
    """测试边界情况"""
    print("\n🧪 测试边界情况...")
    
    # 测试None值
    template_dict = {
        "team_structure_template": None,
        "default_config": None,
        "template_metadata": None,
        "tags": None,
        "keywords": None,
    }
    
    parsed_dict = parse_template_json_fields(template_dict.copy())
    
    assert parsed_dict["team_structure_template"] == {}, "None值应该转换为空字典"
    assert parsed_dict["default_config"] == {}, "None值应该转换为空字典"
    assert parsed_dict["template_metadata"] == {}, "None值应该转换为空字典"
    assert parsed_dict["tags"] == [], "None值应该转换为空列表"
    assert parsed_dict["keywords"] == [], "None值应该转换为空列表"
    
    print("✅ None值处理测试通过!")
    
    # 测试无效JSON
    template_dict = {
        "team_structure_template": "invalid json {",
        "default_config": "not json at all",
        "template_metadata": "{broken: json}",
        "tags": "[invalid, json",
        "keywords": "not a list",
    }
    
    parsed_dict = parse_template_json_fields(template_dict.copy())
    
    assert parsed_dict["team_structure_template"] == {}, "无效JSON应该转换为空字典"
    assert parsed_dict["default_config"] == {}, "无效JSON应该转换为空字典"
    assert parsed_dict["template_metadata"] == {}, "无效JSON应该转换为空字典"
    assert parsed_dict["tags"] == [], "无效JSON应该转换为空列表"
    assert parsed_dict["keywords"] == [], "无效JSON应该转换为空列表"
    
    print("✅ 无效JSON处理测试通过!")
    
    # 测试已经是正确类型的数据
    template_dict = {
        "team_structure_template": {"already": "dict"},
        "default_config": {"already": "dict"},
        "template_metadata": {"already": "dict"},
        "tags": ["already", "list"],
        "keywords": ["already", "list"],
    }
    
    parsed_dict = parse_template_json_fields(template_dict.copy())
    
    assert parsed_dict["team_structure_template"] == {"already": "dict"}, "已经是字典的数据应该保持不变"
    assert parsed_dict["tags"] == ["already", "list"], "已经是列表的数据应该保持不变"
    
    print("✅ 正确类型数据处理测试通过!")

async def main():
    """主测试函数"""
    print("🚀 开始测试模板创建后端修复...")
    print("=" * 50)
    
    # 测试JSON字段解析
    success = await test_json_field_parsing()
    
    # 测试边界情况
    test_edge_cases()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 所有测试通过! 后端修复成功!")
        print("\n修复内容总结:")
        print("1. ✅ 创建了通用的JSON字段解析函数 parse_template_json_fields()")
        print("2. ✅ 修复了 create_template_from_agent 中的字段序列化问题")
        print("3. ✅ 修复了 metadata 字段名错误 (应为 template_metadata)")
        print("4. ✅ 在多个关键函数中应用了JSON字段解析")
        print("5. ✅ 处理了边界情况 (None值、无效JSON、正确类型)")
        
        print("\n现在用户应该能够:")
        print("- 成功从Agent创建模板而不会遇到序列化错误")
        print("- 获取模板详情时JSON字段正确解析")
        print("- 创建和更新模板时数据类型正确")
    else:
        print("❌ 测试失败，需要进一步调试")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
