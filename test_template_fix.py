#!/usr/bin/env python3
"""
Test script to verify the "Save Agent as Template" functionality fix.
"""

import asyncio
import json
import uuid
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

# Add the backend directory to the path
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.core.database import AsyncSessionLocal
from app.models.agent import Agent, AgentStatus, AgentType
from app.models.user import User


async def get_test_user():
    """Get an existing user for testing."""
    async with AsyncSessionLocal() as session:
        # Use the admin user for testing
        result = await session.execute(
            text("SELECT * FROM users WHERE email = :email"),
            {"email": "<EMAIL>"}
        )
        user = result.fetchone()

        if user:
            print(f"✅ Using existing admin user: {user.email}")
            return user
        else:
            print("❌ No admin user found")
            return None


async def create_test_agent(user_id: int):
    """Create a test agent for testing template functionality."""
    async with AsyncSessionLocal() as session:
        agent_id = f"agent_{uuid.uuid4().hex[:12]}"
        
        # Sample team plan with complete structure
        team_plan = {
            "team_name": "内容创作团队",
            "description": "专业的内容创作团队，擅长撰写各类文章、博客和营销内容",
            "objective": "创建高质量、引人入胜的内容，提升品牌影响力和用户参与度",
            "domain": "creative",
            "complexity": "intermediate",
            "team_members": [
                {
                    "name": "内容策略师",
                    "role": "策略规划",
                    "description": "负责内容策略制定和主题规划",
                    "system_prompt": "你是一位经验丰富的内容策略师，擅长分析目标受众需求，制定内容策略和主题规划。"
                },
                {
                    "name": "创意写手",
                    "role": "内容创作",
                    "description": "负责创作引人入胜的内容",
                    "system_prompt": "你是一位富有创意的写手，擅长创作各种类型的内容，包括文章、博客、营销文案等。"
                },
                {
                    "name": "编辑专家",
                    "role": "内容编辑",
                    "description": "负责内容审核和优化",
                    "system_prompt": "你是一位专业的编辑，擅长内容审核、语言优化和质量控制。"
                }
            ],
            "workflow": {
                "steps": [
                    {
                        "name": "需求分析",
                        "description": "分析内容需求和目标受众",
                        "assignee": "内容策略师",
                        "inputs": ["用户需求", "目标受众"],
                        "outputs": ["内容策略", "主题大纲"]
                    },
                    {
                        "name": "内容创作",
                        "description": "根据策略创作内容",
                        "assignee": "创意写手",
                        "inputs": ["内容策略", "主题大纲"],
                        "outputs": ["初稿内容"]
                    },
                    {
                        "name": "内容优化",
                        "description": "编辑和优化内容质量",
                        "assignee": "编辑专家",
                        "inputs": ["初稿内容"],
                        "outputs": ["最终内容"]
                    }
                ]
            },
            "configuration": {
                "execution_mode": "sequential",
                "timeout_per_step": 300,
                "max_iterations": 3,
                "error_handling": "graceful_degradation"
            }
        }
        
        # Extract team members for quick access
        team_members = team_plan["team_members"]
        
        # Create agent
        agent_data = {
            "agent_id": agent_id,
            "team_name": "内容创作团队",
            "description": "专业的内容创作团队，擅长撰写各类文章、博客和营销内容，提供从策略规划到内容发布的全流程服务",
            "agent_type": AgentType.TEAM,
            "status": AgentStatus.ACTIVE,
            "user_id": user_id,
            "prompt_template": "创建关于{topic}的{content_type}，目标受众是{audience}，风格要求{style}，字数约{word_count}字。",
            "system_prompt": "你是一个专业的内容创作团队，由策略师、写手和编辑组成，能够创作高质量的内容。",
            "team_plan": json.dumps(team_plan),
            "team_members": json.dumps(team_members),
            "usage_count": 0,
            "created_at": datetime.now()
        }
        
        # Insert agent using SQL
        await session.execute(
            text("""
                INSERT INTO agents (agent_id, team_name, description, agent_type, status, user_id, 
                                  prompt_template, system_prompt, team_plan, team_members, usage_count, created_at)
                VALUES (:agent_id, :team_name, :description, :agent_type, :status, :user_id,
                        :prompt_template, :system_prompt, :team_plan, :team_members, :usage_count, :created_at)
            """),
            agent_data
        )
        await session.commit()
        
        print(f"✅ Created test agent: {agent_id} - {agent_data['team_name']}")
        return agent_id


async def main():
    """Main test function."""
    print("🧪 Testing 'Save Agent as Template' functionality fix...")
    
    try:
        # Get test user
        user = await get_test_user()
        
        # Create test agent
        agent_id = await create_test_agent(user.id)
        
        print(f"\n✅ Test setup complete!")
        print(f"📧 Test user email: {user.email}")
        print(f"🔑 Test user password: test123")
        print(f"🤖 Test agent ID: {agent_id}")
        print(f"\n🌐 You can now test the fix by:")
        print(f"1. Opening http://localhost:3001 in your browser")
        print(f"2. Logging in with the test user credentials")
        print(f"3. Going to the agents page")
        print(f"4. Finding the test agent and clicking '保存为模板'")
        print(f"5. Verifying that the template form is pre-populated with agent data")
        
    except Exception as e:
        print(f"❌ Error during test setup: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
