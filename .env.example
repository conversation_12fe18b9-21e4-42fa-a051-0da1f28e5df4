# 环境配置示例文件
# 复制此文件为 .env 并填入实际值

# 开发环境设置
NODE_ENV=development
ENVIRONMENT=development

# API URLs
NEXT_PUBLIC_API_URL=http://localhost:8000
BACKEND_URL=http://localhost:8000

# 数据库配置
DATABASE_URL=sqlite:///./app.db
# DATABASE_URL=postgresql://postgres:postgres@localhost:5432/meta_agent

# AI API Keys (必需)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Redis配置 (可选)
REDIS_URL=redis://localhost:6379

# 日志级别
LOG_LEVEL=INFO

# 安全设置
SECRET_KEY=your_secret_key_here
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Agent配置
MAX_CONCURRENT_AGENTS=5
AGENT_TIMEOUT_SECONDS=30
MAX_AGENT_RESPONSE_LENGTH=10000

# 开发工具
ENABLE_DEBUG=true
ENABLE_DOCS=true
