#!/usr/bin/env python3
"""
简化的JSON字段解析测试
测试parse_template_json_fields函数的功能
"""

import json

def parse_template_json_fields(template_dict: dict) -> dict:
    """Parse JSON fields in template dictionary to ensure proper types and formats."""

    # Parse team_structure_template if it's a string
    if isinstance(template_dict.get("team_structure_template"), str):
        try:
            template_dict["team_structure_template"] = json.loads(template_dict["team_structure_template"])
        except (json.JSONDecodeError, TypeError):
            template_dict["team_structure_template"] = {}
    elif template_dict.get("team_structure_template") is None:
        template_dict["team_structure_template"] = {}

    # Parse default_config if it's a string
    if isinstance(template_dict.get("default_config"), str):
        try:
            template_dict["default_config"] = json.loads(template_dict["default_config"])
        except (json.<PERSON>odeError, TypeError):
            template_dict["default_config"] = {}
    elif template_dict.get("default_config") is None:
        template_dict["default_config"] = {}

    # Parse template_metadata if it's a string
    if isinstance(template_dict.get("template_metadata"), str):
        try:
            template_dict["template_metadata"] = json.loads(template_dict["template_metadata"])
        except (json.JSONDecodeError, TypeError):
            template_dict["template_metadata"] = {}
    elif template_dict.get("template_metadata") is None:
        template_dict["template_metadata"] = {}

    # Parse tags if it's a string
    if isinstance(template_dict.get("tags"), str):
        try:
            template_dict["tags"] = json.loads(template_dict["tags"])
        except (json.JSONDecodeError, TypeError):
            template_dict["tags"] = []
    elif template_dict.get("tags") is None:
        template_dict["tags"] = []

    # Parse keywords if it's a string
    if isinstance(template_dict.get("keywords"), str):
        try:
            template_dict["keywords"] = json.loads(template_dict["keywords"])
        except (json.JSONDecodeError, TypeError):
            template_dict["keywords"] = []
    elif template_dict.get("keywords") is None:
        template_dict["keywords"] = []

    # Convert enum values to lowercase for Pydantic compatibility
    enum_fields = ["category", "difficulty", "visibility", "status"]
    for field in enum_fields:
        if template_dict.get(field) and isinstance(template_dict[field], str):
            template_dict[field] = template_dict[field].lower()

    return template_dict

def test_json_field_parsing():
    """测试JSON字段解析函数"""
    print("🧪 测试JSON字段解析函数...")

    # 模拟从数据库获取的数据（JSON字段被序列化为字符串，枚举字段为大写）
    template_dict = {
        "id": 1,
        "template_id": "test_template_123",
        "name": "测试模板",
        "description": "这是一个测试模板",
        "category": "BUSINESS",  # 大写枚举值
        "difficulty": "INTERMEDIATE",  # 大写枚举值
        "visibility": "PRIVATE",  # 大写枚举值
        "status": "ACTIVE",  # 大写枚举值
        "prompt_template": "这是一个测试提示词模板",
        "team_structure_template": '{"team_name": "测试团队", "description": "测试描述", "team_members": [{"name": "成员1", "role": "角色1"}]}',
        "default_config": '{"model": "gpt-4", "temperature": 0.7, "max_tokens": 2000}',
        "template_metadata": '{"created_from_agent": true, "source_agent_name": "原始Agent"}',
        "tags": '["测试", "模板", "AI"]',
        "keywords": '["test", "template", "ai", "automation"]',
        "usage_count": 0,
        "rating_count": 0,
        "user_id": 1,
        "author_name": "测试用户",
        "use_case": "测试用例",
        "created_at": "2025-01-08T05:00:00",
        "updated_at": None
    }
    
    print("修复前的数据类型:")
    for key, value in template_dict.items():
        if key in ["team_structure_template", "default_config", "template_metadata", "tags", "keywords", "category", "difficulty", "visibility", "status"]:
            print(f"  {key}: {type(value).__name__} = {value}")

    # 应用JSON字段解析
    parsed_dict = parse_template_json_fields(template_dict.copy())

    print("\n修复后的数据类型:")
    for key, value in parsed_dict.items():
        if key in ["team_structure_template", "default_config", "template_metadata", "tags", "keywords", "category", "difficulty", "visibility", "status"]:
            print(f"  {key}: {type(value).__name__} = {value}")

    # 验证JSON字段解析结果
    assert isinstance(parsed_dict["team_structure_template"], dict), "team_structure_template应该是字典类型"
    assert isinstance(parsed_dict["default_config"], dict), "default_config应该是字典类型"
    assert isinstance(parsed_dict["template_metadata"], dict), "template_metadata应该是字典类型"
    assert isinstance(parsed_dict["tags"], list), "tags应该是列表类型"
    assert isinstance(parsed_dict["keywords"], list), "keywords应该是列表类型"

    # 验证枚举字段转换结果
    assert parsed_dict["category"] == "business", "category应该转换为小写"
    assert parsed_dict["difficulty"] == "intermediate", "difficulty应该转换为小写"
    assert parsed_dict["visibility"] == "private", "visibility应该转换为小写"
    assert parsed_dict["status"] == "active", "status应该转换为小写"

    # 验证解析内容
    assert parsed_dict["team_structure_template"]["team_name"] == "测试团队", "team_structure_template内容解析错误"
    assert parsed_dict["default_config"]["model"] == "gpt-4", "default_config内容解析错误"
    assert parsed_dict["template_metadata"]["created_from_agent"] == True, "template_metadata内容解析错误"
    assert "测试" in parsed_dict["tags"], "tags内容解析错误"
    assert "test" in parsed_dict["keywords"], "keywords内容解析错误"
    
    print("✅ JSON字段解析测试通过!")
    return True

def test_edge_cases():
    """测试边界情况"""
    print("\n🧪 测试边界情况...")
    
    # 测试None值
    template_dict = {
        "team_structure_template": None,
        "default_config": None,
        "template_metadata": None,
        "tags": None,
        "keywords": None,
    }
    
    parsed_dict = parse_template_json_fields(template_dict.copy())
    
    assert parsed_dict["team_structure_template"] == {}, "None值应该转换为空字典"
    assert parsed_dict["default_config"] == {}, "None值应该转换为空字典"
    assert parsed_dict["template_metadata"] == {}, "None值应该转换为空字典"
    assert parsed_dict["tags"] == [], "None值应该转换为空列表"
    assert parsed_dict["keywords"] == [], "None值应该转换为空列表"
    
    print("✅ None值处理测试通过!")
    
    # 测试无效JSON
    template_dict = {
        "team_structure_template": "invalid json {",
        "default_config": "not json at all",
        "template_metadata": "{broken: json}",
        "tags": "[invalid, json",
        "keywords": "not a list",
    }
    
    parsed_dict = parse_template_json_fields(template_dict.copy())
    
    assert parsed_dict["team_structure_template"] == {}, "无效JSON应该转换为空字典"
    assert parsed_dict["default_config"] == {}, "无效JSON应该转换为空字典"
    assert parsed_dict["template_metadata"] == {}, "无效JSON应该转换为空字典"
    assert parsed_dict["tags"] == [], "无效JSON应该转换为空列表"
    assert parsed_dict["keywords"] == [], "无效JSON应该转换为空列表"
    
    print("✅ 无效JSON处理测试通过!")
    
    # 测试已经是正确类型的数据
    template_dict = {
        "team_structure_template": {"already": "dict", "team_name": "现有团队"},
        "default_config": {"already": "dict", "model": "gpt-3.5"},
        "template_metadata": {"already": "dict", "version": "1.0"},
        "tags": ["already", "list"],
        "keywords": ["already", "list"],
    }
    
    parsed_dict = parse_template_json_fields(template_dict.copy())
    
    assert parsed_dict["team_structure_template"] == {"already": "dict", "team_name": "现有团队"}, "已经是字典的数据应该保持不变"
    assert parsed_dict["tags"] == ["already", "list"], "已经是列表的数据应该保持不变"
    
    print("✅ 正确类型数据处理测试通过!")

def main():
    """主测试函数"""
    print("🚀 开始测试JSON字段解析修复...")
    print("=" * 60)
    
    try:
        # 测试JSON字段解析
        success = test_json_field_parsing()
        
        # 测试边界情况
        test_edge_cases()
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 所有测试通过! JSON字段解析修复成功!")
            print("\n修复内容总结:")
            print("1. ✅ 创建了通用的JSON字段解析函数")
            print("2. ✅ 正确处理字符串形式的JSON字段")
            print("3. ✅ 处理None值和无效JSON的边界情况")
            print("4. ✅ 保持已经是正确类型的数据不变")
            print("5. ✅ 确保所有字段都有合适的默认值")
            
            print("\n这个修复解决了以下问题:")
            print("- ❌ 'Input should be a valid dictionary' 错误")
            print("- ❌ JSON字段被序列化为字符串导致的类型错误")
            print("- ❌ TemplateResponse创建失败")
            
            print("\n现在用户应该能够:")
            print("- ✅ 成功从Agent创建模板")
            print("- ✅ 获取模板详情时数据类型正确")
            print("- ✅ 所有JSON字段正确解析和显示")
        else:
            print("❌ 测试失败，需要进一步调试")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
