#!/usr/bin/env python3
"""
测试双重序列化修复
验证从Agent创建模板时不会发生双重JSON序列化问题
"""

import json

def test_double_serialization_scenarios():
    """测试各种双重序列化场景"""
    print("🧪 测试双重序列化修复...")
    
    # 模拟从数据库获取的Agent数据的不同情况
    test_scenarios = [
        {
            "name": "team_plan已经是字典对象",
            "description": "正常情况，team_plan是字典类型",
            "agent_data": {
                "team_plan": {
                    "team_name": "侦探组合",
                    "description": "Zen和Rizzo组成的侦探组合",
                    "team_members": [
                        {"name": "Zen", "role": "哲学家侦探"},
                        {"name": "<PERSON><PERSON><PERSON>", "role": "街头侦探"}
                    ]
                }
            }
        },
        {
            "name": "team_plan是JSON字符串",
            "description": "从数据库获取时被序列化为字符串",
            "agent_data": {
                "team_plan": '{"team_name": "侦探组合", "description": "Zen和Rizzo组成的侦探组合", "team_members": [{"name": "Zen", "role": "哲学家侦探"}, {"name": "Rizzo", "role": "街头侦探"}]}'
            }
        },
        {
            "name": "team_plan是包含Unicode转义的JSON字符串",
            "description": "包含Unicode转义序列的JSON字符串（实际错误情况）",
            "agent_data": {
                "team_plan": '{"team_name": "\\u4fa6\\u63a2\\u7ec4\\u5408", "description": "Zen\\u548cRizzo\\u7ec4\\u6210\\u7684\\u4fa6\\u63a2\\u7ec4\\u5408", "team_members": [{"name": "Zen", "role": "\\u54f2\\u5b66\\u5bb6\\u4fa6\\u63a2"}, {"name": "Rizzo", "role": "\\u8857\\u5934\\u4fa6\\u63a2"}]}'
            }
        },
        {
            "name": "team_plan是None",
            "description": "team_plan为None的边界情况",
            "agent_data": {
                "team_plan": None
            }
        },
        {
            "name": "team_plan是无效JSON字符串",
            "description": "损坏的JSON字符串",
            "agent_data": {
                "team_plan": '{"team_name": "invalid json'
            }
        }
    ]
    
    def process_team_plan(agent_dict):
        """模拟修复后的team_plan处理逻辑"""
        team_plan = agent_dict.get("team_plan", {})
        if isinstance(team_plan, str):
            try:
                team_plan = json.loads(team_plan)
            except (json.JSONDecodeError, TypeError):
                team_plan = {}
        elif team_plan is None:
            team_plan = {}
        return team_plan
    
    all_passed = True
    
    for scenario in test_scenarios:
        print(f"\n📋 测试场景: {scenario['name']}")
        print(f"   描述: {scenario['description']}")
        
        try:
            # 处理team_plan
            processed_team_plan = process_team_plan(scenario['agent_data'])
            
            # 验证结果
            if isinstance(processed_team_plan, dict):
                print(f"   ✅ team_plan正确处理为字典类型")
                
                # 如果有team_name，验证Unicode是否正确解码
                if 'team_name' in processed_team_plan:
                    team_name = processed_team_plan['team_name']
                    if '\\u' not in str(team_name):
                        print(f"   ✅ Unicode内容正确解码: {team_name}")
                    else:
                        print(f"   ❌ Unicode内容未正确解码: {team_name}")
                        all_passed = False
                
                # 验证team_members结构
                if 'team_members' in processed_team_plan:
                    team_members = processed_team_plan['team_members']
                    if isinstance(team_members, list):
                        print(f"   ✅ team_members正确解析为列表，包含 {len(team_members)} 个成员")
                    else:
                        print(f"   ❌ team_members未正确解析为列表")
                        all_passed = False
                
            else:
                print(f"   ❌ team_plan处理失败，类型为 {type(processed_team_plan)}")
                all_passed = False
                
        except Exception as e:
            print(f"   ❌ 处理过程中发生错误: {e}")
            all_passed = False
    
    return all_passed

def test_template_creation_simulation():
    """模拟完整的模板创建过程"""
    print("\n🧪 模拟完整的模板创建过程...")
    
    # 模拟从数据库获取的Agent数据（包含双重序列化的team_plan）
    agent_data = {
        "agent_id": "agent_test_123",
        "team_name": "侦探组合",
        "description": "专业的侦探团队",
        "prompt_template": "你是一个专业的侦探团队...",
        # 这是实际从数据库获取的双重序列化数据
        "team_plan": '{"team_name": "\\u4fa6\\u63a2\\u7ec4\\u5408", "description": "Zen\\u548cRizzo\\u7ec4\\u6210\\u7684\\u4fa6\\u63a2\\u7ec4\\u5408", "objective": "\\u901a\\u8fc7\\u5bf9\\u8bdd\\u542f\\u53d1\\u548c\\u63a8\\u7406\\uff0c\\u6700\\u7ec8\\u627e\\u5230\\u6848\\u4ef6\\u7684\\u89e3\\u51b3\\u65b9\\u6848", "team_members": [{"name": "Zen", "role": "\\u54f2\\u5b66\\u5bb6\\u4fa6\\u63a2", "description": "Zen\\u662f\\u4e00\\u4f4d\\u5bcc\\u6709\\u54f2\\u7406\\u7684\\u50e7\\u4fa3"}, {"name": "Rizzo", "role": "\\u8857\\u5934\\u4fa6\\u63a2", "description": "Rizzo\\u662f\\u4e00\\u4f4d\\u7ecf\\u9a8c\\u4e30\\u5bcc\\u7684\\u8857\\u5934\\u8001\\u5175"}]}'
    }
    
    # 模拟模板请求数据
    template_request = {
        "name": "侦探组合模板",
        "description": "基于Zen和Rizzo的侦探团队模板",
        "category": "investigation",
        "difficulty": "intermediate",
        "visibility": "private"
    }
    
    try:
        # 步骤1: 处理team_plan（修复后的逻辑）
        team_plan = agent_data.get("team_plan", {})
        if isinstance(team_plan, str):
            try:
                team_plan = json.loads(team_plan)
            except (json.JSONDecodeError, TypeError):
                team_plan = {}
        elif team_plan is None:
            team_plan = {}
        
        print(f"   ✅ team_plan处理成功，类型: {type(team_plan)}")
        
        # 步骤2: 验证解析结果
        if isinstance(team_plan, dict):
            print(f"   ✅ team_plan是字典类型")
            
            if 'team_name' in team_plan:
                print(f"   ✅ 团队名称: {team_plan['team_name']}")
            
            if 'team_members' in team_plan and isinstance(team_plan['team_members'], list):
                print(f"   ✅ 团队成员: {len(team_plan['team_members'])} 个成员")
                for i, member in enumerate(team_plan['team_members']):
                    if isinstance(member, dict) and 'name' in member and 'role' in member:
                        print(f"     - {member['name']}: {member['role']}")
        
        # 步骤3: 模拟创建Template对象
        template_data = {
            "template_id": "template_test_123",
            "name": template_request["name"],
            "description": template_request["description"],
            "category": template_request["category"],
            "difficulty": template_request["difficulty"],
            "visibility": template_request["visibility"],
            "prompt_template": agent_data.get("prompt_template", ""),
            "team_structure_template": team_plan,  # 使用处理后的team_plan
            "default_config": {},
            "tags": [],
            "keywords": [],
        }
        
        print(f"   ✅ 模板数据创建成功")
        print(f"   - 模板名称: {template_data['name']}")
        print(f"   - team_structure_template类型: {type(template_data['team_structure_template'])}")
        
        # 步骤4: 验证不会发生双重序列化
        if isinstance(template_data['team_structure_template'], dict):
            print(f"   ✅ 避免了双重序列化问题")
            return True
        else:
            print(f"   ❌ 仍然存在序列化问题")
            return False
            
    except Exception as e:
        print(f"   ❌ 模拟过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试双重序列化修复...")
    print("=" * 70)
    
    # 测试各种场景
    scenarios_passed = test_double_serialization_scenarios()
    
    # 测试完整流程
    simulation_passed = test_template_creation_simulation()
    
    print("\n" + "=" * 70)
    print("📊 测试结果总结:")
    print(f"- 双重序列化场景测试: {'✅ 通过' if scenarios_passed else '❌ 失败'}")
    print(f"- 模板创建流程模拟: {'✅ 通过' if simulation_passed else '❌ 失败'}")
    
    if scenarios_passed and simulation_passed:
        print("\n🎉 双重序列化修复成功！")
        print("\n✅ 修复内容确认:")
        print("1. 正确处理从数据库获取的JSON字符串")
        print("2. 避免将JSON字符串再次序列化")
        print("3. 正确解析Unicode转义序列")
        print("4. 处理各种边界情况（None值、无效JSON）")
        print("5. 确保team_structure_template始终是字典类型")
        
        print("\n🎯 现在用户应该能够:")
        print("- 从Agent创建模板时不再出现序列化错误")
        print("- 正确显示包含中文字符的团队信息")
        print("- 获取模板详情时看到正确的团队结构")
        print("- 使用包含复杂团队配置的模板")
        
        return True
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
