# Theme System Documentation

## Overview

The Meta-Agent frontend implements a comprehensive theme switching system that supports light, dark, and system preference themes. The implementation is built on top of Tailwind CSS with CSS custom properties and provides seamless theme transitions across all UI components.

## Features

- ✅ **Light/Dark/System Themes**: Support for three theme modes
- ✅ **Persistent Storage**: Theme preference is saved using Zustand with localStorage
- ✅ **System Preference Detection**: Automatically detects and follows system theme changes
- ✅ **Smooth Transitions**: Optional transition disabling to prevent flash during theme changes
- ✅ **Accessibility**: Full keyboard navigation and screen reader support
- ✅ **Component Integration**: All shadcn/ui components automatically support theme switching
- ✅ **Default Dark Theme**: Application defaults to dark theme as requested

## Architecture

### Theme Provider (`/src/components/providers/theme-provider.tsx`)

The `ThemeProvider` component manages the global theme state and applies theme classes to the document:

```typescript
<ThemeProvider
  defaultTheme="dark"
  enableSystem
  disableTransitionOnChange
>
  {children}
</ThemeProvider>
```

**Props:**
- `defaultTheme`: Initial theme ("light" | "dark" | "system")
- `enableSystem`: Enable system preference detection
- `disableTransitionOnChange`: Prevent flash during theme changes
- `attribute`: HTML attribute to use for theme ("class" by default)
- `storageKey`: localStorage key for persistence

### Theme Store Integration

The theme state is managed by Zustand store (`/src/lib/store.ts`):

```typescript
interface AppStore {
  theme: "light" | "dark" | "system";
  setTheme: (theme: "light" | "dark" | "system") => void;
}
```

### Theme Toggle Components (`/src/components/ui/theme-toggle.tsx`)

Two theme toggle components are available:

1. **ThemeToggle**: Dropdown menu with all three options
2. **SimpleThemeToggle**: Simple button that toggles between light/dark

## Usage

### Basic Setup

1. **Root Layout Integration** (`/src/app/layout.tsx`):
```tsx
import { ThemeProvider } from "@/components/providers/theme-provider";

export default function RootLayout({ children }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body>
        <ThemeProvider defaultTheme="dark" enableSystem>
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
```

2. **Using Theme Hook**:
```tsx
import { useTheme } from "@/components/providers/theme-provider";

function MyComponent() {
  const { theme, setTheme, resolvedTheme, systemTheme } = useTheme();
  
  return (
    <div>
      <p>Current theme: {theme}</p>
      <p>Resolved theme: {resolvedTheme}</p>
      <button onClick={() => setTheme("dark")}>
        Switch to Dark
      </button>
    </div>
  );
}
```

3. **Adding Theme Toggle**:
```tsx
import { ThemeToggle } from "@/components/ui/theme-toggle";

function Header() {
  return (
    <header>
      <ThemeToggle />
    </header>
  );
}
```

### CSS Variables

The theme system uses CSS custom properties defined in `/src/app/globals.css`:

**Light Theme Variables:**
```css
:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  /* ... more variables */
}
```

**Dark Theme Variables:**
```css
.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  /* ... more variables */
}
```

### Component Styling

All UI components automatically support themes through Tailwind CSS classes:

```tsx
// Components use semantic color classes
<div className="bg-background text-foreground">
  <button className="bg-primary text-primary-foreground">
    Button
  </button>
</div>

// Dark mode specific styles
<input className="bg-transparent dark:bg-input/30" />
```

## Testing

A comprehensive test page is available at `/theme-test` that demonstrates:

- Theme information display
- Theme toggle components
- All UI component variants in current theme
- Form elements and interactive components
- Accessibility features

## Accessibility

The theme system includes comprehensive accessibility features:

- **Keyboard Navigation**: Full keyboard support for theme toggle
- **Screen Reader Support**: Proper ARIA labels and descriptions
- **High Contrast**: Carefully chosen color combinations for readability
- **Focus Indicators**: Visible focus states in all themes
- **Reduced Motion**: Respects user's motion preferences

## Browser Support

- **Modern Browsers**: Full support in Chrome, Firefox, Safari, Edge
- **CSS Custom Properties**: Required for theme switching
- **Local Storage**: Used for theme persistence
- **Media Queries**: For system preference detection

## Performance

- **Minimal Bundle Size**: Lightweight implementation
- **No Flash**: Proper hydration handling prevents theme flash
- **Efficient Updates**: Only necessary DOM updates during theme changes
- **Cached Preferences**: Theme preference cached in localStorage

## Troubleshooting

### Common Issues

1. **Theme Flash on Load**: Ensure `suppressHydrationWarning` is set on `<html>` tag
2. **Components Not Updating**: Verify components use semantic Tailwind classes
3. **System Theme Not Working**: Check browser support for `prefers-color-scheme`
4. **Persistence Issues**: Verify localStorage is available and not blocked

### Debug Mode

Enable debug mode by checking theme state:

```tsx
const { theme, resolvedTheme, systemTheme } = useTheme();
console.log({ theme, resolvedTheme, systemTheme });
```

## Future Enhancements

Potential improvements for the theme system:

- [ ] Custom theme creation interface
- [ ] Theme scheduling (automatic switching based on time)
- [ ] High contrast mode support
- [ ] Theme preview without applying
- [ ] Export/import theme configurations
- [ ] Theme-specific component variants
