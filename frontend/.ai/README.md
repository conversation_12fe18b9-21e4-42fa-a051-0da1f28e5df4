# Meta-Agent Frontend

This is the frontend application for Meta-Agent, an AI-powered agent generation and management platform built with Next.js.

## Features

- **Agent Management**: Create, edit, and manage AI agents through an intuitive interface
- **Template System**: Use pre-built templates for common agent types
- **API Integration**: Seamless integration with the Meta-Agent backend API
- **Responsive Design**: Works on desktop and mobile devices

## Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm
- Meta-Agent backend running on port 8000

### Installation

1. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

2. Set up environment variables:
```bash
cp .env.example .env.local
```

Edit `.env.local` and configure:
```
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_MOCK_API=false
```

3. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) with your browser.

## Project Structure

```
frontend/
├── src/
│   ├── app/                 # Next.js app router pages
│   ├── components/          # Reusable UI components
│   │   ├── ui/             # Base UI components
│   │   ├── common/         # Common components
│   │   └── features/       # Feature-specific components
│   ├── lib/                # Utilities and configurations
│   │   ├── api.ts          # API client
│   │   ├── store.ts        # State management
│   │   └── types.ts        # TypeScript types
│   └── styles/             # Global styles
├── public/                 # Static assets
└── docs/                   # Documentation
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## Technology Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Radix UI + shadcn/ui
- **State Management**: Zustand
- **HTTP Client**: Axios
- **Form Handling**: React Hook Form + Zod
- **Icons**: Lucide React

## API Integration

The frontend communicates with the Meta-Agent backend through a RESTful API. Key endpoints include:

- `/api/v1/agents` - Agent management
- `/api/v1/planning` - AI planning and generation
- `/api/v1/settings` - Configuration management

## Development Guidelines

### Code Style

- Use TypeScript for all new code
- Follow the existing component structure
- Use Tailwind CSS for styling
- Implement proper error handling
- Add loading states for async operations

### Component Organization

- Place reusable UI components in `components/ui/`
- Feature-specific components go in `components/features/`
- Common utilities in `components/common/`
- Keep components small and focused

## Learn More

To learn more about the technologies used:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
