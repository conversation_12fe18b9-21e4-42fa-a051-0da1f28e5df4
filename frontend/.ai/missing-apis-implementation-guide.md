# Missing APIs Implementation Guide

## 🎯 Priority 1: Agent Favorites System

### **Database Schema Changes**
```sql
-- Add to existing database
CREATE TABLE user_agent_favorites (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(user_id),
    agent_id UUID NOT NULL REFERENCES agents(agent_id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, agent_id)
);

CREATE INDEX idx_user_agent_favorites_user_id ON user_agent_favorites(user_id);
CREATE INDEX idx_user_agent_favorites_agent_id ON user_agent_favorites(agent_id);
```

### **Backend Implementation**
```python
# backend/app/api/v1/agents.py

@router.post("/agents/{agent_id}/favorite")
async def toggle_agent_favorite(
    agent_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Toggle favorite status for an agent"""
    try:
        # Check if agent exists and user has access
        agent = db.query(Agent).filter(
            Agent.agent_id == agent_id,
            Agent.user_id == current_user.user_id
        ).first()
        
        if not agent:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        # Check if already favorited
        favorite = db.query(UserAgentFavorite).filter(
            UserAgentFavorite.user_id == current_user.user_id,
            UserAgentFavorite.agent_id == agent_id
        ).first()
        
        if favorite:
            # Remove favorite
            db.delete(favorite)
            is_favorite = False
        else:
            # Add favorite
            favorite = UserAgentFavorite(
                user_id=current_user.user_id,
                agent_id=agent_id
            )
            db.add(favorite)
            is_favorite = True
        
        db.commit()
        
        return {
            "success": True,
            "is_favorite": is_favorite,
            "message": "Favorite status updated"
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/agents/favorites")
async def get_favorite_agents(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get user's favorite agents with performance metrics"""
    try:
        # Get favorite agent IDs
        favorite_agent_ids = db.query(UserAgentFavorite.agent_id).filter(
            UserAgentFavorite.user_id == current_user.user_id
        ).subquery()
        
        # Get agents with metrics
        agents = db.query(Agent).filter(
            Agent.agent_id.in_(favorite_agent_ids),
            Agent.user_id == current_user.user_id
        ).all()
        
        # Get performance metrics for each agent
        result = []
        for agent in agents:
            metrics = get_agent_performance_metrics(db, agent.agent_id)
            result.append({
                "agent_id": agent.agent_id,
                "name": agent.name,
                "description": agent.description,
                "status": agent.status,
                "created_at": agent.created_at,
                "updated_at": agent.updated_at,
                "performance": metrics
            })
        
        return {
            "success": True,
            "data": result,
            "count": len(result)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

## 🎯 Priority 2: Dashboard Unified Data Endpoint

### **Backend Implementation**
```python
# backend/app/api/v1/dashboard.py

@router.get("/dashboard/data")
async def get_dashboard_data(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Single endpoint for all dashboard data"""
    try:
        # Parallel data fetching for performance
        import asyncio
        
        async def fetch_quick_stats():
            return await get_dashboard_quick_stats(db, current_user)
        
        async def fetch_system_health():
            return await get_system_health_status(db)
        
        async def fetch_recent_activity():
            return await get_recent_activity_feed(db, current_user, limit=10)
        
        async def fetch_active_agents():
            return await get_active_agents_overview(db, current_user)
        
        async def fetch_featured_templates():
            return await get_featured_templates(db, limit=6)
        
        # Execute all fetches concurrently
        results = await asyncio.gather(
            fetch_quick_stats(),
            fetch_system_health(),
            fetch_recent_activity(),
            fetch_active_agents(),
            fetch_featured_templates(),
            return_exceptions=True
        )
        
        quick_stats, system_health, recent_activity, active_agents, featured_templates = results
        
        return {
            "success": True,
            "data": {
                "quick_stats": quick_stats if not isinstance(quick_stats, Exception) else None,
                "system_health": system_health if not isinstance(system_health, Exception) else None,
                "recent_activity": recent_activity if not isinstance(recent_activity, Exception) else [],
                "active_agents": active_agents if not isinstance(active_agents, Exception) else [],
                "featured_templates": featured_templates if not isinstance(featured_templates, Exception) else []
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Dashboard data fetch failed: {str(e)}")

async def get_dashboard_quick_stats(db: Session, user: User):
    """Get optimized quick statistics for dashboard"""
    # Get user's agent count
    total_agents = db.query(Agent).filter(Agent.user_id == user.user_id).count()
    active_agents = db.query(Agent).filter(
        Agent.user_id == user.user_id,
        Agent.status == 'active'
    ).count()
    
    # Get execution statistics
    total_executions = db.query(TestHistory).filter(
        TestHistory.user_id == user.user_id
    ).count()
    
    successful_executions = db.query(TestHistory).filter(
        TestHistory.user_id == user.user_id,
        TestHistory.status == 'completed'
    ).count()
    
    success_rate = (successful_executions / total_executions * 100) if total_executions > 0 else 0
    
    # Get recent activity count (last 24 hours)
    yesterday = datetime.utcnow() - timedelta(days=1)
    recent_activity = db.query(TestHistory).filter(
        TestHistory.user_id == user.user_id,
        TestHistory.started_at >= yesterday
    ).count()
    
    return {
        "total_agents": total_agents,
        "active_agents": active_agents,
        "total_executions": total_executions,
        "successful_executions": successful_executions,
        "success_rate": round(success_rate, 1),
        "recent_activity": recent_activity,
        "trends": {
            "agents": "stable",  # TODO: Calculate actual trends
            "executions": "up",
            "success_rate": "stable",
            "activity": "up"
        }
    }
```

## 🎯 Priority 3: Trend Analysis API

### **Backend Implementation**
```python
@router.get("/dashboard/trends")
async def get_dashboard_trends(
    period: str = Query("7d", regex="^(1d|7d|30d)$"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get trend data for dashboard metrics"""
    try:
        # Calculate date range
        days_map = {"1d": 1, "7d": 7, "30d": 30}
        days = days_map[period]
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # Get daily execution counts
        daily_executions = db.query(
            func.date(TestHistory.started_at).label('date'),
            func.count(TestHistory.test_id).label('count'),
            func.sum(case([(TestHistory.status == 'completed', 1)], else_=0)).label('successful')
        ).filter(
            TestHistory.user_id == current_user.user_id,
            TestHistory.started_at >= start_date
        ).group_by(func.date(TestHistory.started_at)).all()
        
        # Format trend data
        trend_data = []
        for execution in daily_executions:
            success_rate = (execution.successful / execution.count * 100) if execution.count > 0 else 0
            trend_data.append({
                "date": execution.date.isoformat(),
                "executions": execution.count,
                "successful": execution.successful,
                "success_rate": round(success_rate, 1)
            })
        
        return {
            "success": True,
            "data": {
                "period": period,
                "trends": trend_data
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

## 🔧 Frontend Integration Updates

### **Update Dashboard Data Hook**
```typescript
// frontend/src/lib/dashboard-data.ts

export const useDashboardData = () => {
  return useQuery({
    queryKey: ['dashboard-data'],
    queryFn: async () => {
      const response = await api.dashboard.getData();
      return response.data;
    },
    staleTime: 30000, // 30 seconds
    cacheTime: 300000, // 5 minutes
  });
};

export const useAgentFavorites = () => {
  const queryClient = useQueryClient();
  
  const toggleFavorite = useMutation({
    mutationFn: async (agentId: string) => {
      return await api.agents.toggleFavorite(agentId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries(['dashboard-data']);
      queryClient.invalidateQueries(['agents']);
    },
  });
  
  return { toggleFavorite };
};
```

## 📋 Implementation Checklist

### **Phase 1: Critical APIs (Week 1)**
- [ ] Database schema for user_agent_favorites
- [ ] Agent favorites toggle endpoint
- [ ] Agent favorites list endpoint
- [ ] Dashboard unified data endpoint
- [ ] Frontend integration for favorites
- [ ] Testing and validation

### **Phase 2: Enhanced Features (Week 2)**
- [ ] Trend analysis API
- [ ] Real-time updates (WebSocket)
- [ ] Enhanced error handling
- [ ] Performance optimization
- [ ] Caching implementation

### **Phase 3: Polish (Week 3)**
- [ ] API documentation updates
- [ ] Frontend error boundaries
- [ ] Performance monitoring
- [ ] User feedback integration
- [ ] Analytics and metrics

This implementation guide provides the complete technical specifications needed to fill the API gaps identified in the dashboard audit.
