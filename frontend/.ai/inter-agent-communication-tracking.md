# Inter-Agent Communication Variable Tracking

## 🎯 Overview
Enhanced the variable placeholder tracking system to capture and display inter-agent communication patterns during team execution. This provides complete visibility into how agents share data and communicate with each other.

## ✅ Implemented Features

### **1. Enhanced Variable Data Structure**
Extended the variable placeholder structure to include inter-agent communication fields:

```typescript
interface VariablePlaceholder {
  // Existing fields
  id: string;
  placeholderName: string;
  sourceStep: string;
  sourceAgent: string;
  semanticDescription: string;
  value: string | null;
  resolvedAt: string | null;
  stepIndex: number;
  
  // NEW: Inter-agent communication fields
  communicationType: 'inter-agent' | 'user-input' | 'system' | 'internal';
  destinationAgents: string[];
  dependsOn: string[];
  dataFlowChain: Array<{
    agent: string;
    step: string;
    timestamp: string;
  }>;
  contextDependencies: string[];
  isSharedBetweenAgents: boolean;
}
```

### **2. Inter-Agent Communication Detection**
Added `detectInterAgentCommunication()` function that:
- **Analyzes placeholder names** to identify communication patterns
- **Detects communication types**: user-input, system, inter-agent, internal
- **Maps destination agents** based on workflow step order
- **Identifies dependencies** from progress data
- **Tracks context dependencies** from backend context service

### **3. Enhanced Variable Tracking Logic**
Updated `updateVariablePlaceholder()` function to:
- **Accept inter-agent parameters** for communication type, destinations, dependencies
- **Build data flow chains** showing how data moves between agents
- **Track context dependencies** from workflow steps
- **Maintain agent relationship mappings**

### **4. Improved Variable Detection**
Enhanced progress data processing to:
- **Extract inter-agent patterns** from context_data and variables_resolved
- **Detect communication flows** based on placeholder naming conventions
- **Map workflow dependencies** using step relationships
- **Track variable usage** across multiple agents

### **5. Enhanced Simulation Logic**
Updated simulation to create realistic inter-agent variables:
- **User Input → First Agent**: `{user.requirements}` flows to first workflow agent
- **Planner → Analyst/Executor**: `{planner.task_breakdown}` flows to subsequent agents
- **Analyst → Executor/Reviewer**: `{analyst.analysis_results}` flows downstream
- **Executor → Reviewer**: `{executor.execution_output}` flows to final review
- **Dependency Chains**: Each variable depends on previous agent outputs

## 🎨 Visual Enhancements

### **Execution Tab Improvements**
- **Communication Type Badges**: 🔄 代理间, 👤 用户输入, ⚙️ 系统, 🔧 内部
- **Inter-Agent Details Section**: Shows destination agents, dependencies, data flow
- **Data Flow Visualization**: Agent1 → Agent2 → Agent3 chains
- **Context Dependencies**: Shows workflow step dependencies

### **Results Tab Enhancements**
- **Inter-Agent Statistics**: New summary card showing inter-agent communication count
- **Communication Type Categorization**: Visual badges for each variable type
- **Detailed Inter-Agent Info**: Purple-highlighted sections for communication details
- **Data Flow Chains**: Complete visualization of how data moved through agents

## 🔧 Technical Implementation

### **Communication Type Detection**
```javascript
// User input detection
if (sourceAgent === 'user' || placeholderName.includes('user.')) {
  communicationType = 'user-input';
  destinationAgents = [workflowSteps[0].assignee];
}

// Inter-agent detection
else if (placeholderName.includes('.') && !placeholderName.includes('user.')) {
  communicationType = 'inter-agent';
  // Find subsequent agents in workflow
  destinationAgents = workflowSteps.slice(currentIndex + 1).map(step => step.assignee);
}
```

### **Data Flow Chain Building**
```javascript
dataFlowChain: [{
  agent: sourceAgent,
  step: sourceStep,
  timestamp: new Date().toISOString()
}]
```

### **Dependency Tracking**
```javascript
dependsOn: ["{user.requirements}", "{planner.task_breakdown}"]
contextDependencies: ["user_input", "planning"]
```

## 🧪 Testing Features

### **Simulated Inter-Agent Variables**
1. **{user.requirements}** → planner (user-input)
2. **{planner.task_breakdown}** → analyst, executor (inter-agent)
3. **{analyst.analysis_results}** → executor, reviewer (inter-agent)
4. **{executor.execution_output}** → reviewer (inter-agent)
5. **{reviewer.review_feedback}** → final output (inter-agent)

### **Communication Flow Example**
```
User Input → Planner → Analyst → Executor → Reviewer
     ↓         ↓        ↓         ↓         ↓
{user.req} → {plan} → {analysis} → {output} → {review}
```

### **Debug Logging**
- `🔍 [INTER-AGENT DEBUG] Detected communication pattern`
- `🔍 [INTER-AGENT DEBUG] Explicit variable with communication pattern`
- Shows communication type, destination agents, dependencies

## 📊 User Benefits

### **Complete Visibility**
- **See Data Flow**: Understand how information moves between agents
- **Track Dependencies**: Know which variables depend on others
- **Identify Bottlenecks**: Spot where communication breaks down
- **Debug Issues**: Understand why variables aren't being passed correctly

### **Better Understanding**
- **Team Dynamics**: See how agents collaborate and share information
- **Workflow Optimization**: Identify unnecessary or missing communication
- **Variable Usage**: Understand which agents use which data
- **Communication Patterns**: Learn effective inter-agent communication strategies

### **Enhanced Debugging**
- **Missing Variables**: Identify when expected inter-agent variables aren't created
- **Communication Failures**: See where data flow breaks between agents
- **Dependency Issues**: Understand variable dependency problems
- **Workflow Problems**: Spot issues in agent collaboration

## 🚀 Future Enhancements

1. **Visual Flow Diagrams**: Interactive diagrams showing data flow between agents
2. **Communication Metrics**: Statistics on inter-agent communication efficiency
3. **Variable Validation**: Check if expected inter-agent variables are present
4. **Communication Optimization**: Suggestions for improving agent communication
5. **Real-time Flow Monitoring**: Live visualization of data flowing between agents
6. **Communication History**: Track how communication patterns change over time

The inter-agent communication tracking system now provides comprehensive visibility into how agents collaborate and share data during team execution, making it much easier to understand, debug, and optimize agent team workflows.
