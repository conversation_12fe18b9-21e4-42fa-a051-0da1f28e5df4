#!/usr/bin/env node

/**
 * <PERSON>ript to run template-related frontend tests with coverage
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

const FRONTEND_DIR = path.resolve(__dirname, '..');

function runCommand(command, args, options = {}) {
  return new Promise((resolve, reject) => {
    console.log(`\n${'='.repeat(60)}`);
    console.log(`Running: ${command} ${args.join(' ')}`);
    console.log(`${'='.repeat(60)}`);

    const child = spawn(command, args, {
      cwd: FRONTEND_DIR,
      stdio: 'inherit',
      shell: true,
      ...options,
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

async function checkDependencies() {
  console.log('Checking dependencies...');
  
  const packageJsonPath = path.join(FRONTEND_DIR, 'package.json');
  if (!fs.existsSync(packageJsonPath)) {
    throw new Error('package.json not found');
  }

  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  const requiredDeps = [
    '@testing-library/react',
    '@testing-library/jest-dom',
    '@testing-library/user-event',
    'jest',
    'jest-environment-jsdom',
  ];

  const missingDeps = requiredDeps.filter(dep => 
    !packageJson.dependencies?.[dep] && !packageJson.devDependencies?.[dep]
  );

  if (missingDeps.length > 0) {
    console.log('Installing missing test dependencies...');
    await runCommand('npm', ['install', '--save-dev', ...missingDeps]);
  }
}

async function runTests() {
  const testCommands = [
    {
      name: 'Template Component Tests',
      command: 'npx',
      args: ['jest', 'src/components/templates/__tests__', '--verbose'],
    },
    {
      name: 'Template Component Tests with Coverage',
      command: 'npx',
      args: [
        'jest',
        'src/components/templates',
        '--coverage',
        '--coverageDirectory=coverage/templates',
        '--coverageReporters=text',
        '--coverageReporters=html',
        '--coverageReporters=lcov',
      ],
    },
    {
      name: 'All Template-related Tests',
      command: 'npx',
      args: [
        'jest',
        '--testNamePattern=template|Template',
        '--coverage',
        '--coverageDirectory=coverage/all-templates',
        '--coverageReporters=text-summary',
      ],
    },
  ];

  const results = [];

  for (const testConfig of testCommands) {
    try {
      console.log(`\nRunning: ${testConfig.name}`);
      await runCommand(testConfig.command, testConfig.args);
      results.push({ name: testConfig.name, success: true });
    } catch (error) {
      console.error(`Failed: ${testConfig.name}`, error.message);
      results.push({ name: testConfig.name, success: false, error: error.message });
    }
  }

  return results;
}

function printSummary(results) {
  console.log(`\n${'='.repeat(60)}`);
  console.log('TEST SUMMARY');
  console.log(`${'='.repeat(60)}`);

  let allPassed = true;
  for (const result of results) {
    const status = result.success ? '✅ PASSED' : '❌ FAILED';
    console.log(`${result.name}: ${status}`);
    if (!result.success) {
      allPassed = false;
      console.log(`  Error: ${result.error}`);
    }
  }

  console.log(`\nOverall Result: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);

  if (allPassed) {
    console.log('\n🎉 Template frontend functionality is working correctly!');
    console.log('📊 Coverage reports generated in coverage/ directory');
    console.log('📁 Open coverage/templates/index.html to view detailed coverage');
  } else {
    console.log('\n💥 Some tests failed. Please check the output above.');
    process.exit(1);
  }
}

async function main() {
  try {
    console.log('Frontend Template Testing Suite');
    console.log(`${'='.repeat(60)}`);
    console.log(`Working directory: ${FRONTEND_DIR}`);

    // Check and install dependencies
    await checkDependencies();

    // Run tests
    const results = await runTests();

    // Print summary
    printSummary(results);

  } catch (error) {
    console.error('Test suite failed:', error.message);
    process.exit(1);
  }
}

// Handle script termination
process.on('SIGINT', () => {
  console.log('\n\nTest suite interrupted by user');
  process.exit(1);
});

process.on('SIGTERM', () => {
  console.log('\n\nTest suite terminated');
  process.exit(1);
});

if (require.main === module) {
  main();
}

module.exports = { runCommand, checkDependencies, runTests, printSummary };
