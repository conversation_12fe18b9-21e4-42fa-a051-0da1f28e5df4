<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重构流程测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #555;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .info {
            color: #17a2b8;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 重构测试记录创建和变量跟踪流程测试</h1>
        
        <div class="test-section">
            <h3>📋 测试配置</h3>
            <label>Agent ID:</label>
            <input type="text" id="agentId" value="agent_test" placeholder="输入要测试的Agent ID">
            
            <label>测试输入:</label>
            <textarea id="testInput" placeholder="输入测试内容">测试重构后的变量跟踪流程，验证后端主导的测试管理系统是否正常工作。</textarea>
        </div>

        <div class="test-section">
            <h3>🚀 步骤1: 测试执行启动</h3>
            <p>测试新的 <code>/api/v1/test-execution/start</code> 端点</p>
            <button onclick="testExecutionStart()">启动测试执行</button>
            <div id="startResult" class="log" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔗 步骤2: WebSocket连接</h3>
            <p>测试变量跟踪的WebSocket连接</p>
            <button onclick="testWebSocketConnection()">连接WebSocket</button>
            <button onclick="disconnectWebSocket()" disabled id="disconnectBtn">断开连接</button>
            <div id="wsResult" class="log" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📊 步骤3: 变量数据更新</h3>
            <p>测试变量跟踪数据库存储</p>
            <button onclick="testVariableUpdate()" disabled id="variableBtn">更新变量数据</button>
            <div id="variableResult" class="log" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🎯 步骤4: 完整流程测试</h3>
            <p>测试完整的前端到后端集成</p>
            <button onclick="testFullFlow()" disabled id="fullFlowBtn">运行完整流程</button>
            <div id="fullFlowResult" class="log" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📈 测试结果总览</h3>
            <div id="summary">
                <p class="info">请按顺序执行上述测试步骤</p>
            </div>
        </div>
    </div>

    <script>
        let currentTestId = null;
        let websocket = null;
        const API_BASE = 'http://localhost:8000';

        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            element.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            element.scrollTop = element.scrollHeight;
        }

        function clearLog(elementId) {
            const element = document.getElementById(elementId);
            element.innerHTML = '';
        }

        async function testExecutionStart() {
            clearLog('startResult');
            log('startResult', '🚀 开始测试执行启动...', 'info');

            const agentId = document.getElementById('agentId').value;
            const testInput = document.getElementById('testInput').value;

            if (!agentId || !testInput) {
                log('startResult', '❌ 请填写Agent ID和测试输入', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/v1/test-execution/start`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        // Note: In production, you would need proper authentication
                    },
                    body: JSON.stringify({
                        agent_id: agentId,
                        input_text: testInput,
                        ai_config_override: {
                            provider: 'openai',
                            model: 'gpt-4',
                            temperature: 0.7
                        },
                        api_key_name: '测试密钥',
                        input_metadata: {
                            timestamp: new Date().toISOString(),
                            test_type: 'refactored_flow_test'
                        }
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    currentTestId = result.test_id;
                    log('startResult', `✅ 测试执行启动成功!`, 'success');
                    log('startResult', `📋 Test ID: ${result.test_id}`, 'info');
                    log('startResult', `📊 状态: ${result.status}`, 'info');
                    log('startResult', `💬 消息: ${result.message}`, 'info');
                    
                    // Enable next steps
                    document.getElementById('variableBtn').disabled = false;
                    document.getElementById('fullFlowBtn').disabled = false;
                    
                    updateSummary('start', true);
                } else {
                    const error = await response.text();
                    log('startResult', `❌ 启动失败: ${response.status}`, 'error');
                    log('startResult', `📄 错误详情: ${error}`, 'error');
                    updateSummary('start', false);
                }
            } catch (error) {
                log('startResult', `❌ 请求异常: ${error.message}`, 'error');
                updateSummary('start', false);
            }
        }

        function testWebSocketConnection() {
            clearLog('wsResult');
            log('wsResult', '🔗 开始WebSocket连接测试...', 'info');

            const agentId = document.getElementById('agentId').value;
            if (!agentId) {
                log('wsResult', '❌ 请先填写Agent ID', 'error');
                return;
            }

            try {
                const wsUrl = `ws://localhost:8000/api/v1/ws/agents/${agentId}/variables`;
                websocket = new WebSocket(wsUrl);

                websocket.onopen = function(event) {
                    log('wsResult', '✅ WebSocket连接已建立', 'success');
                    document.getElementById('disconnectBtn').disabled = false;
                    updateSummary('websocket', true);
                };

                websocket.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        log('wsResult', `📨 收到消息: ${data.type}`, 'info');
                        if (data.type === 'variable_update') {
                            log('wsResult', `🔄 变量更新: ${data.data.variable_name}`, 'info');
                        }
                    } catch (e) {
                        log('wsResult', `📨 收到原始消息: ${event.data}`, 'info');
                    }
                };

                websocket.onerror = function(error) {
                    log('wsResult', `❌ WebSocket错误: ${error}`, 'error');
                    updateSummary('websocket', false);
                };

                websocket.onclose = function(event) {
                    log('wsResult', `🔌 WebSocket连接已关闭`, 'info');
                    document.getElementById('disconnectBtn').disabled = true;
                };

            } catch (error) {
                log('wsResult', `❌ WebSocket连接异常: ${error.message}`, 'error');
                updateSummary('websocket', false);
            }
        }

        function disconnectWebSocket() {
            if (websocket) {
                websocket.close();
                websocket = null;
                log('wsResult', '🔌 主动断开WebSocket连接', 'info');
            }
        }

        async function testVariableUpdate() {
            if (!currentTestId) {
                log('variableResult', '❌ 请先执行测试启动步骤', 'error');
                return;
            }

            clearLog('variableResult');
            log('variableResult', '📊 开始变量数据更新测试...', 'info');

            try {
                const response = await fetch(`${API_BASE}/api/v1/test-execution/${currentTestId}/variables`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        context_placeholders_used: [
                            {
                                variable_name: '{user.requirements}',
                                variable_value: document.getElementById('testInput').value,
                                source_agent: 'user',
                                execution_step: 0,
                                timestamp: new Date().toISOString(),
                                variable_type: 'user-input',
                                destination_agents: ['test_agent']
                            }
                        ],
                        team_member_interactions: [
                            {
                                source_agent: 'user',
                                destination_agents: ['test_agent'],
                                variable_name: '{user.requirements}',
                                execution_step: 0,
                                timestamp: new Date().toISOString(),
                                data_type: 'variable_resolution'
                            }
                        ],
                        context_summary: {
                            total_variables: 1,
                            resolved_variables: 1,
                            variable_types: {
                                'user-input': 1
                            }
                        }
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    log('variableResult', '✅ 变量数据更新成功!', 'success');
                    log('variableResult', `💬 消息: ${result.message}`, 'info');
                    updateSummary('variable', true);
                } else {
                    const error = await response.text();
                    log('variableResult', `❌ 变量更新失败: ${response.status}`, 'error');
                    log('variableResult', `📄 错误详情: ${error}`, 'error');
                    updateSummary('variable', false);
                }
            } catch (error) {
                log('variableResult', `❌ 请求异常: ${error.message}`, 'error');
                updateSummary('variable', false);
            }
        }

        async function testFullFlow() {
            clearLog('fullFlowResult');
            log('fullFlowResult', '🎯 开始完整流程测试...', 'info');
            log('fullFlowResult', '📋 这将模拟前端完整的测试执行流程', 'info');
            
            // This would be a more comprehensive test that simulates the full frontend flow
            log('fullFlowResult', '⚠️ 完整流程测试需要在实际的前端应用中进行', 'info');
            log('fullFlowResult', '🔗 请在主应用中测试agent执行功能', 'info');
            
            updateSummary('fullflow', true);
        }

        function updateSummary(step, success) {
            const summary = document.getElementById('summary');
            const steps = {
                start: '测试执行启动',
                websocket: 'WebSocket连接',
                variable: '变量数据更新',
                fullflow: '完整流程测试'
            };
            
            const status = success ? '✅' : '❌';
            const existing = summary.querySelector(`[data-step="${step}"]`);
            
            if (existing) {
                existing.innerHTML = `${status} ${steps[step]}`;
            } else {
                const div = document.createElement('div');
                div.setAttribute('data-step', step);
                div.innerHTML = `${status} ${steps[step]}`;
                summary.appendChild(div);
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('summary', '🚀 重构流程测试页面已加载', 'info');
        });
    </script>
</body>
</html>
