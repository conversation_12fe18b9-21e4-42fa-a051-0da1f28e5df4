import { useState, useEffect } from 'react';
import { settingsApi } from '@/lib/api-client-settings';
import { useAuth } from '@/lib/auth';

interface SystemSettings {
  general: {
    app_name: string;
    app_description: string;
    default_language: string;
    timezone: string;
    theme: "light" | "dark" | "system";
  };
  agent: {
    max_concurrent_agents: number;
    default_model: string;
    default_temperature: number;
    max_response_length: number;
    timeout_seconds: number;
  };
  ai_team_generation: {
    enable_ai_team_generation: boolean;
    team_generation_provider: string;
    team_generation_custom_provider_name: string;
    team_generation_model: string;
    team_generation_temperature: number;
    team_generation_max_tokens: number;
    team_generation_base_url: string;
    team_generation_api_key: string;
  };
  api: {
    rate_limit_per_minute: number;
    enable_cors: boolean;
    cors_origins: string;
    enable_docs: boolean;
    enable_debug: boolean;
    agent_api_base_url: string;
  };
  logging: {
    log_level: "DEBUG" | "INFO" | "WARNING" | "ERROR";
    max_log_files: number;
    log_retention_days: number;
    enable_file_logging: boolean;
  };
  security: {
    enable_auth: boolean;
    session_timeout_minutes: number;
    max_login_attempts: number;
    enable_2fa: boolean;
  };
}

export function useSystemSettings() {
  const [settings, setSettings] = useState<SystemSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();

  useEffect(() => {
    const loadSettings = async () => {
      try {
        setLoading(true);

        // Only load settings for admin users
        if (isAuthenticated && user && user.role === 'admin') {
          const result = await settingsApi.getSettings();
          setSettings(result);
          setError(null);
        } else {
          // For non-admin users, don't call the API but still provide default values
          setSettings(null);
          setError(null);
        }
      } catch (err) {
        console.error('Failed to load system settings:', err);
        setError(err instanceof Error ? err.message : 'Failed to load settings');
      } finally {
        setLoading(false);
      }
    };

    // Only load settings after auth is complete
    if (!authLoading) {
      loadSettings();
    }
  }, [isAuthenticated, user, authLoading]);

  return {
    settings,
    loading: loading || authLoading,
    error,
    agentApiBaseUrl: settings?.api?.agent_api_base_url || `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}/api/v1/agents`
  };
}
