import { useState } from 'react';
import { useTwoFactorVerification } from './use-two-factor-verification';

export interface SensitiveOperationOptions {
  operationName: string;
  onSuccess?: (result: any) => void;
  onError?: (error: string) => void;
  requiresConfirmation?: boolean;
  confirmationMessage?: string;
}

export interface SensitiveOperationData {
  totp_code?: string;
  backup_code?: string;
  [key: string]: any; // Allow additional operation-specific data
}

export function useSensitiveOperation(
  operationFunction: (data: any) => Promise<any>,
  options: SensitiveOperationOptions
) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [pendingData, setPendingData] = useState<any>(null);

  // 2FA verification hook
  const twoFactorVerification = useTwoFactorVerification({
    onSuccess: async (result) => {
      try {
        setIsLoading(true);
        setError(null);
        
        // Execute the operation with 2FA verification data
        const operationResult = await operationFunction({
          ...pendingData,
          totp_code: result.totp_code,
          backup_code: result.backup_code,
        });
        
        if (options.onSuccess) {
          options.onSuccess(operationResult);
        }
        
        // Clear pending data
        setPendingData(null);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Operation failed';
        setError(errorMessage);
        
        if (options.onError) {
          options.onError(errorMessage);
        }
      } finally {
        setIsLoading(false);
      }
    },
    onError: (error) => {
      setError(error);
      if (options.onError) {
        options.onError(error);
      }
    },
    verifyFunction: async (tempSessionId, data) => {
      // For sensitive operations, we don't use temp session ID
      // Instead, we return the verification data to be used in the operation
      return data;
    },
  });

  const executeOperation = async (data: any) => {
    setError(null);
    
    // If confirmation is required, show confirmation dialog first
    if (options.requiresConfirmation) {
      setPendingData(data);
      setShowConfirmation(true);
      return;
    }
    
    await performOperation(data);
  };

  const performOperation = async (data: any) => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Try to execute the operation first
      const result = await operationFunction(data);
      
      if (options.onSuccess) {
        options.onSuccess(result);
      }
    } catch (error: any) {
      // Check if error indicates 2FA is required
      if (error.message?.includes('Two-factor authentication required') || 
          error.response?.status === 400 && error.response?.data?.detail?.includes('Two-factor authentication required')) {
        
        // Store the operation data and open 2FA verification
        setPendingData(data);
        twoFactorVerification.actions.openVerification('sensitive-operation');
      } else {
        const errorMessage = error instanceof Error ? error.message : 'Operation failed';
        setError(errorMessage);
        
        if (options.onError) {
          options.onError(errorMessage);
        }
      }
    } finally {
      setIsLoading(false);
    }
  };

  const confirmOperation = () => {
    setShowConfirmation(false);
    if (pendingData) {
      performOperation(pendingData);
    }
  };

  const cancelOperation = () => {
    setShowConfirmation(false);
    setPendingData(null);
    setError(null);
  };

  return {
    executeOperation,
    confirmOperation,
    cancelOperation,
    isLoading,
    error,
    showConfirmation,
    confirmationMessage: options.confirmationMessage || `Are you sure you want to ${options.operationName}?`,
    twoFactorVerification,
  };
}
