"use client";

import { useEffect, useRef, useState, useCallback } from 'react';
import { useInView } from 'react-intersection-observer';

// Performance monitoring hook
export function usePerformanceMonitor(componentName: string) {
  const renderCount = useRef(0);
  const mountTime = useRef<number>(Date.now());
  const [renderTime, setRenderTime] = useState<number>(0);

  useEffect(() => {
    renderCount.current += 1;
    const startTime = performance.now();
    let isMounted = true;

    return () => {
      const endTime = performance.now();
      const renderDuration = endTime - startTime;

      // Only update state if component is still mounted
      if (isMounted) {
        setRenderTime(renderDuration);
      }

      // Log performance metrics in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Performance] ${componentName}:`, {
          renderCount: renderCount.current,
          renderTime: renderDuration,
          mountTime: Date.now() - mountTime.current,
        });
      }

      isMounted = false;
    };
  });

  return {
    renderCount: renderCount.current,
    renderTime,
    mountTime: Date.now() - mountTime.current,
  };
}

// Lazy loading hook with intersection observer
export function useLazyLoad(threshold = 0.1, rootMargin = '50px') {
  const { ref, inView, entry } = useInView({
    threshold,
    rootMargin,
    triggerOnce: true,
  });

  return {
    ref,
    inView,
    entry,
    shouldLoad: inView,
  };
}

// Debounced value hook for performance optimization
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

// Throttled callback hook
export function useThrottle<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const lastCall = useRef<number>(0);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const throttledCallback = useCallback(
    (...args: Parameters<T>) => {
      const now = Date.now();
      
      if (now - lastCall.current >= delay) {
        lastCall.current = now;
        return callback(...args);
      } else {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
        
        timeoutRef.current = setTimeout(() => {
          lastCall.current = Date.now();
          callback(...args);
        }, delay - (now - lastCall.current));
      }
    },
    [callback, delay]
  ) as T;

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return throttledCallback;
}

// Memory usage monitoring hook
export function useMemoryMonitor() {
  const [memoryInfo, setMemoryInfo] = useState<{
    usedJSHeapSize?: number;
    totalJSHeapSize?: number;
    jsHeapSizeLimit?: number;
  }>({});

  useEffect(() => {
    const updateMemoryInfo = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        setMemoryInfo({
          usedJSHeapSize: memory.usedJSHeapSize,
          totalJSHeapSize: memory.totalJSHeapSize,
          jsHeapSizeLimit: memory.jsHeapSizeLimit,
        });
      }
    };

    updateMemoryInfo();
    const interval = setInterval(updateMemoryInfo, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, []);

  return memoryInfo;
}

// FPS monitoring hook
export function useFPSMonitor() {
  const [fps, setFps] = useState<number>(0);
  const frameCount = useRef<number>(0);
  const lastTime = useRef<number>(performance.now());

  useEffect(() => {
    let animationId: number;

    const measureFPS = () => {
      frameCount.current++;
      const currentTime = performance.now();
      
      if (currentTime >= lastTime.current + 1000) {
        setFps(Math.round((frameCount.current * 1000) / (currentTime - lastTime.current)));
        frameCount.current = 0;
        lastTime.current = currentTime;
      }
      
      animationId = requestAnimationFrame(measureFPS);
    };

    animationId = requestAnimationFrame(measureFPS);

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, []);

  return fps;
}

// Component visibility tracking hook
export function useVisibilityTracker(elementRef: React.RefObject<HTMLElement>) {
  const [isVisible, setIsVisible] = useState<boolean>(false);
  const [visibilityTime, setVisibilityTime] = useState<number>(0);
  const visibilityStart = useRef<number>(0);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          visibilityStart.current = Date.now();
        } else {
          setIsVisible(false);
          if (visibilityStart.current > 0) {
            setVisibilityTime(prev => prev + (Date.now() - visibilityStart.current));
          }
        }
      },
      { threshold: 0.5 }
    );

    observer.observe(element);

    return () => {
      observer.disconnect();
      if (isVisible && visibilityStart.current > 0) {
        setVisibilityTime(prev => prev + (Date.now() - visibilityStart.current));
      }
    };
  }, [elementRef, isVisible]);

  return {
    isVisible,
    visibilityTime,
  };
}

// Network status monitoring hook
export function useNetworkStatus() {
  const [isOnline, setIsOnline] = useState<boolean>(
    typeof navigator !== 'undefined' ? navigator.onLine : true
  );
  const [connectionType, setConnectionType] = useState<string>('unknown');

  useEffect(() => {
    const updateOnlineStatus = () => setIsOnline(navigator.onLine);
    
    const updateConnectionType = () => {
      if ('connection' in navigator) {
        const connection = (navigator as any).connection;
        setConnectionType(connection.effectiveType || 'unknown');
      }
    };

    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);
    
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      connection.addEventListener('change', updateConnectionType);
      updateConnectionType();
    }

    return () => {
      window.removeEventListener('online', updateOnlineStatus);
      window.removeEventListener('offline', updateOnlineStatus);
      
      if ('connection' in navigator) {
        const connection = (navigator as any).connection;
        connection.removeEventListener('change', updateConnectionType);
      }
    };
  }, []);

  return {
    isOnline,
    connectionType,
  };
}

// Performance metrics aggregation hook
export function usePerformanceMetrics() {
  const memoryInfo = useMemoryMonitor();
  const fps = useFPSMonitor();
  const networkStatus = useNetworkStatus();

  const getPerformanceScore = useCallback(() => {
    let score = 100;
    
    // Deduct points for low FPS
    if (fps < 30) score -= 20;
    else if (fps < 45) score -= 10;
    
    // Deduct points for high memory usage
    if (memoryInfo.usedJSHeapSize && memoryInfo.jsHeapSizeLimit) {
      const memoryUsagePercent = (memoryInfo.usedJSHeapSize / memoryInfo.jsHeapSizeLimit) * 100;
      if (memoryUsagePercent > 80) score -= 20;
      else if (memoryUsagePercent > 60) score -= 10;
    }
    
    // Deduct points for poor network
    if (!networkStatus.isOnline) score -= 30;
    else if (networkStatus.connectionType === 'slow-2g') score -= 20;
    else if (networkStatus.connectionType === '2g') score -= 10;
    
    return Math.max(0, score);
  }, [fps, memoryInfo, networkStatus]);

  return {
    fps,
    memoryInfo,
    networkStatus,
    performanceScore: getPerformanceScore(),
  };
}

// Resource loading optimization hook
export function useResourcePreload(resources: string[]) {
  useEffect(() => {
    resources.forEach(resource => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = resource;
      
      if (resource.endsWith('.js')) {
        link.as = 'script';
      } else if (resource.endsWith('.css')) {
        link.as = 'style';
      } else if (resource.match(/\.(jpg|jpeg|png|gif|webp)$/)) {
        link.as = 'image';
      }
      
      document.head.appendChild(link);
    });
  }, [resources]);
}

// Idle callback hook for non-critical tasks
export function useIdleCallback(callback: () => void, deps: React.DependencyList) {
  useEffect(() => {
    if ('requestIdleCallback' in window) {
      const id = requestIdleCallback(callback);
      return () => cancelIdleCallback(id);
    } else {
      // Fallback for browsers that don't support requestIdleCallback
      const timeout = setTimeout(callback, 0);
      return () => clearTimeout(timeout);
    }
  }, deps);
}

// All hooks are exported individually above
