import { useEffect, useCallback } from 'react';

interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
  metaKey?: boolean;
  callback: () => void;
  description: string;
}

export function useKeyboardShortcuts(shortcuts: KeyboardShortcut[], enabled: boolean = true) {
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!enabled) return;

    // Don't trigger shortcuts when user is typing in input fields
    const target = event.target as HTMLElement;
    if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true') {
      return;
    }

    for (const shortcut of shortcuts) {
      const keyMatches = event.key.toLowerCase() === shortcut.key.toLowerCase();
      const ctrlMatches = !!shortcut.ctrlKey === event.ctrlKey;
      const shiftMatches = !!shortcut.shiftKey === event.shiftKey;
      const altMatches = !!shortcut.altKey === event.altKey;
      const metaMatches = !!shortcut.metaKey === event.metaKey;

      if (keyMatches && ctrlMatches && shiftMatches && altMatches && metaMatches) {
        event.preventDefault();
        shortcut.callback();
        break;
      }
    }
  }, [shortcuts, enabled]);

  useEffect(() => {
    if (enabled) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [handleKeyDown, enabled]);

  return shortcuts;
}

// Common keyboard shortcuts for the test interface
export const createTestInterfaceShortcuts = (callbacks: {
  onSubmit: () => void;
  onClear: () => void;
  onSave: () => void;
  onExport: () => void;
  onToggleOverride: () => void;
}) => [
  {
    key: 'Enter',
    ctrlKey: true,
    callback: callbacks.onSubmit,
    description: 'Submit test (Ctrl+Enter)'
  },
  {
    key: 'k',
    ctrlKey: true,
    callback: callbacks.onClear,
    description: 'Clear input (Ctrl+K)'
  },
  {
    key: 's',
    ctrlKey: true,
    callback: callbacks.onSave,
    description: 'Save test case (Ctrl+S)'
  },
  {
    key: 'e',
    ctrlKey: true,
    callback: callbacks.onExport,
    description: 'Export results (Ctrl+E)'
  },
  {
    key: 'o',
    ctrlKey: true,
    callback: callbacks.onToggleOverride,
    description: 'Toggle AI override (Ctrl+O)'
  }
];

// Hook for displaying keyboard shortcuts help
export function useKeyboardShortcutsHelp() {
  const showHelp = useCallback(() => {
    const helpContent = `
Keyboard Shortcuts:

Test Interface:
• Ctrl+Enter - Submit test
• Ctrl+K - Clear input
• Ctrl+S - Save test case
• Ctrl+E - Export results
• Ctrl+O - Toggle AI override

General:
• Ctrl+/ - Show this help
• Esc - Close dialogs
    `.trim();

    alert(helpContent);
  }, []);

  useKeyboardShortcuts([
    {
      key: '/',
      ctrlKey: true,
      callback: showHelp,
      description: 'Show keyboard shortcuts help (Ctrl+/)'
    }
  ]);

  return showHelp;
}
