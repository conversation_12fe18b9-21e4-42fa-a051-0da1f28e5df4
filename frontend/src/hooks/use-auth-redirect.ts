import { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@/lib/auth';

/**
 * Hook to handle authentication redirects
 * Redirects unauthenticated users to login page with return URL
 */
export function useAuthRedirect() {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    // Don't redirect while loading or if already authenticated
    if (isLoading || isAuthenticated) {
      return;
    }

    // Don't redirect if already on login/register pages
    if (pathname === '/login' || pathname === '/register') {
      return;
    }

    // Redirect to login with return URL
    const returnUrl = encodeURIComponent(pathname);
    router.replace(`/login?redirect=${returnUrl}`);
  }, [isAuthenticated, isLoading, pathname, router]);

  return {
    isAuthenticated,
    isLoading,
    shouldRender: isAuthenticated || isLoading, // Render content if authenticated or still loading
  };
}

/**
 * Utility function to get redirect URL for login
 */
export function getLoginUrl(returnUrl?: string): string {
  if (!returnUrl) {
    return '/login';
  }
  
  const encodedUrl = encodeURIComponent(returnUrl);
  return `/login?redirect=${encodedUrl}`;
}

/**
 * Utility function to redirect to login with current path
 */
export function redirectToLogin(currentPath?: string) {
  const returnUrl = currentPath || window.location.pathname;
  const loginUrl = getLoginUrl(returnUrl);
  window.location.href = loginUrl;
}
