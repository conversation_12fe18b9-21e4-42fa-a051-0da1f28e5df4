import { useState } from 'react';

export interface TwoFactorVerificationData {
  totp_code?: string;
  backup_code?: string;
}

export interface TwoFactorVerificationState {
  isOpen: boolean;
  loading: boolean;
  error: string | null;
  tempSessionId: string | null;
}

export interface TwoFactorVerificationActions {
  openVerification: (tempSessionId: string) => void;
  closeVerification: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  verify: (data: TwoFactorVerificationData) => Promise<void>;
}

export interface UseTwoFactorVerificationOptions {
  onSuccess?: (result: any) => void;
  onError?: (error: string) => void;
  verifyFunction?: (tempSessionId: string, data: TwoFactorVerificationData) => Promise<any>;
}

export function useTwoFactorVerification(options: UseTwoFactorVerificationOptions = {}) {
  const [state, setState] = useState<TwoFactorVerificationState>({
    isOpen: false,
    loading: false,
    error: null,
    tempSessionId: null,
  });

  const openVerification = (tempSessionId: string) => {
    setState(prev => ({
      ...prev,
      isOpen: true,
      tempSessionId,
      error: null,
    }));
  };

  const closeVerification = () => {
    setState(prev => ({
      ...prev,
      isOpen: false,
      loading: false,
      error: null,
      tempSessionId: null,
    }));
  };

  const setLoading = (loading: boolean) => {
    setState(prev => ({ ...prev, loading }));
  };

  const setError = (error: string | null) => {
    setState(prev => ({ ...prev, error }));
  };

  const verify = async (data: TwoFactorVerificationData) => {
    if (!state.tempSessionId) {
      throw new Error('No temporary session ID');
    }

    setLoading(true);
    setError(null);

    try {
      let result;
      
      if (options.verifyFunction) {
        result = await options.verifyFunction(state.tempSessionId, data);
      } else {
        throw new Error('No verification function provided');
      }

      if (options.onSuccess) {
        options.onSuccess(result);
      }

      closeVerification();
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Verification failed';
      setError(errorMessage);
      
      if (options.onError) {
        options.onError(errorMessage);
      }
      
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return {
    state,
    actions: {
      openVerification,
      closeVerification,
      setLoading,
      setError,
      verify,
    },
  };
}
