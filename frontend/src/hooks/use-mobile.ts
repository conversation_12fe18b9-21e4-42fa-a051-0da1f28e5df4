import * as React from "react"

const M<PERSON><PERSON>LE_BREAKPOINT = 768

export function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState<boolean>(() => {
    // Initialize with a safe default for SSR
    if (typeof window === 'undefined') return false
    return window.innerWidth < MO<PERSON>LE_BREAKPOINT
  })

  React.useEffect(() => {
    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)
    const onChange = () => {
      const newIsMobile = window.innerWidth < MOBILE_BREAKPOINT
      setIsMobile(prevIsMobile => {
        // Only update if the value actually changed to prevent unnecessary re-renders
        if (prevIsMobile !== newIsMobile) {
          return newIsMobile
        }
        return prevIsMobile
      })
    }

    // Set initial value
    const initialIsMobile = window.innerWidth < MO<PERSON>LE_BREAKPOINT
    setIsMobile(prevIsMobile => {
      if (prevIsMobile !== initialIsMobile) {
        return initialIsMobile
      }
      return prevIsMobile
    })

    mql.addEventListener("change", onChange)
    return () => mql.removeEventListener("change", onChange)
  }, [])

  return isMobile
}
