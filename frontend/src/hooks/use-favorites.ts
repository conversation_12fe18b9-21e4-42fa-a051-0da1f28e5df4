/**
 * React hooks for agent favorites functionality
 */

import { useState, useCallback, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { favoritesApi } from '@/lib/api/favorites';
import {
  FavoriteAgentResponse,
  ToggleFavoriteResponse,
  UseFavoritesReturn,
  ToggleFavoriteMutationReturn,
  FavoriteToggleError,
  FavoritesError
} from '@/types/favorites';

// Query keys
const FAVORITES_QUERY_KEYS = {
  all: ['favorites'] as const,
  list: () => [...FAVORITES_QUERY_KEYS.all, 'list'] as const,
  detail: (id: string) => [...FAVORITES_QUERY_KEYS.all, 'detail', id] as const,
  count: () => [...FAVORITES_QUERY_KEYS.all, 'count'] as const,
} as const;

/**
 * Main hook for managing agent favorites
 */
export function useFavorites(includePerformance: boolean = true): UseFavoritesReturn {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Fetch favorites
  const {
    data: favorites = [],
    isLoading,
    error,
    refetch: refetchQuery
  } = useQuery({
    queryKey: [...FAVORITES_QUERY_KEYS.list(), includePerformance],
    queryFn: () => favoritesApi.getFavorites(includePerformance),
    staleTime: 30000, // 30 seconds
    cacheTime: 300000, // 5 minutes
    retry: (failureCount, error) => {
      // Don't retry on auth errors
      if (error instanceof FavoritesError && error.message.includes('401')) {
        return false;
      }
      return failureCount < 3;
    },
    onError: (error) => {
      console.error('Failed to fetch favorites:', error);
      toast({
        title: "获取失败",
        description: "无法获取收藏列表",
        variant: "destructive",
      });
    }
  });

  // Toggle favorite mutation
  const toggleMutation = useMutation({
    mutationFn: (agentId: string) => favoritesApi.toggleFavorite(agentId),
    onSuccess: (data, agentId) => {
      // Update cache optimistically
      queryClient.setQueryData<FavoriteAgentResponse[]>(
        [...FAVORITES_QUERY_KEYS.list(), includePerformance],
        (oldData = []) => {
          if (data.is_favorite) {
            // Agent was favorited - we need to fetch updated data
            refetchQuery();
            return oldData;
          } else {
            // Agent was unfavorited - remove from list
            return oldData.filter(fav => fav.agent_id !== agentId);
          }
        }
      );

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: FAVORITES_QUERY_KEYS.all });
      queryClient.invalidateQueries({ queryKey: ['dashboard-data'] });
      queryClient.invalidateQueries({ queryKey: ['agents'] });

      // Show success message
      toast({
        title: data.is_favorite ? '已添加到收藏' : '已从收藏中移除',
        description: data.message,
      });
    },
    onError: (error: FavoriteToggleError, agentId) => {
      console.error('Failed to toggle favorite:', error);
      toast({
        title: "操作失败",
        description: `无法${isFavorite(agentId) ? '取消收藏' : '收藏'}该Agent`,
        variant: "destructive",
      });
    }
  });

  // Helper functions
  const isFavorite = useCallback((agentId: string): boolean => {
    return favorites.some(fav => fav.agent_id === agentId);
  }, [favorites]);

  const getFavoriteId = useCallback((agentId: string): number | undefined => {
    const favorite = favorites.find(fav => fav.agent_id === agentId);
    return favorite?.favorite_id;
  }, [favorites]);

  const toggleFavorite = useCallback(async (agentId: string): Promise<ToggleFavoriteResponse> => {
    return toggleMutation.mutateAsync(agentId);
  }, [toggleMutation]);

  const refetch = useCallback(async (): Promise<void> => {
    await refetchQuery();
  }, [refetchQuery]);

  return {
    favorites,
    isLoading: isLoading || toggleMutation.isLoading,
    error: error as Error | null,
    toggleFavorite,
    refetch,
    isFavorite,
    getFavoriteId
  };
}

/**
 * Hook for toggle favorite mutation only
 */
export function useToggleFavorite(): ToggleFavoriteMutationReturn {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const mutation = useMutation({
    mutationFn: (agentId: string) => favoritesApi.toggleFavorite(agentId),
    onSuccess: (data, agentId) => {
      // Invalidate all favorites-related queries
      queryClient.invalidateQueries({ queryKey: FAVORITES_QUERY_KEYS.all });
      queryClient.invalidateQueries({ queryKey: ['dashboard-data'] });
      queryClient.invalidateQueries({ queryKey: ['agents'] });

      toast({
        title: data.is_favorite ? '已添加到收藏' : '已从收藏中移除',
      });
    },
    onError: (error: FavoriteToggleError) => {
      console.error('Failed to toggle favorite:', error);
      toast({
        title: "操作失败",
        description: "无法更新收藏状态",
        variant: "destructive",
      });
    }
  });

  return {
    toggleFavorite: mutation.mutateAsync,
    isLoading: mutation.isLoading,
    error: mutation.error as Error | null
  };
}

/**
 * Hook for favorites count
 */
export function useFavoritesCount() {
  return useQuery({
    queryKey: FAVORITES_QUERY_KEYS.count(),
    queryFn: () => favoritesApi.getFavoritesCount(),
    staleTime: 60000, // 1 minute
    cacheTime: 300000, // 5 minutes
  });
}

/**
 * Hook for recently favorited agents
 */
export function useRecentlyFavorited(limit: number = 5) {
  return useQuery({
    queryKey: [...FAVORITES_QUERY_KEYS.all, 'recent', limit],
    queryFn: () => favoritesApi.getRecentlyFavorited(limit),
    staleTime: 30000,
    cacheTime: 300000,
  });
}

/**
 * Hook for most used favorite agents
 */
export function useMostUsedFavorites(limit: number = 5) {
  return useQuery({
    queryKey: [...FAVORITES_QUERY_KEYS.all, 'most-used', limit],
    queryFn: () => favoritesApi.getMostUsedFavorites(limit),
    staleTime: 60000,
    cacheTime: 300000,
  });
}

/**
 * Hook for searching favorites
 */
export function useSearchFavorites(query: string) {
  return useQuery({
    queryKey: [...FAVORITES_QUERY_KEYS.all, 'search', query],
    queryFn: () => favoritesApi.searchFavorites(query),
    enabled: query.length > 0,
    staleTime: 30000,
    cacheTime: 300000,
  });
}

/**
 * Hook for filtering favorites by status
 */
export function useFavoritesByStatus(status: string) {
  return useQuery({
    queryKey: [...FAVORITES_QUERY_KEYS.all, 'status', status],
    queryFn: () => favoritesApi.filterFavoritesByStatus(status),
    enabled: !!status,
    staleTime: 30000,
    cacheTime: 300000,
  });
}

/**
 * Hook for high performing favorites
 */
export function useHighPerformingFavorites(threshold: number = 90) {
  return useQuery({
    queryKey: [...FAVORITES_QUERY_KEYS.all, 'high-performing', threshold],
    queryFn: () => favoritesApi.getHighPerformingFavorites(threshold),
    staleTime: 60000,
    cacheTime: 300000,
  });
}

/**
 * Custom hook for favorites with local state management
 */
export function useFavoritesWithLocalState() {
  const [localFavorites, setLocalFavorites] = useState<Set<string>>(new Set());
  const { favorites, isLoading, error, toggleFavorite: apiToggleFavorite } = useFavorites();

  // Sync with API data
  const allFavoriteIds = useMemo(() => {
    const apiIds = new Set(favorites.map(fav => fav.agent_id));
    return new Set([...apiIds, ...localFavorites]);
  }, [favorites, localFavorites]);

  const toggleFavorite = useCallback(async (agentId: string) => {
    // Optimistic update
    setLocalFavorites(prev => {
      const newSet = new Set(prev);
      if (newSet.has(agentId)) {
        newSet.delete(agentId);
      } else {
        newSet.add(agentId);
      }
      return newSet;
    });

    try {
      const result = await apiToggleFavorite(agentId);
      // Sync local state with API result
      setLocalFavorites(prev => {
        const newSet = new Set(prev);
        if (result.is_favorite) {
          newSet.add(agentId);
        } else {
          newSet.delete(agentId);
        }
        return newSet;
      });
      return result;
    } catch (error) {
      // Revert optimistic update on error
      setLocalFavorites(prev => {
        const newSet = new Set(prev);
        if (newSet.has(agentId)) {
          newSet.delete(agentId);
        } else {
          newSet.add(agentId);
        }
        return newSet;
      });
      throw error;
    }
  }, [apiToggleFavorite]);

  const isFavorite = useCallback((agentId: string): boolean => {
    return allFavoriteIds.has(agentId);
  }, [allFavoriteIds]);

  return {
    favorites,
    isLoading,
    error,
    toggleFavorite,
    isFavorite,
    localFavorites: Array.from(localFavorites)
  };
}
