"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/main-layout";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { Pagination, usePagination } from "@/components/ui/pagination";
import { useToast } from "@/hooks/use-toast";
import Link from "next/link";
import { api } from "@/lib/api";
import { TemplateCard } from "@/components/templates/TemplateCard";
import { TemplateFilters } from "@/components/templates/TemplateFilters";
import { TemplatePreview } from "@/components/templates/TemplatePreview";
import {
  TemplateListItem,
  Template,
  TemplateFilters as ITemplateFilters,
  PopularTag,
  PaginationMeta
} from "@/lib/types";
import {
  Plus,
  RefreshCw,
  TrendingUp,
  BarChart3
} from "lucide-react";

export default function TemplatesPage() {
  const router = useRouter();
  const { toast } = useToast();

  // State management
  const [templates, setTemplates] = useState<TemplateListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<ITemplateFilters>({});
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [sortBy, setSortBy] = useState("created_at");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [previewOpen, setPreviewOpen] = useState(false);

  // Metadata
  const [categories, setCategories] = useState<Array<{ value: string; label: string }>>([]);
  const [difficulties, setDifficulties] = useState<Array<{ value: string; label: string }>>([]);
  const [popularTags, setPopularTags] = useState<PopularTag[]>([]);

  // Pagination
  const {
    currentPage,
    itemsPerPage,
    handlePageChange,
    handleItemsPerPageChange,
    resetPagination
  } = usePagination(1, 20);

  const [paginationMeta, setPaginationMeta] = useState<PaginationMeta | null>(null);

  // Load templates
  const loadTemplates = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // 始终使用分页API来获取所有可见的模板（包括用户模板、公共模板、特色模板）
      const response = await api.templates.listPaginated({
        ...filters,
        page: currentPage,
        limit: itemsPerPage,
        sort_by: sortBy,
        sort_order: sortOrder
      });

      if (response.success && response.data) {
        setTemplates(response.data.items);
        setPaginationMeta(response.data.pagination);
      } else {
        throw new Error(response.error?.message || "Failed to load templates");
      }


    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to load templates";
      setError(errorMessage);
      toast({
        title: "加载失败",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [filters, currentPage, itemsPerPage, sortBy, sortOrder, toast]);

  // Load metadata
  const loadMetadata = useCallback(async () => {
    try {
      const [categoriesRes, difficultiesRes, tagsRes] = await Promise.all([
        api.templates.getCategories(),
        api.templates.getDifficulties(),
        api.templates.getPopularTags(20)
      ]);

      if (categoriesRes.success && categoriesRes.data) {
        setCategories(categoriesRes.data);
      }

      if (difficultiesRes.success && difficultiesRes.data) {
        setDifficulties(difficultiesRes.data);
      }

      if (tagsRes.success && tagsRes.data) {
        setPopularTags(tagsRes.data);
      }
    } catch (err) {
      // Silently handle metadata loading errors
    }
  }, []);

  // Search templates
  const handleSearch = useCallback(async (query: string) => {
    // Update filters to include search query
    const newFilters = { ...filters, search: query };
    setFilters(newFilters);
    resetPagination();

    // The loadTemplates function will be called automatically due to the filters dependency
  }, [filters, resetPagination]);

  // Handle filter changes
  const handleFiltersChange = useCallback((newFilters: ITemplateFilters) => {
    setFilters(newFilters);
    resetPagination();
  }, [resetPagination]);

  // Handle sort changes
  const handleSortChange = useCallback((newSortBy: string, newSortOrder: "asc" | "desc") => {
    setSortBy(newSortBy);
    setSortOrder(newSortOrder);
    resetPagination();
  }, [resetPagination]);

  // Template actions
  const handleViewTemplate = useCallback(async (template: TemplateListItem) => {
    try {
      const response = await api.templates.get(template.template_id);
      if (response.success && response.data) {
        setSelectedTemplate(response.data);
        setPreviewOpen(true);
      } else {
        throw new Error(response.error?.message || "Failed to load template details");
      }
    } catch (err) {
      toast({
        title: "加载失败",
        description: err instanceof Error ? err.message : "Failed to load template details",
        variant: "destructive",
      });
    }
  }, [toast]);



  const handleCreateAgent = useCallback(async (template: TemplateListItem) => {
    try {
      // Show confirmation dialog
      const confirmed = confirm(
        `确定要从模板"${template.name}"直接创建Agent团队吗？\n\n这将使用模板的完整配置创建一个可立即使用的Agent团队。`
      );

      if (!confirmed) return;

      // Call the direct template-to-agent API
      const response = await api.agents.createFromTemplate(
        template.template_id,
        {} // No customizations for direct deployment
      );

      if (response.success && response.data) {
        toast({
          title: "Agent创建成功",
          description: `Agent团队"${response.data.team_name || template.name}"已创建完成`,
        });
        // Navigate to the agent management page
        router.push(`/manage?agent=${response.data.agent_id}`);
      } else {
        throw new Error(response.error?.message || "Failed to create agent from template");
      }
    } catch (err) {
      toast({
        title: "Agent创建失败",
        description: err instanceof Error ? err.message : "从模板创建Agent时出现错误",
        variant: "destructive",
      });
    }
  }, [toast, router]);

  const handleEditTemplate = useCallback((template: TemplateListItem) => {
    router.push(`/templates/${template.template_id}/edit`);
  }, [router]);



  const handleDeleteTemplate = useCallback(async (template: TemplateListItem) => {
    if (!confirm("确定要删除这个模板吗？此操作不可撤销。")) {
      return;
    }

    try {
      const response = await api.templates.delete(template.template_id);
      if (response.success) {
        toast({
          title: "删除成功",
          description: "模板已成功删除",
        });
        loadTemplates(true); // Reload templates
      } else {
        throw new Error(response.error?.message || "Failed to delete template");
      }
    } catch (err) {
      toast({
        title: "删除失败",
        description: err instanceof Error ? err.message : "Failed to delete template",
        variant: "destructive",
      });
    }
  }, [toast, loadTemplates]);

  // Effects
  useEffect(() => {
    loadMetadata();
  }, [loadMetadata]);

  useEffect(() => {
    loadTemplates();
  }, [loadTemplates]);

  // Render loading skeleton
  const renderSkeleton = () => (
    <div className={`grid gap-3 md:gap-6 mobile-grid-gap ${viewMode === "grid" ? "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3" : "grid-cols-1"}`}>
      {Array.from({ length: 6 }).map((_, index) => (
        <div key={index} className="space-y-2 md:space-y-3">
          <Skeleton className="h-40 md:h-48 w-full" />
          <Skeleton className="h-3 md:h-4 w-3/4" />
          <Skeleton className="h-3 md:h-4 w-1/2" />
        </div>
      ))}
    </div>
  );

  return (
    <MainLayout>
      <div className="space-y-4 md:space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex-1 min-w-0">
            <h1 className="text-xl md:text-3xl font-bold flex items-center gap-2">
              📚 模板库
              <TrendingUp className="h-5 md:h-6 w-5 md:w-6 text-muted-foreground" />
            </h1>
            <p className="text-muted-foreground mt-1 text-sm md:text-base mobile-text-base">
              发现和使用预设模板快速创建AI团队，或创建自己的模板
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
            <Button
              variant="outline"
              onClick={() => loadTemplates(true)}
              disabled={loading}
              className="touch-target min-h-[44px] md:min-h-[36px] mobile-button-spacing"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`} />
              刷新
            </Button>
            <Button
              asChild
              className="touch-target min-h-[44px] md:min-h-[36px] mobile-button-spacing"
            >
              <Link href="/templates/create">
                <Plus className="h-4 w-4 mr-2" />
                创建模板
              </Link>
            </Button>
            <Button
              variant="outline"
              asChild
              className="touch-target min-h-[44px] md:min-h-[36px] mobile-button-spacing"
            >
              <Link href="/templates/stats">
                <BarChart3 className="h-4 w-4 mr-2" />
                统计
              </Link>
            </Button>
          </div>
        </div>

        {/* Filters */}
        <TemplateFilters
          filters={filters}
          onFiltersChange={handleFiltersChange}
          onSearch={handleSearch}
          viewMode={viewMode}
          onViewModeChange={setViewMode}
          sortBy={sortBy}
          sortOrder={sortOrder}
          onSortChange={handleSortChange}
          popularTags={popularTags}
          categories={categories}
          difficulties={difficulties}
          showAdvanced={true}
        />

        {/* Error Alert */}
        {error && (
          <Alert variant="destructive">
            <AlertDescription className="text-sm mobile-text-sm">
              {error}
              <Button
                variant="outline"
                size="sm"
                className="ml-2 touch-target min-h-[44px] md:min-h-[32px]"
                onClick={() => loadTemplates(true)}
              >
                重试
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Templates Grid/List */}
        {loading && templates.length === 0 ? (
          renderSkeleton()
        ) : templates.length === 0 ? (
          <div className="text-center py-8 md:py-12">
            <div className="text-4xl md:text-6xl mb-4">📋</div>
            <h3 className="text-base md:text-lg font-semibold mb-2">暂无模板</h3>
            <p className="text-muted-foreground mb-4 text-sm md:text-base mobile-text-base px-4 md:px-0">
              {Object.keys(filters).length > 0
                ? "没有找到符合条件的模板，请尝试调整筛选条件"
                : "还没有任何模板，创建第一个模板吧！"
              }
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-2">
              {Object.keys(filters).length > 0 && (
                <Button
                  variant="outline"
                  onClick={() => setFilters({})}
                  className="touch-target min-h-[44px] md:min-h-[36px] mobile-button-spacing"
                >
                  清除筛选
                </Button>
              )}
              <Button
                asChild
                className="touch-target min-h-[44px] md:min-h-[36px] mobile-button-spacing"
              >
                <Link href="/templates/create">
                  <Plus className="h-4 w-4 mr-2" />
                  创建模板
                </Link>
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-4 md:space-y-6">
            <div className={`grid gap-3 md:gap-6 mobile-grid-gap ${
              viewMode === "grid"
                ? "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
                : "grid-cols-1"
            }`}>
              {templates.map((template) => (
                <TemplateCard
                  key={template.template_id}
                  template={template}
                  onView={handleViewTemplate}
                  onEdit={template.can_edit ? handleEditTemplate : undefined}
                  onDelete={template.can_edit ? handleDeleteTemplate : undefined}
                  onCreateAgent={handleCreateAgent}
                  compact={viewMode === "list"}
                />
              ))}
            </div>

            {/* Pagination */}
            {paginationMeta && paginationMeta.total_pages > 1 && (
              <div className="mt-6 md:mt-8">
                <Pagination
                  currentPage={paginationMeta.page}
                  totalPages={paginationMeta.total_pages}
                  totalItems={paginationMeta.total}
                  itemsPerPage={paginationMeta.limit}
                  onPageChange={handlePageChange}
                  onItemsPerPageChange={handleItemsPerPageChange}
                  disabled={loading}
                  className="justify-center"
                />
              </div>
            )}
          </div>
        )}

        {/* Template Preview Dialog */}
        {selectedTemplate && (
          <TemplatePreview
            template={selectedTemplate}
            isOpen={previewOpen}
            onClose={() => {
              setPreviewOpen(false);
              setSelectedTemplate(null);
            }}
            onEdit={() => {
              setPreviewOpen(false);
              handleEditTemplate(selectedTemplate);
            }}
            onCreateAgent={() => {
              setPreviewOpen(false);
              handleCreateAgent(selectedTemplate);
            }}
          />
        )}
      </div>
    </MainLayout>
  );
}
