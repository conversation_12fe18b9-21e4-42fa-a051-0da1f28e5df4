"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { MainLayout } from "@/components/layout/main-layout";
import { TemplatePreview } from "@/components/templates/TemplatePreview";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { api } from "@/lib/api";
import { Template } from "@/lib/types";
import { ArrowLeft } from "lucide-react";

export default function TemplateDetailPage() {
  const router = useRouter();
  const params = useParams();
  const { toast } = useToast();
  
  const templateId = params.templateId as string;
  
  const [template, setTemplate] = useState<Template | null>(null);
  const [versions, setVersions] = useState<Template[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load template and versions
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const [templateRes, versionsRes] = await Promise.all([
          api.templates.get(templateId),
          api.templates.getVersions(templateId).catch(() => ({ success: false, data: [] }))
        ]);
        
        if (templateRes.success && templateRes.data) {
          setTemplate(templateRes.data);
        } else {
          throw new Error(templateRes.error?.message || "Failed to load template");
        }
        
        if (versionsRes.success && versionsRes.data) {
          setVersions(versionsRes.data);
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to load template";
        setError(errorMessage);
        toast({
          title: "加载失败",
          description: errorMessage,
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    if (templateId) {
      loadData();
    }
  }, [templateId, toast]);

  const handleEdit = () => {
    router.push(`/templates/${templateId}/edit`);
  };





  const handleShare = () => {
    if (!template) return;
    
    const url = `${window.location.origin}/templates/${template.template_id}`;
    navigator.clipboard.writeText(url).then(() => {
      toast({
        title: "链接已复制",
        description: "模板分享链接已复制到剪贴板",
      });
    }).catch(() => {
      toast({
        title: "复制失败",
        description: "无法复制链接到剪贴板",
        variant: "destructive",
      });
    });
  };

  const handleViewVersions = () => {
    router.push(`/templates/${templateId}/versions`);
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">正在加载模板...</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (error && !template) {
    return (
      <MainLayout>
        <div className="space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回
            </Button>
          </div>
          
          <Alert variant="destructive">
            <AlertDescription>
              {error}
            </AlertDescription>
          </Alert>
        </div>
      </MainLayout>
    );
  }

  if (!template) {
    return (
      <MainLayout>
        <div className="space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回
            </Button>
          </div>
          
          <div className="text-center py-12">
            <h3 className="text-lg font-semibold mb-2">模板未找到</h3>
            <p className="text-muted-foreground">
              请检查模板ID是否正确，或者您是否有权限访问此模板。
            </p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Back Button */}
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回
          </Button>
        </div>

        {/* Template Preview */}
        <TemplatePreview
          template={template}
          isOpen={true}
          onClose={() => router.push('/templates')}
          onEdit={template.can_edit ? handleEdit : undefined}
          onShare={handleShare}
          onViewVersions={versions.length > 0 ? handleViewVersions : undefined}
          versions={versions}
        />
      </div>
    </MainLayout>
  );
}
