"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/main-layout";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import Link from "next/link";
import { api } from "@/lib/api";
import { TemplateCard } from "@/components/templates/TemplateCard";
import { TemplateFilters } from "@/components/templates/TemplateFilters";
import { TemplatePreview } from "@/components/templates/TemplatePreview";
import { 
  TemplateListItem, 
  Template,
  TemplateFilters as ITemplateFilters,
  PopularTag
} from "@/lib/types";
import { 
  Globe, 
  Star,
  TrendingUp,
  <PERSON>,
  ArrowLeft
} from "lucide-react";

export default function CommunityTemplatesPage() {
  const router = useRouter();
  const { toast } = useToast();
  
  // State management
  const [featuredTemplates, setFeaturedTemplates] = useState<TemplateListItem[]>([]);
  const [communityTemplates, setCommunityTemplates] = useState<TemplateListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<ITemplateFilters>({});
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [sortBy, setSortBy] = useState("popular");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("featured");
  
  // Metadata
  const [categories, setCategories] = useState<Array<{ value: string; label: string }>>([]);
  const [difficulties, setDifficulties] = useState<Array<{ value: string; label: string }>>([]);
  const [popularTags, setPopularTags] = useState<PopularTag[]>([]);
  
  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const pageSize = 20;

  // Load featured templates
  const loadFeaturedTemplates = useCallback(async () => {
    try {
      const response = await api.templates.getFeatured(10);
      
      if (response.success && response.data) {
        setFeaturedTemplates(response.data);
      } else {
        throw new Error(response.error?.message || "Failed to load featured templates");
      }
    } catch (err) {
      console.error("Failed to load featured templates:", err);
    }
  }, []);

  // Load community templates
  const loadCommunityTemplates = useCallback(async (reset = false) => {
    try {
      setLoading(true);
      setError(null);
      
      const page = reset ? 1 : currentPage;
      const skip = (page - 1) * pageSize;
      
      const response = await api.templates.getCommunity({
        skip,
        limit: pageSize,
        category: filters.category,
        difficulty: filters.difficulty,
        sort_by: sortBy
      });
      
      if (response.success && response.data) {
        if (reset) {
          setCommunityTemplates(response.data);
          setCurrentPage(1);
        } else {
          setCommunityTemplates(prev => [...prev, ...response.data!]);
        }
        
        setHasMore(response.data.length === pageSize);
      } else {
        throw new Error(response.error?.message || "Failed to load community templates");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to load community templates";
      setError(errorMessage);
      toast({
        title: "加载失败",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [filters, currentPage, pageSize, sortBy, toast]);

  // Load metadata
  const loadMetadata = useCallback(async () => {
    try {
      const [categoriesRes, difficultiesRes, tagsRes] = await Promise.all([
        api.templates.getCategories(),
        api.templates.getDifficulties(),
        api.templates.getPopularTags(20)
      ]);
      
      if (categoriesRes.success && categoriesRes.data) {
        setCategories(categoriesRes.data);
      }
      
      if (difficultiesRes.success && difficultiesRes.data) {
        setDifficulties(difficultiesRes.data);
      }
      
      if (tagsRes.success && tagsRes.data) {
        setPopularTags(tagsRes.data);
      }
    } catch (err) {
      console.error("Failed to load metadata:", err);
    }
  }, []);

  // Handle filter changes
  const handleFiltersChange = useCallback((newFilters: ITemplateFilters) => {
    setFilters(newFilters);
    setCurrentPage(1);
  }, []);

  // Handle sort changes
  const handleSortChange = useCallback((newSortBy: string, newSortOrder: "asc" | "desc") => {
    setSortBy(newSortBy);
    setSortOrder(newSortOrder);
    setCurrentPage(1);
  }, []);

  // Template actions
  const handleViewTemplate = useCallback(async (template: TemplateListItem) => {
    try {
      const response = await api.templates.get(template.template_id);
      if (response.success && response.data) {
        setSelectedTemplate(response.data);
        setPreviewOpen(true);
      } else {
        throw new Error(response.error?.message || "Failed to load template details");
      }
    } catch (err) {
      toast({
        title: "加载失败",
        description: err instanceof Error ? err.message : "Failed to load template details",
        variant: "destructive",
      });
    }
  }, [toast]);



  const handleCreateAgent = useCallback(async (template: TemplateListItem) => {
    try {
      // Show confirmation dialog
      const confirmed = confirm(
        `确定要从模板"${template.name}"直接创建Agent团队吗？\n\n这将使用模板的完整配置创建一个可立即使用的Agent团队。`
      );

      if (!confirmed) return;

      // Call the direct template-to-agent API
      const response = await api.agents.createFromTemplate(
        template.template_id,
        {} // No customizations for direct deployment
      );

      if (response.success && response.data) {
        toast({
          title: "Agent创建成功",
          description: `Agent团队"${response.data.team_name || template.name}"已创建完成`,
        });
        // Navigate to the agent management page
        router.push(`/manage?agent=${response.data.agent_id}`);
      } else {
        throw new Error(response.error?.message || "Failed to create agent from template");
      }
    } catch (err) {
      toast({
        title: "Agent创建失败",
        description: err instanceof Error ? err.message : "从模板创建Agent时出现错误",
        variant: "destructive",
      });
    }
  }, [toast, router]);



  // Load more templates
  const loadMore = useCallback(() => {
    if (!loading && hasMore) {
      setCurrentPage(prev => prev + 1);
    }
  }, [loading, hasMore]);

  // Effects
  useEffect(() => {
    loadMetadata();
    loadFeaturedTemplates();
  }, [loadMetadata, loadFeaturedTemplates]);

  useEffect(() => {
    if (activeTab === "community") {
      loadCommunityTemplates(true);
    }
  }, [filters, sortBy, sortOrder, activeTab]);

  useEffect(() => {
    if (currentPage > 1 && activeTab === "community") {
      loadCommunityTemplates(false);
    }
  }, [currentPage, activeTab]);

  // Render loading skeleton
  const renderSkeleton = () => (
    <div className={`grid gap-6 ${viewMode === "grid" ? "grid-cols-1 md:grid-cols-2 lg:grid-cols-3" : "grid-cols-1"}`}>
      {Array.from({ length: 6 }).map((_, index) => (
        <div key={index} className="space-y-3">
          <Skeleton className="h-48 w-full" />
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
        </div>
      ))}
    </div>
  );

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <div className="flex items-center gap-4 mb-2">
              <Button variant="outline" onClick={() => router.back()}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回
              </Button>
            </div>
            <h1 className="text-3xl font-bold flex items-center gap-2">
              <Globe className="h-6 w-6 text-blue-600" />
              社区模板
            </h1>
            <p className="text-muted-foreground mt-1">
              发现社区贡献的优质模板，找到适合您需求的解决方案
            </p>
          </div>
          <div className="flex gap-2">
            <Button asChild>
              <Link href="/templates">
                📚 我的模板
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/templates/create">
                ➕ 创建模板
              </Link>
            </Button>
          </div>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="featured" className="flex items-center gap-2">
              <Star className="h-4 w-4" />
              精选模板
            </TabsTrigger>
            <TabsTrigger value="community" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              社区模板
            </TabsTrigger>
          </TabsList>

          <TabsContent value="featured" className="space-y-6">
            {/* Featured Templates */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Star className="h-5 w-5 text-yellow-500" />
                <h2 className="text-xl font-semibold">精选模板</h2>
                <span className="text-sm text-muted-foreground">
                  ({featuredTemplates.length} 个模板)
                </span>
              </div>
              
              {featuredTemplates.length === 0 ? (
                <div className="text-center py-12">
                  <Star className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">暂无精选模板</h3>
                  <p className="text-muted-foreground">
                    管理员还未设置精选模板
                  </p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {featuredTemplates.map((template) => (
                    <TemplateCard
                      key={template.template_id}
                      template={template}
                      onView={handleViewTemplate}
                      onCreateAgent={handleCreateAgent}
                      showActions={true}
                    />
                  ))}
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="community" className="space-y-6">
            {/* Filters */}
            <TemplateFilters
              filters={filters}
              onFiltersChange={handleFiltersChange}
              onSearch={() => {}} // Community templates don't support search
              viewMode={viewMode}
              onViewModeChange={setViewMode}
              sortBy={sortBy}
              sortOrder={sortOrder}
              onSortChange={handleSortChange}
              popularTags={popularTags}
              categories={categories}
              difficulties={difficulties}
              showAdvanced={false}
            />

            {/* Error Alert */}
            {error && (
              <Alert variant="destructive">
                <AlertDescription>
                  {error}
                  <Button
                    variant="outline"
                    size="sm"
                    className="ml-2"
                    onClick={() => loadCommunityTemplates(true)}
                  >
                    重试
                  </Button>
                </AlertDescription>
              </Alert>
            )}

            {/* Community Templates */}
            {loading && communityTemplates.length === 0 ? (
              renderSkeleton()
            ) : communityTemplates.length === 0 ? (
              <div className="text-center py-12">
                <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">暂无社区模板</h3>
                <p className="text-muted-foreground mb-4">
                  {Object.keys(filters).length > 0 
                    ? "没有找到符合条件的模板，请尝试调整筛选条件" 
                    : "社区还没有公开的模板"
                  }
                </p>
                {Object.keys(filters).length > 0 && (
                  <Button variant="outline" onClick={() => setFilters({})}>
                    清除筛选
                  </Button>
                )}
              </div>
            ) : (
              <div className="space-y-6">
                <div className={`grid gap-6 ${
                  viewMode === "grid" 
                    ? "grid-cols-1 md:grid-cols-2 lg:grid-cols-3" 
                    : "grid-cols-1"
                }`}>
                  {communityTemplates.map((template) => (
                    <TemplateCard
                      key={template.template_id}
                      template={template}
                      onView={handleViewTemplate}
                      onCreateAgent={handleCreateAgent}
                      compact={viewMode === "list"}
                      showActions={true}
                    />
                  ))}
                </div>

                {/* Load More */}
                {hasMore && (
                  <div className="text-center">
                    <Button 
                      variant="outline" 
                      onClick={loadMore} 
                      disabled={loading}
                      className="min-w-32"
                    >
                      {loading ? "加载中..." : "加载更多"}
                    </Button>
                  </div>
                )}
              </div>
            )}
          </TabsContent>
        </Tabs>

        {/* Template Preview Dialog */}
        {selectedTemplate && (
          <TemplatePreview
            template={selectedTemplate}
            isOpen={previewOpen}
            onClose={() => {
              setPreviewOpen(false);
              setSelectedTemplate(null);
            }}
            onCreateAgent={() => {
              setPreviewOpen(false);
              handleCreateAgent(selectedTemplate);
            }}
          />
        )}
      </div>
    </MainLayout>
  );
}
