"use client";

import React, { useState, useEffect, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { MainLayout } from "@/components/layout/main-layout";
import { TemplateForm } from "@/components/templates/TemplateForm";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { api } from "@/lib/api";
import { 
  TemplateCreateRequest,
  Agent
} from "@/lib/types";

function CreateTemplatePageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [categories, setCategories] = useState<Array<{ value: string; label: string }>>([]);
  const [difficulties, setDifficulties] = useState<Array<{ value: string; label: string }>>([]);
  const [sourceAgent, setSourceAgent] = useState<Agent | null>(null);
  
  // Check if creating from an agent
  const agentId = searchParams.get("agent");

  // Load metadata
  useEffect(() => {
    const loadMetadata = async () => {
      try {
        const [categoriesRes, difficultiesRes] = await Promise.all([
          api.templates.getCategories(),
          api.templates.getDifficulties()
        ]);
        
        if (categoriesRes.success && categoriesRes.data) {
          setCategories(categoriesRes.data);
        }
        
        if (difficultiesRes.success && difficultiesRes.data) {
          setDifficulties(difficultiesRes.data);
        }
      } catch (err) {
        console.error("Failed to load metadata:", err);
      }
    };

    loadMetadata();
  }, []);

  // Load source agent if specified
  useEffect(() => {
    const loadSourceAgent = async () => {
      if (!agentId) return;
      
      try {
        setLoading(true);
        const response = await api.agents.get(agentId);
        
        if (response.success && response.data) {
          setSourceAgent(response.data);
        } else {
          throw new Error(response.error?.message || "Failed to load agent");
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to load agent";
        setError(errorMessage);
        toast({
          title: "加载失败",
          description: errorMessage,
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    loadSourceAgent();
  }, [agentId, toast]);

  const handleSubmit = async (data: TemplateCreateRequest) => {
    try {
      setLoading(true);
      setError(null);
      
      let response;
      
      if (sourceAgent) {
        // Create template from agent
        response = await api.templates.createFromAgent({
          agent_id: sourceAgent.agent_id,
          name: data.name,
          description: data.description,
          category: data.category,
          difficulty: data.difficulty,
          visibility: data.visibility,
          tags: data.tags,
          keywords: data.keywords,
          use_case: data.use_case,
          example_input: data.example_input,
          expected_output: data.expected_output,
        });
      } else {
        // Create template from scratch
        response = await api.templates.create(data);
      }
      
      if (response.success && response.data) {
        toast({
          title: "创建成功",
          description: "模板已成功创建",
        });
        
        // Navigate to the new template
        router.push(`/templates/${response.data.template_id}`);
      } else {
        throw new Error(response.error?.message || "Failed to create template");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to create template";
      setError(errorMessage);
      toast({
        title: "创建失败",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  if (loading && !sourceAgent && agentId) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">正在加载Agent信息...</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold">
            {sourceAgent ? "从Agent创建模板" : "创建新模板"}
          </h1>
          <p className="text-muted-foreground mt-1">
            {sourceAgent 
              ? `基于Agent "${sourceAgent.team_name}" 创建一个可重用的模板`
              : "创建一个新的AI团队模板，可以被其他用户使用"
            }
          </p>
        </div>

        {/* Source Agent Info */}
        {sourceAgent && (
          <Alert>
            <AlertDescription>
              <div className="flex items-center gap-2">
                <span className="font-medium">源Agent:</span>
                <span>{sourceAgent.team_name}</span>
                <span className="text-muted-foreground">•</span>
                <span className="text-sm text-muted-foreground">
                  {sourceAgent.description}
                </span>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Error Alert */}
        {error && (
          <Alert variant="destructive">
            <AlertDescription>
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Template Form */}
        <TemplateForm
          mode="create"
          sourceAgent={sourceAgent}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isLoading={loading}
          categories={categories}
          difficulties={difficulties}
          onToast={toast}
        />
      </div>
    </MainLayout>
  );
}

export default function CreateTemplatePage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <CreateTemplatePageContent />
    </Suspense>
  );
}
