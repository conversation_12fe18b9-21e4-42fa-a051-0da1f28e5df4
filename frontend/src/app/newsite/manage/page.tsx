"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  FolderOpen,
  Search,
  Filter,
  Grid3X3,
  List,
  Plus,
  Bot,
  Users,
  TestTube,
  Settings,
  MoreHorizontal,
  Play,
  Edit,
  Trash2,
  Star,
  Zap,
} from "lucide-react";
import Link from "next/link";
import { NewAgentCard } from "@/components/newsite/manage/new-agent-card";
import { NewAgentList } from "@/components/newsite/manage/new-agent-list";

// Mock agents data
const mockAgents = [
  {
    id: "agent_1",
    name: "内容创作团队",
    description: "专业的文案创作和内容策划团队，擅长各类营销文案、技术文档和创意内容的生成",
    status: "active",
    created_at: "2024-01-15T10:30:00Z",
    updated_at: "2024-01-20T14:22:00Z",
    usage_count: 45,
    success_rate: 96,
    last_used: "2小时前",
    team_members: [
      { role: "内容策划师", name: "策划专家" },
      { role: "文案创作者", name: "创作大师" },
      { role: "SEO优化师", name: "优化专家" },
    ],
    tags: ["内容", "营销", "SEO"],
    favorite: true,
  },
  {
    id: "agent_2",
    name: "数据分析专家",
    description: "智能数据处理和洞察分析团队，提供专业的数据可视化和商业智能分析",
    status: "active",
    created_at: "2024-01-10T09:15:00Z",
    updated_at: "2024-01-18T16:45:00Z",
    usage_count: 32,
    success_rate: 92,
    last_used: "1天前",
    team_members: [
      { role: "数据分析师", name: "分析专家" },
      { role: "可视化专家", name: "图表大师" },
    ],
    tags: ["数据", "分析", "BI"],
    favorite: false,
  },
  {
    id: "agent_3",
    name: "客服助手团队",
    description: "24/7智能客户服务支持团队，处理客户咨询、投诉和技术支持",
    status: "inactive",
    created_at: "2024-01-05T14:20:00Z",
    updated_at: "2024-01-12T11:30:00Z",
    usage_count: 18,
    success_rate: 88,
    last_used: "3天前",
    team_members: [
      { role: "客服专员", name: "服务专家" },
      { role: "技术支持", name: "技术大师" },
    ],
    tags: ["客服", "支持"],
    favorite: false,
  },
  {
    id: "agent_4",
    name: "产品开发顾问",
    description: "产品策略和开发指导团队，提供市场调研、功能设计和用户体验优化建议",
    status: "active",
    created_at: "2024-01-08T11:45:00Z",
    updated_at: "2024-01-19T09:15:00Z",
    usage_count: 28,
    success_rate: 94,
    last_used: "6小时前",
    team_members: [
      { role: "产品经理", name: "产品专家" },
      { role: "UX设计师", name: "体验大师" },
      { role: "市场分析师", name: "市场专家" },
    ],
    tags: ["产品", "设计", "UX"],
    favorite: true,
  },
];

type ViewMode = "grid" | "list";
type SortBy = "name" | "created" | "usage" | "success_rate";

export default function NewManageAgentsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [tagFilter, setTagFilter] = useState("all");
  const [viewMode, setViewMode] = useState<ViewMode>("grid");
  const [sortBy, setSortBy] = useState<SortBy>("created");
  const [selectedAgents, setSelectedAgents] = useState<string[]>([]);

  // Get unique tags for filter
  const allTags = Array.from(new Set(mockAgents.flatMap(agent => agent.tags)));

  // Filter and sort agents
  const filteredAgents = mockAgents
    .filter(agent => {
      const matchesSearch = agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           agent.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           agent.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
      const matchesStatus = statusFilter === "all" || agent.status === statusFilter;
      const matchesTag = tagFilter === "all" || agent.tags.includes(tagFilter);
      
      return matchesSearch && matchesStatus && matchesTag;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case "name":
          return a.name.localeCompare(b.name);
        case "created":
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        case "usage":
          return b.usage_count - a.usage_count;
        case "success_rate":
          return b.success_rate - a.success_rate;
        default:
          return 0;
      }
    });

  const handleAgentSelect = (agentId: string) => {
    setSelectedAgents(prev => 
      prev.includes(agentId) 
        ? prev.filter(id => id !== agentId)
        : [...prev, agentId]
    );
  };

  const handleSelectAll = () => {
    setSelectedAgents(
      selectedAgents.length === filteredAgents.length 
        ? [] 
        : filteredAgents.map(agent => agent.id)
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div>
          <h1 className="text-3xl font-bold flex items-center space-x-2">
            <FolderOpen className="h-8 w-8 text-primary" />
            <span>Agent管理</span>
          </h1>
          <p className="text-muted-foreground mt-1">
            管理您的AI团队，监控状态和使用情况
          </p>
        </div>
        <Button asChild className="min-h-[44px] w-full sm:w-auto">
          <Link href="/newsite/create">
            <Plus className="h-4 w-4 mr-2" />
            创建新Agent
          </Link>
        </Button>
      </motion.div>



      {/* Filters and Controls */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索Agent名称、描述或标签..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Filters */}
              <div className="flex flex-col sm:flex-row gap-3">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-full sm:w-32">
                    <SelectValue placeholder="状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部状态</SelectItem>
                    <SelectItem value="active">活跃</SelectItem>
                    <SelectItem value="inactive">未活跃</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={tagFilter} onValueChange={setTagFilter}>
                  <SelectTrigger className="w-full sm:w-32">
                    <SelectValue placeholder="标签" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部标签</SelectItem>
                    {allTags.map(tag => (
                      <SelectItem key={tag} value={tag}>{tag}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={sortBy} onValueChange={(value: SortBy) => setSortBy(value)}>
                  <SelectTrigger className="w-full sm:w-32">
                    <SelectValue placeholder="排序" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="created">创建时间</SelectItem>
                    <SelectItem value="name">名称</SelectItem>
                    <SelectItem value="usage">使用次数</SelectItem>
                    <SelectItem value="success_rate">成功率</SelectItem>
                  </SelectContent>
                </Select>

                {/* View Mode Toggle */}
                <div className="flex border rounded-lg">
                  <Button
                    variant={viewMode === "grid" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("grid")}
                    className="rounded-r-none"
                  >
                    <Grid3X3 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === "list" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("list")}
                    className="rounded-l-none"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Results and Bulk Actions */}
            <div className="flex items-center justify-between mt-4 pt-4 border-t">
              <div className="flex items-center space-x-4">
                <p className="text-sm text-muted-foreground">
                  找到 {filteredAgents.length} 个Agent
                </p>
                {selectedAgents.length > 0 && (
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary">
                      已选择 {selectedAgents.length} 个
                    </Badge>
                    <Button variant="outline" size="sm">
                      批量操作
                    </Button>
                  </div>
                )}
              </div>
              
              {(searchTerm || statusFilter !== "all" || tagFilter !== "all") && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSearchTerm("");
                    setStatusFilter("all");
                    setTagFilter("all");
                  }}
                >
                  清除筛选
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Agent List/Grid */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.3 }}
      >
        {filteredAgents.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                <Bot className="h-8 w-8 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-medium mb-2">
                {mockAgents.length === 0 ? "还没有Agent" : "没有找到匹配的Agent"}
              </h3>
              <p className="text-muted-foreground mb-4">
                {mockAgents.length === 0 
                  ? "创建您的第一个AI团队开始使用吧！" 
                  : "尝试调整搜索条件或筛选器"
                }
              </p>
              {mockAgents.length === 0 && (
                <Button asChild>
                  <Link href="/newsite/create">
                    <Plus className="h-4 w-4 mr-2" />
                    创建第一个Agent
                  </Link>
                </Button>
              )}
            </CardContent>
          </Card>
        ) : viewMode === "grid" ? (
          <NewAgentCard
            agents={filteredAgents}
            selectedAgents={selectedAgents}
            onAgentSelect={handleAgentSelect}
            onSelectAll={handleSelectAll}
          />
        ) : (
          <NewAgentList
            agents={filteredAgents}
            selectedAgents={selectedAgents}
            onAgentSelect={handleAgentSelect}
            onSelectAll={handleSelectAll}
          />
        )}
      </motion.div>
    </div>
  );
}
