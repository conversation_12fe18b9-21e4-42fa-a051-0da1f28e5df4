"use client";

import React, { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  TestTube,
  Play,
  Square,
  RotateCcw,
  Settings,
  History,
  Zap,
} from "lucide-react";
import { NewAgentSelector } from "@/components/newsite/test/new-agent-selector";
import { NewTestForm } from "@/components/newsite/test/new-test-form";
import { NewTestExecution } from "@/components/newsite/test/new-test-execution";
import { NewTestResults } from "@/components/newsite/test/new-test-results";
import { NewTestHistory } from "@/components/newsite/test/new-test-history";

type TestState = "idle" | "running" | "completed" | "error";

interface TestSession {
  id: string;
  agentId: string;
  agentName: string;
  input: string;
  status: TestState;
  startTime: Date;
  endTime?: Date;
  results?: any;
  error?: string;
  stages?: Array<{
    name: string;
    status: "pending" | "running" | "completed" | "error";
    output?: string;
    duration?: number;
  }>;
}

// Mock agents data
const mockAgents = [
  {
    id: "agent_1",
    name: "内容创作团队",
    description: "专业的文案创作和内容策划",
    status: "active",
    team_members: [
      { role: "内容策划师", name: "策划专家" },
      { role: "文案创作者", name: "创作大师" },
      { role: "SEO优化师", name: "优化专家" },
    ],
  },
  {
    id: "agent_2",
    name: "数据分析专家",
    description: "智能数据处理和洞察分析",
    status: "active",
    team_members: [
      { role: "数据分析师", name: "分析专家" },
      { role: "可视化专家", name: "图表大师" },
    ],
  },
];

export default function NewTestAgentPage() {
  const searchParams = useSearchParams();
  const preselectedAgentId = searchParams.get("agent");

  const [selectedAgent, setSelectedAgent] = useState<any>(null);
  const [currentSession, setCurrentSession] = useState<TestSession | null>(null);
  const [testHistory, setTestHistory] = useState<TestSession[]>([]);
  const [activeTab, setActiveTab] = useState("test");

  // Initialize with preselected agent
  useEffect(() => {
    if (preselectedAgentId) {
      const agent = mockAgents.find(a => a.id === preselectedAgentId);
      if (agent) {
        setSelectedAgent(agent);
      }
    }
  }, [preselectedAgentId]);

  const handleAgentSelect = (agent: any) => {
    setSelectedAgent(agent);
    setCurrentSession(null);
  };

  const handleTestStart = async (input: string, config?: any) => {
    if (!selectedAgent) return;

    const session: TestSession = {
      id: `test_${Date.now()}`,
      agentId: selectedAgent.id,
      agentName: selectedAgent.name,
      input,
      status: "running",
      startTime: new Date(),
      stages: [
        { name: "初始化", status: "running" },
        { name: "团队协作", status: "pending" },
        { name: "结果整合", status: "pending" },
        { name: "质量检查", status: "pending" },
      ],
    };

    setCurrentSession(session);
    setActiveTab("execution");

    // Simulate test execution
    await simulateTestExecution(session);
  };

  const simulateTestExecution = async (session: TestSession) => {
    const stages = session.stages || [];
    
    for (let i = 0; i < stages.length; i++) {
      // Update current stage to running
      const updatedStages = [...stages];
      updatedStages[i] = { ...updatedStages[i], status: "running" };
      
      setCurrentSession(prev => prev ? {
        ...prev,
        stages: updatedStages
      } : null);

      // Simulate stage duration
      await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 2000));

      // Complete current stage
      updatedStages[i] = {
        ...updatedStages[i],
        status: "completed",
        output: `Stage ${i + 1} completed successfully`,
        duration: Math.floor(1000 + Math.random() * 3000),
      };

      setCurrentSession(prev => prev ? {
        ...prev,
        stages: updatedStages
      } : null);

      // Start next stage if exists
      if (i < stages.length - 1) {
        updatedStages[i + 1] = { ...updatedStages[i + 1], status: "running" };
        setCurrentSession(prev => prev ? {
          ...prev,
          stages: updatedStages
        } : null);
      }
    }

    // Complete the test
    const completedSession: TestSession = {
      ...session,
      status: "completed",
      endTime: new Date(),
      stages,
      results: {
        output: "这是一个示例输出结果。AI团队已经成功完成了您的任务，生成了高质量的内容。",
        metadata: {
          totalDuration: 8500,
          tokensUsed: 1250,
          cost: 0.025,
        },
      },
    };

    setCurrentSession(completedSession);
    setTestHistory(prev => [completedSession, ...prev]);
    setActiveTab("results");
  };

  const handleTestStop = () => {
    if (currentSession && currentSession.status === "running") {
      setCurrentSession(prev => prev ? {
        ...prev,
        status: "error",
        endTime: new Date(),
        error: "Test stopped by user",
      } : null);
    }
  };

  const handleTestReset = () => {
    setCurrentSession(null);
    setActiveTab("test");
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <motion.div
        className="text-center space-y-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="flex items-center justify-center space-x-2">
          <TestTube className="h-8 w-8 text-primary" />
          <h1 className="text-3xl font-bold">Agent测试中心</h1>
        </div>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          实时测试您的AI团队，查看执行过程和结果
        </p>
      </motion.div>

      {/* Agent Selection */}
      {!selectedAgent && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          <NewAgentSelector
            agents={mockAgents}
            onSelect={handleAgentSelect}
          />
        </motion.div>
      )}

      {/* Main Testing Interface */}
      {selectedAgent && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-4 mobile-tabs">
              <TabsTrigger value="test" className="flex items-center justify-center space-x-1 sm:space-x-2 px-1 sm:px-2">
                <TestTube className="h-4 w-4 flex-shrink-0" />
                <span className="truncate text-xs sm:text-sm">测试</span>
              </TabsTrigger>
              <TabsTrigger value="execution" disabled={!currentSession} className="flex items-center justify-center space-x-1 sm:space-x-2 px-1 sm:px-2">
                <Zap className="h-4 w-4 flex-shrink-0" />
                <span className="truncate text-xs sm:text-sm">执行</span>
              </TabsTrigger>
              <TabsTrigger value="results" disabled={!currentSession?.results} className="flex items-center justify-center space-x-1 sm:space-x-2 px-1 sm:px-2">
                <Settings className="h-4 w-4 flex-shrink-0" />
                <span className="truncate text-xs sm:text-sm">结果</span>
              </TabsTrigger>
              <TabsTrigger value="history" className="flex items-center justify-center space-x-1 sm:space-x-2 px-1 sm:px-2">
                <History className="h-4 w-4 flex-shrink-0" />
                <span className="truncate text-xs sm:text-sm">历史</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="test" className="w-full space-y-6">
              <NewTestForm
                agent={selectedAgent}
                onStart={handleTestStart}
                onAgentChange={() => setSelectedAgent(null)}
                disabled={currentSession?.status === "running"}
              />
            </TabsContent>

            <TabsContent value="execution" className="w-full space-y-6">
              {currentSession && (
                <NewTestExecution
                  session={currentSession}
                  onStop={handleTestStop}
                  onReset={handleTestReset}
                />
              )}
            </TabsContent>

            <TabsContent value="results" className="w-full space-y-6">
              {currentSession?.results && (
                <NewTestResults
                  session={currentSession}
                  onNewTest={handleTestReset}
                />
              )}
            </TabsContent>

            <TabsContent value="history" className="w-full space-y-6">
              <NewTestHistory
                history={testHistory}
                onRerun={(session) => {
                  setSelectedAgent(mockAgents.find(a => a.id === session.agentId));
                  setActiveTab("test");
                }}
              />
            </TabsContent>
          </Tabs>
        </motion.div>
      )}
    </div>
  );
}
