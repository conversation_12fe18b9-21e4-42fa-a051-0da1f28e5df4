"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Sparkles,
  ArrowLeft,
  ArrowRight,
  CheckCircle,
  Clock,
  AlertCircle,
  Bot,
  Users,
  Zap,
} from "lucide-react";
import Link from "next/link";
import { NewAgentForm } from "@/components/newsite/create/new-agent-form";
import { NewPlanningProgress } from "@/components/newsite/create/new-planning-progress";
import { NewPlanReview } from "@/components/newsite/create/new-plan-review";
import { NewCreationSuccess } from "@/components/newsite/create/new-creation-success";

type CreationStep = "form" | "planning" | "review" | "creating" | "success";

interface CreationState {
  step: CreationStep;
  description: string;
  plan: any;
  agent: any;
  error: string | null;
  loading: boolean;
}

export default function NewCreateAgentPage() {
  const [state, setState] = useState<CreationState>({
    step: "form",
    description: "",
    plan: null,
    agent: null,
    error: null,
    loading: false,
  });

  const steps = [
    { id: "form", title: "描述需求", description: "告诉我们您想要什么样的AI团队" },
    { id: "planning", title: "AI规划", description: "AI正在为您设计专业团队" },
    { id: "review", title: "确认方案", description: "查看并确认团队配置" },
    { id: "creating", title: "创建中", description: "正在生成您的AI团队" },
    { id: "success", title: "完成", description: "您的AI团队已准备就绪" },
  ];

  const currentStepIndex = steps.findIndex(step => step.id === state.step);
  const progress = ((currentStepIndex + 1) / steps.length) * 100;

  const handleFormSubmit = async (formData: { description: string; options?: any }) => {
    setState(prev => ({
      ...prev,
      description: formData.description,
      step: "planning",
      loading: true,
      error: null,
    }));

    // Simulate AI planning process
    setTimeout(() => {
      setState(prev => ({
        ...prev,
        step: "review",
        loading: false,
        plan: {
          team_name: "智能内容创作团队",
          description: "专业的内容创作和策划团队",
          team_members: [
            { role: "内容策划师", name: "策划专家", description: "负责内容策略和规划" },
            { role: "文案创作者", name: "创作大师", description: "负责高质量文案创作" },
            { role: "SEO优化师", name: "优化专家", description: "负责搜索引擎优化" },
          ],
          workflow: [
            "分析用户需求和目标受众",
            "制定内容策略和创作计划",
            "创作高质量原创内容",
            "优化内容SEO和可读性",
            "输出最终内容成果",
          ],
        },
      }));
    }, 3000);
  };

  const handlePlanConfirm = async () => {
    setState(prev => ({
      ...prev,
      step: "creating",
      loading: true,
    }));

    // Simulate agent creation process
    setTimeout(() => {
      setState(prev => ({
        ...prev,
        step: "success",
        loading: false,
        agent: {
          id: "agent_" + Date.now(),
          name: prev.plan.team_name,
          description: prev.plan.description,
          status: "active",
        },
      }));
    }, 2000);
  };

  const handlePlanReject = () => {
    setState(prev => ({
      ...prev,
      step: "form",
      plan: null,
      error: null,
    }));
  };

  const handleStartOver = () => {
    setState({
      step: "form",
      description: "",
      plan: null,
      agent: null,
      error: null,
      loading: false,
    });
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <motion.div
        className="text-center space-y-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="flex items-center justify-center space-x-2">
          <Sparkles className="h-8 w-8 text-primary" />
          <h1 className="text-3xl font-bold">创建AI Agent团队</h1>
        </div>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          通过AI驱动的智能规划，为您量身定制专业的AI助手团队
        </p>
      </motion.div>

      {/* Progress Indicator */}
      <motion.div
        className="space-y-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1 }}
      >
        <div className="flex items-center justify-between text-sm">
          <span className="font-medium">创建进度</span>
          <span className="text-muted-foreground">{Math.round(progress)}%</span>
        </div>
        <Progress value={progress} className="h-2" />
        
        {/* Step Indicators */}
        <div className="flex justify-between">
          {steps.map((step, index) => (
            <div
              key={step.id}
              className={`flex flex-col items-center space-y-2 ${
                index <= currentStepIndex ? "text-primary" : "text-muted-foreground"
              }`}
            >
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${
                  index < currentStepIndex
                    ? "bg-primary text-primary-foreground"
                    : index === currentStepIndex
                    ? "bg-primary/20 text-primary border-2 border-primary"
                    : "bg-muted text-muted-foreground"
                }`}
              >
                {index < currentStepIndex ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  index + 1
                )}
              </div>
              <div className="text-center hidden sm:block">
                <div className="font-medium text-xs">{step.title}</div>
                <div className="text-xs text-muted-foreground max-w-20">
                  {step.description}
                </div>
              </div>
            </div>
          ))}
        </div>
      </motion.div>

      {/* Main Content */}
      <motion.div
        key={state.step}
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: -20 }}
        transition={{ duration: 0.4 }}
      >
        {state.step === "form" && (
          <NewAgentForm
            onSubmit={handleFormSubmit}
            initialDescription={state.description}
            disabled={state.loading}
          />
        )}

        {state.step === "planning" && (
          <NewPlanningProgress
            description={state.description}
            onCancel={() => setState(prev => ({ ...prev, step: "form" }))}
          />
        )}

        {state.step === "review" && state.plan && (
          <NewPlanReview
            plan={state.plan}
            description={state.description}
            onConfirm={handlePlanConfirm}
            onReject={handlePlanReject}
            loading={state.loading}
          />
        )}

        {state.step === "creating" && (
          <NewPlanningProgress
            description="正在创建您的AI团队..."
            progress={75}
            onCancel={() => setState(prev => ({ ...prev, step: "form" }))}
          />
        )}

        {state.step === "success" && state.agent && (
          <NewCreationSuccess
            agent={state.agent}
            onStartOver={handleStartOver}
          />
        )}
      </motion.div>

      {/* Navigation */}
      <motion.div
        className="flex justify-between items-center pt-6 border-t"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.3 }}
      >
        <Button variant="outline" asChild>
          <Link href="/newsite">
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回首页
          </Link>
        </Button>
        
        {state.step === "success" && (
          <div className="flex space-x-3">
            <Button variant="outline" asChild>
              <Link href="/newsite/manage">
                查看所有Agent
              </Link>
            </Button>
            <Button asChild>
              <Link href={`/newsite/test?agent=${state.agent?.id}`}>
                立即测试
                <ArrowRight className="h-4 w-4 ml-2" />
              </Link>
            </Button>
          </div>
        )}
      </motion.div>
    </div>
  );
}
