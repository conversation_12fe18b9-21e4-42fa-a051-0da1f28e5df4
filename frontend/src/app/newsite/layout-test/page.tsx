"use client";

import React from "react";
import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { 
  Monitor, 
  Tablet, 
  Smartphone, 
  Layout, 
  Sidebar,
  Eye,
  CheckCircle,
  AlertTriangle
} from "lucide-react";

export default function LayoutTestPage() {
  return (
    <div className="space-y-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold flex items-center justify-center space-x-2">
          <Layout className="h-8 w-8 text-primary" />
          <span>Layout Test - Fixed!</span>
        </h1>
        <p className="text-muted-foreground">
          Layout now uses the proven working pattern from the original interface
        </p>
        <div className="bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-center justify-center space-x-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            <span className="font-medium text-green-800 dark:text-green-200">
              Layout Fixed: Using Original Working Pattern
            </span>
          </div>
          <p className="text-sm text-green-700 dark:text-green-300 mt-2 text-center">
            Simplified structure following MainLayout pattern - no more content overlap!
          </p>
        </div>
      </div>

      {/* Responsive Test Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Mobile Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Smartphone className="h-5 w-5 text-blue-500" />
              <span>Mobile (&lt;768px)</span>
            </CardTitle>
            <CardDescription>
              Sidebar should be overlay, content full width
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="text-sm">Sidebar overlay</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="text-sm">Content full width</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="text-sm">44px touch targets</span>
              </div>
            </div>
            <Badge variant="outline" className="w-full justify-center">
              Mobile Optimized
            </Badge>
          </CardContent>
        </Card>

        {/* Tablet Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Tablet className="h-5 w-5 text-orange-500" />
              <span>Tablet (768px-1199px)</span>
            </CardTitle>
            <CardDescription>
              Sidebar persistent, content adjusts
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="text-sm">Sidebar persistent</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="text-sm">Content adjusts width</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="text-sm">Collapsible sidebar</span>
              </div>
            </div>
            <Badge variant="outline" className="w-full justify-center">
              Tablet Ready
            </Badge>
          </CardContent>
        </Card>

        {/* Desktop Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Monitor className="h-5 w-5 text-purple-500" />
              <span>Desktop (1200px+)</span>
            </CardTitle>
            <CardDescription>
              Full layout with optimal spacing
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="text-sm">Full sidebar</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="text-sm">Optimal content width</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="text-sm">Icon collapse mode</span>
              </div>
            </div>
            <Badge variant="outline" className="w-full justify-center">
              Desktop Optimized
            </Badge>
          </CardContent>
        </Card>
      </div>

      {/* Layout Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Eye className="h-5 w-5 text-primary" />
            <span>Current Layout Information</span>
          </CardTitle>
          <CardDescription>
            Real-time layout debugging information
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium">Screen Width</h4>
              <div className="text-2xl font-bold text-blue-600">
                <span className="block sm:hidden">XS (&lt;640px)</span>
                <span className="hidden sm:block md:hidden">SM (640px+)</span>
                <span className="hidden md:block lg:hidden">MD (768px+)</span>
                <span className="hidden lg:block xl:hidden">LG (1024px+)</span>
                <span className="hidden xl:block 2xl:hidden">XL (1280px+)</span>
                <span className="hidden 2xl:block">2XL (1536px+)</span>
              </div>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">Sidebar State</h4>
              <div className="text-2xl font-bold text-green-600">
                <span className="block md:hidden">Overlay</span>
                <span className="hidden md:block">Persistent</span>
              </div>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">Content Width</h4>
              <div className="text-2xl font-bold text-purple-600">
                <span className="block md:hidden">100%</span>
                <span className="hidden md:block">Adjusted</span>
              </div>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">Touch Targets</h4>
              <div className="text-2xl font-bold text-orange-600">
                <span className="block md:hidden">44px</span>
                <span className="hidden md:block">36px</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Content */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Sidebar className="h-5 w-5 text-primary" />
            <span>Content Overflow Test</span>
          </CardTitle>
          <CardDescription>
            Test that content doesn't overlap with sidebar
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              This content should never be hidden behind or overlap with the sidebar. 
              The layout should automatically adjust the content area width based on 
              the sidebar state and screen size.
            </p>
            
            <div className="bg-muted/50 p-4 rounded-lg">
              <h4 className="font-medium mb-2">Wide Content Test</h4>
              <div className="w-full bg-gradient-to-r from-blue-500 to-purple-500 h-4 rounded"></div>
              <p className="text-xs text-muted-foreground mt-2">
                This colored bar should always be fully visible and never extend behind the sidebar.
              </p>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {Array.from({ length: 8 }, (_, i) => (
                <div key={i} className="bg-muted/30 p-3 rounded text-center">
                  <div className="w-full h-16 bg-primary/20 rounded mb-2"></div>
                  <p className="text-sm">Item {i + 1}</p>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Layout Structure Comparison */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CheckCircle className="h-5 w-5 text-green-500" />
            <span>Layout Structure Fixed</span>
          </CardTitle>
          <CardDescription>
            Comparison of broken vs. fixed layout structure
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-3">
              <h4 className="font-medium text-red-600 flex items-center space-x-2">
                <AlertTriangle className="h-4 w-4" />
                <span>Previous (Broken)</span>
              </h4>
              <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded p-3 text-sm font-mono">
                <div>SidebarProvider</div>
                <div className="ml-2">└─ div.min-h-screen</div>
                <div className="ml-4">├─ NewSidebar</div>
                <div className="ml-4">└─ SidebarInset.flex-col</div>
                <div className="ml-6">├─ NewHeader</div>
                <div className="ml-6">└─ main.flex-1</div>
                <div className="ml-8">└─ div.container.mx-auto</div>
                <div className="ml-10">└─ children</div>
              </div>
              <p className="text-xs text-red-600">
                ❌ Complex nested structure with custom CSS overrides
              </p>
            </div>

            <div className="space-y-3">
              <h4 className="font-medium text-green-600 flex items-center space-x-2">
                <CheckCircle className="h-4 w-4" />
                <span>Current (Fixed)</span>
              </h4>
              <div className="bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded p-3 text-sm font-mono">
                <div>SidebarProvider</div>
                <div className="ml-2">├─ NewSidebar</div>
                <div className="ml-2">└─ SidebarInset</div>
                <div className="ml-4">├─ NewHeader</div>
                <div className="ml-4">└─ div.flex.flex-1.flex-col</div>
                <div className="ml-6">└─ children</div>
              </div>
              <p className="text-xs text-green-600">
                ✅ Simple structure following original working pattern
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row justify-center gap-4">
        <Button variant="outline" className="min-h-[44px]">
          <Eye className="h-4 w-4 mr-2" />
          Inspect Layout
        </Button>
        <Button className="min-h-[44px]">
          <CheckCircle className="h-4 w-4 mr-2" />
          Layout Test Passed
        </Button>
      </div>
    </div>
  );
}
