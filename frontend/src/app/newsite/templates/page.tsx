"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  BookTemplate,
  Search,
  Filter,
  Star,
  Users,
  Zap,
  Download,
  Play,
  Plus,
  TrendingUp,
} from "lucide-react";
import Link from "next/link";

// Mock templates data
const mockTemplates = [
  {
    id: "template_1",
    name: "内容创作专家团队",
    description: "专业的内容创作和营销文案团队，包含策划师、创作者和SEO专家，适用于各类内容营销场景",
    category: "创作",
    difficulty: "中级",
    team_size: 3,
    usage_count: 156,
    rating: 4.8,
    tags: ["内容", "营销", "SEO", "文案"],
    author: "Meta-Agent",
    featured: true,
    workflow_steps: 5,
  },
  {
    id: "template_2",
    name: "数据分析智能团队",
    description: "专业的数据处理和商业智能分析团队，提供数据清洗、分析和可视化服务",
    category: "分析",
    difficulty: "高级",
    team_size: 2,
    usage_count: 89,
    rating: 4.6,
    tags: ["数据", "分析", "BI", "可视化"],
    author: "Meta-Agent",
    featured: true,
    workflow_steps: 4,
  },
  {
    id: "template_3",
    name: "客服支持助手",
    description: "24/7智能客户服务团队，处理客户咨询、投诉和技术支持，提升客户满意度",
    category: "服务",
    difficulty: "初级",
    team_size: 2,
    usage_count: 234,
    rating: 4.9,
    tags: ["客服", "支持", "自动化"],
    author: "Meta-Agent",
    featured: false,
    workflow_steps: 3,
  },
  {
    id: "template_4",
    name: "产品开发顾问团",
    description: "产品策略和开发指导团队，提供市场调研、功能设计和用户体验优化建议",
    category: "开发",
    difficulty: "高级",
    team_size: 3,
    usage_count: 67,
    rating: 4.7,
    tags: ["产品", "设计", "UX", "策略"],
    author: "Meta-Agent",
    featured: false,
    workflow_steps: 6,
  },
  {
    id: "template_5",
    name: "教育培训助手",
    description: "专业的教育内容开发和培训支持团队，适用于在线教育和企业培训场景",
    category: "教育",
    difficulty: "中级",
    team_size: 3,
    usage_count: 123,
    rating: 4.5,
    tags: ["教育", "培训", "课程"],
    author: "Meta-Agent",
    featured: true,
    workflow_steps: 4,
  },
  {
    id: "template_6",
    name: "法律咨询专家",
    description: "专业的法律咨询和文档审查团队，提供合同审查、法律建议和风险评估",
    category: "法律",
    difficulty: "高级",
    team_size: 2,
    usage_count: 45,
    rating: 4.4,
    tags: ["法律", "咨询", "合同"],
    author: "Meta-Agent",
    featured: false,
    workflow_steps: 5,
  },
];

const categories = ["全部", "创作", "分析", "服务", "开发", "教育", "法律"];
const difficulties = ["全部", "初级", "中级", "高级"];

export default function NewTemplatesPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("全部");
  const [difficultyFilter, setDifficultyFilter] = useState("全部");
  const [sortBy, setSortBy] = useState("featured");

  // Filter and sort templates
  const filteredTemplates = mockTemplates
    .filter(template => {
      const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           template.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
      const matchesCategory = categoryFilter === "全部" || template.category === categoryFilter;
      const matchesDifficulty = difficultyFilter === "全部" || template.difficulty === difficultyFilter;
      
      return matchesSearch && matchesCategory && matchesDifficulty;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case "featured":
          return (b.featured ? 1 : 0) - (a.featured ? 1 : 0) || b.usage_count - a.usage_count;
        case "popular":
          return b.usage_count - a.usage_count;
        case "rating":
          return b.rating - a.rating;
        case "name":
          return a.name.localeCompare(b.name);
        default:
          return 0;
      }
    });

  const featuredTemplates = mockTemplates.filter(template => template.featured);

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div>
          <h1 className="text-3xl font-bold flex items-center space-x-2">
            <BookTemplate className="h-8 w-8 text-primary" />
            <span>模板库</span>
          </h1>
          <p className="text-muted-foreground mt-1">
            发现和使用专业的AI团队模板，快速启动您的项目
          </p>
        </div>
        <Button asChild className="min-h-[44px] w-full sm:w-auto">
          <Link href="/newsite/create">
            <Plus className="h-4 w-4 mr-2" />
            创建自定义Agent
          </Link>
        </Button>
      </motion.div>

      {/* Featured Templates */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Star className="h-5 w-5 text-yellow-500" />
              <span>精选模板</span>
            </CardTitle>
            <CardDescription>
              热门和推荐的AI团队模板
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {featuredTemplates.slice(0, 3).map((template, index) => (
                <motion.div
                  key={template.id}
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                >
                  <Card className="hover:shadow-md transition-shadow cursor-pointer group">
                    <CardContent className="p-4">
                      <div className="space-y-3">
                        <div className="flex items-start justify-between">
                          <Badge variant="secondary" className="text-xs">
                            {template.category}
                          </Badge>
                          <div className="flex items-center space-x-1">
                            <Star className="h-3 w-3 text-yellow-500 fill-current" />
                            <span className="text-xs font-medium">{template.rating}</span>
                          </div>
                        </div>
                        <div>
                          <h3 className="font-semibold group-hover:text-primary transition-colors">
                            {template.name}
                          </h3>
                          <p className="text-sm text-muted-foreground line-clamp-2 mt-1">
                            {template.description}
                          </p>
                        </div>
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <div className="flex items-center space-x-3">
                            <span className="flex items-center space-x-1">
                              <Users className="h-3 w-3" />
                              <span>{template.team_size} 成员</span>
                            </span>
                            <span className="flex items-center space-x-1">
                              <Download className="h-3 w-3" />
                              <span>{template.usage_count}</span>
                            </span>
                          </div>
                          <Button size="sm" className="h-7">
                            <Play className="h-3 w-3 mr-1" />
                            使用
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Filters */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索模板名称、描述或标签..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex flex-col sm:flex-row gap-3">
                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger className="w-full sm:w-32">
                    <SelectValue placeholder="分类" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map(category => (
                      <SelectItem key={category} value={category}>{category}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select value={difficultyFilter} onValueChange={setDifficultyFilter}>
                  <SelectTrigger className="w-full sm:w-32">
                    <SelectValue placeholder="难度" />
                  </SelectTrigger>
                  <SelectContent>
                    {difficulties.map(difficulty => (
                      <SelectItem key={difficulty} value={difficulty}>{difficulty}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-full sm:w-32">
                    <SelectValue placeholder="排序" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="featured">推荐</SelectItem>
                    <SelectItem value="popular">热门</SelectItem>
                    <SelectItem value="rating">评分</SelectItem>
                    <SelectItem value="name">名称</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="flex items-center justify-between mt-4 pt-4 border-t">
              <p className="text-sm text-muted-foreground">
                找到 {filteredTemplates.length} 个模板
              </p>
              {(searchTerm || categoryFilter !== "全部" || difficultyFilter !== "全部") && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSearchTerm("");
                    setCategoryFilter("全部");
                    setDifficultyFilter("全部");
                  }}
                >
                  清除筛选
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Template Grid */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.3 }}
      >
        {filteredTemplates.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                <BookTemplate className="h-8 w-8 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-medium mb-2">没有找到匹配的模板</h3>
              <p className="text-muted-foreground mb-4">
                尝试调整搜索条件或筛选器，或者创建一个自定义Agent
              </p>
              <Button asChild>
                <Link href="/newsite/create">
                  <Plus className="h-4 w-4 mr-2" />
                  创建自定义Agent
                </Link>
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTemplates.map((template, index) => (
              <motion.div
                key={template.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.05 }}
              >
                <Card className="hover:shadow-lg transition-all duration-200 hover:border-primary/50 group h-full">
                  <CardContent className="p-6 flex flex-col h-full">
                    <div className="space-y-4 flex-1">
                      {/* Header */}
                      <div className="flex items-start justify-between">
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline" className="text-xs">
                            {template.category}
                          </Badge>
                          <Badge 
                            variant={template.difficulty === "初级" ? "secondary" : 
                                   template.difficulty === "中级" ? "default" : "destructive"}
                            className="text-xs"
                          >
                            {template.difficulty}
                          </Badge>
                        </div>
                        {template.featured && (
                          <Star className="h-4 w-4 text-yellow-500 fill-current" />
                        )}
                      </div>

                      {/* Content */}
                      <div>
                        <h3 className="font-semibold text-lg group-hover:text-primary transition-colors">
                          {template.name}
                        </h3>
                        <p className="text-sm text-muted-foreground line-clamp-3 mt-2">
                          {template.description}
                        </p>
                      </div>

                      {/* Tags */}
                      <div className="flex flex-wrap gap-1">
                        {template.tags.slice(0, 3).map((tag, idx) => (
                          <Badge key={idx} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                        {template.tags.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{template.tags.length - 3}
                          </Badge>
                        )}
                      </div>

                      {/* Stats */}
                      <div className="grid grid-cols-3 gap-3 text-center text-sm">
                        <div className="space-y-1">
                          <div className="flex items-center justify-center space-x-1">
                            <Users className="h-3 w-3 text-blue-500" />
                            <span className="font-medium">{template.team_size}</span>
                          </div>
                          <p className="text-xs text-muted-foreground">成员</p>
                        </div>
                        <div className="space-y-1">
                          <div className="flex items-center justify-center space-x-1">
                            <Download className="h-3 w-3 text-green-500" />
                            <span className="font-medium">{template.usage_count}</span>
                          </div>
                          <p className="text-xs text-muted-foreground">使用</p>
                        </div>
                        <div className="space-y-1">
                          <div className="flex items-center justify-center space-x-1">
                            <Star className="h-3 w-3 text-yellow-500" />
                            <span className="font-medium">{template.rating}</span>
                          </div>
                          <p className="text-xs text-muted-foreground">评分</p>
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center space-x-2 pt-4 mt-auto">
                      <Button className="flex-1">
                        <Play className="h-4 w-4 mr-2" />
                        立即使用
                      </Button>
                      <Button variant="outline" size="sm" className="px-3">
                        预览
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        )}
      </motion.div>
    </div>
  );
}
