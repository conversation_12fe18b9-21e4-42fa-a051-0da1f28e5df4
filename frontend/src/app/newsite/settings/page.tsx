"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Settings,
  Save,
  RotateCcw,
  Shield,
  Zap,
  Globe,
  Bell,
} from "lucide-react";

export default function NewSettingsPage() {
  const [customProvider, setCustomProvider] = useState("");
  const [customModel, setCustomModel] = useState("");
  const [customBaseUrl, setCustomBaseUrl] = useState("");
  const [api<PERSON><PERSON>, setApiKey] = useState("");
  const [temperature, setTemperature] = useState("0.7");
  const [maxTokens, setMaxTokens] = useState("4000");

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div>
          <h1 className="text-3xl font-bold flex items-center space-x-2">
            <Settings className="h-8 w-8 text-primary" />
            <span>系统设置</span>
          </h1>
          <p className="text-muted-foreground mt-1">
            配置系统级设置和偏好
          </p>
        </div>
      </motion.div>

      {/* AI Configuration */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Zap className="h-5 w-5 text-primary" />
              <span>AI配置</span>
            </CardTitle>
            <CardDescription>
              配置AI团队生成的默认设置
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="custom-provider">自定义AI提供商名称</Label>
                <Input
                  id="custom-provider"
                  value={customProvider}
                  onChange={(e) => setCustomProvider(e.target.value)}
                  placeholder="例如：OpenAI, Anthropic, Azure OpenAI"
                />
                <p className="text-xs text-muted-foreground">
                  输入您使用的AI提供商名称
                </p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="custom-model">自定义模型名称</Label>
                <Input
                  id="custom-model"
                  value={customModel}
                  onChange={(e) => setCustomModel(e.target.value)}
                  placeholder="例如：gpt-4, claude-3-opus, gpt-4-turbo"
                />
                <p className="text-xs text-muted-foreground">
                  输入具体的模型名称，支持任何自定义模型
                </p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="custom-base-url">自定义基础URL</Label>
                <Input
                  id="custom-base-url"
                  value={customBaseUrl}
                  onChange={(e) => setCustomBaseUrl(e.target.value)}
                  placeholder="例如：https://api.openai.com/v1"
                />
                <p className="text-xs text-muted-foreground">
                  API端点地址，支持代理服务器或自托管解决方案
                </p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="api-key">API密钥</Label>
                <Input
                  id="api-key"
                  type="password"
                  value={apiKey}
                  onChange={(e) => setApiKey(e.target.value)}
                  placeholder="输入您的API密钥"
                />
                <p className="text-xs text-muted-foreground">
                  用于访问AI服务的密钥，将安全存储
                </p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="temperature">创造性 (Temperature)</Label>
                <Input
                  id="temperature"
                  type="number"
                  min="0"
                  max="2"
                  step="0.1"
                  value={temperature}
                  onChange={(e) => setTemperature(e.target.value)}
                  placeholder="0.7"
                />
                <p className="text-xs text-muted-foreground">
                  0.0 = 确定性，1.0 = 平衡，2.0 = 创造性
                </p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="max-tokens">最大Token数</Label>
                <Input
                  id="max-tokens"
                  type="number"
                  min="100"
                  max="10000"
                  value={maxTokens}
                  onChange={(e) => setMaxTokens(e.target.value)}
                  placeholder="4000"
                />
                <p className="text-xs text-muted-foreground">
                  单次请求的最大token限制
                </p>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>启用AI团队生成</Label>
                <p className="text-sm text-muted-foreground">
                  允许使用AI自动生成Agent团队
                </p>
              </div>
              <Switch defaultChecked />
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* System Configuration */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Globe className="h-5 w-5 text-primary" />
              <span>系统配置</span>
            </CardTitle>
            <CardDescription>
              基本系统设置和API配置
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="api-base-url">API基础URL</Label>
                <Input
                  id="api-base-url"
                  defaultValue="http://localhost:8000"
                  placeholder="http://localhost:8000"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="agent-endpoint-prefix">Agent端点前缀</Label>
                <Input
                  id="agent-endpoint-prefix"
                  defaultValue="/api/v1/agents"
                  placeholder="/api/v1/agents"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="default-language">默认语言</Label>
                <Select defaultValue="zh-CN">
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="zh-CN">中文 (简体)</SelectItem>
                    <SelectItem value="en-US">English (US)</SelectItem>
                    <SelectItem value="ja-JP">日本語</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="timezone">时区</Label>
                <Select defaultValue="Asia/Shanghai">
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Asia/Shanghai">Asia/Shanghai</SelectItem>
                    <SelectItem value="UTC">UTC</SelectItem>
                    <SelectItem value="America/New_York">America/New_York</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Security Settings */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.3 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-primary" />
              <span>安全设置</span>
            </CardTitle>
            <CardDescription>
              配置安全和访问控制选项
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>启用API密钥加密</Label>
                  <p className="text-sm text-muted-foreground">
                    在数据库中加密存储API密钥
                  </p>
                </div>
                <Switch defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>启用访问日志</Label>
                  <p className="text-sm text-muted-foreground">
                    记录所有API访问和操作日志
                  </p>
                </div>
                <Switch defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>启用速率限制</Label>
                  <p className="text-sm text-muted-foreground">
                    限制API调用频率以防止滥用
                  </p>
                </div>
                <Switch defaultChecked />
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Notification Settings */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Bell className="h-5 w-5 text-primary" />
              <span>通知设置</span>
            </CardTitle>
            <CardDescription>
              配置系统通知和警报
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Agent创建通知</Label>
                  <p className="text-sm text-muted-foreground">
                    Agent创建完成时发送通知
                  </p>
                </div>
                <Switch defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>测试完成通知</Label>
                  <p className="text-sm text-muted-foreground">
                    Agent测试完成时发送通知
                  </p>
                </div>
                <Switch defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>错误警报</Label>
                  <p className="text-sm text-muted-foreground">
                    系统错误时发送警报通知
                  </p>
                </div>
                <Switch defaultChecked />
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Action Buttons */}
      <motion.div
        className="flex flex-col sm:flex-row justify-end gap-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.5 }}
      >
        <Button variant="outline" className="min-h-[44px]">
          <RotateCcw className="h-4 w-4 mr-2" />
          重置为默认
        </Button>
        <Button className="min-h-[44px]">
          <Save className="h-4 w-4 mr-2" />
          保存设置
        </Button>
      </motion.div>
    </div>
  );
}
