"use client";

import React from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Key,
  Plus,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  Copy,
  MoreHorizontal,
} from "lucide-react";

// Mock API keys data
const mockApiKeys = [
  {
    id: "1",
    name: "OpenAI Production Key",
    key_value: "sk-proj-***************************",
    created_at: "2024-01-15T10:30:00Z",
    last_used: "2小时前",
    usage_count: 156,
    status: "active",
  },
  {
    id: "2",
    name: "Claude Development Key",
    key_value: "sk-ant-***************************",
    created_at: "2024-01-10T09:15:00Z",
    last_used: "1天前",
    usage_count: 89,
    status: "active",
  },
  {
    id: "3",
    name: "Backup OpenAI Key",
    key_value: "sk-proj-***************************",
    created_at: "2024-01-05T14:20:00Z",
    last_used: "从未使用",
    usage_count: 0,
    status: "inactive",
  },
];

export default function NewApiKeysPage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div>
          <h1 className="text-3xl font-bold flex items-center space-x-2">
            <Key className="h-8 w-8 text-primary" />
            <span>API密钥管理</span>
          </h1>
          <p className="text-muted-foreground mt-1">
            管理您的AI服务API密钥，确保安全访问
          </p>
        </div>
        <Button className="min-h-[44px] w-full sm:w-auto">
          <Plus className="h-4 w-4 mr-2" />
          添加新密钥
        </Button>
      </motion.div>

      {/* Stats */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-3 gap-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1 }}
      >
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Key className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-2xl font-bold">{mockApiKeys.length}</p>
                <p className="text-sm text-muted-foreground">总密钥数</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <div className="w-5 h-5 bg-green-500 rounded-full" />
              <div>
                <p className="text-2xl font-bold">
                  {mockApiKeys.filter(key => key.status === "active").length}
                </p>
                <p className="text-sm text-muted-foreground">活跃密钥</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <div className="w-5 h-5 bg-orange-500 rounded-full" />
              <div>
                <p className="text-2xl font-bold">
                  {mockApiKeys.reduce((sum, key) => sum + key.usage_count, 0)}
                </p>
                <p className="text-sm text-muted-foreground">总使用次数</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* API Keys List */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <Card>
          <CardHeader>
            <CardTitle>API密钥列表</CardTitle>
            <CardDescription>
              管理您的AI服务访问密钥
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockApiKeys.map((apiKey, index) => (
                <motion.div
                  key={apiKey.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                >
                  <div className="flex items-center space-x-4 flex-1">
                    <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                      <Key className="h-5 w-5 text-primary" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium">{apiKey.name}</h3>
                      <div className="flex items-center space-x-2 mt-1">
                        <code className="text-sm bg-muted px-2 py-1 rounded">
                          {apiKey.key_value}
                        </code>
                        <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                      <div className="flex items-center space-x-4 mt-2 text-sm text-muted-foreground">
                        <span>创建于: {new Date(apiKey.created_at).toLocaleDateString()}</span>
                        <span>最后使用: {apiKey.last_used}</span>
                        <span>使用次数: {apiKey.usage_count}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge
                      variant={apiKey.status === "active" ? "default" : "secondary"}
                      className="text-xs"
                    >
                      {apiKey.status === "active" ? "活跃" : "未活跃"}
                    </Badge>
                    <Button variant="outline" size="sm">
                      <Edit className="h-4 w-4 mr-1" />
                      编辑
                    </Button>
                    <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Security Notice */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.3 }}
      >
        <Card className="border-yellow-200 dark:border-yellow-800">
          <CardContent className="p-6">
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center flex-shrink-0">
                <Key className="h-4 w-4 text-yellow-600" />
              </div>
              <div>
                <h3 className="font-medium text-yellow-800 dark:text-yellow-200">
                  安全提醒
                </h3>
                <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                  请妥善保管您的API密钥，不要在公共场所或不安全的环境中暴露。
                  定期轮换密钥以确保账户安全。
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
