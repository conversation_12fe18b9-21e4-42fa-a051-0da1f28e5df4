"use client";

import React from "react";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import {
  Sparkles,
  TestTube,
  Bot,
  BookTemplate,
  Plus,
  ArrowRight,
  Zap,
  TrendingUp,
  Clock,
  CheckCircle,
} from "lucide-react";

// Mock data for demonstration
const mockStats = {
  totalAgents: 12,
  activeAgents: 8,
  testsToday: 24,
  successRate: 94,
};

const mockRecentAgents = [
  {
    id: "1",
    name: "内容创作团队",
    description: "专业的文案创作和内容策划",
    status: "active",
    lastUsed: "2小时前",
    successRate: 96,
  },
  {
    id: "2",
    name: "数据分析专家",
    description: "智能数据处理和洞察分析",
    status: "active",
    lastUsed: "1天前",
    successRate: 92,
  },
  {
    id: "3",
    name: "客服助手团队",
    description: "24/7智能客户服务支持",
    status: "inactive",
    lastUsed: "3天前",
    successRate: 88,
  },
];

const mockRecentActivity = [
  {
    id: "1",
    type: "test",
    title: "测试了内容创作团队",
    time: "30分钟前",
    status: "success",
  },
  {
    id: "2",
    type: "create",
    title: "创建了新的数据分析专家",
    time: "2小时前",
    status: "success",
  },
  {
    id: "3",
    type: "test",
    title: "测试了客服助手团队",
    time: "4小时前",
    status: "failed",
  },
];

export default function NewSiteDashboard() {
  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <motion.div
        className="text-center space-y-4 py-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="space-y-2">
          <h1 className="text-3xl md:text-4xl font-bold tracking-tight">
            欢迎使用 Meta-Agent
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            智能AI团队生成平台，让AI成为您最得力的助手
          </p>
        </div>

        {/* Primary Actions */}
        <motion.div
          className="flex flex-col sm:flex-row justify-center gap-3 sm:gap-4 px-4 sm:px-0 pt-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <Button asChild size="lg" className="min-h-[44px] w-full sm:w-auto">
            <Link href="/newsite/create">
              <Sparkles className="h-4 w-4 mr-2" />
              创建新Agent
            </Link>
          </Button>
          <Button variant="outline" size="lg" asChild className="min-h-[44px] w-full sm:w-auto">
            <Link href="/newsite/test">
              <TestTube className="h-4 w-4 mr-2" />
              测试Agent
            </Link>
          </Button>
        </motion.div>
      </motion.div>

      {/* Stats Cards */}
      <motion.div
        className="grid grid-cols-2 lg:grid-cols-4 gap-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.3 }}
      >
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总Agent数</CardTitle>
            <Bot className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockStats.totalAgents}</div>
            <p className="text-xs text-muted-foreground">
              +2 较上周
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">活跃Agent</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockStats.activeAgents}</div>
            <p className="text-xs text-muted-foreground">
              +1 较昨日
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">今日测试</CardTitle>
            <TestTube className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockStats.testsToday}</div>
            <p className="text-xs text-muted-foreground">
              +12% 较昨日
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">成功率</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockStats.successRate}%</div>
            <p className="text-xs text-muted-foreground">
              +2% 较上周
            </p>
          </CardContent>
        </Card>
      </motion.div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Agents */}
        <motion.div
          className="lg:col-span-2"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>我的Agent团队</CardTitle>
                <CardDescription>
                  管理和监控您的AI助手
                </CardDescription>
              </div>
              <Button variant="outline" size="sm" asChild>
                <Link href="/newsite/manage">
                  查看全部
                  <ArrowRight className="h-4 w-4 ml-1" />
                </Link>
              </Button>
            </CardHeader>
            <CardContent className="space-y-4">
              {mockRecentAgents.map((agent) => (
                <div
                  key={agent.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <Bot className="h-8 w-8 text-primary" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">
                        {agent.name}
                      </p>
                      <p className="text-sm text-muted-foreground truncate">
                        {agent.description}
                      </p>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge
                          variant={agent.status === "active" ? "default" : "secondary"}
                          className="text-xs"
                        >
                          {agent.status === "active" ? "活跃" : "未活跃"}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          成功率 {agent.successRate}%
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-xs text-muted-foreground">
                      {agent.lastUsed}
                    </span>
                    <Button variant="ghost" size="sm" asChild>
                      <Link href={`/newsite/test?agent=${agent.id}`}>
                        <TestTube className="h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </motion.div>

        {/* Quick Actions & Recent Activity */}
        <motion.div
          className="space-y-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
        >
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>快速操作</CardTitle>
              <CardDescription>
                常用功能快速访问
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button asChild className="w-full justify-start min-h-[44px]">
                <Link href="/newsite/create">
                  <Sparkles className="h-4 w-4 mr-2" />
                  创建新Agent
                </Link>
              </Button>
              <Button variant="outline" asChild className="w-full justify-start min-h-[44px]">
                <Link href="/newsite/templates">
                  <BookTemplate className="h-4 w-4 mr-2" />
                  浏览模板
                </Link>
              </Button>
              <Button variant="outline" asChild className="w-full justify-start min-h-[44px]">
                <Link href="/newsite/test">
                  <TestTube className="h-4 w-4 mr-2" />
                  测试Agent
                </Link>
              </Button>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>最近活动</CardTitle>
              <CardDescription>
                您的最新操作记录
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {mockRecentActivity.map((activity) => (
                <div key={activity.id} className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    {activity.status === "success" ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <Clock className="h-4 w-4 text-orange-500" />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">
                      {activity.title}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {activity.time}
                    </p>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
