"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/main-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Skeleton } from "@/components/ui/skeleton";
import { StreamingMarkdownRenderer } from "@/components/ui/markdown-renderer";
import { JsonFormatter } from "@/components/ui/json-formatter";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { api } from "@/lib/api";
import { formatDistanceToNow, format } from "date-fns";
import { zhCN } from "date-fns/locale";
import { UnifiedTestDetailDialog } from "@/components/features/test-history/unified-test-detail-dialog";
import {
  Search, Filter, Clock, Zap, AlertCircle,
  CheckCircle, XCircle, Pause, Trash2, Download, RefreshCw,
  Eye, EyeOff, Calendar, Timer, Cpu, Key, Settings, ChevronLeft, ChevronRight,
  DollarSign, Globe, Play, Square, ArrowRight, ArrowLeft, FileText, Copy, BarChart3,
  MoreHorizontal, RotateCcw, ArrowUpDown, ArrowUp, ArrowDown
} from "lucide-react";

interface TestHistory {
  id: number;
  test_id: string;
  agent_id: string;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  started_at: string;
  completed_at?: string;
  execution_duration_ms?: number;
  ai_config_override?: any;
  api_key_name?: string;
  input_text: string;
  final_output?: string;
  error_message?: string;
  created_at: string;
}

interface TestHistoryDetail extends TestHistory {
  user_id: number;
  execution_stages?: any[];
  progress_updates?: any[];
  response_metadata?: any;
  context_summary?: any;
  context_placeholders_used?: any[];
  team_member_interactions?: any[];
  error_details?: any;
  input_metadata?: any;
}

export default function TestHistoryPage() {
  const router = useRouter();
  const [testHistory, setTestHistory] = useState<TestHistory[]>([]);
  const [selectedTest, setSelectedTest] = useState<TestHistoryDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [sortBy, setSortBy] = useState<string>("started_at");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [selectedTests, setSelectedTests] = useState<Set<string>>(new Set());
  const [showDetailDialog, setShowDetailDialog] = useState(false);

  const [viewMode, setViewMode] = useState<"card" | "table">("card");
  const [isFilterCollapsed, setIsFilterCollapsed] = useState(false);
  const [actionLoading, setActionLoading] = useState<Set<string>>(new Set());

  useEffect(() => {
    loadTestHistory();
  }, [page, pageSize, statusFilter, sortBy, sortOrder]);

  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (page === 1) {
        loadTestHistory();
      } else {
        setPage(1);
      }
    }, 500);

    return () => clearTimeout(delayedSearch);
  }, [searchTerm]);

  const getStatusConfig = (status: string) => {
    const configs = {
      running: {
        icon: Zap,
        color: "text-blue-600 dark:text-blue-400",
        bgColor: "bg-blue-50 dark:bg-blue-950/50",
        borderColor: "border-blue-200 dark:border-blue-800",
        label: "运行中"
      },
      completed: {
        icon: CheckCircle,
        color: "text-green-600 dark:text-green-400",
        bgColor: "bg-green-50 dark:bg-green-950/50",
        borderColor: "border-green-200 dark:border-green-800",
        label: "已完成"
      },
      failed: {
        icon: XCircle,
        color: "text-red-600 dark:text-red-400",
        bgColor: "bg-red-50 dark:bg-red-950/50",
        borderColor: "border-red-200 dark:border-red-800",
        label: "失败"
      },
      cancelled: {
        icon: Pause,
        color: "text-muted-foreground",
        bgColor: "bg-muted/50",
        borderColor: "border-border",
        label: "已取消"
      }
    };
    return configs[status as keyof typeof configs] || configs.cancelled;
  };

  const formatDuration = (ms?: number, compact = false) => {
    if (!ms) return compact ? "?" : "未知";
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return compact ? `${(ms / 60000).toFixed(1)}m` : `${(ms / 60000).toFixed(1)}min`;
  };

  const truncateText = (text: string, maxLength: number = 100) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + "...";
  };

  const loadTestHistory = async () => {
    try {
      setLoading(true);
      const params: any = { 
        page, 
        limit: pageSize,
        sort_by: sortBy,
        sort_order: sortOrder
      };
      
      if (statusFilter !== "all") {
        params.status = statusFilter;
      }
      
      if (searchTerm.trim()) {
        params.search = searchTerm.trim();
      }

      const response = await api.testHistory.list(params);

      const responseData = response.data || response;
      const tests = responseData.tests || responseData || [];
      const total = responseData.total || tests.length || 0;

      setTestHistory(tests);
      setTotalPages(Math.ceil(total / pageSize));
      setTotalCount(total);
    } catch (error) {
      setTestHistory([]);
      setTotalPages(1);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  };

  const loadTestDetail = async (testId: string) => {
    try {
      const response = await api.testHistory.getDetail(testId);

      const detail = response.data || response;

      setSelectedTest(detail);
      setShowDetailDialog(true);
    } catch (error) {
      alert(`获取测试详情失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  };

  const handleDeleteSelected = async () => {
    if (selectedTests.size === 0) return;

    if (!confirm(`确定要删除选中的 ${selectedTests.size} 条测试记录吗？此操作不可撤销。`)) {
      return;
    }

    try {
      const testIds = Array.from(selectedTests);
      await Promise.all(testIds.map(testId => api.testHistory.delete(testId)));

      setSelectedTests(new Set());
      loadTestHistory();
    } catch (error) {
      alert("删除失败，请重试");
    }
  };

  const handleRerunTest = (test: TestHistory) => {
    // Navigate to test page with pre-filled configuration using Next.js router
    const params = new URLSearchParams();
    params.set('agent_id', test.agent_id);
    params.set('input_text', test.input_text);

    // Add AI configuration if available
    if (test.ai_config_override) {
      params.set('ai_config', JSON.stringify(test.ai_config_override));
    }

    // Add API key if available
    if (test.api_key_name) {
      params.set('api_key_name', test.api_key_name);
    }

    // Use Next.js router instead of window.location.href to avoid page refresh
    router.push(`/test?${params.toString()}`);
  };

  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(field);
      setSortOrder("desc");
    }
  };

  const getSortIcon = (field: string) => {
    if (sortBy !== field) {
      return <ArrowUpDown className="w-3 h-3 text-muted-foreground" />;
    }
    return sortOrder === "asc" ?
      <ArrowUp className="w-3 h-3 text-foreground" /> :
      <ArrowDown className="w-3 h-3 text-foreground" />;
  };

  const handleExportSelected = () => {
    if (selectedTests.size === 0) return;
    
    const selectedData = testHistory.filter(test => selectedTests.has(test.test_id));
    const dataStr = JSON.stringify(selectedData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `test-history-${format(new Date(), 'yyyy-MM-dd')}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const toggleTestSelection = (testId: string) => {
    const newSelected = new Set(selectedTests);
    if (newSelected.has(testId)) {
      newSelected.delete(testId);
    } else {
      newSelected.add(testId);
    }
    setSelectedTests(newSelected);
  };

  const selectAllTests = (checked: boolean) => {
    if (checked) {
      setSelectedTests(new Set(testHistory.map(test => test.test_id)));
    } else {
      setSelectedTests(new Set());
    }
  };

  const EmptyState = () => {
    return (
      <div className="text-center py-12">
        <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
          <Clock className="w-12 h-12 text-muted-foreground" />
        </div>
        <h3 className="text-lg font-medium mb-2">暂无测试记录</h3>
        <p className="text-muted-foreground mb-6">
          {searchTerm || statusFilter !== "all"
            ? "没有找到符合条件的测试记录，请尝试调整搜索条件"
            : "还没有进行过任何测试，完成第一次测试后记录将显示在这里"}
        </p>
        {(searchTerm || statusFilter !== "all") && (
          <Button
            variant="outline"
            onClick={() => {
              setSearchTerm("");
              setStatusFilter("all");
            }}
          >
            清除筛选条件
          </Button>
        )}
      </div>
    );
  };

  return (
    <MainLayout>
      <TooltipProvider>
        <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 md:gap-4">
          <div className="flex-1 min-w-0">
            <h1 className="text-2xl md:text-3xl font-bold flex items-center gap-2">
              📊 测试历史
            </h1>
            <p className="text-muted-foreground mt-1 text-sm md:text-base">
              查看和管理代理测试执行历史
              {totalCount > 0 && (
                <span className="ml-2 text-xs md:text-sm bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full">
                  共 {totalCount} 条记录
                </span>
              )}
            </p>
          </div>
          <div className="flex gap-2 w-full sm:w-auto">
            <Button
              variant="outline"
              size="sm"
              onClick={loadTestHistory}
              disabled={loading}
              className="flex items-center gap-2 touch-target min-h-[44px] md:min-h-[36px] flex-1 sm:flex-initial"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? "animate-spin" : ""}`} />
              <span className="hidden sm:inline">刷新</span>
              <span className="sm:hidden">刷新</span>
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card className="p-3 md:p-4">
          <div className="flex flex-col gap-3 md:gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="搜索测试内容、代理ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 mobile-form-element"
              />
            </div>

            <div className="flex flex-col sm:flex-row gap-2 md:gap-3">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full sm:w-40 touch-target min-h-[44px] md:min-h-[36px]">
                  <SelectValue placeholder="状态筛选" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="running">运行中</SelectItem>
                  <SelectItem value="completed">已完成</SelectItem>
                  <SelectItem value="failed">失败</SelectItem>
                  <SelectItem value="cancelled">已取消</SelectItem>
                </SelectContent>
              </Select>

              <Select value={`${sortBy}-${sortOrder}`} onValueChange={(value) => {
                const [field, order] = value.split('-');
                setSortBy(field);
                setSortOrder(order as "asc" | "desc");
              }}>
                <SelectTrigger className="w-full sm:w-40 touch-target min-h-[44px] md:min-h-[36px]">
                  <SelectValue placeholder="排序方式" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="started_at-desc">最新创建</SelectItem>
                  <SelectItem value="started_at-asc">最早创建</SelectItem>
                  <SelectItem value="execution_duration_ms-desc">耗时最长</SelectItem>
                  <SelectItem value="execution_duration_ms-asc">耗时最短</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </Card>

        {/* Loading State */}
        {loading && testHistory.length === 0 ? (
          <Card>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-muted/50 border-b">
                    <tr>
                      <th className="text-left p-2 md:p-3 w-8">
                        <Skeleton className="w-4 h-4" />
                      </th>
                      <th className="text-left p-2 md:p-3 min-w-[120px]">
                        <Skeleton className="w-16 h-4" />
                      </th>
                      <th className="text-left p-2 md:p-3 min-w-[100px]">
                        <Skeleton className="w-12 h-4" />
                      </th>
                      <th className="text-left p-2 md:p-3 min-w-[80px]">
                        <Skeleton className="w-12 h-4" />
                      </th>
                      <th className="text-left p-2 md:p-3 min-w-[200px]">
                        <Skeleton className="w-24 h-4" />
                      </th>
                      <th className="text-right p-2 md:p-3 w-24">
                        <Skeleton className="w-12 h-4 ml-auto" />
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {Array.from({ length: 6 }).map((_, index) => (
                      <tr key={index} className="border-b">
                        <td className="p-2 md:p-3">
                          <Skeleton className="w-4 h-4" />
                        </td>
                        <td className="p-2 md:p-3">
                          <Skeleton className="w-20 h-4" />
                        </td>
                        <td className="p-2 md:p-3">
                          <div className="flex items-center gap-2">
                            <Skeleton className="w-4 h-4" />
                            <Skeleton className="w-12 h-4" />
                          </div>
                        </td>
                        <td className="p-2 md:p-3">
                          <Skeleton className="w-12 h-4" />
                        </td>
                        <td className="p-2 md:p-3">
                          <Skeleton className="w-32 h-4" />
                        </td>
                        <td className="p-2 md:p-3">
                          <div className="flex items-center gap-1 justify-end">
                            <Skeleton className="w-8 h-8" />
                            <Skeleton className="w-8 h-8" />
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        ) : testHistory.length === 0 ? (
          <EmptyState />
        ) : (
          <>
            {/* Bulk Actions */}
            {selectedTests.size > 0 && (
              <Card className="p-4 bg-blue-50/50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Checkbox
                      checked={selectedTests.size === testHistory.length}
                      onCheckedChange={selectAllTests}
                    />
                    <span className="text-sm font-medium">
                      已选择 {selectedTests.size} 条记录
                    </span>
                  </div>
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" onClick={handleExportSelected}>
                      <Download className="w-4 h-4 mr-1" />
                      导出
                    </Button>
                    <Button size="sm" variant="destructive" onClick={handleDeleteSelected}>
                      <Trash2 className="w-4 h-4 mr-1" />
                      删除
                    </Button>
                  </div>
                </div>
              </Card>
            )}

            {/* Test History Table */}
            <Card>
              <CardContent className="p-0">
                {/* Mobile View - Stacked Cards */}
                <div className="block md:hidden">
                  <div className="space-y-2 p-2">
                    {testHistory.map((test) => {
                      const statusConfig = getStatusConfig(test.status);
                      const StatusIcon = statusConfig.icon;
                      const isSelected = selectedTests.has(test.test_id);
                      const isActionLoading = actionLoading.has(test.test_id);

                      return (
                        <div
                          key={test.test_id}
                          className={`border rounded-lg p-3 space-y-2 ${
                            isSelected ? 'bg-blue-50/50 dark:bg-blue-950/30 border-blue-200 dark:border-blue-800' : 'border-border'
                          }`}
                        >
                          {/* Mobile Header */}
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Checkbox
                                checked={isSelected}
                                onCheckedChange={() => toggleTestSelection(test.test_id)}
                                className="w-4 h-4"
                              />
                              <div className={`p-1 rounded ${statusConfig.bgColor}`}>
                                <StatusIcon className={`w-3 h-3 ${statusConfig.color}`} />
                              </div>
                              <Badge className={`${statusConfig.bgColor} ${statusConfig.color} border-0 text-xs px-2 py-1`}>
                                {statusConfig.label}
                              </Badge>
                            </div>
                            <div className="flex items-center gap-1">
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => loadTestDetail(test.test_id)}
                                    disabled={isActionLoading}
                                    className="h-8 w-8 p-0"
                                  >
                                    <Eye className="w-4 h-4" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>查看详情</p>
                                </TooltipContent>
                              </Tooltip>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleRerunTest(test)}
                                    disabled={isActionLoading}
                                    className="h-8 w-8 p-0"
                                  >
                                    <RotateCcw className="w-4 h-4" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>重新运行</p>
                                </TooltipContent>
                              </Tooltip>
                            </div>
                          </div>

                          {/* Mobile Content */}
                          <div className="space-y-1">
                            <div className="font-medium text-sm">{test.agent_id}</div>
                            <div className="text-xs text-muted-foreground">
                              {format(new Date(test.started_at), 'MM-dd HH:mm')} •
                              {test.execution_duration_ms ? ` ${formatDuration(test.execution_duration_ms, true)}` : ' -'}
                            </div>
                            <div className="text-sm line-clamp-2">
                              {truncateText(test.input_text, 100)}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>

                {/* Desktop View - Table */}
                <div className="hidden md:block overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-muted/50 border-b">
                      <tr>
                        <th className="text-left p-2 md:p-3 w-8">
                          <Checkbox
                            checked={selectedTests.size === testHistory.length && testHistory.length > 0}
                            onCheckedChange={selectAllTests}
                            className="w-4 h-4"
                          />
                        </th>
                        <th className="text-left p-2 md:p-3 min-w-[120px] font-medium text-xs md:text-sm">
                          <button
                            onClick={() => handleSort('agent_id')}
                            className="flex items-center gap-1 hover:text-foreground transition-colors"
                          >
                            Agent名称
                            {getSortIcon('agent_id')}
                          </button>
                        </th>
                        <th className="text-left p-2 md:p-3 min-w-[100px] font-medium text-xs md:text-sm">
                          <button
                            onClick={() => handleSort('status')}
                            className="flex items-center gap-1 hover:text-foreground transition-colors"
                          >
                            状态
                            {getSortIcon('status')}
                          </button>
                        </th>
                        <th className="text-left p-2 md:p-3 min-w-[80px] font-medium text-xs md:text-sm">
                          <button
                            onClick={() => handleSort('execution_duration_ms')}
                            className="flex items-center gap-1 hover:text-foreground transition-colors"
                          >
                            持续时间
                            {getSortIcon('execution_duration_ms')}
                          </button>
                        </th>
                        <th className="text-left p-2 md:p-3 min-w-[200px] font-medium text-xs md:text-sm">
                          输入内容
                        </th>
                        <th className="text-right p-2 md:p-3 w-24 font-medium text-xs md:text-sm">
                          操作
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {testHistory.map((test) => {
                        const statusConfig = getStatusConfig(test.status);
                        const StatusIcon = statusConfig.icon;
                        const isSelected = selectedTests.has(test.test_id);
                        const isActionLoading = actionLoading.has(test.test_id);

                        return (
                          <tr
                            key={test.test_id}
                            className={`border-b hover:bg-muted/30 transition-colors ${
                              isSelected ? 'bg-blue-50/50 dark:bg-blue-950/30' : ''
                            }`}
                          >
                            {/* Checkbox */}
                            <td className="p-2 md:p-3">
                              <div className="relative">
                                <Checkbox
                                  checked={isSelected}
                                  onCheckedChange={() => toggleTestSelection(test.test_id)}
                                  className="w-4 h-4"
                                />
                                {/* Touch target overlay for mobile */}
                                <div className="absolute inset-0 -m-3 md:hidden" onClick={(e) => {
                                  e.stopPropagation();
                                  toggleTestSelection(test.test_id);
                                }} />
                              </div>
                            </td>

                            {/* Agent Name */}
                            <td className="p-2 md:p-3">
                              <div className="font-medium text-sm truncate max-w-[120px]">
                                {test.agent_id}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                ID: {test.test_id.slice(-6)}
                              </div>
                            </td>

                            {/* Status */}
                            <td className="p-2 md:p-3">
                              <div className="flex items-center gap-2">
                                <div className={`p-1 rounded ${statusConfig.bgColor}`}>
                                  <StatusIcon className={`w-3 h-3 ${statusConfig.color}`} />
                                </div>
                                <Badge className={`${statusConfig.bgColor} ${statusConfig.color} border-0 text-xs px-2 py-1`}>
                                  {statusConfig.label}
                                </Badge>
                              </div>
                            </td>

                            {/* Duration */}
                            <td className="p-2 md:p-3">
                              {test.execution_duration_ms ? (
                                <div className="flex items-center gap-1 text-sm">
                                  <Timer className="w-3 h-3 text-muted-foreground" />
                                  {formatDuration(test.execution_duration_ms)}
                                </div>
                              ) : (
                                <span className="text-xs text-muted-foreground">-</span>
                              )}
                            </td>

                            {/* Input Content */}
                            <td className="p-2 md:p-3">
                              <div className="text-sm max-w-[200px] truncate">
                                {truncateText(test.input_text, 80)}
                              </div>
                            </td>

                            {/* Actions */}
                            <td className="p-2 md:p-3">
                              <div className="flex items-center gap-1 justify-end">
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => loadTestDetail(test.test_id)}
                                      disabled={isActionLoading}
                                      className="h-8 w-8 p-0 hover:bg-muted"
                                    >
                                      <Eye className="w-4 h-4" />
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>查看详情</p>
                                  </TooltipContent>
                                </Tooltip>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleRerunTest(test)}
                                      disabled={isActionLoading}
                                      className="h-8 w-8 p-0 hover:bg-muted"
                                    >
                                      <RotateCcw className="w-4 h-4" />
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>重新运行</p>
                                  </TooltipContent>
                                </Tooltip>
                              </div>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </>
        )}

        {/* Enhanced Pagination */}
        {(totalPages > 1 || totalCount > pageSize) && (
          <Card className="p-3 md:p-4">
            <div className="flex flex-col sm:flex-row items-center justify-between gap-3 md:gap-4">
              <div className="text-xs md:text-sm text-muted-foreground text-center sm:text-left mobile-text-sm">
                显示第 {(page - 1) * pageSize + 1} - {Math.min(page * pageSize, totalCount)} 条，共 {totalCount} 条记录
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(Math.max(1, page - 1))}
                  disabled={page <= 1 || loading}
                  className="touch-target min-h-[44px] md:min-h-[36px] mobile-button-spacing"
                >
                  <ChevronLeft className="w-4 h-4" />
                  <span className="hidden sm:inline">上一页</span>
                  <span className="sm:hidden">上一页</span>
                </Button>
                <span className="text-xs md:text-sm px-2 md:px-3 py-1 bg-muted rounded mobile-text-sm">
                  {page} / {totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(Math.min(totalPages, page + 1))}
                  disabled={page >= totalPages || loading}
                  className="touch-target min-h-[44px] md:min-h-[36px] mobile-button-spacing"
                >
                  <span className="hidden sm:inline">下一页</span>
                  <span className="sm:hidden">下一页</span>
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </Card>
        )}

        {/* Enhanced Test Detail Dialog */}
        <UnifiedTestDetailDialog
          open={showDetailDialog}
          onOpenChange={setShowDetailDialog}
          testDetail={selectedTest}
          title="测试详情"
        />
        </div>
      </TooltipProvider>
    </MainLayout>
  );
}
