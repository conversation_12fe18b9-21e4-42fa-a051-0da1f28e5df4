"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/main-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/components/common/toast";
import { DeleteConfirmDialog } from "@/components/common/confirm-dialog";
import { api } from "@/lib/api";

interface APIKey {
  id: string;
  name: string;
  key_prefix: string;
  status: "active" | "inactive" | "error" | "expired" | "revoked";
  usage?: {
    requests_today: number;
    requests_month: number;
    cost_today: number;
    cost_month: number;
  };
  usage_count?: number;
  created_at: string;
  last_used?: string;
}

const mockAPIKeys: APIKey[] = [
  {
    id: "key_001",
    name: "OpenAI GPT-4 密钥",
    key_prefix: "sk-abc123",
    status: "active",
    usage: {
      requests_today: 45,
      requests_month: 1250,
      cost_today: 2.34,
      cost_month: 89.67
    },
    created_at: "2025-06-01T10:00:00Z",
    last_used: "2025-06-30T11:30:00Z"
  },
  {
    id: "key_002",
    name: "Claude-3 Sonnet 密钥",
    key_prefix: "sk-ant-def456",
    status: "active",
    usage: {
      requests_today: 23,
      requests_month: 678,
      cost_today: 1.45,
      cost_month: 45.23
    },
    created_at: "2025-06-05T14:20:00Z",
    last_used: "2025-06-30T10:15:00Z"
  },
  {
    id: "key_003",
    name: "Google Gemini 密钥",
    key_prefix: "AIza-ghi789",
    status: "inactive",
    usage: {
      requests_today: 0,
      requests_month: 234,
      cost_today: 0,
      cost_month: 12.45
    },
    created_at: "2025-06-10T09:30:00Z",
    last_used: "2025-06-25T16:45:00Z"
  }
];

// Remove provider-related code as we're simplifying the API key management

export default function APIKeysPage() {
  const [apiKeys, setAPIKeys] = useState<APIKey[]>([]);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingKey, setEditingKey] = useState<APIKey | null>(null);
  const [newKey, setNewKey] = useState({
    name: "",
    key: ""
  });
  const [editKey, setEditKey] = useState({
    name: ""
  });
  const [isLoading, setIsLoading] = useState(true);
  const [usageSummary, setUsageSummary] = useState({
    total_keys: 0,
    active_keys: 0,
    total_requests: 0,
    requests_today: 0,
    requests_month: 0,
    cost_today: 0.0,
    cost_month: 0.0
  });
  const { success, error } = useToast();

  // Load API keys and usage summary from backend on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        // Load API keys
        const keysResponse = await api.apiKeys.list();
        if (keysResponse.success && keysResponse.data) {
          // Handle both possible response formats
          const apiKeys = keysResponse.data.api_keys || keysResponse.data;
          setAPIKeys(apiKeys);
        } else {
          console.warn('Failed to load API keys:', keysResponse.error?.message);
        }

        // Note: Usage summary endpoint may not be implemented yet
        // For now, we'll skip this to avoid errors
        // TODO: Implement usage summary endpoint in backend
        /*
        const summaryResponse = await api.apiKeys.getUsageSummary();
        if (summaryResponse.success && summaryResponse.data) {
          setUsageSummary(summaryResponse.data);
        } else if (process.env.NODE_ENV === 'development') {
          console.warn('Failed to load usage summary:', summaryResponse.error?.message);
        }
        */
      } catch (err) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Failed to load data:', err);
        }
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  const getStatusBadge = (status: APIKey["status"]) => {
    const variants = {
      active: { color: "bg-green-100 text-green-800", label: "活跃" },
      inactive: { color: "bg-yellow-100 text-yellow-800", label: "未激活" },
      error: { color: "bg-red-100 text-red-800", label: "错误" },
      expired: { color: "bg-orange-100 text-orange-800", label: "已过期" },
      revoked: { color: "bg-red-100 text-red-800", label: "已撤销" }
    };
    const config = variants[status] || variants.error;
    return (
      <Badge className={config.color}>
        {config.label}
      </Badge>
    );
  };

  // Remove provider-related helper function

  const maskAPIKey = (keyPrefix: string | undefined) => {
    if (!keyPrefix || typeof keyPrefix !== 'string') return '***';
    return keyPrefix + "...";
  };

  const handleAddKey = async () => {
    if (!newKey.name || !newKey.key) {
      error("请填写完整信息");
      return;
    }

    try {
      const response = await api.apiKeys.create({
        name: newKey.name,
        key: newKey.key
      });

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to create API key');
      }

      // Reload API keys to get the updated list
      const listResponse = await api.apiKeys.list();
      if (listResponse.success && listResponse.data) {
        // Handle both possible response formats
        const apiKeys = listResponse.data.api_keys || listResponse.data;
        setAPIKeys(apiKeys);
      }

      setNewKey({ name: "", key: "" });
      setIsAddDialogOpen(false);
      success("AI密钥已添加");
    } catch (err) {
      console.error('Failed to add API key:', err);
      error("添加AI密钥失败");
    }
  };

  const handleDeleteKey = async (keyId: string) => {
    try {
      const response = await api.apiKeys.delete(keyId);

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to delete API key');
      }

      setAPIKeys(apiKeys.filter(key => key.id !== keyId));
      success("AI密钥已删除");
    } catch (err) {
      console.error('Failed to delete API key:', err);
      error("删除AI密钥失败");
    }
  };

  const handleToggleStatus = async (keyId: string) => {
    try {
      const key = apiKeys.find(k => k.id === keyId);
      if (!key) return;

      // Check if the key can be toggled
      if (key.status === "error" || key.status === "expired" || key.status === "revoked") {
        error(`无法切换状态：AI密钥当前状态为"${getStatusLabel(key.status)}"，请重新创建密钥`);
        return;
      }

      const newStatus = key.status === "active" ? "inactive" : "active";
      const action = newStatus === "active" ? "启用" : "禁用";

      const response = await api.apiKeys.update(keyId, {
        is_active: newStatus === "active"
      });

      if (!response.success) {
        throw new Error(response.error?.message || `Failed to ${action} API key`);
      }

      setAPIKeys(apiKeys.map(k =>
        k.id === keyId
          ? { ...k, status: newStatus }
          : k
      ));
      success(`AI密钥已${action}`);
    } catch (err) {
      console.error('Failed to update API key status:', err);
      error(err instanceof Error ? err.message : "更新AI密钥状态失败");
    }
  };

  const getStatusLabel = (status: APIKey["status"]) => {
    const labels = {
      active: "活跃",
      inactive: "未激活",
      error: "错误",
      expired: "已过期",
      revoked: "已撤销"
    };
    return labels[status] || status;
  };

  const canToggleStatus = (status: APIKey["status"]) => {
    return status === "active" || status === "inactive";
  };



  const handleEditKey = (key: APIKey) => {
    setEditingKey(key);
    setEditKey({
      name: key.name
    });
    setIsEditDialogOpen(true);
  };

  const handleUpdateKey = async () => {
    if (!editingKey || !editKey.name) {
      error("请填写完整信息");
      return;
    }

    try {
      const response = await api.apiKeys.update(editingKey.id, {
        name: editKey.name
      });

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to update API key');
      }

      // Update the key in the list
      setAPIKeys(apiKeys.map(k =>
        k.id === editingKey.id
          ? { ...k, name: editKey.name }
          : k
      ));

      setEditKey({ name: "" });
      setEditingKey(null);
      setIsEditDialogOpen(false);
      success("AI密钥已更新");
    } catch (err) {
      console.error('Failed to update API key:', err);
      error("更新AI密钥失败");
    }
  };

  // Use backend usage summary data instead of calculating from individual keys
  const totalCostToday = usageSummary.cost_today;
  const totalCostMonth = usageSummary.cost_month;
  const totalRequestsToday = usageSummary.requests_today;
  const totalRequestsMonth = usageSummary.requests_month;

  if (isLoading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">加载AI密钥中...</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex-1 min-w-0">
            <h1 className="text-2xl md:text-3xl font-bold">🔑 AI密钥管理</h1>
            <p className="text-muted-foreground text-sm md:text-base">
              管理AI服务提供商的AI密钥和使用情况
            </p>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button className="w-full sm:w-auto touch-target min-h-[44px] md:min-h-[36px] mobile-button-spacing">
                ➕ 添加AI密钥
              </Button>
            </DialogTrigger>
            <DialogContent className="w-[95vw] md:w-full max-w-md mobile-modal">
              <DialogHeader>
                <DialogTitle className="text-lg md:text-xl">添加新的AI密钥</DialogTitle>
                <DialogDescription className="text-sm mobile-text-sm">
                  添加AI服务提供商的AI密钥以启用相关功能
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="key-name" className="text-sm mobile-text-sm">密钥名称</Label>
                  <Input
                    id="key-name"
                    placeholder="例如：OpenAI GPT-4 密钥"
                    value={newKey.name}
                    onChange={(e) => setNewKey({ ...newKey, name: e.target.value })}
                    className="mobile-form-element"
                  />
                  <p className="text-xs md:text-sm text-muted-foreground mobile-text-sm">
                    为这个AI密钥起一个便于识别的名称
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="api-key" className="text-sm mobile-text-sm">AI密钥值</Label>
                  <Input
                    id="api-key"
                    type="password"
                    placeholder="输入完整的AI密钥"
                    value={newKey.key}
                    onChange={(e) => setNewKey({ ...newKey, key: e.target.value })}
                    className="mobile-form-element"
                  />
                  <p className="text-xs md:text-sm text-muted-foreground mobile-text-sm">
                    支持OpenAI、Anthropic、Google等主流AI服务提供商的AI密钥
                  </p>
                </div>
                <div className="flex flex-col sm:flex-row justify-end gap-2">
                  <Button
                    variant="outline"
                    onClick={() => setIsAddDialogOpen(false)}
                    className="touch-target min-h-[44px] md:min-h-[36px] mobile-button-spacing"
                  >
                    取消
                  </Button>
                  <Button
                    onClick={handleAddKey}
                    className="touch-target min-h-[44px] md:min-h-[36px] mobile-button-spacing"
                  >
                    添加
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          {/* Edit API Key Dialog */}
          <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
            <DialogContent className="w-[95vw] md:w-full max-w-md mobile-modal">
              <DialogHeader>
                <DialogTitle className="text-lg md:text-xl">编辑AI密钥</DialogTitle>
                <DialogDescription className="text-sm mobile-text-sm">
                  更新AI密钥的名称
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-key-name" className="text-sm mobile-text-sm">密钥名称</Label>
                  <Input
                    id="edit-key-name"
                    placeholder="例如：OpenAI GPT-4 密钥"
                    value={editKey.name}
                    onChange={(e) => setEditKey({ ...editKey, name: e.target.value })}
                    className="mobile-form-element"
                  />
                  <p className="text-xs md:text-sm text-muted-foreground mobile-text-sm">
                    为这个AI密钥起一个便于识别的名称
                  </p>
                </div>
                <div className="flex flex-col sm:flex-row justify-end gap-2">
                  <Button
                    variant="outline"
                    onClick={() => setIsEditDialogOpen(false)}
                    className="touch-target min-h-[44px] md:min-h-[36px] mobile-button-spacing"
                  >
                    取消
                  </Button>
                  <Button
                    onClick={handleUpdateKey}
                    className="touch-target min-h-[44px] md:min-h-[36px] mobile-button-spacing"
                  >
                    更新
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Usage Overview */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-4 mobile-grid-gap">
          <Card>
            <CardContent className="pt-4 md:pt-6 mobile-card-spacing-sm">
              <div className="text-xl md:text-2xl font-bold">{apiKeys.length}</div>
              <p className="text-xs text-muted-foreground mobile-text-sm">AI密钥总数</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-4 md:pt-6 mobile-card-spacing-sm">
              <div className="text-xl md:text-2xl font-bold">{totalRequestsToday}</div>
              <p className="text-xs text-muted-foreground mobile-text-sm">今日请求数</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-4 md:pt-6 mobile-card-spacing-sm">
              <div className="text-xl md:text-2xl font-bold">${totalCostToday.toFixed(2)}</div>
              <p className="text-xs text-muted-foreground mobile-text-sm">今日费用</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-4 md:pt-6 mobile-card-spacing-sm">
              <div className="text-xl md:text-2xl font-bold">${totalCostMonth.toFixed(2)}</div>
              <p className="text-xs text-muted-foreground mobile-text-sm">本月费用</p>
            </CardContent>
          </Card>
        </div>

        {/* Security Alert */}
        <Alert>
          <span className="text-lg">🔒</span>
          <AlertDescription>
            <strong>安全提醒：</strong>
            AI密钥是敏感信息，请妥善保管。建议定期轮换密钥，并监控使用情况以防止滥用。
          </AlertDescription>
        </Alert>

        {/* API Keys List */}
        <div className="space-y-3 md:space-y-4">
          {apiKeys.map((apiKey) => {
            return (
              <Card key={apiKey.id}>
                <CardHeader className="pb-3 md:pb-6">
                  <div className="flex items-start justify-between gap-3">
                    <div className="flex items-center gap-2 md:gap-3 min-w-0 flex-1">
                      <span className="text-xl md:text-2xl">🔑</span>
                      <div className="min-w-0 flex-1">
                        <CardTitle className="text-base md:text-lg truncate">{apiKey.name}</CardTitle>
                        <CardDescription className="text-xs md:text-sm mobile-text-sm">
                          创建于 {new Date(apiKey.created_at).toLocaleDateString()}
                        </CardDescription>
                      </div>
                    </div>
                    {getStatusBadge(apiKey.status)}
                  </div>
                </CardHeader>
                <CardContent className="space-y-3 md:space-y-4 mobile-card-spacing">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4 mobile-grid-gap">
                    <div>
                      <Label className="text-xs md:text-sm text-muted-foreground mobile-text-sm">AI密钥</Label>
                      <div className="font-mono text-xs md:text-sm bg-muted p-2 rounded mobile-text-sm">
                        {maskAPIKey(apiKey.key_prefix)}
                      </div>
                    </div>
                    <div>
                      <Label className="text-xs md:text-sm text-muted-foreground mobile-text-sm">最后使用</Label>
                      <div className="text-xs md:text-sm mobile-text-sm">
                        {apiKey.last_used ? new Date(apiKey.last_used).toLocaleString() : '从未使用'}
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-4 mobile-grid-gap">
                    <div className="text-center">
                      <div className="text-base md:text-lg font-semibold">{apiKey.usage?.requests_today || 0}</div>
                      <p className="text-xs text-muted-foreground mobile-text-sm">今日请求</p>
                    </div>
                    <div className="text-center">
                      <div className="text-base md:text-lg font-semibold">{apiKey.usage?.requests_month || apiKey.usage_count || 0}</div>
                      <p className="text-xs text-muted-foreground mobile-text-sm">总请求数</p>
                    </div>
                    <div className="text-center">
                      <div className="text-base md:text-lg font-semibold">${(apiKey.usage?.cost_today || 0).toFixed(2)}</div>
                      <p className="text-xs text-muted-foreground mobile-text-sm">今日费用</p>
                    </div>
                    <div className="text-center">
                      <div className="text-base md:text-lg font-semibold">${(apiKey.usage?.cost_month || 0).toFixed(2)}</div>
                      <p className="text-xs text-muted-foreground mobile-text-sm">本月费用</p>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between pt-3 md:pt-4 border-t gap-3">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id={`api-key-toggle-${apiKey.id}`}
                        checked={apiKey.status === "active"}
                        disabled={!canToggleStatus(apiKey.status)}
                        onCheckedChange={() => handleToggleStatus(apiKey.id)}
                      />
                      <Label htmlFor={`api-key-toggle-${apiKey.id}`}>
                        {apiKey.status === "active" ? "已启用" :
                         apiKey.status === "inactive" ? "已禁用" :
                         `状态：${getStatusLabel(apiKey.status)}`}
                      </Label>
                      {!canToggleStatus(apiKey.status) && (
                        <span className="text-xs text-muted-foreground">
                          (无法切换)
                        </span>
                      )}
                    </div>
                    <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          // Note: API key reveal functionality not implemented yet
                          // For security reasons, API keys should only be shown once during creation
                          error("AI密钥只在创建时显示一次，请在创建时保存");
                        }}
                        className="touch-target min-h-[44px] md:min-h-[36px] mobile-button-spacing"
                      >
                        📋 复制
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditKey(apiKey)}
                        className="touch-target min-h-[44px] md:min-h-[36px] mobile-button-spacing"
                      >
                        ✏️ 编辑
                      </Button>
                      <DeleteConfirmDialog
                        trigger={
                          <Button
                            variant="destructive"
                            size="sm"
                            className="touch-target min-h-[44px] md:min-h-[36px] mobile-button-spacing"
                          >
                            🗑️ 删除
                          </Button>
                        }
                        itemName={apiKey.name}
                        onConfirm={() => handleDeleteKey(apiKey.id)}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Empty State */}
        {apiKeys.length === 0 && (
          <Card>
            <CardContent className="text-center py-8 md:py-12 mobile-card-spacing">
              <div className="text-4xl md:text-6xl mb-4">🔑</div>
              <h3 className="text-base md:text-lg font-semibold mb-2">暂无AI密钥</h3>
              <p className="text-muted-foreground mb-4 text-sm md:text-base mobile-text-base">
                添加AI服务提供商的AI密钥以开始使用Meta-Agent
              </p>
              <Button
                onClick={() => setIsAddDialogOpen(true)}
                className="touch-target min-h-[44px] md:min-h-[36px] mobile-button-spacing"
              >
                添加第一个AI密钥
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Usage Tips */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg md:text-xl">💡 使用建议</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3 mobile-card-spacing">
            <div className="flex items-start gap-2">
              <span className="text-green-600">✓</span>
              <span className="text-xs md:text-sm mobile-text-sm">定期检查AI密钥的使用情况和费用</span>
            </div>
            <div className="flex items-start gap-2">
              <span className="text-green-600">✓</span>
              <span className="text-xs md:text-sm mobile-text-sm">为不同用途设置不同的AI密钥</span>
            </div>
            <div className="flex items-start gap-2">
              <span className="text-green-600">✓</span>
              <span className="text-xs md:text-sm mobile-text-sm">及时禁用不再使用的AI密钥</span>
            </div>
            <div className="flex items-start gap-2">
              <span className="text-green-600">✓</span>
              <span className="text-xs md:text-sm mobile-text-sm">设置费用预警以避免意外支出</span>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
