"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";

/**
 * Root page that redirects to agent creation page
 * This eliminates the dashboard and makes agent creation the default landing page
 */
export default function HomePage() {
  const router = useRouter();

  useEffect(() => {
    // Immediately redirect to the agent creation page
    router.replace('/create');
  }, [router]);

  // Show a minimal loading state while redirecting
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center space-y-4">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
        <p className="text-muted-foreground">正在跳转到Agent创建页面...</p>
      </div>
    </div>
  );
}

