"use client";

import { useState, useEffect, useR<PERSON>, use<PERSON>em<PERSON>, use<PERSON>allback } from "react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/main-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/components/common/toast";
import { useAuth } from "@/lib/auth";
import { settingsA<PERSON>, handleApiError } from "@/lib/api-client-settings";

interface SystemSettings {
  general: {
    app_name: string;
    app_description: string;
    default_language: string;
    timezone: string;
    theme: "light" | "dark" | "system";
  };
  agent: {
    max_concurrent_agents: number;
    default_model: string;
    default_temperature: number;
    max_response_length: number;
    timeout_seconds: number;
  };
  ai_team_generation: {
    enable_ai_team_generation: boolean;
    team_generation_provider: string;
    team_generation_custom_provider_name: string;
    team_generation_model: string;
    team_generation_temperature: number;
    team_generation_max_tokens: number;
    team_generation_base_url: string;
    team_generation_api_key: string;
  };
  api: {
    rate_limit_per_minute: number;
    enable_cors: boolean;
    cors_origins: string;
    enable_docs: boolean;
    enable_debug: boolean;
    agent_api_base_url: string;
  };
  logging: {
    log_level: "DEBUG" | "INFO" | "WARNING" | "ERROR";
    max_log_files: number;
    log_retention_days: number;
    enable_file_logging: boolean;
  };
  security: {
    enable_auth: boolean;
    session_timeout_minutes: number;
    max_login_attempts: number;
    enable_2fa: boolean;
  };
}

const defaultSettings: SystemSettings = {
  general: {
    app_name: "Meta-Agent",
    app_description: "AI Agent自动生成服务",
    default_language: "zh-CN",
    timezone: "Asia/Shanghai",
    theme: "system"
  },
  agent: {
    max_concurrent_agents: 10,
    default_model: "gpt-4",
    default_temperature: 0.7,
    max_response_length: 10000,
    timeout_seconds: 30
  },
  ai_team_generation: {
    enable_ai_team_generation: true,
    team_generation_provider: "openai",
    team_generation_custom_provider_name: "",
    team_generation_model: "gpt-4",
    team_generation_temperature: 0.7,
    team_generation_max_tokens: 4000,
    team_generation_base_url: "",
    team_generation_api_key: ""
  },
  api: {
    rate_limit_per_minute: 100,
    enable_cors: true,
    cors_origins: "http://localhost:3000,http://127.0.0.1:3000",
    enable_docs: true,
    enable_debug: true,
    agent_api_base_url: `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000"}/api/v1/agents`
  },
  logging: {
    log_level: "INFO",
    max_log_files: 10,
    log_retention_days: 30,
    enable_file_logging: true
  },
  security: {
    enable_auth: false,
    session_timeout_minutes: 60,
    max_login_attempts: 5,
    enable_2fa: false
  }
};

// Constants for model selection - defined outside component to prevent recreation
const PREDEFINED_MODELS = ['gpt-4', 'gpt-4-turbo', 'gpt-4o-mini', 'gpt-3.5-turbo', 'claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku', 'gemini-pro'] as const;
const PREDEFINED_MODELS_SET = new Set(PREDEFINED_MODELS);

export default function SettingsPage() {
  const [settings, setSettings] = useState<SystemSettings | null>(null);
  const [hasChanges, setHasChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [loadError, setLoadError] = useState<string | null>(null);
  const [isCustomModelMode, setIsCustomModelMode] = useState(false);
  const { success, error } = useToast();
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const hasLoadedRef = useRef(false);

  // Admin protection - redirect non-admin users
  useEffect(() => {
    if (!authLoading && isAuthenticated && user) {
      if (user.role !== 'admin') {
        error('访问被拒绝：需要管理员权限');
        router.push('/');
        return;
      }
    }
  }, [user, isAuthenticated, authLoading, router, error]);

  // Memoized model selection logic to prevent unnecessary re-renders
  const currentModel = settings?.ai_team_generation?.team_generation_model || 'gpt-4';

  // Determine if we should show custom input based on explicit mode state and model value
  const isCustomModel = useMemo(() => {
    // If we're explicitly in custom mode, stay in custom mode regardless of current value
    if (isCustomModelMode) {
      return true;
    }

    // If not in custom mode, check if current model is a non-predefined model
    // This handles cases where settings are loaded with a custom model from backend
    return !PREDEFINED_MODELS_SET.has(currentModel as any) && currentModel !== '' && currentModel !== 'gpt-4';
  }, [currentModel, isCustomModelMode]);

  // Effect to handle backend model loading - set custom mode if backend has custom model
  useEffect(() => {
    const modelFromBackend = settings?.ai_team_generation?.team_generation_model;
    const isBackendModelCustom = modelFromBackend && 
                                 !PREDEFINED_MODELS_SET.has(modelFromBackend as any) &&
                                 modelFromBackend !== '' &&
                                 modelFromBackend !== 'gpt-4';

    if (isBackendModelCustom && !isCustomModelMode) {
      setIsCustomModelMode(true);
    }
  }, [settings?.ai_team_generation?.team_generation_model, isCustomModelMode]);

  // Optimized update function with useCallback to prevent recreation
  const updateModelSetting = useCallback((value: string) => {
    updateSetting('ai_team_generation', 'team_generation_model', value);
  }, []);

  // Optimized custom model handler
  const handleCustomModelSelect = useCallback(() => {
    setIsCustomModelMode(true);
    // Don't change the actual model value here - let user type it
  }, []);

  // Optimized predefined model handler
  const handlePredefinedModelSelect = useCallback((value: string) => {
    if (value === 'custom') {
      handleCustomModelSelect();
    } else {
      setIsCustomModelMode(false);
      updateModelSetting(value);
    }
  }, [updateModelSetting, handleCustomModelSelect]);

  // Load settings from backend on component mount, but only after authentication and user info are confirmed
  useEffect(() => {
    // Don't do anything while auth is still loading
    if (authLoading) {
      return;
    }

    // If not authenticated, stop loading
    if (!isAuthenticated) {
      setIsLoading(false);
      return;
    }

    // Wait for user info to be loaded
    if (!user) {
      return;
    }

    // If user is not admin, stop loading (will be redirected)
    if (user.role !== 'admin') {
      setIsLoading(false);
      return;
    }

    // If already loaded, don't reload
    if (hasLoadedRef.current) {
      setIsLoading(false);
      return;
    }

    // Only proceed if we have a valid admin user and haven't loaded yet
    let isMounted = true;

    const loadSettings = async () => {
      try {
        setIsLoading(true);
        setLoadError(null); // Clear any previous errors
        const result = await settingsApi.getSettings();

        if (isMounted) {
          setSettings(result);
          hasLoadedRef.current = true; // Mark as loaded only after successful load
          setIsLoading(false);
        }
      } catch (err) {
        console.error('Failed to load settings:', err);
        if (isMounted) {
          const errorMessage = handleApiError(err);
          setLoadError(errorMessage);
          error(`加载设置失败: ${errorMessage}`);
          setIsLoading(false);
          // Don't set hasLoadedRef.current = true on error, allow retry
        }
      }
    };

    loadSettings();

    return () => {
      isMounted = false;
    };
  }, [authLoading, isAuthenticated, user, error]); // Include error in dependencies for proper cleanup

  // Retry function for failed settings load
  const retryLoadSettings = useCallback(() => {
    hasLoadedRef.current = false; // Reset the loaded flag
    setLoadError(null); // Clear error state
    // The useEffect will automatically trigger a reload since hasLoadedRef.current is now false
  }, []);

  const updateSetting = (section: keyof SystemSettings, key: string, value: any) => {
    if (!settings) return;

    setSettings(prev => prev ? ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value
      }
    }) : null);
    setHasChanges(true);
  };

  const handleSave = async () => {
    if (!settings) return;

    setIsSaving(true);
    try {
      const result = await settingsApi.updateSettings(settings);
      setSettings(result);
      setHasChanges(false);
      success("设置已保存");
    } catch (err) {
      console.error('Failed to save settings:', err);
      error(`保存设置失败: ${handleApiError(err)}`);
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = async () => {
    setIsSaving(true);
    try {
      const result = await settingsApi.resetSettings();
      setSettings(result);
      setHasChanges(false);
      success("设置已重置为默认值");
    } catch (err) {
      console.error('Failed to reset settings:', err);
      error(`重置设置失败: ${handleApiError(err)}`);
    } finally {
      setIsSaving(false);
    }
  };

  const exportSettings = async () => {
    try {
      const result = await settingsApi.exportSettings();
      const blob = new Blob([JSON.stringify(result, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `meta-agent-settings-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      success("设置已导出");
    } catch (err) {
      console.error('Failed to export settings:', err);
      error(`导出设置失败: ${handleApiError(err)}`);
    }
  };

  const importSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const importedSettings = JSON.parse(e.target?.result as string);
          setSettings(importedSettings);
          setHasChanges(false);
          success("设置已导入");
        } catch (err) {
          console.error('Failed to import settings:', err);
          error(`导入设置失败: ${handleApiError(err)}`);
        }
      };
      reader.readAsText(file);
    }
  };

  // Show loading state while auth is loading or settings are loading
  if (authLoading || isLoading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">
              {authLoading ? '验证权限中...' : '加载设置中...'}
            </p>
          </div>
        </div>
      </MainLayout>
    );
  }

  // Prevent rendering for non-admin users (additional protection)
  if (!user || user.role !== 'admin') {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-muted-foreground">访问被拒绝：需要管理员权限</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex-1 min-w-0">
            <h1 className="text-2xl md:text-3xl font-bold">⚙️ 系统配置</h1>
            <p className="text-muted-foreground text-sm md:text-base">
              配置系统参数和运行环境
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
            <input
              type="file"
              accept=".json"
              onChange={importSettings}
              className="hidden"
              id="import-settings"
            />
            <Button
              variant="outline"
              onClick={() => document.getElementById('import-settings')?.click()}
              className="touch-target min-h-[44px] md:min-h-[36px] mobile-button-spacing"
            >
              📥 导入配置
            </Button>
            <Button
              variant="outline"
              onClick={exportSettings}
              className="touch-target min-h-[44px] md:min-h-[36px] mobile-button-spacing"
            >
              📤 导出配置
            </Button>
            <Button
              variant="outline"
              onClick={handleReset}
              className="touch-target min-h-[44px] md:min-h-[36px] mobile-button-spacing"
            >
              🔄 重置默认
            </Button>
            <Button
              onClick={handleSave}
              disabled={!hasChanges || isSaving}
              className="touch-target min-h-[44px] md:min-h-[36px] mobile-button-spacing"
            >
              {isSaving ? "保存中..." : "💾 保存设置"}
            </Button>
          </div>
        </div>

        {/* Changes Alert */}
        {hasChanges && (
          <Alert>
            <span className="text-lg">⚠️</span>
            <AlertDescription>
              你有未保存的更改。请记得保存设置以使更改生效。
            </AlertDescription>
          </Alert>
        )}

        {/* Settings Content */}
        {settings ? (
          <>
            {/* General Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg md:text-xl">基础设置</CardTitle>
                <CardDescription className="text-sm mobile-text-sm">应用程序的基本配置</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 mobile-card-spacing">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4 mobile-grid-gap">
                  <div className="space-y-2">
                    <Label htmlFor="app_name" className="text-sm mobile-text-sm">应用名称</Label>
                    <Input
                      id="app_name"
                      value={settings.general?.app_name || ''}
                      onChange={(e) => updateSetting('general', 'app_name', e.target.value)}
                      className="mobile-form-element"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="default_language" className="text-sm mobile-text-sm">默认语言</Label>
                    <Select
                      value={settings.general?.default_language || 'zh-CN'}
                      onValueChange={(value) => updateSetting('general', 'default_language', value)}
                    >
                      <SelectTrigger className="touch-target min-h-[44px] md:min-h-[36px]">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="zh-CN">中文</SelectItem>
                        <SelectItem value="en">English</SelectItem>
                        <SelectItem value="ja">日本語</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="timezone" className="text-sm mobile-text-sm">时区</Label>
                    <Select
                      value={settings.general?.timezone || 'Asia/Shanghai'}
                      onValueChange={(value) => updateSetting('general', 'timezone', value)}
                    >
                      <SelectTrigger className="touch-target min-h-[44px] md:min-h-[36px]">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Asia/Shanghai">Asia/Shanghai</SelectItem>
                        <SelectItem value="UTC">UTC</SelectItem>
                        <SelectItem value="America/New_York">America/New_York</SelectItem>
                        <SelectItem value="Europe/London">Europe/London</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="theme" className="text-sm mobile-text-sm">主题</Label>
                    <Select
                      value={settings.general?.theme || 'system'}
                      onValueChange={(value) => updateSetting('general', 'theme', value)}
                    >
                      <SelectTrigger className="touch-target min-h-[44px] md:min-h-[36px]">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="light">浅色</SelectItem>
                        <SelectItem value="dark">深色</SelectItem>
                        <SelectItem value="system">跟随系统</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="app_description" className="text-sm mobile-text-sm">应用描述</Label>
                  <Textarea
                    id="app_description"
                    value={settings.general?.app_description || ''}
                    onChange={(e) => updateSetting('general', 'app_description', e.target.value)}
                    rows={3}
                    className="mobile-form-element"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Agent Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg md:text-xl">Agent设置</CardTitle>
                <CardDescription className="text-sm mobile-text-sm">AI Agent的运行参数配置</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 mobile-card-spacing">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4 mobile-grid-gap">
                  <div className="space-y-2">
                    <Label htmlFor="max_concurrent_agents" className="text-sm mobile-text-sm">最大并发Agent数</Label>
                    <Input
                      id="max_concurrent_agents"
                      type="number"
                      min="1"
                      max="100"
                      value={settings.agent?.max_concurrent_agents || 10}
                      onChange={(e) => updateSetting('agent', 'max_concurrent_agents', parseInt(e.target.value))}
                      className="mobile-form-element"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="default_model" className="text-sm mobile-text-sm">默认模型</Label>
                    <Input
                      id="default_model"
                      value={settings.agent?.default_model || 'gpt-4'}
                      onChange={(e) => updateSetting('agent', 'default_model', e.target.value)}
                      className="mobile-form-element"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="default_temperature" className="text-sm mobile-text-sm">默认温度</Label>
                    <Input
                      id="default_temperature"
                      type="number"
                      min="0"
                      max="2"
                      step="0.1"
                      value={settings.agent?.default_temperature || 0.7}
                      onChange={(e) => updateSetting('agent', 'default_temperature', parseFloat(e.target.value))}
                      className="mobile-form-element"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="max_response_length" className="text-sm mobile-text-sm">最大响应长度</Label>
                    <Input
                      id="max_response_length"
                      type="number"
                      min="100"
                      max="100000"
                      value={settings.agent?.max_response_length || 10000}
                      onChange={(e) => updateSetting('agent', 'max_response_length', parseInt(e.target.value))}
                      className="mobile-form-element"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="timeout_seconds" className="text-sm mobile-text-sm">超时时间(秒)</Label>
                    <Input
                      id="timeout_seconds"
                      type="number"
                      min="5"
                      max="300"
                      value={settings.agent?.timeout_seconds || 30}
                      onChange={(e) => updateSetting('agent', 'timeout_seconds', parseInt(e.target.value))}
                      className="mobile-form-element"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* AI Team Generation Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg md:text-xl">AI团队生成设置</CardTitle>
                <CardDescription className="text-sm mobile-text-sm">
                  配置AI自动生成团队的参数。这些设置用于团队生成过程，与Agent运行时的AI配置分开管理。
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 mobile-card-spacing">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="enable_ai_team_generation"
                    checked={settings.ai_team_generation?.enable_ai_team_generation || false}
                    onCheckedChange={(checked) => updateSetting('ai_team_generation', 'enable_ai_team_generation', checked)}
                  />
                  <Label htmlFor="enable_ai_team_generation" className="text-sm mobile-text-sm">启用AI团队生成</Label>
                </div>

                {settings.ai_team_generation?.enable_ai_team_generation && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4 mobile-grid-gap">
                    <div className="space-y-2">
                      <Label htmlFor="team_generation_provider" className="text-sm mobile-text-sm">AI提供商</Label>
                      <Select
                        value={settings.ai_team_generation?.team_generation_provider || 'openai'}
                        onValueChange={(value) => updateSetting('ai_team_generation', 'team_generation_provider', value)}
                      >
                        <SelectTrigger className="touch-target min-h-[44px] md:min-h-[36px]">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="openai">OpenAI</SelectItem>
                          <SelectItem value="anthropic">Anthropic</SelectItem>
                          <SelectItem value="azure">Azure OpenAI</SelectItem>
                          <SelectItem value="custom">自定义</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="team_generation_model" className="text-sm mobile-text-sm">模型</Label>
                      <Input
                        id="team_generation_model"
                        value={settings.ai_team_generation?.team_generation_model || 'gpt-4'}
                        onChange={(e) => updateSetting('ai_team_generation', 'team_generation_model', e.target.value)}
                        className="mobile-form-element"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="team_generation_temperature">温度</Label>
                      <Input
                        id="team_generation_temperature"
                        type="number"
                        min="0"
                        max="2"
                        step="0.1"
                        value={settings.ai_team_generation?.team_generation_temperature || 0.7}
                        onChange={(e) => updateSetting('ai_team_generation', 'team_generation_temperature', parseFloat(e.target.value))}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="team_generation_max_tokens">最大Token数</Label>
                      <Input
                        id="team_generation_max_tokens"
                        type="number"
                        min="100"
                        max="10000"
                        value={settings.ai_team_generation?.team_generation_max_tokens || 4000}
                        onChange={(e) => updateSetting('ai_team_generation', 'team_generation_max_tokens', parseInt(e.target.value))}
                      />
                    </div>
                    {(settings.ai_team_generation?.team_generation_provider === 'azure' ||
                      settings.ai_team_generation?.team_generation_provider === 'custom') && (
                      <div className="space-y-2">
                        <Label htmlFor="team_generation_base_url">基础URL</Label>
                        <Input
                          id="team_generation_base_url"
                          value={settings.ai_team_generation?.team_generation_base_url || ''}
                          onChange={(e) => updateSetting('ai_team_generation', 'team_generation_base_url', e.target.value)}
                          placeholder={
                            settings.ai_team_generation?.team_generation_provider === 'azure'
                              ? "https://your-resource.openai.azure.com"
                              : "https://your-api.com/v1"
                          }
                        />
                      </div>
                    )}
                    <div className="space-y-2">
                      <Label htmlFor="team_generation_api_key">API密钥</Label>
                      <Input
                        id="team_generation_api_key"
                        type="password"
                        value={settings.ai_team_generation?.team_generation_api_key || ''}
                        onChange={(e) => updateSetting('ai_team_generation', 'team_generation_api_key', e.target.value)}
                        placeholder="输入API密钥"
                      />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* API Settings */}
            <Card>
              <CardHeader>
                <CardTitle>API设置</CardTitle>
                <CardDescription>API服务的配置参数</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="rate_limit_per_minute">每分钟请求限制</Label>
                    <Input
                      id="rate_limit_per_minute"
                      type="number"
                      min="1"
                      max="10000"
                      value={settings.api?.rate_limit_per_minute || 100}
                      onChange={(e) => updateSetting('api', 'rate_limit_per_minute', parseInt(e.target.value))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="agent_api_base_url">Agent API基础URL</Label>
                    <Input
                      id="agent_api_base_url"
                      value={settings.api?.agent_api_base_url || ''}
                      onChange={(e) => updateSetting('api', 'agent_api_base_url', e.target.value)}
                      placeholder="http://localhost:8000/api/v1/agents"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="cors_origins">CORS允许的源</Label>
                  <Input
                    id="cors_origins"
                    value={settings.api?.cors_origins || ''}
                    onChange={(e) => updateSetting('api', 'cors_origins', e.target.value)}
                    placeholder="http://localhost:3000,http://127.0.0.1:3000"
                  />
                </div>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="enable_cors"
                      checked={settings.api?.enable_cors || false}
                      onCheckedChange={(checked) => updateSetting('api', 'enable_cors', checked)}
                    />
                    <Label htmlFor="enable_cors">启用CORS</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="enable_docs"
                      checked={settings.api?.enable_docs || false}
                      onCheckedChange={(checked) => updateSetting('api', 'enable_docs', checked)}
                    />
                    <Label htmlFor="enable_docs">启用API文档</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="enable_debug"
                      checked={settings.api?.enable_debug || false}
                      onCheckedChange={(checked) => updateSetting('api', 'enable_debug', checked)}
                    />
                    <Label htmlFor="enable_debug">启用调试模式</Label>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Logging Settings */}
            <Card>
              <CardHeader>
                <CardTitle>日志设置</CardTitle>
                <CardDescription>系统日志的配置参数</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="log_level">日志级别</Label>
                    <Select
                      value={settings.logging?.log_level || 'INFO'}
                      onValueChange={(value) => updateSetting('logging', 'log_level', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="DEBUG">DEBUG</SelectItem>
                        <SelectItem value="INFO">INFO</SelectItem>
                        <SelectItem value="WARNING">WARNING</SelectItem>
                        <SelectItem value="ERROR">ERROR</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="max_log_files">最大日志文件数</Label>
                    <Input
                      id="max_log_files"
                      type="number"
                      min="1"
                      max="100"
                      value={settings.logging?.max_log_files || 10}
                      onChange={(e) => updateSetting('logging', 'max_log_files', parseInt(e.target.value))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="log_retention_days">日志保留天数</Label>
                    <Input
                      id="log_retention_days"
                      type="number"
                      min="1"
                      max="365"
                      value={settings.logging?.log_retention_days || 30}
                      onChange={(e) => updateSetting('logging', 'log_retention_days', parseInt(e.target.value))}
                    />
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="enable_file_logging"
                    checked={settings.logging?.enable_file_logging || false}
                    onCheckedChange={(checked) => updateSetting('logging', 'enable_file_logging', checked)}
                  />
                  <Label htmlFor="enable_file_logging">启用文件日志</Label>
                </div>
              </CardContent>
            </Card>

            {/* Security Settings */}
            <Card>
              <CardHeader>
                <CardTitle>安全设置</CardTitle>
                <CardDescription>系统安全相关配置</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="session_timeout_minutes">会话超时(分钟)</Label>
                    <Input
                      id="session_timeout_minutes"
                      type="number"
                      min="5"
                      max="1440"
                      value={settings.security?.session_timeout_minutes || 60}
                      onChange={(e) => updateSetting('security', 'session_timeout_minutes', parseInt(e.target.value))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="max_login_attempts">最大登录尝试次数</Label>
                    <Input
                      id="max_login_attempts"
                      type="number"
                      min="1"
                      max="20"
                      value={settings.security?.max_login_attempts || 5}
                      onChange={(e) => updateSetting('security', 'max_login_attempts', parseInt(e.target.value))}
                    />
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="enable_auth"
                      checked={settings.security?.enable_auth || false}
                      onCheckedChange={(checked) => updateSetting('security', 'enable_auth', checked)}
                    />
                    <Label htmlFor="enable_auth">启用身份验证</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="enable_2fa"
                      checked={settings.security?.enable_2fa || false}
                      onCheckedChange={(checked) => updateSetting('security', 'enable_2fa', checked)}
                    />
                    <Label htmlFor="enable_2fa">启用双因素认证</Label>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* System Status */}
            <Card>
              <CardHeader>
                <CardTitle>系统状态</CardTitle>
                <CardDescription>当前系统运行状态信息</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="space-y-2">
                    <Label>应用版本</Label>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">v1.0.0</Badge>
                      <span className="text-sm text-muted-foreground">最新版本</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>运行环境</Label>
                    <Badge variant="secondary">开发环境</Badge>
                  </div>
                  <div className="space-y-2">
                    <Label>数据库状态</Label>
                    <Badge className="bg-green-100 text-green-800">正常连接</Badge>
                  </div>
                  <div className="space-y-2">
                    <Label>API状态</Label>
                    <Badge className="bg-green-100 text-green-800">服务正常</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </>
        ) : (
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col items-center justify-center space-y-4">
                <div className="flex items-center space-x-2">
                  <span className="text-lg">⚠️</span>
                  <span>设置加载失败</span>
                </div>
                {loadError && (
                  <p className="text-sm text-muted-foreground text-center">
                    错误详情: {loadError}
                  </p>
                )}
                <Button onClick={retryLoadSettings} variant="outline">
                  🔄 重试加载
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </MainLayout>
  );
}
