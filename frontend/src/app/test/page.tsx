"use client";

import { useState, useEffect, Suspense } from "react";
import { useSearchParams } from "next/navigation";
import { motion } from "framer-motion";
import { MainLayout } from "@/components/layout/main-layout";
import { AgentSelector } from "@/components/features/agent-testing/agent-selector";
import { TestInterface } from "@/components/features/agent-testing/test-interface";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { LoadingPage, LoadingOverlay } from "@/components/ui/loading";
import { api } from "@/lib/api";
import { Agent } from "@/lib/types";
import { useAuth } from "@/lib/auth";
import { TestTube } from "lucide-react";

function TestPageContent() {
  const searchParams = useSearchParams();
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const [agents, setAgents] = useState<Agent[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 加载Agent数据
  useEffect(() => {
    loadAgents();
  }, []);

  // 从URL参数中获取Agent ID并自动选择
  useEffect(() => {
    // Support both 'agent' and 'agent_id' parameters for compatibility
    const agentId = searchParams.get('agent_id') || searchParams.get('agent');
    if (agentId && agents.length > 0) {
      const agent = agents.find(a => a.agent_id === agentId);
      if (agent) {
        setSelectedAgent(agent);
      }
    }
  }, [searchParams, agents]);

  const loadAgents = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await api.agents.list();

      if (response.success && response.data) {
        const agentList = response.data.data || [];
        // 只显示活跃的Agent
        const activeAgents = agentList.filter(agent => agent.status === "active");
        setAgents(activeAgents);

        // Don't auto-select an agent - let users choose explicitly
        // This ensures the agent selection interface is prominently displayed
      } else {
        setError(response.error?.message || "Failed to load agents");
      }
    } catch (err) {
      setError("Failed to load agents");
    } finally {
      setLoading(false);
    }
  };

  const handleAgentSelect = (agent: Agent) => {
    setSelectedAgent(agent);
    // Note: Tab switching will be handled by the TestInterface component
  };

  const handleAgentChange = () => {
    setSelectedAgent(null);
  };

  const handleTestSubmit = async (input: string, options: any, aiOverride?: any, responseMetadata?: any) => {
    // The actual API call is now handled in the TestInterface component
    // This function is kept for compatibility but doesn't need to do anything
  };

  return (
    <MainLayout>
      <div className="max-w-6xl mx-auto space-y-4 md:space-y-6 px-3 md:px-0">
        {/* Header */}
        <motion.div
          className="text-center space-y-3 md:space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center justify-center space-x-2">
            <TestTube className="h-6 md:h-8 w-6 md:w-8 text-primary" />
            <h1 className="text-xl md:text-3xl font-bold">Agent测试</h1>
          </div>
          <p className="text-sm md:text-lg text-muted-foreground max-w-2xl mx-auto mobile-text-base px-2 md:px-0">
            实时测试您的AI团队，查看执行过程和结果
          </p>
        </motion.div>

        {/* Error Message */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="pt-4 md:pt-6 mobile-card-spacing">
              <div className="flex items-center space-x-2">
                <div className="text-red-600">⚠️</div>
                <div>
                  <p className="text-red-800 font-medium text-sm md:text-base">加载Agent时出现错误</p>
                  <p className="text-red-600 text-xs md:text-sm mobile-text-sm">{error}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {loading ? (
          <LoadingPage message="加载Agent列表中..." />
        ) : (
          <>
            {/* Agent Selection - Only show when no agent is selected */}
            {!selectedAgent && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
              >
                <AgentSelector
                  agents={agents}
                  selectedAgent={selectedAgent}
                  onAgentSelect={handleAgentSelect}
                />
              </motion.div>
            )}

            {/* Main Testing Interface - Show when agent is selected */}
            {selectedAgent && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <TestInterface
                  agent={selectedAgent}
                  agents={agents}
                  onTestSubmit={handleTestSubmit}
                  onAgentChange={handleAgentChange}
                  initialParams={{
                    input_text: searchParams.get('input_text'),
                    ai_config: searchParams.get('ai_config'),
                    api_key_name: searchParams.get('api_key_name')
                  }}
                />
              </motion.div>
            )}
          </>
        )}
      </div>
    </MainLayout>
  );
}

export default function TestPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <TestPageContent />
    </Suspense>
  );
}
