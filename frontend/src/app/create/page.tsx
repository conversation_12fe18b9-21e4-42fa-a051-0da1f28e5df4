"use client";

import { useState, useEffect, Suspense } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { MainLayout } from "@/components/layout/main-layout";
import { AgentCreationForm } from "@/components/features/agent-creation/agent-creation-form";
import { PlanningResult } from "@/components/features/agent-creation/planning-result";
import { CreationProgress } from "@/components/features/agent-creation/creation-progress";
import { AgentCreationLoading } from "@/components/common/enhanced-loading";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useToast, ToastContainer } from "@/components/common/toast";
import { Sparkles } from "lucide-react";

import { api } from "@/lib/api";
import { TeamPlan, RequirementAnalysis, CreateAgentResponse } from "@/lib/types";

type CreationStep = "form" | "planning" | "confirmation" | "generating" | "completed";



function CreateAgentPageContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { success, toasts, removeToast } = useToast();
  const [currentStep, setCurrentStep] = useState<CreationStep>("form");
  const [description, setDescription] = useState("");
  const [teamPlan, setTeamPlan] = useState<TeamPlan | null>(null);
  const [taskId, setTaskId] = useState<string | null>(null);
  const [result, setResult] = useState<CreateAgentResponse | null>(null);

  const [analysis, setAnalysis] = useState<RequirementAnalysis | null>(null);
  const [runtimeAiConfig, setRuntimeAiConfig] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);


  // Copy to clipboard function
  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      success(`${label}已复制到剪贴板`);
    } catch (err) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      success(`${label}已复制到剪贴板`);
    }
  };

  // 从URL参数中获取模板信息
  useEffect(() => {
    // 兼容旧的prompt参数
    const templatePrompt = searchParams.get('prompt');
    if (templatePrompt) {
      setDescription(decodeURIComponent(templatePrompt));
    }
  }, [searchParams]);





  const handleFormSubmit = async (formData: { description: string; options?: any }) => {
    try {
      setDescription(formData.description);
      setCurrentStep("planning");
      setLoading(true);
      setError(null);

      // Store runtime AI configuration for later use when creating the agent
      const runtimeAiConfig = formData.options ? {
        provider: "openai", // Default provider, could be made configurable
        model: formData.options.model || "gpt-4",
        temperature: formData.options.temperature || 0.7,
        max_tokens: 2000, // Default value, could be made configurable
      } : undefined;

      // Store runtime AI config for agent creation
      setRuntimeAiConfig(runtimeAiConfig);

      // 使用团队生成API（使用系统设置中的AI配置，不传递用户的运行时配置）
      const planningResponse = await api.planning.analyze(formData.description);

      if (planningResponse.success && planningResponse.data) {
        // 设置生成的团队计划
        setTeamPlan(planningResponse.data.team_plan);
        setCurrentStep("confirmation");
      } else {
        throw new Error(planningResponse.error?.message || "Failed to generate team plan");
      }
    } catch (err: any) {
      setError(err.message || "规划过程中出现错误");
      setCurrentStep("form");
    } finally {
      setLoading(false);
    }
  };

  const handlePlanConfirm = async () => {
    if (!teamPlan) return;

    try {
      setCurrentStep("generating");
      setLoading(true);
      setError(null);

      // 调用Agent创建API
      const agentCreateRequest = {
        ...teamPlan,
      };

      const response = await api.agents.create(agentCreateRequest);

      if (response.success && response.data) {
        setResult(response.data);
        setCurrentStep("completed");
      } else {
        throw new Error(response.error?.message || "Failed to create agent");
      }
    } catch (err: any) {
      setError(err.message || "创建Agent时出现错误");
      setCurrentStep("confirmation");
    } finally {
      setLoading(false);
    }
  };

  const handlePlanReject = () => {
    setCurrentStep("form");
    setTeamPlan(null);
    setAnalysis(null);
    setError(null);
  };

  const handleStartOver = () => {
    setCurrentStep("form");
    setDescription("");
    setTeamPlan(null);
    setTaskId(null);
    setResult(null);
    setAnalysis(null);
    setError(null);
  };

  return (
    <MainLayout>
      <div className="max-w-4xl mx-auto space-y-4 md:space-y-6 px-3 md:px-0">
        {/* Header */}
        <motion.div
          className="text-center space-y-3 md:space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center justify-center space-x-2">
            <Sparkles className="h-6 md:h-8 w-6 md:w-8 text-primary" />
            <h1 className="text-xl md:text-3xl font-bold">创建AI Agent团队</h1>
          </div>
          <p className="text-sm md:text-lg text-muted-foreground max-w-2xl mx-auto mobile-text-base px-2 md:px-0">
            通过AI驱动的智能规划，为您量身定制专业的AI助手团队
          </p>
        </motion.div>

        {/* Progress Indicator */}
        <CreationProgress currentStep={currentStep} />

        {/* Error Message */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="pt-4 md:pt-6 mobile-card-spacing">
              <div className="flex items-center space-x-2">
                <div className="text-red-600">⚠️</div>
                <div>
                  <p className="text-red-800 font-medium text-sm md:text-base">创建过程中出现错误</p>
                  <p className="text-red-600 text-xs md:text-sm mobile-text-sm">{error}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Main Content */}
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.4 }}
          className="space-y-4 md:space-y-6"
        >
          {currentStep === "form" && (
            <AgentCreationForm
              onSubmit={handleFormSubmit}
              initialDescription={description}
              disabled={loading}
            />
          )}

          {currentStep === "planning" && (
            <AgentCreationLoading
              currentStep="planning"
              progress={25}
              onCancel={() => {
                setCurrentStep("form");
                setTeamPlan(null);
              }}
            />
          )}

          {currentStep === "confirmation" && teamPlan && (
            <PlanningResult
              plan={teamPlan}
              description={description}
              onConfirm={handlePlanConfirm}
              onReject={handlePlanReject}
            />
          )}

          {currentStep === "generating" && (
            <AgentCreationLoading
              currentStep="generating"
              progress={75}
              onCancel={() => {
                setCurrentStep("form");
                setTeamPlan(null);
                setTaskId(null);
              }}
            />
          )}

          {currentStep === "completed" && (
            <Card>
              <CardHeader>
                <CardTitle>✅ Agent创建成功！</CardTitle>
                <CardDescription>
                  你的Agent团队已经准备就绪
                </CardDescription>
              </CardHeader>
              <CardContent>
                {result && (
                  <div className="space-y-4">
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-semibold mb-2 text-sm md:text-base">Agent ID</h4>
                        <div className="flex items-center gap-2">
                          <code className="text-xs md:text-sm bg-muted p-2 rounded flex-1 break-all mobile-text-sm">{result.agent_id}</code>
                          <button
                            onClick={() => copyToClipboard(result.agent_id, "Agent ID")}
                            className="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors touch-target min-h-[44px] md:min-h-[32px]"
                            title="复制Agent ID"
                          >
                            📋
                          </button>
                        </div>
                      </div>
                      <div>
                        <h4 className="font-semibold mb-2 text-sm md:text-base">API端点</h4>
                        <div className="flex items-start gap-2">
                          <code className="text-xs md:text-sm bg-muted p-2 rounded flex-1 break-all whitespace-pre-wrap mobile-text-sm">{result.api_endpoint}</code>
                          <button
                            onClick={() => copyToClipboard(result.api_endpoint, "API端点")}
                            className="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors flex-shrink-0 touch-target min-h-[44px] md:min-h-[32px]"
                            title="复制API端点"
                          >
                            📋
                          </button>
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-col sm:flex-row gap-2">
                      <button
                        onClick={handleStartOver}
                        className="px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90 touch-target min-h-[44px] md:min-h-[36px] mobile-button-spacing"
                      >
                        创建新Agent
                      </button>
                      <button
                        className="px-4 py-2 border rounded hover:bg-accent touch-target min-h-[44px] md:min-h-[36px] mobile-button-spacing"
                        onClick={() => {
                          if (result?.agent_id) {
                            window.location.href = `/test?agent=${result.agent_id}`;
                          }
                        }}
                      >
                        测试Agent
                      </button>
                      <button
                        className="px-4 py-2 border rounded hover:bg-accent touch-target min-h-[44px] md:min-h-[36px] mobile-button-spacing"
                        onClick={() => {
                          if (result?.agent_id) {
                            router.push(`/manage?agent=${result.agent_id}`);
                          }
                        }}
                      >
                        查看详情
                      </button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </motion.div>
      </div>
      <ToastContainer toasts={toasts} onRemove={removeToast} />
    </MainLayout>
  );
}

export default function CreateAgentPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <CreateAgentPageContent />
    </Suspense>
  );
}
