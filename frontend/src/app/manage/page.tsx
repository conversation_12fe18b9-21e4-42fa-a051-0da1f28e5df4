"use client";

import { useState, useEffect, Suspense } from "react";
import { useSearchParams } from "next/navigation";
import { MainLayout } from "@/components/layout/main-layout";
import { LazyAgentList } from "@/components/features/agent-management/lazy-agent-list";
import { AgentFiltersSkeleton } from "@/components/features/agent-management/agent-card-skeleton";
import { AgentFilters } from "@/components/features/agent-management/agent-filters";
import { Button } from "@/components/ui/button";
import { LoadingOverlay, LoadingPage } from "@/components/ui/loading";
import { ErrorDisplay } from "@/components/common/error-display";
import Link from "next/link";
import { api } from "@/lib/api";
import { Agent, AgentUpdate } from "@/lib/types";
import { useAuth } from "@/lib/auth";
import { useToast } from "@/hooks/use-toast";

// Mock data for development (kept for reference)
/*
const mockAgents = [
  {
    agent_id: "zen-rizzo-001",
    team_name: "禅探二人组",
    description: "一个由禅意僧侣和街头老兵组成的侦探二人组",
    status: "active" as const,
    created_at: "2025-06-30T10:00:00Z",
    last_used: "2025-06-30T11:30:00Z",
    usage_count: 15,
    specialists: [
      { name: "Zen", role: "哲学思考者" },
      { name: "Rizzo", role: "实用主义者" }
    ]
  },
  {
    agent_id: "tech-team-002",
    team_name: "技术咨询团队",
    description: "包含架构师、前端专家和后端专家的技术咨询团队",
    status: "active" as const,
    created_at: "2025-06-29T14:20:00Z",
    last_used: "2025-06-30T09:15:00Z",
    usage_count: 8,
    specialists: [
      { name: "Alex", role: "架构师" },
      { name: "Sarah", role: "前端专家" },
      { name: "Mike", role: "后端专家" }
    ]
  },
  {
    agent_id: "creative-writers-003",
    team_name: "创意写作工作室",
    description: "专业的创意写作团队，帮助用户创作各类文学作品",
    status: "inactive" as const,
    created_at: "2025-06-28T16:45:00Z",
    last_used: "2025-06-29T20:30:00Z",
    usage_count: 23,
    specialists: [
      { name: "Emma", role: "故事构思师" },
      { name: "David", role: "角色设计师" },
      { name: "Lisa", role: "情节编辑" }
    ]
  }
];
*/

function ManageAgentsPageContent() {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const searchParams = useSearchParams();
  const { toast } = useToast();
  const [agents, setAgents] = useState<Agent[]>([]);
  const [filteredAgents, setFilteredAgents] = useState<Agent[]>([]);
  const [selectedAgents, setSelectedAgents] = useState<string[]>([]);
  const [highlightedAgent, setHighlightedAgent] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [processingAgents, setProcessingAgents] = useState<Set<string>>(new Set());

  // 加载Agent数据
  useEffect(() => {
    loadAgents();
  }, []);

  // 从URL参数中获取要高亮的Agent ID
  useEffect(() => {
    const agentId = searchParams.get('agent');
    if (agentId) {
      setHighlightedAgent(agentId);
      // 3秒后取消高亮
      setTimeout(() => setHighlightedAgent(null), 3000);
    }
  }, [searchParams]);

  const loadAgents = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load agents from backend API
      const response = await api.agents.list();

      if (response.success && response.data) {
        const agentList = response.data.items || response.data.data || [];
        setAgents(agentList);
        setFilteredAgents(agentList);
      } else {
        throw new Error(response.error?.message || "Failed to load agents from server");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load agents");
      console.error("Error loading agents:", err);
      // Set empty arrays on error instead of falling back to local storage
      setAgents([]);
      setFilteredAgents([]);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (filters: {
    status?: string;
    search?: string;
    sortBy?: string;
  }) => {
    let filtered = [...agents];

    // Status filter
    if (filters.status && filters.status !== "all") {
      filtered = filtered.filter(agent => agent.status === filters.status);
    }

    // Search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(agent => 
        agent.team_name.toLowerCase().includes(searchLower) ||
        agent.description.toLowerCase().includes(searchLower) ||
        agent.agent_id.toLowerCase().includes(searchLower)
      );
    }

    // Sort
    if (filters.sortBy) {
      switch (filters.sortBy) {
        case "name":
          filtered.sort((a, b) => a.team_name.localeCompare(b.team_name));
          break;
        case "created":
          filtered.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
          break;
        case "usage":
          filtered.sort((a, b) => (b.usage_count || 0) - (a.usage_count || 0));
          break;
        case "last_used":
          filtered.sort((a, b) => new Date(b.last_used || 0).getTime() - new Date(a.last_used || 0).getTime());
          break;
      }
    }

    setFilteredAgents(filtered);
  };

  const handleAgentSelect = (agentId: string, selected: boolean) => {
    if (selected) {
      setSelectedAgents([...selectedAgents, agentId]);
    } else {
      setSelectedAgents(selectedAgents.filter(id => id !== agentId));
    }
  };

  const handleBulkAction = async (action: string) => {
    try {
      switch (action) {
        case "delete":
          // Delete selected agents via API
          for (const agentId of selectedAgents) {
            const response = await api.agents.delete(agentId);
            if (!response.success) {
              throw new Error(response.error?.message || `Failed to delete agent ${agentId}`);
            }
          }
          break;
        case "activate":
          // Activate selected agents via API
          for (const agentId of selectedAgents) {
            const response = await api.agents.updateStatus(agentId, "active");
            if (!response.success) {
              throw new Error(response.error?.message || `Failed to activate agent ${agentId}`);
            }
          }
          break;
        case "deactivate":
          // Deactivate selected agents via API
          for (const agentId of selectedAgents) {
            const response = await api.agents.updateStatus(agentId, "inactive");
            if (!response.success) {
              throw new Error(response.error?.message || `Failed to deactivate agent ${agentId}`);
            }
          }
          break;
      }

      // Reload data and clear selection
      await loadAgents();
      setSelectedAgents([]);
    } catch (err) {
      console.error(`Error performing bulk action ${action}:`, err);
      setError(err instanceof Error ? err.message : `Failed to perform ${action} operation`);
    }
  };

  const handleAgentAction = async (agentId: string, action: string) => {
    // Add agent to processing set
    setProcessingAgents(prev => new Set(prev).add(agentId));

    try {
      switch (action) {
        case "delete":
          const agent = agents.find(a => a.agent_id === agentId);
          const deleteResponse = await api.agents.delete(agentId);
          if (!deleteResponse.success) {
            throw new Error(deleteResponse.error?.message || "Failed to delete agent");
          }
          await loadAgents();
          toast({
            title: "删除成功",
            description: `Agent "${agent?.team_name || agentId}" 已成功删除`,
          });
          break;
        case "toggle_status":
          const agentToUpdate = agents.find(a => a.agent_id === agentId);
          if (agentToUpdate) {
            const newStatus = agentToUpdate.status === "active" ? "inactive" : "active";
            const updateResponse = await api.agents.updateStatus(agentId, newStatus);

            if (!updateResponse.success) {
              throw new Error(updateResponse.error?.message || "Failed to update agent status");
            }

            await loadAgents();
            toast({
              title: "状态更新成功",
              description: `Agent "${agentToUpdate.team_name}" 已${newStatus === "active" ? "激活" : "停用"}`,
            });
          } else {
            throw new Error("Agent not found");
          }
          break;
      }
    } catch (err) {
      console.error(`Error performing action ${action} on agent ${agentId}:`, err);
      const errorMessage = err instanceof Error ? err.message : `Failed to perform ${action} operation`;
      setError(errorMessage);
      toast({
        title: action === "delete" ? "删除失败" : "操作失败",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      // Remove agent from processing set
      setProcessingAgents(prev => {
        const newSet = new Set(prev);
        newSet.delete(agentId);
        return newSet;
      });
    }
  };

  const handleAgentEdit = async (agentId: string, updates: AgentUpdate) => {
    try {
      const updateResponse = await api.agents.update(agentId, updates);

      if (!updateResponse.success) {
        throw new Error(updateResponse.error?.message || "Failed to update agent");
      }

      // Refresh the agent list to show updated data
      await loadAgents();
    } catch (err) {
      console.error(`Error updating agent ${agentId}:`, err);
      setError(err instanceof Error ? err.message : "Failed to update agent");
      throw err; // Re-throw to let the dialog handle the error
    }
  };

  // Show loading while checking authentication
  if (authLoading || loading) {
    return (
      <MainLayout>
        <LoadingPage message="正在加载Agent数据..." />
      </MainLayout>
    );
  }

  // Show error if there's an error
  if (error) {
    return (
      <MainLayout>
        <div className="container mx-auto py-8">
          <ErrorDisplay
            title="加载失败"
            message={error}
            description="无法加载Agent列表，请检查网络连接或稍后重试"
            onRetry={loadAgents}
            retryText="重新加载"
          />
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <LoadingOverlay isLoading={loading} loadingText="正在加载Agent数据...">
        <div className="space-y-4 md:space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex-1 min-w-0">
            <h1 className="text-2xl md:text-3xl font-bold">Agent管理</h1>
            <p className="text-muted-foreground text-sm md:text-base">
              管理你的AI Agent团队，监控状态和使用情况
            </p>
          </div>
          <Button asChild className="w-full sm:w-auto min-h-[44px] md:min-h-[36px]">
            <Link href="/create">
              🤖 创建新Agent
            </Link>
          </Button>
        </div>

        {/* Filters */}
        {loading ? (
          <AgentFiltersSkeleton />
        ) : (
          <AgentFilters
            onFilterChange={handleFilterChange}
            selectedCount={selectedAgents.length}
            onBulkAction={handleBulkAction}
          />
        )}

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-800">{error}</p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setError(null);
                loadAgents();
              }}
              className="mt-2"
            >
              重试
            </Button>
          </div>
        )}

        {/* Agent List with Lazy Loading */}
        <LazyAgentList
          agents={filteredAgents}
          selectedAgents={selectedAgents}
          highlightedAgent={highlightedAgent}
          processingAgents={processingAgents}
          onAgentSelect={handleAgentSelect}
          onAgentAction={handleAgentAction}
          onAgentEdit={handleAgentEdit}
          loading={loading}
          itemsPerPage={12}
        />
        </div>
      </LoadingOverlay>
    </MainLayout>
  );
}

export default function ManageAgentsPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ManageAgentsPageContent />
    </Suspense>
  );
}
