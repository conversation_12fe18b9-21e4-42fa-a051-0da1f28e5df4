"use client"

import { useState } from 'react'
import { useAuth } from '@/lib/auth'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { EyeIcon, EyeOffIcon, UploadIcon, UserIcon, KeyIcon, BellIcon } from 'lucide-react'
import { TwoFactorSettings } from '@/components/features/security/two-factor-settings'
import { useSensitiveOperation } from '@/hooks/use-sensitive-operation'
import { SensitiveOperationDialog } from '@/components/auth/sensitive-operation-dialog'
import { TwoFactorVerificationDialog } from '@/components/auth/two-factor-verification-dialog'

export default function AccountPage() {
  const { user, updateProfile, changePassword, uploadAvatar } = useAuth()
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [error, setError] = useState('')

  // Profile form state
  const [profileData, setProfileData] = useState({
    name: user?.name || '',
    email: user?.email || ''
  })

  // Password form state
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  })

  const breadcrumbs = [
    { label: "设置", href: "/settings" },
    { label: "账户管理" }
  ]

  const handleProfileSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')
    setMessage('')

    try {
      await updateProfile(profileData)
      setMessage('个人信息更新成功')
    } catch (err) {
      setError(err instanceof Error ? err.message : '更新失败')
    } finally {
      setIsLoading(false)
    }
  }

  // Sensitive operation for password change
  const passwordChangeOperation = useSensitiveOperation(
    async (data) => {
      return await changePassword({
        currentPassword: data.currentPassword,
        newPassword: data.newPassword,
        confirmPassword: data.confirmPassword,
        totp_code: data.totp_code,
        backup_code: data.backup_code,
      });
    },
    {
      operationName: 'change your password',
      requiresConfirmation: true,
      confirmationMessage: 'Are you sure you want to change your password? This will require you to log in again on other devices.',
      onSuccess: () => {
        setMessage('密码修改成功');
        setPasswordData({
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        });
      },
      onError: (error) => {
        setError(error);
      },
    }
  );

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setMessage('');

    // Validate passwords match
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setError('新密码和确认密码不匹配');
      return;
    }

    await passwordChangeOperation.executeOperation(passwordData);
  };

  const handleAvatarUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    setIsLoading(true)
    setError('')
    setMessage('')

    try {
      const avatarUrl = await uploadAvatar(file)
      await updateProfile({ avatar: avatarUrl })
      setMessage('头像更新成功')
    } catch (err) {
      setError(err instanceof Error ? err.message : '头像上传失败')
    } finally {
      setIsLoading(false)
    }
  }

  if (!user) {
    return null
  }

  return (
    <MainLayout breadcrumbs={breadcrumbs}>
      <div className="max-w-4xl mx-auto space-y-4 md:space-y-6 px-3 md:px-0">
        <div>
          <h1 className="text-xl md:text-3xl font-bold">账户管理</h1>
          <p className="text-muted-foreground text-sm md:text-base mobile-text-base">
            管理您的个人信息、安全设置和偏好配置
          </p>
        </div>

        {message && (
          <Alert>
            <AlertDescription className="text-sm mobile-text-sm">{message}</AlertDescription>
          </Alert>
        )}

        {error && (
          <Alert variant="destructive">
            <AlertDescription className="text-sm mobile-text-sm">{error}</AlertDescription>
          </Alert>
        )}

        <Tabs defaultValue="profile" className="space-y-4 md:space-y-6">
          <TabsList className="grid w-full grid-cols-3 h-auto">
            <TabsTrigger value="profile" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 p-2 md:p-3 touch-target min-h-[44px] md:min-h-[36px]">
              <UserIcon className="h-4 w-4" />
              <span className="text-xs sm:text-sm mobile-text-sm">个人信息</span>
            </TabsTrigger>
            <TabsTrigger value="security" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 p-2 md:p-3 touch-target min-h-[44px] md:min-h-[36px]">
              <KeyIcon className="h-4 w-4" />
              <span className="text-xs sm:text-sm mobile-text-sm">安全设置</span>
            </TabsTrigger>
            <TabsTrigger value="notifications" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 p-2 md:p-3 touch-target min-h-[44px] md:min-h-[36px]">
              <BellIcon className="h-4 w-4" />
              <span className="text-xs sm:text-sm mobile-text-sm">通知设置</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="profile" className="space-y-4 md:space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg md:text-xl">个人信息</CardTitle>
                <CardDescription className="text-sm mobile-text-sm">
                  更新您的个人信息和头像
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 md:space-y-6 mobile-card-spacing">
                {/* Avatar Section */}
                <div className="flex flex-col sm:flex-row items-center sm:items-start gap-3 md:gap-4">
                  <Avatar className="h-16 md:h-20 w-16 md:w-20">
                    <AvatarImage src={user.avatar} alt={user.name} />
                    <AvatarFallback className="text-base md:text-lg">
                      {user.name.charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="space-y-2 text-center sm:text-left">
                    <Label htmlFor="avatar-upload" className="cursor-pointer">
                      <Button
                        variant="outline"
                        size="sm"
                        asChild
                        className="touch-target min-h-[44px] md:min-h-[32px] mobile-button-spacing"
                      >
                        <span>
                          <UploadIcon className="h-4 w-4 mr-2" />
                          更换头像
                        </span>
                      </Button>
                    </Label>
                    <Input
                      id="avatar-upload"
                      type="file"
                      accept="image/*"
                      className="hidden"
                      onChange={handleAvatarUpload}
                    />
                    <p className="text-xs text-muted-foreground mobile-text-sm">
                      支持 JPG、PNG 格式，建议尺寸 200x200px
                    </p>
                  </div>
                </div>

                <Separator />

                {/* Profile Form */}
                <form onSubmit={handleProfileSubmit} className="space-y-3 md:space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4 mobile-grid-gap">
                    <div className="space-y-2">
                      <Label htmlFor="name" className="text-sm mobile-text-sm">姓名</Label>
                      <Input
                        id="name"
                        value={profileData.name}
                        onChange={(e) => setProfileData(prev => ({ ...prev, name: e.target.value }))}
                        required
                        className="mobile-form-element"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email" className="text-sm mobile-text-sm">邮箱</Label>
                      <Input
                        id="email"
                        type="email"
                        value={profileData.email}
                        onChange={(e) => setProfileData(prev => ({ ...prev, email: e.target.value }))}
                        required
                        className="mobile-form-element"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4 mobile-grid-gap">
                    <div className="space-y-2">
                      <Label className="text-sm mobile-text-sm">用户ID</Label>
                      <Input value={user.id} disabled className="mobile-form-element" />
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm mobile-text-sm">注册时间</Label>
                      <Input
                        value={user.createdAt ? new Date(user.createdAt).toLocaleDateString() : '未知'}
                        disabled
                        className="mobile-form-element"
                      />
                    </div>
                  </div>

                  <Button
                    type="submit"
                    disabled={isLoading}
                    className="w-full sm:w-auto touch-target min-h-[44px] md:min-h-[36px] mobile-button-spacing"
                  >
                    {isLoading ? '保存中...' : '保存更改'}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="security" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>修改密码</CardTitle>
                <CardDescription>
                  定期更新密码以保护您的账户安全
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handlePasswordSubmit} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="currentPassword">当前密码</Label>
                    <div className="relative">
                      <Input
                        id="currentPassword"
                        type={showPasswords.current ? 'text' : 'password'}
                        value={passwordData.currentPassword}
                        onChange={(e) => setPasswordData(prev => ({ ...prev, currentPassword: e.target.value }))}
                        required
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowPasswords(prev => ({ ...prev, current: !prev.current }))}
                      >
                        {showPasswords.current ? <EyeOffIcon className="h-4 w-4" /> : <EyeIcon className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="newPassword">新密码</Label>
                    <div className="relative">
                      <Input
                        id="newPassword"
                        type={showPasswords.new ? 'text' : 'password'}
                        value={passwordData.newPassword}
                        onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}
                        required
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowPasswords(prev => ({ ...prev, new: !prev.new }))}
                      >
                        {showPasswords.new ? <EyeOffIcon className="h-4 w-4" /> : <EyeIcon className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">确认新密码</Label>
                    <div className="relative">
                      <Input
                        id="confirmPassword"
                        type={showPasswords.confirm ? 'text' : 'password'}
                        value={passwordData.confirmPassword}
                        onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                        required
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowPasswords(prev => ({ ...prev, confirm: !prev.confirm }))}
                      >
                        {showPasswords.confirm ? <EyeOffIcon className="h-4 w-4" /> : <EyeIcon className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>

                  <Button type="submit" disabled={passwordChangeOperation.isLoading}>
                    {passwordChangeOperation.isLoading ? '修改中...' : '修改密码'}
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* Two-Factor Authentication Settings */}
            <TwoFactorSettings />
          </TabsContent>

          <TabsContent value="notifications" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>通知设置</CardTitle>
                <CardDescription>
                  管理您希望接收的通知类型
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    通知设置功能即将推出，敬请期待。
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Sensitive Operation Dialogs */}
      <SensitiveOperationDialog
        open={passwordChangeOperation.showConfirmation}
        onOpenChange={() => {}}
        onConfirm={passwordChangeOperation.confirmOperation}
        onCancel={passwordChangeOperation.cancelOperation}
        loading={passwordChangeOperation.isLoading}
        title="Confirm Password Change"
        description={passwordChangeOperation.confirmationMessage}
        confirmText="Change Password"
        variant="default"
      />

      <TwoFactorVerificationDialog
        open={passwordChangeOperation.twoFactorVerification.state.isOpen}
        onOpenChange={passwordChangeOperation.twoFactorVerification.actions.closeVerification}
        onVerify={passwordChangeOperation.twoFactorVerification.actions.verify}
        loading={passwordChangeOperation.twoFactorVerification.state.loading}
        error={passwordChangeOperation.twoFactorVerification.state.error}
        title="Verify Password Change"
        description="Enter your verification code to confirm the password change"
      />
    </MainLayout>
  )
}
