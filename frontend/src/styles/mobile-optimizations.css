/* Mobile Optimizations for Meta-Agent Dashboard */

/* Touch-friendly interactions */
@media (max-width: 768px) {
  /* Ensure minimum touch target size */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Global touch target class for all screen sizes */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

/* Line clamping utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

@media (max-width: 768px) {
  /* Ensure all interactive elements meet touch target requirements */
  button,
  input[type="checkbox"],
  input[type="radio"],
  select,
  a[role="button"],
  [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improve text readability on small screens */
  .mobile-text-base {
    font-size: 14px;
    line-height: 1.5;
  }

  .mobile-text-sm {
    font-size: 12px;
    line-height: 1.4;
  }

  /* Optimize card spacing */
  .mobile-card-spacing {
    padding: 12px;
  }

  .mobile-card-spacing-sm {
    padding: 8px;
  }

  /* Improve button spacing */
  .mobile-button-spacing {
    padding: 8px 16px;
    min-height: 40px;
  }

  /* Optimize grid gaps */
  .mobile-grid-gap {
    gap: 8px;
  }

  .mobile-grid-gap-lg {
    gap: 12px;
  }

  /* Improve scroll behavior */
  .mobile-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Optimize dropdown menus for mobile */
  [data-slot="dropdown-menu-content"] {
    min-width: 200px;
    max-width: calc(100vw - 32px);
  }

  /* Improve dialog positioning on mobile */
  [data-slot="dialog-content"] {
    margin: 16px;
    max-height: calc(100vh - 32px);
    max-width: calc(100vw - 32px);
  }

  /* Optimize card layouts for mobile */
  [data-slot="card"] {
    margin-bottom: 16px;
  }

  /* Improve badge readability */
  [data-slot="badge"] {
    font-size: 11px;
    padding: 2px 6px;
  }

  /* Test history card optimizations */
  .test-card-compact {
    transition: all 0.2s ease-in-out;
  }

  .test-card-compact:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .test-card-mobile {
    /* Ensure proper spacing for mobile cards */
  }

  /* Ultra-compact text for very small screens */
  .mobile-text-xs {
    font-size: 10px;
    line-height: 1.3;
  }

  /* Add touch feedback for interactive elements */
  button:active,
  [role="button"]:active {
    transform: scale(0.98);
    transition: transform 0.1s ease-in-out;
  }

  /* Optimize input fields for mobile */
  input, textarea, select {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  /* Improve focus states for accessibility */
  button:focus-visible,
  [role="button"]:focus-visible {
    outline: 2px solid var(--color-ring);
    outline-offset: 2px;
  }

  /* Optimize loading states */
  .animate-pulse {
    animation: pulse 1.5s ease-in-out infinite;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }

  /* Optimize chart containers */
  .mobile-chart-container {
    height: 200px;
    margin: 0 -8px;
  }

  /* Improve form elements */
  .mobile-form-element {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 12px;
  }

  /* Optimize modal and dialog positioning */
  .mobile-modal {
    margin: 16px;
    max-height: calc(100vh - 32px);
  }

  /* Improve tab navigation */
  .mobile-tabs {
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .mobile-tabs::-webkit-scrollbar {
    display: none;
  }

  /* Ensure consistent tab widths */
  .mobile-tabs [data-slot="tabs-trigger"] {
    min-width: 0;
    flex: 1;
    text-align: center;
  }

  /* Improve tab text handling on small screens */
  .mobile-tabs [data-slot="tabs-trigger"] span {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /* Optimize badge sizing */
  .mobile-badge {
    font-size: 10px;
    padding: 2px 6px;
  }

  /* Improve progress bar visibility */
  .mobile-progress {
    height: 6px;
  }

  /* Optimize icon sizing */
  .mobile-icon-sm {
    width: 14px;
    height: 14px;
  }

  .mobile-icon-base {
    width: 16px;
    height: 16px;
  }

  /* Improve hover states for touch devices */
  .mobile-hover-none {
    @media (hover: none) {
      opacity: 1 !important;
    }
  }
}

/* Extra small devices (phones in portrait) */
@media (max-width: 480px) {
  /* Further reduce spacing */
  .xs-spacing {
    padding: 8px;
  }

  .xs-margin {
    margin: 8px;
  }

  /* Optimize typography */
  .xs-text-lg {
    font-size: 16px;
  }

  .xs-text-base {
    font-size: 14px;
  }

  .xs-text-sm {
    font-size: 12px;
  }

  .xs-text-xs {
    font-size: 10px;
  }

  /* Single column layouts */
  .xs-single-col {
    grid-template-columns: 1fr !important;
  }

  /* Optimize button groups */
  .xs-button-group {
    flex-direction: column;
    gap: 8px;
  }

  .xs-button-group > * {
    width: 100%;
  }

  /* Improve card layouts */
  .xs-card-compact {
    padding: 12px;
    margin: 8px 0;
  }

  /* Optimize navigation */
  .xs-nav-compact {
    padding: 8px 12px;
  }
}

/* Landscape orientation optimizations */
@media (max-width: 768px) and (orientation: landscape) {
  .landscape-compact {
    padding: 8px 16px;
  }

  .landscape-height {
    max-height: 80vh;
    overflow-y: auto;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .retina-border {
    border-width: 0.5px;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .motion-safe {
    animation: none !important;
    transition: none !important;
  }
}

/* Dark mode mobile optimizations */
@media (prefers-color-scheme: dark) {
  .dark-mobile-bg {
    background-color: rgb(3 7 18);
  }

  .dark-mobile-border {
    border-color: rgb(39 39 42);
  }
}

/* Utility classes for mobile optimization */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Safe area insets for devices with notches */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-left {
  padding-left: env(safe-area-inset-left);
}

.safe-area-right {
  padding-right: env(safe-area-inset-right);
}

/* Improved focus states for keyboard navigation */
.focus-visible-ring {
  @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2;
}

/* Smooth scrolling for better UX */
.smooth-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Prevent text selection on interactive elements */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Optimize tap highlights */
.tap-highlight-none {
  -webkit-tap-highlight-color: transparent;
}

/* Ultra-small screen optimizations (320px - 480px) */
@media (max-width: 480px) {
  /* Further optimize test history cards for very small screens */
  .test-card-compact .text-xs {
    font-size: 10px;
  }

  .test-card-compact .text-\[11px\] {
    font-size: 10px;
  }

  .test-card-compact .text-\[10px\] {
    font-size: 9px;
  }

  /* Ensure badges remain readable */
  .test-card-compact .badge {
    font-size: 9px;
    padding: 1px 4px;
  }

  /* Optimize button sizing for very small screens */
  .test-card-compact button {
    min-height: 36px;
    font-size: 10px;
    padding: 4px 8px;
  }

  /* Reduce spacing even further on very small screens */
  .test-card-compact .space-y-1\.5 > * + * {
    margin-top: 4px;
  }

  /* Optimize checkbox touch targets */
  .test-card-compact input[type="checkbox"] {
    width: 14px;
    height: 14px;
  }

  /* Ensure minimum touch targets are maintained */
  .test-card-compact .touch-target {
    min-height: 40px;
    min-width: 40px;
  }
}
