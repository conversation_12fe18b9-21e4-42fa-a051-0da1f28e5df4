/**
 * Performance Optimizations for Dashboard
 * GPU acceleration, smooth transitions, and optimized animations
 */

/* GPU Acceleration for smooth animations */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Optimized transitions */
.smooth-transition {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.smooth-transition-slow {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hardware-accelerated hover effects */
.hover-lift {
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}

.hover-lift:hover {
  transform: translateY(-2px) translateZ(0);
  will-change: transform;
}

/* Optimized card animations */
.card-entrance {
  animation: cardSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px) translateZ(0);
  }
  to {
    opacity: 1;
    transform: translateY(0) translateZ(0);
  }
}

/* Smooth scroll optimizations */
.smooth-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted)) transparent;
}

.smooth-scroll::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.smooth-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.smooth-scroll::-webkit-scrollbar-thumb {
  background: hsl(var(--muted));
  border-radius: 3px;
}

.smooth-scroll::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground));
}

/* Optimized loading animations */
.loading-shimmer {
  background: linear-gradient(
    90deg,
    hsl(var(--muted)) 0%,
    hsl(var(--muted-foreground) / 0.1) 50%,
    hsl(var(--muted)) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Optimized pulse animation */
.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.4;
  }
  50% {
    opacity: 0.8;
  }
}

/* Performance-optimized button states */
.button-optimized {
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateZ(0);
}

.button-optimized:hover {
  transform: translateY(-1px) translateZ(0);
}

.button-optimized:active {
  transform: translateY(0) translateZ(0);
  transition-duration: 0.05s;
}

/* Optimized focus states */
.focus-optimized:focus-visible {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
  transition: outline-offset 0.1s ease;
}

/* Container queries for responsive optimizations */
@container (max-width: 768px) {
  .mobile-optimized {
    padding: 0.75rem;
  }
  
  .mobile-optimized .text-responsive {
    font-size: 0.875rem;
    line-height: 1.5;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .respect-motion-preference {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .respect-motion-preference * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .high-contrast-optimized {
    border-width: 2px;
    font-weight: 600;
  }
  
  .high-contrast-optimized .text-muted {
    color: hsl(var(--foreground));
  }
}

/* Dark mode optimizations */
@media (prefers-color-scheme: dark) {
  .dark-mode-optimized {
    color-scheme: dark;
  }
  
  .dark-mode-optimized .shadow-optimized {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 
                0 2px 4px -1px rgba(0, 0, 0, 0.2);
  }
}

/* Performance monitoring */
.performance-monitor {
  contain: layout style paint;
}

/* Optimized grid layouts */
.grid-optimized {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  contain: layout;
}

@media (max-width: 768px) {
  .grid-optimized {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
}

/* Optimized flexbox layouts */
.flex-optimized {
  display: flex;
  gap: 1rem;
  contain: layout;
}

@media (max-width: 768px) {
  .flex-optimized {
    flex-direction: column;
    gap: 0.75rem;
  }
}

/* Memory-efficient animations */
.memory-efficient {
  animation-fill-mode: both;
  animation-play-state: running;
}

.memory-efficient.paused {
  animation-play-state: paused;
}

/* Optimized backdrop blur */
.backdrop-blur-optimized {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  background-color: hsl(var(--background) / 0.8);
}

@supports not (backdrop-filter: blur(8px)) {
  .backdrop-blur-optimized {
    background-color: hsl(var(--background) / 0.95);
  }
}

/* Touch optimization */
@media (hover: none) and (pointer: coarse) {
  .touch-optimized {
    min-height: 44px;
    min-width: 44px;
    padding: 0.75rem;
  }
  
  .touch-optimized:hover {
    transform: none;
  }
  
  .touch-optimized:active {
    transform: scale(0.98);
    transition: transform 0.1s ease-out;
  }
}

/* Intersection observer optimizations */
.lazy-load {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.3s ease-out, transform 0.3s ease-out;
}

.lazy-load.loaded {
  opacity: 1;
  transform: translateY(0);
}

/* Critical CSS optimizations */
.above-fold {
  content-visibility: visible;
}

.below-fold {
  content-visibility: auto;
  contain-intrinsic-size: 200px;
}

/* Font loading optimizations */
.font-optimized {
  font-display: swap;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
