/**
 * Mobile-Specific Style Enhancements
 * Optimized touch feedback, visual hierarchy, and readability for mobile devices
 */

/* Touch Feedback Animations */
@media (max-width: 768px) {
  /* Enhanced touch feedback for buttons */
  .touch-feedback {
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .touch-feedback:active {
    transform: scale(0.96);
    filter: brightness(0.95);
  }
  
  /* Improved button press animations */
  button:active,
  [role="button"]:active {
    transform: scale(0.98);
    transition: transform 0.1s ease-out;
  }
  
  /* Card touch feedback */
  .card-interactive:active {
    transform: scale(0.99);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  /* Enhanced ripple effect for touch */
  .ripple-effect {
    position: relative;
    overflow: hidden;
  }
  
  .ripple-effect::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
  }
  
  .ripple-effect:active::before {
    width: 300px;
    height: 300px;
  }
}

/* Mobile Visual Hierarchy */
@media (max-width: 768px) {
  /* Improved text contrast for small screens */
  .mobile-text-primary {
    color: hsl(var(--foreground));
    font-weight: 600;
  }
  
  .mobile-text-secondary {
    color: hsl(var(--muted-foreground));
    font-weight: 500;
  }
  
  .mobile-text-caption {
    color: hsl(var(--muted-foreground));
    font-size: 0.75rem;
    line-height: 1.4;
  }
  
  /* Enhanced section separation */
  .mobile-section-divider {
    height: 1px;
    background: linear-gradient(
      90deg,
      transparent,
      hsl(var(--border)) 20%,
      hsl(var(--border)) 80%,
      transparent
    );
    margin: 1rem 0;
  }
  
  /* Better card spacing on mobile */
  .mobile-card-spacing {
    margin-bottom: 0.75rem;
    padding: 0.75rem;
  }
  
  /* Improved focus indicators for accessibility */
  .mobile-focus:focus-visible {
    outline: 2px solid hsl(var(--primary));
    outline-offset: 2px;
    border-radius: 0.375rem;
  }
}

/* Mobile Typography Enhancements */
@media (max-width: 768px) {
  /* Optimized font sizes for mobile readability */
  .mobile-heading-1 {
    font-size: 1.5rem;
    line-height: 1.3;
    font-weight: 700;
    letter-spacing: -0.025em;
  }
  
  .mobile-heading-2 {
    font-size: 1.25rem;
    line-height: 1.4;
    font-weight: 600;
    letter-spacing: -0.015em;
  }
  
  .mobile-heading-3 {
    font-size: 1.125rem;
    line-height: 1.4;
    font-weight: 600;
  }
  
  .mobile-body {
    font-size: 0.875rem;
    line-height: 1.5;
    font-weight: 400;
  }
  
  .mobile-caption {
    font-size: 0.75rem;
    line-height: 1.4;
    font-weight: 400;
  }
  
  /* Better text spacing */
  .mobile-text-spacing {
    margin-bottom: 0.5rem;
  }
  
  .mobile-text-spacing:last-child {
    margin-bottom: 0;
  }
}

/* Mobile Animation Optimizations */
@media (max-width: 768px) {
  /* Reduced motion for better performance */
  @media (prefers-reduced-motion: reduce) {
    .mobile-animation {
      animation: none !important;
      transition: none !important;
    }
  }
  
  /* Optimized hover states for touch devices */
  @media (hover: none) {
    .hover-only {
      display: none;
    }
    
    .touch-only {
      display: block;
    }
  }
  
  /* Smooth scroll behavior */
  .mobile-scroll {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }
  
  /* Better loading animations */
  .mobile-loading {
    animation: mobile-pulse 1.5s ease-in-out infinite;
  }
  
  @keyframes mobile-pulse {
    0%, 100% {
      opacity: 0.6;
    }
    50% {
      opacity: 1;
    }
  }
}

/* Mobile Layout Improvements */
@media (max-width: 768px) {
  /* Safe area handling for notched devices */
  .mobile-safe-area {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
  
  /* Improved sticky positioning */
  .mobile-sticky {
    position: sticky;
    top: env(safe-area-inset-top, 0);
    z-index: 10;
    background: hsl(var(--background) / 0.95);
    backdrop-filter: blur(8px);
  }
  
  /* Better modal/dialog positioning */
  .mobile-modal {
    position: fixed;
    inset: 0;
    padding: 1rem;
    display: flex;
    align-items: flex-end;
  }
  
  .mobile-modal-content {
    width: 100%;
    max-height: 90vh;
    border-radius: 1rem 1rem 0 0;
    background: hsl(var(--background));
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
  }
}

/* Mobile Accessibility Enhancements */
@media (max-width: 768px) {
  /* Improved contrast ratios */
  .mobile-high-contrast {
    color: hsl(var(--foreground));
    background: hsl(var(--background));
  }
  
  /* Better focus management */
  .mobile-focus-trap {
    outline: none;
  }
  
  .mobile-focus-trap:focus-within {
    outline: 2px solid hsl(var(--primary));
    outline-offset: 2px;
  }
  
  /* Screen reader optimizations */
  .mobile-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }
}

/* Mobile Performance Optimizations */
@media (max-width: 768px) {
  /* GPU acceleration for smooth animations */
  .mobile-gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
  }
  
  /* Optimized shadows for mobile */
  .mobile-shadow-sm {
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }
  
  .mobile-shadow-md {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .mobile-shadow-lg {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
  
  /* Reduced backdrop blur for performance */
  .mobile-backdrop-blur {
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
  }
}
