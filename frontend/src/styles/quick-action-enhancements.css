/**
 * Quick Action Card Enhancements
 * Advanced animations and visual effects for quick action buttons
 */

/* Enhanced Quick Action Card Animations */
.quick-action-card {
  position: relative;
  transform: translateZ(0);
  will-change: transform, box-shadow;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Smooth hover transitions with GPU acceleration */
.quick-action-card:hover {
  transform: translateY(-2px) translateZ(0) scale(1.02);
  box-shadow: 
    0 8px 25px rgba(0, 0, 0, 0.1),
    0 4px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Active state with tactile feedback */
.quick-action-card:active {
  transform: translateY(0) translateZ(0) scale(0.98);
  transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Icon animation enhancements */
.quick-action-icon {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.quick-action-card:hover .quick-action-icon {
  transform: scale(1.1) rotate(3deg) translateZ(0);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.quick-action-card:active .quick-action-icon {
  transform: scale(0.95) rotate(0deg) translateZ(0);
}

/* Text animation enhancements */
.quick-action-text {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.quick-action-card:hover .quick-action-text {
  font-weight: 600;
  letter-spacing: 0.025em;
}

/* Badge animation */
.quick-action-badge {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.quick-action-card:hover .quick-action-badge {
  transform: scale(1.1) translateZ(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Ripple effect for touch feedback */
.quick-action-ripple {
  position: relative;
  overflow: hidden;
}

.quick-action-ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.4s ease-out, height 0.4s ease-out;
  pointer-events: none;
  z-index: 1;
}

.quick-action-ripple:active::before {
  width: 200px;
  height: 200px;
}

/* Gradient overlay effects */
.quick-action-gradient {
  position: relative;
  overflow: hidden;
}

.quick-action-gradient::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.5s ease-out;
  pointer-events: none;
}

.quick-action-gradient:hover::after {
  left: 100%;
}

/* Status indicator animations */
.quick-action-status {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.quick-action-status.disabled {
  background: rgba(156, 163, 175, 0.5);
  animation: pulse-disabled 2s infinite;
}

.quick-action-status.available {
  background: rgba(34, 197, 94, 0.8);
  box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2);
  animation: pulse-available 2s infinite;
}

@keyframes pulse-disabled {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 0.8; }
}

@keyframes pulse-available {
  0%, 100% { 
    opacity: 1;
    box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2);
  }
  50% { 
    opacity: 0.8;
    box-shadow: 0 0 0 4px rgba(34, 197, 94, 0.1);
  }
}

/* Focus enhancements for accessibility */
.quick-action-card:focus-visible {
  outline: none;
  box-shadow: 
    0 0 0 2px rgba(59, 130, 246, 0.5),
    0 8px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px) translateZ(0);
}

/* Dark mode optimizations */
@media (prefers-color-scheme: dark) {
  .quick-action-card:hover {
    box-shadow: 
      0 8px 25px rgba(0, 0, 0, 0.3),
      0 4px 10px rgba(0, 0, 0, 0.2);
  }
  
  .quick-action-ripple::before {
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .quick-action-card {
    border-width: 2px;
  }
  
  .quick-action-card:hover {
    border-width: 3px;
  }
  
  .quick-action-card:focus-visible {
    outline: 3px solid;
    outline-offset: 2px;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .quick-action-card,
  .quick-action-icon,
  .quick-action-text,
  .quick-action-badge,
  .quick-action-status {
    transition: none !important;
    animation: none !important;
  }
  
  .quick-action-card:hover {
    transform: none;
  }
  
  .quick-action-ripple::before {
    display: none;
  }
  
  .quick-action-gradient::after {
    display: none;
  }
}

/* Mobile touch optimizations */
@media (hover: none) and (pointer: coarse) {
  .quick-action-card {
    min-height: 64px;
    padding: 16px;
  }
  
  .quick-action-card:hover {
    transform: none;
    box-shadow: 
      0 4px 12px rgba(0, 0, 0, 0.1),
      0 2px 4px rgba(0, 0, 0, 0.05);
  }
  
  .quick-action-card:active {
    transform: scale(0.98) translateZ(0);
    transition: transform 0.1s ease-out;
  }
  
  .quick-action-icon {
    width: 24px;
    height: 24px;
  }
}

/* Grid layout optimizations */
.quick-action-grid {
  display: grid;
  gap: 16px;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  align-items: stretch;
}

@media (max-width: 640px) {
  .quick-action-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .quick-action-grid {
    grid-template-columns: 1fr 1fr;
    gap: 8px;
  }
}

/* Performance optimizations */
.quick-action-performance {
  contain: layout style paint;
  content-visibility: auto;
  contain-intrinsic-size: 120px;
}

/* Loading state animations */
.quick-action-loading {
  opacity: 0.6;
  pointer-events: none;
  animation: loading-pulse 1.5s ease-in-out infinite;
}

@keyframes loading-pulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 0.8; }
}
