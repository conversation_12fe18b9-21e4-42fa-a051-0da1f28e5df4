/**
 * TypeScript types for agent favorites functionality
 */

export interface UserAgentFavorite {
  id: number;
  uuid: string;
  user_id: number;
  agent_id: string;
  created_at: string;
  updated_at?: string;
}

export interface FavoriteAgentPerformance {
  execution_count: number;
  success_count: number;
  error_count: number;
  success_rate: number;
  avg_response_time: number;
  total_cost: number;
  last_execution_time?: string;
}

export interface FavoriteAgentResponse {
  // Agent information
  agent_id: string;
  name: string;
  description: string;
  status: string;
  agent_type: string;
  created_at: string;
  updated_at?: string;
  last_used?: string;
  usage_count: number;
  
  // Favorite information
  favorite_id: number;
  favorite_uuid: string;
  favorited_at: string;
  
  // Performance metrics (optional)
  performance?: FavoriteAgentPerformance;
}

export interface ToggleFavoriteRequest {
  // No additional fields needed, agent_id comes from URL
}

export interface ToggleFavoriteResponse {
  success: boolean;
  is_favorite: boolean;
  message: string;
  agent_id: string;
  favorite_id?: number;
}

export interface FavoritesApiResponse {
  success: boolean;
  data: FavoriteAgentResponse[];
  count: number;
}

// Extended agent interface with favorite status
export interface AgentWithFavorite {
  agent_id: string;
  name: string;
  description: string;
  status: 'active' | 'inactive' | 'error' | 'creating' | 'deleted';
  agent_type: 'single' | 'team' | 'workflow';
  created_at: string;
  updated_at?: string;
  last_used?: string;
  usage_count: number;
  user_id: number;
  
  // Favorite status
  is_favorite: boolean;
  favorite_id?: number;
  favorited_at?: string;
  
  // Performance metrics
  performance?: {
    execution_count: number;
    success_count: number;
    error_count: number;
    success_rate: number;
    avg_response_time: number;
    total_cost?: number;
    last_execution_time?: string;
    trend: 'up' | 'down' | 'stable';
    score: number;
  };
}

// Hook return types
export interface UseFavoritesReturn {
  favorites: FavoriteAgentResponse[];
  isLoading: boolean;
  error: Error | null;
  toggleFavorite: (agentId: string) => Promise<ToggleFavoriteResponse>;
  refetch: () => Promise<void>;
  isFavorite: (agentId: string) => boolean;
  getFavoriteId: (agentId: string) => number | undefined;
}

export interface ToggleFavoriteMutationReturn {
  toggleFavorite: (agentId: string) => Promise<ToggleFavoriteResponse>;
  isLoading: boolean;
  error: Error | null;
}

// API client types
export interface FavoritesApiClient {
  toggleFavorite: (agentId: string) => Promise<ToggleFavoriteResponse>;
  getFavorites: (includePerformance?: boolean) => Promise<FavoriteAgentResponse[]>;
}

// Component prop types
export interface FavoriteButtonProps {
  agentId: string;
  isFavorite: boolean;
  onToggle?: (agentId: string, isFavorite: boolean) => void;
  disabled?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'ghost' | 'outline';
  className?: string;
}

export interface FavoriteAgentCardProps {
  agent: FavoriteAgentResponse;
  onRun?: (agentId: string) => void;
  onEdit?: (agentId: string) => void;
  onToggleFavorite?: (agentId: string) => void;
  showPerformance?: boolean;
  className?: string;
}

// Filter and sort types
export type AgentSortBy = 'name' | 'created_at' | 'last_used' | 'usage_count' | 'success_rate' | 'favorited_at';
export type SortOrder = 'asc' | 'desc';

export interface AgentFilters {
  status?: string[];
  agent_type?: string[];
  favorites_only?: boolean;
  search?: string;
  sort_by?: AgentSortBy;
  sort_order?: SortOrder;
}

// Error types
export class FavoritesError extends Error {
  code?: string;
  status?: number;
  details?: Record<string, any>;

  constructor(message: string, options?: { code?: string; status?: number; details?: Record<string, any> }) {
    super(message);
    this.name = 'FavoritesError';
    this.code = options?.code;
    this.status = options?.status;
    this.details = options?.details;
  }
}

export class FavoriteNotFoundError extends Error {
  constructor(agentId: string) {
    super(`Favorite not found for agent: ${agentId}`);
    this.name = 'FavoriteNotFoundError';
  }
}

export class FavoriteToggleError extends Error {
  constructor(agentId: string, originalError?: Error) {
    super(`Failed to toggle favorite for agent: ${agentId}`);
    this.name = 'FavoriteToggleError';
    this.cause = originalError;
  }
}
