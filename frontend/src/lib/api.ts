import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from "axios";
import {
  ApiResponse,
  Agent,
  AgentUpdate,
  Task,
  TestRecord,
  CreateAgentRequest,
  CreateAgentResponse,
  AgentInvokeRequest,
  AgentInvokeResponse,
  PaginatedResponse,
  AgentFilters,
  PaginationParams,
  PaginatedTemplateResponse,
  LogsResponse,
  APIKey,
  APIKeyCreate,
  APIKeyUpdate,
  APIKeyTest,
  Template,
  TemplateListItem,
  TemplateFilters,
  TemplateCreateRequest,
  TemplateUpdateRequest,
  TemplateFromAgentRequest,
  TemplateCategory,
  TemplateDifficulty,
  TemplateVisibility,
  TemplateStatus,
  TemplateStats,
  PopularTag
} from "./types";
import { mockAPI } from "./mock-api";
import { handleApiError, handleNetworkError, handleTimeoutError, handleAuthError, handleServerError } from "./error-handler";

// API配置
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";
const USE_MOCK_API = process.env.NEXT_PUBLIC_MOCK_API === "true";

// 创建axios实例
const createAxiosInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: API_BASE_URL,
    timeout: 30000, // Default timeout for most requests
    headers: {
      "Content-Type": "application/json",
    },
  });

  // 请求拦截器
  instance.interceptors.request.use(
    (config) => {
      // 添加认证token（如果有）
      const token = localStorage.getItem("auth_token");
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }

      // 添加请求ID用于追踪
      config.headers["X-Request-ID"] = generateRequestId();

      // Log API requests only in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, config.data);
      }
      return config;
    },
    (error) => {
      // Log API errors only in development
      if (process.env.NODE_ENV === 'development') {
        console.error("[API Request Error]", error);
      }
      return Promise.reject(error);
    }
  );

  // 响应拦截器
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      // Log API responses only in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`[API Response] ${response.status} ${response.config.url}`, response.data);
      }
      return response;
    },
    (error) => {
      // Log API errors only in development
      if (process.env.NODE_ENV === 'development') {
        console.error("[API Response Error]", error);
      }

      // 使用标准化错误处理
      if (error.response) {
        const { status } = error.response;
        switch (status) {
          case 401:
            handleAuthError(error, {
              onError: () => {
                localStorage.removeItem("auth_token");
                window.location.href = "/login";
              }
            });
            break;
          case 403:
            handleApiError(error, { fallbackMessage: "权限不足" });
            break;
          case 404:
            handleApiError(error, { fallbackMessage: "资源不存在" });
            break;
          case 429:
            handleApiError(error, { fallbackMessage: "请求过于频繁，请稍后再试" });
            break;
          case 500:
          case 502:
          case 503:
          case 504:
            handleServerError(error);
            break;
          default:
            handleApiError(error);
        }
      } else if (error.request) {
        // 网络错误
        handleNetworkError(error);
      } else if (error.code === 'ECONNABORTED') {
        // 超时错误
        handleTimeoutError();
      } else {
        // 其他错误
        handleApiError(error);
      }

      return Promise.reject(error);
    }
  );

  return instance;
};

// 生成请求ID
const generateRequestId = (): string => {
  return `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

// API客户端类
class APIClient {
  private axios: AxiosInstance;

  constructor() {
    this.axios = createAxiosInstance();
  }

  // 通用请求方法
  private async request<T>(config: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.axios.request<T>(config);
      // 后端直接返回数据，我们包装成ApiResponse格式
      return {
        success: true,
        data: response.data,
        timestamp: new Date().toISOString()
      };
    } catch (error: any) {
      // 使用标准化错误处理
      const handledError = handleApiError(error, {
        logError: true,
        showToast: false // 让调用方决定是否显示toast
      });

      const apiError: ApiResponse<T> = {
        success: false,
        error: {
          code: handledError.code || "UNKNOWN_ERROR",
          message: handledError.message,
          details: handledError.details
        },
        timestamp: new Date().toISOString()
      };
      return apiError;
    }
  }

  // Agent相关API
  async getAgents(filters?: AgentFilters, pagination?: PaginationParams): Promise<ApiResponse<PaginatedResponse<Agent>>> {
    if (USE_MOCK_API) {
      return mockAPI.getAgents(filters, pagination);
    }

    const params = new URLSearchParams();

    if (filters) {
      if (filters.status && filters.status !== "all") {
        params.append("status", filters.status);
      }
      if (filters.search) {
        params.append("search", filters.search);
      }
      if (filters.sortBy) {
        params.append("sort_by", filters.sortBy);
      }
      if (filters.sortOrder) {
        params.append("sort_order", filters.sortOrder);
      }
    }

    if (pagination) {
      params.append("page", pagination.page.toString());
      params.append("size", pagination.size.toString());
    }

    try {
      const response = await this.request<Agent[]>({
        method: "GET",
        url: `/api/v1/agents?${params.toString()}`
      });

      if (response.success && response.data) {
        // 将Agent数组转换为分页响应格式
        const agents = response.data;
        const page = pagination?.page || 1;
        const size = pagination?.size || 10;
        const startIndex = (page - 1) * size;
        const endIndex = startIndex + size;
        const paginatedAgents = agents.slice(startIndex, endIndex);

        const paginatedResponse: PaginatedResponse<Agent> = {
          data: paginatedAgents,
          total: agents.length,
          page: page,
          size: size,
          total_pages: Math.ceil(agents.length / size)
        };

        return {
          success: true,
          data: paginatedResponse,
          timestamp: response.timestamp
        };
      }

      return response as unknown as ApiResponse<PaginatedResponse<Agent>>;
    } catch (error) {
      return {
        success: false,
        error: {
          code: "FETCH_ERROR",
          message: "Failed to fetch agents"
        },
        timestamp: new Date().toISOString()
      };
    }
  }

  async getAgent(agentId: string): Promise<ApiResponse<Agent>> {
    if (USE_MOCK_API) {
      return mockAPI.getAgent(agentId);
    }

    return this.request<Agent>({
      method: "GET",
      url: `/api/v1/agents/${agentId}`
    });
  }

  async discoverAgentVariables(agentId: string): Promise<ApiResponse<{
    agent_id: string;
    team_name: string;
    variables: Array<{
      name: string;
      placeholder: string;
      variable_type: string;
      source_agent: string | null;
      destination_agents: string[];
      semantic_description: string;
      workflow_step: number | null;
      dependencies: string[];
      is_required: boolean;
      example_value: string | null;
      status: string;
    }>;
    summary: {
      total_variables: number;
      user_input_variables: number;
      inter_agent_variables: number;
      system_variables: number;
      output_variables: number;
      context_variables: number;
    };
    discovery_timestamp: string;
    message: string;
  }>> {
    if (USE_MOCK_API) {
      // Mock response for development
      return {
        success: true,
        data: {
          agent_id: agentId,
          team_name: "Mock Team",
          variables: [
            {
              name: "user.requirements",
              placeholder: "{user.requirements}",
              variable_type: "user-input",
              source_agent: null,
              destination_agents: ["planner"],
              semantic_description: "用户输入的需求和任务描述",
              workflow_step: 0,
              dependencies: [],
              is_required: true,
              example_value: "用户的具体需求和任务描述...",
              status: "pending"
            },
            {
              name: "planner.task_breakdown",
              placeholder: "{planner.task_breakdown}",
              variable_type: "inter-agent",
              source_agent: "planner",
              destination_agents: ["analyst", "executor"],
              semantic_description: "规划师的任务分解结果",
              workflow_step: 1,
              dependencies: ["user.requirements"],
              is_required: true,
              example_value: "任务分解和执行计划...",
              status: "pending"
            }
          ],
          summary: {
            total_variables: 2,
            user_input_variables: 1,
            inter_agent_variables: 1,
            system_variables: 0,
            output_variables: 0,
            context_variables: 0
          },
          discovery_timestamp: new Date().toISOString(),
          message: "Successfully discovered 2 variables"
        },
        timestamp: new Date().toISOString()
      };
    }

    return this.request<any>({
      method: "GET",
      url: `/api/v1/agents/${agentId}/team/variables`
    });
  }

  async createAgent(request: CreateAgentRequest): Promise<ApiResponse<CreateAgentResponse>> {
    if (USE_MOCK_API) {
      return mockAPI.createAgent(request);
    }

    return this.request<CreateAgentResponse>({
      method: "POST",
      url: "/api/v1/agents/create",
      data: request
    });
  }

  async createAgentFromTemplate(templateId: string, customizations?: any): Promise<ApiResponse<CreateAgentResponse>> {
    if (USE_MOCK_API) {
      return {
        success: true,
        data: {
          agent_id: `agent_${Date.now()}`,
          status: "active",
          message: "Agent created successfully from template",
          api_endpoint: `http://localhost:8000/api/v1/agents/agent_${Date.now()}/execute`,
        },
        timestamp: new Date().toISOString()
      };
    }

    return this.request<CreateAgentResponse>({
      method: "POST",
      url: "/api/v1/agents/from-template",
      data: {
        template_id: templateId,
        customizations: customizations || {}
      }
    });
  }

  async deleteAgent(agentId: string): Promise<ApiResponse<void>> {
    if (USE_MOCK_API) {
      return mockAPI.deleteAgent(agentId);
    }

    return this.request<void>({
      method: "DELETE",
      url: `/api/v1/agents/${agentId}`
    });
  }

  async updateAgentStatus(agentId: string, status: Agent["status"]): Promise<ApiResponse<Agent>> {
    if (USE_MOCK_API) {
      return mockAPI.updateAgentStatus(agentId, status);
    }

    return this.request<Agent>({
      method: "PATCH",
      url: `/api/v1/agents/${agentId}`,
      data: { status }
    });
  }

  async updateAgent(agentId: string, updates: AgentUpdate): Promise<ApiResponse<Agent>> {
    if (USE_MOCK_API) {
      return mockAPI.updateAgent(agentId, updates);
    }

    return this.request<Agent>({
      method: "PATCH",
      url: `/api/v1/agents/${agentId}`,
      data: updates
    });
  }

  // Task相关API
  async getTaskStatus(taskId: string): Promise<ApiResponse<Task>> {
    if (USE_MOCK_API) {
      return mockAPI.getTaskStatus(taskId);
    }

    return this.request<Task>({
      method: "GET",
      url: `/api/v1/agents/${taskId}/history`
    });
  }

  // Agent调用API
  async invokeAgent(agentId: string, request: AgentInvokeRequest): Promise<ApiResponse<AgentInvokeResponse>> {
    if (USE_MOCK_API) {
      return mockAPI.invokeAgent(agentId, request);
    }

    return this.request<AgentInvokeResponse>({
      method: "POST",
      url: `/api/v1/agents/${agentId}/execute`,
      data: request,
      timeout: 300000 // 5 minutes timeout for agent execution
    });
  }

  // 流式调用Agent（Server-Sent Events）
  async invokeAgentStream(
    agentId: string,
    request: AgentInvokeRequest,
    onMessage: (data: string) => void,
    onError: (error: Error) => void,
    onComplete: () => void
  ): Promise<void> {
    if (USE_MOCK_API) {
      // Mock流式响应
      const response = await mockAPI.invokeAgent(agentId, request);
      if (response.success && response.data && response.data.result) {
        const words = response.data.result.final_result.toString().split(" ");
        let index = 0;
        const interval = setInterval(() => {
          if (index < words.length) {
            onMessage(words[index]);
            index++;
          } else {
            clearInterval(interval);
            onComplete();
          }
        }, 100);
      } else {
        onError(new Error(response.error?.message || "Stream failed"));
      }
      return;
    }

    // 实际的SSE实现
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 300000); // 5 minutes timeout

    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/agents/${agentId}/execute/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`
        },
        body: JSON.stringify(request),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('No response body reader available');
    }

    const decoder = new TextDecoder();

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              if (data.type === 'agent_response') {
                onMessage(data.content);
              } else if (data.type === 'end') {
                onComplete();
                return;
              } else if (data.type === 'error') {
                onError(new Error(data.content));
                return;
              }
            } catch (parseError) {
              // Log parsing errors only in development
              if (process.env.NODE_ENV === 'development') {
                console.warn('Failed to parse SSE data:', line);
              }
            }
          }
        }
      }
    } catch (error) {
      onError(error instanceof Error ? error : new Error('Stream reading failed'));
    } finally {
      reader.releaseLock();
    }
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error && error.name === 'AbortError') {
        onError(new Error('请求超时，请稍后重试'));
      } else {
        onError(error instanceof Error ? error : new Error('Network error'));
      }
    }
  }

  // 流式调用Agent with progress callbacks
  async invokeAgentStreamWithProgress(
    agentId: string,
    request: AgentInvokeRequest,
    callbacks: {
      onProgress?: (progressData: any) => void;
      onComplete?: (result: any) => void;
      onError?: (error: string) => void;
    }
  ): Promise<void> {
    if (USE_MOCK_API) {
      // Mock progress updates
      const { onProgress, onComplete, onError } = callbacks;

      try {
        // Simulate initialization
        onProgress?.({
          stage: "initializing",
          message: "正在初始化代理...",
          progress: 0,
          total_steps: 3
        });

        await new Promise(resolve => setTimeout(resolve, 500));

        // Simulate planning
        onProgress?.({
          stage: "planning",
          message: "工作流规划完成，共 3 个步骤",
          progress: 10,
          total_steps: 3
        });

        await new Promise(resolve => setTimeout(resolve, 500));

        // Simulate execution steps
        const steps = [
          { name: "故事构思", assignee: "故事构思师" },
          { name: "角色设计", assignee: "角色设计师" },
          { name: "情节编辑", assignee: "情节编辑" }
        ];

        for (let i = 0; i < steps.length; i++) {
          const step = steps[i];

          // Step start
          onProgress?.({
            stage: "executing",
            message: `正在执行: ${step.name} (负责人: ${step.assignee})`,
            progress: 10 + (i / steps.length) * 80,
            current_step: i + 1,
            total_steps: steps.length,
            step_name: step.name,
            assignee: step.assignee
          });

          await new Promise(resolve => setTimeout(resolve, 2000));

          // Step completion
          onProgress?.({
            stage: "step_completed",
            message: `完成: ${step.name}`,
            progress: 10 + ((i + 1) / steps.length) * 80,
            current_step: i + 1,
            total_steps: steps.length,
            step_result: `${step.assignee}完成了${step.name}的相关工作`
          });

          await new Promise(resolve => setTimeout(resolve, 500));
        }

        // Final completion
        onProgress?.({
          stage: "completed",
          message: "创意写作团队 执行完成！",
          progress: 100,
          total_steps: steps.length,
          final_output: "创作完成！团队协作产出了一个精彩的故事。"
        });

        await new Promise(resolve => setTimeout(resolve, 500));

        // Complete with result
        onComplete?.({
          content: "创作完成！团队协作产出了一个精彩的故事。",
          full_result: {
            status: "success",
            final_output: "创作完成！团队协作产出了一个精彩的故事。",
            results: steps.map((step, i) => ({
              step: step.name,
              assignee: step.assignee,
              output: `${step.assignee}完成了${step.name}的相关工作`
            }))
          }
        });

      } catch (error) {
        onError?.(error instanceof Error ? error.message : "Mock execution failed");
      }
      return;
    }

    // 实际的SSE实现
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 300000); // 5 minutes timeout

    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/agents/${agentId}/execute/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`
        },
        body: JSON.stringify(request),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader available');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6));

                if (data.type === 'progress') {
                  callbacks.onProgress?.(data);
                } else if (data.type === 'streaming') {
                  // Handle real-time streaming content
                  callbacks.onProgress?.({
                    stage: 'streaming',
                    streaming_content: data.content,
                    partial_output: data.partial_output,
                    step_name: data.step_name,
                    assignee: data.assignee
                  });
                } else if (data.type === 'final_result') {
                  callbacks.onComplete?.(data);
                } else if (data.type === 'error') {
                  callbacks.onError?.(data.content);
                  return;
                } else if (data.type === 'end') {
                  return;
                }
              } catch (e) {
                console.warn('Failed to parse SSE data:', line);
              }
            }
          }
        }
      } catch (error) {
        callbacks.onError?.(error instanceof Error ? error.message : 'Stream reading failed');
      } finally {
        reader.releaseLock();
      }
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error && error.name === 'AbortError') {
        callbacks.onError?.('请求超时，请稍后重试');
      } else {
        callbacks.onError?.(error instanceof Error ? error.message : 'Network error');
      }
    }
  }

  // 测试记录API
  async getTestHistory(agentId?: string): Promise<ApiResponse<TestRecord[]>> {
    if (USE_MOCK_API) {
      return mockAPI.getTestHistory(agentId);
    }

    // Note: This endpoint doesn't exist in the backend yet
    // For now, return empty array to prevent errors
    return {
      success: true,
      data: [],
      timestamp: new Date().toISOString()
    };
  }

  // 统计API
  async getStats(): Promise<ApiResponse<any>> {
    if (USE_MOCK_API) {
      return mockAPI.getStats();
    }

    // Use the actual system metrics endpoint
    return this.request<any>({
      method: "GET",
      url: "/api/v1/system/metrics"
    });
  }





  async restartAgent(agentId: string): Promise<ApiResponse<{ agent_id: string; status: string; restarted_at: string }>> {
    return this.request({
      method: "POST",
      url: `/api/v1/system/agents/${agentId}/restart`
    });
  }

  // 日志管理API
  async getLogs(params?: {
    level?: string;
    source?: string;
    lines?: number;
  }): Promise<ApiResponse<LogsResponse>> {
    const searchParams = new URLSearchParams();
    if (params?.level) searchParams.append("level", params.level);
    if (params?.source) searchParams.append("source", params.source);
    if (params?.lines) searchParams.append("lines", params.lines.toString());

    return this.request<LogsResponse>({
      method: "GET",
      url: `/api/v1/logs?${searchParams.toString()}`
    });
  }

  async clearLogs(source?: string): Promise<ApiResponse<{ cleared_sources: string[]; cleared_at: string }>> {
    const searchParams = new URLSearchParams();
    if (source) searchParams.append("source", source);

    return this.request({
      method: "DELETE",
      url: `/api/v1/logs?${searchParams.toString()}`
    });
  }

  async exportLogs(source: string, format: "json" | "txt" = "json"): Promise<ApiResponse<Blob>> {
    const searchParams = new URLSearchParams();
    searchParams.append("source", source);
    searchParams.append("format", format);

    return this.request<Blob>({
      method: "GET",
      url: `/api/v1/logs/export?${searchParams.toString()}`,
      responseType: "blob"
    });
  }

  // API密钥管理API
  async getApiKeys(): Promise<ApiResponse<{ api_keys: APIKey[]; total: number }>> {
    return this.request({
      method: "GET",
      url: "/api/v1/api-keys"
    });
  }

  async createApiKey(keyData: APIKeyCreate): Promise<ApiResponse<{ id: string; name: string; api_key: string; created_at: string; expires_at?: string }>> {
    return this.request({
      method: "POST",
      url: "/api/v1/api-keys",
      data: keyData
    });
  }

  async updateApiKey(keyId: string, keyUpdate: APIKeyUpdate): Promise<ApiResponse<{ id: string; name: string; description?: string; is_active: boolean; updated_at: string }>> {
    return this.request({
      method: "PUT",
      url: `/api/v1/api-keys/${keyId}`,
      data: keyUpdate
    });
  }

  async deleteApiKey(keyId: string): Promise<ApiResponse<{ id: string; deleted_at: string }>> {
    return this.request({
      method: "DELETE",
      url: `/api/v1/api-keys/${keyId}`
    });
  }

  async testApiKey(keyId: string, testData: APIKeyTest): Promise<ApiResponse<{ key_id: string; status: string; test_endpoint: string; tested_at: string }>> {
    return this.request({
      method: "POST",
      url: `/api/v1/api-keys/${keyId}/test`,
      data: testData
    });
  }

  // Template Management API
  async getTemplates(filters?: TemplateFilters & PaginationParams): Promise<ApiResponse<TemplateListItem[]>> {
    if (USE_MOCK_API) {
      return {
        success: true,
        data: [
          {
            id: 1,
            template_id: "detective_team",
            name: "侦探二人组",
            description: "由禅意僧侣和街头老兵组成的侦探团队",
            category: "investigation",
            difficulty: "intermediate",
            visibility: "public",
            status: "active",
            tags: ["推理", "对话", "案件分析"],
            usage_count: 156,
            rating: 4.8,
            rating_count: 25,
            version: "1.0.0",
            author_name: "系统模板",
            use_case: "适用于需要深度分析和实地调查的复杂问题解决",
            created_at: "2025-06-01T00:00:00Z",
            is_owner: false,
            can_edit: false,
          }
        ],
        timestamp: new Date().toISOString()
      };
    }

    const params = new URLSearchParams();
    if (filters?.skip !== undefined) params.append('skip', filters.skip.toString());
    if (filters?.limit !== undefined) params.append('limit', filters.limit.toString());
    if (filters?.category) params.append('category', filters.category);
    if (filters?.difficulty) params.append('difficulty', filters.difficulty);
    if (filters?.visibility) params.append('visibility', filters.visibility);
    if (filters?.status) params.append('status', filters.status);
    if (filters?.search) params.append('search', filters.search);
    if (filters?.tags) params.append('tags', filters.tags);
    if (filters?.my_templates) params.append('my_templates', filters.my_templates.toString());

    return this.request<TemplateListItem[]>({
      method: "GET",
      url: `/api/v1/templates/legacy?${params.toString()}`
    });
  }

  async getTemplatesPaginated(filters?: TemplateFilters & { page?: number; limit?: number }): Promise<ApiResponse<PaginatedTemplateResponse>> {
    if (USE_MOCK_API) {
      const mockItems = [
        {
          id: 1,
          template_id: "detective_team",
          name: "侦探二人组",
          description: "由禅意僧侣和街头老兵组成的侦探团队",
          category: "analysis" as TemplateCategory,
          difficulty: "intermediate" as TemplateDifficulty,
          visibility: "public" as TemplateVisibility,
          status: "active" as TemplateStatus,
          tags: ["推理", "对话", "案件分析"],
          usage_count: 156,
          rating: 4.8,
          rating_count: 25,
          version: "1.0.0",
          author_name: "系统模板",
          use_case: "适用于需要深度分析和实地调查的复杂问题解决",
          created_at: "2025-06-01T00:00:00Z",
          is_owner: false,
          can_edit: false,
        }
      ];

      return {
        success: true,
        data: {
          items: mockItems,
          pagination: {
            page: filters?.page || 1,
            limit: filters?.limit || 20,
            total: 1,
            total_pages: 1,
            totalPages: 1,
            has_next: false,
            has_prev: false,
            hasNext: false,
            hasPrev: false
          }
        },
        timestamp: new Date().toISOString()
      };
    }

    const params = new URLSearchParams();
    if (filters?.page !== undefined) params.append('page', filters.page.toString());
    if (filters?.limit !== undefined) params.append('limit', filters.limit.toString());
    if (filters?.category) params.append('category', filters.category);
    if (filters?.difficulty) params.append('difficulty', filters.difficulty);
    if (filters?.visibility) params.append('visibility', filters.visibility);
    if (filters?.status) params.append('status', filters.status);
    if (filters?.search) params.append('search', filters.search);
    if (filters?.tags) params.append('tags', filters.tags);
    if (filters?.my_templates) params.append('my_templates', filters.my_templates.toString());

    return this.request<PaginatedTemplateResponse>({
      method: "GET",
      url: `/api/v1/templates?${params.toString()}`
    });
  }

  async getTemplate(templateId: string): Promise<ApiResponse<Template>> {
    if (USE_MOCK_API) {
      return {
        success: true,
        data: {
          id: 1,
          template_id: templateId,
          name: "侦探二人组",
          description: "由禅意僧侣和街头老兵组成的侦探团队，专门解决复杂案件",
          category: "investigation",
          difficulty: "intermediate",
          visibility: "public",
          status: "active",
          prompt_template: "你们是一个专业的侦探团队...",
          team_structure_template: {
            team_name: "侦探二人组",
            team_members: [
              {
                name: "禅意僧侣",
                role: "analyst",
                description: "冷静理性的分析师"
              }
            ]
          },
          tags: ["推理", "对话", "案件分析"],
          usage_count: 156,
          rating: 4.8,
          rating_count: 25,
          version: "1.0.0",
          author_name: "系统模板",
          created_at: "2025-06-01T00:00:00Z",
          is_owner: false,
          can_edit: false,
        },
        timestamp: new Date().toISOString()
      };
    }

    return this.request<Template>({
      method: "GET",
      url: `/api/v1/templates/${templateId}`
    });
  }

  async createTemplate(templateData: TemplateCreateRequest): Promise<ApiResponse<Template>> {
    if (USE_MOCK_API) {
      return {
        success: true,
        data: {
          id: Date.now(),
          template_id: `template_${Date.now()}`,
          ...templateData,
          visibility: templateData.visibility || "private",
          status: templateData.status || "active",
          tags: templateData.tags || [],
          usage_count: 0,
          rating_count: 0,
          version: "1.0.0",
          created_at: new Date().toISOString(),
          is_owner: true,
          can_edit: true,
        },
        timestamp: new Date().toISOString()
      };
    }

    return this.request<Template>({
      method: "POST",
      url: "/api/v1/templates",
      data: templateData
    });
  }

  async updateTemplate(templateId: string, updates: TemplateUpdateRequest): Promise<ApiResponse<Template>> {
    if (USE_MOCK_API) {
      return {
        success: true,
        data: {
          id: 1,
          template_id: templateId,
          name: "Updated Template",
          description: "Updated description",
          category: "business",
          difficulty: "intermediate",
          visibility: "private",
          status: "active",
          prompt_template: "Updated prompt",
          team_structure_template: {},
          tags: [],
          usage_count: 0,
          rating_count: 0,
          version: "1.0.1",
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          is_owner: true,
          can_edit: true,
          ...updates
        },
        timestamp: new Date().toISOString()
      };
    }

    return this.request<Template>({
      method: "PUT",
      url: `/api/v1/templates/${templateId}`,
      data: updates
    });
  }

  async deleteTemplate(templateId: string): Promise<ApiResponse<{ message: string }>> {
    if (USE_MOCK_API) {
      return {
        success: true,
        data: { message: "Template deleted successfully" },
        timestamp: new Date().toISOString()
      };
    }

    return this.request<{ message: string }>({
      method: "DELETE",
      url: `/api/v1/templates/${templateId}`
    });
  }

  async createTemplateFromAgent(request: TemplateFromAgentRequest): Promise<ApiResponse<Template>> {
    if (USE_MOCK_API) {
      return {
        success: true,
        data: {
          id: Date.now(),
          template_id: `template_${Date.now()}`,
          name: request.name,
          description: request.description,
          category: request.category,
          difficulty: request.difficulty,
          visibility: request.visibility || "private",
          status: "active",
          prompt_template: "Generated from agent",
          team_structure_template: {},
          tags: request.tags || [],
          usage_count: 0,
          rating_count: 0,
          version: "1.0.0",
          source_agent_id: request.agent_id,
          created_at: new Date().toISOString(),
          is_owner: true,
          can_edit: true,
        },
        timestamp: new Date().toISOString()
      };
    }

    return this.request<Template>({
      method: "POST",
      url: "/api/v1/templates/from-agent",
      data: request
    });
  }

  async duplicateTemplate(templateId: string, name?: string): Promise<ApiResponse<Template>> {
    if (USE_MOCK_API) {
      return {
        success: true,
        data: {
          id: Date.now(),
          template_id: `template_${Date.now()}`,
          name: name || "Template Copy",
          description: "Duplicated template",
          category: "business",
          difficulty: "intermediate",
          visibility: "private",
          status: "active",
          prompt_template: "Duplicated prompt",
          team_structure_template: {},
          tags: [],
          usage_count: 0,
          rating_count: 0,
          version: "1.0.0",
          parent_template_id: templateId,
          created_at: new Date().toISOString(),
          is_owner: true,
          can_edit: true,
        },
        timestamp: new Date().toISOString()
      };
    }

    const params = new URLSearchParams();
    if (name) params.append('name', name);

    return this.request<Template>({
      method: "POST",
      url: `/api/v1/templates/${templateId}/duplicate?${params.toString()}`
    });
  }

  async searchTemplates(query: string, filters?: Omit<TemplateFilters, 'search'>): Promise<ApiResponse<TemplateListItem[]>> {
    if (USE_MOCK_API) {
      return {
        success: true,
        data: [
          {
            id: 1,
            template_id: "search_result_1",
            name: "搜索结果模板",
            description: "匹配搜索查询的模板",
            category: "business",
            difficulty: "intermediate",
            visibility: "public",
            status: "active",
            tags: [query],
            usage_count: 50,
            rating: 4.5,
            rating_count: 10,
            version: "1.0.0",
            author_name: "用户",
            created_at: new Date().toISOString(),
            is_owner: false,
            can_edit: false,
          }
        ],
        timestamp: new Date().toISOString()
      };
    }

    const params = new URLSearchParams();
    params.append('q', query);
    if (filters?.category) params.append('category', filters.category);
    if (filters?.difficulty) params.append('difficulty', filters.difficulty);
    if (filters?.tags) params.append('tags', filters.tags);

    return this.request<TemplateListItem[]>({
      method: "GET",
      url: `/api/v1/templates/search?${params.toString()}`
    });
  }

  async getTemplateVersions(templateId: string): Promise<ApiResponse<TemplateListItem[]>> {
    if (USE_MOCK_API) {
      return {
        success: true,
        data: [
          {
            id: 1,
            template_id: templateId,
            name: "模板 v1.0.0",
            description: "原始版本",
            category: "business",
            difficulty: "intermediate",
            visibility: "private",
            status: "active",
            tags: [],
            usage_count: 10,
            rating: 4.0,
            rating_count: 2,
            version: "1.0.0",
            author_name: "用户",
            created_at: new Date(Date.now() - 86400000).toISOString(),
            is_owner: true,
            can_edit: true,
          },
          {
            id: 2,
            template_id: `${templateId}_v2`,
            name: "模板 v1.0.1",
            description: "更新版本",
            category: "business",
            difficulty: "intermediate",
            visibility: "private",
            status: "active",
            tags: [],
            usage_count: 5,
            rating: 4.2,
            rating_count: 1,
            version: "1.0.1",
            author_name: "用户",
            created_at: new Date().toISOString(),
            is_owner: true,
            can_edit: true,
          }
        ],
        timestamp: new Date().toISOString()
      };
    }

    return this.request<TemplateListItem[]>({
      method: "GET",
      url: `/api/v1/templates/${templateId}/versions`
    });
  }

  async createTemplateVersion(templateId: string, updates: TemplateUpdateRequest, version?: string): Promise<ApiResponse<Template>> {
    if (USE_MOCK_API) {
      return {
        success: true,
        data: {
          id: Date.now(),
          template_id: `${templateId}_v${Date.now()}`,
          name: updates.name || "新版本模板",
          description: updates.description || "模板新版本",
          category: updates.category || "business",
          difficulty: updates.difficulty || "intermediate",
          visibility: "private",
          status: "active",
          prompt_template: updates.prompt_template || "更新的提示词",
          team_structure_template: updates.team_structure_template || {},
          tags: updates.tags || [],
          usage_count: 0,
          rating_count: 0,
          version: version || "1.0.1",
          parent_template_id: templateId,
          created_at: new Date().toISOString(),
          is_owner: true,
          can_edit: true,
        },
        timestamp: new Date().toISOString()
      };
    }

    const requestData = { ...updates };
    if (version) {
      requestData.version = version;
    }

    return this.request<Template>({
      method: "POST",
      url: `/api/v1/templates/${templateId}/create-version`,
      data: requestData
    });
  }

  async rollbackTemplate(templateId: string, versionTemplateId: string): Promise<ApiResponse<Template>> {
    if (USE_MOCK_API) {
      return {
        success: true,
        data: {
          id: 1,
          template_id: templateId,
          name: "回滚后的模板",
          description: "已回滚到之前版本",
          category: "business",
          difficulty: "intermediate",
          visibility: "private",
          status: "active",
          prompt_template: "回滚的提示词",
          team_structure_template: {},
          tags: [],
          usage_count: 15,
          rating_count: 3,
          version: "1.0.0",
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          is_owner: true,
          can_edit: true,
        },
        timestamp: new Date().toISOString()
      };
    }

    return this.request<Template>({
      method: "POST",
      url: `/api/v1/templates/${templateId}/rollback/${versionTemplateId}`
    });
  }

  async getTemplateCategories(): Promise<ApiResponse<Array<{ value: string; label: string }>>> {
    if (USE_MOCK_API) {
      return {
        success: true,
        data: [
          { value: "business", label: "Business" },
          { value: "technical", label: "Technical" },
          { value: "creative", label: "Creative" },
          { value: "analysis", label: "Analysis" },
          { value: "support", label: "Support" },
          { value: "education", label: "Education" },
          { value: "investigation", label: "Investigation" },
          { value: "consulting", label: "Consulting" },
          { value: "research", label: "Research" },
          { value: "customer_service", label: "Customer Service" },
          { value: "marketing", label: "Marketing" },
          { value: "sales", label: "Sales" },
          { value: "healthcare", label: "Healthcare" },
          { value: "finance", label: "Finance" },
          { value: "legal", label: "Legal" },
          { value: "other", label: "Other" }
        ],
        timestamp: new Date().toISOString()
      };
    }

    return this.request<Array<{ value: string; label: string }>>({
      method: "GET",
      url: "/api/v1/templates/categories/list"
    });
  }

  async getTemplateDifficulties(): Promise<ApiResponse<Array<{ value: string; label: string }>>> {
    if (USE_MOCK_API) {
      return {
        success: true,
        data: [
          { value: "beginner", label: "Beginner" },
          { value: "intermediate", label: "Intermediate" },
          { value: "advanced", label: "Advanced" },
          { value: "expert", label: "Expert" }
        ],
        timestamp: new Date().toISOString()
      };
    }

    return this.request<Array<{ value: string; label: string }>>({
      method: "GET",
      url: "/api/v1/templates/difficulties/list"
    });
  }

  async getPopularTags(limit: number = 20): Promise<ApiResponse<PopularTag[]>> {
    if (USE_MOCK_API) {
      return {
        success: true,
        data: [
          { tag: "推理", count: 15, avg_rating: 4.5, total_usage: 200 },
          { tag: "对话", count: 12, avg_rating: 4.3, total_usage: 180 },
          { tag: "分析", count: 10, avg_rating: 4.6, total_usage: 150 },
          { tag: "创意", count: 8, avg_rating: 4.2, total_usage: 120 },
          { tag: "技术", count: 7, avg_rating: 4.4, total_usage: 100 }
        ],
        timestamp: new Date().toISOString()
      };
    }

    return this.request<PopularTag[]>({
      method: "GET",
      url: `/api/v1/templates/tags/popular?limit=${limit}`
    });
  }

  async getTemplateStats(): Promise<ApiResponse<TemplateStats>> {
    if (USE_MOCK_API) {
      return {
        success: true,
        data: {
          overview: {
            total_templates: 150,
            public_templates: 80,
            featured_templates: 15,
            user_templates: 25,
            avg_rating: 4.3,
            total_usage: 2500,
            categories_count: 12
          },
          categories: [
            { category: "business", count: 30, avg_rating: 4.2, total_usage: 500 },
            { category: "technical", count: 25, avg_rating: 4.4, total_usage: 450 },
            { category: "creative", count: 20, avg_rating: 4.1, total_usage: 300 }
          ],
          difficulties: [
            { difficulty: "beginner", count: 40, avg_rating: 4.0 },
            { difficulty: "intermediate", count: 60, avg_rating: 4.3 },
            { difficulty: "advanced", count: 35, avg_rating: 4.5 },
            { difficulty: "expert", count: 15, avg_rating: 4.7 }
          ]
        },
        timestamp: new Date().toISOString()
      };
    }

    return this.request<TemplateStats>({
      method: "GET",
      url: "/api/v1/templates/stats"
    });
  }

  async shareTemplate(templateId: string, visibility: TemplateVisibility): Promise<ApiResponse<{ message: string }>> {
    if (USE_MOCK_API) {
      return {
        success: true,
        data: { message: `Template sharing updated to ${visibility}` },
        timestamp: new Date().toISOString()
      };
    }

    return this.request<{ message: string }>({
      method: "POST",
      url: `/api/v1/templates/${templateId}/share`,
      params: { visibility }
    });
  }

  async getFeaturedTemplates(limit: number = 10): Promise<ApiResponse<TemplateListItem[]>> {
    if (USE_MOCK_API) {
      return {
        success: true,
        data: [
          {
            id: 1,
            template_id: "featured_1",
            name: "精选模板",
            description: "这是一个精选的高质量模板",
            category: "business",
            difficulty: "intermediate",
            visibility: "featured",
            status: "active",
            tags: ["精选", "推荐"],
            usage_count: 500,
            rating: 4.9,
            rating_count: 50,
            version: "1.0.0",
            author_name: "系统",
            created_at: new Date().toISOString(),
            is_owner: false,
            can_edit: false,
          }
        ],
        timestamp: new Date().toISOString()
      };
    }

    return this.request<TemplateListItem[]>({
      method: "GET",
      url: `/api/v1/templates/public/featured?limit=${limit}`
    });
  }

  async getCommunityTemplates(filters?: {
    skip?: number;
    limit?: number;
    category?: TemplateCategory;
    difficulty?: TemplateDifficulty;
    sort_by?: string;
  }): Promise<ApiResponse<TemplateListItem[]>> {
    if (USE_MOCK_API) {
      return {
        success: true,
        data: [
          {
            id: 1,
            template_id: "community_1",
            name: "社区模板",
            description: "来自社区的优质模板",
            category: "technical",
            difficulty: "advanced",
            visibility: "public",
            status: "active",
            tags: ["社区", "开源"],
            usage_count: 200,
            rating: 4.5,
            rating_count: 20,
            version: "1.0.0",
            author_name: "社区用户",
            created_at: new Date().toISOString(),
            is_owner: false,
            can_edit: false,
          }
        ],
        timestamp: new Date().toISOString()
      };
    }

    const params = new URLSearchParams();
    if (filters?.skip !== undefined) params.append('skip', filters.skip.toString());
    if (filters?.limit !== undefined) params.append('limit', filters.limit.toString());
    if (filters?.category) params.append('category', filters.category);
    if (filters?.difficulty) params.append('difficulty', filters.difficulty);
    if (filters?.sort_by) params.append('sort_by', filters.sort_by);

    return this.request<TemplateListItem[]>({
      method: "GET",
      url: `/api/v1/templates/community?${params.toString()}`
    });
  }

  async rateTemplate(templateId: string, rating: number): Promise<ApiResponse<{ message: string; new_rating: number; rating_count: number }>> {
    if (USE_MOCK_API) {
      return {
        success: true,
        data: {
          message: "Rating submitted successfully",
          new_rating: 4.5,
          rating_count: 10
        },
        timestamp: new Date().toISOString()
      };
    }

    return this.request<{ message: string; new_rating: number; rating_count: number }>({
      method: "POST",
      url: `/api/v1/templates/${templateId}/rate`,
      params: { rating }
    });
  }

  async analyzeRequirements(userDescription: string): Promise<ApiResponse<any>> {
    if (USE_MOCK_API) {
      // Simulate async response for mock
      await new Promise(resolve => setTimeout(resolve, 2000));
      return {
        success: true,
        data: {
          request_id: "mock_plan_123",
          status: "completed",
          team_plan: {
            team_name: "测试团队",
            description: "基于用户需求生成的测试团队",
            objective: "为用户提供智能服务",
            team_members: [
              {
                name: "分析师",
                role: "数据分析专家",
                system_prompt: "你是一个专业的数据分析师...",
                description: "负责数据分析和洞察"
              }
            ],
            workflow: {
              steps: [
                {
                  name: "需求分析",
                  description: "分析用户需求",
                  assignee: "分析师",
                  inputs: ["用户描述"],
                  outputs: ["需求文档"]
                }
              ]
            }
          },
          message: "Team plan generated successfully"
        },
        timestamp: new Date().toISOString()
      };
    }

    // Start async team generation
    const response = await this.request<any>({
      method: "POST",
      url: "/api/v1/planning/analyze",
      data: { user_description: userDescription }
    });

    if (!response.success || !response.data?.request_id) {
      return response;
    }

    // Poll for completion
    return this.pollPlanningStatus(response.data.request_id);
  }

  async createPlanningRequest(userDescription: string, templateId?: string): Promise<ApiResponse<any>> {
    if (USE_MOCK_API) {
      // Simulate async response for mock
      await new Promise(resolve => setTimeout(resolve, 2000));
      return {
        success: true,
        data: {
          request_id: "mock_plan_456",
          status: "completed",
          team_plan: {
            team_name: "测试团队",
            description: "基于用户需求生成的测试团队",
            objective: "为用户提供智能服务",
            team_members: [],
            workflow: { steps: [] }
          },
          message: "Team plan generated using ai_powered method"
        },
        timestamp: new Date().toISOString()
      };
    }

    const data: any = { user_description: userDescription };
    if (templateId) {
      data.template_id = templateId;
    }
    // Note: AI configuration is not sent to planning API
    // Planning uses system settings for team generation

    // Start async team generation
    const response = await this.request<any>({
      method: "POST",
      url: "/api/v1/planning/generate",
      data
    });

    if (!response.success || !response.data?.request_id) {
      return response;
    }

    // Poll for completion
    return this.pollPlanningStatus(response.data.request_id);
  }

  async pollPlanningStatus(requestId: string, maxAttempts: number = 30): Promise<ApiResponse<any>> {
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      try {
        const response = await this.request<any>({
          method: "GET",
          url: `/api/v1/planning/status/${requestId}`
        });

        if (!response.success) {
          return response;
        }

        const status = response.data?.status;

        if (status === "completed") {
          return {
            success: true,
            data: {
              request_id: requestId,
              status: "completed",
              team_plan: response.data.team_plan,
              message: "Team plan generated successfully"
            },
            timestamp: new Date().toISOString()
          };
        } else if (status === "failed") {
          return {
            success: false,
            error: {
              message: response.data?.error?.message || "Team generation failed",
              code: "GENERATION_FAILED"
            },
            timestamp: new Date().toISOString()
          };
        }

        // Still processing, wait before next poll
        await new Promise(resolve => setTimeout(resolve, 2000));
      } catch (error) {
        // Log polling errors only in development
        if (process.env.NODE_ENV === 'development') {
          console.error(`Polling attempt ${attempt + 1} failed:`, error);
        }
        if (attempt === maxAttempts - 1) {
          return {
            success: false,
            error: {
              message: "Failed to get planning status after multiple attempts",
              code: "POLLING_FAILED"
            },
            timestamp: new Date().toISOString()
          };
        }
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    return {
      success: false,
      error: {
        message: "Team generation timed out",
        code: "TIMEOUT"
      },
      timestamp: new Date().toISOString()
    };
  }

  // 健康检查
  async healthCheck(): Promise<ApiResponse<{ status: string; timestamp: string }>> {
    return this.request<{ status: string; timestamp: string }>({
      method: "GET",
      url: "/health"
    });
  }

  // 测试执行相关API
  async startTestExecution(data: any): Promise<ApiResponse<any>> {
    return this.request<any>({
      method: "POST",
      url: "/api/v1/test-execution/start",
      data
    });
  }

  // 测试历史相关API
  async createTestHistory(data: any): Promise<ApiResponse<any>> {
    return this.request<any>({
      method: "POST",
      url: "/api/v1/test-history/",
      data
    });
  }

  async updateTestHistory(testId: string, data: any): Promise<ApiResponse<any>> {
    return this.request<any>({
      method: "PUT",
      url: `/api/v1/test-history/${testId}`,
      data
    });
  }

  async listTestHistory(params?: any): Promise<ApiResponse<any>> {
    return this.request<any>({
      method: "GET",
      url: "/api/v1/test-history/",
      params
    });
  }

  async getTestHistoryDetail(testId: string): Promise<ApiResponse<any>> {
    return this.request<any>({
      method: "GET",
      url: `/api/v1/test-history/${testId}`
    });
  }

  async deleteTestHistory(testId: string): Promise<ApiResponse<any>> {
    return this.request<any>({
      method: "DELETE",
      url: `/api/v1/test-history/${testId}`
    });
  }

  async getTestHistoryByAgent(agentId: string, params?: any): Promise<ApiResponse<any>> {
    return this.request<any>({
      method: "GET",
      url: "/api/v1/test-history/",
      params: { ...params, agent_id: agentId }
    });
  }

  async getRecentTestHistory(): Promise<ApiResponse<any>> {
    return this.request<any>({
      method: "GET",
      url: "/api/v1/test-history/",
      params: { limit: 10, page: 1 }
    });
  }

  // Intelligence and Analytics API
  async getIntelligenceInsights(params?: {
    agent_id?: string;
    insight_type?: string;
    severity?: string;
    limit?: number;
  }): Promise<ApiResponse<any[]>> {
    const searchParams = new URLSearchParams();
    if (params?.agent_id) searchParams.append('agent_id', params.agent_id);
    if (params?.insight_type) searchParams.append('insight_type', params.insight_type);
    if (params?.severity) searchParams.append('severity', params.severity);
    if (params?.limit) searchParams.append('limit', params.limit.toString());

    return this.request<any[]>({
      method: "GET",
      url: `/api/v1/intelligence/insights?${searchParams.toString()}`
    });
  }

  async generateIntelligenceInsights(agentId?: string): Promise<ApiResponse<any>> {
    const searchParams = new URLSearchParams();
    if (agentId) searchParams.append('agent_id', agentId);

    return this.request<any>({
      method: "POST",
      url: `/api/v1/intelligence/insights/generate?${searchParams.toString()}`
    });
  }

  async performInsightAction(insightId: string, action: string): Promise<ApiResponse<any>> {
    return this.request<any>({
      method: "POST",
      url: `/api/v1/intelligence/insights/${insightId}/action`,
      data: { action }
    });
  }

  async getSystemMetrics(): Promise<ApiResponse<any>> {
    return this.request<any>({
      method: "GET",
      url: "/api/v1/intelligence/metrics/system"
    });
  }

  async getAgentMetrics(agentId?: string): Promise<ApiResponse<any[]>> {
    const searchParams = new URLSearchParams();
    if (agentId) searchParams.append('agent_id', agentId);

    return this.request<any[]>({
      method: "GET",
      url: `/api/v1/intelligence/metrics/agents?${searchParams.toString()}`
    });
  }

  async getPredictiveAnalytics(timeframe: string = '30d'): Promise<ApiResponse<any>> {
    return this.request<any>({
      method: "GET",
      url: `/api/v1/intelligence/analytics/predictive?timeframe=${timeframe}`
    });
  }

  async updateAgentMetrics(agentId: string, executionData: any): Promise<ApiResponse<any>> {
    return this.request<any>({
      method: "POST",
      url: `/api/v1/intelligence/metrics/agent/${agentId}/update`,
      data: executionData
    });
  }

  // Dashboard API
  async getDashboardStats(): Promise<ApiResponse<any>> {
    return this.request<any>({
      method: "GET",
      url: "/api/v1/dashboard/stats"
    });
  }

  async getDashboardData(): Promise<ApiResponse<any>> {
    return this.request<any>({
      method: "GET",
      url: "/api/v1/dashboard/data"
    });
  }

  async getDashboardHealth(): Promise<ApiResponse<any>> {
    return this.request<any>({
      method: "GET",
      url: "/api/v1/dashboard/health"
    });
  }

  // Application Logs API
  async getApplicationLogs(params: {
    page?: number;
    limit?: number;
    level?: string;
    event_type?: string;
    agent_id?: string;
    test_id?: string;
    template_id?: string;
    source_module?: string;
    error_code?: string;
    start_date?: string;
    end_date?: string;
    search_query?: string;
    tags?: string[];
    ip_address?: string;
  } = {}): Promise<ApiResponse<any>> {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          value.forEach(v => searchParams.append(key, v));
        } else {
          searchParams.append(key, value.toString());
        }
      }
    });

    return this.request<any>({
      method: "GET",
      url: `/api/v1/logs?${searchParams.toString()}`
    });
  }

  async getApplicationLogDetail(logId: number): Promise<ApiResponse<any>> {
    return this.request<any>({
      method: "GET",
      url: `/api/v1/logs/${logId}`
    });
  }

  async exportApplicationLogs(params: {
    format?: string;
    level?: string;
    event_type?: string;
    agent_id?: string;
    test_id?: string;
    start_date?: string;
    end_date?: string;
    search_query?: string;
    limit?: number;
  } = {}): Promise<Blob> {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, value.toString());
      }
    });

    const response = await this.axios.request({
      method: "GET",
      url: `/api/v1/logs/export?${searchParams.toString()}`,
      responseType: 'blob'
    });

    return response.data;
  }

  async getLogStatistics(): Promise<ApiResponse<any>> {
    return this.request<any>({
      method: "GET",
      url: `/api/v1/logs/stats`
    });
  }
  // Agent Favorites API
  async toggleAgentFavorite(agentId: string): Promise<ApiResponse<any>> {
    return this.request<any>({
      method: "POST",
      url: `/api/v1/agents/${agentId}/favorite`
    });
  }

  async getFavoriteAgents(includePerformance: boolean = true): Promise<ApiResponse<any[]>> {
    return this.request<any[]>({
      method: "GET",
      url: `/api/v1/agents/favorites?include_performance=${includePerformance}`
    });
  }
}

// 导出API客户端实例
export const apiClient = new APIClient();

// 导出便捷方法
export const api = {
  // Agent相关
  agents: {
    list: (filters?: AgentFilters, pagination?: PaginationParams) =>
      apiClient.getAgents(filters, pagination),
    get: (id: string) => apiClient.getAgent(id),
    create: (request: CreateAgentRequest) => apiClient.createAgent(request),
    createFromTemplate: (templateId: string, customizations?: any) =>
      apiClient.createAgentFromTemplate(templateId, customizations),
    update: (id: string, updates: AgentUpdate) => apiClient.updateAgent(id, updates),
    delete: (id: string) => apiClient.deleteAgent(id),
    updateStatus: (id: string, status: Agent["status"]) =>
      apiClient.updateAgentStatus(id, status),
    invoke: (id: string, request: AgentInvokeRequest) =>
      apiClient.invokeAgent(id, request),
    invokeStream: (
      id: string,
      request: AgentInvokeRequest,
      onMessage: (data: string) => void,
      onError: (error: Error) => void,
      onComplete: () => void
    ) => apiClient.invokeAgentStream(id, request, onMessage, onError, onComplete),
    invokeStreamWithProgress: (
      id: string,
      request: AgentInvokeRequest,
      callbacks: {
        onProgress?: (progressData: any) => void;
        onComplete?: (result: any) => void;
        onError?: (error: string) => void;
      }
    ) => apiClient.invokeAgentStreamWithProgress(id, request, callbacks),
    toggleFavorite: (agentId: string) => apiClient.toggleAgentFavorite(agentId),
    getFavorites: (includePerformance?: boolean) => apiClient.getFavoriteAgents(includePerformance),
    discoverVariables: (agentId: string) => apiClient.discoverAgentVariables(agentId)
  },

  // Task相关
  tasks: {
    getStatus: (taskId: string) => apiClient.getTaskStatus(taskId)
  },

  // 测试相关
  tests: {
    getHistory: (agentId?: string) => apiClient.getTestHistory(agentId)
  },

  // 统计相关
  stats: {
    get: () => apiClient.getStats()
  },

  // 模板管理相关
  templates: {
    list: (filters?: TemplateFilters & PaginationParams) => apiClient.getTemplates(filters),
    listPaginated: (filters?: TemplateFilters & { page?: number; limit?: number }) => apiClient.getTemplatesPaginated(filters),
    get: (templateId: string) => apiClient.getTemplate(templateId),
    create: (templateData: TemplateCreateRequest) => apiClient.createTemplate(templateData),
    update: (templateId: string, updates: TemplateUpdateRequest) =>
      apiClient.updateTemplate(templateId, updates),
    delete: (templateId: string) => apiClient.deleteTemplate(templateId),
    createFromAgent: (request: TemplateFromAgentRequest) =>
      apiClient.createTemplateFromAgent(request),
    duplicate: (templateId: string, name?: string) =>
      apiClient.duplicateTemplate(templateId, name),
    search: (query: string, filters?: Omit<TemplateFilters, 'search'>) =>
      apiClient.searchTemplates(query, filters),
    getVersions: (templateId: string) => apiClient.getTemplateVersions(templateId),
    createVersion: (templateId: string, updates: TemplateUpdateRequest, version?: string) =>
      apiClient.createTemplateVersion(templateId, updates, version),
    rollback: (templateId: string, versionTemplateId: string) =>
      apiClient.rollbackTemplate(templateId, versionTemplateId),
    getCategories: () => apiClient.getTemplateCategories(),
    getDifficulties: () => apiClient.getTemplateDifficulties(),
    getPopularTags: (limit?: number) => apiClient.getPopularTags(limit),
    getStats: () => apiClient.getTemplateStats(),
    share: (templateId: string, visibility: TemplateVisibility) =>
      apiClient.shareTemplate(templateId, visibility),
    getFeatured: (limit?: number) => apiClient.getFeaturedTemplates(limit),
    getCommunity: (filters?: any) => apiClient.getCommunityTemplates(filters),
    rate: (templateId: string, rating: number) => apiClient.rateTemplate(templateId, rating)
  },

  // 规划相关
  planning: {
    analyze: (userDescription: string) => apiClient.analyzeRequirements(userDescription),
    create: (userDescription: string, templateId?: string) =>
      apiClient.createPlanningRequest(userDescription, templateId)
  },

  // 认证相关
  auth: {
    me: () => {
      // Import the API client from the auth module
      const { apiClient: authApiClient } = require('@/lib/api-client');
      return authApiClient.getCurrentUser();
    }
  },

  // 系统相关
  system: {
    healthCheck: () => apiClient.healthCheck(),
    restartAgent: (agentId: string) => apiClient.restartAgent(agentId)
  },

  // 测试执行相关
  testExecution: {
    start: (data: any) => apiClient.startTestExecution(data)
  },

  // 测试历史相关
  testHistory: {
    create: (data: any) => apiClient.createTestHistory(data),
    update: (testId: string, data: any) => apiClient.updateTestHistory(testId, data),
    list: (params?: any) => apiClient.listTestHistory(params),
    getDetail: (testId: string) => apiClient.getTestHistoryDetail(testId),
    delete: (testId: string) => apiClient.deleteTestHistory(testId),
    getByAgent: (agentId: string, params?: any) => apiClient.getTestHistoryByAgent(agentId, params),
    getRecent: () => apiClient.getRecentTestHistory()
  },

  // 日志相关
  logs: {
    get: (params?: { level?: string; source?: string; lines?: number }) =>
      apiClient.getLogs(params),
    clear: (source?: string) => apiClient.clearLogs(source),
    export: (source: string, format?: "json" | "txt") =>
      apiClient.exportLogs(source, format)
  },

  // API密钥相关
  apiKeys: {
    list: () => apiClient.getApiKeys(),
    create: (keyData: APIKeyCreate) => apiClient.createApiKey(keyData),
    update: (keyId: string, keyUpdate: APIKeyUpdate) =>
      apiClient.updateApiKey(keyId, keyUpdate),
    delete: (keyId: string) => apiClient.deleteApiKey(keyId),
    test: (keyId: string, testData: APIKeyTest) =>
      apiClient.testApiKey(keyId, testData)
  },

  // 智能分析相关
  intelligence: {
    getInsights: (params?: {
      agent_id?: string;
      insight_type?: string;
      severity?: string;
      limit?: number;
    }) => apiClient.getIntelligenceInsights(params),
    generateInsights: (agentId?: string) => apiClient.generateIntelligenceInsights(agentId),
    performAction: (insightId: string, action: string) =>
      apiClient.performInsightAction(insightId, action),
    getSystemMetrics: () => apiClient.getSystemMetrics(),
    getAgentMetrics: (agentId?: string) => apiClient.getAgentMetrics(agentId),
    getPredictiveAnalytics: (timeframe?: string) =>
      apiClient.getPredictiveAnalytics(timeframe),
    updateAgentMetrics: (agentId: string, executionData: any) =>
      apiClient.updateAgentMetrics(agentId, executionData)
  },

  // 仪表板相关
  dashboard: {
    getStats: () => apiClient.getDashboardStats(),
    getData: () => apiClient.getDashboardData(),
    getHealth: () => apiClient.getDashboardHealth()
  },

  // 应用日志相关
  logs: {
    list: (params?: {
      page?: number;
      limit?: number;
      level?: string;
      event_type?: string;
      agent_id?: string;
      test_id?: string;
      template_id?: string;
      source_module?: string;
      error_code?: string;
      start_date?: string;
      end_date?: string;
      search_query?: string;
      tags?: string[];
      ip_address?: string;
    }) => apiClient.getApplicationLogs(params),
    get: (logId: number) => apiClient.getApplicationLogDetail(logId),
    export: (params?: {
      format?: string;
      level?: string;
      event_type?: string;
      agent_id?: string;
      test_id?: string;
      start_date?: string;
      end_date?: string;
      search_query?: string;
      limit?: number;
    }) => apiClient.exportApplicationLogs(params),
    getStats: () => apiClient.getLogStatistics()
  }
};
