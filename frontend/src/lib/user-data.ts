/**
 * User Data Management Utilities
 * Provides data isolation and user-specific data management for multi-user environment
 */

export interface UserDataManager {
  getUserData<T>(key: string, defaultValue?: T): T | null
  setUserData<T>(key: string, data: T): void
  removeUserData(key: string): void
  clearUserData(): void
  getUserId(): string | null
}

class LocalStorageUserDataManager implements UserDataManager {
  private getUserPrefix(): string {
    const userData = localStorage.getItem('user_data')
    if (!userData) return ''
    
    try {
      const user = JSON.parse(userData)
      return `user_${user.id}_`
    } catch {
      return ''
    }
  }

  getUserId(): string | null {
    const userData = localStorage.getItem('user_data')
    if (!userData) return null
    
    try {
      const user = JSON.parse(userData)
      return user.id
    } catch {
      return null
    }
  }

  getUserData<T>(key: string, defaultValue?: T): T | null {
    const prefix = this.getUserPrefix()
    if (!prefix) return defaultValue || null

    try {
      const data = localStorage.getItem(`${prefix}${key}`)
      return data ? JSON.parse(data) : (defaultValue || null)
    } catch {
      return defaultValue || null
    }
  }

  setUserData<T>(key: string, data: T): void {
    const prefix = this.getUserPrefix()
    if (!prefix) return

    try {
      localStorage.setItem(`${prefix}${key}`, JSON.stringify(data))
    } catch (error) {
      console.error('Failed to save user data:', error)
    }
  }

  removeUserData(key: string): void {
    const prefix = this.getUserPrefix()
    if (!prefix) return

    localStorage.removeItem(`${prefix}${key}`)
  }

  clearUserData(): void {
    const prefix = this.getUserPrefix()
    if (!prefix) return

    // Remove all user-specific data
    const keys = Object.keys(localStorage)
    keys.forEach(key => {
      if (key.startsWith(prefix)) {
        localStorage.removeItem(key)
      }
    })
  }
}

// Singleton instance
export const userDataManager: UserDataManager = new LocalStorageUserDataManager()

// Convenience functions for common data types
export const userAgents = {
  getAll: () => userDataManager.getUserData('agents', []),
  save: (agents: any[]) => userDataManager.setUserData('agents', agents),
  add: (agent: any) => {
    const agents = userAgents.getAll()
    agents.push({ ...agent, id: Date.now().toString(), userId: userDataManager.getUserId() })
    userAgents.save(agents)
    return agents[agents.length - 1]
  },
  update: (id: string, updates: any) => {
    const agents = userAgents.getAll()
    const index = agents.findIndex((a: any) => a.id === id)
    if (index !== -1) {
      agents[index] = { ...agents[index], ...updates }
      userAgents.save(agents)
      return agents[index]
    }
    return null
  },
  remove: (id: string) => {
    const agents = userAgents.getAll()
    const filtered = agents.filter((a: any) => a.id !== id)
    userAgents.save(filtered)
    return filtered
  },
  getById: (id: string) => {
    const agents = userAgents.getAll()
    return agents.find((a: any) => a.id === id) || null
  }
}

export const userTests = {
  getAll: () => userDataManager.getUserData('tests', []),
  save: (tests: any[]) => userDataManager.setUserData('tests', tests),
  add: (test: any) => {
    const tests = userTests.getAll()
    tests.push({ ...test, id: Date.now().toString(), userId: userDataManager.getUserId() })
    userTests.save(tests)
    return tests[tests.length - 1]
  },
  getByAgentId: (agentId: string) => {
    const tests = userTests.getAll()
    return tests.filter((t: any) => t.agentId === agentId)
  }
}

export const userLogs = {
  getAll: () => userDataManager.getUserData('logs', []),
  save: (logs: any[]) => userDataManager.setUserData('logs', logs),
  add: (log: any) => {
    const logs = userLogs.getAll()
    logs.push({ ...log, id: Date.now().toString(), userId: userDataManager.getUserId(), timestamp: new Date().toISOString() })
    userLogs.save(logs)
    return logs[logs.length - 1]
  },
  getByAgentId: (agentId: string) => {
    const logs = userLogs.getAll()
    return logs.filter((l: any) => l.agentId === agentId)
  }
}

export const userSettings = {
  get: () => userDataManager.getUserData('settings', {}),
  save: (settings: any) => userDataManager.setUserData('settings', settings),
  update: (updates: any) => {
    const settings = userSettings.get()
    const updated = { ...settings, ...updates }
    userSettings.save(updated)
    return updated
  }
}

// Initialize user data when user logs in
export const initializeUserData = (userId: string) => {
  // Create default data structure if it doesn't exist
  if (!userAgents.getAll().length) {
    userAgents.save([])
  }
  if (!userTests.getAll().length) {
    userTests.save([])
  }
  if (!userLogs.getAll().length) {
    userLogs.save([])
  }
  if (!Object.keys(userSettings.get()).length) {
    userSettings.save({
      theme: 'system',
      notifications: true,
      language: 'zh-CN'
    })
  }
}

// Clean up user data when user logs out
export const cleanupUserData = () => {
  userDataManager.clearUserData()
}
