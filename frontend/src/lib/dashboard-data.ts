"use client";

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/lib/auth';
import { api } from '@/lib/api';
import { testHistoryApi } from '@/lib/api/test-history';
import { useEffect, useCallback, useState } from 'react';

// Enhanced Dashboard Data Types

export interface RecentActivity {
  id: string;
  type: 'test_execution' | 'agent_created' | 'agent_updated' | 'template_used';
  title: string;
  description: string;
  timestamp: string;
  status: 'success' | 'error' | 'warning' | 'info';
  agentId?: string;
  agentName?: string;
  duration?: number;
  metadata?: Record<string, any>;
}

export interface AgentUsageData {
  date: string;
  requests: number;
  successRate: number;
  avgResponseTime: number;
}

export interface TopAgent {
  id: string;
  name: string;
  requests: number;
  successRate: number;
  lastUsed: string;
}

// Dashboard Agent Data Interface (matching ActiveAgentsSection requirements)
export interface DashboardAgentData {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'inactive' | 'error';
  lastUsed: string;
  totalRuns: number;
  successRate: number;
  avgResponseTime: number;
  isFavorite: boolean;
  category: string;
  performance: {
    trend: 'up' | 'down' | 'stable';
    score: number;
  };
}

export interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: string;
  href: string;
  color: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  badge?: string;
  disabled?: boolean;
}

export interface TemplatePreview {
  id: string;
  name: string;
  description: string;
  category: string;
  difficulty: string;
  rating: number;
  usageCount: number;
  thumbnail?: string;
  tags: string[];
}

export interface SystemHealth {
  apiStatus: 'healthy' | 'degraded' | 'down';
  aiPlannerStatus: 'healthy' | 'degraded' | 'down';
  codeGeneratorStatus: 'healthy' | 'degraded' | 'down';
  databaseStatus: 'healthy' | 'degraded' | 'down';
  uptime: number;
  lastCheck: string;
}

export interface UserProfile {
  id: number;
  name: string;
  email: string;
  avatar?: string;
  role: string;
  lastLogin: string;
  totalAgents: number;
  totalTests: number;
}

export interface DashboardData {
  usageHistory: AgentUsageData[];
  topAgents: TopAgent[];
  dashboardAgents: DashboardAgentData[];
  systemHealth: SystemHealth;
  recentActivity: RecentActivity[];
  quickActions: QuickAction[];
  featuredTemplates: TemplatePreview[];
  userProfile: UserProfile;
}

export interface ActivityItem {
  id: string;
  type: 'agent_created' | 'agent_executed' | 'agent_updated' | 'system_event';
  title: string;
  description: string;
  timestamp: string;
  userId?: string;
  agentId?: string;
  metadata?: Record<string, any>;
}

// Cache keys
export const DASHBOARD_CACHE_KEYS = {
  usageHistory: 'dashboard-usage-history',
  topAgents: 'dashboard-top-agents',
  systemHealth: 'dashboard-system-health',
  recentActivity: 'dashboard-recent-activity',
  quickActions: 'dashboard-quick-actions',
  featuredTemplates: 'dashboard-featured-templates',
  userProfile: 'dashboard-user-profile',
  fullDashboard: 'dashboard-full-data',
} as const;

// Cache configuration
export const CACHE_CONFIG = {
  staleTime: 30 * 1000, // 30 seconds
  cacheTime: 5 * 60 * 1000, // 5 minutes
  refetchInterval: 60 * 1000, // 1 minute
  refetchOnWindowFocus: true,
  retry: 3,
  retryDelay: (attemptIndex: number) => Math.min(1000 * 2 ** attemptIndex, 30000),
};

// Mock data generators for development

export const generateMockUsageHistory = (): AgentUsageData[] => {
  const history: AgentUsageData[] = [];
  const now = new Date();
  
  for (let i = 6; i >= 0; i--) {
    const date = new Date(now);
    date.setDate(date.getDate() - i);
    
    history.push({
      date: date.toISOString().split('T')[0],
      requests: 50 + Math.floor(Math.random() * 100),
      successRate: 90 + Math.random() * 10,
      avgResponseTime: 1000 + Math.random() * 1000,
    });
  }
  
  return history;
};

export const generateMockTopAgents = (): TopAgent[] => {
  const agents = [
    { name: "数据分析助手", id: "agent_1" },
    { name: "内容创作团队", id: "agent_2" },
    { name: "客服机器人", id: "agent_3" },
    { name: "代码审查助手", id: "agent_4" },
    { name: "文档生成器", id: "agent_5" },
  ];
  
  return agents.map(agent => ({
    ...agent,
    requests: 100 + Math.floor(Math.random() * 500),
    successRate: 85 + Math.random() * 15,
    lastUsed: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
  })).sort((a, b) => b.requests - a.requests);
};

export const generateMockSystemHealth = (): SystemHealth => ({
  apiStatus: Math.random() > 0.1 ? 'healthy' : 'degraded',
  aiPlannerStatus: Math.random() > 0.15 ? 'healthy' : 'degraded',
  codeGeneratorStatus: Math.random() > 0.2 ? 'healthy' : 'degraded',
  databaseStatus: Math.random() > 0.05 ? 'healthy' : 'degraded',
  uptime: 99.5 + Math.random() * 0.5,
  lastCheck: new Date().toISOString(),
});

export const generateMockRecentActivity = (): RecentActivity[] => {
  const activities: RecentActivity[] = [];
  const types: RecentActivity['type'][] = ['test_execution', 'agent_created', 'agent_updated', 'template_used'];

  for (let i = 0; i < 10; i++) {
    const type = types[Math.floor(Math.random() * types.length)];
    const timestamp = new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000);

    activities.push({
      id: `activity_${i}`,
      type,
      title: getActivityTitle(type),
      description: getActivityDescription(type),
      timestamp: timestamp.toISOString(),
      status: Math.random() > 0.8 ? 'error' : Math.random() > 0.9 ? 'warning' : 'success',
      agentId: type.includes('agent') || type === 'test_execution' ? `agent_${Math.floor(Math.random() * 10) + 1}` : undefined,
      agentName: type.includes('agent') || type === 'test_execution' ? `Agent ${Math.floor(Math.random() * 10) + 1}` : undefined,
      duration: type === 'test_execution' ? 1000 + Math.random() * 5000 : undefined,
    });
  }
  
  return activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
};

export const generateMockFeaturedTemplates = (): TemplatePreview[] => {
  return [
    {
      id: 'template_1',
      name: '客服机器人模板',
      description: '智能客服系统，支持多轮对话和问题解答',
      category: '客服',
      difficulty: '简单',
      rating: 4.8,
      usageCount: 156,
      tags: ['客服', '对话', 'FAQ'],
    },
    {
      id: 'template_2',
      name: '代码审查助手',
      description: '自动化代码审查和质量检测工具',
      category: '开发',
      difficulty: '中等',
      rating: 4.6,
      usageCount: 89,
      tags: ['代码', '审查', '质量'],
    },
    {
      id: 'template_3',
      name: '文档生成器',
      description: '自动生成技术文档和API文档',
      category: '文档',
      difficulty: '简单',
      rating: 4.7,
      usageCount: 234,
      tags: ['文档', 'API', '自动化'],
    },
    {
      id: 'template_4',
      name: '数据分析助手',
      description: '智能数据分析和报告生成',
      category: '分析',
      difficulty: '困难',
      rating: 4.9,
      usageCount: 67,
      tags: ['数据', '分析', '报告'],
    },
    {
      id: 'template_5',
      name: '内容创作助手',
      description: 'AI驱动的内容创作和编辑工具',
      category: '创作',
      difficulty: '中等',
      rating: 4.5,
      usageCount: 123,
      tags: ['创作', '编辑', '内容'],
    },
    {
      id: 'template_6',
      name: '项目管理助手',
      description: '智能项目规划和任务管理',
      category: '管理',
      difficulty: '中等',
      rating: 4.4,
      usageCount: 98,
      tags: ['项目', '管理', '规划'],
    },
  ];
};

export const generateMockUserProfile = (): UserProfile => ({
  id: 1,
  name: '开发者',
  email: '<EMAIL>',
  avatar: undefined,
  role: 'user',
  lastLogin: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString(),
  totalAgents: 5 + Math.floor(Math.random() * 15),
  totalTests: 50 + Math.floor(Math.random() * 200),
});

function getActivityTitle(type: RecentActivity['type']): string {
  switch (type) {
    case 'test_execution': return 'Agent 测试执行';
    case 'agent_created': return '新 Agent 创建';
    case 'agent_updated': return 'Agent 配置更新';
    case 'template_used': return '模板使用';
    default: return '未知活动';
  }
}

function getActivityDescription(type: RecentActivity['type']): string {
  switch (type) {
    case 'test_execution': return 'Agent 成功处理了用户请求';
    case 'agent_created': return '用户创建了新的 AI Agent 团队';
    case 'agent_updated': return '用户更新了 Agent 配置';
    case 'template_used': return '用户使用了模板创建 Agent';
    default: return '系统记录了一个活动';
  }
}

// Real API data fetching functions

export const fetchRecentActivity = async (): Promise<RecentActivity[]> => {
  try {
    // Fetch recent test history
    const testHistory = await testHistoryApi.getRecent();

    const activities: RecentActivity[] = testHistory.map(test => ({
      id: test.test_id,
      type: 'test_execution' as const,
      title: `Agent Test: ${test.agent_id}`,
      description: test.input_text.substring(0, 100) + (test.input_text.length > 100 ? '...' : ''),
      timestamp: test.started_at,
      status: test.status === 'completed' ? 'success' :
              test.status === 'failed' ? 'error' :
              test.status === 'cancelled' ? 'warning' : 'info',
      agentId: test.agent_id,
      duration: test.execution_duration_ms,
      metadata: {
        testId: test.test_id,
        apiKeyName: test.api_key_name,
        hasError: !!test.error_message
      }
    }));

    return activities;
  } catch (error) {
    console.warn('Failed to fetch recent activity from API:', error);
    return generateMockRecentActivity();
  }
};

export const fetchQuickActions = async (): Promise<QuickAction[]> => {
  // Static quick actions for personal dashboard
  return [
    {
      id: 'create-agent',
      title: '创建 Agent',
      description: '快速创建新的 AI Agent',
      icon: 'plus',
      href: '/agents/create',
      color: 'primary',
    },
    {
      id: 'test-agent',
      title: '测试 Agent',
      description: '测试现有 Agent 功能',
      icon: 'play',
      href: '/agents/test',
      color: 'success',
    },

    {
      id: 'view-history',
      title: '查看历史',
      description: '查看测试执行历史',
      icon: 'history',
      href: '/test-history',
      color: 'warning',
    },
  ];
};

export const fetchFeaturedTemplates = async (): Promise<TemplatePreview[]> => {
  try {
    const response = await api.templates.getFeatured(6);
    if (response.success && response.data) {
      return response.data.map((template: any) => ({
        id: template.template_id,
        name: template.name,
        description: template.description,
        category: template.category,
        difficulty: template.difficulty,
        rating: template.rating || 0,
        usageCount: template.usage_count || 0,
        thumbnail: template.thumbnail,
        tags: template.tags || [],
      }));
    }
  } catch (error) {
    console.warn('Failed to fetch featured templates:', error);
  }

  return generateMockFeaturedTemplates();
};

export const fetchUserProfile = async (): Promise<UserProfile> => {
  try {
    // Fetch user profile from auth API
    const userResponse = await api.auth?.me?.();
    const agentsResponse = await api.agents.list();
    const testsResponse = await testHistoryApi.list({ limit: 1 });

    if (userResponse?.success && userResponse.data) {
      return {
        id: userResponse.data.id,
        name: userResponse.data.name,
        email: userResponse.data.email,
        avatar: userResponse.data.avatar,
        role: userResponse.data.role,
        lastLogin: userResponse.data.last_login_at || new Date().toISOString(),
        totalAgents: agentsResponse?.success ? agentsResponse.data?.length || 0 : 0,
        totalTests: testsResponse?.total || 0,
      };
    }
  } catch (error) {
    console.warn('Failed to fetch user profile:', error);
  }

  return generateMockUserProfile();
};

export const fetchUsageHistory = async (): Promise<AgentUsageData[]> => {
  try {
    // Fetch agent metrics for usage history
    const response = await api.intelligence.getAgentMetrics();
    if (response.success && response.data) {
      // Convert agent metrics to usage history format
      const last7Days = Array.from({ length: 7 }, (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() - i);
        return date.toISOString().split('T')[0];
      }).reverse();

      return last7Days.map(date => ({
        date,
        requests: Math.floor(Math.random() * 100) + 20, // TODO: Get real daily data
        successRate: 85 + Math.random() * 15,
        avgResponseTime: 1000 + Math.random() * 2000,
      }));
    }
  } catch (error) {
    console.warn('Failed to fetch usage history:', error);
  }

  return generateMockUsageHistory();
};

export const fetchTopAgents = async (): Promise<TopAgent[]> => {
  try {
    // Fetch agents and their metrics
    const [agentsResponse, metricsResponse] = await Promise.all([
      api.agents.list(),
      api.intelligence.getAgentMetrics()
    ]);

    if (agentsResponse.success && agentsResponse.data &&
        metricsResponse.success && metricsResponse.data) {

      const agents = agentsResponse.data;
      const metrics = metricsResponse.data;

      // Combine agent data with metrics
      const topAgents = agents.map(agent => {
        const agentMetrics = metrics.find(m => m.agent_id === agent.agent_id);
        return {
          id: agent.agent_id,
          name: agent.name,
          requests: agentMetrics?.execution_count || 0,
          successRate: agentMetrics ?
            (agentMetrics.success_count / agentMetrics.execution_count * 100) : 0,
          lastUsed: agentMetrics?.last_execution_time || agent.updated_at || agent.created_at,
        };
      })
      .filter(agent => agent.requests > 0)
      .sort((a, b) => b.requests - a.requests)
      .slice(0, 5);

      return topAgents;
    }
  } catch (error) {
    console.warn('Failed to fetch top agents:', error);
  }

  return generateMockTopAgents();
};



// Helper function to derive category from agent data
const deriveCategory = (agent: any): string => {
  if (agent.team_plan?.domain) {
    return agent.team_plan.domain;
  }

  // Derive from team name or description
  const name = agent.team_name?.toLowerCase() || '';
  const desc = agent.description?.toLowerCase() || '';

  if (name.includes('数据') || desc.includes('数据') || name.includes('分析') || desc.includes('分析')) {
    return '数据处理';
  }
  if (name.includes('文档') || desc.includes('文档') || name.includes('pdf') || desc.includes('word')) {
    return '文档处理';
  }
  if (name.includes('代码') || desc.includes('代码') || name.includes('开发') || desc.includes('编程')) {
    return '开发工具';
  }
  if (name.includes('客服') || desc.includes('客服') || name.includes('服务') || desc.includes('支持')) {
    return '客户服务';
  }

  return '通用工具';
};

// Helper function to calculate performance trend
const calculateTrend = (metrics: any): 'up' | 'down' | 'stable' => {
  if (!metrics) return 'stable';

  // Simple trend calculation based on success rate
  const successRate = metrics.success_count && metrics.execution_count
    ? (metrics.success_count / metrics.execution_count * 100)
    : 0;

  if (successRate >= 95) return 'up';
  if (successRate <= 80) return 'down';
  return 'stable';
};

// Helper function to calculate performance score
const calculateScore = (metrics: any): number => {
  if (!metrics) return 8.0;

  const successRate = metrics.success_count && metrics.execution_count
    ? (metrics.success_count / metrics.execution_count * 100)
    : 85;
  const responseTime = metrics.avg_response_time || 2000;

  // Score based on success rate (70%) and response time (30%)
  const successScore = (successRate / 100) * 7;
  const speedScore = Math.max(0, (5000 - responseTime) / 5000) * 3; // Better score for faster response

  return Math.round((successScore + speedScore) * 10) / 10;
};

// Fetch dashboard agents with metrics
export const fetchDashboardAgents = async (): Promise<DashboardAgentData[]> => {
  try {
    // Fetch agents and their metrics in parallel
    const [agentsResponse, metricsResponse] = await Promise.all([
      api.agents.list(
        { status: 'all', sortBy: 'last_used', sortOrder: 'desc' }, // Filter for dashboard
        { page: 1, size: 20 } // Limit to recent agents for dashboard
      ),
      api.intelligence.getAgentMetrics()
    ]);

    if (agentsResponse?.success && agentsResponse.data) {
      const agents = agentsResponse.data;
      const metrics = metricsResponse?.success ? metricsResponse.data : [];

      // Transform backend agent data to dashboard format
      const dashboardAgents: DashboardAgentData[] = agents.map((agent: any) => {
        const agentMetrics = metrics.find((m: any) => m.agent_id === agent.agent_id);

        // Calculate success rate
        const successRate = agentMetrics?.success_count && agentMetrics?.execution_count
          ? (agentMetrics.success_count / agentMetrics.execution_count * 100)
          : Math.random() * 20 + 80; // Fallback to reasonable range

        return {
          id: agent.agent_id,
          name: agent.team_name,
          description: agent.description,
          status: agent.status === 'active' ? 'active' :
                  agent.status === 'error' ? 'error' : 'inactive',
          lastUsed: agent.last_used || new Date().toISOString(),
          totalRuns: agentMetrics?.execution_count || agent.usage_count || 0,
          successRate: Math.round(successRate * 10) / 10,
          avgResponseTime: agentMetrics?.avg_response_time || 1500 + Math.random() * 1000,
          isFavorite: false, // Will be handled by useFavorites hook
          category: deriveCategory(agent),
          performance: {
            trend: calculateTrend(agentMetrics),
            score: calculateScore(agentMetrics)
          }
        };
      });

      // Sort by last used (most recent first) and return top 12 for dashboard
      return dashboardAgents
        .sort((a, b) => new Date(b.lastUsed).getTime() - new Date(a.lastUsed).getTime())
        .slice(0, 12);
    }
  } catch (error) {
    console.warn('Failed to fetch dashboard agents:', error);
  }

  // Return empty array on error - component will show "no agents" message
  return [];
};

export const fetchSystemHealth = async (): Promise<SystemHealth> => {
  try {
    // Fetch system health from intelligence metrics
    const response = await api.intelligence.getSystemMetrics();
    if (response.success && response.data) {
      const data = response.data;
      const totalAgents = data.total_agents || 0;
      const healthyAgents = data.healthy_agents || 0;
      const warningAgents = data.warning_agents || 0;
      const criticalAgents = data.critical_agents || 0;

      let status: 'healthy' | 'degraded' | 'down' = 'healthy';
      if (criticalAgents > 0 || (totalAgents > 0 && healthyAgents / totalAgents < 0.5)) {
        status = 'down';
      } else if (warningAgents > 0 || (totalAgents > 0 && healthyAgents / totalAgents < 0.8)) {
        status = 'degraded';
      }

      return {
        apiStatus: status,
        aiPlannerStatus: data.system_success_rate > 90 ? 'healthy' : 'degraded',
        codeGeneratorStatus: data.avg_response_time < 3000 ? 'healthy' : 'degraded',
        databaseStatus: 'healthy', // Assume healthy if we can fetch data
        uptime: 99.5 + Math.random() * 0.5,
        lastCheck: new Date().toISOString(),
      };
    }
  } catch (error) {
    console.warn('Failed to fetch system health:', error);
  }

  return generateMockSystemHealth();
};

export const fetchFullDashboardData = async (): Promise<DashboardData> => {
  try {
    // Try to fetch consolidated dashboard data first
    const dashboardResponse = await api.dashboard.getData();
    if (dashboardResponse.success && dashboardResponse.data) {
      const data = dashboardResponse.data;

      // Convert the consolidated data to the expected format
      return {
        usageHistory: generateMockUsageHistory(), // TODO: Add to backend
        topAgents: data.active_agents.map((agent: any) => ({
          id: agent.id,
          name: agent.name,
          description: agent.description,
          status: agent.status,
          lastUsed: agent.lastUsed,
          totalRuns: agent.usageCount || 0,
          successRate: agent.successRate || 95.0,
          avgResponseTime: agent.responseTime || 1200,
          isFavorite: false, // Will be updated by favorites hook
          category: '智能助手', // Default category
          performance: {
            trend: 'stable' as const,
            score: Math.min(9.5, (agent.successRate || 95) / 10)
          }
        })),
        systemHealth: data.system_health,
        recentActivity: data.recent_activity,
        quickActions: await fetchQuickActions(),
        featuredTemplates: data.featured_templates,
        userProfile: await fetchUserProfile(),
      };
    }
  } catch (error) {
    console.warn('Failed to fetch consolidated dashboard data, falling back to individual calls:', error);
  }

  try {
    // Fallback to individual API calls
    const [usageHistory, topAgents, dashboardAgents, systemHealth, recentActivity, quickActions, featuredTemplates, userProfile] = await Promise.all([
      fetchUsageHistory(),
      fetchTopAgents(),
      fetchDashboardAgents(),
      fetchSystemHealth(),
      fetchRecentActivity(),
      fetchQuickActions(),
      fetchFeaturedTemplates(),
      fetchUserProfile(),
    ]);

    return {
      usageHistory,
      topAgents,
      dashboardAgents,
      systemHealth,
      recentActivity,
      quickActions,
      featuredTemplates,
      userProfile,
    };
  } catch (error) {
    console.error('Failed to fetch dashboard data:', error);
    // Return mock data as fallback (excluding stats)
    return {
      usageHistory: generateMockUsageHistory(),
      topAgents: generateMockTopAgents(),
      dashboardAgents: [],
      systemHealth: generateMockSystemHealth(),
      recentActivity: generateMockRecentActivity(),
      quickActions: await fetchQuickActions(),
      featuredTemplates: generateMockFeaturedTemplates(),
      userProfile: generateMockUserProfile(),
    };
  }
};

// Custom hooks for dashboard data

export function useDashboardData() {
  const { user } = useAuth();

  return useQuery({
    queryKey: [DASHBOARD_CACHE_KEYS.fullDashboard, user?.id],
    queryFn: fetchFullDashboardData,
    ...CACHE_CONFIG,
    enabled: !!user,
  });
}

export function useRecentActivity() {
  const { user } = useAuth();

  return useQuery({
    queryKey: [DASHBOARD_CACHE_KEYS.recentActivity, user?.id],
    queryFn: fetchRecentActivity,
    ...CACHE_CONFIG,
    enabled: !!user,
  });
}

export function useQuickActions() {
  return useQuery({
    queryKey: [DASHBOARD_CACHE_KEYS.quickActions],
    queryFn: fetchQuickActions,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
  });
}

export function useFeaturedTemplates() {
  const { user } = useAuth();

  return useQuery({
    queryKey: [DASHBOARD_CACHE_KEYS.featuredTemplates, user?.id],
    queryFn: fetchFeaturedTemplates,
    ...CACHE_CONFIG,
    enabled: !!user,
  });
}

export function useUserProfile() {
  const { user } = useAuth();

  return useQuery({
    queryKey: [DASHBOARD_CACHE_KEYS.userProfile, user?.id],
    queryFn: fetchUserProfile,
    ...CACHE_CONFIG,
    enabled: !!user,
  });
}

export function useUsageHistory() {
  const { user } = useAuth();

  return useQuery({
    queryKey: [DASHBOARD_CACHE_KEYS.usageHistory, user?.id],
    queryFn: fetchUsageHistory,
    ...CACHE_CONFIG,
    enabled: !!user,
  });
}

export function useTopAgents() {
  const { user } = useAuth();

  return useQuery({
    queryKey: [DASHBOARD_CACHE_KEYS.topAgents, user?.id],
    queryFn: fetchTopAgents,
    ...CACHE_CONFIG,
    enabled: !!user,
  });
}

export function useDashboardAgents() {
  const { user } = useAuth();

  return useQuery({
    queryKey: [DASHBOARD_CACHE_KEYS.topAgents, 'dashboard', user?.id],
    queryFn: fetchDashboardAgents,
    ...CACHE_CONFIG,
    enabled: !!user,
  });
}

export function useSystemHealth() {
  const { user } = useAuth();

  return useQuery({
    queryKey: [DASHBOARD_CACHE_KEYS.systemHealth, user?.id],
    queryFn: fetchSystemHealth,
    ...CACHE_CONFIG,
    enabled: !!user,
  });
}

// Real-time updates hook
export function useRealTimeUpdates() {
  const queryClient = useQueryClient();
  const { user } = useAuth();
  
  const invalidateQueries = useCallback(() => {
    if (!user) return;

    // Invalidate all dashboard-related queries
    const cacheKeys = [
      DASHBOARD_CACHE_KEYS.fullDashboard,
      DASHBOARD_CACHE_KEYS.recentActivity,
      DASHBOARD_CACHE_KEYS.featuredTemplates,
      DASHBOARD_CACHE_KEYS.userProfile,
      DASHBOARD_CACHE_KEYS.usageHistory,
      DASHBOARD_CACHE_KEYS.topAgents,
      DASHBOARD_CACHE_KEYS.systemHealth,
    ];

    cacheKeys.forEach(key => {
      queryClient.invalidateQueries({
        queryKey: [key, user.id],
      });
    });

    // Also invalidate quick actions (no user ID)
    queryClient.invalidateQueries({
      queryKey: [DASHBOARD_CACHE_KEYS.quickActions],
    });
  }, [queryClient, user]);
  
  useEffect(() => {
    if (!user) return;
    
    // Set up real-time updates every 30 seconds
    const interval = setInterval(invalidateQueries, 30000);
    
    // Listen for visibility change to refresh when user returns
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        invalidateQueries();
      }
    };
    
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      clearInterval(interval);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [user, invalidateQueries]);
  
  return { invalidateQueries };
}



// Performance monitoring hook for dashboard
export function useDashboardPerformance() {
  const [loadTime, setLoadTime] = useState<number>(0);
  const [isOptimized, setIsOptimized] = useState<boolean>(true);

  useEffect(() => {
    const startTime = performance.now();

    // Monitor when dashboard is fully loaded
    const checkLoadComplete = () => {
      const endTime = performance.now();
      const totalTime = endTime - startTime;
      setLoadTime(totalTime);
      setIsOptimized(totalTime < 2000); // Consider optimized if loads in under 2s
    };

    // Use requestIdleCallback if available, otherwise setTimeout
    if ('requestIdleCallback' in window) {
      requestIdleCallback(checkLoadComplete);
    } else {
      setTimeout(checkLoadComplete, 100);
    }
  }, []);

  return { loadTime, isOptimized };
}

export function useDashboardStats() {
  const { user } = useAuth();

  return useQuery({
    queryKey: [DASHBOARD_CACHE_KEYS.quickStats, user?.id],
    queryFn: async () => {
      try {
        const response = await api.dashboard.getStats();
        if (response.success && response.data) {
          return response.data;
        }
      } catch (error) {
        console.warn('Failed to fetch dashboard stats from API:', error);
      }

      // Fallback to mock data
      return generateMockDashboardStats();
    },
    ...CACHE_CONFIG,
    enabled: !!user,
  });
}
