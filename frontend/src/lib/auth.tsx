"use client"

import React, { createContext, use<PERSON>ontext, useEffect, useState } from 'react'
import { initializeUserData, cleanupUserData } from './user-data'
import { apiClient, UserResponse, UserProfile } from './api-client'

export interface User {
  id: number
  uuid: string
  name: string
  email: string
  avatar?: string
  role: string
  status: string
  bio?: string
  timezone?: string
  language: string
  created_at: string
  updated_at?: string
  last_login_at?: string
  is_email_verified: boolean
  login_count: number
  // Extended profile fields
  preferences?: Record<string, any>
  last_activity_at?: string
  email_verified_at?: string
  password_changed_at?: string
}

export interface RegisterData {
  name: string
  email: string
  password: string
  confirmPassword: string
}

export interface ResetPasswordData {
  email: string
}

export interface ChangePasswordData {
  currentPassword: string
  newPassword: string
  confirmPassword: string
  totp_code?: string
  backup_code?: string
}

interface AuthContextType {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  login: (email: string, password: string, rememberMe?: boolean) => Promise<any>
  register: (data: RegisterData) => Promise<void>
  logout: () => void
  updateProfile: (data: Partial<User>) => Promise<void>
  changePassword: (data: ChangePasswordData) => Promise<void>
  resetPassword: (data: ResetPasswordData) => Promise<void>
  uploadAvatar: (file: File) => Promise<string>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: React.ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true) // Fixed: Start with true to properly handle auth initialization

  // Initialize default users and check for existing session on mount
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Check for existing session
        const token = localStorage.getItem('auth_token')
        const userData = localStorage.getItem('user_data')

        console.log('Auth initialization - token exists:', !!token, 'userData exists:', !!userData)

        if (token && userData) {
          // Validate token with backend by fetching current user
          console.log('Validating existing token...')
          const response = await apiClient.getCurrentUser()

          if (response.data) {
            console.log('Token validation successful, setting user data')
            // Convert backend user format to frontend User interface
            const user: User = {
              id: response.data.id,
              uuid: response.data.uuid,
              name: response.data.name,
              email: response.data.email,
              avatar: response.data.avatar,
              role: response.data.role,
              status: response.data.status,
              bio: response.data.bio,
              timezone: response.data.timezone,
              language: response.data.language,
              created_at: response.data.created_at,
              updated_at: response.data.updated_at,
              last_login_at: response.data.last_login_at,
              is_email_verified: response.data.is_email_verified,
              login_count: response.data.login_count,
              preferences: response.data.preferences,
              last_activity_at: response.data.last_activity_at,
              email_verified_at: response.data.email_verified_at,
              password_changed_at: response.data.password_changed_at,
            }

            // Update stored user data
            localStorage.setItem('user_data', JSON.stringify(user))
            setUser(user)
          } else {
            console.log('Token validation failed, clearing auth')
            // Token is invalid, clear auth
            localStorage.removeItem('auth_token')
            localStorage.removeItem('user_data')
            apiClient.clearAuth()
          }
        } else {
          console.log('No existing token or user data found')
        }
      } catch (error) {
        console.error('Auth initialization failed:', error)
        // Clear invalid auth data
        localStorage.removeItem('auth_token')
        localStorage.removeItem('user_data')
        apiClient.clearAuth()
      } finally {
        console.log('Auth initialization complete, setting isLoading to false')
        setIsLoading(false)
      }
    }

    initializeAuth()
  }, [])

  const login = async (email: string, password: string, rememberMe: boolean = false) => {
    setIsLoading(true)
    try {
      const response = await apiClient.login({
        email,
        password,
        remember_me: rememberMe
      })

      if (response.error) {
        throw new Error(response.error)
      }

      if (!response.data) {
        throw new Error('Login failed - no data received')
      }

      // Check if response indicates 2FA is required
      if ('requires_2fa' in response.data && response.data.requires_2fa) {
        // Return the 2FA requirement response for the component to handle
        return response.data
      }

      // Normal login flow - extract user data
      const { user: userData } = response.data

      // Convert backend user format to frontend User interface
      const user: User = {
        id: userData.id,
        uuid: userData.uuid,
        name: userData.name,
        email: userData.email,
        avatar: userData.avatar,
        role: userData.role,
        status: userData.status,
        bio: userData.bio,
        timezone: userData.timezone,
        language: userData.language,
        created_at: userData.created_at,
        updated_at: userData.updated_at,
        last_login_at: userData.last_login_at,
        is_email_verified: userData.is_email_verified,
        login_count: userData.login_count,
        preferences: userData.preferences || {},
        last_activity_at: userData.last_activity_at || null,
        email_verified_at: userData.email_verified_at || null,
        password_changed_at: userData.password_changed_at || null,
      }

      // Store user data
      localStorage.setItem('user_data', JSON.stringify(user))

      // Initialize user-specific data
      initializeUserData(user.id.toString())

      setUser(user)

      // Return success indicator for normal login
      return { success: true }
    } catch (error) {
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const register = async (data: RegisterData) => {
    setIsLoading(true)
    try {
      const response = await apiClient.register({
        name: data.name,
        email: data.email,
        password: data.password,
        confirm_password: data.confirmPassword,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        language: 'en'
      })

      if (response.error) {
        throw new Error(response.error)
      }

      if (!response.data) {
        throw new Error('Registration failed - no data received')
      }

      const { user: userData } = response.data

      // Convert backend user format to frontend User interface
      const user: User = {
        id: userData.id,
        uuid: userData.uuid,
        name: userData.name,
        email: userData.email,
        avatar: userData.avatar,
        role: userData.role,
        status: userData.status,
        bio: userData.bio,
        timezone: userData.timezone,
        language: userData.language,
        created_at: userData.created_at,
        updated_at: userData.updated_at,
        last_login_at: userData.last_login_at,
        is_email_verified: userData.is_email_verified,
        login_count: userData.login_count,
      }

      // Store user data
      localStorage.setItem('user_data', JSON.stringify(user))

      // Initialize user-specific data
      initializeUserData(user.id.toString())

      setUser(user)
    } catch (error) {
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async () => {
    try {
      // Call backend logout endpoint
      await apiClient.logout()
    } catch (error) {
      console.error('Logout error:', error)
      // Continue with local cleanup even if backend call fails
    }

    // Clean up user-specific data
    cleanupUserData()

    // Clear local storage
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user_data')

    // Clear API client auth
    apiClient.clearAuth()

    setUser(null)
  }

  const updateProfile = async (data: Partial<User>) => {
    if (!user) return

    try {
      const response = await apiClient.updateProfile(data)

      if (response.error) {
        throw new Error(response.error)
      }

      if (!response.data) {
        throw new Error('Profile update failed - no data received')
      }

      // Convert backend user format to frontend User interface
      const updatedUser: User = {
        id: response.data.id,
        uuid: response.data.uuid,
        name: response.data.name,
        email: response.data.email,
        avatar: response.data.avatar,
        role: response.data.role,
        status: response.data.status,
        bio: response.data.bio,
        timezone: response.data.timezone,
        language: response.data.language,
        created_at: response.data.created_at,
        updated_at: response.data.updated_at,
        last_login_at: response.data.last_login_at,
        is_email_verified: response.data.is_email_verified,
        login_count: response.data.login_count,
        preferences: response.data.preferences,
        last_activity_at: response.data.last_activity_at,
        email_verified_at: response.data.email_verified_at,
        password_changed_at: response.data.password_changed_at,
      }

      // Update stored user data
      localStorage.setItem('user_data', JSON.stringify(updatedUser))
      setUser(updatedUser)
    } catch (error) {
      throw error
    }
  }

  const changePassword = async (data: ChangePasswordData) => {
    if (!user) return

    try {
      const response = await apiClient.changePassword({
        current_password: data.currentPassword,
        new_password: data.newPassword,
        confirm_password: data.confirmPassword,
        totp_code: data.totp_code,
        backup_code: data.backup_code
      })

      if (response.error) {
        throw new Error(response.error)
      }

      // Password changed successfully
      // Note: The backend will invalidate other sessions
    } catch (error) {
      throw error
    }
  }

  const resetPassword = async (data: ResetPasswordData) => {
    try {
      const response = await apiClient.resetPassword({
        email: data.email
      })

      if (response.error) {
        throw new Error(response.error)
      }

      // Password reset email sent successfully
      // The backend will handle sending the email
    } catch (error) {
      throw error
    }
  }

  const uploadAvatar = async (file: File): Promise<string> => {
    try {
      const response = await apiClient.uploadAvatar(file)

      if (response.error) {
        throw new Error(response.error)
      }

      if (!response.data) {
        throw new Error('Avatar upload failed - no data received')
      }

      // Update user avatar in local state
      if (user) {
        const updatedUser = { ...user, avatar: response.data.avatar_url }
        localStorage.setItem('user_data', JSON.stringify(updatedUser))
        setUser(updatedUser)
      }

      return response.data.avatar_url
    } catch (error) {
      throw error
    }
  }

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated: !!user,
    login,
    register,
    logout,
    updateProfile,
    changePassword,
    resetPassword,
    uploadAvatar,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
