/**
 * API client for backend communication
 */

// Use relative path to leverage Next.js proxy instead of direct backend connection
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || ''

// Debug: Log the API base URL
console.log('API_BASE_URL:', API_BASE_URL, 'NEXT_PUBLIC_API_URL:', process.env.NEXT_PUBLIC_API_URL)

export interface ApiResponse<T> {
  data?: T
  error?: string
  message?: string
}

export interface LoginRequest {
  email: string
  password: string
  remember_me?: boolean
}

export interface RegisterRequest {
  name: string
  email: string
  password: string
  confirm_password: string
  timezone?: string
  language?: string
}

export interface ChangePasswordRequest {
  current_password: string
  new_password: string
  confirm_password: string
  totp_code?: string
  backup_code?: string
}

export interface ResetPasswordRequest {
  email: string
}

export interface UserResponse {
  id: number
  uuid: string
  name: string
  email: string
  role: string
  status: string
  avatar?: string
  bio?: string
  timezone?: string
  language: string
  last_login_at?: string
  is_email_verified: boolean
  login_count: number
  created_at: string
  updated_at?: string
}

export interface AuthTokens {
  access_token: string
  token_type: string
  expires_in: number
  refresh_token?: string
}

export interface LoginResponse {
  user: UserResponse
  tokens: AuthTokens
  session_id: string
}

export interface UserProfile extends UserResponse {
  preferences?: Record<string, any>
  last_activity_at?: string
  email_verified_at?: string
  password_changed_at?: string
}

class ApiClient {
  private baseUrl: string
  private token: string | null = null

  // Public getters for accessing private properties
  get baseURL(): string {
    return this.baseUrl
  }

  get authToken(): string | null {
    return this.token
  }

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl
    this.token = this.getStoredToken()
  }

  private getStoredToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('auth_token')
    }
    return null
  }

  private setStoredToken(token: string | null) {
    if (typeof window !== 'undefined') {
      if (token) {
        localStorage.setItem('auth_token', token)
      } else {
        localStorage.removeItem('auth_token')
      }
    }
    this.token = token
  }

  // Public method to set auth token (for 2FA flow)
  setAuthToken(token: string | null) {
    this.setStoredToken(token)
  }

  async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    }

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`
    }

    try {
      console.log('API Request:', { url, method: options.method || 'GET', headers })

      const response = await fetch(url, {
        ...options,
        headers,
      })

      console.log('API Response:', { status: response.status, statusText: response.statusText })

      const data = await response.json()
      console.log('API Response Data:', data)

      if (!response.ok) {
        // Handle authentication/authorization errors
        if (response.status === 401) {
          // Clear stored token and redirect to login
          this.setStoredToken(null)
          if (typeof window !== 'undefined') {
            window.location.href = '/login'
          }
          return {
            error: 'Authentication required. Please log in again.',
          }
        } else if (response.status === 403) {
          return {
            error: 'Access denied. You do not have permission to access this resource.',
          }
        }

        return {
          error: data.detail || data.message || `HTTP ${response.status}`,
        }
      }

      return { data }
    } catch (error) {
      console.error('API request failed:', error)
      return {
        error: error instanceof Error ? error.message : 'Network error',
      }
    }
  }

  // Authentication endpoints
  async login(credentials: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    const response = await this.request<LoginResponse>('/api/v1/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    })

    // Only set token if this is a complete login response (not 2FA required)
    if (response.data && 'tokens' in response.data) {
      this.setStoredToken(response.data.tokens.access_token)
    }

    return response
  }

  async register(userData: RegisterRequest): Promise<ApiResponse<LoginResponse>> {
    const response = await this.request<LoginResponse>('/api/v1/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    })

    if (response.data) {
      this.setStoredToken(response.data.tokens.access_token)
    }

    return response
  }

  async logout(): Promise<ApiResponse<{ message: string }>> {
    const response = await this.request<{ message: string }>('/api/v1/auth/logout', {
      method: 'POST',
    })

    this.setStoredToken(null)
    return response
  }

  async getCurrentUser(): Promise<ApiResponse<UserProfile>> {
    return this.request<UserProfile>('/api/v1/auth/me')
  }

  async updateProfile(updates: Partial<UserProfile>): Promise<ApiResponse<UserProfile>> {
    return this.request<UserProfile>('/api/v1/auth/me', {
      method: 'PUT',
      body: JSON.stringify(updates),
    })
  }

  async changePassword(passwordData: ChangePasswordRequest): Promise<ApiResponse<{ message: string }>> {
    return this.request<{ message: string }>('/api/v1/auth/change-password', {
      method: 'PUT',
      body: JSON.stringify(passwordData),
    })
  }

  async resetPassword(resetData: ResetPasswordRequest): Promise<ApiResponse<{ message: string }>> {
    return this.request<{ message: string }>('/api/v1/auth/reset-password', {
      method: 'POST',
      body: JSON.stringify(resetData),
    })
  }

  async uploadAvatar(file: File): Promise<ApiResponse<{ avatar_url: string }>> {
    const formData = new FormData()
    formData.append('file', file)

    const url = `${this.baseUrl}/api/v1/auth/upload-avatar`
    
    const headers: HeadersInit = {}
    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`
    }

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: formData,
      })

      const data = await response.json()

      if (!response.ok) {
        // Handle authentication/authorization errors
        if (response.status === 401) {
          this.setStoredToken(null)
          if (typeof window !== 'undefined') {
            window.location.href = '/login'
          }
          return {
            error: 'Authentication required. Please log in again.',
          }
        } else if (response.status === 403) {
          return {
            error: 'Access denied. You do not have permission to perform this action.',
          }
        }

        return {
          error: data.detail || data.message || `HTTP ${response.status}`,
        }
      }

      return { data }
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Avatar upload failed:', error)
      }
      return {
        error: error instanceof Error ? error.message : 'Upload failed',
      }
    }
  }

  // Utility methods
  isAuthenticated(): boolean {
    return !!this.token
  }

  clearAuth() {
    this.setStoredToken(null)
  }
}

// Export singleton instance
export const apiClient = new ApiClient()
export default apiClient
