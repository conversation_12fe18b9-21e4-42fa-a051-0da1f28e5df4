import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { formatDistanceToNow } from "date-fns"
import { zhCN } from "date-fns/locale"
import { Agent, Template } from "./types"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Safely format a date string to relative time
 * @param dateString - The date string to format
 * @param fallback - The fallback text if date is invalid (default: "未知")
 * @returns Formatted relative time string or fallback
 */
export function formatRelativeTime(dateString: string | null | undefined, fallback: string = "未知"): string {
  if (!dateString) return fallback;

  const date = new Date(dateString);
  if (isNaN(date.getTime())) return fallback;

  return formatDistanceToNow(date, {
    addSuffix: true,
    locale: zhCN
  });
}

/**
 * Safely get specialists from an agent object
 * @param agent - The agent object
 * @returns Array of specialists or empty array if none found
 */
export function getAgentSpecialists(agent: any): Array<{ name: string; role: string }> {
  // Priority 1: Use team_members from database (most complete data)
  if (agent?.team_members && Array.isArray(agent.team_members)) {
    return agent.team_members.map((member: any) => ({
      name: member.name || 'Unknown',
      role: member.role || 'Member'
    }));
  }

  // Priority 2: Use specialists field (backward compatibility)
  if (agent?.specialists && Array.isArray(agent.specialists)) {
    return agent.specialists;
  }

  // Priority 3: Try team_plan.team_members
  if (agent?.team_plan?.team_members && Array.isArray(agent.team_plan.team_members)) {
    return agent.team_plan.team_members.map((member: any) => ({
      name: member.name || 'Unknown',
      role: member.role || 'Member'
    }));
  }

  // Priority 4: Try team_plan.specialists (legacy)
  if (agent?.team_plan?.specialists && Array.isArray(agent.team_plan.specialists)) {
    return agent.team_plan.specialists;
  }

  return [];
}

/**
 * Get detailed team members with full information
 * @param agent - The agent object
 * @returns Array of team members with description and system_prompt
 */
export function getAgentTeamMembers(agent: any): Array<{
  name: string;
  role: string;
  description?: string;
  system_prompt?: string;
}> {
  // Priority 1: Use team_members from database (most complete data)
  if (agent?.team_members && Array.isArray(agent.team_members)) {
    return agent.team_members;
  }

  // Priority 2: Try team_plan.team_members
  if (agent?.team_plan?.team_members && Array.isArray(agent.team_plan.team_members)) {
    return agent.team_plan.team_members;
  }

  // Fallback: Convert specialists to team members format
  const specialists = getAgentSpecialists(agent);
  return specialists.map(specialist => ({
    name: specialist.name,
    role: specialist.role,
    description: undefined,
    system_prompt: undefined
  }));
}

/**
 * Get agent workflow steps
 * @param agent - The agent object
 * @returns Array of workflow steps
 */
export function getAgentWorkflowSteps(agent: any): Array<{
  name: string;
  description?: string;
  assignee: string;
  inputs?: string[];
  outputs?: string[];
  dependencies?: string[];
}> {
  // Try to get workflow steps from team_plan
  if (agent?.team_plan?.workflow?.steps && Array.isArray(agent.team_plan.workflow.steps)) {
    return agent.team_plan.workflow.steps;
  }

  return [];
}

/**
 * Generate agent API endpoint URL
 * @param agentId - The agent ID
 * @param baseUrl - The base URL for agent APIs (optional, will use default if not provided)
 * @returns Complete API endpoint URL for the agent
 */
export function getAgentApiEndpoint(agentId: string, baseUrl?: string): string {
  const defaultBaseUrl = `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000"}/api/v1/agents`;
  const apiBaseUrl = baseUrl || defaultBaseUrl;

  // Ensure base URL doesn't end with slash
  const cleanBaseUrl = apiBaseUrl.replace(/\/$/, '');

  return `${cleanBaseUrl}/${agentId}/execute`;
}

/**
 * Safely format a number for display
 * @param value - The value to format
 * @param fallback - The fallback value if invalid (default: 0)
 * @returns Valid number or fallback
 */
export function safeNumber(value: any, fallback: number = 0): number {
  if (typeof value === 'number' && !isNaN(value)) {
    return value;
  }
  return fallback;
}

/**
 * Transform agent data into template form data for pre-population
 */
export function transformAgentToTemplateFormData(agent: Agent): Partial<Template> {
  // Create enhanced description for template
  const enhancedDescription = agent.description
    ? `${agent.description}\n\n这是一个基于 "${agent.team_name}" 团队的模板，可以用于类似的任务场景。模板包含完整的团队配置和工作流程，可以直接部署使用。`
    : `基于 "${agent.team_name}" 团队创建的AI模板，包含完整的团队配置和工作流程，适用于相关业务场景的自动化处理。`;

  // Create comprehensive prompt template
  const promptTemplate = agent.team_plan?.objective ||
                        agent.prompt_template ||
                        agent.description ||
                        `这是一个基于 "${agent.team_name}" 的AI团队模板。

团队目标：${agent.description || '解决复杂的业务问题'}

主要功能：
- 分析和理解用户需求
- 协调团队成员完成任务
- 提供高质量的解决方案

使用方法：
1. 描述您的具体需求
2. 团队将自动分析并制定执行计划
3. 各成员协作完成任务
4. 输出最终结果和建议

请详细描述您的需求，团队将为您提供专业的解决方案。`;

  // Map agent domain to valid template categories
  const domainToCategoryMap: Record<string, string> = {
    'creative': 'creative',
    'writing': 'creative',
    'content': 'creative',
    'analysis': 'data_analysis',
    'data': 'data_analysis',
    'analytics': 'data_analysis',
    'business': 'business',
    'strategy': 'business',
    'marketing': 'business',
    'product': 'product_development',
    'development': 'product_development',
    'tech': 'product_development',
    'education': 'education',
    'teaching': 'education',
    'learning': 'education',
    'legal': 'legal',
    'law': 'legal',
    'compliance': 'legal',
    'medical': 'medical',
    'health': 'medical',
    'healthcare': 'medical',
    'general': 'business'
  };

  const agentDomain = agent.metadata?.domain?.toLowerCase() || 'general';
  const templateCategory = domainToCategoryMap[agentDomain] || 'business';

  // Map complexity to difficulty
  const complexityToDifficultyMap: Record<string, string> = {
    'simple': 'beginner',
    'basic': 'beginner',
    'easy': 'beginner',
    'intermediate': 'intermediate',
    'medium': 'intermediate',
    'advanced': 'advanced',
    'complex': 'advanced',
    'expert': 'advanced'
  };

  const agentComplexity = agent.metadata?.complexity?.toLowerCase() || 'intermediate';
  const templateDifficulty = complexityToDifficultyMap[agentComplexity] || 'intermediate';

  // Create a basic team structure template from agent data
  const teamStructureTemplate = {
    team_name: agent.team_name,
    description: agent.description,
    objective: agent.team_plan?.objective || agent.description,
    domain: templateCategory,
    complexity: templateDifficulty,
    team_members: agent.team_members || agent.specialists?.map(s => ({
      name: s.name,
      role: s.role,
      description: "",
      system_prompt: ""
    })) || [],
    workflow: agent.team_plan?.workflow || {
      steps: [
        {
          name: "分析需求",
          description: "理解和分析用户需求",
          assignee: agent.team_members?.[0]?.name || "团队负责人",
          inputs: ["用户输入"],
          outputs: ["需求分析结果"]
        },
        {
          name: "执行任务",
          description: "根据需求执行相应任务",
          assignee: agent.team_members?.[1]?.name || "执行专家",
          inputs: ["需求分析结果"],
          outputs: ["任务执行结果"]
        },
        {
          name: "总结输出",
          description: "整理和总结最终结果",
          assignee: agent.team_members?.[2]?.name || "总结专家",
          inputs: ["任务执行结果"],
          outputs: ["最终结果"]
        }
      ]
    },
    configuration: {
      execution_mode: "sequential",
      timeout_per_step: 300,
      max_iterations: 3,
      error_handling: "graceful_degradation"
    }
  };

  return {
    name: `${agent.team_name}团队模板`,
    description: enhancedDescription,
    category: templateCategory,
    difficulty: templateDifficulty,
    prompt_template: promptTemplate,
    team_structure_template: teamStructureTemplate,
    visibility: "private",
    status: "active",
    tags: [],
    keywords: [],
    use_case: enhancedDescription
  };
}
