/**
 * Centralized error handling utilities
 */

export interface ApiError {
  message: string;
  code?: string;
  status?: number;
  details?: any;
}

export interface ErrorHandlerOptions {
  showToast?: boolean;
  logError?: boolean;
  fallbackMessage?: string;
  onError?: (error: ApiError) => void;
}

/**
 * Standardized error handler for API responses
 */
export function handleApiError(
  error: any,
  options: ErrorHandlerOptions = {}
): ApiError {
  const {
    showToast = true,
    logError = true,
    fallbackMessage = "操作失败，请稍后重试",
    onError
  } = options;

  let apiError: ApiError;

  // Handle different error types
  if (error?.response) {
    // HTTP error response
    apiError = {
      message: error.response.data?.message || error.response.data?.detail || fallbackMessage,
      code: error.response.data?.code,
      status: error.response.status,
      details: error.response.data
    };
  } else if (error?.message) {
    // JavaScript Error object
    apiError = {
      message: error.message,
      details: error
    };
  } else if (typeof error === 'string') {
    // String error
    apiError = {
      message: error
    };
  } else {
    // Unknown error type
    apiError = {
      message: fallbackMessage,
      details: error
    };
  }

  // Log error if enabled
  if (logError) {
    console.error('API Error:', apiError);
  }

  // Show toast notification if enabled
  if (showToast && typeof window !== 'undefined') {
    // You can integrate with your toast library here
    // For now, we'll just log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.warn('Toast notification:', apiError.message);
    }
  }

  // Call custom error handler if provided
  if (onError) {
    onError(apiError);
  }

  return apiError;
}

/**
 * Wrapper for async operations with standardized error handling
 */
export async function withErrorHandling<T>(
  operation: () => Promise<T>,
  options: ErrorHandlerOptions = {}
): Promise<{ data?: T; error?: ApiError }> {
  try {
    const data = await operation();
    return { data };
  } catch (error) {
    const apiError = handleApiError(error, options);
    return { error: apiError };
  }
}

/**
 * Hook for handling loading states and errors
 */
export function useAsyncOperation<T>() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<ApiError | null>(null);
  const [data, setData] = useState<T | null>(null);

  const execute = async (
    operation: () => Promise<T>,
    options: ErrorHandlerOptions = {}
  ) => {
    setLoading(true);
    setError(null);

    const result = await withErrorHandling(operation, {
      ...options,
      onError: (err) => {
        setError(err);
        options.onError?.(err);
      }
    });

    if (result.data !== undefined) {
      setData(result.data);
    }

    setLoading(false);
    return result;
  };

  const reset = () => {
    setLoading(false);
    setError(null);
    setData(null);
  };

  return {
    loading,
    error,
    data,
    execute,
    reset
  };
}

/**
 * Validation error handler
 */
export function handleValidationError(
  errors: Record<string, string[]>,
  options: ErrorHandlerOptions = {}
): ApiError {
  const errorMessages = Object.entries(errors)
    .map(([field, messages]) => `${field}: ${messages.join(', ')}`)
    .join('; ');

  return handleApiError(
    { message: `验证失败: ${errorMessages}` },
    options
  );
}

/**
 * Network error handler
 */
export function handleNetworkError(
  error: any,
  options: ErrorHandlerOptions = {}
): ApiError {
  if (!navigator.onLine) {
    return handleApiError(
      { message: "网络连接已断开，请检查您的网络设置" },
      options
    );
  }

  if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error')) {
    return handleApiError(
      { message: "网络请求失败，请检查网络连接" },
      options
    );
  }

  return handleApiError(error, options);
}

/**
 * Timeout error handler
 */
export function handleTimeoutError(
  options: ErrorHandlerOptions = {}
): ApiError {
  return handleApiError(
    { message: "请求超时，请稍后重试" },
    options
  );
}

/**
 * Authentication error handler
 */
export function handleAuthError(
  error: any,
  options: ErrorHandlerOptions = {}
): ApiError {
  const apiError = handleApiError(error, {
    ...options,
    fallbackMessage: "身份验证失败，请重新登录"
  });

  // Redirect to login if needed
  if (apiError.status === 401 && typeof window !== 'undefined') {
    // You can add redirect logic here
    if (process.env.NODE_ENV === 'development') {
      console.warn('Authentication required, should redirect to login');
    }
  }

  return apiError;
}

/**
 * Permission error handler
 */
export function handlePermissionError(
  error: any,
  options: ErrorHandlerOptions = {}
): ApiError {
  return handleApiError(error, {
    ...options,
    fallbackMessage: "您没有权限执行此操作"
  });
}

/**
 * Rate limit error handler
 */
export function handleRateLimitError(
  error: any,
  options: ErrorHandlerOptions = {}
): ApiError {
  return handleApiError(error, {
    ...options,
    fallbackMessage: "请求过于频繁，请稍后再试"
  });
}

/**
 * Server error handler
 */
export function handleServerError(
  error: any,
  options: ErrorHandlerOptions = {}
): ApiError {
  return handleApiError(error, {
    ...options,
    fallbackMessage: "服务器错误，请稍后重试"
  });
}

// Import React hooks
import { useState } from 'react';
