/**
 * Dashboard Styling Utilities
 * Centralized styling system for consistent visual polish across dashboard components
 */

import { cn } from '@/lib/utils';

// Utility functions for combining styles (defined early to avoid hoisting issues)
export const combineStyles = (...styles: (string | undefined | false)[]): string => {
  return cn(...styles.filter(Boolean));
};

// Enhanced card styling with depth and polish (theme-aware)
export const cardStyles = {
  base: 'border border-border/60 bg-card/80 backdrop-blur-sm shadow-sm hover:shadow-md transition-all duration-300 dark:shadow-black/20',
  elevated: 'border border-border/40 bg-card/90 backdrop-blur-md shadow-md hover:shadow-lg transition-all duration-300 dark:shadow-black/30',
  interactive: 'border border-border/60 bg-card/80 backdrop-blur-sm shadow-sm hover:shadow-md hover:border-border/80 transition-all duration-300 cursor-pointer dark:shadow-black/20',
  gradient: 'border border-border/40 bg-gradient-to-br from-card/90 to-card/70 backdrop-blur-md shadow-md hover:shadow-lg transition-all duration-300 dark:shadow-black/30',
};

// Typography system for consistent text styling
export const typography = {
  // Headings
  h1: 'text-2xl md:text-3xl font-bold tracking-tight text-foreground/95',
  h2: 'text-xl md:text-2xl font-semibold tracking-tight text-foreground/90',
  h3: 'text-lg md:text-xl font-semibold text-foreground/85',
  h4: 'text-base md:text-lg font-medium text-foreground/80',
  
  // Body text
  body: 'text-sm md:text-base text-foreground/75 leading-relaxed',
  bodySmall: 'text-xs md:text-sm text-foreground/70 leading-relaxed',
  caption: 'text-xs text-muted-foreground leading-normal',
  
  // Interactive text
  link: 'text-primary hover:text-primary/80 transition-colors duration-200',
  button: 'text-sm font-medium leading-none',
  
  // Status text
  success: 'text-green-600 dark:text-green-400 font-medium',
  warning: 'text-yellow-600 dark:text-yellow-400 font-medium',
  error: 'text-red-600 dark:text-red-400 font-medium',
  info: 'text-blue-600 dark:text-blue-400 font-medium',
};

// Spacing system for consistent layout
export const spacing = {
  // Card padding
  cardPadding: 'p-3 md:p-4',
  cardPaddingCompact: 'p-2 md:p-3',
  cardHeader: 'p-3 pb-2 md:p-4 md:pb-3',
  cardContent: 'p-3 pt-0 md:p-4 md:pt-0',
  
  // Section spacing
  sectionGap: 'space-y-3 md:space-y-4',
  itemGap: 'space-y-2 md:space-y-3',
  inlineGap: 'gap-2 md:gap-3',
  
  // Grid spacing
  gridGap: 'gap-2 md:gap-3',
  gridGapLarge: 'gap-3 md:gap-4',
};

// Button styling system
export const buttonStyles = {
  // Base styles
  base: 'inline-flex items-center justify-center rounded-md text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',

  // Touch-friendly sizing
  touchFriendly: 'min-h-[44px] min-w-[44px] touch-manipulation',
  touchFriendlyLarge: 'min-h-[48px] min-w-[48px] touch-manipulation',

  // Enhanced hover states
  primaryHover: 'bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-[1.02] active:scale-[0.98]',
  secondaryHover: 'bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:scale-[1.02] active:scale-[0.98]',
  outlineHover: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98]',
  ghostHover: 'hover:bg-accent hover:text-accent-foreground hover:scale-[1.05] active:scale-[0.95]',

  // Status-specific styles
  success: 'bg-green-600 text-white hover:bg-green-700 hover:scale-[1.02] active:scale-[0.98]',
  warning: 'bg-yellow-600 text-white hover:bg-yellow-700 hover:scale-[1.02] active:scale-[0.98]',
  error: 'bg-red-600 text-white hover:bg-red-700 hover:scale-[1.02] active:scale-[0.98]',

  // Quick Action Card specific styles
  quickActionCard: 'relative w-full h-auto min-h-[64px] p-4 flex flex-col items-center justify-center gap-3 border-2 border-border/40 rounded-xl bg-card/60 backdrop-blur-sm shadow-sm hover:shadow-lg hover:shadow-primary/10 transition-all duration-300 ease-out transform-gpu hover:scale-[1.02] hover:-translate-y-1 hover:border-border/60 active:scale-[0.98] active:translate-y-0 focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 group overflow-hidden cursor-pointer',

  // Quick Action disabled state
  quickActionDisabled: 'opacity-50 cursor-not-allowed pointer-events-none hover:scale-100 hover:translate-y-0 hover:shadow-sm',
};

// Icon styling system
export const iconStyles = {
  // Standard sizes
  xs: 'h-3 w-3',
  sm: 'h-4 w-4',
  md: 'h-5 w-5',
  lg: 'h-6 w-6',
  xl: 'h-8 w-8',
  
  // Status colors
  success: 'text-green-600 dark:text-green-400',
  warning: 'text-yellow-600 dark:text-yellow-400',
  error: 'text-red-600 dark:text-red-400',
  info: 'text-blue-600 dark:text-blue-400',
  muted: 'text-muted-foreground',
  
  // Interactive states
  interactive: 'transition-colors duration-200 hover:text-foreground/80',
  button: 'transition-all duration-200 group-hover:scale-110',
};

// Animation utilities
export const animations = {
  // Entrance animations
  fadeIn: 'animate-in fade-in duration-300',
  slideIn: 'animate-in slide-in-from-bottom-4 duration-300',
  scaleIn: 'animate-in zoom-in-95 duration-200',
  
  // Hover animations
  hoverScale: 'transition-transform duration-200 hover:scale-[1.02] active:scale-[0.98]',
  hoverScaleSmall: 'transition-transform duration-200 hover:scale-[1.05] active:scale-[0.95]',
  hoverLift: 'transition-all duration-200 hover:-translate-y-0.5 hover:shadow-lg',
  
  // Loading animations
  pulse: 'animate-pulse',
  spin: 'animate-spin',
  bounce: 'animate-bounce',
  
  // Micro-interactions
  wiggle: 'transition-transform duration-200 hover:rotate-1',
  glow: 'transition-all duration-300 hover:shadow-lg hover:shadow-primary/20',

  // Quick Action specific animations
  quickActionHover: 'transition-all duration-300 ease-out transform-gpu hover:scale-[1.02] hover:-translate-y-1 active:scale-[0.98] active:translate-y-0 active:duration-100',

  quickActionIcon: 'transition-all duration-300 ease-out transform-gpu group-hover:scale-110 group-hover:rotate-3 group-active:scale-95 group-active:rotate-0',

  quickActionRipple: 'before:absolute before:inset-0 before:rounded-xl before:opacity-0 before:bg-gradient-to-br before:from-white/20 before:to-transparent before:transition-opacity before:duration-300 hover:before:opacity-100 active:before:opacity-50',
};

// Status indicator styles
export const statusStyles = {
  success: {
    bg: 'bg-green-50 dark:bg-green-950/20',
    border: 'border-green-200 dark:border-green-800',
    text: 'text-green-700 dark:text-green-300',
    icon: 'text-green-600 dark:text-green-400',
  },
  warning: {
    bg: 'bg-yellow-50 dark:bg-yellow-950/20',
    border: 'border-yellow-200 dark:border-yellow-800',
    text: 'text-yellow-700 dark:text-yellow-300',
    icon: 'text-yellow-600 dark:text-yellow-400',
  },
  error: {
    bg: 'bg-red-50 dark:bg-red-950/20',
    border: 'border-red-200 dark:border-red-800',
    text: 'text-red-700 dark:text-red-300',
    icon: 'text-red-600 dark:text-red-400',
  },
  info: {
    bg: 'bg-blue-50 dark:bg-blue-950/20',
    border: 'border-blue-200 dark:border-blue-800',
    text: 'text-blue-700 dark:text-blue-300',
    icon: 'text-blue-600 dark:text-blue-400',
  },
  neutral: {
    bg: 'bg-muted/50',
    border: 'border-border',
    text: 'text-muted-foreground',
    icon: 'text-muted-foreground',
  },
};

// Gradient utilities (theme-aware)
export const gradients = {
  primary: 'bg-gradient-to-br from-primary/10 to-primary/5 dark:from-primary/20 dark:to-primary/10',
  success: 'bg-gradient-to-br from-green-500/10 to-green-600/5 dark:from-green-400/15 dark:to-green-500/8',
  warning: 'bg-gradient-to-br from-yellow-500/10 to-yellow-600/5 dark:from-yellow-400/15 dark:to-yellow-500/8',
  error: 'bg-gradient-to-br from-red-500/10 to-red-600/5 dark:from-red-400/15 dark:to-red-500/8',
  info: 'bg-gradient-to-br from-blue-500/10 to-blue-600/5 dark:from-blue-400/15 dark:to-blue-500/8',
  neutral: 'bg-gradient-to-br from-muted/20 to-muted/10 dark:from-muted/30 dark:to-muted/15',
  card: 'bg-gradient-to-br from-card/95 to-card/85 dark:from-card/90 dark:to-card/75',
  subtle: 'bg-gradient-to-br from-background/50 to-muted/20 dark:from-background/60 dark:to-muted/25',
};

// Utility functions for combining styles (moved to top of file)

export const createCardStyle = (variant: keyof typeof cardStyles = 'base', additional?: string) => {
  return combineStyles(cardStyles[variant], additional);
};

export const createButtonStyle = (variant: keyof typeof buttonStyles, size: 'sm' | 'md' | 'lg' = 'md', additional?: string) => {
  const sizeClass = size === 'sm' ? 'h-8 px-3' : size === 'lg' ? 'h-12 px-6' : 'h-10 px-4';
  return combineStyles(buttonStyles.base, buttonStyles[variant], sizeClass, additional);
};

export const createStatusStyle = (status: keyof typeof statusStyles, element: 'bg' | 'border' | 'text' | 'icon' = 'text') => {
  return statusStyles[status][element];
};

// Quick Action Card Color Themes
export const quickActionThemes = {
  primary: {
    base: 'bg-blue-50/80 border-blue-200/60 text-blue-700 dark:bg-blue-950/30 dark:border-blue-800/40 dark:text-blue-300',
    hover: 'hover:bg-blue-100/90 hover:border-blue-300/80 hover:text-blue-800 hover:shadow-blue-500/20 dark:hover:bg-blue-900/40 dark:hover:border-blue-700/60 dark:hover:text-blue-200 dark:hover:shadow-blue-400/20',
    icon: 'text-blue-600 dark:text-blue-400 group-hover:text-blue-700 dark:group-hover:text-blue-300',
    gradient: 'before:bg-gradient-to-br before:from-blue-500/10 before:to-blue-600/5'
  },

  secondary: {
    base: 'bg-gray-50/80 border-gray-200/60 text-gray-700 dark:bg-gray-950/30 dark:border-gray-800/40 dark:text-gray-300',
    hover: 'hover:bg-gray-100/90 hover:border-gray-300/80 hover:text-gray-800 hover:shadow-gray-500/20 dark:hover:bg-gray-900/40 dark:hover:border-gray-700/60 dark:hover:text-gray-200 dark:hover:shadow-gray-400/20',
    icon: 'text-gray-600 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300',
    gradient: 'before:bg-gradient-to-br before:from-gray-500/10 before:to-gray-600/5'
  },

  success: {
    base: 'bg-green-50/80 border-green-200/60 text-green-700 dark:bg-green-950/30 dark:border-green-800/40 dark:text-green-300',
    hover: 'hover:bg-green-100/90 hover:border-green-300/80 hover:text-green-800 hover:shadow-green-500/20 dark:hover:bg-green-900/40 dark:hover:border-green-700/60 dark:hover:text-green-200 dark:hover:shadow-green-400/20',
    icon: 'text-green-600 dark:text-green-400 group-hover:text-green-700 dark:group-hover:text-green-300',
    gradient: 'before:bg-gradient-to-br before:from-green-500/10 before:to-green-600/5'
  },

  warning: {
    base: 'bg-yellow-50/80 border-yellow-200/60 text-yellow-700 dark:bg-yellow-950/30 dark:border-yellow-800/40 dark:text-yellow-300',
    hover: 'hover:bg-yellow-100/90 hover:border-yellow-300/80 hover:text-yellow-800 hover:shadow-yellow-500/20 dark:hover:bg-yellow-900/40 dark:hover:border-yellow-700/60 dark:hover:text-yellow-200 dark:hover:shadow-yellow-400/20',
    icon: 'text-yellow-600 dark:text-yellow-400 group-hover:text-yellow-700 dark:group-hover:text-yellow-300',
    gradient: 'before:bg-gradient-to-br before:from-yellow-500/10 before:to-yellow-600/5'
  },

  error: {
    base: 'bg-red-50/80 border-red-200/60 text-red-700 dark:bg-red-950/30 dark:border-red-800/40 dark:text-red-300',
    hover: 'hover:bg-red-100/90 hover:border-red-300/80 hover:text-red-800 hover:shadow-red-500/20 dark:hover:bg-red-900/40 dark:hover:border-red-700/60 dark:hover:text-red-200 dark:hover:shadow-red-400/20',
    icon: 'text-red-600 dark:text-red-400 group-hover:text-red-700 dark:group-hover:text-red-300',
    gradient: 'before:bg-gradient-to-br before:from-red-500/10 before:to-red-600/5'
  }
};

// Theme-aware accessibility utilities
export const accessibilityStyles = {
  // High contrast mode support
  highContrast: 'contrast-more:border-2 contrast-more:border-foreground contrast-more:bg-background contrast-more:text-foreground',

  // Focus indicators that work in both themes
  focusRing: 'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 focus-visible:ring-offset-background',

  // Screen reader friendly text
  srOnly: 'sr-only',

  // Reduced motion support
  reducedMotion: 'motion-reduce:transition-none motion-reduce:animation-none',

  // Color blind friendly indicators
  colorBlindSafe: 'underline decoration-2 decoration-dotted',
};

// Theme detection utilities
export const themeAwareStyles = {
  // Text that adapts to theme
  adaptiveText: 'text-foreground/90 dark:text-foreground/95',
  adaptiveTextMuted: 'text-muted-foreground dark:text-muted-foreground/90',

  // Borders that work in both themes
  adaptiveBorder: 'border-border/60 dark:border-border/40',

  // Backgrounds with proper contrast
  adaptiveBackground: 'bg-background dark:bg-background',
  adaptiveCard: 'bg-card dark:bg-card/95',

  // Shadows that work in both themes
  adaptiveShadow: 'shadow-sm dark:shadow-black/20',
  adaptiveShadowMd: 'shadow-md dark:shadow-black/30',
  adaptiveShadowLg: 'shadow-lg dark:shadow-black/40',
};
