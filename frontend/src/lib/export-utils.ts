import { TestRecord } from "./types";

export interface ExportOptions {
  format: 'json' | 'csv' | 'markdown';
  includeMetadata?: boolean;
  dateRange?: {
    start: Date;
    end: Date;
  };
  agentFilter?: string[];
  statusFilter?: ('success' | 'error')[];
}

export class TestResultExporter {
  static exportToJSON(records: TestRecord[], options: ExportOptions = { format: 'json' }): string {
    const filteredRecords = this.filterRecords(records, options);
    
    const exportData = {
      exportedAt: new Date().toISOString(),
      totalRecords: filteredRecords.length,
      filters: {
        dateRange: options.dateRange,
        agentFilter: options.agentFilter,
        statusFilter: options.statusFilter
      },
      records: filteredRecords.map(record => ({
        ...record,
        metadata: options.includeMetadata ? {
          exportedAt: new Date().toISOString(),
          originalTimestamp: record.timestamp
        } : undefined
      }))
    };

    return JSON.stringify(exportData, null, 2);
  }

  static exportToCSV(records: TestRecord[], options: ExportOptions = { format: 'csv' }): string {
    const filteredRecords = this.filterRecords(records, options);
    
    if (filteredRecords.length === 0) {
      return 'No records to export';
    }

    // CSV headers
    const headers = [
      'ID',
      'Agent ID',
      'Agent Name',
      'Input',
      'Output',
      'Status',
      'Duration (ms)',
      'Timestamp'
    ];

    if (options.includeMetadata) {
      headers.push('Export Date');
    }

    // CSV rows
    const rows = filteredRecords.map(record => {
      const row = [
        this.escapeCsvValue(record.id),
        this.escapeCsvValue(record.agent_id),
        this.escapeCsvValue(record.agent_name),
        this.escapeCsvValue(record.input),
        this.escapeCsvValue(record.output),
        this.escapeCsvValue(record.status),
        record.duration.toString(),
        this.escapeCsvValue(record.timestamp)
      ];

      if (options.includeMetadata) {
        row.push(this.escapeCsvValue(new Date().toISOString()));
      }

      return row.join(',');
    });

    return [headers.join(','), ...rows].join('\n');
  }

  static exportToMarkdown(records: TestRecord[], options: ExportOptions = { format: 'markdown' }): string {
    const filteredRecords = this.filterRecords(records, options);
    
    if (filteredRecords.length === 0) {
      return '# Test Results Export\n\nNo records to export.';
    }

    let markdown = '# Test Results Export\n\n';
    
    if (options.includeMetadata) {
      markdown += `**Exported:** ${new Date().toISOString()}\n`;
      markdown += `**Total Records:** ${filteredRecords.length}\n\n`;
      
      if (options.dateRange) {
        markdown += `**Date Range:** ${options.dateRange.start.toISOString()} to ${options.dateRange.end.toISOString()}\n`;
      }
      
      if (options.agentFilter && options.agentFilter.length > 0) {
        markdown += `**Agent Filter:** ${options.agentFilter.join(', ')}\n`;
      }
      
      if (options.statusFilter && options.statusFilter.length > 0) {
        markdown += `**Status Filter:** ${options.statusFilter.join(', ')}\n`;
      }
      
      markdown += '\n---\n\n';
    }

    filteredRecords.forEach((record, index) => {
      markdown += `## Test ${index + 1}: ${record.agent_name}\n\n`;
      markdown += `**Agent ID:** \`${record.agent_id}\`\n`;
      markdown += `**Status:** ${record.status === 'success' ? '✅' : '❌'} ${record.status}\n`;
      markdown += `**Duration:** ${record.duration}ms\n`;
      markdown += `**Timestamp:** ${record.timestamp}\n\n`;
      
      markdown += `### Input\n\`\`\`\n${record.input}\n\`\`\`\n\n`;
      markdown += `### Output\n\`\`\`\n${record.output}\n\`\`\`\n\n`;
      markdown += '---\n\n';
    });

    return markdown;
  }

  static downloadFile(content: string, filename: string, mimeType: string): void {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.style.display = 'none';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
  }

  static exportAndDownload(records: TestRecord[], options: ExportOptions): void {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    let content: string;
    let filename: string;
    let mimeType: string;

    switch (options.format) {
      case 'json':
        content = this.exportToJSON(records, options);
        filename = `test-results-${timestamp}.json`;
        mimeType = 'application/json';
        break;
      case 'csv':
        content = this.exportToCSV(records, options);
        filename = `test-results-${timestamp}.csv`;
        mimeType = 'text/csv';
        break;
      case 'markdown':
        content = this.exportToMarkdown(records, options);
        filename = `test-results-${timestamp}.md`;
        mimeType = 'text/markdown';
        break;
      default:
        throw new Error(`Unsupported export format: ${options.format}`);
    }

    this.downloadFile(content, filename, mimeType);
  }

  private static filterRecords(records: TestRecord[], options: ExportOptions): TestRecord[] {
    return records.filter(record => {
      // Date range filter
      if (options.dateRange) {
        const recordDate = new Date(record.timestamp);
        if (recordDate < options.dateRange.start || recordDate > options.dateRange.end) {
          return false;
        }
      }

      // Agent filter
      if (options.agentFilter && options.agentFilter.length > 0) {
        if (!options.agentFilter.includes(record.agent_id)) {
          return false;
        }
      }

      // Status filter
      if (options.statusFilter && options.statusFilter.length > 0) {
        if (!options.statusFilter.includes(record.status)) {
          return false;
        }
      }

      return true;
    });
  }

  private static escapeCsvValue(value: string): string {
    if (value.includes(',') || value.includes('"') || value.includes('\n')) {
      return `"${value.replace(/"/g, '""')}"`;
    }
    return value;
  }

  static getExportSummary(records: TestRecord[], options: ExportOptions): {
    totalRecords: number;
    filteredRecords: number;
    successCount: number;
    errorCount: number;
    dateRange: { earliest: string; latest: string } | null;
    uniqueAgents: string[];
  } {
    const filteredRecords = this.filterRecords(records, options);
    
    const successCount = filteredRecords.filter(r => r.status === 'success').length;
    const errorCount = filteredRecords.filter(r => r.status === 'error').length;
    
    const uniqueAgents = [...new Set(filteredRecords.map(r => r.agent_name))];
    
    let dateRange = null;
    if (filteredRecords.length > 0) {
      const dates = filteredRecords.map(r => new Date(r.timestamp));
      const earliest = new Date(Math.min(...dates.map(d => d.getTime())));
      const latest = new Date(Math.max(...dates.map(d => d.getTime())));
      dateRange = {
        earliest: earliest.toISOString(),
        latest: latest.toISOString()
      };
    }

    return {
      totalRecords: records.length,
      filteredRecords: filteredRecords.length,
      successCount,
      errorCount,
      dateRange,
      uniqueAgents
    };
  }
}
