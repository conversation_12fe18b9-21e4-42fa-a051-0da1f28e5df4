import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import {
  Agent,
  AgentUpdate,
  Task,
  TestRecord,
  AgentStats,
  AgentFilters,
  LoadingState,
  ErrorState,
  AppConfig
} from "./types";
import { api } from "./api";

// Agent Store
interface AgentStore {
  // State
  agents: Agent[];
  selectedAgent: Agent | null;
  filters: AgentFilters;
  loading: LoadingState;
  error: ErrorState;
  stats: AgentStats | null;

  // Actions
  fetchAgents: () => Promise<void>;
  fetchAgent: (agentId: string) => Promise<void>;
  createAgent: (description: string, options?: any) => Promise<string>;
  updateAgent: (agentId: string, updates: AgentUpdate) => Promise<void>;
  deleteAgent: (agentId: string) => Promise<void>;
  updateAgentStatus: (agentId: string, status: Agent["status"]) => Promise<void>;
  setSelectedAgent: (agent: Agent | null) => void;
  setFilters: (filters: Partial<AgentFilters>) => void;
  clearError: () => void;
  fetchStats: () => Promise<void>;
}

export const useAgentStore = create<AgentStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      agents: [],
      selectedAgent: null,
      filters: {
        status: "all",
        search: "",
        sortBy: "created",
        sortOrder: "desc"
      },
      loading: { isLoading: false },
      error: { hasError: false },
      stats: null,

      // Actions
      fetchAgents: async () => {
        set({ loading: { isLoading: true, message: "加载Agent列表..." } });
        try {
          const response = await api.getAgents(get().filters);
          if (response.success && response.data) {
            set({ 
              agents: response.data.data,
              loading: { isLoading: false },
              error: { hasError: false }
            });
          } else {
            throw new Error(response.error?.message || "获取Agent列表失败");
          }
        } catch (error) {
          set({ 
            loading: { isLoading: false },
            error: { 
              hasError: true, 
              error: error as Error,
              retry: get().fetchAgents
            }
          });
        }
      },

      fetchAgent: async (agentId: string) => {
        set({ loading: { isLoading: true, message: "加载Agent详情..." } });
        try {
          const response = await api.getAgent(agentId);
          if (response.success && response.data) {
            set({ 
              selectedAgent: response.data,
              loading: { isLoading: false },
              error: { hasError: false }
            });
          } else {
            throw new Error(response.error?.message || "获取Agent详情失败");
          }
        } catch (error) {
          set({ 
            loading: { isLoading: false },
            error: { 
              hasError: true, 
              error: error as Error,
              retry: () => get().fetchAgent(agentId)
            }
          });
        }
      },

      createAgent: async (description: string, options?: any) => {
        set({ loading: { isLoading: true, message: "创建Agent中..." } });
        try {
          const response = await api.createAgent({ description, options });
          if (response.success && response.data) {
            set({ 
              loading: { isLoading: false },
              error: { hasError: false }
            });
            return response.data.task_id;
          } else {
            throw new Error(response.error?.message || "创建Agent失败");
          }
        } catch (error) {
          set({ 
            loading: { isLoading: false },
            error: { 
              hasError: true, 
              error: error as Error,
              retry: () => get().createAgent(description, options)
            }
          });
          throw error;
        }
      },

      deleteAgent: async (agentId: string) => {
        set({ loading: { isLoading: true, message: "删除Agent中..." } });
        try {
          const response = await api.deleteAgent(agentId);
          if (response.success) {
            const agents = get().agents.filter(a => a.agent_id !== agentId);
            set({ 
              agents,
              loading: { isLoading: false },
              error: { hasError: false }
            });
          } else {
            throw new Error(response.error?.message || "删除Agent失败");
          }
        } catch (error) {
          set({ 
            loading: { isLoading: false },
            error: { 
              hasError: true, 
              error: error as Error,
              retry: () => get().deleteAgent(agentId)
            }
          });
          throw error;
        }
      },

      updateAgentStatus: async (agentId: string, status: Agent["status"]) => {
        try {
          const response = await api.updateAgentStatus(agentId, status);
          if (response.success && response.data) {
            const agents = get().agents.map(a =>
              a.agent_id === agentId ? response.data! : a
            );
            set({ agents });
          } else {
            throw new Error(response.error?.message || "更新Agent状态失败");
          }
        } catch (error) {
          set({
            error: {
              hasError: true,
              error: error as Error,
              retry: () => get().updateAgentStatus(agentId, status)
            }
          });
          throw error;
        }
      },

      updateAgent: async (agentId: string, updates: AgentUpdate) => {
        try {
          const response = await api.updateAgent(agentId, updates);
          if (response.success && response.data) {
            const agents = get().agents.map(a =>
              a.agent_id === agentId ? response.data! : a
            );
            set({ agents });
          } else {
            throw new Error(response.error?.message || "更新Agent失败");
          }
        } catch (error) {
          set({
            error: {
              hasError: true,
              error: error as Error,
              retry: () => get().updateAgent(agentId, updates)
            }
          });
          throw error;
        }
      },

      setSelectedAgent: (agent: Agent | null) => {
        set({ selectedAgent: agent });
      },

      setFilters: (filters: Partial<AgentFilters>) => {
        set({ filters: { ...get().filters, ...filters } });
        get().fetchAgents();
      },

      clearError: () => {
        set({ error: { hasError: false } });
      },

      fetchStats: async () => {
        try {
          const response = await api.getStats();
          if (response.success && response.data) {
            set({ stats: response.data });
          }
        } catch (error) {
          if (process.env.NODE_ENV === 'development') {
            console.error("Failed to fetch stats:", error);
          }
        }
      }
    }),
    { name: "agent-store" }
  )
);

// Task Store
interface TaskStore {
  tasks: Task[];
  currentTask: Task | null;
  loading: LoadingState;
  error: ErrorState;

  fetchTaskStatus: (taskId: string) => Promise<void>;
  setCurrentTask: (task: Task | null) => void;
  clearError: () => void;
}

export const useTaskStore = create<TaskStore>()(
  devtools(
    (set, get) => ({
      tasks: [],
      currentTask: null,
      loading: { isLoading: false },
      error: { hasError: false },

      fetchTaskStatus: async (taskId: string) => {
        set({ loading: { isLoading: true, message: "获取任务状态..." } });
        try {
          const response = await api.getTaskStatus(taskId);
          if (response.success && response.data) {
            set({ 
              currentTask: response.data,
              loading: { isLoading: false },
              error: { hasError: false }
            });
          } else {
            throw new Error(response.error?.message || "获取任务状态失败");
          }
        } catch (error) {
          set({ 
            loading: { isLoading: false },
            error: { 
              hasError: true, 
              error: error as Error,
              retry: () => get().fetchTaskStatus(taskId)
            }
          });
        }
      },

      setCurrentTask: (task: Task | null) => {
        set({ currentTask: task });
      },

      clearError: () => {
        set({ error: { hasError: false } });
      }
    }),
    { name: "task-store" }
  )
);

// Test Store
interface TestStore {
  testRecords: TestRecord[];
  currentTest: TestRecord | null;
  loading: LoadingState;
  error: ErrorState;

  fetchTestHistory: (agentId?: string) => Promise<void>;
  invokeAgent: (agentId: string, input: string, options?: any) => Promise<string>;
  setCurrentTest: (test: TestRecord | null) => void;
  clearError: () => void;
}

export const useTestStore = create<TestStore>()(
  devtools(
    (set, get) => ({
      testRecords: [],
      currentTest: null,
      loading: { isLoading: false },
      error: { hasError: false },

      fetchTestHistory: async (agentId?: string) => {
        set({ loading: { isLoading: true, message: "加载测试历史..." } });
        try {
          const response = await api.getTestHistory(agentId);
          if (response.success && response.data) {
            set({ 
              testRecords: response.data,
              loading: { isLoading: false },
              error: { hasError: false }
            });
          } else {
            throw new Error(response.error?.message || "获取测试历史失败");
          }
        } catch (error) {
          set({ 
            loading: { isLoading: false },
            error: { 
              hasError: true, 
              error: error as Error,
              retry: () => get().fetchTestHistory(agentId)
            }
          });
        }
      },

      invokeAgent: async (agentId: string, input: string, options?: any) => {
        set({ loading: { isLoading: true, message: "Agent处理中..." } });
        try {
          const response = await api.invokeAgent(agentId, { input, options });
          if (response.success && response.data) {
            set({ 
              loading: { isLoading: false },
              error: { hasError: false }
            });
            // 刷新测试历史
            get().fetchTestHistory();
            return response.data.response;
          } else {
            throw new Error(response.error?.message || "Agent调用失败");
          }
        } catch (error) {
          set({ 
            loading: { isLoading: false },
            error: { 
              hasError: true, 
              error: error as Error,
              retry: () => get().invokeAgent(agentId, input, options)
            }
          });
          throw error;
        }
      },

      setCurrentTest: (test: TestRecord | null) => {
        set({ currentTest: test });
      },

      clearError: () => {
        set({ error: { hasError: false } });
      }
    }),
    { name: "test-store" }
  )
);

// App Store
interface AppStore {
  config: AppConfig;
  theme: "light" | "dark" | "system";
  sidebarCollapsed: boolean;

  setTheme: (theme: "light" | "dark" | "system") => void;
  toggleSidebar: () => void;
  setSidebarCollapsed: (collapsed: boolean) => void;
}

export const useAppStore = create<AppStore>()(
  devtools(
    persist(
      (set) => ({
        config: {
          api_url: process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000",
          app_name: process.env.NEXT_PUBLIC_APP_NAME || "Meta-Agent",
          app_version: process.env.NEXT_PUBLIC_APP_VERSION || "1.0.0",
          debug: process.env.NEXT_PUBLIC_DEBUG === "true",
          mock_api: process.env.NEXT_PUBLIC_MOCK_API === "true"
        },
        theme: "dark",
        sidebarCollapsed: false,

        setTheme: (theme) => set({ theme }),
        toggleSidebar: () => set((state) => ({ sidebarCollapsed: !state.sidebarCollapsed })),
        setSidebarCollapsed: (collapsed) => set({ sidebarCollapsed: collapsed })
      }),
      { name: "app-store" }
    ),
    { name: "app-store" }
  )
);
