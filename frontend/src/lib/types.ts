// 核心数据类型定义

// Agent相关类型
export interface Specialist {
  name: string;
  system_prompt: string;
  role?: string;
  description?: string;
}

export interface TeamPlan {
  team_name: string;
  description: string;
  objective: string;
  domain?: string;
  complexity?: string;
  template_id?: string;
  orchestrator_prompt?: string;
  specialists?: Specialist[];
  team_members: TeamMember[];
  workflow: Workflow;
  created_at?: string;
  customizations?: any;


}

export interface TeamMember {
  id?: string;
  name: string;
  role: string;
  description: string;
  system_prompt: string;
  capabilities: string[];
  tools: string[];
  metadata?: any;
}

export interface Workflow {
  steps: WorkflowStep[];
}

export interface WorkflowStep {
  name: string;
  description: string;
  assignee: string;
  inputs: string[];
  outputs: string[];
  dependencies?: string[];
}

export interface PlanningTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  difficulty: string;
  example_prompt: string;
}

export interface RequirementAnalysis {
  domain: string;
  complexity: string;
  key_requirements: string[];
  suggested_templates: string[];
  custom_needs: string[];
}

export interface PlanningRequest {
  request_id: string;
  status: string;
  message: string;
  team_plan: TeamPlan;
}

export interface TestRecord {
  id: string;
  agent_id: string;
  agent_name: string;
  input: string;
  output: string;
  timestamp: string;
  duration: number;
  status: "success" | "error";
}

export interface Agent {
  agent_id: string;
  class_name?: string;
  team_name: string;
  description: string;
  team_plan?: TeamPlan;
  specialists?: Array<{ name: string; role: string }>;
  team_members?: Array<{
    name: string;
    role: string;
    description?: string;
    system_prompt?: string;
  }>;
  status: "active" | "inactive" | "error" | "created_but_not_loaded" | "fallback";
  created_at: string;
  generated_at?: string;
  last_used?: string;
  usage_count?: number;
  total_executions?: number;
  api_endpoint?: string;
  is_loaded?: boolean;
  metadata?: {
    team_size?: number;
    workflow_steps?: number;
    domain?: string;
    complexity?: string;
  };
  version?: string;
  team_size?: number;
  workflow_steps?: number;
  success_rate?: number;
  avg_response_time?: number;


}

// 任务相关类型
export type TaskStatus = "pending" | "planning" | "generating" | "loading" | "completed" | "failed";

export interface Task {
  task_id: string;
  status: TaskStatus;
  progress: number;
  description: string;
  result?: {
    agent_id: string;
    team_plan: TeamPlan;
    api_endpoint: string;
  };
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  created_at: string;
  completed_at?: string;
}

// API请求/响应类型
export interface CreateAgentRequest extends TeamPlan {
  // 继承TeamPlan的所有属性
}

export interface CreateAgentResponse {
  agent_id: string;
  status: "active" | "created_but_not_loaded" | "fallback";
  message: string;
  api_endpoint: string;
  generation_info?: {
    class_name: string;
    file_path: string;
    generated_at: string;
  };
}

export interface AgentUpdate {
  team_name?: string;
  description?: string;
  status?: Agent["status"];
  prompt_template?: string;
  system_prompt?: string;
  team_plan?: TeamPlan;
  team_members?: Array<{
    name: string;
    role: string;
    description?: string;
    system_prompt?: string;
  }>;


}

export interface AgentInvokeRequest {
  input: string;
  conversation_id?: string;
  stream?: boolean;
  options?: {
    max_tokens?: number;
    temperature?: number;
  };
  ai_override?: {
    provider?: string;
    model?: string;
    temperature?: number;
    max_tokens?: number;
    base_url?: string;
    custom_provider_name?: string;
  };
}

export interface AgentInvokeResponse {
  agent_id: string;
  status: "completed" | "failed" | "completed_fallback";
  result?: {
    final_result: any;
    execution_summary: {
      total_steps: number;
      completed_steps: number;
      team_members_involved: number;
      execution_quality: string;
      recommendations: string[];
    };
    team_collaboration: {
      collaboration_score: number;
      communication_quality: string;
      role_distribution: string;
      efficiency_rating: string;
      improvement_suggestions: string[];
    };
  };
  executed_at?: string;
  error?: string;
  note?: string;
}

// 测试相关类型
export interface TestRecord {
  id: string;
  agent_id: string;
  agent_name: string;
  input: string;
  output: string;
  timestamp: string;
  duration: number;
  status: "success" | "error";
  error_message?: string;
}

export interface TestOptions {
  stream: boolean;
  temperature: number;
  max_tokens: number;
}

// 统计相关类型
export interface AgentStats {
  total_agents: number;
  active_agents: number;
  inactive_agents: number;
  error_agents: number;
  total_usage: number;
  recent_usage: number;
}

export interface UsageStats {
  daily_calls: number;
  weekly_calls: number;
  monthly_calls: number;
  average_response_time: number;
  success_rate: number;
}

// 过滤和排序类型
export interface AgentFilters {
  status?: "all" | "active" | "inactive" | "error";
  search?: string;
  sortBy?: "name" | "created" | "usage" | "last_used";
  sortOrder?: "asc" | "desc";
}

// 分页类型
export interface PaginationParams {
  page: number;
  size: number;
  // Legacy support
  skip?: number;
  limit?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  size: number;
  total_pages: number;
}

// New pagination types matching backend
export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  total_pages: number;
  has_next: boolean;
  has_prev: boolean;
}

export interface PaginatedTemplateResponse {
  items: TemplateListItem[];
  pagination: PaginationMeta;
}

// 错误类型
export interface ApiError {
  code: string;
  message: string;
  details?: any;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ApiError;
  message?: string;
  timestamp: string;
}

// 表单类型
export interface AgentCreationFormData {
  description: string;
  model?: string;
  temperature?: number;
}

export interface TestFormData {
  input: string;
  options: TestOptions;
}

// 组件Props类型
export interface LoadingState {
  isLoading: boolean;
  message?: string;
}

export interface ErrorState {
  hasError: boolean;
  error?: Error | ApiError;
  retry?: () => void;
}

// 导航和路由类型
export interface NavigationItem {
  name: string;
  href: string;
  icon: string;
  badge?: string;
}

export interface SidebarSection {
  title: string;
  items: NavigationItem[];
}

// 主题和样式类型
export type ThemeMode = "light" | "dark" | "system";

export interface ThemeConfig {
  mode: ThemeMode;
  primaryColor: string;
  borderRadius: number;
}

// 用户相关类型（为未来扩展预留）
export interface User {
  id: string;
  name: string;
  email: string;
  role: "admin" | "user";
  created_at: string;
}

export interface UserSession {
  user: User;
  token: string;
  expires_at: string;
}

// 配置类型
export interface AppConfig {
  api_url: string;
  app_name: string;
  app_version: string;
  debug: boolean;
  mock_api: boolean;
}

// 事件类型
export interface AppEvent {
  type: string;
  payload: any;
  timestamp: string;
}





// 日志相关类型
export interface LogEntry {
  timestamp: string;
  level: "DEBUG" | "INFO" | "WARNING" | "ERROR" | "CRITICAL";
  message: string;
  source: string;
  details?: any;
}

export interface LogsResponse {
  logs: LogEntry[];
  total: number;
  available_sources: string[];
  available_levels: string[];
}

// API密钥相关类型
export interface APIKey {
  id: string;
  name: string;
  description?: string;
  key_prefix: string;
  status: "active" | "inactive" | "error" | "expired" | "revoked";
  usage?: {
    requests_today: number;
    requests_month: number;
    cost_today: number;
    cost_month: number;
  };
  usage_count?: number;
  created_at: string;
  last_used?: string;
}

export interface APIKeyCreate {
  name: string;
  key: string;
  description?: string;
  expires_at?: string;
}

export interface APIKeyUpdate {
  name?: string;
  description?: string;
  status?: "active" | "inactive" | "error" | "expired" | "revoked";
}

export interface APIKeyTest {
  test_endpoint?: string;
}

// Template-related types
export type TemplateCategory =
  | "business"
  | "technical"
  | "creative"
  | "analysis"
  | "support"
  | "education"
  | "investigation"
  | "consulting"
  | "research"
  | "customer_service"
  | "marketing"
  | "sales"
  | "healthcare"
  | "finance"
  | "legal"
  | "other";

export type TemplateDifficulty = "beginner" | "intermediate" | "advanced" | "expert";

export type TemplateVisibility = "private" | "shared" | "public" | "featured";

export type TemplateStatus = "draft" | "active" | "archived" | "deprecated";

// Enhanced Template interface
export interface Template {
  // Core identification
  id: number;
  template_id: string;
  name: string;
  description: string;

  // Categorization
  category: TemplateCategory;
  difficulty: TemplateDifficulty;
  visibility: TemplateVisibility;
  status: TemplateStatus;

  // Content
  prompt_template: string;
  team_structure_template: Record<string, any>;
  default_config?: Record<string, any>;

  // Organization and discovery
  tags: string[];
  keywords?: string[];
  use_case?: string;

  // Versioning and relationships
  version: string;
  parent_template_id?: string;
  source_agent_id?: string;

  // User and ownership
  user_id?: number;
  author_name?: string;

  // Statistics
  usage_count: number;
  rating?: number;
  rating_count: number;

  // Timestamps
  created_at: string;
  updated_at?: string;

  // Computed fields (set by API)
  is_owner?: boolean;
  can_edit?: boolean;

  // Legacy fields for backward compatibility
  title?: string;
  prompt?: string;
  system_prompt?: string;
  specialists?: Array<{
    name: string;
    role: string;
    description: string;
  }>;
  usageCount?: number;
  author?: string;
  createdAt?: string;

  // Extended metadata
  metadata?: Record<string, any>;
}

// Template list response (optimized for listing)
export interface TemplateListItem {
  id: number;
  template_id: string;
  name: string;
  description: string;
  category: TemplateCategory;
  difficulty: TemplateDifficulty;
  visibility: TemplateVisibility;
  status: TemplateStatus;
  tags: string[];
  usage_count: number;
  rating?: number;
  rating_count: number;
  version: string;
  author_name?: string;
  use_case?: string;
  created_at: string;
  updated_at?: string;
  is_owner?: boolean;
  can_edit?: boolean;
}

// Template creation request
export interface TemplateCreateRequest {
  name: string;
  description: string;
  category: TemplateCategory;
  difficulty: TemplateDifficulty;
  prompt_template: string;
  team_structure_template: Record<string, any>;
  visibility?: TemplateVisibility;
  status?: TemplateStatus;
  default_config?: Record<string, any>;
  tags?: string[];
  keywords?: string[];
  use_case?: string;
  source_agent_id?: string;
  parent_template_id?: string;
  metadata?: Record<string, any>;
}

// Template update request
export interface TemplateUpdateRequest {
  name?: string;
  description?: string;
  category?: TemplateCategory;
  difficulty?: TemplateDifficulty;
  visibility?: TemplateVisibility;
  status?: TemplateStatus;
  prompt_template?: string;
  team_structure_template?: Record<string, any>;
  default_config?: Record<string, any>;
  tags?: string[];
  keywords?: string[];
  use_case?: string;
  metadata?: Record<string, any>;
  version?: string;
}

// Template from agent request
export interface TemplateFromAgentRequest {
  agent_id: string;
  name: string;
  description: string;
  category: TemplateCategory;
  difficulty: TemplateDifficulty;
  visibility?: TemplateVisibility;
  tags?: string[];
  keywords?: string[];
  use_case?: string;
}

// Template search filters
export interface TemplateFilters {
  category?: TemplateCategory;
  difficulty?: TemplateDifficulty;
  visibility?: TemplateVisibility;
  status?: TemplateStatus;
  search?: string;
  tags?: string;
  my_templates?: boolean;
}

// Template statistics
export interface TemplateStats {
  overview: {
    total_templates: number;
    public_templates: number;
    featured_templates: number;
    user_templates: number;
    avg_rating: number;
    total_usage: number;
    categories_count: number;
  };
  categories: Array<{
    category: string;
    count: number;
    avg_rating: number;
    total_usage: number;
  }>;
  difficulties: Array<{
    difficulty: string;
    count: number;
    avg_rating: number;
  }>;
}

// Popular tags
export interface PopularTag {
  tag: string;
  count: number;
  avg_rating: number;
  total_usage: number;
}

// 错误处理相关类型
export interface ErrorDetails {
  code?: string;
  field?: string;
  value?: any;
  constraint?: string;
}

export interface ValidationError {
  field: string;
  messages: string[];
}

export interface ApiErrorResponse {
  message: string;
  code?: string;
  status?: number;
  details?: ErrorDetails | ValidationError[];
  timestamp?: string;
}

// 加载状态相关类型
export interface LoadingState {
  isLoading: boolean;
  error?: string | null;
  data?: any;
}

export interface AsyncOperationResult<T> {
  data?: T;
  error?: ApiErrorResponse;
  loading: boolean;
}

// 分页增强类型
export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface PaginatedData<T> {
  items: T[];
  data?: T[]; // 向后兼容
  pagination: PaginationMeta;
  meta?: PaginationMeta; // 向后兼容
}

// 流式响应类型
export interface StreamMessage {
  type: "agent_response" | "error" | "end" | "status";
  content: string;
  timestamp?: string;
}

export interface StreamingOptions {
  onMessage: (message: StreamMessage) => void;
  onError: (error: Error) => void;
  onComplete: () => void;
}

// 导出所有类型的联合类型，便于类型检查
export type AllTypes =
  | Agent
  | TeamPlan
  | Specialist
  | Task
  | TestRecord
  | AgentStats
  | UsageStats
  | ApiResponse
  | ApiError
  | LogEntry
  | APIKey
  | Template;
