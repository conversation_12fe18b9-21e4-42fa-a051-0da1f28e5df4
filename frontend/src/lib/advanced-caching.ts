"use client";

import { QueryClient, QueryKey, QueryFunction } from '@tanstack/react-query';
import { useCallback, useEffect, useMemo } from 'react';

// Advanced Caching Strategy Types
export interface CacheStrategy {
  name: string;
  staleTime: number;
  gcTime: number;
  refetchInterval?: number;
  refetchOnWindowFocus?: boolean;
  refetchOnReconnect?: boolean;
  retry?: number | ((failureCount: number, error: unknown) => boolean);
  retryDelay?: number | ((retryAttempt: number, error: unknown) => number);
}

export interface CacheConfig {
  strategies: Record<string, CacheStrategy>;
  globalDefaults: CacheStrategy;
  dataTypes: Record<string, string>; // Maps data type to strategy name
}

// Predefined caching strategies
export const CACHE_STRATEGIES: Record<string, CacheStrategy> = {
  // Real-time data that changes frequently
  realtime: {
    name: 'realtime',
    staleTime: 10 * 1000, // 10 seconds
    gcTime: 30 * 1000, // 30 seconds
    refetchInterval: 15 * 1000, // 15 seconds
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  },

  // Frequently accessed data
  frequent: {
    name: 'frequent',
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 60 * 1000, // 1 minute
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  },

  // Standard data with moderate update frequency
  standard: {
    name: 'standard',
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  },

  // Static or rarely changing data
  static: {
    name: 'static',
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    retry: 1,
    retryDelay: 5000,
  },

  // Long-term cached data
  persistent: {
    name: 'persistent',
    staleTime: 60 * 60 * 1000, // 1 hour
    gcTime: 24 * 60 * 60 * 1000, // 24 hours
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    retry: 1,
    retryDelay: 10000,
  },

  // Background data that updates silently
  background: {
    name: 'background',
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    refetchInterval: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
    retry: 2,
    retryDelay: 2000,
  },
};

// Data type to strategy mapping
export const DATA_TYPE_STRATEGIES: Record<string, string> = {
  'agent-health': 'realtime',
  'system-metrics': 'realtime',
  'dashboard-stats': 'frequent',
  'intelligence-insights': 'standard',
  'user-preferences': 'static',
  'agent-list': 'standard',
  'templates': 'persistent',
  'analytics': 'background',
  'monitoring-data': 'realtime',
  'performance-trends': 'frequent',
};

// Advanced cache configuration
export const ADVANCED_CACHE_CONFIG: CacheConfig = {
  strategies: CACHE_STRATEGIES,
  globalDefaults: CACHE_STRATEGIES.standard,
  dataTypes: DATA_TYPE_STRATEGIES,
};

// Cache key utilities
export class CacheKeyManager {
  private static instance: CacheKeyManager;
  private keyRegistry: Map<string, QueryKey[]> = new Map();

  static getInstance(): CacheKeyManager {
    if (!CacheKeyManager.instance) {
      CacheKeyManager.instance = new CacheKeyManager();
    }
    return CacheKeyManager.instance;
  }

  generateKey(type: string, params: Record<string, any> = {}): QueryKey {
    const baseKey = [type];
    
    // Add user context
    if (params.userId) {
      baseKey.push('user', params.userId);
    }
    
    // Add tenant context
    if (params.tenantId) {
      baseKey.push('tenant', params.tenantId);
    }
    
    // Add specific parameters
    Object.entries(params).forEach(([key, value]) => {
      if (key !== 'userId' && key !== 'tenantId' && value !== undefined) {
        baseKey.push(key, value);
      }
    });

    const queryKey = baseKey as QueryKey;
    
    // Register the key for invalidation purposes
    if (!this.keyRegistry.has(type)) {
      this.keyRegistry.set(type, []);
    }
    this.keyRegistry.get(type)!.push(queryKey);
    
    return queryKey;
  }

  getRelatedKeys(type: string): QueryKey[] {
    return this.keyRegistry.get(type) || [];
  }

  invalidateByType(queryClient: QueryClient, type: string): void {
    queryClient.invalidateQueries({
      predicate: (query) => {
        const key = query.queryKey;
        return Array.isArray(key) && key[0] === type;
      },
    });
  }

  invalidateByPattern(queryClient: QueryClient, pattern: string[]): void {
    queryClient.invalidateQueries({
      predicate: (query) => {
        const key = query.queryKey;
        if (!Array.isArray(key)) return false;
        
        return pattern.every((part, index) => {
          if (index >= key.length) return false;
          return part === '*' || key[index] === part;
        });
      },
    });
  }
}

// Smart cache invalidation
export class SmartCacheInvalidator {
  private dependencies: Map<string, string[]> = new Map();

  constructor() {
    this.setupDependencies();
  }

  private setupDependencies(): void {
    // Define data dependencies
    this.dependencies.set('agent-created', [
      'agent-list',
      'dashboard-stats',
      'system-metrics',
    ]);

    this.dependencies.set('agent-updated', [
      'agent-list',
      'agent-health',
      'intelligence-insights',
    ]);

    this.dependencies.set('agent-executed', [
      'agent-health',
      'system-metrics',
      'performance-trends',
      'monitoring-data',
    ]);

    this.dependencies.set('user-preferences-updated', [
      'user-preferences',
      'dashboard-stats',
    ]);

    this.dependencies.set('system-alert', [
      'system-metrics',
      'monitoring-data',
      'agent-health',
    ]);
  }

  invalidateRelated(queryClient: QueryClient, event: string, context: Record<string, any> = {}): void {
    const relatedTypes = this.dependencies.get(event) || [];
    const keyManager = CacheKeyManager.getInstance();

    relatedTypes.forEach(type => {
      keyManager.invalidateByType(queryClient, type);
    });

    // Context-specific invalidations
    if (context.agentId) {
      keyManager.invalidateByPattern(queryClient, ['*', 'agent', context.agentId]);
    }

    if (context.userId) {
      keyManager.invalidateByPattern(queryClient, ['*', 'user', context.userId]);
    }
  }
}

// Performance monitoring for cache
export class CachePerformanceMonitor {
  private metrics: Map<string, {
    hits: number;
    misses: number;
    errors: number;
    avgFetchTime: number;
    lastAccessed: number;
  }> = new Map();

  recordHit(queryKey: QueryKey): void {
    const key = this.keyToString(queryKey);
    const metric = this.metrics.get(key) || this.createEmptyMetric();
    metric.hits++;
    metric.lastAccessed = Date.now();
    this.metrics.set(key, metric);
  }

  recordMiss(queryKey: QueryKey, fetchTime: number): void {
    const key = this.keyToString(queryKey);
    const metric = this.metrics.get(key) || this.createEmptyMetric();
    metric.misses++;
    metric.avgFetchTime = (metric.avgFetchTime + fetchTime) / 2;
    metric.lastAccessed = Date.now();
    this.metrics.set(key, metric);
  }

  recordError(queryKey: QueryKey): void {
    const key = this.keyToString(queryKey);
    const metric = this.metrics.get(key) || this.createEmptyMetric();
    metric.errors++;
    metric.lastAccessed = Date.now();
    this.metrics.set(key, metric);
  }

  getMetrics(): Record<string, any> {
    const result: Record<string, any> = {};
    
    this.metrics.forEach((metric, key) => {
      const total = metric.hits + metric.misses;
      result[key] = {
        ...metric,
        hitRate: total > 0 ? metric.hits / total : 0,
        errorRate: total > 0 ? metric.errors / total : 0,
      };
    });

    return result;
  }

  private keyToString(queryKey: QueryKey): string {
    return JSON.stringify(queryKey);
  }

  private createEmptyMetric() {
    return {
      hits: 0,
      misses: 0,
      errors: 0,
      avgFetchTime: 0,
      lastAccessed: Date.now(),
    };
  }
}

// Custom hooks for advanced caching
export function useAdvancedCache() {
  const keyManager = useMemo(() => CacheKeyManager.getInstance(), []);
  const invalidator = useMemo(() => new SmartCacheInvalidator(), []);
  const monitor = useMemo(() => new CachePerformanceMonitor(), []);

  const getCacheStrategy = useCallback((dataType: string): CacheStrategy => {
    const strategyName = DATA_TYPE_STRATEGIES[dataType] || 'standard';
    return CACHE_STRATEGIES[strategyName] || CACHE_STRATEGIES.standard;
  }, []);

  const generateCacheKey = useCallback((type: string, params: Record<string, any> = {}): QueryKey => {
    return keyManager.generateKey(type, params);
  }, [keyManager]);

  const invalidateRelated = useCallback((queryClient: QueryClient, event: string, context: Record<string, any> = {}) => {
    invalidator.invalidateRelated(queryClient, event, context);
  }, [invalidator]);

  return {
    getCacheStrategy,
    generateCacheKey,
    invalidateRelated,
    keyManager,
    monitor,
  };
}

// Optimized query function wrapper
export function createOptimizedQuery<T>(
  dataType: string,
  queryFn: QueryFunction<T>,
  options: {
    userId?: string;
    tenantId?: string;
    params?: Record<string, any>;
  } = {}
) {
  const { getCacheStrategy, generateCacheKey } = useAdvancedCache();
  
  const strategy = getCacheStrategy(dataType);
  const queryKey = generateCacheKey(dataType, {
    userId: options.userId,
    tenantId: options.tenantId,
    ...options.params,
  });

  return {
    queryKey,
    queryFn,
    ...strategy,
  };
}

// Cache warming utilities
export function useCacheWarming() {
  const { generateCacheKey, getCacheStrategy } = useAdvancedCache();

  const warmCache = useCallback(async (
    queryClient: QueryClient,
    dataTypes: string[],
    context: { userId?: string; tenantId?: string } = {}
  ) => {
    const warmingPromises = dataTypes.map(async (dataType) => {
      const strategy = getCacheStrategy(dataType);
      const queryKey = generateCacheKey(dataType, context);
      
      // Only warm if not already cached or stale
      const existingData = queryClient.getQueryData(queryKey);
      const queryState = queryClient.getQueryState(queryKey);
      
      if (!existingData || (queryState && Date.now() - queryState.dataUpdatedAt > strategy.staleTime)) {
        try {
          await queryClient.prefetchQuery({
            queryKey,
            queryFn: () => {
              // This would be replaced with actual query function
              console.log(`Warming cache for ${dataType}`);
              return Promise.resolve(null);
            },
            ...strategy,
          });
        } catch (error) {
          console.warn(`Failed to warm cache for ${dataType}:`, error);
        }
      }
    });

    await Promise.allSettled(warmingPromises);
  }, [generateCacheKey, getCacheStrategy]);

  return { warmCache };
}
