"use client";

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/lib/auth';
import { api } from '@/lib/api';
import { useEffect, useCallback, useState, useMemo } from 'react';

// Advanced Monitoring Types
export interface AgentHealthMetrics {
  agentId: string;
  agentName: string;
  status: 'healthy' | 'warning' | 'critical' | 'offline';
  uptime: number; // percentage
  responseTime: {
    current: number;
    average: number;
    p95: number;
    p99: number;
  };
  successRate: {
    current: number;
    last24h: number;
    last7d: number;
  };
  errorRate: number;
  throughput: {
    requestsPerMinute: number;
    requestsPerHour: number;
  };
  resourceUsage: {
    cpu: number;
    memory: number;
    tokens: number;
  };
  lastHealthCheck: string;
  alerts: AgentAlert[];
}

export interface AgentAlert {
  id: string;
  agentId: string;
  agentName: string;
  type: 'performance' | 'error' | 'resource' | 'availability' | 'cost';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  threshold: number;
  currentValue: number;
  triggeredAt: string;
  acknowledged: boolean;
  resolvedAt?: string;
  metadata?: Record<string, any>;
}

export interface SystemMetrics {
  totalAgents: number;
  healthyAgents: number;
  warningAgents: number;
  criticalAgents: number;
  offlineAgents: number;
  totalRequests: number;
  totalErrors: number;
  averageResponseTime: number;
  systemLoad: number;
  activeAlerts: number;
  resolvedAlerts: number;
  lastUpdated: string;
}

export interface PerformanceTrend {
  timestamp: string;
  responseTime: number;
  successRate: number;
  throughput: number;
  errorRate: number;
  activeUsers: number;
}

export interface AlertRule {
  id: string;
  name: string;
  description: string;
  agentId?: string; // undefined for system-wide rules
  metric: 'response_time' | 'success_rate' | 'error_rate' | 'throughput' | 'uptime';
  operator: 'gt' | 'lt' | 'eq' | 'gte' | 'lte';
  threshold: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  enabled: boolean;
  cooldownMinutes: number;
  notificationChannels: ('email' | 'slack' | 'webhook')[];
  createdAt: string;
  lastTriggered?: string;
}

export interface MonitoringData {
  systemMetrics: SystemMetrics;
  agentHealth: AgentHealthMetrics[];
  performanceTrends: PerformanceTrend[];
  activeAlerts: AgentAlert[];
  alertRules: AlertRule[];
}

// Mock Data Generators
export const generateMockAgentHealth = (): AgentHealthMetrics[] => {
  const agents = [
    { id: 'agent_1', name: '数据分析助手' },
    { id: 'agent_2', name: '内容创作团队' },
    { id: 'agent_3', name: '客服机器人' },
    { id: 'agent_4', name: '代码审查助手' },
    { id: 'agent_5', name: '文档生成器' },
  ];

  return agents.map((agent, index) => {
    const baseResponseTime = 1000 + Math.random() * 2000;
    const baseSuccessRate = 90 + Math.random() * 10;
    const hasIssue = Math.random() < 0.3; // 30% chance of having issues
    
    const status = hasIssue 
      ? (Math.random() < 0.5 ? 'warning' : 'critical')
      : 'healthy';

    const alerts: AgentAlert[] = hasIssue ? [{
      id: `alert_${agent.id}_${Date.now()}`,
      agentId: agent.id,
      agentName: agent.name,
      type: Math.random() < 0.5 ? 'performance' : 'error',
      severity: status === 'critical' ? 'high' : 'medium',
      title: status === 'critical' ? '响应时间异常' : '成功率下降',
      message: status === 'critical' 
        ? `${agent.name}的响应时间超过阈值` 
        : `${agent.name}的成功率低于预期`,
      threshold: status === 'critical' ? 2000 : 95,
      currentValue: status === 'critical' ? baseResponseTime : baseSuccessRate,
      triggeredAt: new Date(Date.now() - Math.random() * 60 * 60 * 1000).toISOString(),
      acknowledged: false,
    }] : [];

    return {
      agentId: agent.id,
      agentName: agent.name,
      status,
      uptime: hasIssue ? 85 + Math.random() * 10 : 95 + Math.random() * 5,
      responseTime: {
        current: baseResponseTime,
        average: baseResponseTime * 0.9,
        p95: baseResponseTime * 1.2,
        p99: baseResponseTime * 1.5,
      },
      successRate: {
        current: baseSuccessRate,
        last24h: baseSuccessRate - Math.random() * 2,
        last7d: baseSuccessRate - Math.random() * 5,
      },
      errorRate: hasIssue ? 5 + Math.random() * 10 : Math.random() * 3,
      throughput: {
        requestsPerMinute: 10 + Math.random() * 50,
        requestsPerHour: 600 + Math.random() * 3000,
      },
      resourceUsage: {
        cpu: hasIssue ? 70 + Math.random() * 25 : 20 + Math.random() * 40,
        memory: hasIssue ? 60 + Math.random() * 30 : 30 + Math.random() * 40,
        tokens: Math.floor(1000 + Math.random() * 9000),
      },
      lastHealthCheck: new Date(Date.now() - Math.random() * 5 * 60 * 1000).toISOString(),
      alerts,
    };
  });
};

export const generateMockSystemMetrics = (agentHealth: AgentHealthMetrics[]): SystemMetrics => {
  const totalAgents = agentHealth.length;
  const healthyAgents = agentHealth.filter(a => a.status === 'healthy').length;
  const warningAgents = agentHealth.filter(a => a.status === 'warning').length;
  const criticalAgents = agentHealth.filter(a => a.status === 'critical').length;
  const offlineAgents = agentHealth.filter(a => a.status === 'offline').length;
  
  const totalRequests = agentHealth.reduce((sum, agent) => sum + agent.throughput.requestsPerHour, 0);
  const totalErrors = agentHealth.reduce((sum, agent) => 
    sum + (agent.throughput.requestsPerHour * agent.errorRate / 100), 0);
  const averageResponseTime = agentHealth.reduce((sum, agent) => 
    sum + agent.responseTime.current, 0) / totalAgents;
  
  const activeAlerts = agentHealth.reduce((sum, agent) => sum + agent.alerts.length, 0);

  return {
    totalAgents,
    healthyAgents,
    warningAgents,
    criticalAgents,
    offlineAgents,
    totalRequests: Math.floor(totalRequests),
    totalErrors: Math.floor(totalErrors),
    averageResponseTime: Math.floor(averageResponseTime),
    systemLoad: 45 + Math.random() * 30,
    activeAlerts,
    resolvedAlerts: Math.floor(Math.random() * 20),
    lastUpdated: new Date().toISOString(),
  };
};

export const generateMockPerformanceTrends = (): PerformanceTrend[] => {
  const trends: PerformanceTrend[] = [];
  const now = new Date();
  
  for (let i = 23; i >= 0; i--) {
    const timestamp = new Date(now.getTime() - i * 60 * 60 * 1000);
    trends.push({
      timestamp: timestamp.toISOString(),
      responseTime: 1000 + Math.random() * 1000 + Math.sin(i / 4) * 200,
      successRate: 92 + Math.random() * 8,
      throughput: 50 + Math.random() * 100 + Math.sin(i / 6) * 30,
      errorRate: Math.random() * 5,
      activeUsers: 10 + Math.random() * 40 + Math.sin(i / 3) * 15,
    });
  }
  
  return trends;
};

export const generateMockAlertRules = (): AlertRule[] => [
  {
    id: 'rule_1',
    name: '响应时间过高',
    description: '当Agent响应时间超过2秒时触发警报',
    metric: 'response_time',
    operator: 'gt',
    threshold: 2000,
    severity: 'high',
    enabled: true,
    cooldownMinutes: 15,
    notificationChannels: ['email', 'slack'],
    createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    lastTriggered: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
  },
  {
    id: 'rule_2',
    name: '成功率过低',
    description: '当Agent成功率低于90%时触发警报',
    metric: 'success_rate',
    operator: 'lt',
    threshold: 90,
    severity: 'medium',
    enabled: true,
    cooldownMinutes: 30,
    notificationChannels: ['email'],
    createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
  },
  {
    id: 'rule_3',
    name: '错误率异常',
    description: '当错误率超过5%时触发警报',
    metric: 'error_rate',
    operator: 'gt',
    threshold: 5,
    severity: 'critical',
    enabled: true,
    cooldownMinutes: 10,
    notificationChannels: ['email', 'slack', 'webhook'],
    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
  },
];

// Data fetching functions
export const fetchMonitoringData = async (): Promise<MonitoringData> => {
  try {
    // In production, this would call real monitoring APIs
    const agentHealth = generateMockAgentHealth();
    const systemMetrics = generateMockSystemMetrics(agentHealth);
    const performanceTrends = generateMockPerformanceTrends();
    const alertRules = generateMockAlertRules();
    const activeAlerts = agentHealth.flatMap(agent => agent.alerts);

    return {
      systemMetrics,
      agentHealth,
      performanceTrends,
      activeAlerts,
      alertRules,
    };
  } catch (error) {
    console.error('Failed to fetch monitoring data:', error);
    throw error;
  }
};

// Custom hooks for monitoring
export function useMonitoringData() {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['monitoring-data', user?.id],
    queryFn: fetchMonitoringData,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 30 * 1000, // 30 seconds
    enabled: !!user,
  });
}

export function useAgentHealth(agentId?: string) {
  const { data: monitoringData } = useMonitoringData();

  return useMemo(() => {
    if (!monitoringData?.agentHealth) return agentId ? null : [];
    
    return agentId 
      ? monitoringData.agentHealth.find(health => health.agentId === agentId) || null
      : monitoringData.agentHealth;
  }, [monitoringData?.agentHealth, agentId]);
}

export function useAlertManagement() {
  const queryClient = useQueryClient();
  const [notifications, setNotifications] = useState<AgentAlert[]>([]);

  const acknowledgeAlert = useCallback(async (alertId: string) => {
    // In production, this would call an API
    console.log(`Acknowledging alert ${alertId}`);
    queryClient.invalidateQueries({ queryKey: ['monitoring-data'] });
  }, [queryClient]);

  const resolveAlert = useCallback(async (alertId: string) => {
    // In production, this would call an API
    console.log(`Resolving alert ${alertId}`);
    queryClient.invalidateQueries({ queryKey: ['monitoring-data'] });
  }, [queryClient]);

  const createAlertRule = useCallback(async (rule: Omit<AlertRule, 'id' | 'createdAt'>) => {
    // In production, this would call an API
    console.log('Creating alert rule:', rule);
    queryClient.invalidateQueries({ queryKey: ['monitoring-data'] });
  }, [queryClient]);

  return {
    acknowledgeAlert,
    resolveAlert,
    createAlertRule,
    notifications,
  };
}

export function useRealTimeMonitoring() {
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    if (!user) return;

    // Simulate real-time connection
    setIsConnected(true);
    
    // Set up real-time monitoring updates
    const interval = setInterval(() => {
      queryClient.invalidateQueries({
        queryKey: ['monitoring-data', user.id],
      });
    }, 30000); // Update every 30 seconds

    return () => {
      clearInterval(interval);
      setIsConnected(false);
    };
  }, [user, queryClient]);

  return {
    isConnected,
    lastUpdate: new Date().toISOString(),
  };
}

// Performance analysis utilities
export function analyzePerformanceTrends(trends: PerformanceTrend[]) {
  if (trends.length < 2) return null;

  const latest = trends[trends.length - 1];
  const previous = trends[trends.length - 2];
  
  return {
    responseTimeChange: ((latest.responseTime - previous.responseTime) / previous.responseTime) * 100,
    successRateChange: latest.successRate - previous.successRate,
    throughputChange: ((latest.throughput - previous.throughput) / previous.throughput) * 100,
    errorRateChange: latest.errorRate - previous.errorRate,
  };
}

export function getHealthStatus(metrics: AgentHealthMetrics): {
  status: string;
  score: number;
  issues: string[];
} {
  let score = 100;
  const issues: string[] = [];

  if (metrics.responseTime.current > 2000) {
    score -= 20;
    issues.push('响应时间过长');
  }

  if (metrics.successRate.current < 95) {
    score -= 15;
    issues.push('成功率偏低');
  }

  if (metrics.errorRate > 5) {
    score -= 25;
    issues.push('错误率过高');
  }

  if (metrics.uptime < 95) {
    score -= 20;
    issues.push('可用性不足');
  }

  if (metrics.resourceUsage.cpu > 80) {
    score -= 10;
    issues.push('CPU使用率过高');
  }

  if (metrics.resourceUsage.memory > 80) {
    score -= 10;
    issues.push('内存使用率过高');
  }

  let status = 'excellent';
  if (score < 60) status = 'poor';
  else if (score < 75) status = 'fair';
  else if (score < 90) status = 'good';

  return { status, score: Math.max(0, score), issues };
}
