/**
 * API client for settings and API key management
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

class ApiError extends Error {
  constructor(public status: number, message: string) {
    super(message);
    this.name = 'ApiError';
  }
}

async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const token = localStorage.getItem('auth_token');
  
  const config: RequestInit = {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers,
    },
  };

  const response = await fetch(`${API_BASE_URL}${endpoint}`, config);

  if (!response.ok) {
    const errorText = await response.text();
    let errorMessage = `HTTP ${response.status}`;
    
    try {
      const errorData = JSON.parse(errorText);
      errorMessage = errorData.detail || errorData.message || errorMessage;
    } catch {
      errorMessage = errorText || errorMessage;
    }
    
    throw new ApiError(response.status, errorMessage);
  }

  const contentType = response.headers.get('content-type');
  if (contentType && contentType.includes('application/json')) {
    return response.json();
  }
  
  return response.text() as T;
}

// Transform flat backend response to nested frontend structure
function transformBackendToFrontend(backendSettings: any) {
  return {
    general: {
      app_name: backendSettings.app_name,
      app_description: backendSettings.app_description,
      default_language: backendSettings.default_language,
      timezone: backendSettings.timezone,
      theme: backendSettings.theme
    },
    agent: {
      max_concurrent_agents: backendSettings.max_concurrent_agents,
      default_model: backendSettings.default_model,
      default_temperature: backendSettings.default_temperature,
      max_response_length: backendSettings.max_response_length,
      timeout_seconds: backendSettings.timeout_seconds
    },
    ai_team_generation: {
      enable_ai_team_generation: backendSettings.enable_ai_team_generation,
      team_generation_provider: backendSettings.team_generation_provider,
      team_generation_custom_provider_name: backendSettings.team_generation_custom_provider_name || '',
      team_generation_model: backendSettings.team_generation_model,
      team_generation_temperature: backendSettings.team_generation_temperature,
      team_generation_max_tokens: backendSettings.team_generation_max_tokens,
      team_generation_base_url: backendSettings.team_generation_base_url || '',
      team_generation_api_key: backendSettings.team_generation_api_key || ''
    },
    api: {
      rate_limit_per_minute: backendSettings.rate_limit_per_minute,
      enable_cors: backendSettings.enable_cors,
      cors_origins: backendSettings.cors_origins,
      enable_docs: backendSettings.enable_docs,
      enable_debug: backendSettings.enable_debug,
      agent_api_base_url: backendSettings.agent_api_base_url || `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}/api/v1/agents`
    },
    logging: {
      log_level: backendSettings.log_level,
      max_log_files: backendSettings.max_log_files,
      log_retention_days: backendSettings.log_retention_days,
      enable_file_logging: backendSettings.enable_file_logging
    },
    security: {
      enable_auth: backendSettings.enable_auth,
      session_timeout_minutes: backendSettings.session_timeout_minutes,
      max_login_attempts: backendSettings.max_login_attempts,
      enable_2fa: backendSettings.enable_2fa
    }
  };
}

// Transform nested frontend structure to flat backend structure
function transformFrontendToBackend(frontendSettings: any) {
  return {
    // General settings
    app_name: frontendSettings.general.app_name,
    app_description: frontendSettings.general.app_description,
    default_language: frontendSettings.general.default_language,
    timezone: frontendSettings.general.timezone,
    theme: frontendSettings.general.theme,

    // Agent settings
    max_concurrent_agents: frontendSettings.agent.max_concurrent_agents,
    default_model: frontendSettings.agent.default_model,
    default_temperature: frontendSettings.agent.default_temperature,
    max_response_length: frontendSettings.agent.max_response_length,
    timeout_seconds: frontendSettings.agent.timeout_seconds,

    // AI Team Generation settings
    enable_ai_team_generation: frontendSettings.ai_team_generation.enable_ai_team_generation,
    team_generation_provider: frontendSettings.ai_team_generation.team_generation_provider,
    team_generation_custom_provider_name: frontendSettings.ai_team_generation.team_generation_custom_provider_name || null,
    team_generation_model: frontendSettings.ai_team_generation.team_generation_model,
    team_generation_temperature: frontendSettings.ai_team_generation.team_generation_temperature,
    team_generation_max_tokens: frontendSettings.ai_team_generation.team_generation_max_tokens,
    team_generation_base_url: frontendSettings.ai_team_generation.team_generation_base_url || null,
    team_generation_api_key: frontendSettings.ai_team_generation.team_generation_api_key || null,

    // API settings
    rate_limit_per_minute: frontendSettings.api.rate_limit_per_minute,
    enable_cors: frontendSettings.api.enable_cors,
    cors_origins: frontendSettings.api.cors_origins,
    enable_docs: frontendSettings.api.enable_docs,
    enable_debug: frontendSettings.api.enable_debug,
    agent_api_base_url: frontendSettings.api.agent_api_base_url,

    // Logging settings
    log_level: frontendSettings.logging.log_level,
    max_log_files: frontendSettings.logging.max_log_files,
    log_retention_days: frontendSettings.logging.log_retention_days,
    enable_file_logging: frontendSettings.logging.enable_file_logging,

    // Security settings
    enable_auth: frontendSettings.security.enable_auth,
    session_timeout_minutes: frontendSettings.security.session_timeout_minutes,
    max_login_attempts: frontendSettings.security.max_login_attempts,
    enable_2fa: frontendSettings.security.enable_2fa
  };
}

// Settings API
export const settingsApi = {
  async getSettings() {
    const backendSettings = await apiRequest<any>('/api/v1/settings');
    return transformBackendToFrontend(backendSettings);
  },

  async updateSettings(settings: any) {
    const backendSettings = transformFrontendToBackend(settings);
    const result = await apiRequest<any>('/api/v1/settings', {
      method: 'PUT',
      body: JSON.stringify(backendSettings),
    });
    return transformBackendToFrontend(result);
  },

  async resetSettings() {
    const result = await apiRequest<any>('/api/v1/settings/reset', {
      method: 'POST',
    });
    return transformBackendToFrontend(result);
  },

  async exportSettings() {
    const result = await apiRequest<{ data: any; exported_at: string }>('/api/v1/settings/export');
    return {
      ...result,
      data: transformBackendToFrontend(result.data)
    };
  },

  async importSettings(settings: any) {
    const backendSettings = transformFrontendToBackend(settings);
    const result = await apiRequest<any>('/api/v1/settings/import', {
      method: 'POST',
      body: JSON.stringify(backendSettings),
    });
    return transformBackendToFrontend(result);
  },
};

// API Keys API
export const apiKeysApi = {
  async getApiKeys() {
    return apiRequest<any[]>('/api/v1/api-keys');
  },

  async createApiKey(keyData: {
    name: string;
    provider: string;
    key: string;
    base_url?: string;
    description?: string;
    expires_at?: string;
  }) {
    return apiRequest<{ success: boolean; message: string; data: any }>('/api/v1/api-keys', {
      method: 'POST',
      body: JSON.stringify(keyData),
    });
  },

  async updateApiKey(keyId: string, updates: {
    name?: string;
    description?: string;
    status?: string;
    base_url?: string;
  }) {
    return apiRequest<any>(`/api/v1/api-keys/${keyId}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  },

  async deleteApiKey(keyId: string) {
    return apiRequest<{ success: boolean; message: string }>(`/api/v1/api-keys/${keyId}`, {
      method: 'DELETE',
    });
  },

  async testApiKey(keyId: string, testData: { test_endpoint?: string } = {}) {
    return apiRequest<{ success: boolean; message: string; data: any }>(`/api/v1/api-keys/${keyId}/test`, {
      method: 'POST',
      body: JSON.stringify({
        test_endpoint: '/api/v1/health',
        ...testData,
      }),
    });
  },
};

// Error handling utilities - use the centralized error handler
import { handleApiError as centralizedHandleApiError } from './error-handler';

export function handleApiError(error: unknown): string {
  const apiError = centralizedHandleApiError(error, { showToast: false, logError: false });
  return apiError.message;
}

// Loading state management
export function createLoadingState() {
  let loadingCount = 0;
  const listeners: ((isLoading: boolean) => void)[] = [];

  return {
    start() {
      loadingCount++;
      if (loadingCount === 1) {
        listeners.forEach(listener => listener(true));
      }
    },
    
    stop() {
      loadingCount = Math.max(0, loadingCount - 1);
      if (loadingCount === 0) {
        listeners.forEach(listener => listener(false));
      }
    },
    
    subscribe(listener: (isLoading: boolean) => void) {
      listeners.push(listener);
      return () => {
        const index = listeners.indexOf(listener);
        if (index > -1) {
          listeners.splice(index, 1);
        }
      };
    },
    
    get isLoading() {
      return loadingCount > 0;
    }
  };
}

export { ApiError };
