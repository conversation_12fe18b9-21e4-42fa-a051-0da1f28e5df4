"use client";

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/lib/auth';
import { api } from '@/lib/api';
import { useMemo, useCallback } from 'react';

// Intelligence Engine Types
export interface AgentInsight {
  id: string;
  type: 'performance' | 'optimization' | 'usage' | 'cost' | 'quality';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  recommendation: string;
  impact: string;
  confidence: number; // 0-100
  agentId?: string;
  agentName?: string;
  metrics?: Record<string, number>;
  actionable: boolean;
  estimatedSavings?: number;
  estimatedImprovement?: number;
  createdAt: string;
}

export interface PredictiveAnalytics {
  usage_trend: {
    direction: 'increasing' | 'decreasing' | 'stable';
    confidence: number;
    predicted_change: number;
    timeframe: string;
  };
  cost_forecast: {
    next_month: number;
    next_quarter: number;
    trend: 'increasing' | 'decreasing' | 'stable';
    factors: string[];
  };
  performance_trend: {
    success_rate: {
      current: number;
      predicted: number;
      trend: 'improving' | 'declining' | 'stable';
    };
    response_time: {
      current: number;
      predicted: number;
      trend: 'improving' | 'declining' | 'stable';
    };
  };
  recommendations: {
    priority: 'high' | 'medium' | 'low';
    action: string;
    expected_impact: string;
    time_to_implement: string;
  }[];
}

export interface WorkflowOptimization {
  agentId: string;
  agentName: string;
  currentWorkflow: string[];
  suggestedWorkflow: string[];
  improvements: {
    step: string;
    issue: string;
    suggestion: string;
    impact: 'high' | 'medium' | 'low';
  }[];
  estimatedSpeedup: number;
  estimatedCostReduction: number;
}

export interface UserBehaviorPattern {
  userId: string;
  patterns: {
    mostUsedAgents: string[];
    preferredTimeSlots: string[];
    commonWorkflows: string[];
    failurePatterns: string[];
  };
  recommendations: {
    suggestedAgents: string[];
    optimizationTips: string[];
    trainingNeeds: string[];
  };
}

export interface IntelligenceData {
  insights: AgentInsight[];
  predictiveAnalytics: PredictiveAnalytics;
  workflowOptimizations: WorkflowOptimization[];
  userPatterns: UserBehaviorPattern;
  lastAnalyzed: string;
}

// Mock Intelligence Data Generators
export const generateMockInsights = (): AgentInsight[] => {
  const insights: AgentInsight[] = [
    {
      id: 'insight_1',
      type: 'performance',
      severity: 'high',
      title: '数据分析助手响应时间异常',
      description: '数据分析助手的平均响应时间比正常水平高出45%，可能影响用户体验',
      recommendation: '建议优化数据处理算法或增加计算资源',
      impact: '可提升用户满意度15-20%',
      confidence: 87,
      agentId: 'agent_1',
      agentName: '数据分析助手',
      metrics: { avgResponseTime: 2800, normalResponseTime: 1900 },
      actionable: true,
      estimatedImprovement: 45,
      createdAt: new Date().toISOString(),
    },
    {
      id: 'insight_2',
      type: 'cost',
      severity: 'medium',
      title: 'API调用成本优化机会',
      description: '检测到多个Agent使用了昂贵的GPT-4模型处理简单任务',
      recommendation: '将简单任务路由到GPT-3.5-turbo可节省60%成本',
      impact: '预计每月节省$240-320',
      confidence: 92,
      actionable: true,
      estimatedSavings: 280,
      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: 'insight_3',
      type: 'usage',
      severity: 'low',
      title: '创意写作团队使用率偏低',
      description: '创意写作团队在过去7天仅被使用3次，可能需要推广或优化',
      recommendation: '考虑添加更多使用场景示例或改进团队配置',
      impact: '可提升团队利用率30-50%',
      confidence: 73,
      agentId: 'agent_3',
      agentName: '创意写作团队',
      actionable: true,
      estimatedImprovement: 40,
      createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: 'insight_4',
      type: 'quality',
      severity: 'medium',
      title: '客服机器人准确率下降',
      description: '客服机器人的回答准确率从95%下降到87%，需要重新训练',
      recommendation: '更新知识库并重新训练模型，添加最新的FAQ数据',
      impact: '可恢复95%以上的准确率',
      confidence: 89,
      agentId: 'agent_4',
      agentName: '客服机器人',
      actionable: true,
      estimatedImprovement: 8,
      createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: 'insight_5',
      type: 'optimization',
      severity: 'high',
      title: '工作流程瓶颈识别',
      description: '代码审查助手的第二步骤耗时过长，成为整个流程的瓶颈',
      recommendation: '并行化部分检查步骤或优化算法复杂度',
      impact: '可减少总体执行时间35%',
      confidence: 94,
      agentId: 'agent_5',
      agentName: '代码审查助手',
      actionable: true,
      estimatedImprovement: 35,
      createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
    }
  ];

  return insights.sort((a, b) => {
    const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
    return severityOrder[b.severity] - severityOrder[a.severity];
  });
};

export const generateMockPredictiveAnalytics = (): any => ({
  usage_trend: {
    direction: 'increasing',
    confidence: 85,
    predicted_change: 23,
    timeframe: '下个月',
  },
  cost_forecast: {
    next_month: 1250,
    next_quarter: 3600,
    trend: 'increasing',
    factors: ['使用量增长', 'GPT-4使用增加', '新用户加入'],
  },
  performance_trend: {
    success_rate: {
      current: 94.2,
      predicted: 96.1,
      trend: 'improving',
    },
    response_time: {
      current: 1850,
      predicted: 1650,
      trend: 'improving',
    },
  },
  recommendations: [
    {
      priority: 'high',
      action: '实施智能模型路由策略',
      expected_impact: '降低成本25%，提升响应速度15%',
      time_to_implement: '2-3周',
    },
    {
      priority: 'medium',
      action: '优化低使用率Agent配置',
      expected_impact: '提升整体利用率30%',
      time_to_implement: '1-2周',
    },
    {
      priority: 'low',
      action: '添加更多预设模板',
      expected_impact: '减少创建时间50%',
      time_to_implement: '3-4周',
    },
  ],
});

export const generateMockWorkflowOptimizations = (): WorkflowOptimization[] => [
  {
    agentId: 'agent_1',
    agentName: '数据分析助手',
    currentWorkflow: ['数据收集', '数据清洗', '分析处理', '结果生成', '报告输出'],
    suggestedWorkflow: ['数据收集', '并行清洗+预处理', '分析处理', '结果生成'],
    improvements: [
      {
        step: '数据清洗',
        issue: '串行处理效率低',
        suggestion: '并行化数据清洗和预处理步骤',
        impact: 'high',
      },
      {
        step: '报告输出',
        issue: '重复格式化操作',
        suggestion: '使用模板化输出减少处理时间',
        impact: 'medium',
      },
    ],
    estimatedSpeedup: 35,
    estimatedCostReduction: 20,
  },
  {
    agentId: 'agent_2',
    agentName: '内容创作团队',
    currentWorkflow: ['需求分析', '创意构思', '内容撰写', '审核修改', '最终输出'],
    suggestedWorkflow: ['需求分析', '并行创意+大纲', '内容撰写', '自动审核+人工确认'],
    improvements: [
      {
        step: '创意构思',
        issue: '单线程思考限制',
        suggestion: '多角度并行创意生成',
        impact: 'high',
      },
      {
        step: '审核修改',
        issue: '人工审核耗时',
        suggestion: 'AI预审核+人工确认',
        impact: 'medium',
      },
    ],
    estimatedSpeedup: 28,
    estimatedCostReduction: 15,
  },
];

export const generateMockUserPatterns = (): UserBehaviorPattern => ({
  userId: 'current_user',
  patterns: {
    mostUsedAgents: ['数据分析助手', '内容创作团队', '客服机器人'],
    preferredTimeSlots: ['09:00-11:00', '14:00-16:00', '19:00-21:00'],
    commonWorkflows: ['数据分析→报告生成', '内容创作→审核发布'],
    failurePatterns: ['复杂查询超时', '大文件处理失败'],
  },
  recommendations: {
    suggestedAgents: ['代码审查助手', '技术咨询团队'],
    optimizationTips: [
      '在高峰时段前预热常用Agent',
      '将大文件分块处理',
      '使用缓存减少重复计算',
    ],
    trainingNeeds: [
      '学习高级查询语法',
      '了解Agent工作流程优化',
      '掌握成本控制技巧',
    ],
  },
});

// Data fetching functions
export const fetchIntelligenceData = async (): Promise<IntelligenceData> => {
  try {
    // Fetch real insights from API
    const insightsResponse = await api.intelligence.getInsights();
    const insights = insightsResponse.success ? insightsResponse.data : [];

    // Fetch predictive analytics
    const analyticsResponse = await api.intelligence.getPredictiveAnalytics();
    const predictiveAnalytics = analyticsResponse.success ? analyticsResponse.data : generateMockPredictiveAnalytics();

    // For now, use mock data for workflow optimizations and user patterns
    // These will be implemented in future iterations
    const workflowOptimizations = generateMockWorkflowOptimizations();
    const userPatterns = generateMockUserPatterns();

    return {
      insights,
      predictiveAnalytics,
      workflowOptimizations,
      userPatterns,
      lastAnalyzed: new Date().toISOString(),
    };
  } catch (error) {
    console.warn('Failed to fetch intelligence data from API, using mock data:', error);

    // Fallback to mock data
    const [insights, predictiveAnalytics, workflowOptimizations, userPatterns] = await Promise.all([
      Promise.resolve(generateMockInsights()),
      Promise.resolve(generateMockPredictiveAnalytics()),
      Promise.resolve(generateMockWorkflowOptimizations()),
      Promise.resolve(generateMockUserPatterns()),
    ]);

    return {
      insights,
      predictiveAnalytics,
      workflowOptimizations,
      userPatterns,
      lastAnalyzed: new Date().toISOString(),
    };
  }
};

// Custom hooks for intelligence data
export function useIntelligenceData() {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['intelligence-data', user?.id],
    queryFn: fetchIntelligenceData,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    refetchInterval: 10 * 60 * 1000, // 10 minutes
    enabled: !!user,
  });
}

export function useAgentInsights(agentId?: string) {
  const { data: intelligenceData } = useIntelligenceData();

  return useMemo(() => {
    if (!intelligenceData?.insights) return [];
    
    return agentId 
      ? intelligenceData.insights.filter(insight => insight.agentId === agentId)
      : intelligenceData.insights;
  }, [intelligenceData?.insights, agentId]);
}

export function useWorkflowRecommendations() {
  const { data: intelligenceData } = useIntelligenceData();
  const queryClient = useQueryClient();

  const applyRecommendation = useCallback(async (agentId: string, optimizationId: string) => {
    // In production, this would call an API to apply the optimization
    console.log(`Applying optimization ${optimizationId} to agent ${agentId}`);
    
    // Invalidate related queries to refresh data
    queryClient.invalidateQueries({ queryKey: ['intelligence-data'] });
    queryClient.invalidateQueries({ queryKey: ['dashboard-full-data'] });
  }, [queryClient]);

  return {
    optimizations: intelligenceData?.workflowOptimizations || [],
    applyRecommendation,
  };
}

export function usePredictiveInsights() {
  const { data: intelligenceData } = useIntelligenceData();

  const getInsightsByType = useCallback((type: AgentInsight['type']) => {
    return intelligenceData?.insights.filter(insight => insight.type === type) || [];
  }, [intelligenceData?.insights]);

  const getHighPriorityInsights = useCallback(() => {
    return intelligenceData?.insights.filter(insight => 
      insight.severity === 'high' || insight.severity === 'critical'
    ) || [];
  }, [intelligenceData?.insights]);

  const getActionableInsights = useCallback(() => {
    return intelligenceData?.insights.filter(insight => insight.actionable) || [];
  }, [intelligenceData?.insights]);

  return {
    predictiveAnalytics: intelligenceData?.predictiveAnalytics,
    getInsightsByType,
    getHighPriorityInsights,
    getActionableInsights,
  };
}
