"use client";

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/lib/auth';
import { api } from '@/lib/api';
import { useCallback, useMemo, useState, useEffect } from 'react';

// Personalization Types
export interface UserPreferences {
  userId: string;
  dashboard: {
    layout: 'compact' | 'detailed' | 'custom';
    theme: 'light' | 'dark' | 'auto';
    defaultView: 'overview' | 'agents' | 'monitoring' | 'analytics';
    refreshInterval: number; // seconds
    showAdvancedMetrics: boolean;
    compactMode: boolean;
    autoRefresh: boolean;
  };
  notifications: {
    email: boolean;
    browser: boolean;
    slack: boolean;
    alertThreshold: 'low' | 'medium' | 'high' | 'critical';
    quietHours: {
      enabled: boolean;
      start: string; // HH:mm
      end: string; // HH:mm
    };
  };
  workflow: {
    favoriteAgents: string[];
    recentlyUsed: string[];
    customShortcuts: {
      id: string;
      name: string;
      action: string;
      hotkey?: string;
    }[];
    defaultAgentSettings: Record<string, any>;
  };
  accessibility: {
    reducedMotion: boolean;
    highContrast: boolean;
    fontSize: 'small' | 'medium' | 'large';
    screenReader: boolean;
    keyboardNavigation: boolean;
  };
  privacy: {
    analyticsOptIn: boolean;
    dataRetention: number; // days
    shareUsageData: boolean;
  };
}

export interface UserExpertiseLevel {
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  areas: {
    aiAgents: number; // 0-100
    dataAnalysis: number;
    automation: number;
    apiIntegration: number;
    troubleshooting: number;
  };
  completedTutorials: string[];
  certifications: string[];
  lastAssessment: string;
}

export interface ContextualHelp {
  id: string;
  component: string;
  title: string;
  content: string;
  type: 'tooltip' | 'popover' | 'modal' | 'inline';
  trigger: 'hover' | 'click' | 'focus' | 'auto';
  expertiseLevel: ('beginner' | 'intermediate' | 'advanced' | 'expert')[];
  conditions?: {
    userAction?: string;
    componentState?: Record<string, any>;
    timeSpent?: number;
  };
  priority: number;
  dismissible: boolean;
  showCount?: number;
}

export interface PersonalizationData {
  preferences: UserPreferences;
  expertiseLevel: UserExpertiseLevel;
  contextualHelp: ContextualHelp[];
  recommendations: {
    features: string[];
    tutorials: string[];
    optimizations: string[];
  };
  usage: {
    totalSessions: number;
    averageSessionTime: number;
    mostUsedFeatures: string[];
    strugglingAreas: string[];
  };
}

// Default preferences
export const defaultPreferences: UserPreferences = {
  userId: '',
  dashboard: {
    layout: 'detailed',
    theme: 'auto',
    defaultView: 'overview',
    refreshInterval: 30,
    showAdvancedMetrics: false,
    compactMode: false,
    autoRefresh: true,
  },
  notifications: {
    email: true,
    browser: true,
    slack: false,
    alertThreshold: 'medium',
    quietHours: {
      enabled: false,
      start: '22:00',
      end: '08:00',
    },
  },
  workflow: {
    favoriteAgents: [],
    recentlyUsed: [],
    customShortcuts: [],
    defaultAgentSettings: {},
  },
  accessibility: {
    reducedMotion: false,
    highContrast: false,
    fontSize: 'medium',
    screenReader: false,
    keyboardNavigation: false,
  },
  privacy: {
    analyticsOptIn: true,
    dataRetention: 90,
    shareUsageData: true,
  },
};

// Mock data generators
export const generateMockExpertiseLevel = (): UserExpertiseLevel => ({
  level: 'intermediate',
  areas: {
    aiAgents: 65,
    dataAnalysis: 45,
    automation: 70,
    apiIntegration: 55,
    troubleshooting: 60,
  },
  completedTutorials: [
    'basic-agent-creation',
    'dashboard-navigation',
    'monitoring-basics',
  ],
  certifications: [],
  lastAssessment: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
});

export const generateMockContextualHelp = (expertiseLevel: string): ContextualHelp[] => {
  const allHelp: ContextualHelp[] = [
    {
      id: 'dashboard-overview',
      component: 'dashboard',
      title: '仪表板概览',
      content: '这里显示了您的AI Agent团队的整体状态和性能指标。点击各个卡片可以查看详细信息。',
      type: 'popover',
      trigger: 'auto',
      expertiseLevel: ['beginner'],
      priority: 1,
      dismissible: true,
      showCount: 3,
    },
    {
      id: 'agent-creation-tip',
      component: 'create-agent',
      title: '创建高效Agent',
      content: '为了获得最佳性能，建议在创建Agent时明确定义其职责范围和预期输出格式。',
      type: 'tooltip',
      trigger: 'hover',
      expertiseLevel: ['beginner', 'intermediate'],
      priority: 2,
      dismissible: true,
    },
    {
      id: 'monitoring-alerts',
      component: 'monitoring',
      title: '监控警报设置',
      content: '您可以自定义警报阈值来及时发现性能问题。建议根据业务需求调整敏感度。',
      type: 'inline',
      trigger: 'click',
      expertiseLevel: ['intermediate', 'advanced'],
      priority: 3,
      dismissible: true,
    },
    {
      id: 'advanced-analytics',
      component: 'analytics',
      title: '高级分析功能',
      content: '使用预测性分析来优化Agent性能和成本。这些功能需要一定的数据分析基础。',
      type: 'modal',
      trigger: 'click',
      expertiseLevel: ['advanced', 'expert'],
      priority: 4,
      dismissible: true,
    },
  ];

  return allHelp.filter(help => help.expertiseLevel.includes(expertiseLevel as any));
};

// Data fetching functions
export const fetchPersonalizationData = async (userId: string): Promise<PersonalizationData> => {
  try {
    // In production, this would call real APIs
    const preferences = { ...defaultPreferences, userId };
    const expertiseLevel = generateMockExpertiseLevel();
    const contextualHelp = generateMockContextualHelp(expertiseLevel.level);

    return {
      preferences,
      expertiseLevel,
      contextualHelp,
      recommendations: {
        features: ['智能监控', '成本优化', '工作流自动化'],
        tutorials: ['高级Agent配置', 'API集成最佳实践'],
        optimizations: ['响应时间优化', '资源使用优化'],
      },
      usage: {
        totalSessions: 45,
        averageSessionTime: 25 * 60, // 25 minutes
        mostUsedFeatures: ['agent-creation', 'monitoring', 'testing'],
        strugglingAreas: ['advanced-analytics', 'cost-optimization'],
      },
    };
  } catch (error) {
    console.error('Failed to fetch personalization data:', error);
    throw error;
  }
};

// Custom hooks
export function usePersonalization() {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['personalization', user?.id],
    queryFn: () => fetchPersonalizationData(user?.id || ''),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    enabled: !!user,
  });
}

export function useUserPreferences() {
  const { data: personalizationData } = usePersonalization();
  const queryClient = useQueryClient();

  const updatePreferences = useCallback(async (updates: Partial<UserPreferences>) => {
    // In production, this would call an API
    console.log('Updating preferences:', updates);
    queryClient.invalidateQueries({ queryKey: ['personalization'] });
  }, [queryClient]);

  return {
    preferences: personalizationData?.preferences || defaultPreferences,
    updatePreferences,
  };
}

export function useExpertiseLevel() {
  const { data: personalizationData } = usePersonalization();

  const getRecommendedComplexity = useCallback((feature: string) => {
    const level = personalizationData?.expertiseLevel.level || 'beginner';
    
    switch (level) {
      case 'beginner': return 'basic';
      case 'intermediate': return 'standard';
      case 'advanced': return 'detailed';
      case 'expert': return 'comprehensive';
      default: return 'standard';
    }
  }, [personalizationData?.expertiseLevel.level]);

  const shouldShowAdvancedFeatures = useMemo(() => {
    const level = personalizationData?.expertiseLevel.level;
    return level === 'advanced' || level === 'expert';
  }, [personalizationData?.expertiseLevel.level]);

  return {
    expertiseLevel: personalizationData?.expertiseLevel,
    getRecommendedComplexity,
    shouldShowAdvancedFeatures,
  };
}

export function useContextualHelp(componentName: string) {
  const { data: personalizationData } = usePersonalization();
  const [dismissedHelp, setDismissedHelp] = useState<Set<string>>(new Set());

  const relevantHelp = useMemo(() => {
    if (!personalizationData?.contextualHelp) return [];
    
    return personalizationData.contextualHelp
      .filter(help => 
        help.component === componentName && 
        !dismissedHelp.has(help.id)
      )
      .sort((a, b) => a.priority - b.priority);
  }, [personalizationData?.contextualHelp, componentName, dismissedHelp]);

  const dismissHelp = useCallback((helpId: string) => {
    setDismissedHelp(prev => new Set([...prev, helpId]));
    // In production, this would persist to backend
    localStorage.setItem('dismissedHelp', JSON.stringify([...dismissedHelp, helpId]));
  }, [dismissedHelp]);

  useEffect(() => {
    // Load dismissed help from localStorage
    const stored = localStorage.getItem('dismissedHelp');
    if (stored) {
      try {
        const dismissed = JSON.parse(stored);
        setDismissedHelp(new Set(dismissed));
      } catch (error) {
        console.warn('Failed to parse dismissed help from localStorage');
      }
    }
  }, []);

  return {
    relevantHelp,
    dismissHelp,
  };
}

export function useProgressiveDisclosure() {
  const { expertiseLevel, shouldShowAdvancedFeatures } = useExpertiseLevel();
  const { preferences } = useUserPreferences();

  const getVisibilityLevel = useCallback((feature: string, defaultLevel: 'basic' | 'intermediate' | 'advanced' = 'basic') => {
    if (preferences.dashboard.showAdvancedMetrics) return 'advanced';
    
    const userLevel = expertiseLevel?.level || 'beginner';
    
    const levelMap = {
      beginner: 'basic',
      intermediate: 'intermediate',
      advanced: 'advanced',
      expert: 'advanced',
    };

    return levelMap[userLevel] || defaultLevel;
  }, [expertiseLevel?.level, preferences.dashboard.showAdvancedMetrics]);

  const shouldShowFeature = useCallback((feature: string, requiredLevel: 'basic' | 'intermediate' | 'advanced' = 'basic') => {
    const currentLevel = getVisibilityLevel(feature);
    
    const levelOrder = { basic: 1, intermediate: 2, advanced: 3 };
    return levelOrder[currentLevel] >= levelOrder[requiredLevel];
  }, [getVisibilityLevel]);

  return {
    getVisibilityLevel,
    shouldShowFeature,
    shouldShowAdvancedFeatures,
    compactMode: preferences.dashboard.compactMode,
  };
}

export function useAdaptiveUI() {
  const { preferences, updatePreferences } = useUserPreferences();
  const { expertiseLevel } = useExpertiseLevel();

  const adaptToUserBehavior = useCallback((action: string, context: Record<string, any>) => {
    // Analyze user behavior and adapt UI accordingly
    console.log('Adapting UI based on user action:', action, context);
    
    // Example: If user frequently uses advanced features, suggest enabling advanced mode
    if (action === 'advanced-feature-used' && !preferences.dashboard.showAdvancedMetrics) {
      // Could show a suggestion to enable advanced metrics
    }
  }, [preferences.dashboard.showAdvancedMetrics]);

  const getOptimalLayout = useCallback(() => {
    const level = expertiseLevel?.level || 'beginner';
    
    if (level === 'beginner') return 'compact';
    if (level === 'expert') return 'detailed';
    return 'detailed';
  }, [expertiseLevel?.level]);

  return {
    adaptToUserBehavior,
    getOptimalLayout,
    currentLayout: preferences.dashboard.layout,
  };
}

// Multi-tenant support utilities
export function useMultiTenantSupport() {
  const { user } = useAuth();

  const getUserRole = useCallback(() => {
    // In production, this would come from user data
    return user?.role || 'user';
  }, [user?.role]);

  const hasPermission = useCallback((permission: string) => {
    const role = getUserRole();
    
    // Define role-based permissions
    const permissions = {
      admin: ['*'],
      manager: ['view_all_agents', 'manage_team', 'view_analytics'],
      user: ['view_own_agents', 'create_agents', 'test_agents'],
      viewer: ['view_own_agents'],
    };

    const userPermissions = permissions[role as keyof typeof permissions] || [];
    return userPermissions.includes('*') || userPermissions.includes(permission);
  }, [getUserRole]);

  const getDataScope = useCallback(() => {
    const role = getUserRole();
    
    if (role === 'admin') return 'global';
    if (role === 'manager') return 'team';
    return 'user';
  }, [getUserRole]);

  return {
    getUserRole,
    hasPermission,
    getDataScope,
    userId: user?.id,
    tenantId: user?.tenantId || 'default',
  };
}
