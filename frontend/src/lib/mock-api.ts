import {
  Agent,
  AgentUpdate,
  Task,
  TestRecord,
  CreateAgentRequest,
  CreateAgentResponse,
  AgentInvokeRequest,
  AgentInvokeResponse,
  ApiResponse,
  PaginatedResponse,
  AgentFilters,
  PaginationParams
} from "./types";
import { 
  mockAgents, 
  mockTasks, 
  mockTestRecords, 
  mockStats, 
  generateMockResponse 
} from "./mock-data";

// 模拟延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 生成随机ID
const generateId = () => Math.random().toString(36).substr(2, 9);

// Mock API类
export class MockAPI {
  private agents: Agent[] = [...mockAgents];
  private tasks: Task[] = [...mockTasks];
  private testRecords: TestRecord[] = [...mockTestRecords];

  // Agent相关API
  async getAgents(filters?: AgentFilters, pagination?: PaginationParams): Promise<ApiResponse<PaginatedResponse<Agent>>> {
    await delay(500);

    let filteredAgents = [...this.agents];

    // 应用过滤器
    if (filters) {
      if (filters.status && filters.status !== "all") {
        filteredAgents = filteredAgents.filter(agent => agent.status === filters.status);
      }

      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        filteredAgents = filteredAgents.filter(agent => 
          agent.team_name.toLowerCase().includes(searchLower) ||
          agent.description.toLowerCase().includes(searchLower) ||
          agent.agent_id.toLowerCase().includes(searchLower)
        );
      }

      // 排序
      if (filters.sortBy) {
        filteredAgents.sort((a, b) => {
          let aValue: any, bValue: any;
          
          switch (filters.sortBy) {
            case "name":
              aValue = a.team_name;
              bValue = b.team_name;
              break;
            case "created":
              aValue = new Date(a.created_at);
              bValue = new Date(b.created_at);
              break;
            case "usage":
              aValue = a.usage_count;
              bValue = b.usage_count;
              break;
            case "last_used":
              aValue = new Date(a.last_used || 0);
              bValue = new Date(b.last_used || 0);
              break;
            default:
              return 0;
          }

          if (aValue < bValue) return filters.sortOrder === "desc" ? 1 : -1;
          if (aValue > bValue) return filters.sortOrder === "desc" ? -1 : 1;
          return 0;
        });
      }
    }

    // 应用分页
    const page = pagination?.page || 1;
    const size = pagination?.size || 20;
    const startIndex = (page - 1) * size;
    const endIndex = startIndex + size;
    const paginatedAgents = filteredAgents.slice(startIndex, endIndex);

    return {
      success: true,
      data: {
        data: paginatedAgents,
        total: filteredAgents.length,
        page,
        size,
        total_pages: Math.ceil(filteredAgents.length / size)
      },
      timestamp: new Date().toISOString()
    };
  }

  async getAgent(agentId: string): Promise<ApiResponse<Agent>> {
    await delay(300);

    const agent = this.agents.find(a => a.agent_id === agentId);
    if (!agent) {
      return {
        success: false,
        error: {
          code: "AGENT_NOT_FOUND",
          message: "Agent not found"
        },
        timestamp: new Date().toISOString()
      };
    }

    return {
      success: true,
      data: agent,
      timestamp: new Date().toISOString()
    };
  }

  async createAgent(request: CreateAgentRequest): Promise<ApiResponse<CreateAgentResponse>> {
    await delay(1000);

    const taskId = `task_${generateId()}`;
    const newTask: Task = {
      task_id: taskId,
      status: "pending",
      progress: 0,
      description: `创建Agent: ${request.description.substring(0, 50)}...`,
      created_at: new Date().toISOString()
    };

    this.tasks.unshift(newTask);

    // 模拟异步处理
    setTimeout(() => {
      this.simulateAgentCreation(taskId, request);
    }, 2000);

    return {
      success: true,
      data: {
        task_id: taskId,
        status: "pending",
        estimated_time: 180
      },
      timestamp: new Date().toISOString()
    };
  }

  async deleteAgent(agentId: string): Promise<ApiResponse<void>> {
    await delay(500);

    const index = this.agents.findIndex(a => a.agent_id === agentId);
    if (index === -1) {
      return {
        success: false,
        error: {
          code: "AGENT_NOT_FOUND",
          message: "Agent not found"
        },
        timestamp: new Date().toISOString()
      };
    }

    this.agents.splice(index, 1);

    return {
      success: true,
      message: "Agent deleted successfully",
      timestamp: new Date().toISOString()
    };
  }

  async updateAgentStatus(agentId: string, status: Agent["status"]): Promise<ApiResponse<Agent>> {
    await delay(300);

    const agent = this.agents.find(a => a.agent_id === agentId);
    if (!agent) {
      return {
        success: false,
        error: {
          code: "AGENT_NOT_FOUND",
          message: "Agent not found"
        },
        timestamp: new Date().toISOString()
      };
    }

    agent.status = status;

    return {
      success: true,
      data: agent,
      timestamp: new Date().toISOString()
    };
  }

  async updateAgent(agentId: string, updates: AgentUpdate): Promise<ApiResponse<Agent>> {
    await delay(300);

    const agent = this.agents.find(a => a.agent_id === agentId);
    if (!agent) {
      return {
        success: false,
        error: {
          code: "AGENT_NOT_FOUND",
          message: "Agent not found"
        },
        timestamp: new Date().toISOString()
      };
    }

    // Apply updates
    if (updates.team_name !== undefined) {
      agent.team_name = updates.team_name;
    }
    if (updates.description !== undefined) {
      agent.description = updates.description;
    }
    if (updates.status !== undefined) {
      agent.status = updates.status;
    }
    // Note: prompt_template and system_prompt are not part of the Agent interface
    // but could be added to team_plan if needed

    return {
      success: true,
      data: agent,
      timestamp: new Date().toISOString()
    };
  }

  // Task相关API
  async getTaskStatus(taskId: string): Promise<ApiResponse<Task>> {
    await delay(200);

    const task = this.tasks.find(t => t.task_id === taskId);
    if (!task) {
      return {
        success: false,
        error: {
          code: "TASK_NOT_FOUND",
          message: "Task not found"
        },
        timestamp: new Date().toISOString()
      };
    }

    return {
      success: true,
      data: task,
      timestamp: new Date().toISOString()
    };
  }

  // Agent调用API
  async invokeAgent(agentId: string, request: AgentInvokeRequest): Promise<ApiResponse<AgentInvokeResponse>> {
    await delay(1500 + Math.random() * 1000);

    const agent = this.agents.find(a => a.agent_id === agentId);
    if (!agent) {
      return {
        success: false,
        error: {
          code: "AGENT_NOT_FOUND",
          message: "Agent not found"
        },
        timestamp: new Date().toISOString()
      };
    }

    if (agent.status !== "active") {
      return {
        success: false,
        error: {
          code: "AGENT_INACTIVE",
          message: "Agent is not active"
        },
        timestamp: new Date().toISOString()
      };
    }

    const response = generateMockResponse(agentId, request.input);
    
    // 更新使用统计
    agent.usage_count++;
    agent.last_used = new Date().toISOString();

    // 添加测试记录
    const testRecord: TestRecord = {
      id: `test_${generateId()}`,
      agent_id: agentId,
      agent_name: agent.team_name,
      input: request.input,
      output: response,
      timestamp: new Date().toISOString(),
      duration: 1500 + Math.random() * 1000,
      status: "success"
    };
    this.testRecords.unshift(testRecord);

    return {
      success: true,
      data: {
        response,
        conversation_id: request.conversation_id || `conv_${generateId()}`,
        agent_id: agentId,
        usage: {
          input_tokens: request.input.length,
          output_tokens: response.length,
          total_tokens: request.input.length + response.length
        }
      },
      timestamp: new Date().toISOString()
    };
  }

  // 测试记录API
  async getTestHistory(agentId?: string): Promise<ApiResponse<TestRecord[]>> {
    await delay(300);

    let records = [...this.testRecords];
    if (agentId) {
      records = records.filter(r => r.agent_id === agentId);
    }

    return {
      success: true,
      data: records,
      timestamp: new Date().toISOString()
    };
  }

  // 统计API
  async getStats(): Promise<ApiResponse<typeof mockStats>> {
    await delay(200);

    const stats = {
      ...mockStats,
      total_agents: this.agents.length,
      active_agents: this.agents.filter(a => a.status === "active").length,
      inactive_agents: this.agents.filter(a => a.status === "inactive").length,
      error_agents: this.agents.filter(a => a.status === "error").length,
      total_usage: this.agents.reduce((sum, agent) => sum + agent.usage_count, 0)
    };

    return {
      success: true,
      data: stats,
      timestamp: new Date().toISOString()
    };
  }

  // 私有方法：模拟Agent创建过程
  private async simulateAgentCreation(taskId: string, request: CreateAgentRequest) {
    const task = this.tasks.find(t => t.task_id === taskId);
    if (!task) return;

    // 规划阶段
    task.status = "planning";
    task.progress = 25;
    
    await delay(2000);

    // 生成阶段
    task.status = "generating";
    task.progress = 50;
    
    await delay(2000);

    // 加载阶段
    task.status = "loading";
    task.progress = 75;
    
    await delay(1000);

    // 完成
    const agentId = `agent_${generateId()}`;
    const newAgent: Agent = {
      agent_id: agentId,
      team_name: "新创建的Agent",
      description: request.description,
      team_plan: {
        team_name: "新创建的Agent",
        orchestrator_prompt: "你是一个协调员...",
        specialists: [
          {
            name: "专家1",
            role: "专家",
            system_prompt: "你是一个专家..."
          }
        ]
      },
      status: "active",
      created_at: new Date().toISOString(),
      usage_count: 0,
      api_endpoint: `/agents/${agentId}/invoke`
    };

    this.agents.unshift(newAgent);

    task.status = "completed";
    task.progress = 100;
    task.completed_at = new Date().toISOString();
    task.result = {
      agent_id: agentId,
      team_plan: newAgent.team_plan,
      api_endpoint: newAgent.api_endpoint
    };
  }
}

// 导出单例实例
export const mockAPI = new MockAPI();
