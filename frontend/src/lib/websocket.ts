/**
 * WebSocket service for real-time variable tracking.
 * 
 * This service manages WebSocket connections for receiving real-time updates
 * about variable resolution during agent execution.
 */

export interface VariableUpdate {
  variable_name: string;
  variable_value: string;
  source_agent: string;
  execution_step: number;
  timestamp: string;
  variable_type: string;
  destination_agents: string[];
  metadata: Record<string, any>;
}

export interface WebSocketMessage {
  type: 'connection_established' | 'variable_update' | 'execution_progress' | 'error' | 'pong';
  agent_id?: string;
  session_id?: string;
  data?: any;
  message?: string;
  timestamp: string;
}

export interface VariableTrackingCallbacks {
  onVariableUpdate?: (update: VariableUpdate) => void;
  onExecutionProgress?: (progress: any) => void;
  onConnectionEstablished?: (sessionId: string) => void;
  onError?: (error: string) => void;
  onDisconnect?: () => void;
}

export class VariableTrackingWebSocket {
  private ws: WebSocket | null = null;
  private agentId: string;
  private token: string;
  private callbacks: VariableTrackingCallbacks;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // Start with 1 second
  private sessionId: string | null = null;
  private isConnecting = false;
  private pingInterval: NodeJS.Timeout | null = null;

  constructor(agentId: string, token: string, callbacks: VariableTrackingCallbacks = {}) {
    this.agentId = agentId;
    this.token = token;
    this.callbacks = callbacks;
  }

  /**
   * Connect to the WebSocket server for variable tracking.
   */
  async connect(): Promise<boolean> {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      return true;
    }

    this.isConnecting = true;

    try {
      // Generate session ID if not available
      if (!this.sessionId) {
        this.sessionId = `ws_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      }

      // Construct WebSocket URL
      const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsHost = process.env.NODE_ENV === 'production'
        ? window.location.host
        : 'localhost:8000';
      
      const wsUrl = `${wsProtocol}//${wsHost}/api/v1/ws/agents/${this.agentId}/variables?token=${this.token}&session_id=${this.sessionId}`;

      console.log('🔌 [WebSocket] Connecting to:', wsUrl);

      this.ws = new WebSocket(wsUrl);

      // Set up event handlers
      this.ws.onopen = this.handleOpen.bind(this);
      this.ws.onmessage = this.handleMessage.bind(this);
      this.ws.onclose = this.handleClose.bind(this);
      this.ws.onerror = this.handleError.bind(this);

      // Wait for connection to be established
      return new Promise((resolve) => {
        const timeout = setTimeout(() => {
          this.isConnecting = false;
          resolve(false);
        }, 10000); // 10 second timeout

        this.ws!.onopen = () => {
          clearTimeout(timeout);
          this.handleOpen();
          this.isConnecting = false;
          resolve(true);
        };

        this.ws!.onerror = () => {
          clearTimeout(timeout);
          this.isConnecting = false;
          resolve(false);
        };
      });

    } catch (error) {
      console.error('🔌 [WebSocket] Connection error:', error);
      this.isConnecting = false;
      return false;
    }
  }

  /**
   * Disconnect from the WebSocket server.
   */
  disconnect(): void {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }

    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }

    this.reconnectAttempts = 0;
    console.log('🔌 [WebSocket] Disconnected');
  }

  /**
   * Send a message to the WebSocket server.
   */
  send(message: any): boolean {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      try {
        this.ws.send(JSON.stringify(message));
        return true;
      } catch (error) {
        console.error('🔌 [WebSocket] Send error:', error);
        return false;
      }
    }
    return false;
  }

  /**
   * Request current variable state from the server.
   */
  requestVariables(): boolean {
    return this.send({
      type: 'request_variables',
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Get connection status.
   */
  isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }

  /**
   * Get session ID.
   */
  getSessionId(): string | null {
    return this.sessionId;
  }

  private handleOpen(): void {
    console.log('🔌 [WebSocket] Connected successfully');
    this.reconnectAttempts = 0;
    this.reconnectDelay = 1000;

    // Start ping interval to keep connection alive
    this.pingInterval = setInterval(() => {
      this.send({ type: 'ping', timestamp: new Date().toISOString() });
    }, 30000); // Ping every 30 seconds
  }

  private handleMessage(event: MessageEvent): void {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);
      
      console.log('🔌 [WebSocket] Received message:', message.type, message);

      switch (message.type) {
        case 'connection_established':
          this.sessionId = message.session_id || this.sessionId;
          this.callbacks.onConnectionEstablished?.(this.sessionId!);
          break;

        case 'variable_update':
          if (message.data) {
            this.callbacks.onVariableUpdate?.(message.data as VariableUpdate);
          }
          break;

        case 'execution_progress':
          if (message.data) {
            this.callbacks.onExecutionProgress?.(message.data);
          }
          break;

        case 'error':
          console.error('🔌 [WebSocket] Server error:', message.message);
          this.callbacks.onError?.(message.message || 'Unknown server error');
          break;

        case 'pong':
          // Server responded to ping - connection is alive
          break;

        default:
          console.warn('🔌 [WebSocket] Unknown message type:', message.type);
      }

    } catch (error) {
      console.error('🔌 [WebSocket] Message parsing error:', error);
    }
  }

  private handleClose(event: CloseEvent): void {
    console.log('🔌 [WebSocket] Connection closed:', event.code, event.reason);
    
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }

    this.ws = null;
    this.callbacks.onDisconnect?.();

    // Attempt to reconnect if not a normal closure
    if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
      this.attemptReconnect();
    }
  }

  private handleError(event: Event): void {
    console.error('🔌 [WebSocket] Connection error:', event);
    this.callbacks.onError?.('WebSocket connection error');
  }

  private async attemptReconnect(): Promise<void> {
    this.reconnectAttempts++;
    
    console.log(`🔌 [WebSocket] Attempting reconnect ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${this.reconnectDelay}ms`);

    setTimeout(async () => {
      const success = await this.connect();
      
      if (!success) {
        // Exponential backoff
        this.reconnectDelay = Math.min(this.reconnectDelay * 2, 30000);
        
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.attemptReconnect();
        } else {
          console.error('🔌 [WebSocket] Max reconnection attempts reached');
          this.callbacks.onError?.('Failed to reconnect to WebSocket server');
        }
      }
    }, this.reconnectDelay);
  }
}

/**
 * Create a WebSocket connection for variable tracking.
 */
export function createVariableTrackingWebSocket(
  agentId: string, 
  token: string, 
  callbacks: VariableTrackingCallbacks = {}
): VariableTrackingWebSocket {
  return new VariableTrackingWebSocket(agentId, token, callbacks);
}
