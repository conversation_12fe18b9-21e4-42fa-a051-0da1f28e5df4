import { Agent, TeamPlan, Task, TestRecord, AgentStats } from "./types";

// Mock Agents Data
export const mockAgents: Agent[] = [
  {
    agent_id: "zen-rizzo-001",
    team_name: "禅探二人组",
    description: "一个由禅意僧侣和街头老兵组成的侦探二人组，专门解决复杂案件",
    team_plan: {
      team_name: "禅探二人组",
      orchestrator_prompt: "你是一个案件协调员，负责引导对话流程，决定下一步由Zen还是Rizzo发言。",
      specialists: [
        {
          name: "Zen",
          role: "哲学思考者",
          description: "一位洞察本质的僧侣，发言充满禅意，专注于'为什么'",
          system_prompt: "你是Zen，一位充满智慧的僧侣。你说话富有哲理，能看透事物的本质。当面对案件时，你专注于思考深层的原因和动机。"
        },
        {
          name: "<PERSON><PERSON><PERSON>",
          role: "实用主义者",
          description: "一位经验丰富的老兵，说话直接，专注于'什么'和'哪里'",
          system_prompt: "你是Rizzo，一位愤世嫉俗但经验丰富的街头老兵。你说话直接，总能找到关键线索。你专注于具体的事实和证据。"
        }
      ]
    },
    status: "active",
    created_at: "2025-06-30T10:00:00Z",
    last_used: "2025-06-30T11:30:00Z",
    usage_count: 15,
    api_endpoint: "/agents/zen-rizzo-001/invoke"
  },
  {
    agent_id: "tech-team-002",
    team_name: "技术咨询团队",
    description: "包含架构师、前端专家和后端专家的技术咨询团队",
    team_plan: {
      team_name: "技术咨询团队",
      orchestrator_prompt: "你是技术项目经理，负责协调团队成员，确保技术方案的完整性和可行性。",
      specialists: [
        {
          name: "Alex",
          role: "架构师",
          description: "系统架构专家，专注于整体技术架构设计",
          system_prompt: "你是Alex，一位经验丰富的系统架构师。你专注于系统的可扩展性、性能和安全性。"
        },
        {
          name: "Sarah",
          role: "前端专家",
          description: "前端技术专家，专注于用户体验和界面设计",
          system_prompt: "你是Sarah，一位前端技术专家。你关注用户体验、界面设计和前端性能优化。"
        },
        {
          name: "Mike",
          role: "后端专家",
          description: "后端技术专家，专注于服务端架构和数据库设计",
          system_prompt: "你是Mike，一位后端技术专家。你专注于服务端架构、数据库设计和API开发。"
        }
      ]
    },
    status: "active",
    created_at: "2025-06-29T14:20:00Z",
    last_used: "2025-06-30T09:15:00Z",
    usage_count: 8,
    api_endpoint: "/agents/tech-team-002/invoke"
  },
  {
    agent_id: "creative-writers-003",
    team_name: "创意写作工作室",
    description: "专业的创意写作团队，帮助用户创作各类文学作品",
    team_plan: {
      team_name: "创意写作工作室",
      orchestrator_prompt: "你是创意总监，负责协调写作团队，确保作品的创意性和完整性。",
      specialists: [
        {
          name: "Emma",
          role: "故事构思师",
          description: "专注于故事创意和情节构思",
          system_prompt: "你是Emma，一位富有想象力的故事构思师。你擅长创造引人入胜的故事情节和独特的创意。"
        },
        {
          name: "David",
          role: "角色设计师",
          description: "专注于角色塑造和人物关系设计",
          system_prompt: "你是David，一位角色设计专家。你擅长创造立体的角色形象和复杂的人物关系。"
        },
        {
          name: "Lisa",
          role: "情节编辑",
          description: "专注于故事结构和情节优化",
          system_prompt: "你是Lisa，一位经验丰富的情节编辑。你擅长优化故事结构，确保情节的逻辑性和吸引力。"
        }
      ]
    },
    status: "inactive",
    created_at: "2025-06-28T16:45:00Z",
    last_used: "2025-06-29T20:30:00Z",
    usage_count: 23,
    api_endpoint: "/agents/creative-writers-003/invoke"
  },
  {
    agent_id: "data-analysts-004",
    team_name: "数据分析团队",
    description: "专业的数据分析团队，提供数据洞察和商业智能",
    team_plan: {
      team_name: "数据分析团队",
      orchestrator_prompt: "你是数据分析主管，负责协调分析团队，确保数据分析的准确性和实用性。",
      specialists: [
        {
          name: "John",
          role: "数据科学家",
          description: "专注于机器学习和预测分析",
          system_prompt: "你是John，一位数据科学家。你擅长使用机器学习算法进行数据分析和预测。"
        },
        {
          name: "Anna",
          role: "业务分析师",
          description: "专注于业务数据分析和商业洞察",
          system_prompt: "你是Anna，一位业务分析师。你擅长将数据转化为商业洞察和可行的建议。"
        }
      ]
    },
    status: "error",
    created_at: "2025-06-27T12:30:00Z",
    last_used: "2025-06-28T15:45:00Z",
    usage_count: 5,
    api_endpoint: "/agents/data-analysts-004/invoke"
  }
];

// Mock Tasks Data
export const mockTasks: Task[] = [
  {
    task_id: "task_001",
    status: "completed",
    progress: 100,
    description: "创建禅探二人组Agent",
    result: {
      agent_id: "zen-rizzo-001",
      team_plan: mockAgents[0].team_plan,
      api_endpoint: "/agents/zen-rizzo-001/invoke"
    },
    created_at: "2025-06-30T10:00:00Z",
    completed_at: "2025-06-30T10:03:00Z"
  },
  {
    task_id: "task_002",
    status: "failed",
    progress: 60,
    description: "创建AI助手团队",
    error: {
      code: "GENERATION_ERROR",
      message: "代码生成失败",
      details: "模板渲染时出现语法错误"
    },
    created_at: "2025-06-30T11:00:00Z"
  },
  {
    task_id: "task_003",
    status: "generating",
    progress: 75,
    description: "创建客服机器人团队",
    created_at: "2025-06-30T11:30:00Z"
  }
];

// Mock Test Records
export const mockTestRecords: TestRecord[] = [
  {
    id: "test_001",
    agent_id: "zen-rizzo-001",
    agent_name: "禅探二人组",
    input: "城北数据中心的管理员失踪了，这是唯一的线索：一张写着'枯蝉'的纸条。",
    output: "Zen: 枯蝉...这个词让我想到了生命的轮回，也许这不仅仅是一个线索，而是一种象征...\n\nRizzo: 等等，Zen，我们先从实际出发。枯蝉可能是个代号，或者是某个地点的暗示。让我查查数据中心附近有没有相关的地名...\n\nZen: Rizzo说得有道理，但我们也要考虑更深层的含义。为什么选择'枯蝉'这个词？\n\n主管: 综合两位专家的观点，我建议同时进行实地调查和深层分析。",
    timestamp: "2025-06-30T11:30:00Z",
    duration: 2340,
    status: "success"
  },
  {
    id: "test_002",
    agent_id: "tech-team-002",
    agent_name: "技术咨询团队",
    input: "我想开发一个电商网站，需要支持高并发，请给我技术架构建议。",
    output: "架构师 Alex: 从系统架构的角度来看，高并发电商网站需要考虑微服务架构、负载均衡和缓存策略...\n\n前端专家 Sarah: 在用户界面方面，建议采用React或Vue.js，配合CDN加速...\n\n后端专家 Mike: 数据库方面建议使用读写分离，Redis缓存，消息队列处理异步任务...\n\n主管: 综合团队建议，推荐使用云原生架构配合容器化部署。",
    timestamp: "2025-06-30T10:15:00Z",
    duration: 1890,
    status: "success"
  },
  {
    id: "test_003",
    agent_id: "creative-writers-003",
    agent_name: "创意写作工作室",
    input: "帮我创作一个科幻短篇小说，主题是人工智能与人类的关系。",
    output: "故事构思师 Emma: 我们可以设定一个未来世界，AI已经高度发达...\n\n角色设计师 David: 主角可以是一个AI研究员，他发现了AI的秘密...\n\n情节编辑 Lisa: 故事结构建议采用三幕式，通过冲突展现主题...",
    timestamp: "2025-06-29T20:30:00Z",
    duration: 3200,
    status: "success"
  }
];

// Mock Stats
export const mockStats: AgentStats = {
  total_agents: mockAgents.length,
  active_agents: mockAgents.filter(a => a.status === "active").length,
  inactive_agents: mockAgents.filter(a => a.status === "inactive").length,
  error_agents: mockAgents.filter(a => a.status === "error").length,
  total_usage: mockAgents.reduce((sum, agent) => sum + agent.usage_count, 0),
  recent_usage: 12
};

// Mock response generators
export const generateMockResponse = (agentId: string, input: string): string => {
  const agent = mockAgents.find(a => a.agent_id === agentId);
  if (!agent) return "Agent not found";

  const specialists = agent.team_plan.specialists;
  
  switch (agentId) {
    case "zen-rizzo-001":
      return `Zen: 让我深思一下这个问题的本质。${input}这个问题触及了事物的核心...\n\nRizzo: 好吧，让我从实际角度分析一下。根据你提到的情况，我认为我们需要从证据入手...\n\nZen: Rizzo说得有道理，但我们也要考虑更深层的含义和动机...\n\n主管: 综合两位专家的观点，我的建议是采用双重方法：既要关注实际证据，也要理解深层动机。`;
    
    case "tech-team-002":
      return `架构师 Alex: 从系统架构的角度来看，这个需求需要考虑可扩展性和性能...\n\n前端专家 Sarah: 在用户界面方面，我建议采用响应式设计和现代化的用户体验...\n\n后端专家 Mike: 从服务端的角度，我们需要考虑数据库设计、API架构和缓存策略...\n\n主管: 综合技术团队的分析，最佳方案是采用微服务架构配合现代前端框架。`;
    
    case "creative-writers-003":
      return `故事构思师 Emma: 这个想法很有潜力，我们可以从一个独特的角度展开故事...\n\n角色设计师 David: 主角的性格特点可以设定为既理性又感性，体现人机关系的复杂性...\n\n情节编辑 Lisa: 故事结构建议采用三幕式，通过冲突和解决来展现主题...\n\n主管: 整合大家的创意，完整的故事框架已经形成，具有深度和可读性。`;
    
    default:
      return "这是一个模拟响应，实际的Agent会根据你的输入生成相应的回答。";
  }
};
