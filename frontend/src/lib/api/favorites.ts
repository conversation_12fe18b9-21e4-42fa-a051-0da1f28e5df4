/**
 * API client for agent favorites functionality
 */

import { api } from '@/lib/api';
import {
  FavoriteAgentResponse,
  ToggleFavoriteResponse,
  FavoritesApiClient,
  FavoriteToggleError,
  FavoritesError
} from '@/types/favorites';
import { ApiResponse } from '@/lib/types';

class FavoritesApiClientImpl implements FavoritesApiClient {
  /**
   * Toggle favorite status for an agent
   */
  async toggleFavorite(agentId: string): Promise<ToggleFavoriteResponse> {
    try {
      const response = await api.agents.toggleFavorite(agentId);

      if (!response.success || !response.data) {
        throw new FavoriteToggleError(
          agentId,
          new Error(response.error?.message || 'Failed to toggle favorite')
        );
      }

      return response.data;
    } catch (error) {
      console.error('Failed to toggle favorite:', error);
      throw new FavoriteToggleError(agentId, error as Error);
    }
  }

  /**
   * Get user's favorite agents with optional performance metrics
   */
  async getFavorites(includePerformance: boolean = true): Promise<FavoriteAgentResponse[]> {
    try {
      const response = await api.agents.getFavorites(includePerformance);

      if (!response.success || !response.data) {
        throw new FavoritesError(
          response.error?.message || 'Failed to fetch favorites'
        );
      }

      return response.data;
    } catch (error) {
      console.error('Failed to fetch favorites:', error);
      throw new FavoritesError(
        error instanceof Error ? error.message : 'Failed to fetch favorites'
      );
    }
  }

  /**
   * Check if an agent is favorited by the current user
   */
  async isFavorite(agentId: string): Promise<boolean> {
    try {
      const favorites = await this.getFavorites(false);
      return favorites.some(fav => fav.agent_id === agentId);
    } catch (error) {
      console.error('Failed to check favorite status:', error);
      return false;
    }
  }

  /**
   * Get favorite ID for a specific agent
   */
  async getFavoriteId(agentId: string): Promise<number | undefined> {
    try {
      const favorites = await this.getFavorites(false);
      const favorite = favorites.find(fav => fav.agent_id === agentId);
      return favorite?.favorite_id;
    } catch (error) {
      console.error('Failed to get favorite ID:', error);
      return undefined;
    }
  }

  /**
   * Batch toggle favorites for multiple agents
   */
  async batchToggleFavorites(agentIds: string[]): Promise<ToggleFavoriteResponse[]> {
    const results: ToggleFavoriteResponse[] = [];
    const errors: Error[] = [];

    // Process in parallel with error handling
    const promises = agentIds.map(async (agentId) => {
      try {
        const result = await this.toggleFavorite(agentId);
        results.push(result);
      } catch (error) {
        errors.push(error as Error);
      }
    });

    await Promise.allSettled(promises);

    if (errors.length > 0) {
      console.warn(`${errors.length} favorites failed to toggle:`, errors);
    }

    return results;
  }

  /**
   * Get favorites count for the current user
   */
  async getFavoritesCount(): Promise<number> {
    try {
      const favorites = await this.getFavorites(false);
      return favorites.length;
    } catch (error) {
      console.error('Failed to get favorites count:', error);
      return 0;
    }
  }

  /**
   * Get recently favorited agents
   */
  async getRecentlyFavorited(limit: number = 5): Promise<FavoriteAgentResponse[]> {
    try {
      const favorites = await this.getFavorites(true);
      return favorites
        .sort((a, b) => new Date(b.favorited_at).getTime() - new Date(a.favorited_at).getTime())
        .slice(0, limit);
    } catch (error) {
      console.error('Failed to get recently favorited agents:', error);
      return [];
    }
  }

  /**
   * Get most used favorite agents
   */
  async getMostUsedFavorites(limit: number = 5): Promise<FavoriteAgentResponse[]> {
    try {
      const favorites = await this.getFavorites(true);
      return favorites
        .sort((a, b) => b.usage_count - a.usage_count)
        .slice(0, limit);
    } catch (error) {
      console.error('Failed to get most used favorites:', error);
      return [];
    }
  }

  /**
   * Search favorites by name or description
   */
  async searchFavorites(query: string): Promise<FavoriteAgentResponse[]> {
    try {
      const favorites = await this.getFavorites(true);
      const lowercaseQuery = query.toLowerCase();
      
      return favorites.filter(favorite => 
        favorite.name.toLowerCase().includes(lowercaseQuery) ||
        favorite.description.toLowerCase().includes(lowercaseQuery)
      );
    } catch (error) {
      console.error('Failed to search favorites:', error);
      return [];
    }
  }

  /**
   * Filter favorites by status
   */
  async filterFavoritesByStatus(status: string): Promise<FavoriteAgentResponse[]> {
    try {
      const favorites = await this.getFavorites(true);
      return favorites.filter(favorite => favorite.status === status);
    } catch (error) {
      console.error('Failed to filter favorites by status:', error);
      return [];
    }
  }

  /**
   * Get favorites with performance above threshold
   */
  async getHighPerformingFavorites(
    successRateThreshold: number = 90
  ): Promise<FavoriteAgentResponse[]> {
    try {
      const favorites = await this.getFavorites(true);
      return favorites.filter(favorite => 
        favorite.performance && 
        favorite.performance.success_rate >= successRateThreshold
      );
    } catch (error) {
      console.error('Failed to get high performing favorites:', error);
      return [];
    }
  }
}

// Export singleton instance
export const favoritesApi = new FavoritesApiClientImpl();

// Export for testing
export { FavoritesApiClientImpl };

// Helper functions
export const favoritesHelpers = {
  /**
   * Format favorited date for display
   */
  formatFavoritedDate: (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return '刚刚收藏';
    if (diffInHours < 24) return `${diffInHours}小时前收藏`;
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}天前收藏`;
    return date.toLocaleDateString('zh-CN');
  },

  /**
   * Get performance badge color based on success rate
   */
  getPerformanceBadgeColor: (successRate: number): string => {
    if (successRate >= 95) return 'green';
    if (successRate >= 85) return 'blue';
    if (successRate >= 70) return 'yellow';
    return 'red';
  },

  /**
   * Calculate performance score
   */
  calculatePerformanceScore: (performance: FavoriteAgentResponse['performance']): number => {
    if (!performance) return 0;
    
    const successWeight = 0.4;
    const speedWeight = 0.3;
    const reliabilityWeight = 0.3;
    
    const successScore = performance.success_rate;
    const speedScore = Math.max(0, 100 - (performance.avg_response_time / 1000) * 10);
    const reliabilityScore = Math.min(100, (performance.execution_count / 10) * 10);
    
    return Math.round(
      successScore * successWeight +
      speedScore * speedWeight +
      reliabilityScore * reliabilityWeight
    );
  }
};
