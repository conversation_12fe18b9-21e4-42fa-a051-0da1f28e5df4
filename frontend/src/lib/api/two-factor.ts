/**
 * Two-Factor Authentication API client
 */

import { apiClient } from '../api-client';

export interface TwoFactorSetupRequest {
  password: string;
}

export interface TwoFactorSetupResponse {
  secret: string;
  qr_code_url: string;
  backup_codes: string[];
}

export interface TwoFactorEnableRequest {
  totp_code: string;
}

export interface TwoFactorDisableRequest {
  password: string;
  totp_code?: string;
  backup_code?: string;
}

export interface TwoFactorVerifyRequest {
  totp_code?: string;
  backup_code?: string;
}

export interface TwoFactorStatusResponse {
  is_enabled: boolean;
  enabled_at: string | null;
  has_backup_codes: boolean;
}

export interface LoginWith2FARequest {
  email: string;
  password: string;
  totp_code?: string;
  backup_code?: string;
  remember_me?: boolean;
}

export interface VerifyLoginRequest {
  temp_session_id: string;
  totp_code?: string;
  backup_code?: string;
  remember_me?: boolean;
}

export class TwoFactorAPI {
  /**
   * Setup 2FA for the current user
   */
  static async setup(data: TwoFactorSetupRequest): Promise<TwoFactorSetupResponse> {
    const response = await apiClient.request<TwoFactorSetupResponse>('/api/v1/2fa/setup', {
      method: 'POST',
      body: JSON.stringify(data),
    });
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data!;
  }

  /**
   * Enable 2FA after setup
   */
  static async enable(data: TwoFactorEnableRequest): Promise<{ message: string }> {
    const response = await apiClient.request<{ message: string }>('/api/v1/2fa/enable', {
      method: 'POST',
      body: JSON.stringify(data),
    });
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data!;
  }

  /**
   * Disable 2FA
   */
  static async disable(data: TwoFactorDisableRequest): Promise<{ message: string }> {
    const response = await apiClient.request<{ message: string }>('/api/v1/2fa/disable', {
      method: 'POST',
      body: JSON.stringify(data),
    });
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data!;
  }

  /**
   * Verify 2FA code
   */
  static async verify(data: TwoFactorVerifyRequest): Promise<{ valid: boolean }> {
    const response = await apiClient.request<{ valid: boolean }>('/api/v1/2fa/verify', {
      method: 'POST',
      body: JSON.stringify(data),
    });
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data!;
  }

  /**
   * Get 2FA status
   */
  static async getStatus(): Promise<TwoFactorStatusResponse> {
    const response = await apiClient.request<TwoFactorStatusResponse>('/api/v1/2fa/status');
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data!;
  }

  /**
   * Get QR code image
   */
  static async getQRCode(): Promise<Blob> {
    // For blob responses, we need to use fetch directly
    const url = `${apiClient.baseURL}/api/v1/2fa/qr-code`;
    const token = apiClient.authToken;

    const headers: HeadersInit = {};
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    const response = await fetch(url, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      throw new Error(`Failed to get QR code: ${response.statusText}`);
    }

    return response.blob();
  }

  /**
   * Login with 2FA
   */
  static async loginWith2FA(data: LoginWith2FARequest): Promise<any> {
    const response = await apiClient.request<any>('/api/v1/auth/login-2fa', {
      method: 'POST',
      body: JSON.stringify(data),
    });
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data!;
  }

  /**
   * Verify 2FA for login completion
   */
  static async verifyLogin(data: VerifyLoginRequest): Promise<any> {
    const response = await apiClient.request<any>('/api/v1/auth/verify-2fa', {
      method: 'POST',
      body: JSON.stringify(data),
    });
    if (response.error) {
      throw new Error(response.error);
    }

    // If successful, the response should contain tokens - let the API client handle token storage
    if (response.data && response.data.tokens) {
      // Use the API client's token management system instead of manual localStorage
      apiClient.setAuthToken(response.data.tokens.access_token);
    }

    return response.data!;
  }
}

export default TwoFactorAPI;
