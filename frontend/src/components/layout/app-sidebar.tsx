"use client"

import * as React from "react"
import {
  BookOpenIcon,
  BotIcon,
  GalleryVerticalEndIcon,
  Settings2Icon,
  SquareTerminalIcon,
} from "lucide-react"
import { usePathname } from "next/navigation"

import { NavMain } from "@/components/layout/nav-main"
// import { NavProjects } from "@/components/layout/nav-projects"
import { NavUser } from "@/components/layout/nav-user"
import { TeamSwitcher } from "@/components/layout/team-switcher"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar"
import { useAuth } from "@/lib/auth"

// Base navigation data - static to prevent recreation
// Dashboard section removed - Agent Management is now the primary section
const BASE_NAV_ITEMS = [
  {
    title: "Agent管理",
    url: "#",
    icon: BotIcon,
    items: [
      {
        title: "创建Agent",
        url: "/create",
      },
      {
        title: "Agent列表",
        url: "/manage",
      },
      {
        title: "模板库",
        url: "/templates",
      },
    ],
  },
  {
    title: "开发工具",
    url: "#",
    icon: BookOpenIcon,
    items: [
      {
        title: "Agent测试",
        url: "/test",
      },
      {
        title: "测试历史",
        url: "/test-history",
      },
      {
        title: "日志查看",
        url: "/logs",
      },
    ],
  },
]

// Static settings items
const ADMIN_SETTINGS_ITEM = {
  title: "系统配置",
  url: "/settings",
}

const USER_SETTINGS_ITEM = {
  title: "AI密钥",
  url: "/api-keys",
}



export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { user } = useAuth()
  const pathname = usePathname()

  // Check if user is admin
  const isAdmin = user?.role === 'admin'

  // Memoize settings items based on user role
  const settingsItems = React.useMemo(() => {
    return isAdmin
      ? [ADMIN_SETTINGS_ITEM, USER_SETTINGS_ITEM]
      : [USER_SETTINGS_ITEM]
  }, [isAdmin])

  // Memoize navigation items
  const navItems = React.useMemo(() => {
    const settingsSection = {
      title: "设置",
      url: "#",
      icon: Settings2Icon,
      items: settingsItems,
    }
    return [...BASE_NAV_ITEMS, settingsSection]
  }, [settingsItems])

  // Memoize navigation data - remove active state calculation as NavMain handles this
  const data = React.useMemo(() => {
    return {
      teams: [
        {
          name: "Meta-Agent",
          logo: GalleryVerticalEndIcon,
          plan: "Enterprise",
        },
      ],
      navMain: navItems,
      projects: [],
    }
  }, [navItems])

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <TeamSwitcher teams={data.teams} />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        {/* NavProjects removed - will be added back when we have actual Agent projects */}
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
