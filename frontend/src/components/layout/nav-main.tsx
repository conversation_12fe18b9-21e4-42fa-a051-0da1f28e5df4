"use client"

import { ChevronRightIcon, type LucideIcon } from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useState, useEffect, useMemo } from "react"

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar"

export function NavMain({
  items,
}: {
  items: {
    title: string
    url: string
    icon?: LucideIcon
    isActive?: boolean
    items?: {
      title: string
      url: string
    }[]
  }[]
}) {
  const pathname = usePathname()

  // State to track which sections are open
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({})

  // Calculate active items using useMemo to prevent recreation on every render
  // Treat "/" and "/create" as equivalent since "/" redirects to "/create"
  const activeItems = useMemo(() => {
    return items.reduce((acc, item) => {
      acc[item.title] = item.items?.some(subItem => {
        // Special case: treat "/" and "/create" as the same for active state
        if (subItem.url === "/create" && (pathname === "/" || pathname === "/create")) {
          return true
        }
        return subItem.url === pathname
      }) || false
      return acc
    }, {} as Record<string, boolean>)
  }, [items, pathname])

  // Update open sections when active items change
  useEffect(() => {
    setOpenSections(prev => {
      const newSections = { ...prev }
      let hasChanges = false

      // Only update sections that have active items and aren't already open
      Object.keys(activeItems).forEach(key => {
        if (activeItems[key] && !prev[key]) {
          newSections[key] = true
          hasChanges = true
        }
      })

      return hasChanges ? newSections : prev
    })
  }, [activeItems])

  return (
    <SidebarGroup>
      <SidebarGroupLabel>Platform</SidebarGroupLabel>
      <SidebarMenu>
        {items.map((item) => {
          // Use pre-calculated active state
          const hasActiveSubItem = activeItems[item.title] || false
          const isOpen = openSections[item.title] || false

          return (
            <Collapsible
              key={item.title}
              asChild
              open={isOpen}
              onOpenChange={(open) => {
                setOpenSections(prev => ({
                  ...prev,
                  [item.title]: open
                }))
              }}
              className="group/collapsible"
            >
              <SidebarMenuItem>
                <CollapsibleTrigger asChild>
                  <SidebarMenuButton
                    tooltip={item.title}
                    isActive={hasActiveSubItem}
                  >
                    {item.icon && <item.icon />}
                    <span>{item.title}</span>
                    <ChevronRightIcon className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                  </SidebarMenuButton>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <SidebarMenuSub>
                    {item.items?.map((subItem) => {
                      // Special case: treat "/" and "/create" as the same for active state
                      const isSubItemActive = subItem.url === "/create"
                        ? (pathname === "/" || pathname === "/create")
                        : pathname === subItem.url
                      return (
                        <SidebarMenuSubItem key={subItem.title}>
                          <SidebarMenuSubButton
                            asChild
                            isActive={isSubItemActive}
                          >
                            <Link href={subItem.url}>
                              <span>{subItem.title}</span>
                            </Link>
                          </SidebarMenuSubButton>
                        </SidebarMenuSubItem>
                      )
                    })}
                  </SidebarMenuSub>
                </CollapsibleContent>
              </SidebarMenuItem>
            </Collapsible>
          )
        })}
      </SidebarMenu>
    </SidebarGroup>
  )
}
