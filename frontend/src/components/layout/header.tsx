"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { NotificationCenter } from "@/components/common/notification-center";
import { KeyboardShortcuts, useKeyboardShortcuts } from "@/components/common/keyboard-shortcuts";
import { ThemeToggle } from "@/components/ui/theme-toggle";

const navigation = [
  { name: "创建Agent", href: "/create", icon: "🤖" },
  { name: "Agent管理", href: "/manage", icon: "📋" },
  { name: "测试中心", href: "/test", icon: "🧪" },
];

export function Header() {
  const pathname = usePathname();
  const shortcuts = useKeyboardShortcuts();

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center">
        {/* Logo */}
        <div className="mr-8">
          <Link href="/" className="flex items-center space-x-2">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
              <span className="text-sm font-bold">M</span>
            </div>
            <span className="font-bold">Meta-Agent</span>
            <Badge variant="secondary" className="ml-2">
              Beta
            </Badge>
          </Link>
        </div>

        {/* Navigation */}
        <nav className="flex items-center space-x-6 text-sm font-medium">
          {navigation.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                "flex items-center space-x-2 transition-colors hover:text-foreground/80",
                pathname === item.href
                  ? "text-foreground"
                  : "text-foreground/60"
              )}
            >
              <span>{item.icon}</span>
              <span>{item.name}</span>
            </Link>
          ))}
        </nav>

        {/* Right side */}
        <div className="ml-auto flex items-center space-x-2">
          <KeyboardShortcuts shortcuts={shortcuts} />
          <NotificationCenter />
          <ThemeToggle />
          <Button variant="outline" size="sm">
            📚 文档
          </Button>
          <Button size="sm" asChild>
            <Link href="/create">
              🤖 新建Agent
            </Link>
          </Button>
        </div>
      </div>
    </header>
  );
}
