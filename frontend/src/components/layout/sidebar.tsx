"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";

const sidebarItems = [
  {
    title: "概览",
    items: [
      { name: "仪表板", href: "/", icon: "📊" },
    ],
  },
  {
    title: "Agent管理",
    items: [
      { name: "创建Agent", href: "/create", icon: "🤖" },
      { name: "Agent列表", href: "/manage", icon: "📋" },
      { name: "模板库", href: "/templates", icon: "📚" },
    ],
  },
  {
    title: "开发工具",
    items: [
      { name: "Agent测试", href: "/test", icon: "🧪" },
      { name: "日志查看", href: "/logs", icon: "📝" },
    ],
  },
  {
    title: "设置",
    items: [
      { name: "系统配置", href: "/settings", icon: "⚙️" },
      { name: "AI密钥", href: "/api-keys", icon: "🔑" },
    ],
  },
];

interface SidebarProps {
  className?: string;
}

export function Sidebar({ className }: SidebarProps) {
  const pathname = usePathname();

  return (
    <div className={cn("pb-12 w-64", className)}>
      <div className="space-y-4 py-4">
        {sidebarItems.map((section) => (
          <div key={section.title} className="px-3 py-2">
            <h2 className="mb-2 px-4 text-lg font-semibold tracking-tight">
              {section.title}
            </h2>
            <div className="space-y-1">
              {section.items.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    "flex items-center space-x-3 rounded-lg px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground",
                    pathname === item.href
                      ? "bg-accent text-accent-foreground"
                      : "transparent"
                  )}
                >
                  <span className="text-base">{item.icon}</span>
                  <span>{item.name}</span>
                  {item.href === "/create" && (
                    <Badge variant="secondary" className="ml-auto">
                      New
                    </Badge>
                  )}
                </Link>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
