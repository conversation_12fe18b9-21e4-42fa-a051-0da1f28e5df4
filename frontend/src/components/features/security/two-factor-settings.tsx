"use client";

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { 
  Shield, 
  QrCode, 
  Key, 
  AlertTriangle, 
  CheckCircle, 
  Copy,
  Download,
  Eye,
  EyeOff
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import TwoFactorAPI, { TwoFactorStatusResponse } from '@/lib/api/two-factor';
import { useSensitiveOperation } from '@/hooks/use-sensitive-operation';
import { SensitiveOperationDialog } from '@/components/auth/sensitive-operation-dialog';
import { TwoFactorVerificationDialog } from '@/components/auth/two-factor-verification-dialog';

interface TwoFactorSettingsProps {
  className?: string;
}

export function TwoFactorSettings({ className }: TwoFactorSettingsProps) {
  const [status, setStatus] = useState<TwoFactorStatusResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [setupData, setSetupData] = useState<any>(null);
  const [showSetupDialog, setShowSetupDialog] = useState(false);
  const [showDisableDialog, setShowDisableDialog] = useState(false);
  const [password, setPassword] = useState('');
  const [totpCode, setTotpCode] = useState('');
  const [backupCode, setBackupCode] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  // Sensitive operation for 2FA disable
  const disableTwoFactorOperation = useSensitiveOperation(
    async (data) => {
      return await TwoFactorAPI.disable({
        password: data.password,
        totp_code: data.totp_code,
        backup_code: data.backup_code,
      });
    },
    {
      operationName: 'disable two-factor authentication',
      requiresConfirmation: true,
      confirmationMessage: 'Are you sure you want to disable two-factor authentication? This will make your account less secure.',
      onSuccess: () => {
        toast({
          title: "成功",
          description: "双因素认证已禁用",
        });
        setShowDisableDialog(false);
        resetSetupState();
        loadStatus();
      },
      onError: (error) => {
        toast({
          title: "错误",
          description: error,
          variant: "destructive",
        });
      },
    }
  );
  const [qrCodeUrl, setQrCodeUrl] = useState<string | null>(null);
  const [step, setStep] = useState<'password' | 'qr' | 'verify'>('password');
  const { toast } = useToast();

  useEffect(() => {
    loadStatus();
  }, []);

  const loadStatus = async () => {
    try {
      setLoading(true);
      const statusData = await TwoFactorAPI.getStatus();
      setStatus(statusData);
    } catch (error) {
      console.error('Failed to load 2FA status:', error);
      toast({
        title: "错误",
        description: "无法加载双因素认证状态",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSetup = async () => {
    if (step === 'password') {
      try {
        const data = await TwoFactorAPI.setup({ password });
        setSetupData(data);
        
        // Generate QR code image URL
        const qrBlob = await TwoFactorAPI.getQRCode();
        const qrUrl = URL.createObjectURL(qrBlob);
        setQrCodeUrl(qrUrl);
        
        setStep('qr');
      } catch (error: any) {
        toast({
          title: "设置失败",
          description: error.response?.data?.detail || "无法设置双因素认证",
          variant: "destructive",
        });
      }
    } else if (step === 'verify') {
      try {
        await TwoFactorAPI.enable({ totp_code: totpCode });
        toast({
          title: "成功",
          description: "双因素认证已启用",
        });
        setShowSetupDialog(false);
        resetSetupState();
        loadStatus();
      } catch (error: any) {
        toast({
          title: "验证失败",
          description: error.response?.data?.detail || "验证码无效",
          variant: "destructive",
        });
      }
    }
  };

  const handleDisable = async () => {
    await disableTwoFactorOperation.executeOperation({
      password,
      totp_code: totpCode || undefined,
      backup_code: backupCode || undefined,
    });
  };

  const resetSetupState = () => {
    setPassword('');
    setTotpCode('');
    setBackupCode('');
    setSetupData(null);
    setQrCodeUrl(null);
    setStep('password');
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "已复制",
      description: "内容已复制到剪贴板",
    });
  };

  const downloadBackupCodes = () => {
    if (!setupData?.backup_codes) return;
    
    const content = setupData.backup_codes.join('\n');
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'backup-codes.txt';
    a.click();
    URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            双因素认证
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          双因素认证
        </CardTitle>
        <CardDescription>
          为您的账户添加额外的安全保护层
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Status Section */}
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <span className="font-medium">状态</span>
              <Badge variant={status?.is_enabled ? "default" : "secondary"}>
                {status?.is_enabled ? "已启用" : "未启用"}
              </Badge>
            </div>
            {status?.enabled_at && (
              <p className="text-sm text-muted-foreground">
                启用时间: {new Date(status.enabled_at).toLocaleString()}
              </p>
            )}
          </div>
          <Switch
            checked={status?.is_enabled || false}
            onCheckedChange={(checked) => {
              if (checked) {
                setShowSetupDialog(true);
              } else {
                setShowDisableDialog(true);
              }
            }}
          />
        </div>

        {status?.is_enabled && (
          <>
            <Separator />
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="text-sm">双因素认证已激活</span>
              </div>
              {status.has_backup_codes && (
                <div className="flex items-center gap-2">
                  <Key className="h-4 w-4 text-blue-500" />
                  <span className="text-sm">备用代码已生成</span>
                </div>
              )}
            </div>
          </>
        )}

        {!status?.is_enabled && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              启用双因素认证可以显著提高您账户的安全性。即使密码被泄露，攻击者也无法访问您的账户。
            </AlertDescription>
          </Alert>
        )}

        {/* Setup Dialog */}
        <Dialog open={showSetupDialog} onOpenChange={setShowSetupDialog}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>设置双因素认证</DialogTitle>
              <DialogDescription>
                {step === 'password' && "请输入您的密码以继续"}
                {step === 'qr' && "使用认证应用扫描二维码"}
                {step === 'verify' && "输入认证应用中的验证码"}
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              {step === 'password' && (
                <div className="space-y-2">
                  <Label htmlFor="setup-password">密码</Label>
                  <div className="relative">
                    <Input
                      id="setup-password"
                      type={showPassword ? "text" : "password"}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      placeholder="输入您的密码"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>
              )}

              {step === 'qr' && setupData && (
                <div className="space-y-4">
                  <div className="text-center">
                    {qrCodeUrl && (
                      <img 
                        src={qrCodeUrl} 
                        alt="QR Code" 
                        className="mx-auto border rounded-lg"
                      />
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <Label>手动输入密钥</Label>
                    <div className="flex items-center gap-2">
                      <Input
                        value={setupData.secret}
                        readOnly
                        className="font-mono text-sm"
                      />
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyToClipboard(setupData.secret)}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>备用代码</Label>
                    <div className="bg-muted p-3 rounded-lg space-y-1">
                      {setupData.backup_codes.map((code: string, index: number) => (
                        <div key={index} className="font-mono text-sm">
                          {code}
                        </div>
                      ))}
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={downloadBackupCodes}
                      className="w-full"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      下载备用代码
                    </Button>
                  </div>
                </div>
              )}

              {step === 'verify' && (
                <div className="space-y-2">
                  <Label htmlFor="verify-code">验证码</Label>
                  <Input
                    id="verify-code"
                    value={totpCode}
                    onChange={(e) => setTotpCode(e.target.value)}
                    placeholder="输入6位验证码"
                    maxLength={6}
                  />
                </div>
              )}

              <div className="flex gap-2">
                {step !== 'password' && (
                  <Button
                    variant="outline"
                    onClick={() => {
                      if (step === 'verify') setStep('qr');
                      else if (step === 'qr') setStep('password');
                    }}
                  >
                    上一步
                  </Button>
                )}
                <Button
                  onClick={() => {
                    if (step === 'qr') setStep('verify');
                    else handleSetup();
                  }}
                  disabled={
                    (step === 'password' && !password) ||
                    (step === 'verify' && totpCode.length !== 6)
                  }
                  className="flex-1"
                >
                  {step === 'password' && "继续"}
                  {step === 'qr' && "下一步"}
                  {step === 'verify' && "启用"}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {/* Disable Dialog */}
        <Dialog open={showDisableDialog} onOpenChange={setShowDisableDialog}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>禁用双因素认证</DialogTitle>
              <DialogDescription>
                请输入密码和验证码以禁用双因素认证
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="disable-password">密码</Label>
                <Input
                  id="disable-password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="输入您的密码"
                />
              </div>

              <div className="space-y-2">
                <Label>验证方式</Label>
                <div className="space-y-2">
                  <Input
                    value={totpCode}
                    onChange={(e) => setTotpCode(e.target.value)}
                    placeholder="认证应用验证码 (6位数字)"
                    maxLength={6}
                  />
                  <div className="text-center text-sm text-muted-foreground">或</div>
                  <Input
                    value={backupCode}
                    onChange={(e) => setBackupCode(e.target.value)}
                    placeholder="备用代码 (8位字符)"
                    maxLength={8}
                  />
                </div>
              </div>

              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowDisableDialog(false)}
                  className="flex-1"
                >
                  取消
                </Button>
                <Button
                  onClick={handleDisable}
                  disabled={!password || (!totpCode && !backupCode)}
                  variant="destructive"
                  className="flex-1"
                >
                  禁用
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {/* Sensitive Operation Dialogs */}
        <SensitiveOperationDialog
          open={disableTwoFactorOperation.showConfirmation}
          onOpenChange={() => {}}
          onConfirm={disableTwoFactorOperation.confirmOperation}
          onCancel={disableTwoFactorOperation.cancelOperation}
          loading={disableTwoFactorOperation.isLoading}
          title="Confirm Disable Two-Factor Authentication"
          description={disableTwoFactorOperation.confirmationMessage}
          confirmText="Disable 2FA"
          variant="destructive"
        />

        <TwoFactorVerificationDialog
          open={disableTwoFactorOperation.twoFactorVerification.state.isOpen}
          onOpenChange={disableTwoFactorOperation.twoFactorVerification.actions.closeVerification}
          onVerify={disableTwoFactorOperation.twoFactorVerification.actions.verify}
          loading={disableTwoFactorOperation.twoFactorVerification.state.loading}
          error={disableTwoFactorOperation.twoFactorVerification.state.error}
          title="Verify Disable Two-Factor Authentication"
          description="Enter your verification code to confirm disabling two-factor authentication"
        />
      </CardContent>
    </Card>
  );
}
