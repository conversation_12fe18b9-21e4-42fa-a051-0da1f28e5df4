"use client";

import { Bad<PERSON> } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { StreamingMarkdownRenderer } from "@/components/ui/markdown-renderer";
import { JsonFormatter } from "@/components/ui/json-formatter";
import { 
  FileText, Clock, Settings, BarChart3, ChevronDown, ChevronUp,
  Cpu, Key, Globe, DollarSign, Timer, Zap, Copy, Variable
} from "lucide-react";
import { TestHistoryDetailResponse } from "@/lib/api/test-history";
import { useState } from "react";

interface UnifiedTestDetailDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  testDetail: TestHistoryDetailResponse | null;
  title?: string;
}

interface VariablePlaceholder {
  name: string;
  placeholder: string;
  variable_type: string;
  source_agent?: string;
  destination_agents: string[];
  semantic_description: string;
  workflow_step?: number;
  is_required: boolean;
  example_value?: string;
  resolved_value?: string;
  generated_content?: string; // 新增：生成的内容
  resolution_status?: 'resolved' | 'pending' | 'failed';
  resolved_at?: string;
}

interface TeamMemberInteraction {
  source_agent: string;
  destination_agent: string;
  variable_name: string;
  variable_value: string;
  step_index: number;
  timestamp: string;
  interaction_type: string;
}

export function UnifiedTestDetailDialog({ 
  open, 
  onOpenChange, 
  testDetail, 
  title = "测试详情" 
}: UnifiedTestDetailDialogProps) {
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    execution: true,
    variables: true,
    config: true,
    stats: true,
    time: true
  });

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const getStatusConfig = (status: string) => {
    switch (status) {
      case "completed":
        return { label: "已完成", color: "text-green-600", bgColor: "bg-green-50 dark:bg-green-950/30" };
      case "running":
        return { label: "运行中", color: "text-blue-600", bgColor: "bg-blue-50 dark:bg-blue-950/30" };
      case "failed":
        return { label: "失败", color: "text-red-600", bgColor: "bg-red-50 dark:bg-red-950/30" };
      case "cancelled":
        return { label: "已取消", color: "text-gray-600", bgColor: "bg-gray-50 dark:bg-gray-950/30" };
      default:
        return { label: status, color: "text-gray-600", bgColor: "bg-gray-50 dark:bg-gray-950/30" };
    }
  };

  const formatDuration = (ms: number, compact = false) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    if (ms < 3600000) return `${(ms / 60000).toFixed(1)}m`;
    return `${(ms / 3600000).toFixed(1)}h`;
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  if (!testDetail) return null;

  const statusConfig = getStatusConfig(testDetail.status);
  
  // Parse variable placeholders data
  const variablePlaceholders: VariablePlaceholder[] = testDetail.context_placeholders_used || [];
  const teamInteractions: TeamMemberInteraction[] = testDetail.team_member_interactions || [];

  // Group variables by type
  const variablesByType = variablePlaceholders.reduce((acc, variable) => {
    const type = variable.variable_type || 'unknown';
    if (!acc[type]) acc[type] = [];
    acc[type].push(variable);
    return acc;
  }, {} as Record<string, VariablePlaceholder[]>);

  const getVariableTypeLabel = (type: string) => {
    switch (type) {
      case 'user-input': return '用户输入';
      case 'inter-agent': return '团队间变量';
      case 'system': return '系统变量';
      case 'output': return '输出变量';
      default: return type;
    }
  };

  const getVariableTypeColor = (type: string) => {
    switch (type) {
      case 'user-input': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';
      case 'inter-agent': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300';
      case 'system': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
      case 'output': return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[calc(100%-2rem)] w-[95vw] sm:max-w-4xl md:max-w-6xl lg:max-w-7xl xl:w-[85vw] xl:max-w-none max-h-[90vh] overflow-hidden flex flex-col mobile-modal">
        <DialogHeader className="shrink-0">
          <DialogTitle className="flex items-center gap-2 text-base md:text-lg">
            📝 {title}
            <Badge variant={testDetail.status === "completed" ? "default" : "destructive"} className="mobile-text-sm">
              {statusConfig.label}
            </Badge>
          </DialogTitle>
          <DialogDescription className="text-sm mobile-text-sm">
            测试ID: {testDetail.test_id}
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-6">
          {/* 1. 执行详情 (第一位) */}
          <Collapsible open={expandedSections.execution} onOpenChange={() => toggleSection('execution')}>
            <CollapsibleTrigger className="flex items-center justify-between w-full p-0 hover:no-underline">
              <div className="flex items-center space-x-2 pb-2 border-b border-border/50">
                <FileText className="h-4 w-4 text-muted-foreground" />
                <h4 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">执行详情</h4>
              </div>
              {expandedSections.execution ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-4 pt-4">
              {/* Input Content */}
              <div className="space-y-2">
                <Label className="text-sm font-semibold">输入内容</Label>
                <div className="bg-muted/50 rounded-lg p-4">
                  <p className="text-sm whitespace-pre-wrap">{testDetail.input_text}</p>
                </div>
              </div>

              {/* Output Content */}
              {testDetail.final_output && (
                <div className="space-y-2">
                  <Label className="text-sm font-semibold">执行结果</Label>
                  <div className="bg-muted/30 rounded-lg p-4">
                    <JsonFormatter
                      content={testDetail.final_output}
                      isStreaming={false}
                      className="text-sm"
                    />
                  </div>
                </div>
              )}

              {/* Error Information */}
              {testDetail.error_message && (
                <div className="space-y-2">
                  <Label className="text-sm font-semibold text-red-600">错误信息</Label>
                  <div className="bg-red-50 dark:bg-red-950/30 rounded-lg p-4 border border-red-200 dark:border-red-800">
                    <p className="text-sm text-red-800 dark:text-red-200 whitespace-pre-wrap">
                      {testDetail.error_message}
                    </p>
                  </div>
                </div>
              )}

              {/* Execution Stages */}
              {testDetail.execution_stages && testDetail.execution_stages.length > 0 && (
                <div className="space-y-2">
                  <Label className="text-sm font-semibold">执行阶段</Label>
                  <div className="space-y-2">
                    {testDetail.execution_stages.map((stage, index) => (
                      <div key={index} className="bg-muted/30 rounded-lg p-3 border border-border/50">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium">阶段 {index + 1}</span>
                          {stage.status && (
                            <Badge variant="outline" className="text-xs">
                              {stage.status}
                            </Badge>
                          )}
                        </div>
                        {stage.output && (
                          <JsonFormatter
                            content={typeof stage.output === 'string' ? stage.output : JSON.stringify(stage.output)}
                            isStreaming={false}
                            className="text-xs"
                          />
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CollapsibleContent>
          </Collapsible>

          {/* 2. 变量占位符 (第二位) */}
          <Collapsible open={expandedSections.variables} onOpenChange={() => toggleSection('variables')}>
            <CollapsibleTrigger className="flex items-center justify-between w-full p-0 hover:no-underline">
              <div className="flex items-center space-x-2 pb-2 border-b border-border/50">
                <Variable className="h-4 w-4 text-muted-foreground" />
                <h4 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">变量占位符</h4>
                {variablePlaceholders.length > 0 && (
                  <Badge variant="outline" className="text-xs">
                    {variablePlaceholders.length} 个变量
                  </Badge>
                )}
              </div>
              {expandedSections.variables ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-4 pt-4">
              {variablePlaceholders.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Variable className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">此次执行未发现变量占位符</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Variables by Type */}
                  {Object.entries(variablesByType).map(([type, variables]) => (
                    <div key={type} className="space-y-3">
                      <div className="flex items-center gap-2">
                        <Badge className={getVariableTypeColor(type)}>
                          {getVariableTypeLabel(type)}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {variables.length} 个变量
                        </span>
                      </div>
                      <div className="grid gap-3">
                        {variables.map((variable, index) => (
                          <div key={index} className="bg-muted/30 rounded-lg p-3 border border-border/50">
                            <div className="flex items-start justify-between mb-2">
                              <div className="flex items-center gap-2">
                                <code className="text-sm font-mono bg-muted px-2 py-1 rounded">
                                  {variable.placeholder}
                                </code>
                                {variable.is_required && (
                                  <Badge variant="outline" className="text-xs">必需</Badge>
                                )}
                              </div>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => copyToClipboard(variable.placeholder)}
                                className="h-6 w-6 p-0"
                              >
                                <Copy className="h-3 w-3" />
                              </Button>
                            </div>
                            
                            <p className="text-sm text-muted-foreground mb-2">
                              {variable.semantic_description}
                            </p>
                            
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs">
                              {variable.source_agent && (
                                <div>
                                  <span className="font-medium">来源:</span> {variable.source_agent}
                                </div>
                              )}
                              {variable.destination_agents.length > 0 && (
                                <div>
                                  <span className="font-medium">目标:</span> {variable.destination_agents.join(', ')}
                                </div>
                              )}
                              {variable.workflow_step !== undefined && (
                                <div>
                                  <span className="font-medium">工作流步骤:</span> {variable.workflow_step}
                                </div>
                              )}
                              {variable.resolved_value && (
                                <div className="md:col-span-2">
                                  <span className="font-medium">解析值:</span>
                                  <code className="ml-1 text-xs bg-muted px-1 py-0.5 rounded">
                                    {variable.resolved_value.length > 50
                                      ? variable.resolved_value.substring(0, 50) + '...'
                                      : variable.resolved_value}
                                  </code>
                                </div>
                              )}
                            </div>

                            {/* 生成内容展示 */}
                            {variable.generated_content && (
                              <div className="mt-3 space-y-2">
                                <div className="flex items-center justify-between">
                                  <Label className="text-sm font-semibold text-green-700 dark:text-green-300">生成内容</Label>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => copyToClipboard(variable.generated_content || '')}
                                    className="h-6 px-2 text-xs"
                                  >
                                    <Copy className="h-3 w-3 mr-1" />
                                    复制
                                  </Button>
                                </div>
                                <div className="bg-green-50/50 dark:bg-green-950/20 rounded-lg p-3 border border-green-200/50 dark:border-green-800/30">
                                  <JsonFormatter
                                    content={variable.generated_content}
                                    isStreaming={false}
                                    className="text-xs"
                                  />
                                </div>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}

                  {/* Team Member Interactions */}
                  {teamInteractions.length > 0 && (
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <Badge className="bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300">
                          团队成员交互
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {teamInteractions.length} 次交互
                        </span>
                      </div>
                      <div className="space-y-2">
                        {teamInteractions.map((interaction, index) => (
                          <div key={index} className="bg-orange-50/50 dark:bg-orange-950/20 rounded-lg p-3 border border-orange-200/50 dark:border-orange-800/50">
                            <div className="flex items-center justify-between mb-1">
                              <div className="text-sm font-medium">
                                {interaction.source_agent} → {interaction.destination_agent}
                              </div>
                              <Badge variant="outline" className="text-xs">
                                步骤 {interaction.step_index}
                              </Badge>
                            </div>
                            <div className="text-xs text-muted-foreground">
                              变量: <code className="bg-muted px-1 py-0.5 rounded">{interaction.variable_name}</code>
                            </div>
                            {interaction.variable_value && (
                              <div className="text-xs mt-1">
                                <span className="font-medium">传递值:</span>
                                <code className="ml-1 bg-muted px-1 py-0.5 rounded">
                                  {interaction.variable_value.length > 100 
                                    ? interaction.variable_value.substring(0, 100) + '...' 
                                    : interaction.variable_value}
                                </code>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </CollapsibleContent>
          </Collapsible>

          {/* 3. 配置详情 (第三位) */}
          <Collapsible open={expandedSections.config} onOpenChange={() => toggleSection('config')}>
            <CollapsibleTrigger className="flex items-center justify-between w-full p-0 hover:no-underline">
              <div className="flex items-center space-x-2 pb-2 border-b border-border/50">
                <Settings className="h-4 w-4 text-muted-foreground" />
                <h4 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">配置详情</h4>
              </div>
              {expandedSections.config ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-4 pt-4">
              {/* AI Configuration */}
              {testDetail.ai_config_override && (
                <div className="space-y-3">
                  <h5 className="text-xs font-semibold text-muted-foreground uppercase tracking-wide">AI 模型配置</h5>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                    {testDetail.ai_config_override.provider && (
                      <div className="flex flex-col items-center justify-center p-3 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/30 dark:to-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800/50">
                        <Cpu className="h-4 w-4 text-purple-600 dark:text-purple-400 mb-1" />
                        <div className="text-sm font-bold text-purple-900 dark:text-purple-100">
                          {testDetail.ai_config_override.provider}
                        </div>
                        <div className="text-xs font-medium text-purple-700 dark:text-purple-300">AI 提供商</div>
                      </div>
                    )}

                    {testDetail.ai_config_override.model && (
                      <div className="flex flex-col items-center justify-center p-3 bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-indigo-950/30 dark:to-indigo-900/20 rounded-lg border border-indigo-200 dark:border-indigo-800/50">
                        <Zap className="h-4 w-4 text-indigo-600 dark:text-indigo-400 mb-1" />
                        <div className="text-sm font-bold text-indigo-900 dark:text-indigo-100">
                          {testDetail.ai_config_override.model}
                        </div>
                        <div className="text-xs font-medium text-indigo-700 dark:text-indigo-300">模型</div>
                      </div>
                    )}

                    {testDetail.ai_config_override.temperature !== undefined && (
                      <div className="flex flex-col items-center justify-center p-3 bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950/30 dark:to-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800/50">
                        <Timer className="h-4 w-4 text-orange-600 dark:text-orange-400 mb-1" />
                        <div className="text-sm font-bold text-orange-900 dark:text-orange-100">
                          {testDetail.ai_config_override.temperature}
                        </div>
                        <div className="text-xs font-medium text-orange-700 dark:text-orange-300">温度</div>
                      </div>
                    )}

                    {testDetail.ai_config_override.max_tokens && (
                      <div className="flex flex-col items-center justify-center p-3 bg-gradient-to-br from-teal-50 to-teal-100 dark:from-teal-950/30 dark:to-teal-900/20 rounded-lg border border-teal-200 dark:border-teal-800/50">
                        <BarChart3 className="h-4 w-4 text-teal-600 dark:text-teal-400 mb-1" />
                        <div className="text-sm font-bold text-teal-900 dark:text-teal-100">
                          {testDetail.ai_config_override.max_tokens}
                        </div>
                        <div className="text-xs font-medium text-teal-700 dark:text-teal-300">最大令牌</div>
                      </div>
                    )}

                    {testDetail.ai_config_override.base_url && (
                      <div className="flex flex-col items-center justify-center p-3 bg-gradient-to-br from-cyan-50 to-cyan-100 dark:from-cyan-950/30 dark:to-cyan-900/20 rounded-lg border border-cyan-200 dark:border-cyan-800/50">
                        <Globe className="h-4 w-4 text-cyan-600 dark:text-cyan-400 mb-1" />
                        <div className="text-sm font-bold text-cyan-900 dark:text-cyan-100 truncate max-w-full">
                          {testDetail.ai_config_override.base_url}
                        </div>
                        <div className="text-xs font-medium text-cyan-700 dark:text-cyan-300">基础URL</div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* API Key Information */}
              {testDetail.api_key_name && (
                <div className="space-y-3">
                  <h5 className="text-xs font-semibold text-muted-foreground uppercase tracking-wide">API 密钥</h5>
                  <div className="p-3 bg-muted/30 rounded-lg border border-border/50">
                    <div className="flex items-center gap-2">
                      <Key className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">{testDetail.api_key_name}</span>
                    </div>
                  </div>
                </div>
              )}

              {/* Agent Information */}
              <div className="space-y-3">
                <h5 className="text-xs font-semibold text-muted-foreground uppercase tracking-wide">Agent 信息</h5>
                <div className="p-3 bg-muted/30 rounded-lg border border-border/50">
                  <div className="text-sm">
                    <span className="font-medium">Agent ID:</span> {testDetail.agent_id}
                  </div>
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>

          {/* 4. 统计信息 (第四位) */}
          <Collapsible open={expandedSections.stats} onOpenChange={() => toggleSection('stats')}>
            <CollapsibleTrigger className="flex items-center justify-between w-full p-0 hover:no-underline">
              <div className="flex items-center space-x-2 pb-2 border-b border-border/50">
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
                <h4 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">统计信息</h4>
              </div>
              {expandedSections.stats ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-4 pt-4">
              {/* Performance Metrics Grid */}
              <div className="space-y-3">
                <h5 className="text-xs font-semibold text-muted-foreground uppercase tracking-wide">性能指标</h5>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
                  <div className="flex flex-col items-center justify-center p-3 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800/50">
                    <Clock className="h-4 w-4 text-blue-600 dark:text-blue-400 mb-1" />
                    <div className="text-lg font-bold text-blue-900 dark:text-blue-100">
                      {testDetail.execution_duration_ms ? formatDuration(testDetail.execution_duration_ms) : '未知'}
                    </div>
                    <div className="text-xs font-medium text-blue-700 dark:text-blue-300">执行时间</div>
                  </div>

                  <div className="flex flex-col items-center justify-center p-3 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/20 rounded-lg border border-green-200 dark:border-green-800/50">
                    <FileText className="h-4 w-4 text-green-600 dark:text-green-400 mb-1" />
                    <div className="text-lg font-bold text-green-900 dark:text-green-100">
                      {testDetail.execution_stages?.length || 0}
                    </div>
                    <div className="text-xs font-medium text-green-700 dark:text-green-300">执行阶段</div>
                  </div>

                  <div className="flex flex-col items-center justify-center p-3 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/30 dark:to-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800/50">
                    <Variable className="h-4 w-4 text-purple-600 dark:text-purple-400 mb-1" />
                    <div className="text-lg font-bold text-purple-900 dark:text-purple-100">
                      {variablePlaceholders.length}
                    </div>
                    <div className="text-xs font-medium text-purple-700 dark:text-purple-300">变量占位符</div>
                  </div>

                  <div className="flex flex-col items-center justify-center p-3 bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950/30 dark:to-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800/50">
                    <Zap className="h-4 w-4 text-orange-600 dark:text-orange-400 mb-1" />
                    <div className="text-lg font-bold text-orange-900 dark:text-orange-100">
                      {teamInteractions.length}
                    </div>
                    <div className="text-xs font-medium text-orange-700 dark:text-orange-300">团队交互</div>
                  </div>
                </div>
              </div>

              {/* Response Metadata */}
              {testDetail.response_metadata && (
                <div className="space-y-3">
                  <h5 className="text-xs font-semibold text-muted-foreground uppercase tracking-wide">响应元数据</h5>
                  <div className="bg-muted/30 rounded-lg p-4">
                    <JsonFormatter
                      content={JSON.stringify(testDetail.response_metadata, null, 2)}
                      isStreaming={false}
                      className="text-xs"
                    />
                  </div>
                </div>
              )}
            </CollapsibleContent>
          </Collapsible>

          {/* 5. 时间详情 (第五位) */}
          <Collapsible open={expandedSections.time} onOpenChange={() => toggleSection('time')}>
            <CollapsibleTrigger className="flex items-center justify-between w-full p-0 hover:no-underline">
              <div className="flex items-center space-x-2 pb-2 border-b border-border/50">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <h4 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">时间详情</h4>
              </div>
              {expandedSections.time ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-4 pt-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-3 bg-muted/30 rounded-lg border border-border/50">
                  <Label className="text-xs font-semibold text-muted-foreground uppercase tracking-wide">开始时间</Label>
                  <div className="text-sm font-medium mt-1 text-foreground">
                    {testDetail.started_at ? new Date(testDetail.started_at).toLocaleString('zh-CN') : '未记录'}
                  </div>
                </div>
                <div className="p-3 bg-muted/30 rounded-lg border border-border/50">
                  <Label className="text-xs font-semibold text-muted-foreground uppercase tracking-wide">完成时间</Label>
                  <div className="text-sm font-medium mt-1 text-foreground">
                    {testDetail.completed_at ? new Date(testDetail.completed_at).toLocaleString('zh-CN') : '未完成'}
                  </div>
                </div>
                <div className="p-3 bg-muted/30 rounded-lg border border-border/50">
                  <Label className="text-xs font-semibold text-muted-foreground uppercase tracking-wide">执行持续时间</Label>
                  <div className="text-sm font-medium mt-1 text-foreground">
                    {testDetail.execution_duration_ms ? formatDuration(testDetail.execution_duration_ms) : '未记录'}
                  </div>
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>
        </div>
      </DialogContent>
    </Dialog>
  );
}
