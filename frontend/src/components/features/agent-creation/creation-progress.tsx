"use client";

import { motion } from "framer-motion";
import { Progress } from "@/components/ui/progress";
import { CheckCircle } from "lucide-react";
import { cn } from "@/lib/utils";

type CreationStep = "form" | "planning" | "confirmation" | "generating" | "completed";

interface CreationProgressProps {
  currentStep: CreationStep;
}

const steps = [
  { id: "form", title: "描述需求", description: "告诉我们您想要什么样的AI团队" },
  { id: "planning", title: "AI规划", description: "AI正在为您设计专业团队" },
  { id: "confirmation", title: "确认方案", description: "查看并确认团队配置" },
  { id: "generating", title: "创建中", description: "正在生成您的AI团队" },
  { id: "completed", title: "完成", description: "您的AI团队已准备就绪" },
];

export function CreationProgress({ currentStep }: CreationProgressProps) {
  const currentStepIndex = steps.findIndex(step => step.id === currentStep);
  const progress = ((currentStepIndex + 1) / steps.length) * 100;

  return (
    <motion.div
      className="space-y-4"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.1 }}
    >
      <div className="flex items-center justify-between text-sm">
        <span className="font-medium">创建进度</span>
        <span className="text-muted-foreground">{Math.round(progress)}%</span>
      </div>
      <Progress value={progress} className="h-2" />

      {/* Step Indicators */}
      <div className="flex justify-between">
        {steps.map((step, index) => (
          <div
            key={step.id}
            className={`flex flex-col items-center space-y-2 ${
              index <= currentStepIndex ? "text-primary" : "text-muted-foreground"
            }`}
          >
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${
                index < currentStepIndex
                  ? "bg-primary text-primary-foreground"
                  : index === currentStepIndex
                  ? "bg-primary/20 text-primary border-2 border-primary"
                  : "bg-muted text-muted-foreground"
              }`}
            >
              {index < currentStepIndex ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                index + 1
              )}
            </div>
            <div className="text-center hidden sm:block">
              <div className="font-medium text-xs">{step.title}</div>
              <div className="text-xs text-muted-foreground max-w-20">
                {step.description}
              </div>
            </div>
          </div>
        ))}
      </div>
    </motion.div>
  );
}
