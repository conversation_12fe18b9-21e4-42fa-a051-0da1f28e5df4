"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { motion } from "framer-motion";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Sparkles,
  Lightbulb,
  Target,
} from "lucide-react";


const formSchema = z.object({
  description: z.string().min(10, "描述至少需要10个字符").max(5000, "描述不能超过5000个字符"),
});

type FormData = z.infer<typeof formSchema>;



interface AgentCreationFormProps {
  onSubmit: (data: FormData) => void;
  initialDescription?: string;
  disabled?: boolean;
}

const examplePrompts = [
  {
    title: "内容创作团队",
    description: "我需要一个专业的内容创作团队，能够帮我写文章、制作社交媒体内容和营销文案",
    category: "创作",
  },
  {
    title: "数据分析专家",
    description: "我需要一个数据分析团队，能够处理销售数据、生成报告和提供业务洞察",
    category: "分析",
  },
  {
    title: "客服助手",
    description: "我需要一个智能客服团队，能够回答常见问题、处理投诉和提供技术支持",
    category: "服务",
  },
  {
    title: "产品开发顾问",
    description: "我需要一个产品开发团队，能够帮我进行市场调研、功能设计和用户体验优化",
    category: "开发",
  },
];

export function AgentCreationForm({ onSubmit, initialDescription, disabled }: AgentCreationFormProps) {
  const [selectedExample, setSelectedExample] = useState<number | null>(null);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isSubmitting }
  } = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      description: initialDescription || "",
    }
  });

  const description = watch("description");

  const handleExampleClick = (index: number) => {
    setSelectedExample(index);
    setValue("description", examplePrompts[index].description);
  };

  const onFormSubmit = (data: FormData) => {
    onSubmit(data);
  };

  const isValid = description && description.trim().length >= 10;

  return (
    <div className="space-y-6">
      {/* Main Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5 text-primary" />
            <span>描述您的需求</span>
          </CardTitle>
          <CardDescription>
            详细描述您希望AI团队帮您完成的任务，我们的AI规划师将为您设计最适合的团队配置
          </CardDescription>
        </CardHeader>
        <CardContent>

          <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="description" className="text-base font-medium">
                团队任务描述 *
              </Label>
              <Textarea
                id="description"
                placeholder="例如：我需要一个内容创作团队，能够帮我写高质量的技术博客文章，包括研究主题、撰写内容、优化SEO等..."
                className="min-h-[120px] resize-none"
                maxLength={1000}
                {...register("description")}
              />
              {errors.description && (
                <p className="text-sm text-destructive">{errors.description.message}</p>
              )}
              <div className="flex justify-between items-center text-sm text-muted-foreground">
                <span>
                  {description && description.length < 10 ? "至少需要10个字符" : "描述越详细，AI规划越精准"}
                </span>
                <span>{description?.length || 0}/1000</span>
              </div>
            </div>

            <Button
              type="submit"
              disabled={!isValid || disabled || isSubmitting}
              className="w-full min-h-[44px]"
              size="lg"
            >
              <Sparkles className="h-4 w-4 mr-2" />
              {isSubmitting ? "规划中..." : "开始AI规划"}
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Example Prompts */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Lightbulb className="h-5 w-5 text-primary" />
            <span>灵感示例</span>
          </CardTitle>
          <CardDescription>
            点击下方示例快速开始，或参考这些例子来描述您的需求
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {examplePrompts.map((example, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <Card
                  className={`cursor-pointer hover:shadow-md transition-all duration-200 hover:border-primary/50 ${
                    selectedExample === index ? "border-primary bg-accent" : ""
                  }`}
                  onClick={() => handleExampleClick(index)}
                >
                  <CardContent className="p-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">{example.title}</h4>
                        <Badge variant="secondary" className="text-xs">
                          {example.category}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground line-clamp-3">
                        {example.description}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
