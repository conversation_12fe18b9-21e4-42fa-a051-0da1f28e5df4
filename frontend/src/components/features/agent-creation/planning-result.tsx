"use client";

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface TeamPlan {
  team_name: string;
  description: string;
  objective: string;
  team_members: Array<{
    name: string;
    system_prompt: string;
    role?: string;
    description?: string;
    capabilities?: string[];
    tools?: string[];
  }>;
  workflow?: {
    steps: Array<{
      name: string;
      description: string;
      assignee: string;
      inputs: string[];
      outputs: string[];
    }>;
  };
}

interface PlanningResultProps {
  plan: TeamPlan;
  description: string;
  onConfirm: () => void;
  onReject: () => void;
}

export function PlanningResult({ plan, description, onConfirm, onReject }: PlanningResultProps) {
  return (
    <div className="space-y-6">
      <Alert>
        <span className="text-lg">🎉</span>
        <AlertDescription>
          AI已为您规划好团队，请确认以下设计是否符合您的需求
        </AlertDescription>
      </Alert>

      {/* Original Request */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">📋 原始需求</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground bg-muted p-3 rounded">
            {description}
          </p>
        </CardContent>
      </Card>

      {/* Team Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            🤖 团队概览
            <Badge variant="secondary">AI生成</Badge>
          </CardTitle>
          <CardDescription>
            AI规划师为您设计的团队结构和角色分工
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold text-lg mb-2">团队名称</h3>
            <p className="text-xl font-bold text-primary">{plan.team_name}</p>
          </div>

          <div>
            <h3 className="font-semibold mb-2">团队描述</h3>
            <div className="bg-muted p-3 rounded">
              <p className="text-sm">{plan.description}</p>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">团队目标</h3>
            <div className="bg-muted p-3 rounded">
              <p className="text-sm">{plan.objective}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Team Members */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">👥 团队成员</CardTitle>
          <CardDescription>
            团队中的专业角色，每个成员都有独特的能力和职责
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {plan.team_members?.map((member, index) => (
              <div key={index} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="font-semibold text-lg">{member.name}</h4>
                  {member.role && (
                    <Badge variant="outline">{member.role}</Badge>
                  )}
                </div>

                {member.description && (
                  <p className="text-sm text-muted-foreground">
                    {member.description}
                  </p>
                )}

                {member.capabilities && member.capabilities.length > 0 && (
                  <div>
                    <h5 className="font-medium text-sm mb-2">能力</h5>
                    <div className="flex flex-wrap gap-1">
                      {member.capabilities.map((capability, capIndex) => (
                        <Badge key={capIndex} variant="secondary" className="text-xs">
                          {capability}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {member.tools && member.tools.length > 0 && (
                  <div>
                    <h5 className="font-medium text-sm mb-2">工具</h5>
                    <div className="flex flex-wrap gap-1">
                      {member.tools.map((tool, toolIndex) => (
                        <Badge key={toolIndex} variant="outline" className="text-xs">
                          {tool}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                <div>
                  <h5 className="font-medium text-sm mb-2">系统提示词</h5>
                  <div className="bg-muted p-2 rounded text-xs">
                    {member.system_prompt}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Team Workflow */}
      {plan.workflow && plan.workflow.steps && plan.workflow.steps.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">🔄 工作流程</CardTitle>
            <CardDescription>
              团队成员之间的协作方式
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {plan.workflow.steps.map((step, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">
                    {index + 1}
                  </div>
                  <div className="flex-1">
                    <p className="font-medium">{step.name}</p>
                    <p className="text-sm text-muted-foreground">{step.description}</p>
                    {step.assignee && (
                      <p className="text-xs text-muted-foreground mt-1">
                        负责人: {step.assignee}
                      </p>
                    )}
                    {step.inputs && step.inputs.length > 0 && (
                      <div className="mt-1">
                        <span className="text-xs font-medium">输入: </span>
                        <span className="text-xs text-muted-foreground">
                          {step.inputs.join(', ')}
                        </span>
                      </div>
                    )}
                    {step.outputs && step.outputs.length > 0 && (
                      <div className="mt-1">
                        <span className="text-xs font-medium">输出: </span>
                        <span className="text-xs text-muted-foreground">
                          {step.outputs.join(', ')}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex justify-center space-x-4">
        <Button variant="outline" onClick={onReject} size="lg">
          ❌ 重新规划
        </Button>
        <Button onClick={onConfirm} size="lg">
          ✅ 确认并生成
        </Button>
      </div>
    </div>
  );
}
