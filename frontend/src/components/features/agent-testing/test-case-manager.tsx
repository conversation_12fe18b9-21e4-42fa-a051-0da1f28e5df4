"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Save, Play, Trash2, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Plus, TestTube } from "lucide-react";
import { AIModelOverrideType } from "./ai-model-override";
import { Agent } from "@/lib/types";

export interface TestCase {
  id: string;
  name: string;
  description: string;
  input: string;
  expectedOutput?: string;
  agentId: string;
  agentName: string;
  aiOverride?: AIModelOverrideType;
  tags: string[];
  createdAt: string;
  updatedAt: string;
  lastRunAt?: string;
  lastRunStatus?: "success" | "error";
  lastRunOutput?: string;
}

interface TestCaseManagerProps {
  agents: Agent[];
  currentInput: string;
  currentAiOverride: AIModelOverrideType;
  selectedAgent: Agent | null;
  onLoadTestCase: (testCase: TestCase) => void;
  onRunTestCase: (testCase: TestCase) => void;
}

export function TestCaseManager({ 
  agents, 
  currentInput, 
  currentAiOverride, 
  selectedAgent,
  onLoadTestCase,
  onRunTestCase 
}: TestCaseManagerProps) {
  const [testCases, setTestCases] = useState<TestCase[]>([]);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [showManagerDialog, setShowManagerDialog] = useState(false);
  const [editingCase, setEditingCase] = useState<TestCase | null>(null);
  const [newCase, setNewCase] = useState({
    name: "",
    description: "",
    expectedOutput: "",
    tags: ""
  });

  // Load test cases from localStorage on mount
  useEffect(() => {
    const saved = localStorage.getItem("test-cases");
    if (saved) {
      try {
        setTestCases(JSON.parse(saved));
      } catch (error) {
        console.error("Failed to load test cases:", error);
      }
    }
  }, []);

  // Save test cases to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem("test-cases", JSON.stringify(testCases));
  }, [testCases]);

  const handleSaveTestCase = () => {
    if (!selectedAgent || !newCase.name.trim()) return;

    const testCase: TestCase = {
      id: `tc_${Date.now()}`,
      name: newCase.name.trim(),
      description: newCase.description.trim(),
      input: currentInput,
      expectedOutput: newCase.expectedOutput.trim() || undefined,
      agentId: selectedAgent.agent_id,
      agentName: selectedAgent.team_name,
      aiOverride: currentAiOverride.enabled ? currentAiOverride : undefined,
      tags: newCase.tags.split(",").map(tag => tag.trim()).filter(Boolean),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    setTestCases(prev => [testCase, ...prev]);
    setNewCase({ name: "", description: "", expectedOutput: "", tags: "" });
    setShowSaveDialog(false);
  };

  const handleUpdateTestCase = () => {
    if (!editingCase) return;

    setTestCases(prev => prev.map(tc => 
      tc.id === editingCase.id 
        ? { ...editingCase, updatedAt: new Date().toISOString() }
        : tc
    ));
    setEditingCase(null);
  };

  const handleDeleteTestCase = (id: string) => {
    setTestCases(prev => prev.filter(tc => tc.id !== id));
  };

  const handleDuplicateTestCase = (testCase: TestCase) => {
    const duplicate: TestCase = {
      ...testCase,
      id: `tc_${Date.now()}`,
      name: `${testCase.name} (副本)`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      lastRunAt: undefined,
      lastRunStatus: undefined,
      lastRunOutput: undefined
    };
    setTestCases(prev => [duplicate, ...prev]);
  };

  const handleRunTestCase = (testCase: TestCase) => {
    // Update last run timestamp
    setTestCases(prev => prev.map(tc => 
      tc.id === testCase.id 
        ? { ...tc, lastRunAt: new Date().toISOString() }
        : tc
    ));
    onRunTestCase(testCase);
  };

  const updateTestCaseResult = (id: string, status: "success" | "error", output: string) => {
    setTestCases(prev => prev.map(tc => 
      tc.id === id 
        ? { ...tc, lastRunStatus: status, lastRunOutput: output }
        : tc
    ));
  };

  const filteredTestCases = selectedAgent 
    ? testCases.filter(tc => tc.agentId === selectedAgent.agent_id)
    : testCases;

  return (
    <div className="space-y-4">
      {/* Action Buttons */}
      <div className="flex gap-2">
        <Dialog open={showSaveDialog} onOpenChange={setShowSaveDialog}>
          <DialogTrigger asChild>
            <Button 
              variant="outline" 
              size="sm"
              disabled={!selectedAgent || !currentInput.trim()}
              className="flex items-center gap-2"
            >
              <Save className="h-4 w-4" />
              保存测试用例
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>保存测试用例</DialogTitle>
              <DialogDescription>
                将当前的输入和配置保存为可重复使用的测试用例
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>用例名称 *</Label>
                <Input
                  value={newCase.name}
                  onChange={(e) => setNewCase(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="输入测试用例名称"
                />
              </div>
              <div className="space-y-2">
                <Label>描述</Label>
                <Textarea
                  value={newCase.description}
                  onChange={(e) => setNewCase(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="描述这个测试用例的目的和预期行为"
                  rows={3}
                />
              </div>
              <div className="space-y-2">
                <Label>预期输出</Label>
                <Textarea
                  value={newCase.expectedOutput}
                  onChange={(e) => setNewCase(prev => ({ ...prev, expectedOutput: e.target.value }))}
                  placeholder="输入预期的输出结果（可选）"
                  rows={3}
                />
              </div>
              <div className="space-y-2">
                <Label>标签</Label>
                <Input
                  value={newCase.tags}
                  onChange={(e) => setNewCase(prev => ({ ...prev, tags: e.target.value }))}
                  placeholder="用逗号分隔的标签，如：功能测试,回归测试"
                />
              </div>
              
              {/* Preview */}
              <div className="p-3 bg-muted/50 rounded-lg">
                <h4 className="text-sm font-medium mb-2">预览</h4>
                <div className="text-xs space-y-1">
                  <div><span className="text-muted-foreground">Agent:</span> {selectedAgent?.team_name}</div>
                  <div><span className="text-muted-foreground">输入:</span> {currentInput.slice(0, 100)}...</div>
                  {currentAiOverride.enabled && (
                    <div><span className="text-muted-foreground">AI配置:</span> {currentAiOverride.provider}/{currentAiOverride.model}</div>
                  )}
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowSaveDialog(false)}>
                取消
              </Button>
              <Button 
                onClick={handleSaveTestCase}
                disabled={!newCase.name.trim()}
              >
                保存
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        <Dialog open={showManagerDialog} onOpenChange={setShowManagerDialog}>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm" className="flex items-center gap-2">
              <FolderOpen className="h-4 w-4" />
              管理测试用例 ({filteredTestCases.length})
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[80vh]">
            <DialogHeader>
              <DialogTitle>测试用例管理</DialogTitle>
              <DialogDescription>
                管理和运行您保存的测试用例
              </DialogDescription>
            </DialogHeader>
            
            <ScrollArea className="h-[60vh]">
              {filteredTestCases.length === 0 ? (
                <div className="text-center py-8">
                  <TestTube className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">
                    {selectedAgent ? "当前 Agent 暂无保存的测试用例" : "请先选择一个 Agent"}
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredTestCases.map((testCase) => (
                    <Card key={testCase.id} className="p-4">
                      <div className="space-y-3">
                        {/* Header */}
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h4 className="font-medium">{testCase.name}</h4>
                            {testCase.description && (
                              <p className="text-sm text-muted-foreground mt-1">
                                {testCase.description}
                              </p>
                            )}
                          </div>
                          <div className="flex gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => onLoadTestCase(testCase)}
                              title="加载到测试界面"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleRunTestCase(testCase)}
                              title="运行测试"
                            >
                              <Play className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDuplicateTestCase(testCase)}
                              title="复制"
                            >
                              <Copy className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteTestCase(testCase.id)}
                              title="删除"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>

                        {/* Tags */}
                        {testCase.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {testCase.tags.map((tag, index) => (
                              <Badge key={index} variant="secondary" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        )}

                        {/* Input Preview */}
                        <div className="text-sm">
                          <span className="text-muted-foreground">输入: </span>
                          <span className="line-clamp-2">{testCase.input}</span>
                        </div>

                        {/* Expected Output */}
                        {testCase.expectedOutput && (
                          <div className="text-sm">
                            <span className="text-muted-foreground">预期输出: </span>
                            <span className="line-clamp-2">{testCase.expectedOutput}</span>
                          </div>
                        )}

                        {/* Last Run Status */}
                        {testCase.lastRunAt && (
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <span>最后运行: {new Date(testCase.lastRunAt).toLocaleString()}</span>
                            {testCase.lastRunStatus && (
                              <Badge 
                                variant={testCase.lastRunStatus === "success" ? "default" : "destructive"}
                                className="text-xs"
                              >
                                {testCase.lastRunStatus === "success" ? "成功" : "失败"}
                              </Badge>
                            )}
                          </div>
                        )}

                        {/* AI Override Info */}
                        {testCase.aiOverride && (
                          <div className="text-xs text-muted-foreground">
                            AI配置: {testCase.aiOverride.provider}/{testCase.aiOverride.model} 
                            (temp: {testCase.aiOverride.temperature})
                          </div>
                        )}
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </ScrollArea>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
