"use client";

import { useState, useEffect } from "react";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, Download, FileText, Database, FileSpreadsheet } from "lucide-react";
import { format } from "date-fns";
import { TestRecord } from "@/lib/types";
import { TestResultExporter, ExportOptions } from "@/lib/export-utils";
import { cn } from "@/lib/utils";

interface ExportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  testHistory: TestRecord[];
  availableAgents: { agent_id: string; agent_name: string }[];
}

export function ExportDialog({ open, onOpenChange, testHistory, availableAgents }: ExportDialogProps) {
  const [exportFormat, setExportFormat] = useState<'json' | 'csv' | 'markdown'>('json');
  const [includeMetadata, setIncludeMetadata] = useState(true);
  const [dateRange, setDateRange] = useState<{ start: Date | undefined; end: Date | undefined }>({
    start: undefined,
    end: undefined
  });
  const [selectedAgents, setSelectedAgents] = useState<string[]>([]);
  const [selectedStatuses, setSelectedStatuses] = useState<('success' | 'error')[]>(['success', 'error']);
  const [exportSummary, setExportSummary] = useState<any>(null);

  // Update export summary when filters change
  useEffect(() => {
    const options: ExportOptions = {
      format: exportFormat,
      includeMetadata,
      dateRange: dateRange.start && dateRange.end ? {
        start: dateRange.start,
        end: dateRange.end
      } : undefined,
      agentFilter: selectedAgents.length > 0 ? selectedAgents : undefined,
      statusFilter: selectedStatuses.length > 0 ? selectedStatuses : undefined
    };

    const summary = TestResultExporter.getExportSummary(testHistory, options);
    setExportSummary(summary);
  }, [exportFormat, includeMetadata, dateRange, selectedAgents, selectedStatuses, testHistory]);

  const handleAgentToggle = (agentId: string) => {
    setSelectedAgents(prev => 
      prev.includes(agentId) 
        ? prev.filter(id => id !== agentId)
        : [...prev, agentId]
    );
  };

  const handleStatusToggle = (status: 'success' | 'error') => {
    setSelectedStatuses(prev => 
      prev.includes(status) 
        ? prev.filter(s => s !== status)
        : [...prev, status]
    );
  };

  const handleExport = () => {
    const options: ExportOptions = {
      format: exportFormat,
      includeMetadata,
      dateRange: dateRange.start && dateRange.end ? {
        start: dateRange.start,
        end: dateRange.end
      } : undefined,
      agentFilter: selectedAgents.length > 0 ? selectedAgents : undefined,
      statusFilter: selectedStatuses.length > 0 ? selectedStatuses : undefined
    };

    TestResultExporter.exportAndDownload(testHistory, options);
    onOpenChange(false);
  };

  const formatIcons = {
    json: Database,
    csv: FileSpreadsheet,
    markdown: FileText
  };

  const FormatIcon = formatIcons[exportFormat];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            导出测试结果
          </DialogTitle>
          <DialogDescription>
            选择导出格式和筛选条件，导出您的测试历史记录
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Export Format */}
          <div className="space-y-3">
            <Label className="text-base font-medium">导出格式</Label>
            <div className="grid grid-cols-3 gap-3">
              {(['json', 'csv', 'markdown'] as const).map((format) => {
                const Icon = formatIcons[format];
                return (
                  <Button
                    key={format}
                    variant={exportFormat === format ? "default" : "outline"}
                    onClick={() => setExportFormat(format)}
                    className="h-16 flex-col gap-2"
                  >
                    <Icon className="h-5 w-5" />
                    <span className="uppercase">{format}</span>
                  </Button>
                );
              })}
            </div>
          </div>

          <Separator />

          {/* Options */}
          <div className="space-y-3">
            <Label className="text-base font-medium">导出选项</Label>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="include-metadata"
                checked={includeMetadata}
                onCheckedChange={(checked) => setIncludeMetadata(checked as boolean)}
              />
              <Label htmlFor="include-metadata" className="text-sm">
                包含元数据（导出时间、筛选条件等）
              </Label>
            </div>
          </div>

          <Separator />

          {/* Date Range Filter */}
          <div className="space-y-3">
            <Label className="text-base font-medium">日期范围筛选</Label>
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-2">
                <Label className="text-sm">开始日期</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !dateRange.start && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateRange.start ? format(dateRange.start, "PPP") : "选择开始日期"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={dateRange.start}
                      onSelect={(date) => setDateRange(prev => ({ ...prev, start: date }))}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="space-y-2">
                <Label className="text-sm">结束日期</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !dateRange.end && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateRange.end ? format(dateRange.end, "PPP") : "选择结束日期"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={dateRange.end}
                      onSelect={(date) => setDateRange(prev => ({ ...prev, end: date }))}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
            {(dateRange.start || dateRange.end) && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setDateRange({ start: undefined, end: undefined })}
              >
                清除日期筛选
              </Button>
            )}
          </div>

          <Separator />

          {/* Agent Filter */}
          <div className="space-y-3">
            <Label className="text-base font-medium">Agent 筛选</Label>
            <div className="space-y-2">
              <div className="flex flex-wrap gap-2">
                {availableAgents.map((agent) => (
                  <Badge
                    key={agent.agent_id}
                    variant={selectedAgents.includes(agent.agent_id) ? "default" : "outline"}
                    className="cursor-pointer"
                    onClick={() => handleAgentToggle(agent.agent_id)}
                  >
                    {agent.agent_name}
                  </Badge>
                ))}
              </div>
              {selectedAgents.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedAgents([])}
                >
                  清除 Agent 筛选
                </Button>
              )}
            </div>
          </div>

          <Separator />

          {/* Status Filter */}
          <div className="space-y-3">
            <Label className="text-base font-medium">状态筛选</Label>
            <div className="flex gap-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="status-success"
                  checked={selectedStatuses.includes('success')}
                  onCheckedChange={() => handleStatusToggle('success')}
                />
                <Label htmlFor="status-success" className="text-sm">
                  成功
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="status-error"
                  checked={selectedStatuses.includes('error')}
                  onCheckedChange={() => handleStatusToggle('error')}
                />
                <Label htmlFor="status-error" className="text-sm">
                  错误
                </Label>
              </div>
            </div>
          </div>

          <Separator />

          {/* Export Summary */}
          {exportSummary && (
            <div className="p-4 bg-muted/50 rounded-lg">
              <h4 className="text-sm font-medium mb-2">导出预览</h4>
              <div className="grid grid-cols-2 gap-3 text-xs">
                <div>
                  <span className="text-muted-foreground">总记录数:</span>
                  <span className="ml-1 font-medium">{exportSummary.totalRecords}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">筛选后:</span>
                  <span className="ml-1 font-medium">{exportSummary.filteredRecords}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">成功:</span>
                  <span className="ml-1 font-medium text-green-600">{exportSummary.successCount}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">错误:</span>
                  <span className="ml-1 font-medium text-red-600">{exportSummary.errorCount}</span>
                </div>
                <div className="col-span-2">
                  <span className="text-muted-foreground">涉及 Agent:</span>
                  <span className="ml-1 font-medium">{exportSummary.uniqueAgents.length}</span>
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button 
            onClick={handleExport}
            disabled={!exportSummary || exportSummary.filteredRecords === 0}
            className="flex items-center gap-2"
          >
            <FormatIcon className="h-4 w-4" />
            导出 {exportFormat.toUpperCase()}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
