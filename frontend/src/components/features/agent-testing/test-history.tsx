"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Download, FileText, BarChart3 } from "lucide-react";
import { formatRelativeTime } from "@/lib/utils";
import { TestRecord } from "@/lib/types";
import { ExportDialog } from "./export-dialog";
import { UnifiedTestDetailDialog } from "../test-history/unified-test-detail-dialog";
import { api } from "@/lib/api";


interface TestHistoryProps {
  history: TestRecord[];
  onRerunTest: (record: TestRecord) => void;
  availableAgents?: { agent_id: string; agent_name: string }[];
  currentResponseMetadata?: any;
}

function TestDetailDialog({ record }: { record: TestRecord }) {
  const [showDetailDialog, setShowDetailDialog] = useState(false);
  const [testDetail, setTestDetail] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const loadTestDetail = async () => {
    if (!record.id) return;

    setLoading(true);
    try {
      const response = await api.testHistory.getDetail(record.id);
      setTestDetail(response);
      setShowDetailDialog(true);
    } catch (error) {
      console.error("Failed to load test detail:", error);
      // Fallback to simple record data
      const fallbackDetail = {
        test_id: record.id,
        agent_id: record.agent_id,
        status: record.status === "success" ? "completed" : "failed",
        started_at: record.timestamp,
        execution_duration_ms: record.duration,
        input_text: record.input,
        final_output: record.output,
        context_placeholders_used: [],
        team_member_interactions: []
      };
      setTestDetail(fallbackDetail);
      setShowDetailDialog(true);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        onClick={loadTestDetail}
        disabled={loading}
      >
        {loading ? "加载中..." : "查看详情"}
      </Button>

      <UnifiedTestDetailDialog
        open={showDetailDialog}
        onOpenChange={setShowDetailDialog}
        testDetail={testDetail}
        title="测试详情"
      />
    </>
  );
}

export function TestHistory({ history, onRerunTest, availableAgents = [], currentResponseMetadata }: TestHistoryProps) {
  const [showExportDialog, setShowExportDialog] = useState(false);

  // Generate available agents from history if not provided
  const agentsForExport = availableAgents.length > 0
    ? availableAgents
    : [...new Set(history.map(h => ({ agent_id: h.agent_id, agent_name: h.agent_name })))];

  if (history.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>📊 测试历史</CardTitle>
          <CardDescription>
            你的测试记录将显示在这里
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <div className="text-4xl mb-4">📝</div>
            <p className="text-muted-foreground">
              暂无测试记录<br />
              开始测试后记录将显示在这里
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>📊 测试历史</CardTitle>
              <CardDescription>
                最近的测试记录和结果
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowExportDialog(true)}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                导出
              </Button>
            </div>
          </div>
        </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {history.map((record) => (
            <div
              key={record.id}
              className="border rounded-lg p-4 space-y-3 hover:shadow-sm transition-shadow"
            >
              {/* Header */}
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-medium text-sm">{record.agent_name}</h4>
                    <Badge 
                      variant={record.status === "success" ? "default" : "destructive"}
                      className="text-xs"
                    >
                      {record.status === "success" ? "✅" : "❌"}
                    </Badge>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {formatRelativeTime(record.timestamp)} • {record.duration}ms
                  </p>
                </div>
              </div>

              {/* Input Preview */}
              <div>
                <p className="text-xs text-muted-foreground mb-1">输入:</p>
                <p className="text-sm line-clamp-2 bg-muted/50 p-2 rounded">
                  {record.input}
                </p>
              </div>

              {/* Output Preview */}
              <div>
                <p className="text-xs text-muted-foreground mb-1">输出:</p>
                <p className="text-sm line-clamp-3 bg-muted/50 p-2 rounded">
                  {record.output}
                </p>
              </div>

              {/* Actions */}
              <div className="flex gap-2">
                <TestDetailDialog record={record} />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onRerunTest(record)}
                >
                  重新测试
                </Button>
              </div>
            </div>
          ))}

          {/* Load More */}
          {history.length >= 10 && (
            <div className="text-center pt-4">
              <Button variant="outline" size="sm">
                加载更多
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>

    <ExportDialog
      open={showExportDialog}
      onOpenChange={setShowExportDialog}
      testHistory={history}
      availableAgents={agentsForExport}
    />
    </>
  );
}
