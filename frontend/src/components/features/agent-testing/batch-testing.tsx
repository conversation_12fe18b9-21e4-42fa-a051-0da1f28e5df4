"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Play, Pause, Square, Download, Upload, Zap, Target, BarChart3 } from "lucide-react";
import { Agent } from "@/lib/types";
import { AIModelOverrideType } from "./ai-model-override";
import { api } from "@/lib/api";

interface BatchTestInput {
  id: string;
  input: string;
  expectedOutput?: string;
  tags: string[];
}

interface BatchTestResult {
  id: string;
  inputId: string;
  input: string;
  output: string;
  status: "success" | "error";
  duration: number;
  timestamp: string;
  aiConfig: {
    provider: string;
    model: string;
    temperature: number;
  };
}

interface BatchTestSuite {
  id: string;
  name: string;
  description: string;
  inputs: BatchTestInput[];
  agentId: string;
  aiConfigs: AIModelOverrideType[];
  createdAt: string;
}

interface BatchTestingProps {
  agent: Agent;
  agents: Agent[];
}

export function BatchTesting({ agent, agents }: BatchTestingProps) {
  const [isRunning, setIsRunning] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentTest, setCurrentTest] = useState<string>("");
  const [results, setResults] = useState<BatchTestResult[]>([]);
  const [testInputs, setTestInputs] = useState<BatchTestInput[]>([]);
  const [newInput, setNewInput] = useState("");
  const [newExpectedOutput, setNewExpectedOutput] = useState("");
  const [newTags, setNewTags] = useState("");
  const [aiConfigs, setAiConfigs] = useState<AIModelOverrideType[]>([]);
  const [showDialog, setShowDialog] = useState(false);

  // Add default AI config
  useEffect(() => {
    if (aiConfigs.length === 0) {
      setAiConfigs([{
        enabled: true,
        provider: "openai",
        model: "gpt-4",
        temperature: 0.7,
        maxTokens: 2000,
        baseUrl: undefined,
        customProviderName: undefined
      }]);
    }
  }, [agent]);

  const addTestInput = () => {
    if (!newInput.trim()) return;

    const input: BatchTestInput = {
      id: `input_${Date.now()}`,
      input: newInput.trim(),
      expectedOutput: newExpectedOutput.trim() || undefined,
      tags: newTags.split(",").map(tag => tag.trim()).filter(Boolean)
    };

    setTestInputs(prev => [...prev, input]);
    setNewInput("");
    setNewExpectedOutput("");
    setNewTags("");
  };

  const removeTestInput = (id: string) => {
    setTestInputs(prev => prev.filter(input => input.id !== id));
  };

  const addAiConfig = () => {
    const newConfig: AIModelOverrideType = {
      enabled: true,
      provider: "openai",
      model: "gpt-4",
      temperature: 0.7,
      maxTokens: 2000
    };
    setAiConfigs(prev => [...prev, newConfig]);
  };

  const updateAiConfig = (index: number, config: AIModelOverrideType) => {
    setAiConfigs(prev => prev.map((c, i) => i === index ? config : c));
  };

  const removeAiConfig = (index: number) => {
    setAiConfigs(prev => prev.filter((_, i) => i !== index));
  };

  const runBatchTest = async () => {
    if (testInputs.length === 0 || aiConfigs.length === 0) return;

    setIsRunning(true);
    setIsPaused(false);
    setProgress(0);
    setResults([]);

    const totalTests = testInputs.length * aiConfigs.length;
    let completedTests = 0;

    for (const input of testInputs) {
      for (const aiConfig of aiConfigs) {
        if (isPaused) {
          await new Promise(resolve => {
            const checkPause = () => {
              if (!isPaused) resolve(undefined);
              else setTimeout(checkPause, 100);
            };
            checkPause();
          });
        }

        setCurrentTest(`Testing: ${input.input.slice(0, 50)}... with ${aiConfig.provider}/${aiConfig.model}`);

        try {
          const startTime = Date.now();
          const response = await api.agents.invoke(agent.agent_id, {
            input: input.input,
            ai_override: aiConfig.enabled ? {
              provider: aiConfig.provider,
              model: aiConfig.model,
              temperature: aiConfig.temperature,
              max_tokens: aiConfig.maxTokens,
              base_url: aiConfig.baseUrl,
              custom_provider_name: aiConfig.customProviderName
            } : undefined
          });

          const duration = Date.now() - startTime;
          let output = "";
          let status: "success" | "error" = "success";

          if (response.success && response.data) {
            if (response.data.status === "success" && response.data.final_output) {
              output = typeof response.data.final_output === 'string' 
                ? response.data.final_output 
                : JSON.stringify(response.data.final_output);
            } else if (response.data.output) {
              output = response.data.output;
            } else {
              output = "No output generated";
            }
          } else {
            output = response.error?.message || "Test failed";
            status = "error";
          }

          const result: BatchTestResult = {
            id: `result_${Date.now()}_${Math.random()}`,
            inputId: input.id,
            input: input.input,
            output,
            status,
            duration,
            timestamp: new Date().toISOString(),
            aiConfig: {
              provider: aiConfig.provider,
              model: aiConfig.model,
              temperature: aiConfig.temperature
            }
          };

          setResults(prev => [...prev, result]);
        } catch (error) {
          const result: BatchTestResult = {
            id: `result_${Date.now()}_${Math.random()}`,
            inputId: input.id,
            input: input.input,
            output: `Error: ${error instanceof Error ? error.message : "Unknown error"}`,
            status: "error",
            duration: 0,
            timestamp: new Date().toISOString(),
            aiConfig: {
              provider: aiConfig.provider,
              model: aiConfig.model,
              temperature: aiConfig.temperature
            }
          };

          setResults(prev => [...prev, result]);
        }

        completedTests++;
        setProgress((completedTests / totalTests) * 100);
      }
    }

    setIsRunning(false);
    setCurrentTest("Batch testing completed");
  };

  const pauseTest = () => {
    setIsPaused(true);
  };

  const resumeTest = () => {
    setIsPaused(false);
  };

  const stopTest = () => {
    setIsRunning(false);
    setIsPaused(false);
    setCurrentTest("Batch testing stopped");
  };

  const exportResults = () => {
    const data = {
      agent: agent.team_name,
      agentId: agent.agent_id,
      testInputs,
      aiConfigs,
      results,
      summary: {
        totalTests: results.length,
        successCount: results.filter(r => r.status === "success").length,
        errorCount: results.filter(r => r.status === "error").length,
        averageDuration: results.reduce((sum, r) => sum + r.duration, 0) / results.length || 0
      },
      exportedAt: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `batch-test-results-${agent.agent_id}-${Date.now()}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const importInputs = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        const lines = content.split('\n').filter(line => line.trim());
        const inputs: BatchTestInput[] = lines.map((line, index) => ({
          id: `imported_${Date.now()}_${index}`,
          input: line.trim(),
          tags: ["imported"]
        }));
        setTestInputs(prev => [...prev, ...inputs]);
      } catch (error) {
        alert("Failed to import file. Please ensure it's a text file with one input per line.");
      }
    };
    reader.readAsText(file);
  };

  const successRate = results.length > 0 
    ? (results.filter(r => r.status === "success").length / results.length) * 100 
    : 0;

  const averageDuration = results.length > 0 
    ? results.reduce((sum, r) => sum + r.duration, 0) / results.length 
    : 0;

  return (
    <Dialog open={showDialog} onOpenChange={setShowDialog}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="flex items-center gap-2">
          <Zap className="h-4 w-4" />
          批量测试
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            批量测试 - {agent.team_name}
          </DialogTitle>
          <DialogDescription>
            同时测试多个输入和AI配置，进行A/B测试和性能对比
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="setup" className="w-full">
          <TabsList className="grid w-full grid-cols-3 mobile-tabs">
            <TabsTrigger value="setup" className="flex items-center justify-center px-2">
              <span className="truncate text-xs sm:text-sm">设置</span>
            </TabsTrigger>
            <TabsTrigger value="run" className="flex items-center justify-center px-2">
              <span className="truncate text-xs sm:text-sm">执行</span>
            </TabsTrigger>
            <TabsTrigger value="results" className="flex items-center justify-center px-2">
              <span className="truncate text-xs sm:text-sm">结果</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="setup" className="w-full space-y-6">
            {/* Test Inputs */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base">测试输入</CardTitle>
                <CardDescription>添加要测试的输入内容</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>测试输入</Label>
                    <Textarea
                      value={newInput}
                      onChange={(e) => setNewInput(e.target.value)}
                      placeholder="输入测试内容..."
                      rows={3}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>预期输出 (可选)</Label>
                    <Textarea
                      value={newExpectedOutput}
                      onChange={(e) => setNewExpectedOutput(e.target.value)}
                      placeholder="输入预期的输出结果..."
                      rows={3}
                    />
                  </div>
                </div>
                <div className="flex gap-2">
                  <Input
                    value={newTags}
                    onChange={(e) => setNewTags(e.target.value)}
                    placeholder="标签 (用逗号分隔)"
                    className="flex-1"
                  />
                  <Button onClick={addTestInput} disabled={!newInput.trim()}>
                    添加
                  </Button>
                </div>
                
                <div className="flex gap-2">
                  <input
                    type="file"
                    accept=".txt,.csv"
                    onChange={importInputs}
                    className="hidden"
                    id="import-inputs"
                  />
                  <Button
                    variant="outline"
                    onClick={() => document.getElementById("import-inputs")?.click()}
                    className="flex items-center gap-2"
                  >
                    <Upload className="h-4 w-4" />
                    导入文件
                  </Button>
                </div>

                {/* Input List */}
                {testInputs.length > 0 && (
                  <ScrollArea className="h-40 border rounded-lg p-4">
                    <div className="space-y-2">
                      {testInputs.map((input) => (
                        <div key={input.id} className="flex items-start justify-between p-2 border rounded">
                          <div className="flex-1">
                            <p className="text-sm">{input.input}</p>
                            {input.tags.length > 0 && (
                              <div className="flex gap-1 mt-1">
                                {input.tags.map((tag, index) => (
                                  <Badge key={index} variant="secondary" className="text-xs">
                                    {tag}
                                  </Badge>
                                ))}
                              </div>
                            )}
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeTestInput(input.id)}
                          >
                            ×
                          </Button>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                )}
              </CardContent>
            </Card>

            {/* AI Configurations */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base">AI 配置</CardTitle>
                <CardDescription>设置要测试的不同AI配置进行A/B测试</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {aiConfigs.map((config, index) => (
                    <div key={index} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">配置 {index + 1}</h4>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeAiConfig(index)}
                          disabled={aiConfigs.length === 1}
                        >
                          删除
                        </Button>
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                        <div>
                          <span className="text-muted-foreground">提供商:</span>
                          <span className="ml-1 font-medium">{config.provider}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">模型:</span>
                          <span className="ml-1 font-medium">{config.model}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">温度:</span>
                          <span className="ml-1 font-medium">{config.temperature}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Token:</span>
                          <span className="ml-1 font-medium">{config.maxTokens}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                  <Button variant="outline" onClick={addAiConfig}>
                    添加配置
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="run" className="w-full space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">执行批量测试</CardTitle>
                <CardDescription>
                  将执行 {testInputs.length} 个输入 × {aiConfigs.length} 个配置 = {testInputs.length * aiConfigs.length} 个测试
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  {!isRunning ? (
                    <Button 
                      onClick={runBatchTest}
                      disabled={testInputs.length === 0 || aiConfigs.length === 0}
                      className="flex items-center gap-2"
                    >
                      <Play className="h-4 w-4" />
                      开始测试
                    </Button>
                  ) : (
                    <>
                      {!isPaused ? (
                        <Button onClick={pauseTest} variant="outline">
                          <Pause className="h-4 w-4" />
                          暂停
                        </Button>
                      ) : (
                        <Button onClick={resumeTest}>
                          <Play className="h-4 w-4" />
                          继续
                        </Button>
                      )}
                      <Button onClick={stopTest} variant="destructive">
                        <Square className="h-4 w-4" />
                        停止
                      </Button>
                    </>
                  )}
                </div>

                {isRunning && (
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>进度</span>
                      <span>{progress.toFixed(1)}%</span>
                    </div>
                    <Progress value={progress} />
                    <p className="text-sm text-muted-foreground">{currentTest}</p>
                  </div>
                )}

                {results.length > 0 && (
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-2xl font-bold">{results.length}</div>
                      <div className="text-sm text-muted-foreground">已完成</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-green-600">{successRate.toFixed(1)}%</div>
                      <div className="text-sm text-muted-foreground">成功率</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold">{averageDuration.toFixed(0)}ms</div>
                      <div className="text-sm text-muted-foreground">平均耗时</div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="results" className="w-full space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-base">测试结果</CardTitle>
                    <CardDescription>批量测试的详细结果和分析</CardDescription>
                  </div>
                  {results.length > 0 && (
                    <Button onClick={exportResults} variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      导出结果
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                {results.length === 0 ? (
                  <div className="text-center py-8">
                    <Target className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <p className="text-muted-foreground">暂无测试结果</p>
                  </div>
                ) : (
                  <ScrollArea className="h-96">
                    <div className="space-y-4">
                      {results.map((result) => (
                        <div key={result.id} className="p-4 border rounded-lg">
                          <div className="flex items-start justify-between mb-2">
                            <div className="flex items-center gap-2">
                              <Badge variant={result.status === "success" ? "default" : "destructive"}>
                                {result.status === "success" ? "成功" : "失败"}
                              </Badge>
                              <span className="text-sm text-muted-foreground">
                                {result.aiConfig.provider}/{result.aiConfig.model}
                              </span>
                              <span className="text-sm text-muted-foreground">
                                {result.duration}ms
                              </span>
                            </div>
                          </div>
                          <div className="space-y-2 text-sm">
                            <div>
                              <span className="font-medium">输入:</span>
                              <p className="text-muted-foreground">{result.input}</p>
                            </div>
                            <div>
                              <span className="font-medium">输出:</span>
                              <p className="text-muted-foreground line-clamp-3">{result.output}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
