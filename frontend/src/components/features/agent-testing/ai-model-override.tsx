"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { AlertCircle, Settings, RotateCcw, Zap, CheckCircle2, Key } from "lucide-react";
import { cn } from "@/lib/utils";
import { APIKey } from "@/lib/types";

export interface AIModelOverride {
  enabled: boolean;
  provider: string;
  model: string;
  temperature: number;
  maxTokens: number;
  baseUrl?: string;
  customProviderName?: string;
  apiKeyId?: string; // Selected API key ID
}

interface Agent {
  agent_id: string;
  team_name: string;
}

interface AIModelOverrideProps {
  agent: Agent;
  override: AIModelOverride;
  onOverrideChange: (override: AIModelOverride) => void;
  apiKeys?: APIKey[];
  options?: { stream: boolean };
  onOptionsChange?: (options: { stream: boolean }) => void;
}

const AI_PROVIDERS = {
  openai: {
    name: "OpenAI",
    models: ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-4o", "gpt-4o-mini"],
    defaultBaseUrl: "https://api.openai.com/v1",
    maxTokens: 4096
  },
  anthropic: {
    name: "Anthropic",
    models: ["claude-3-opus", "claude-3-sonnet", "claude-3-haiku", "claude-3-5-sonnet"],
    defaultBaseUrl: "https://api.anthropic.com/v1",
    maxTokens: 4096
  },
  google: {
    name: "Google",
    models: ["gemini-pro", "gemini-pro-vision", "gemini-1.5-pro", "gemini-1.5-flash"],
    defaultBaseUrl: "https://generativelanguage.googleapis.com/v1",
    maxTokens: 2048
  },
  custom: {
    name: "Custom",
    models: ["custom-model"],
    defaultBaseUrl: "",
    maxTokens: 4096
  }
};

export function AIModelOverride({ agent, override, onOverrideChange, apiKeys = [], options, onOptionsChange }: AIModelOverrideProps) {
  const [localOverride, setLocalOverride] = useState<AIModelOverride>(override);

  useEffect(() => {
    setLocalOverride(override);
  }, [override]);

  const handleOverrideToggle = (enabled: boolean) => {
    const newOverride = { ...localOverride, enabled };
    if (enabled && !localOverride.provider) {
      // Set defaults when enabling for the first time
      newOverride.provider = "openai";
      newOverride.model = AI_PROVIDERS[newOverride.provider as keyof typeof AI_PROVIDERS]?.models[0] || "gpt-4";
      newOverride.temperature = 0.7;
      newOverride.maxTokens = 2000;
      newOverride.baseUrl = AI_PROVIDERS[newOverride.provider as keyof typeof AI_PROVIDERS]?.defaultBaseUrl;
      newOverride.customProviderName = "";
    }
    setLocalOverride(newOverride);
    onOverrideChange(newOverride);
  };

  const handleProviderChange = (provider: string) => {
    const providerConfig = AI_PROVIDERS[provider as keyof typeof AI_PROVIDERS];
    const newOverride = {
      ...localOverride,
      provider,
      model: providerConfig?.models[0] || "gpt-4",
      baseUrl: provider === "custom" ? localOverride.baseUrl : providerConfig?.defaultBaseUrl,
      maxTokens: Math.min(localOverride.maxTokens, providerConfig?.maxTokens || 4096)
    };
    setLocalOverride(newOverride);
    onOverrideChange(newOverride);
  };

  const handleFieldChange = (field: keyof AIModelOverride, value: any) => {
    const newOverride = { ...localOverride, [field]: value };
    setLocalOverride(newOverride);
    onOverrideChange(newOverride);
  };

  const resetToDefaults = () => {
    const defaultOverride: AIModelOverride = {
      enabled: false,
      provider: "openai",
      model: "gpt-4",
      temperature: 0.7,
      maxTokens: 2000,
      baseUrl: undefined,
      customProviderName: undefined
    };
    setLocalOverride(defaultOverride);
    onOverrideChange(defaultOverride);
  };

  const currentProvider = AI_PROVIDERS[localOverride.provider as keyof typeof AI_PROVIDERS];
  const isCustomProvider = localOverride.provider === "custom";

  return (
    <Card className={cn(
      "transition-all duration-200 ease-in-out",
      localOverride.enabled
        ? "border-amber-200 bg-gradient-to-br from-amber-50/80 to-orange-50/60 dark:border-amber-800/50 dark:from-amber-950/30 dark:to-orange-950/20 shadow-sm"
        : "hover:shadow-sm"
    )}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className={cn(
              "p-1.5 rounded-md transition-colors",
              localOverride.enabled
                ? "bg-amber-100 text-amber-700 dark:bg-amber-900/50 dark:text-amber-300"
                : "bg-muted text-muted-foreground"
            )}>
              {localOverride.enabled ? <Zap className="h-4 w-4" /> : <Settings className="h-4 w-4" />}
            </div>
            <CardTitle className="text-base">运行时AI模型配置</CardTitle>
            {localOverride.enabled && (
              <Badge
                variant="secondary"
                className="bg-amber-100 text-amber-800 border-amber-200 dark:bg-amber-900/50 dark:text-amber-200 dark:border-amber-800/50 flex items-center gap-1"
              >
                <CheckCircle2 className="h-3 w-3" />
                已启用
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Switch
              checked={localOverride.enabled}
              onCheckedChange={handleOverrideToggle}
              className="data-[state=checked]:bg-amber-500 dark:data-[state=checked]:bg-amber-600"
            />
            <Button
              variant="ghost"
              size="sm"
              onClick={resetToDefaults}
              className={cn(
                "h-8 w-8 p-0 transition-colors",
                "hover:bg-amber-100 hover:text-amber-700 dark:hover:bg-amber-900/50 dark:hover:text-amber-300"
              )}
              title="重置为默认配置"
            >
              <RotateCcw className="h-3 w-3" />
            </Button>
          </div>
        </div>
        <CardDescription className={cn(
          "transition-colors",
          localOverride.enabled
            ? "text-amber-700 dark:text-amber-300"
            : "text-muted-foreground"
        )}>
          {localOverride.enabled
            ? "🔧 使用自定义 AI 配置进行测试，将覆盖 Agent 的默认设置"
            : "⚙️ 使用 Agent 的默认 AI 配置进行测试"
          }
        </CardDescription>
      </CardHeader>

      {localOverride.enabled && (
        <CardContent className="space-y-4 bg-gradient-to-br from-white/50 to-amber-50/30 dark:from-slate-900/50 dark:to-amber-950/10 rounded-b-lg border-t border-amber-100 dark:border-amber-900/30">
          {/* Provider Selection */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-slate-700 dark:text-slate-300">AI 提供商</Label>
            <Select value={localOverride.provider} onValueChange={handleProviderChange}>
              <SelectTrigger className="border-amber-200 focus:border-amber-400 focus:ring-amber-400/20 dark:border-amber-800/50 dark:focus:border-amber-600">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(AI_PROVIDERS).map(([key, provider]) => (
                  <SelectItem key={key} value={key}>
                    {provider.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Custom Provider Name */}
          {isCustomProvider && (
            <div className="space-y-2">
              <Label className="text-sm font-medium text-slate-700 dark:text-slate-300">自定义提供商名称</Label>
              <Input
                value={localOverride.customProviderName || ""}
                onChange={(e) => handleFieldChange("customProviderName", e.target.value)}
                placeholder="输入自定义提供商名称"
                className="border-amber-200 focus:border-amber-400 focus:ring-amber-400/20 dark:border-amber-800/50 dark:focus:border-amber-600"
              />
            </div>
          )}

          {/* Model Selection */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-slate-700 dark:text-slate-300">模型</Label>
            {isCustomProvider ? (
              <Input
                value={localOverride.model}
                onChange={(e) => handleFieldChange("model", e.target.value)}
                placeholder="输入自定义模型名称"
                className="border-amber-200 focus:border-amber-400 focus:ring-amber-400/20 dark:border-amber-800/50 dark:focus:border-amber-600"
              />
            ) : (
              <Select value={localOverride.model} onValueChange={(value) => handleFieldChange("model", value)}>
                <SelectTrigger className="border-amber-200 focus:border-amber-400 focus:ring-amber-400/20 dark:border-amber-800/50 dark:focus:border-amber-600">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {currentProvider?.models.map((model) => (
                    <SelectItem key={model} value={model}>
                      {model}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>

          {/* Temperature Slider */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium text-slate-700 dark:text-slate-300">温度 (创造性)</Label>
              <Badge
                variant="outline"
                className="bg-amber-50 border-amber-200 text-amber-700 dark:bg-amber-950/50 dark:border-amber-800/50 dark:text-amber-300"
              >
                {localOverride.temperature.toFixed(1)}
              </Badge>
            </div>
            <Slider
              value={[localOverride.temperature]}
              onValueChange={([value]) => handleFieldChange("temperature", value)}
              min={0}
              max={2}
              step={0.1}
              className="w-full [&_[role=slider]]:bg-amber-500 [&_[role=slider]]:border-amber-600 dark:[&_[role=slider]]:bg-amber-600 [&_.bg-primary]:bg-amber-500 dark:[&_.bg-primary]:bg-amber-600"
            />
            <div className="flex justify-between text-xs text-slate-500 dark:text-slate-400">
              <span>0.0 (确定性)</span>
              <span>1.0 (平衡)</span>
              <span>2.0 (创造性)</span>
            </div>
          </div>

          {/* Max Tokens */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-slate-700 dark:text-slate-300">最大 Token 数</Label>
            <Input
              type="number"
              value={localOverride.maxTokens}
              onChange={(e) => handleFieldChange("maxTokens", parseInt(e.target.value) || 0)}
              min={1}
              max={currentProvider?.maxTokens || 4096}
              placeholder="输入最大 token 数"
              className="border-amber-200 focus:border-amber-400 focus:ring-amber-400/20 dark:border-amber-800/50 dark:focus:border-amber-600"
            />
            <div className="text-xs text-slate-500 dark:text-slate-400">
              最大值: {currentProvider?.maxTokens || 4096} tokens
            </div>
          </div>

          {/* API Key Selection */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-slate-700 dark:text-slate-300 flex items-center gap-2">
              <Key className="h-4 w-4" />
              AI 密钥 *
            </Label>
            <Select
              value={localOverride.apiKeyId || ""}
              onValueChange={(value) => handleFieldChange("apiKeyId", value)}
            >
              <SelectTrigger className="border-amber-200 focus:border-amber-400 focus:ring-amber-400/20 dark:border-amber-800/50 dark:focus:border-amber-600">
                <SelectValue placeholder="选择AI密钥" />
              </SelectTrigger>
              <SelectContent>
                {apiKeys.length === 0 ? (
                  <div className="p-2 text-sm text-muted-foreground">
                    暂无可用的AI密钥，请先在设置中添加
                  </div>
                ) : (
                  apiKeys
                    .filter(key => key.status === "active")
                    .map((key) => (
                      <SelectItem key={key.id} value={key.id}>
                        <div className="flex items-center gap-2">
                          <span>{key.name}</span>
                          <Badge variant="secondary" className="text-xs">
                            {key.key_prefix}...
                          </Badge>
                        </div>
                      </SelectItem>
                    ))
                )}
              </SelectContent>
            </Select>
            <div className="text-xs text-slate-500 dark:text-slate-400">
              选择用于此次测试的AI密钥。只显示状态为"活跃"的密钥。
            </div>
          </div>

          {/* Base URL */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-slate-700 dark:text-slate-300">API 基础 URL</Label>
            <Input
              value={localOverride.baseUrl || ""}
              onChange={(e) => handleFieldChange("baseUrl", e.target.value)}
              placeholder={isCustomProvider ? "输入自定义 API 地址" : currentProvider?.defaultBaseUrl}
              className="border-amber-200 focus:border-amber-400 focus:ring-amber-400/20 dark:border-amber-800/50 dark:focus:border-amber-600"
            />
            {!isCustomProvider && (
              <div className="text-xs text-slate-500 dark:text-slate-400">
                默认: {currentProvider?.defaultBaseUrl}
              </div>
            )}
          </div>

          <Separator className="bg-amber-200/50 dark:bg-amber-800/30" />

          {/* Response Mode */}
          {onOptionsChange && options && (
            <div className="space-y-2">
              <Label className="text-sm font-medium text-slate-700 dark:text-slate-300">响应模式</Label>
              <Select
                value={options.stream ? "stream" : "complete"}
                onValueChange={(value) => onOptionsChange({...options, stream: value === "stream"})}
              >
                <SelectTrigger className="border-amber-200 focus:border-amber-400 focus:ring-amber-400/20 dark:border-amber-800/50 dark:focus:border-amber-600">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="complete">完整响应 (推荐)</SelectItem>
                  <SelectItem value="stream">流式响应</SelectItem>
                </SelectContent>
              </Select>
              <div className="text-xs text-slate-500 dark:text-slate-400">
                完整响应模式提供更好的错误处理和响应元数据
              </div>
            </div>
          )}


        </CardContent>
      )}
    </Card>
  );
}
