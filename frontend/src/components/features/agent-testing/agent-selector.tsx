"use client";

import { motion } from "framer-motion";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { <PERSON><PERSON>, <PERSON>, Play, ArrowRight, Sparkles } from "lucide-react";

interface Agent {
  agent_id: string;
  team_name: string;
  description: string;
  status: "active" | "inactive" | "error";
  api_endpoint: string;
  team_members?: Array<{
    role: string;
    name: string;
  }>;
}

interface AgentSelectorProps {
  agents: Agent[];
  selectedAgent: Agent | null;
  onAgentSelect: (agent: Agent) => void;
}

function getStatusBadge(status: Agent["status"]) {
  switch (status) {
    case "active":
      return <Badge variant="default">活跃</Badge>;
    case "inactive":
      return <Badge variant="secondary">非活跃</Badge>;
    case "error":
      return <Badge variant="destructive">错误</Badge>;
    default:
      return <Badge variant="outline">未知</Badge>;
  }
}

export function AgentSelector({ agents, selectedAgent, onAgentSelect }: AgentSelectorProps) {
  const activeAgents = agents.filter(agent => agent.status === "active");
  const inactiveAgents = agents.filter(agent => agent.status !== "active");

  return (
    <Card className="border-primary/20 shadow-lg">
      <CardHeader className="text-center pb-6">
        <div className="space-y-4">
          <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
            <Bot className="h-8 w-8 text-primary" />
          </div>
          <CardTitle className="text-2xl font-bold">
            选择要测试的Agent
          </CardTitle>
          <CardDescription className="text-base">
            选择一个AI团队开始测试，体验智能协作的强大功能
          </CardDescription>
          <div className="flex items-center justify-center space-x-2 text-sm text-muted-foreground">
            <div className="w-6 h-6 bg-primary/20 rounded-full flex items-center justify-center text-primary font-semibold text-xs">1</div>
            <span>选择Agent</span>
            <ArrowRight className="h-4 w-4" />
            <div className="w-6 h-6 bg-muted rounded-full flex items-center justify-center text-muted-foreground font-semibold text-xs">2</div>
            <span>开始测试</span>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Active Agents */}
        {activeAgents.length > 0 && (
          <div className="space-y-4">
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-2">可用的AI团队</h3>
              <p className="text-sm text-muted-foreground">点击任意团队开始测试</p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {activeAgents.map((agent, index) => (
              <motion.div
                key={agent.agent_id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <Card
                  className={cn(
                    "cursor-pointer hover:shadow-lg transition-all duration-300 hover:border-primary/70 hover:scale-[1.02] group border-2",
                    "hover:bg-gradient-to-br hover:from-primary/5 hover:to-primary/10",
                    selectedAgent?.agent_id === agent.agent_id
                      ? "border-primary bg-primary/5 shadow-lg scale-[1.02]"
                      : "border-border hover:border-primary/50"
                  )}
                  onClick={() => onAgentSelect(agent)}
                >
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      {/* Agent Header */}
                      <div className="flex items-start justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                            <Bot className="h-5 w-5 text-primary" />
                          </div>
                          <div className="flex-1">
                            <h3 className="font-semibold group-hover:text-primary transition-colors">
                              {agent.team_name}
                            </h3>
                            <p className="text-sm text-muted-foreground line-clamp-2">
                              {agent.description}
                            </p>
                          </div>
                        </div>
                        {getStatusBadge(agent.status)}
                      </div>

                      {/* Team Members */}
                      {agent.team_members && agent.team_members.length > 0 && (
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                            <Users className="h-4 w-4" />
                            <span>团队成员 ({agent.team_members.length})</span>
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {agent.team_members.slice(0, 3).map((member, idx) => (
                              <Badge key={idx} variant="outline" className="text-xs">
                                {member.role}
                              </Badge>
                            ))}
                            {agent.team_members.length > 3 && (
                              <Badge variant="outline" className="text-xs">
                                +{agent.team_members.length - 3}
                              </Badge>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Action Button */}
                      <div className="flex justify-between items-center pt-3 border-t border-border/50">
                        <span className="text-sm font-medium text-muted-foreground">
                          {selectedAgent?.agent_id === agent.agent_id ? "✓ 已选择" : "点击开始测试"}
                        </span>
                        <div className="flex items-center space-x-1 text-primary group-hover:translate-x-1 transition-all duration-200">
                          <Play className="h-4 w-4 group-hover:scale-110 transition-transform" />
                          <ArrowRight className="h-4 w-4 group-hover:scale-110 transition-transform" />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
          </div>
        )}

        {/* Inactive Agents */}
        {inactiveAgents.length > 0 && (
          <div className="mt-6 space-y-3">
            <h4 className="font-medium text-sm text-muted-foreground text-center">不可用Agent</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {inactiveAgents.map((agent, index) => (
                <motion.div
                  key={agent.agent_id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: (activeAgents.length + index) * 0.1 }}
                >
                  <Card className="opacity-60 cursor-not-allowed">
                    <CardContent className="p-6">
                      <div className="space-y-4">
                        {/* Agent Header */}
                        <div className="flex items-start justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 bg-muted rounded-lg flex items-center justify-center">
                              <Bot className="h-5 w-5 text-muted-foreground" />
                            </div>
                            <div className="flex-1">
                              <h3 className="font-semibold">{agent.team_name}</h3>
                              <p className="text-sm text-muted-foreground line-clamp-2">
                                {agent.description}
                              </p>
                            </div>
                          </div>
                          {getStatusBadge(agent.status)}
                        </div>

                        {/* Status Message */}
                        <div className="text-center py-2">
                          <span className="text-xs text-muted-foreground">
                            需要激活后才能测试
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        )}

        {/* No Agents */}
        {agents.length === 0 && (
          <div className="text-center py-12 space-y-4">
            <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto">
              <Bot className="h-8 w-8 text-muted-foreground" />
            </div>
            <div className="space-y-2">
              <h3 className="text-lg font-medium">还没有Agent</h3>
              <p className="text-muted-foreground">
                您还没有创建任何AI团队，先创建一个吧！
              </p>
            </div>
            <Button asChild>
              <a href="/create">
                <Sparkles className="h-4 w-4 mr-2" />
                创建第一个Agent
              </a>
            </Button>
          </div>
        )}

        {/* Quick Actions */}
        {agents.length > 0 && (
          <div className="flex justify-center pt-6 border-t mt-6">
            <Button variant="outline" asChild>
              <a href="/create">
                <Sparkles className="h-4 w-4 mr-2" />
                创建新Agent
              </a>
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
