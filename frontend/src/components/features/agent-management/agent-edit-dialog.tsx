"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { LoadingButton } from "@/components/ui/loading";
import { AgentUpdate } from "@/lib/types";
import { getAgentWorkflowSteps } from "@/lib/utils";

// Local Agent interface to match the one used in agent-list
interface Agent {
  agent_id: string;
  team_name: string;
  description: string;
  status: "active" | "inactive" | "error";
  created_at: string;
  last_used?: string;
  usage_count: number;
  success_rate?: number;
  avg_response_time?: number;
  specialists?: Array<{
    name: string;
    role: string;
  }>;
  team_members?: Array<{
    name: string;
    role: string;
    description?: string;
    system_prompt?: string;
  }>;
  team_plan?: {
    specialists?: Array<{
      name: string;
      role: string;
    }>;
    team_members?: Array<{
      name: string;
      role: string;
      description?: string;
      system_prompt?: string;
    }>;
  };


}

// Team member validation schema
const teamMemberSchema = z.object({
  name: z.string().min(1, "成员名称不能为空"),
  role: z.string().min(1, "角色不能为空"),
  description: z.string().optional(),
  system_prompt: z.string().optional(),
});

// Form validation schema
const editAgentSchema = z.object({
  team_name: z.string().min(1, "团队名称不能为空").max(255, "团队名称不能超过255个字符"),
  description: z.string().min(1, "描述不能为空").max(1000, "描述不能超过1000个字符"),
  status: z.enum(["active", "inactive", "error"], {
    required_error: "请选择状态",
  }),
  team_members: z.array(teamMemberSchema).optional(),
});

type EditAgentFormData = z.infer<typeof editAgentSchema>;

interface AgentEditDialogProps {
  agent: Agent | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (agentId: string, updates: AgentUpdate) => Promise<void>;
}

export function AgentEditDialog({
  agent,
  open,
  onOpenChange,
  onSave,
}: AgentEditDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [teamMembers, setTeamMembers] = useState<Array<{
    name: string;
    role: string;
    description?: string;
    system_prompt?: string;
  }>>([]);
  const [teamMembersChanged, setTeamMembersChanged] = useState(false);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors, isDirty },
  } = useForm<EditAgentFormData>({
    resolver: zodResolver(editAgentSchema),
    defaultValues: {
      team_name: "",
      description: "",
      status: "active",
    },
  });

  // Reset form when agent changes
  React.useEffect(() => {
    if (agent) {
      // Get team members from agent data
      const members = agent.team_members ||
                     agent.team_plan?.team_members ||
                     agent.specialists?.map(s => ({ name: s.name, role: s.role })) ||
                     [];

      setTeamMembers(members);
      setTeamMembersChanged(false);

      reset({
        team_name: agent.team_name,
        description: agent.description,
        status: agent.status,
        team_members: members,
      });
      setError(null);
    }
  }, [agent, reset]);

  const onSubmit = async (data: EditAgentFormData) => {
    if (!agent) return;

    setIsSubmitting(true);
    setError(null);

    try {
      const updateData: AgentUpdate = {
        ...data,
        team_members: teamMembers,
      };
      await onSave(agent.agent_id, updateData);
      onOpenChange(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : "保存失败");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (agent) {
      // Reset form data
      reset({
        team_name: agent.team_name,
        description: agent.description,
        status: agent.status,
      });

      // Reset team members to original state
      const originalMembers = agent.team_members ||
                             agent.team_plan?.team_members ||
                             agent.specialists?.map(s => ({ name: s.name, role: s.role })) ||
                             [];
      setTeamMembers(originalMembers);
      setTeamMembersChanged(false);
    }
    setError(null);
    onOpenChange(false);
  };

  const statusOptions = [
    { value: "active", label: "活跃", description: "Agent正常运行" },
    { value: "inactive", label: "非活跃", description: "Agent暂停使用" },
    { value: "error", label: "错误", description: "Agent出现错误" },
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>编辑 Agent</DialogTitle>
          <DialogDescription>
            修改 Agent 的基本信息和状态。
          </DialogDescription>
          {/* Debug info - remove in production */}
          {process.env.NODE_ENV === 'development' && (
            <div className="text-xs text-gray-500 mt-2">
              Debug: isDirty={isDirty.toString()}, teamMembersChanged={teamMembersChanged.toString()}
            </div>
          )}
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Label htmlFor="team_name">团队名称</Label>
            <Input
              id="team_name"
              {...register("team_name")}
              placeholder="输入团队名称"
            />
            {errors.team_name && (
              <p className="text-sm text-destructive">{errors.team_name.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">描述</Label>
            <Textarea
              id="description"
              {...register("description")}
              placeholder="输入 Agent 描述"
              rows={3}
            />
            {errors.description && (
              <p className="text-sm text-destructive">{errors.description.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">状态</Label>
            <Select
              value={watch("status")}
              onValueChange={(value) => setValue("status", value as Agent["status"])}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择状态" />
              </SelectTrigger>
              <SelectContent>
                {statusOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex flex-col">
                      <span>{option.label}</span>
                      <span className="text-xs text-muted-foreground">
                        {option.description}
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.status && (
              <p className="text-sm text-destructive">{errors.status.message}</p>
            )}
          </div>



          {/* Team Members Section */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label>团队成员</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => {
                  setTeamMembers([...teamMembers, { name: "", role: "", description: "", system_prompt: "" }]);
                  setTeamMembersChanged(true);
                }}
              >
                + 添加成员
              </Button>
            </div>

            <div className="space-y-3">
              {teamMembers.map((member, index) => (
                <div key={index} className="p-3 border rounded-lg space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">成员 {index + 1}</span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setTeamMembers(teamMembers.filter((_, i) => i !== index));
                        setTeamMembersChanged(true);
                      }}
                    >
                      删除
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <Label className="text-xs">姓名</Label>
                      <Input
                        value={member.name}
                        onChange={(e) => {
                          const newMembers = [...teamMembers];
                          newMembers[index].name = e.target.value;
                          setTeamMembers(newMembers);
                          setTeamMembersChanged(true);
                        }}
                        placeholder="成员姓名"
                        className="h-8"
                      />
                    </div>
                    <div>
                      <Label className="text-xs">角色</Label>
                      <Input
                        value={member.role}
                        onChange={(e) => {
                          const newMembers = [...teamMembers];
                          newMembers[index].role = e.target.value;
                          setTeamMembers(newMembers);
                          setTeamMembersChanged(true);
                        }}
                        placeholder="角色"
                        className="h-8"
                      />
                    </div>
                  </div>

                  <div>
                    <Label className="text-xs">描述</Label>
                    <Textarea
                      value={member.description || ""}
                      onChange={(e) => {
                        const newMembers = [...teamMembers];
                        newMembers[index].description = e.target.value;
                        setTeamMembers(newMembers);
                        setTeamMembersChanged(true);
                      }}
                      placeholder="成员描述"
                      rows={2}
                      className="text-xs"
                    />
                  </div>

                  <div>
                    <Label className="text-xs">系统提示</Label>
                    <Textarea
                      value={member.system_prompt || ""}
                      onChange={(e) => {
                        const newMembers = [...teamMembers];
                        newMembers[index].system_prompt = e.target.value;
                        setTeamMembers(newMembers);
                        setTeamMembersChanged(true);
                      }}
                      placeholder="系统提示"
                      rows={2}
                      className="text-xs"
                    />
                  </div>
                </div>
              ))}

              {teamMembers.length === 0 && (
                <div className="text-center py-4 text-muted-foreground">
                  暂无团队成员，点击"添加成员"开始添加
                </div>
              )}
            </div>
          </div>

          {/* Workflow Steps Section (Read-only) */}
          <div className="space-y-2">
            <Label>工作流程 (只读)</Label>
            <div className="space-y-3">
              {getAgentWorkflowSteps(agent).map((step, index) => (
                <div key={index} className="p-3 border rounded-lg bg-muted/30">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">{step.name}</span>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">
                        步骤 {index + 1}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {step.assignee}
                      </Badge>
                    </div>
                  </div>
                  {step.description && (
                    <p className="text-xs text-muted-foreground mb-2">{step.description}</p>
                  )}
                  {step.inputs && step.inputs.length > 0 && (
                    <div className="text-xs mb-1">
                      <span className="font-medium text-green-600">输入: </span>
                      <span className="text-muted-foreground">{step.inputs.join(", ")}</span>
                    </div>
                  )}
                  {step.outputs && step.outputs.length > 0 && (
                    <div className="text-xs">
                      <span className="font-medium text-blue-600">输出: </span>
                      <span className="text-muted-foreground">{step.outputs.join(", ")}</span>
                    </div>
                  )}
                </div>
              ))}
              {getAgentWorkflowSteps(agent).length === 0 && (
                <div className="text-center py-4 text-muted-foreground">
                  <div className="text-2xl mb-2">📋</div>
                  <p className="text-sm">暂无工作流程信息</p>
                </div>
              )}
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleCancel}>
              取消
            </Button>
            <LoadingButton
              type="submit"
              isLoading={isSubmitting}
              disabled={isSubmitting}
            >
              保存更改
            </LoadingButton>

          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
