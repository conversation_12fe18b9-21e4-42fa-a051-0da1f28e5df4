"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { safeNumber } from "@/lib/utils";

interface Agent {
  agent_id: string;
  team_name: string;
  status: "active" | "inactive" | "error";
  usage_count: number;
  last_used?: string;
}

interface AgentStatsProps {
  agents: Agent[];
}

export function AgentStats({ agents }: AgentStatsProps) {
  const activeAgents = agents.filter(agent => agent.status === "active").length;
  const inactiveAgents = agents.filter(agent => agent.status === "inactive").length;
  const errorAgents = agents.filter(agent => agent.status === "error").length;
  const totalUsage = agents.reduce((sum, agent) => {
    return sum + safeNumber(agent.usage_count, 0);
  }, 0);

  // Calculate agents used in last 24 hours
  const now = new Date();
  const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
  const recentlyUsed = agents.filter(agent => {
    if (!agent.last_used) return false;
    const lastUsedDate = new Date(agent.last_used);
    if (isNaN(lastUsedDate.getTime())) return false;
    return lastUsedDate > yesterday;
  }).length;

  const stats = [
    {
      title: "总Agent数",
      value: agents.length,
      icon: "🤖",
      description: "已创建的Agent总数"
    },
    {
      title: "活跃Agent",
      value: activeAgents,
      icon: "🟢",
      description: "当前运行中的Agent"
    },
    {
      title: "今日活跃",
      value: recentlyUsed,
      icon: "📊",
      description: "24小时内使用过的Agent"
    },
    {
      title: "总调用次数",
      value: totalUsage,
      icon: "📈",
      description: "所有Agent的累计调用次数"
    }
  ];

  return (
    <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4">
      {stats.map((stat, index) => (
        <Card key={index} className="min-h-[120px] md:min-h-[140px]">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 px-4 md:px-6 pt-4 md:pt-6">
            <CardTitle className="text-xs md:text-sm font-medium leading-tight">
              {stat.title}
            </CardTitle>
            <span className="text-lg md:text-2xl flex-shrink-0">{stat.icon}</span>
          </CardHeader>
          <CardContent className="px-4 md:px-6 pb-4 md:pb-6">
            <div className="text-xl md:text-2xl font-bold">
              {safeNumber(stat.value)}
            </div>
            <p className="text-xs text-muted-foreground mt-1 leading-tight">
              {stat.description}
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
