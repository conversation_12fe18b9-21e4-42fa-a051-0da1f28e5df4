"use client";

import { useState, useEffect, useMemo } from "react";
import { AgentList } from "./agent-list";
import { AgentCardSkeleton } from "./agent-card-skeleton";
import { useIsMobile } from "@/hooks/use-mobile";

interface Agent {
  agent_id: string;
  team_name: string;
  description: string;
  status: "active" | "inactive" | "error";
  usage_count: number;
  created_at: string;
  last_used?: string;
  success_rate?: number;
  avg_response_time?: number;
  team_plan?: any;
  specialists?: any[];
  team_members?: any[];
}

interface LazyAgentListProps {
  agents: Agent[];
  selectedAgents: string[];
  highlightedAgent?: string | null;
  processingAgents?: Set<string>;
  onAgentSelect: (agentId: string, selected: boolean) => void;
  onAgentAction: (agentId: string, action: string) => void;
  onAgentEdit?: (agentId: string, updates: any) => Promise<void>;
  loading?: boolean;
  itemsPerPage?: number;
}

export function LazyAgentList({
  agents,
  selectedAgents,
  highlightedAgent,
  processingAgents = new Set(),
  onAgentSelect,
  onAgentAction,
  onAgentEdit,
  loading = false,
  itemsPerPage = 20
}: LazyAgentListProps) {
  const isMobile = useIsMobile();
  const [visibleCount, setVisibleCount] = useState(itemsPerPage);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  // Memoize visible agents to prevent unnecessary re-renders
  const visibleAgents = useMemo(() => {
    return agents.slice(0, visibleCount);
  }, [agents, visibleCount]);

  const hasMore = visibleCount < agents.length;

  // Load more items when scrolling near bottom
  useEffect(() => {
    const handleScroll = () => {
      if (isLoadingMore || !hasMore) return;

      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;

      // Load more when user is 200px from bottom
      if (scrollTop + windowHeight >= documentHeight - 200) {
        setIsLoadingMore(true);
        
        // Simulate loading delay for better UX
        setTimeout(() => {
          setVisibleCount(prev => Math.min(prev + itemsPerPage, agents.length));
          setIsLoadingMore(false);
        }, 300);
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [isLoadingMore, hasMore, itemsPerPage, agents.length]);

  // Reset visible count when agents change
  useEffect(() => {
    setVisibleCount(itemsPerPage);
  }, [agents.length, itemsPerPage]);

  if (loading) {
    return (
      <div className={isMobile ? "space-y-4" : "grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4"}>
        {Array.from({ length: Math.min(6, itemsPerPage) }).map((_, index) => (
          <AgentCardSkeleton key={index} />
        ))}
      </div>
    );
  }

  return (
    <>
      <AgentList
        agents={visibleAgents}
        selectedAgents={selectedAgents}
        highlightedAgent={highlightedAgent}
        processingAgents={processingAgents}
        onAgentSelect={onAgentSelect}
        onAgentAction={onAgentAction}
        onAgentEdit={onAgentEdit}
      />
      
      {/* Loading more indicator */}
      {isLoadingMore && (
        <div className={isMobile ? "space-y-4 mt-4" : "grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 mt-4"}>
          {Array.from({ length: Math.min(3, itemsPerPage) }).map((_, index) => (
            <AgentCardSkeleton key={`loading-${index}`} />
          ))}
        </div>
      )}
      
      {/* Load more button for manual loading (fallback) */}
      {hasMore && !isLoadingMore && (
        <div className="flex justify-center mt-6">
          <button
            onClick={() => {
              setIsLoadingMore(true);
              setTimeout(() => {
                setVisibleCount(prev => Math.min(prev + itemsPerPage, agents.length));
                setIsLoadingMore(false);
              }, 300);
            }}
            className="px-6 py-3 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors min-h-[44px] touch-target"
          >
            加载更多 ({agents.length - visibleCount} 个剩余)
          </button>
        </div>
      )}
    </>
  );
}

// Performance optimized version with React.memo
export const OptimizedLazyAgentList = LazyAgentList;
