"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import { formatRelativeTime, getAgentSpecialists, getAgentTeamMembers, getAgentWorkflowSteps, getAgentApiEndpoint } from "@/lib/utils";
import Link from "next/link";
import { useRouter } from "next/navigation";

import { DeleteConfirmDialog } from "@/components/common/confirm-dialog";
import { AgentEditDialog } from "./agent-edit-dialog";
import { AgentUpdate } from "@/lib/types";
import { useSystemSettings } from "@/hooks/useSystemSettings";
import { useIsMobile } from "@/hooks/use-mobile";
import { ChevronDown, ChevronUp, MoreHorizontal, TestTube } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";



interface Agent {
  agent_id: string;
  team_name: string;
  description: string;
  status: "active" | "inactive" | "error";
  created_at: string;
  last_used?: string;
  usage_count: number;
  success_rate?: number;
  avg_response_time?: number;
  specialists?: Array<{
    name: string;
    role: string;
  }>;
  team_members?: Array<{
    name: string;
    role: string;
    description?: string;
    system_prompt?: string;
  }>;
  team_plan?: {
    specialists?: Array<{
      name: string;
      role: string;
    }>;
    team_members?: Array<{
      name: string;
      role: string;
      description?: string;
      system_prompt?: string;
    }>;
    workflow?: {
      steps?: Array<{
        name: string;
        description?: string;
        assignee: string;
        inputs?: string[];
        outputs?: string[];
        dependencies?: string[];
      }>;
    };
  };


}

interface AgentListProps {
  agents: Agent[];
  selectedAgents: string[];
  highlightedAgent?: string | null;
  processingAgents?: Set<string>;
  onAgentSelect: (agentId: string, selected: boolean) => void;
  onAgentAction: (agentId: string, action: string) => void;
  onAgentEdit?: (agentId: string, updates: AgentUpdate) => Promise<void>;
}

function getStatusBadge(status: Agent["status"]) {
  switch (status) {
    case "active":
      return <Badge className="bg-green-100 text-green-800 hover:bg-green-100 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-900/40">🟢 活跃</Badge>;
    case "inactive":
      return <Badge variant="secondary">🟡 非活跃</Badge>;
    case "error":
      return <Badge variant="destructive">🔴 错误</Badge>;
    default:
      return <Badge variant="outline">未知</Badge>;
  }
}

// Mobile-optimized agent card component
function MobileAgentCard({
  agent,
  isSelected,
  isProcessing,
  onSelect,
  onAction,
  onEdit
}: {
  agent: Agent;
  isSelected: boolean;
  isProcessing: boolean;
  onSelect: (selected: boolean) => void;
  onAction: (action: string) => void;
  onEdit: () => void;
}) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isStatsDialogOpen, setIsStatsDialogOpen] = useState(false);
  const router = useRouter();

  const handleSaveAsTemplate = () => {
    router.push(`/templates/create?agent=${agent.agent_id}`);
  };

  return (
    <Card className="hover:shadow-lg transition-all duration-300 border-border/50 hover:border-border">
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between gap-4">
          <div className="flex items-start space-x-4 flex-1 min-w-0">
            <input
              type="checkbox"
              checked={isSelected}
              onChange={(e) => onSelect(e.target.checked)}
              className="mt-2 min-h-[20px] min-w-[20px] h-5 w-5 rounded border-2 border-muted-foreground/30 text-primary focus:ring-2 focus:ring-primary/20 touch-target"
            />
            <div className="flex-1 min-w-0">
              <CardTitle className="flex items-center gap-2 text-lg font-semibold leading-tight mb-2">
                <span className="truncate">{agent.team_name}</span>
                {getStatusBadge(agent.status)}
              </CardTitle>
              <CardDescription className="text-sm line-clamp-2 text-muted-foreground/80 mb-3">
                {agent.description}
              </CardDescription>

              {/* Essential info - always visible */}
              <div className="flex items-center gap-2 text-xs text-muted-foreground bg-muted/30 rounded-md px-3 py-2">
                <span className="font-medium">调用 {agent.usage_count} 次</span>
                <span>•</span>
                <span>{formatRelativeTime(agent.created_at)}</span>
              </div>
            </div>
          </div>

          {/* Mobile action menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="min-h-[44px] min-w-[44px] touch-target hover:bg-muted/80 rounded-lg"
              >
                <MoreHorizontal className="h-5 w-5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem onClick={() => setIsStatsDialogOpen(true)}>
                📊 统计
              </DropdownMenuItem>
              <DropdownMenuItem onClick={onEdit}>
                ✏️ 编辑
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleSaveAsTemplate}>
                📚 保存为模板
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onAction("toggle_status")} disabled={isProcessing}>
                {isProcessing ? "处理中..." : (agent.status === "active" ? "停用" : "激活")}
              </DropdownMenuItem>
              <DeleteConfirmDialog
                trigger={
                  <DropdownMenuItem
                    onSelect={(e) => e.preventDefault()}
                    className="text-destructive focus:text-destructive"
                  >
                    删除
                  </DropdownMenuItem>
                }
                itemName={`${agent.team_name} (ID: ${agent.agent_id})`}
                onConfirm={() => onAction("delete")}
              />
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      {/* Expandable content */}
      <CardContent className="pt-0 border-t border-border/50">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsExpanded(!isExpanded)}
          className="w-full justify-between min-h-[44px] touch-target hover:bg-muted/50 rounded-lg mt-2"
        >
          <span className="font-medium">查看详细信息</span>
          {isExpanded ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
        </Button>

        {isExpanded && (
          <div className="mt-4 space-y-4 animate-in slide-in-from-top-2 duration-200">
            <div>
              <h4 className="font-medium text-sm mb-3 text-muted-foreground uppercase tracking-wide">团队成员</h4>
              <div className="flex flex-wrap gap-2">
                {getAgentSpecialists(agent).map((specialist, index) => (
                  <Badge key={index} variant="secondary" className="text-xs px-3 py-1 bg-muted/50">
                    {specialist.name}
                  </Badge>
                ))}
              </div>
            </div>

            <div className="grid grid-cols-1 gap-4 text-sm">
              <div className="bg-muted/30 rounded-lg p-3">
                <span className="font-medium text-muted-foreground">Agent ID</span>
                <p className="text-xs mt-1 break-all font-mono">
                  {agent.agent_id}
                </p>
              </div>
              <div className="bg-muted/30 rounded-lg p-3">
                <span className="font-medium text-muted-foreground">最后使用</span>
                <p className="text-xs mt-1">
                  {formatRelativeTime(agent.last_used, "从未使用")}
                </p>
              </div>
            </div>

            <div className="flex gap-2 pt-2">
              <AgentDetailDialog agent={agent} />
              <Button asChild variant="outline" size="sm" className="flex-1 text-xs">
                <Link href={`/test?agent=${agent.agent_id}`}>
                  <TestTube className="h-4 w-4 mr-1" />
                  测试
                </Link>
              </Button>
            </div>
          </div>
        )}
      </CardContent>

      {/* Stats Dialog - triggered from dropdown */}
      <Dialog open={isStatsDialogOpen} onOpenChange={setIsStatsDialogOpen}>
        <DialogContent className="max-w-lg max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              📊 {agent.team_name} - 统计信息
            </DialogTitle>
            <DialogDescription>
              Agent 性能和使用统计
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 bg-muted/30 rounded-lg">
                <div className="text-2xl font-bold">{agent.usage_count}</div>
                <div className="text-sm text-muted-foreground">总调用次数</div>
              </div>
              <div className="text-center p-4 bg-muted/30 rounded-lg">
                <div className="text-2xl font-bold">{agent.success_rate || 0}%</div>
                <div className="text-sm text-muted-foreground">成功率</div>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 bg-muted/30 rounded-lg">
                <div className="text-2xl font-bold">{agent.avg_response_time || 0}ms</div>
                <div className="text-sm text-muted-foreground">平均响应时间</div>
              </div>
              <div className="text-center p-4 bg-muted/30 rounded-lg">
                <div className="text-2xl font-bold">{formatRelativeTime(agent.last_used, "从未")}</div>
                <div className="text-sm text-muted-foreground">最后使用</div>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </Card>
  );
}

// Desktop-optimized agent card component for grid layout
function DesktopAgentCard({
  agent,
  isSelected,
  isProcessing,
  isHighlighted,
  onSelect,
  onAction,
  onEdit
}: {
  agent: Agent;
  isSelected: boolean;
  isProcessing: boolean;
  isHighlighted: boolean;
  onSelect: (selected: boolean) => void;
  onAction: (action: string) => void;
  onEdit: () => void;
}) {
  const [isStatsDialogOpen, setIsStatsDialogOpen] = useState(false);
  const router = useRouter();

  const handleSaveAsTemplate = () => {
    router.push(`/templates/create?agent=${agent.agent_id}`);
  };

  return (
    <Card
      className={`hover:shadow-lg transition-all duration-300 h-full flex flex-col border-border/50 hover:border-border ${
        isHighlighted ? "ring-2 ring-primary shadow-lg border-primary/20" : ""
      }`}
    >
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between gap-3">
          <div className="flex items-start space-x-3 flex-1 min-w-0">
            <input
              type="checkbox"
              checked={isSelected}
              onChange={(e) => onSelect(e.target.checked)}
              className="mt-1.5 flex-shrink-0 h-4 w-4 rounded border-2 border-muted-foreground/30 text-primary focus:ring-2 focus:ring-primary/20"
            />
            <div className="flex-1 min-w-0">
              <CardTitle className="flex items-center gap-2 text-lg font-semibold leading-tight mb-2">
                <span className="truncate">{agent.team_name}</span>
                {getStatusBadge(agent.status)}
              </CardTitle>
              <CardDescription className="text-sm line-clamp-2 text-muted-foreground/80">
                {agent.description}
              </CardDescription>
            </div>
          </div>

          {/* Compact action menu for grid layout */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="flex-shrink-0 h-8 w-8 p-0 hover:bg-muted/80"
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem onClick={() => setIsStatsDialogOpen(true)}>
                📊 统计
              </DropdownMenuItem>
              <DropdownMenuItem onClick={onEdit}>
                ✏️ 编辑
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleSaveAsTemplate}>
                📚 保存为模板
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onAction("toggle_status")} disabled={isProcessing}>
                {isProcessing ? "处理中..." : (agent.status === "active" ? "停用" : "激活")}
              </DropdownMenuItem>
              <DeleteConfirmDialog
                trigger={
                  <DropdownMenuItem
                    onSelect={(e) => e.preventDefault()}
                    className="text-destructive focus:text-destructive"
                  >
                    删除
                  </DropdownMenuItem>
                }
                itemName={`${agent.team_name} (ID: ${agent.agent_id})`}
                onConfirm={() => onAction("delete")}
              />
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="pt-0 flex-1 flex flex-col">
        {/* Team members */}
        <div className="mb-4">
          <h4 className="text-xs font-medium text-muted-foreground mb-2 uppercase tracking-wide">团队成员</h4>
          <div className="flex flex-wrap gap-1.5">
            {getAgentSpecialists(agent).slice(0, 3).map((specialist, index) => (
              <Badge key={index} variant="secondary" className="text-xs px-2 py-1 bg-muted/50">
                {specialist.name}
              </Badge>
            ))}
            {getAgentSpecialists(agent).length > 3 && (
              <Badge variant="secondary" className="text-xs px-2 py-1 bg-muted/50">
                +{getAgentSpecialists(agent).length - 3}
              </Badge>
            )}
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 gap-3 mb-4 text-xs">
          <div className="space-y-1">
            <span className="font-medium text-muted-foreground">调用次数</span>
            <p className="text-sm font-semibold">{agent.usage_count}</p>
          </div>
          <div className="space-y-1">
            <span className="font-medium text-muted-foreground">创建时间</span>
            <p className="text-sm font-semibold">{formatRelativeTime(agent.created_at)}</p>
          </div>
        </div>

        {/* Quick actions */}
        <div className="flex gap-2 mt-auto pt-2 border-t border-border/50">
          <AgentDetailDialog agent={agent} />
          <Button asChild variant="outline" size="sm" className="flex-1 text-xs">
            <Link href={`/test?agent=${agent.agent_id}`}>
              <TestTube className="h-4 w-4 mr-1" />
              测试
            </Link>
          </Button>
        </div>
      </CardContent>

      {/* Stats Dialog - triggered from dropdown */}
      <Dialog open={isStatsDialogOpen} onOpenChange={setIsStatsDialogOpen}>
        <DialogContent className="max-w-lg max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              📊 {agent.team_name} - 统计信息
            </DialogTitle>
            <DialogDescription>
              Agent 性能和使用统计
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 bg-muted/30 rounded-lg">
                <div className="text-2xl font-bold">{agent.usage_count}</div>
                <div className="text-sm text-muted-foreground">总调用次数</div>
              </div>
              <div className="text-center p-4 bg-muted/30 rounded-lg">
                <div className="text-2xl font-bold">{agent.success_rate || 0}%</div>
                <div className="text-sm text-muted-foreground">成功率</div>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 bg-muted/30 rounded-lg">
                <div className="text-2xl font-bold">{agent.avg_response_time || 0}ms</div>
                <div className="text-sm text-muted-foreground">平均响应时间</div>
              </div>
              <div className="text-center p-4 bg-muted/30 rounded-lg">
                <div className="text-2xl font-bold">{formatRelativeTime(agent.last_used, "从未")}</div>
                <div className="text-sm text-muted-foreground">最后使用</div>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </Card>
  );
}

function AgentDetailDialog({ agent }: { agent: Agent }) {
  const { agentApiBaseUrl } = useSystemSettings();
  const apiEndpoint = getAgentApiEndpoint(agent.agent_id, agentApiBaseUrl);

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="flex-1 text-xs">
          查看详情
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            🤖 {agent.team_name}
            {getStatusBadge(agent.status)}
          </DialogTitle>
          <DialogDescription>
            Agent ID: {agent.agent_id}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div>
            <h4 className="font-semibold mb-2">描述</h4>
            <p className="text-sm text-muted-foreground">{agent.description}</p>
          </div>

          <div>
            <h4 className="font-semibold mb-2">API端点</h4>
            <div className="bg-muted p-3 rounded-lg">
              <code className="text-sm text-foreground break-all">
                {apiEndpoint}
              </code>
              <div className="mt-2 flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={async () => {
                    try {
                      await navigator.clipboard.writeText(apiEndpoint);
                      // You could add a toast notification here
                      if (process.env.NODE_ENV === 'development') {
                        console.log('API endpoint copied to clipboard');
                      }
                    } catch (err) {
                      if (process.env.NODE_ENV === 'development') {
                        console.error('Failed to copy to clipboard:', err);
                      }
                    }
                  }}
                >
                  📋 复制
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
                    window.open(`${apiUrl}/docs#/agents/${agent.agent_id}_execute_agent_post`, '_blank');
                  }}
                >
                  📖 API文档
                </Button>
              </div>
            </div>
          </div>

          <div>
            <h4 className="font-semibold mb-2">团队成员</h4>
            <div className="space-y-3">
              {getAgentTeamMembers(agent).map((member, index) => (
                <div key={index} className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium">{member.name}</span>
                    <Badge variant="outline">{member.role}</Badge>
                  </div>
                  {member.description && (
                    <p className="text-sm text-muted-foreground mb-2">{member.description}</p>
                  )}
                  {member.system_prompt && (
                    <div className="text-xs bg-muted p-2 rounded">
                      <span className="font-medium">系统提示: </span>
                      <span className="text-muted-foreground">{member.system_prompt}</span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Workflow Steps Display */}
          <div>
            <h4 className="font-semibold mb-2">工作流程</h4>
            <div className="space-y-3">
              {getAgentWorkflowSteps(agent).map((step, index) => (
                <div key={index} className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium">{step.name}</span>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">
                        步骤 {index + 1}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {step.assignee}
                      </Badge>
                    </div>
                  </div>
                  {step.description && (
                    <p className="text-sm text-muted-foreground mb-2">{step.description}</p>
                  )}
                  {step.inputs && step.inputs.length > 0 && (
                    <div className="text-xs mb-1">
                      <span className="font-medium text-green-600">输入: </span>
                      <span className="text-muted-foreground">{step.inputs.join(", ")}</span>
                    </div>
                  )}
                  {step.outputs && step.outputs.length > 0 && (
                    <div className="text-xs">
                      <span className="font-medium text-blue-600">输出: </span>
                      <span className="text-muted-foreground">{step.outputs.join(", ")}</span>
                    </div>
                  )}
                </div>
              ))}
              {getAgentWorkflowSteps(agent).length === 0 && (
                <div className="text-center py-4 text-muted-foreground">
                  <div className="text-2xl mb-2">📋</div>
                  <p className="text-sm">暂无工作流程信息</p>
                </div>
              )}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="font-semibold mb-1">创建时间</h4>
              <p className="text-sm text-muted-foreground">
                {formatRelativeTime(agent.created_at)}
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-1">最后使用</h4>
              <p className="text-sm text-muted-foreground">
                {formatRelativeTime(agent.last_used, "从未使用")}
              </p>
            </div>
          </div>

          <div>
            <h4 className="font-semibold mb-1">使用统计</h4>
            <p className="text-sm text-muted-foreground">
              总调用次数: {agent.usage_count}
            </p>
          </div>


        </div>
      </DialogContent>
    </Dialog>
  );
}

function AgentStatsDialog({ agent }: { agent: Agent }) {
  const successRate = agent.success_rate || 0;
  const avgResponseTime = agent.avg_response_time || 0;

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="flex-1 text-xs">
          📊 统计
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-lg max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            📊 {agent.team_name} - 统计信息
          </DialogTitle>
          <DialogDescription>
            Agent 性能和使用统计
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{agent.usage_count || 0}</div>
              <div className="text-sm text-muted-foreground">总调用次数</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">{successRate.toFixed(1)}%</div>
              <div className="text-sm text-muted-foreground">成功率</div>
            </div>
          </div>

          <div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium">成功率</span>
              <span className="text-sm text-muted-foreground">{successRate.toFixed(1)}%</span>
            </div>
            <Progress value={successRate} className="h-2" />
          </div>

          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">平均响应时间</span>
              <span className="text-sm font-medium">
                {avgResponseTime > 0 ? `${avgResponseTime.toFixed(2)}s` : '暂无数据'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">创建时间</span>
              <span className="text-sm font-medium">
                {formatRelativeTime(agent.created_at)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">最后使用</span>
              <span className="text-sm font-medium">
                {formatRelativeTime(agent.last_used, "从未使用")}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">团队成员数</span>
              <span className="text-sm font-medium">
                {getAgentSpecialists(agent).length} 人
              </span>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export function AgentList({ agents, selectedAgents, highlightedAgent, processingAgents = new Set(), onAgentSelect, onAgentAction, onAgentEdit }: AgentListProps) {
  const router = useRouter();
  const isMobile = useIsMobile();
  const [editingAgent, setEditingAgent] = useState<Agent | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  const handleEditAgent = (agent: Agent) => {
    setEditingAgent(agent);
    setIsEditDialogOpen(true);
  };

  const handleSaveEdit = async (agentId: string, updates: AgentUpdate) => {
    if (onAgentEdit) {
      await onAgentEdit(agentId, updates);
    }
  };
  if (agents.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <div className="text-6xl mb-4">🤖</div>
          <h3 className="text-lg font-semibold mb-2">暂无Agent</h3>
          <p className="text-muted-foreground text-center mb-4">
            你还没有创建任何Agent团队。<br />
            点击上方按钮开始创建你的第一个Agent。
          </p>
          <Button>
            创建第一个Agent
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={isMobile ? "space-y-4" : "grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4"}>
      {agents.map((agent) => {
        const isSelected = selectedAgents.includes(agent.agent_id);
        const isProcessing = processingAgents.has(agent.agent_id);
        const isHighlighted = highlightedAgent === agent.agent_id;

        return isMobile ? (
          <MobileAgentCard
            key={agent.agent_id}
            agent={agent}
            isSelected={isSelected}
            isProcessing={isProcessing}
            onSelect={(selected) => onAgentSelect(agent.agent_id, selected)}
            onAction={(action) => onAgentAction(agent.agent_id, action)}
            onEdit={() => handleEditAgent(agent)}
          />
        ) : (
          <DesktopAgentCard
            key={agent.agent_id}
            agent={agent}
            isSelected={isSelected}
            isProcessing={isProcessing}
            isHighlighted={isHighlighted}
            onSelect={(selected) => onAgentSelect(agent.agent_id, selected)}
            onAction={(action) => onAgentAction(agent.agent_id, action)}
            onEdit={() => handleEditAgent(agent)}
          />
        );
      })}

      {/* Edit Dialog */}
      <AgentEditDialog
        agent={editingAgent}
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        onSave={handleSaveEdit}
      />
    </div>
  );
}
