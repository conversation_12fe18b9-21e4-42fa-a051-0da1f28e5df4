"use client";

import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { useIsMobile } from "@/hooks/use-mobile";

export function AgentCardSkeleton() {
  const isMobile = useIsMobile();

  if (isMobile) {
    return <MobileAgentCardSkeleton />;
  }

  return <DesktopAgentCardSkeleton />;
}

function MobileAgentCardSkeleton() {
  return (
    <Card className="animate-pulse">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3 flex-1 min-w-0">
            <Skeleton className="h-6 w-6 rounded" />
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2">
                <Skeleton className="h-5 w-32" />
                <Skeleton className="h-5 w-16 rounded-full" />
              </div>
              <Skeleton className="h-4 w-full mt-2" />
              <Skeleton className="h-4 w-3/4 mt-1" />
              
              <div className="flex items-center gap-2 mt-2">
                <Skeleton className="h-3 w-16" />
                <Skeleton className="h-3 w-1" />
                <Skeleton className="h-3 w-20" />
              </div>
            </div>
          </div>
          <Skeleton className="h-11 w-11 rounded-md" />
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <Skeleton className="h-11 w-full rounded-md" />
      </CardContent>
    </Card>
  );
}

function DesktopAgentCardSkeleton() {
  return (
    <Card className="animate-pulse h-full flex flex-col">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3 flex-1 min-w-0">
            <Skeleton className="h-5 w-5 rounded" />
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2">
                <Skeleton className="h-5 w-40" />
                <Skeleton className="h-5 w-16 rounded-full" />
              </div>
              <Skeleton className="h-4 w-full mt-2" />
              <Skeleton className="h-4 w-2/3 mt-1" />
            </div>
          </div>
          <Skeleton className="h-8 w-8 rounded-md" />
        </div>
      </CardHeader>
      
      <CardContent className="pt-0 flex-1 flex flex-col justify-between">
        {/* Team members skeleton */}
        <div className="mb-3">
          <div className="flex flex-wrap gap-1">
            <Skeleton className="h-6 w-16 rounded-full" />
            <Skeleton className="h-6 w-20 rounded-full" />
            <Skeleton className="h-6 w-14 rounded-full" />
          </div>
        </div>
        
        {/* Stats skeleton */}
        <div className="grid grid-cols-2 gap-2 mb-3">
          <div>
            <Skeleton className="h-3 w-16 mb-1" />
            <Skeleton className="h-4 w-8" />
          </div>
          <div>
            <Skeleton className="h-3 w-16 mb-1" />
            <Skeleton className="h-4 w-12" />
          </div>
        </div>
        
        {/* Quick actions skeleton */}
        <div className="flex gap-1">
          <Skeleton className="h-8 w-20" />
          <Skeleton className="h-8 w-16" />
        </div>
      </CardContent>
    </Card>
  );
}

// Skeleton for agent stats
export function AgentStatsSkeleton() {
  return (
    <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4">
      {Array.from({ length: 4 }).map((_, index) => (
        <Card key={index} className="min-h-[120px] md:min-h-[140px] animate-pulse">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 px-4 md:px-6 pt-4 md:pt-6">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-6 w-6 rounded-full" />
          </CardHeader>
          <CardContent className="px-4 md:px-6 pb-4 md:pb-6">
            <Skeleton className="h-8 w-12 mb-2" />
            <Skeleton className="h-3 w-24" />
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

// Skeleton for agent filters
export function AgentFiltersSkeleton() {
  return (
    <Card>
      <CardContent className="pt-4 md:pt-6 px-4 md:px-6">
        <div className="flex flex-col lg:flex-row gap-3 md:gap-4 items-start lg:items-center justify-between">
          <div className="flex flex-col sm:flex-row gap-3 md:gap-4 flex-1 w-full">
            <Skeleton className="h-11 w-full sm:max-w-sm" />
            <Skeleton className="h-11 w-full sm:w-[140px]" />
            <Skeleton className="h-11 w-full sm:w-[140px]" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
