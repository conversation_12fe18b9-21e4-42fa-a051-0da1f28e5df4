"use client";

import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { DeleteConfirmDialog } from "@/components/common/confirm-dialog";

interface AgentFiltersProps {
  onFilterChange: (filters: {
    status?: string;
    search?: string;
    sortBy?: string;
  }) => void;
  selectedCount: number;
  onBulkAction: (action: string) => void;
}

export function AgentFilters({ onFilterChange, selectedCount, onBulkAction }: AgentFiltersProps) {
  const [filters, setFilters] = useState({
    status: "all",
    search: "",
    sortBy: "created"
  });

  const updateFilter = (key: string, value: string) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  return (
    <Card>
      <CardContent className="pt-3 md:pt-6 px-3 md:px-6 mobile-card-spacing">
        <div className="flex flex-col lg:flex-row gap-3 md:gap-4 items-start lg:items-center justify-between">
          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-2 md:gap-4 flex-1 w-full mobile-grid-gap">
            {/* Search */}
            <div className="flex-1 max-w-full sm:max-w-sm">
              <Input
                placeholder="搜索Agent名称或ID..."
                value={filters.search}
                onChange={(e) => updateFilter("search", e.target.value)}
                className="w-full touch-target min-h-[44px] md:min-h-[36px] mobile-form-element"
              />
            </div>

            {/* Status Filter */}
            <Select value={filters.status} onValueChange={(value) => updateFilter("status", value)}>
              <SelectTrigger className="w-full sm:w-[140px] touch-target min-h-[44px] md:min-h-[36px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部状态</SelectItem>
                <SelectItem value="active">🟢 活跃</SelectItem>
                <SelectItem value="inactive">🟡 非活跃</SelectItem>
                <SelectItem value="error">🔴 错误</SelectItem>
              </SelectContent>
            </Select>

            {/* Sort */}
            <Select value={filters.sortBy} onValueChange={(value) => updateFilter("sortBy", value)}>
              <SelectTrigger className="w-full sm:w-[140px] touch-target min-h-[44px] md:min-h-[36px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="created">创建时间</SelectItem>
                <SelectItem value="name">名称</SelectItem>
                <SelectItem value="usage">使用次数</SelectItem>
                <SelectItem value="last_used">最后使用</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Bulk Actions */}
          {selectedCount > 0 && (
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 w-full lg:w-auto">
              <Badge variant="secondary" className="text-xs mobile-text-sm">
                已选择 {selectedCount} 个Agent
              </Badge>
              <div className="flex flex-wrap gap-2 w-full sm:w-auto">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onBulkAction("activate")}
                  className="flex-1 sm:flex-none touch-target min-h-[44px] md:min-h-[32px] mobile-button-spacing"
                >
                  激活
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onBulkAction("deactivate")}
                  className="flex-1 sm:flex-none touch-target min-h-[44px] md:min-h-[32px] mobile-button-spacing"
                >
                  停用
                </Button>
                <DeleteConfirmDialog
                  trigger={
                    <Button
                      variant="destructive"
                      size="sm"
                      className="flex-1 sm:flex-none min-h-[44px] md:min-h-[32px]"
                    >
                      删除
                    </Button>
                  }
                  itemName={`${selectedCount} 个Agent`}
                  onConfirm={() => onBulkAction("delete")}
                />
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
