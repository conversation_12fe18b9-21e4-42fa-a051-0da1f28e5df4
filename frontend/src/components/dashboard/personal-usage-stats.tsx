"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import { 
  Calendar,
  Clock,
  TrendingUp,
  TrendingDown,
  Activity,
  Zap,
  Target,
  BarChart3
} from 'lucide-react';
import { 
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';

// Personal Usage Stats Types
interface UsageStats {
  today: {
    executions: number;
    successRate: number;
    avgResponseTime: number;
    activeAgents: number;
  };
  week: {
    executions: number;
    successRate: number;
    avgResponseTime: number;
    mostUsedAgent: string;
  };
  month: {
    executions: number;
    successRate: number;
    avgResponseTime: number;
    totalAgents: number;
  };
  trend: 'up' | 'down' | 'stable';
  dailyUsage: Array<{
    date: string;
    executions: number;
    success: number;
  }>;
}

interface PersonalUsageStatsProps {
  className?: string;
  stats?: UsageStats;
  loading?: boolean;
}

// Mock data for development
const mockStats: UsageStats = {
  today: {
    executions: 12,
    successRate: 95,
    avgResponseTime: 1200,
    activeAgents: 3
  },
  week: {
    executions: 78,
    successRate: 92,
    avgResponseTime: 1350,
    mostUsedAgent: '代码审查助手'
  },
  month: {
    executions: 234,
    successRate: 89,
    avgResponseTime: 1400,
    totalAgents: 5
  },
  trend: 'up',
  dailyUsage: [
    { date: '周一', executions: 15, success: 14 },
    { date: '周二', executions: 12, success: 11 },
    { date: '周三', executions: 18, success: 17 },
    { date: '周四', executions: 8, success: 8 },
    { date: '周五', executions: 22, success: 20 },
    { date: '周六', executions: 5, success: 5 },
    { date: '周日', executions: 3, success: 3 }
  ]
};

function StatCard({ title, value, subtitle, trend, icon: Icon, color = "blue" }: {
  title: string;
  value: string | number;
  subtitle: string;
  trend?: 'up' | 'down' | 'stable';
  icon: React.ElementType;
  color?: string;
}) {
  const getTrendIcon = () => {
    switch (trend) {
      case 'up': return <TrendingUp className="h-3 w-3 text-green-500" />;
      case 'down': return <TrendingDown className="h-3 w-3 text-red-500" />;
      default: return null;
    }
  };

  const getColorClasses = (color: string) => {
    switch (color) {
      case 'green': return 'bg-green-50 border-green-200 dark:bg-green-950/20 dark:border-green-800';
      case 'blue': return 'bg-blue-50 border-blue-200 dark:bg-blue-950/20 dark:border-blue-800';
      case 'purple': return 'bg-purple-50 border-purple-200 dark:bg-purple-950/20 dark:border-purple-800';
      case 'orange': return 'bg-orange-50 border-orange-200 dark:bg-orange-950/20 dark:border-orange-800';
      default: return 'bg-gray-50 border-gray-200 dark:bg-gray-950/20 dark:border-gray-800';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card className={cn("relative overflow-hidden", getColorClasses(color))}>
        <CardContent className="p-3 sm:p-4">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <Icon className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground flex-shrink-0" />
                <span className="text-xs sm:text-sm font-medium text-muted-foreground truncate">{title}</span>
                {getTrendIcon()}
              </div>
              <div className="text-lg sm:text-2xl font-bold mb-1">{value}</div>
              <div className="text-xs text-muted-foreground">{subtitle}</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

function UsageChart({ data }: { data: Array<{ date: string; executions: number; success: number }> }) {
  return (
    <div className="h-48 sm:h-64">
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart data={data} margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
          <XAxis
            dataKey="date"
            axisLine={false}
            tickLine={false}
            className="text-xs"
            tick={{ fontSize: 10 }}
          />
          <YAxis
            axisLine={false}
            tickLine={false}
            className="text-xs"
            tick={{ fontSize: 10 }}
            width={30}
          />
          <Tooltip
            contentStyle={{
              backgroundColor: 'hsl(var(--background))',
              border: '1px solid hsl(var(--border))',
              borderRadius: '8px',
              fontSize: '11px'
            }}
          />
          <Area
            type="monotone"
            dataKey="executions"
            stroke="hsl(var(--primary))"
            fill="hsl(var(--primary))"
            fillOpacity={0.1}
            strokeWidth={2}
            name="总执行"
          />
          <Area
            type="monotone"
            dataKey="success"
            stroke="hsl(var(--chart-2))"
            fill="hsl(var(--chart-2))"
            fillOpacity={0.1}
            strokeWidth={2}
            name="成功执行"
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
}

export function PersonalUsageStats({ className, stats = mockStats, loading = false }: PersonalUsageStatsProps) {
  const [activeTab, setActiveTab] = useState('today');

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <div className="h-6 bg-muted animate-pulse rounded" />
          <div className="h-4 bg-muted animate-pulse rounded w-2/3" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {Array.from({ length: 4 }).map((_, i) => (
                <div key={i} className="h-20 bg-muted animate-pulse rounded-lg" />
              ))}
            </div>
            <div className="h-64 bg-muted animate-pulse rounded-lg" />
          </div>
        </CardContent>
      </Card>
    );
  }

  const getCurrentStats = () => {
    switch (activeTab) {
      case 'today': return stats.today;
      case 'week': return stats.week;
      case 'month': return stats.month;
      default: return stats.today;
    }
  };

  const currentStats = getCurrentStats();

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart3 className="h-5 w-5" />
          使用统计
        </CardTitle>
        <CardDescription>
          查看您的Agent使用情况和性能表现
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="today">今日</TabsTrigger>
            <TabsTrigger value="week">本周</TabsTrigger>
            <TabsTrigger value="month">本月</TabsTrigger>
          </TabsList>

          <TabsContent value="today" className="space-y-4 sm:space-y-6">
            <div className="grid grid-cols-1 xs:grid-cols-2 lg:grid-cols-4 gap-4">
              <StatCard
                title="执行次数"
                value={stats.today.executions}
                subtitle="今日总执行"
                trend={stats.trend}
                icon={Activity}
                color="blue"
              />
              <StatCard
                title="成功率"
                value={`${stats.today.successRate}%`}
                subtitle="执行成功率"
                trend={stats.today.successRate > 90 ? 'up' : 'down'}
                icon={Target}
                color="green"
              />
              <StatCard
                title="响应时间"
                value={`${stats.today.avgResponseTime}ms`}
                subtitle="平均响应"
                trend={stats.today.avgResponseTime < 1500 ? 'up' : 'down'}
                icon={Clock}
                color="purple"
              />
              <StatCard
                title="活跃Agent"
                value={stats.today.activeAgents}
                subtitle="今日使用"
                icon={Zap}
                color="orange"
              />
            </div>
          </TabsContent>

          <TabsContent value="week" className="space-y-4 sm:space-y-6">
            <div className="grid grid-cols-1 xs:grid-cols-2 lg:grid-cols-4 gap-4">
              <StatCard
                title="执行次数"
                value={stats.week.executions}
                subtitle="本周总执行"
                trend={stats.trend}
                icon={Activity}
                color="blue"
              />
              <StatCard
                title="成功率"
                value={`${stats.week.successRate}%`}
                subtitle="执行成功率"
                trend={stats.week.successRate > 90 ? 'up' : 'down'}
                icon={Target}
                color="green"
              />
              <StatCard
                title="响应时间"
                value={`${stats.week.avgResponseTime}ms`}
                subtitle="平均响应"
                trend={stats.week.avgResponseTime < 1500 ? 'up' : 'down'}
                icon={Clock}
                color="purple"
              />
              <StatCard
                title="最常用"
                value={stats.week.mostUsedAgent}
                subtitle="本周最活跃"
                icon={Zap}
                color="orange"
              />
            </div>

            {/* Weekly Usage Chart */}
            <Card>
              <CardHeader className="pb-3 p-4 sm:p-6">
                <CardTitle className="text-sm sm:text-base">本周使用趋势</CardTitle>
                <CardDescription className="text-xs sm:text-sm">每日执行次数和成功率</CardDescription>
              </CardHeader>
              <CardContent className="p-4 sm:p-6 pt-0">
                <UsageChart data={stats.dailyUsage} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="month" className="space-y-4 sm:space-y-6">
            <div className="grid grid-cols-1 xs:grid-cols-2 lg:grid-cols-4 gap-4">
              <StatCard
                title="执行次数"
                value={stats.month.executions}
                subtitle="本月总执行"
                trend={stats.trend}
                icon={Activity}
                color="blue"
              />
              <StatCard
                title="成功率"
                value={`${stats.month.successRate}%`}
                subtitle="执行成功率"
                trend={stats.month.successRate > 90 ? 'up' : 'down'}
                icon={Target}
                color="green"
              />
              <StatCard
                title="响应时间"
                value={`${stats.month.avgResponseTime}ms`}
                subtitle="平均响应"
                trend={stats.month.avgResponseTime < 1500 ? 'up' : 'down'}
                icon={Clock}
                color="purple"
              />
              <StatCard
                title="总Agent"
                value={stats.month.totalAgents}
                subtitle="创建总数"
                icon={Zap}
                color="orange"
              />
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
