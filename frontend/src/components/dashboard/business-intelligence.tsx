"use client";

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import { 
  BarChart3, 
  DollarSign, 
  TrendingUp, 
  TrendingDown,
  Users, 
  Clock, 
  Target,
  Shield,
  Download,
  Share,
  Calendar,
  Filter,
  Zap,
  Award,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { 
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  ResponsiveContainer,
} from 'recharts';

// Business Intelligence Types
interface UsageAnalytics {
  totalAgentExecutions: number;
  uniqueUsers: number;
  averageSessionTime: number;
  peakUsageHours: string[];
  topPerformingAgents: {
    id: string;
    name: string;
    executions: number;
    successRate: number;
    avgResponseTime: number;
    roi: number;
  }[];
  userEngagement: {
    dailyActiveUsers: number;
    weeklyActiveUsers: number;
    monthlyActiveUsers: number;
    retentionRate: number;
  };
}

interface CostOptimization {
  totalCost: number;
  costPerExecution: number;
  costTrends: {
    date: string;
    cost: number;
    executions: number;
    efficiency: number;
  }[];
  optimizationOpportunities: {
    id: string;
    type: 'model_optimization' | 'resource_allocation' | 'workflow_efficiency';
    title: string;
    description: string;
    potentialSavings: number;
    implementationEffort: 'low' | 'medium' | 'high';
    priority: number;
  }[];
  costBreakdown: {
    category: string;
    amount: number;
    percentage: number;
  }[];
}

interface ComplianceMetrics {
  auditTrail: {
    totalEvents: number;
    criticalEvents: number;
    lastAudit: string;
  };
  dataRetention: {
    compliantRecords: number;
    totalRecords: number;
    retentionPeriod: number;
  };
  accessControl: {
    activeUsers: number;
    privilegedUsers: number;
    lastAccessReview: string;
  };
  securityScore: number;
  complianceStatus: 'compliant' | 'warning' | 'non_compliant';
}

interface TeamCollaboration {
  activeTeams: number;
  sharedAgents: number;
  collaborationScore: number;
  teamPerformance: {
    teamId: string;
    teamName: string;
    members: number;
    agentsCreated: number;
    avgPerformance: number;
    collaborationIndex: number;
  }[];
  knowledgeSharing: {
    templatesShared: number;
    bestPracticesDocumented: number;
    trainingSessionsCompleted: number;
  };
}

// Mock data generators
const generateMockUsageAnalytics = (): UsageAnalytics => ({
  totalAgentExecutions: 15420,
  uniqueUsers: 89,
  averageSessionTime: 28.5,
  peakUsageHours: ['09:00-10:00', '14:00-15:00', '16:00-17:00'],
  topPerformingAgents: [
    {
      id: 'agent_1',
      name: '数据分析助手',
      executions: 3240,
      successRate: 96.8,
      avgResponseTime: 1850,
      roi: 340,
    },
    {
      id: 'agent_2',
      name: '内容创作团队',
      executions: 2890,
      successRate: 94.2,
      avgResponseTime: 2100,
      roi: 280,
    },
    {
      id: 'agent_3',
      name: '客服机器人',
      executions: 4560,
      successRate: 98.1,
      avgResponseTime: 1200,
      roi: 420,
    },
  ],
  userEngagement: {
    dailyActiveUsers: 45,
    weeklyActiveUsers: 78,
    monthlyActiveUsers: 89,
    retentionRate: 87.3,
  },
});

const generateMockCostOptimization = (): CostOptimization => ({
  totalCost: 2840.50,
  costPerExecution: 0.184,
  costTrends: Array.from({ length: 30 }, (_, i) => ({
    date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    cost: 80 + Math.random() * 40,
    executions: 400 + Math.random() * 200,
    efficiency: 0.15 + Math.random() * 0.1,
  })),
  optimizationOpportunities: [
    {
      id: 'opt_1',
      type: 'model_optimization',
      title: '模型选择优化',
      description: '将简单任务从GPT-4切换到GPT-3.5-turbo可节省60%成本',
      potentialSavings: 680,
      implementationEffort: 'low',
      priority: 1,
    },
    {
      id: 'opt_2',
      type: 'workflow_efficiency',
      title: '工作流程优化',
      description: '并行化处理步骤可减少25%的执行时间和成本',
      potentialSavings: 420,
      implementationEffort: 'medium',
      priority: 2,
    },
  ],
  costBreakdown: [
    { category: 'AI模型调用', amount: 1980.35, percentage: 69.7 },
    { category: '计算资源', amount: 568.10, percentage: 20.0 },
    { category: '存储费用', amount: 170.43, percentage: 6.0 },
    { category: '网络传输', amount: 121.62, percentage: 4.3 },
  ],
});

const generateMockComplianceMetrics = (): ComplianceMetrics => ({
  auditTrail: {
    totalEvents: 45230,
    criticalEvents: 12,
    lastAudit: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
  },
  dataRetention: {
    compliantRecords: 44890,
    totalRecords: 45230,
    retentionPeriod: 90,
  },
  accessControl: {
    activeUsers: 89,
    privilegedUsers: 8,
    lastAccessReview: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
  },
  securityScore: 94.2,
  complianceStatus: 'compliant',
});

const generateMockTeamCollaboration = (): TeamCollaboration => ({
  activeTeams: 12,
  sharedAgents: 34,
  collaborationScore: 8.7,
  teamPerformance: [
    {
      teamId: 'team_1',
      teamName: '数据科学团队',
      members: 8,
      agentsCreated: 15,
      avgPerformance: 92.5,
      collaborationIndex: 9.2,
    },
    {
      teamId: 'team_2',
      teamName: '产品团队',
      members: 12,
      agentsCreated: 23,
      avgPerformance: 88.7,
      collaborationIndex: 8.8,
    },
    {
      teamId: 'team_3',
      teamName: '客服团队',
      members: 6,
      agentsCreated: 8,
      avgPerformance: 95.1,
      collaborationIndex: 7.9,
    },
  ],
  knowledgeSharing: {
    templatesShared: 45,
    bestPracticesDocumented: 28,
    trainingSessionsCompleted: 156,
  },
});

interface BusinessIntelligenceProps {
  className?: string;
  timeRange?: '7d' | '30d' | '90d' | '1y';
}

export function BusinessIntelligence({ className, timeRange = '30d' }: BusinessIntelligenceProps) {
  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange);
  const [selectedMetric, setSelectedMetric] = useState('overview');

  // Mock data
  const usageAnalytics = useMemo(() => generateMockUsageAnalytics(), []);
  const costOptimization = useMemo(() => generateMockCostOptimization(), []);
  const complianceMetrics = useMemo(() => generateMockComplianceMetrics(), []);
  const teamCollaboration = useMemo(() => generateMockTeamCollaboration(), []);

  const chartColors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];

  return (
    <Card className={cn('relative overflow-hidden', className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-indigo-600" />
            <CardTitle>商业智能分析</CardTitle>
            <Badge variant="secondary" className="text-xs">
              企业版
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            <Select value={selectedTimeRange} onValueChange={setSelectedTimeRange}>
              <SelectTrigger className="w-32 h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">最近7天</SelectItem>
                <SelectItem value="30d">最近30天</SelectItem>
                <SelectItem value="90d">最近90天</SelectItem>
                <SelectItem value="1y">最近1年</SelectItem>
              </SelectContent>
            </Select>
            <Button size="sm" variant="outline" className="h-8">
              <Download className="h-3 w-3 mr-1" />
              导出
            </Button>
          </div>
        </div>
        <CardDescription>
          全面的业务指标分析和优化建议
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Tabs value={selectedMetric} onValueChange={setSelectedMetric} className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview" className="text-xs">
              使用分析
            </TabsTrigger>
            <TabsTrigger value="cost" className="text-xs">
              成本优化
            </TabsTrigger>
            <TabsTrigger value="compliance" className="text-xs">
              合规监控
            </TabsTrigger>
            <TabsTrigger value="collaboration" className="text-xs">
              团队协作
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Key Metrics */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-4 rounded-lg border bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/20">
                <div className="text-2xl font-bold text-blue-600">{usageAnalytics.totalAgentExecutions.toLocaleString()}</div>
                <div className="text-xs text-muted-foreground">总执行次数</div>
              </div>
              <div className="text-center p-4 rounded-lg border bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/20 dark:to-green-900/20">
                <div className="text-2xl font-bold text-green-600">{usageAnalytics.uniqueUsers}</div>
                <div className="text-xs text-muted-foreground">活跃用户</div>
              </div>
              <div className="text-center p-4 rounded-lg border bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/20 dark:to-purple-900/20">
                <div className="text-2xl font-bold text-purple-600">{usageAnalytics.averageSessionTime.toFixed(1)}min</div>
                <div className="text-xs text-muted-foreground">平均会话时长</div>
              </div>
              <div className="text-center p-4 rounded-lg border bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950/20 dark:to-orange-900/20">
                <div className="text-2xl font-bold text-orange-600">{usageAnalytics.userEngagement.retentionRate.toFixed(1)}%</div>
                <div className="text-xs text-muted-foreground">用户留存率</div>
              </div>
            </div>

            {/* Top Performing Agents */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">🏆 顶级表现 Agents</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {usageAnalytics.topPerformingAgents.map((agent, index) => (
                    <div key={agent.id} className="flex items-center justify-between p-3 rounded-lg border">
                      <div className="flex items-center gap-3">
                        <Badge variant="outline" className="w-8 h-8 rounded-full flex items-center justify-center">
                          {index + 1}
                        </Badge>
                        <div>
                          <h5 className="font-medium text-sm">{agent.name}</h5>
                          <p className="text-xs text-muted-foreground">
                            {agent.executions.toLocaleString()} 次执行 • {agent.successRate.toFixed(1)}% 成功率
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium text-green-600">ROI: {agent.roi}%</div>
                        <div className="text-xs text-muted-foreground">{agent.avgResponseTime}ms</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="cost" className="space-y-6">
            {/* Cost Overview */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 rounded-lg border">
                <div className="text-2xl font-bold">${costOptimization.totalCost.toFixed(2)}</div>
                <div className="text-xs text-muted-foreground">总成本 (本月)</div>
              </div>
              <div className="text-center p-4 rounded-lg border">
                <div className="text-2xl font-bold">${costOptimization.costPerExecution.toFixed(3)}</div>
                <div className="text-xs text-muted-foreground">每次执行成本</div>
              </div>
              <div className="text-center p-4 rounded-lg border">
                <div className="text-2xl font-bold text-green-600">
                  ${costOptimization.optimizationOpportunities.reduce((sum, opp) => sum + opp.potentialSavings, 0)}
                </div>
                <div className="text-xs text-muted-foreground">潜在节省</div>
              </div>
            </div>

            {/* Cost Breakdown */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">💰 成本构成</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={200}>
                    <PieChart>
                      <Pie
                        data={costOptimization.costBreakdown}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        dataKey="amount"
                        label={({ name, percentage }) => `${name} ${percentage.toFixed(1)}%`}
                      >
                        {costOptimization.costBreakdown.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={chartColors[index % chartColors.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value: number) => [`$${value.toFixed(2)}`, '金额']} />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">🎯 优化机会</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {costOptimization.optimizationOpportunities.map((opp) => (
                      <div key={opp.id} className="p-3 rounded-lg border">
                        <div className="flex items-start justify-between mb-2">
                          <h5 className="font-medium text-sm">{opp.title}</h5>
                          <Badge variant="default" className="text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            ${opp.potentialSavings}
                          </Badge>
                        </div>
                        <p className="text-xs text-muted-foreground mb-2">{opp.description}</p>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">
                            {opp.implementationEffort === 'low' ? '🟢 简单' : 
                             opp.implementationEffort === 'medium' ? '🟡 中等' : '🔴 复杂'}
                          </Badge>
                          <span className="text-xs text-muted-foreground">优先级: {opp.priority}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="compliance" className="space-y-6">
            {/* Compliance Overview */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center p-4 rounded-lg border bg-green-50 dark:bg-green-950/20">
                <div className="text-2xl font-bold text-green-600">{complianceMetrics.securityScore.toFixed(1)}</div>
                <div className="text-xs text-muted-foreground">安全评分</div>
              </div>
              <div className="text-center p-4 rounded-lg border">
                <div className="text-2xl font-bold">{complianceMetrics.auditTrail.totalEvents.toLocaleString()}</div>
                <div className="text-xs text-muted-foreground">审计事件</div>
              </div>
              <div className="text-center p-4 rounded-lg border">
                <div className="text-2xl font-bold">{complianceMetrics.dataRetention.compliantRecords.toLocaleString()}</div>
                <div className="text-xs text-muted-foreground">合规记录</div>
              </div>
              <div className="text-center p-4 rounded-lg border">
                <div className="text-2xl font-bold">{complianceMetrics.accessControl.activeUsers}</div>
                <div className="text-xs text-muted-foreground">活跃用户</div>
              </div>
            </div>

            {/* Compliance Status */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  合规状态
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">整体合规状态</span>
                    <Badge variant={complianceMetrics.complianceStatus === 'compliant' ? 'default' : 'destructive'}>
                      {complianceMetrics.complianceStatus === 'compliant' ? '✅ 合规' : '⚠️ 需要关注'}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">数据保留合规率</span>
                    <span className="text-sm font-medium">
                      {((complianceMetrics.dataRetention.compliantRecords / complianceMetrics.dataRetention.totalRecords) * 100).toFixed(1)}%
                    </span>
                  </div>
                  <Progress 
                    value={(complianceMetrics.dataRetention.compliantRecords / complianceMetrics.dataRetention.totalRecords) * 100} 
                    className="h-2" 
                  />
                  <div className="text-xs text-muted-foreground">
                    最后审计: {new Date(complianceMetrics.auditTrail.lastAudit).toLocaleDateString('zh-CN')}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="collaboration" className="space-y-6">
            {/* Collaboration Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center p-4 rounded-lg border">
                <div className="text-2xl font-bold">{teamCollaboration.activeTeams}</div>
                <div className="text-xs text-muted-foreground">活跃团队</div>
              </div>
              <div className="text-center p-4 rounded-lg border">
                <div className="text-2xl font-bold">{teamCollaboration.sharedAgents}</div>
                <div className="text-xs text-muted-foreground">共享 Agents</div>
              </div>
              <div className="text-center p-4 rounded-lg border">
                <div className="text-2xl font-bold">{teamCollaboration.collaborationScore.toFixed(1)}</div>
                <div className="text-xs text-muted-foreground">协作评分</div>
              </div>
              <div className="text-center p-4 rounded-lg border">
                <div className="text-2xl font-bold">{teamCollaboration.knowledgeSharing.templatesShared}</div>
                <div className="text-xs text-muted-foreground">共享模板</div>
              </div>
            </div>

            {/* Team Performance */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">👥 团队表现</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {teamCollaboration.teamPerformance.map((team) => (
                    <div key={team.teamId} className="flex items-center justify-between p-3 rounded-lg border">
                      <div>
                        <h5 className="font-medium text-sm">{team.teamName}</h5>
                        <p className="text-xs text-muted-foreground">
                          {team.members} 成员 • {team.agentsCreated} 个 Agents
                        </p>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium">{team.avgPerformance.toFixed(1)}% 性能</div>
                        <div className="text-xs text-muted-foreground">协作指数: {team.collaborationIndex.toFixed(1)}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </CardContent>

      {/* Animated background gradient */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-indigo-500/5 to-transparent -translate-x-full animate-shimmer" />
    </Card>
  );
}

export default BusinessIntelligence;
