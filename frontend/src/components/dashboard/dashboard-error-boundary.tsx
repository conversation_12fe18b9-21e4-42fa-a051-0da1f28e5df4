"use client";

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  AlertTriangle, 
  RefreshCw, 
  Home, 
  Bug, 
  Copy,
  ExternalLink,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
  level?: 'page' | 'section' | 'component';
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
  showDetails: boolean;
  retryCount: number;
}

class DashboardErrorBoundary extends Component<Props, State> {
  private retryTimeouts: NodeJS.Timeout[] = [];

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
      showDetails: false,
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      errorInfo,
    });

    // Log error to console in development
    if (process.env.NODE_ENV === 'development') {
      console.group('🚨 Dashboard Error Boundary');
      console.error('Error:', error);
      console.error('Error Info:', errorInfo);
      console.error('Component Stack:', errorInfo.componentStack);
      console.groupEnd();
    }

    // Call custom error handler
    this.props.onError?.(error, errorInfo);

    // Log error to external service (in production)
    this.logErrorToService(error, errorInfo);
  }

  componentWillUnmount() {
    // Clear any pending retry timeouts
    this.retryTimeouts.forEach(timeout => clearTimeout(timeout));
  }

  private logErrorToService = (error: Error, errorInfo: ErrorInfo) => {
    // In a real application, you would send this to your error tracking service
    // like Sentry, LogRocket, or Bugsnag
    const errorData = {
      errorId: this.state.errorId,
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      level: this.props.level || 'component',
    };

    // Mock error logging
    if (process.env.NODE_ENV === 'development') {
      console.log('📊 Error logged to service:', errorData);
    }
  };

  private handleRetry = () => {
    const { retryCount } = this.state;
    const maxRetries = 3;
    const retryDelay = Math.min(1000 * Math.pow(2, retryCount), 10000); // Exponential backoff

    if (retryCount < maxRetries) {
      this.setState({ retryCount: retryCount + 1 });

      const timeout = setTimeout(() => {
        this.setState({
          hasError: false,
          error: null,
          errorInfo: null,
          showDetails: false,
        });
      }, retryDelay);

      this.retryTimeouts.push(timeout);
    }
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  private handleReload = () => {
    window.location.reload();
  };

  private copyErrorDetails = () => {
    const { error, errorInfo, errorId } = this.state;
    const errorDetails = `
Error ID: ${errorId}
Message: ${error?.message}
Stack: ${error?.stack}
Component Stack: ${errorInfo?.componentStack}
Timestamp: ${new Date().toISOString()}
URL: ${window.location.href}
User Agent: ${navigator.userAgent}
    `.trim();

    navigator.clipboard.writeText(errorDetails).then(() => {
      // Could show a toast notification here
      console.log('Error details copied to clipboard');
    });
  };

  private toggleDetails = () => {
    this.setState(prev => ({ showDetails: !prev.showDetails }));
  };

  private getErrorSeverity = (): 'low' | 'medium' | 'high' => {
    const { error } = this.state;
    const { level } = this.props;

    if (level === 'page') return 'high';
    if (level === 'section') return 'medium';
    
    // Check error type
    if (error?.name === 'ChunkLoadError' || error?.message?.includes('Loading chunk')) {
      return 'medium';
    }
    
    return 'low';
  };

  private getErrorIcon = () => {
    const severity = this.getErrorSeverity();
    switch (severity) {
      case 'high': return '🚨';
      case 'medium': return '⚠️';
      case 'low': return '⚡';
      default: return '❌';
    }
  };

  private getErrorTitle = () => {
    const { level } = this.props;
    const severity = this.getErrorSeverity();

    if (level === 'page') return '页面加载失败';
    if (level === 'section') return '模块加载失败';
    
    switch (severity) {
      case 'high': return '严重错误';
      case 'medium': return '加载错误';
      case 'low': return '组件错误';
      default: return '未知错误';
    }
  };

  private getErrorDescription = () => {
    const { error } = this.state;
    const { level } = this.props;

    if (error?.name === 'ChunkLoadError' || error?.message?.includes('Loading chunk')) {
      return '代码块加载失败，可能是网络问题或新版本发布导致的。';
    }

    if (level === 'page') {
      return '页面遇到了意外错误，无法正常显示内容。';
    }

    if (level === 'section') {
      return '此模块遇到了错误，但不影响页面其他功能的使用。';
    }

    return '组件渲染时遇到了错误，请尝试刷新或联系技术支持。';
  };

  render() {
    if (this.state.hasError) {
      const { level = 'component', fallback } = this.props;
      const { error, errorInfo, errorId, showDetails, retryCount } = this.state;
      const severity = this.getErrorSeverity();
      const maxRetries = 3;

      // Use custom fallback if provided
      if (fallback) {
        return fallback;
      }

      // Minimal error display for component-level errors
      if (level === 'component') {
        return (
          <Alert variant="destructive" className="my-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="flex items-center justify-between">
              <span>组件加载失败</span>
              <Button
                variant="outline"
                size="sm"
                onClick={this.handleRetry}
                disabled={retryCount >= maxRetries}
                className="ml-2"
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                重试
              </Button>
            </AlertDescription>
          </Alert>
        );
      }

      // Full error page for page/section level errors
      return (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="min-h-[400px] flex items-center justify-center p-4"
        >
          <Card className="max-w-2xl w-full">
            <CardHeader className="text-center">
              <div className="flex items-center justify-center mb-4">
                <div className="text-6xl">{this.getErrorIcon()}</div>
              </div>
              <CardTitle className="text-2xl flex items-center justify-center gap-2">
                {this.getErrorTitle()}
                <Badge variant={severity === 'high' ? 'destructive' : 'secondary'}>
                  {severity === 'high' ? '严重' : severity === 'medium' ? '中等' : '轻微'}
                </Badge>
              </CardTitle>
              <CardDescription className="text-base leading-relaxed">
                {this.getErrorDescription()}
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-6">
              {/* Error ID */}
              <div className="text-center">
                <p className="text-sm text-muted-foreground">
                  错误ID: <code className="bg-muted px-2 py-1 rounded text-xs">{errorId}</code>
                </p>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                {retryCount < maxRetries && (
                  <Button onClick={this.handleRetry} className="flex-1 sm:flex-none">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    重试 {retryCount > 0 && `(${retryCount}/${maxRetries})`}
                  </Button>
                )}
                
                <Button variant="outline" onClick={this.handleReload} className="flex-1 sm:flex-none">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  刷新页面
                </Button>
                
                {level === 'page' && (
                  <Button variant="outline" onClick={this.handleGoHome} className="flex-1 sm:flex-none">
                    <Home className="h-4 w-4 mr-2" />
                    返回首页
                  </Button>
                )}
              </div>

              {/* Error Details Toggle */}
              <div className="border-t pt-4">
                <Button
                  variant="ghost"
                  onClick={this.toggleDetails}
                  className="w-full justify-between"
                >
                  <span className="flex items-center gap-2">
                    <Bug className="h-4 w-4" />
                    技术详情
                  </span>
                  {showDetails ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </Button>

                <AnimatePresence>
                  {showDetails && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.2 }}
                      className="mt-4 space-y-4"
                    >
                      <div className="bg-muted p-4 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-sm">错误信息</h4>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={this.copyErrorDetails}
                            className="h-6 px-2"
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                        </div>
                        <pre className="text-xs bg-background p-2 rounded border overflow-auto max-h-32">
                          {error?.message}
                        </pre>
                      </div>

                      {process.env.NODE_ENV === 'development' && (
                        <div className="bg-muted p-4 rounded-lg">
                          <h4 className="font-medium text-sm mb-2">组件堆栈 (开发模式)</h4>
                          <pre className="text-xs bg-background p-2 rounded border overflow-auto max-h-32">
                            {errorInfo?.componentStack}
                          </pre>
                        </div>
                      )}

                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open('mailto:<EMAIL>?subject=Error Report&body=' + encodeURIComponent(`Error ID: ${errorId}\nMessage: ${error?.message}`))}
                          className="flex-1"
                        >
                          <ExternalLink className="h-3 w-3 mr-1" />
                          报告问题
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={this.copyErrorDetails}
                          className="flex-1"
                        >
                          <Copy className="h-3 w-3 mr-1" />
                          复制详情
                        </Button>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      );
    }

    return this.props.children;
  }
}

export default DashboardErrorBoundary;
