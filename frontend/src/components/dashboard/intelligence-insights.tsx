"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Brain, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  DollarSign,
  Zap,
  Target,
  Lightbulb,
  ArrowRight,
  BarChart3,
  Users,
  Settings,
  Sparkles
} from 'lucide-react';
import { 
  useIntelligenceData, 
  useAgentInsights, 
  usePredictiveInsights,
  AgentInsight,
  PredictiveAnalytics 
} from '@/lib/intelligence-engine';

interface IntelligenceInsightsProps {
  className?: string;
  agentId?: string;
}

const getSeverityIcon = (severity: AgentInsight['severity']) => {
  switch (severity) {
    case 'critical': return <AlertTriangle className="h-4 w-4 text-red-500" />;
    case 'high': return <AlertTriangle className="h-4 w-4 text-orange-500" />;
    case 'medium': return <Clock className="h-4 w-4 text-yellow-500" />;
    case 'low': return <CheckCircle className="h-4 w-4 text-blue-500" />;
    default: return <CheckCircle className="h-4 w-4 text-gray-500" />;
  }
};

const getSeverityColor = (severity: AgentInsight['severity']) => {
  switch (severity) {
    case 'critical': return 'border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950/20';
    case 'high': return 'border-orange-200 dark:border-orange-800 bg-orange-50 dark:bg-orange-950/20';
    case 'medium': return 'border-yellow-200 dark:border-yellow-800 bg-yellow-50 dark:bg-yellow-950/20';
    case 'low': return 'border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-950/20';
    default: return '';
  }
};

const getTypeIcon = (type: AgentInsight['type']) => {
  switch (type) {
    case 'performance': return <Zap className="h-4 w-4" />;
    case 'cost': return <DollarSign className="h-4 w-4" />;
    case 'usage': return <Users className="h-4 w-4" />;
    case 'quality': return <Target className="h-4 w-4" />;
    case 'optimization': return <Settings className="h-4 w-4" />;
    default: return <Lightbulb className="h-4 w-4" />;
  }
};

function InsightCard({ insight, onAction }: { insight: AgentInsight; onAction?: (insight: AgentInsight) => void }) {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className={cn(
        'p-4 rounded-lg border transition-all duration-200 hover:shadow-md cursor-pointer',
        getSeverityColor(insight.severity)
      )}
      onClick={() => setIsExpanded(!isExpanded)}
    >
      <div className="flex items-start justify-between">
        <div className="flex items-start gap-3 flex-1">
          <div className="flex items-center gap-2 mt-1">
            {getSeverityIcon(insight.severity)}
            {getTypeIcon(insight.type)}
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h4 className="font-medium text-sm text-foreground truncate">
                {insight.title}
              </h4>
              <Badge variant="outline" className="text-xs">
                {insight.confidence}% 置信度
              </Badge>
            </div>
            
            <p className="text-sm text-muted-foreground leading-relaxed">
              {insight.description}
            </p>
            
            {insight.agentName && (
              <div className="flex items-center gap-1 mt-2">
                <Badge variant="secondary" className="text-xs">
                  {insight.agentName}
                </Badge>
              </div>
            )}
          </div>
        </div>
        
        <div className="flex items-center gap-2 ml-2">
          {insight.estimatedSavings && (
            <Badge variant="default" className="text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
              节省 ${insight.estimatedSavings}
            </Badge>
          )}
          {insight.estimatedImprovement && (
            <Badge variant="default" className="text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
              提升 {insight.estimatedImprovement}%
            </Badge>
          )}
        </div>
      </div>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className="mt-4 pt-4 border-t border-border/50"
          >
            <div className="space-y-3">
              <div>
                <h5 className="text-sm font-medium text-foreground mb-1">💡 建议方案</h5>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  {insight.recommendation}
                </p>
              </div>
              
              <div>
                <h5 className="text-sm font-medium text-foreground mb-1">📈 预期影响</h5>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  {insight.impact}
                </p>
              </div>
              
              {insight.metrics && (
                <div>
                  <h5 className="text-sm font-medium text-foreground mb-2">📊 相关指标</h5>
                  <div className="grid grid-cols-2 gap-2">
                    {Object.entries(insight.metrics).map(([key, value]) => (
                      <div key={key} className="text-xs">
                        <span className="text-muted-foreground">{key}:</span>
                        <span className="font-medium ml-1">{value}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              {insight.actionable && onAction && (
                <div className="flex justify-end pt-2">
                  <Button
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      onAction(insight);
                    }}
                    className="text-xs"
                  >
                    <Sparkles className="h-3 w-3 mr-1" />
                    应用建议
                  </Button>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}

function PredictiveAnalyticsCard({ analytics }: { analytics: PredictiveAnalytics }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {/* Usage Trend */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            使用趋势预测
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">趋势方向</span>
              <Badge variant={analytics.usage_trend?.direction === 'increasing' ? 'default' : 'secondary'}>
                {analytics.usage_trend?.direction === 'increasing' ? '📈 上升' :
                 analytics.usage_trend?.direction === 'decreasing' ? '📉 下降' : '➡️ 稳定'}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">预测变化</span>
              <span className="font-medium">+{analytics.usage_trend?.predicted_change || 0}%</span>
            </div>
            <Progress value={analytics.usage_trend?.confidence || 0} className="h-2" />
            <p className="text-xs text-muted-foreground">
              置信度: {analytics.usage_trend?.confidence || 0}%
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Cost Forecast */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            成本预测
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">下月预估</span>
              <span className="font-medium">${analytics.cost_forecast?.next_month || 0}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">季度预估</span>
              <span className="font-medium">${analytics.cost_forecast?.next_quarter || 0}</span>
            </div>
            <div className="text-xs text-muted-foreground">
              主要因素: {analytics.cost_forecast?.factors?.join(', ') || '暂无数据'}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Performance Trend */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            性能趋势
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div>
              <div className="flex items-center justify-between mb-1">
                <span className="text-xs text-muted-foreground">成功率</span>
                <span className="text-xs font-medium">
                  {analytics.performance_trend?.success_rate?.current || 0}% → {analytics.performance_trend?.success_rate?.predicted || 0}%
                </span>
              </div>
              <Progress value={analytics.performance_trend?.success_rate?.predicted || 0} className="h-1" />
            </div>
            <div>
              <div className="flex items-center justify-between mb-1">
                <span className="text-xs text-muted-foreground">响应时间</span>
                <span className="text-xs font-medium">
                  {analytics.performance_trend?.response_time?.current || 0}ms → {analytics.performance_trend?.response_time?.predicted || 0}ms
                </span>
              </div>
              <Progress value={100 - ((analytics.performance_trend?.response_time?.predicted || 1000) / 3000 * 100)} className="h-1" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export function IntelligenceInsights({ className, agentId }: IntelligenceInsightsProps) {
  const { data: intelligenceData, isLoading } = useIntelligenceData();
  const insights = useAgentInsights(agentId);
  const { predictiveAnalytics, getHighPriorityInsights, getActionableInsights } = usePredictiveInsights();

  const handleInsightAction = (insight: AgentInsight) => {
    console.log('Applying insight action:', insight);
    // In production, this would trigger the actual optimization
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Brain className="h-5 w-5 animate-pulse" />
            <CardTitle>AI 智能洞察</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="h-20 bg-muted animate-pulse rounded-lg" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const highPriorityInsights = getHighPriorityInsights();
  const actionableInsights = getActionableInsights();

  return (
    <Card className={cn('relative overflow-hidden', className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-purple-600" />
            <CardTitle>AI 智能洞察</CardTitle>
            <Badge variant="secondary" className="text-xs">
              {insights.length} 项洞察
            </Badge>
          </div>
          <div className="text-xs text-muted-foreground">
            最后分析: {intelligenceData?.lastAnalyzed ? 
              new Date(intelligenceData.lastAnalyzed).toLocaleTimeString('zh-CN') : '刚刚'}
          </div>
        </div>
        <CardDescription>
          基于AI分析的性能优化建议和预测性洞察
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Tabs defaultValue="insights" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="insights" className="text-xs">
              洞察建议 ({insights.length})
            </TabsTrigger>
            <TabsTrigger value="predictions" className="text-xs">
              预测分析
            </TabsTrigger>
            <TabsTrigger value="actions" className="text-xs">
              可执行项 ({actionableInsights.length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="insights" className="space-y-4">
            {highPriorityInsights.length > 0 && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  发现 {highPriorityInsights.length} 个高优先级问题需要关注
                </AlertDescription>
              </Alert>
            )}
            
            <div className="space-y-3">
              <AnimatePresence>
                {insights.map((insight) => (
                  <InsightCard
                    key={insight.id}
                    insight={insight}
                    onAction={handleInsightAction}
                  />
                ))}
              </AnimatePresence>
            </div>
          </TabsContent>

          <TabsContent value="predictions" className="space-y-4">
            {predictiveAnalytics && <PredictiveAnalyticsCard analytics={predictiveAnalytics} />}
            
            {predictiveAnalytics?.recommendations && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">🎯 优化建议</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {predictiveAnalytics.recommendations.map((rec, index) => (
                      <div key={index} className="flex items-start gap-3 p-3 rounded-lg border">
                        <Badge variant={rec.priority === 'high' ? 'destructive' : rec.priority === 'medium' ? 'default' : 'secondary'}>
                          {rec.priority === 'high' ? '高' : rec.priority === 'medium' ? '中' : '低'}
                        </Badge>
                        <div className="flex-1">
                          <h5 className="font-medium text-sm">{rec.action}</h5>
                          <p className="text-xs text-muted-foreground mt-1">{rec.expected_impact}</p>
                          <p className="text-xs text-muted-foreground">预计耗时: {rec.time_to_implement}</p>
                        </div>
                        <ArrowRight className="h-4 w-4 text-muted-foreground mt-1" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="actions" className="space-y-4">
            <div className="space-y-3">
              {actionableInsights.map((insight) => (
                <InsightCard
                  key={insight.id}
                  insight={insight}
                  onAction={handleInsightAction}
                />
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>

      {/* Animated background gradient */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-purple-500/5 to-transparent -translate-x-full animate-shimmer" />
    </Card>
  );
}

export default IntelligenceInsights;
