"use client";

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { 
  Zap, 
  Clock, 
  CheckCircle, 
  AlertTriangle 
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface PerformanceIndicatorProps {
  loadTime: number;
  isOptimized: boolean;
  className?: string;
}

export function PerformanceIndicator({ loadTime, isOptimized, className }: PerformanceIndicatorProps) {
  const formatLoadTime = (time: number) => {
    if (time < 1000) {
      return `${Math.round(time)}ms`;
    }
    return `${(time / 1000).toFixed(1)}s`;
  };

  const getPerformanceLevel = (time: number) => {
    if (time < 1000) return 'excellent';
    if (time < 2000) return 'good';
    if (time < 3000) return 'fair';
    return 'poor';
  };

  const performanceLevel = getPerformanceLevel(loadTime);
  
  const performanceConfig = {
    excellent: {
      icon: Zap,
      color: 'bg-green-100 text-green-700 border-green-200',
      label: '极快',
      description: '仪表板加载性能优秀'
    },
    good: {
      icon: CheckCircle,
      color: 'bg-blue-100 text-blue-700 border-blue-200',
      label: '良好',
      description: '仪表板加载性能良好'
    },
    fair: {
      icon: Clock,
      color: 'bg-yellow-100 text-yellow-700 border-yellow-200',
      label: '一般',
      description: '仪表板加载性能一般'
    },
    poor: {
      icon: AlertTriangle,
      color: 'bg-red-100 text-red-700 border-red-200',
      label: '较慢',
      description: '仪表板加载性能需要优化'
    }
  };

  const config = performanceConfig[performanceLevel];
  const IconComponent = config.icon;

  if (loadTime === 0) {
    return null; // Don't show until we have load time data
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge 
            variant="outline" 
            className={cn(
              'text-xs px-2 py-1 flex items-center gap-1 cursor-help',
              config.color,
              className
            )}
          >
            <IconComponent className="h-3 w-3" />
            <span>{formatLoadTime(loadTime)}</span>
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <div className="text-center">
            <p className="font-medium">{config.label}</p>
            <p className="text-xs text-muted-foreground">{config.description}</p>
            <p className="text-xs text-muted-foreground mt-1">
              加载时间: {formatLoadTime(loadTime)}
            </p>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

PerformanceIndicator.displayName = 'PerformanceIndicator';
