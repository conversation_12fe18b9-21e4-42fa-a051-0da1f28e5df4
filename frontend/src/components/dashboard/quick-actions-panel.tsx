"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import { 
  Plus,
  Play,
  Clock,
  Star,
  Zap,
  FileText,
  Code,
  MessageSquare,
  Image,
  Database,
  Settings,
  History,
  Bookmark,
  ArrowRight
} from 'lucide-react';
import Link from 'next/link';

// Quick Actions Types
interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: React.ElementType;
  href: string;
  color: string;
  category: 'create' | 'manage' | 'tools';
}

interface RecentActivity {
  id: string;
  agentName: string;
  action: string;
  timestamp: string;
  status: 'success' | 'error' | 'running';
}

interface FavoriteAgent {
  id: string;
  name: string;
  description: string;
  lastUsed: string;
  category: string;
}

interface QuickActionsPanelProps {
  className?: string;
  recentActivities?: RecentActivity[];
  favoriteAgents?: FavoriteAgent[];
  loading?: boolean;
}

// Mock data
const quickActions: QuickAction[] = [
  {
    id: '1',
    title: '创建新Agent',
    description: '快速创建一个新的AI助手',
    icon: Plus,
    href: '/create',
    color: 'blue',
    category: 'create'
  },
  {
    id: '2',
    title: '管理Agent',
    description: '查看和编辑现有Agent',
    icon: Settings,
    href: '/manage',
    color: 'green',
    category: 'manage'
  },
  {
    id: '3',
    title: '模板库',
    description: '从预设模板快速创建',
    icon: FileText,
    href: '/templates',
    color: 'purple',
    category: 'create'
  },
  {
    id: '4',
    title: 'Agent测试',
    description: '测试Agent的API接口',
    icon: Code,
    href: '/test',
    color: 'orange',
    category: 'tools'
  }
];

const mockRecentActivities: RecentActivity[] = [
  {
    id: '1',
    agentName: '代码审查助手',
    action: '执行代码审查',
    timestamp: '2分钟前',
    status: 'success'
  },
  {
    id: '2',
    agentName: '文档生成器',
    action: '生成API文档',
    timestamp: '15分钟前',
    status: 'success'
  },
  {
    id: '3',
    agentName: '内容创作助手',
    action: '创作博客文章',
    timestamp: '1小时前',
    status: 'error'
  }
];

const mockFavoriteAgents: FavoriteAgent[] = [
  {
    id: '1',
    name: '代码审查助手',
    description: '帮助审查和优化代码质量',
    lastUsed: '2小时前',
    category: '开发工具'
  },
  {
    id: '2',
    name: '内容创作助手',
    description: '协助创作博客文章和技术内容',
    lastUsed: '1天前',
    category: '创作工具'
  }
];

function QuickActionCard({ action, onClick }: { action: QuickAction; onClick?: () => void }) {
  const getColorClasses = (color: string) => {
    switch (color) {
      case 'blue': return 'bg-blue-50 border-blue-200 hover:bg-blue-100 dark:bg-blue-950/20 dark:border-blue-800 dark:hover:bg-blue-950/30';
      case 'green': return 'bg-green-50 border-green-200 hover:bg-green-100 dark:bg-green-950/20 dark:border-green-800 dark:hover:bg-green-950/30';
      case 'purple': return 'bg-purple-50 border-purple-200 hover:bg-purple-100 dark:bg-purple-950/20 dark:border-purple-800 dark:hover:bg-purple-950/30';
      case 'orange': return 'bg-orange-50 border-orange-200 hover:bg-orange-100 dark:bg-orange-950/20 dark:border-orange-800 dark:hover:bg-orange-950/30';
      default: return 'bg-gray-50 border-gray-200 hover:bg-gray-100 dark:bg-gray-950/20 dark:border-gray-800 dark:hover:bg-gray-950/30';
    }
  };

  const IconComponent = action.icon;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.2 }}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
    >
      <Card
        className={cn(
          "cursor-pointer transition-all duration-200 hover:shadow-md group",
          getColorClasses(action.color)
        )}
        onClick={onClick}
      >
        <CardContent className="p-3 sm:p-4">
          <div className="flex items-start gap-2 sm:gap-3">
            <div className="p-2 rounded-lg bg-white/50 dark:bg-black/20 flex-shrink-0">
              <IconComponent className="h-4 w-4 sm:h-5 sm:w-5" />
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="font-medium text-xs sm:text-sm mb-1">{action.title}</h3>
              <p className="text-xs text-muted-foreground line-clamp-2">{action.description}</p>
            </div>
            <ArrowRight className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0" />
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

function RecentActivityItem({ activity }: { activity: RecentActivity }) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-600 dark:text-green-400';
      case 'error': return 'text-red-600 dark:text-red-400';
      case 'running': return 'text-blue-600 dark:text-blue-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'running': return '🔄';
      default: return '⏸️';
    }
  };

  return (
    <div className="flex items-center gap-2 sm:gap-3 p-2 sm:p-3 rounded-lg hover:bg-muted/50 transition-colors min-h-[48px]">
      <div className="text-base sm:text-lg flex-shrink-0">{getStatusIcon(activity.status)}</div>
      <div className="flex-1 min-w-0">
        <div className="font-medium text-xs sm:text-sm truncate">{activity.agentName}</div>
        <div className="text-xs text-muted-foreground truncate">{activity.action}</div>
      </div>
      <div className="text-xs text-muted-foreground flex-shrink-0">{activity.timestamp}</div>
    </div>
  );
}

function FavoriteAgentItem({ agent, onRun }: { agent: FavoriteAgent; onRun: (id: string) => void }) {
  return (
    <div className="flex items-center gap-2 sm:gap-3 p-2 sm:p-3 rounded-lg hover:bg-muted/50 transition-colors group min-h-[60px]">
      <div className="p-2 rounded-lg bg-primary/10 flex-shrink-0">
        <Star className="h-3 w-3 sm:h-4 sm:w-4 text-primary" />
      </div>
      <div className="flex-1 min-w-0">
        <div className="font-medium text-xs sm:text-sm truncate">{agent.name}</div>
        <div className="text-xs text-muted-foreground truncate">{agent.description}</div>
        <div className="text-xs text-muted-foreground">最后使用: {agent.lastUsed}</div>
      </div>
      <Button
        size="sm"
        variant="ghost"
        className="opacity-100 sm:opacity-0 sm:group-hover:opacity-100 transition-opacity min-h-[36px] min-w-[36px] p-2 flex-shrink-0"
        onClick={() => onRun(agent.id)}
      >
        <Play className="h-3 w-3" />
      </Button>
    </div>
  );
}

export function QuickActionsPanel({ 
  className, 
  recentActivities = mockRecentActivities,
  favoriteAgents = mockFavoriteAgents,
  loading = false 
}: QuickActionsPanelProps) {
  const handleActionClick = (action: QuickAction) => {
    console.log('Quick action clicked:', action.title);
    // In production, this would navigate to the action's href
  };

  const handleRunFavorite = (agentId: string) => {
    console.log('Run favorite agent:', agentId);
    // In production, this would navigate to test page with agent
  };

  if (loading) {
    return (
      <div className={cn("space-y-6", className)}>
        <Card>
          <CardHeader>
            <div className="h-6 bg-muted animate-pulse rounded" />
            <div className="h-4 bg-muted animate-pulse rounded w-2/3" />
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-3">
              {Array.from({ length: 4 }).map((_, i) => (
                <div key={i} className="h-20 bg-muted animate-pulse rounded-lg" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Quick Actions */}
      <Card>
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            快速操作
          </CardTitle>
          <CardDescription>
            常用功能快速入口
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {quickActions.map((action) => (
              <Link key={action.id} href={action.href}>
                <QuickActionCard
                  action={action}
                  onClick={() => handleActionClick(action)}
                />
              </Link>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Activities */}
      <Card>
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            最近活动
          </CardTitle>
          <CardDescription>
            最近的Agent执行记录
          </CardDescription>
        </CardHeader>
        <CardContent>
          {recentActivities.length === 0 ? (
            <div className="text-center py-4 text-muted-foreground text-sm">
              暂无最近活动
            </div>
          ) : (
            <div className="space-y-1">
              {recentActivities.map((activity) => (
                <RecentActivityItem key={activity.id} activity={activity} />
              ))}
            </div>
          )}
          {recentActivities.length > 0 && (
            <div className="pt-3 border-t mt-3">
              <Button variant="ghost" size="sm" className="w-full" asChild>
                <Link href="/logs">查看全部活动</Link>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Favorite Agents */}
      <Card>
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2">
            <Bookmark className="h-5 w-5" />
            收藏的Agent
          </CardTitle>
          <CardDescription>
            快速访问常用的AI助手
          </CardDescription>
        </CardHeader>
        <CardContent>
          {favoriteAgents.length === 0 ? (
            <div className="text-center py-4 text-muted-foreground text-sm">
              暂无收藏的Agent
            </div>
          ) : (
            <div className="space-y-1">
              {favoriteAgents.map((agent) => (
                <FavoriteAgentItem 
                  key={agent.id} 
                  agent={agent} 
                  onRun={handleRunFavorite}
                />
              ))}
            </div>
          )}
          {favoriteAgents.length > 0 && (
            <div className="pt-3 border-t mt-3">
              <Button variant="ghost" size="sm" className="w-full" asChild>
                <Link href="/manage">管理所有Agent</Link>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
