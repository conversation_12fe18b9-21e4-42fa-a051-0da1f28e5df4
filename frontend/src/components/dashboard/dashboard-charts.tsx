"use client";

import React from 'react';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import { AgentUsageData, TopAgent, SystemHealth } from '@/lib/dashboard-data';

interface DashboardChartsProps {
  usageHistory?: AgentUsageData[];
  topAgents?: TopAgent[];
  systemHealth?: SystemHealth;
  loading?: boolean;
  className?: string;
}

// Chart color schemes for dark/light theme compatibility
const chartColors = {
  primary: 'hsl(var(--primary))',
  secondary: 'hsl(var(--secondary))',
  accent: 'hsl(var(--accent))',
  muted: 'hsl(var(--muted))',
  success: '#10b981',
  warning: '#f59e0b',
  error: '#ef4444',
  info: '#3b82f6',
};

const pieColors = [
  chartColors.primary,
  chartColors.success,
  chartColors.warning,
  chartColors.error,
  chartColors.info,
];

// Custom tooltip component
const CustomTooltip = ({ active, payload, label, formatter }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-background border border-border rounded-lg shadow-lg p-3">
        <p className="text-sm font-medium text-foreground mb-2">{label}</p>
        {payload.map((entry: any, index: number) => (
          <div key={index} className="flex items-center gap-2 text-sm">
            <div 
              className="w-3 h-3 rounded-full" 
              style={{ backgroundColor: entry.color }}
            />
            <span className="text-muted-foreground">{entry.dataKey}:</span>
            <span className="font-medium text-foreground">
              {formatter ? formatter(entry.value, entry.dataKey) : entry.value}
            </span>
          </div>
        ))}
      </div>
    );
  }
  return null;
};

// Usage trend chart
export function UsageTrendChart({ data, loading }: { data?: AgentUsageData[]; loading?: boolean }) {
  if (loading || !data) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-4 w-48" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-64 w-full" />
        </CardContent>
      </Card>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.1 }}
    >
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            📈 使用趋势
          </CardTitle>
          <CardDescription>
            过去7天的 Agent 使用情况和性能指标
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={data}>
              <defs>
                <linearGradient id="requestsGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor={chartColors.primary} stopOpacity={0.3} />
                  <stop offset="95%" stopColor={chartColors.primary} stopOpacity={0} />
                </linearGradient>
                <linearGradient id="successGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor={chartColors.success} stopOpacity={0.3} />
                  <stop offset="95%" stopColor={chartColors.success} stopOpacity={0} />
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                dataKey="date" 
                className="text-xs"
                tickFormatter={(value) => new Date(value).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })}
              />
              <YAxis className="text-xs" />
              <Tooltip 
                content={<CustomTooltip formatter={(value: number, name: string) => {
                  if (name === 'successRate') return `${value.toFixed(1)}%`;
                  if (name === 'avgResponseTime') return `${value.toFixed(0)}ms`;
                  return value.toLocaleString();
                }} />}
              />
              <Legend />
              <Area
                type="monotone"
                dataKey="requests"
                stroke={chartColors.primary}
                fillOpacity={1}
                fill="url(#requestsGradient)"
                name="请求数量"
              />
              <Line
                type="monotone"
                dataKey="successRate"
                stroke={chartColors.success}
                strokeWidth={2}
                dot={{ fill: chartColors.success, strokeWidth: 2, r: 4 }}
                name="成功率 (%)"
              />
            </AreaChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </motion.div>
  );
}

// Top agents chart
export function TopAgentsChart({ data, loading }: { data?: TopAgent[]; loading?: boolean }) {
  if (loading || !data) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-4 w-48" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-64 w-full" />
        </CardContent>
      </Card>
    );
  }

  const chartData = data.slice(0, 5).map(agent => ({
    name: agent.name.length > 15 ? agent.name.substring(0, 15) + '...' : agent.name,
    fullName: agent.name,
    requests: agent.requests,
    successRate: agent.successRate,
  }));

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.2 }}
    >
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🏆 热门 Agents
          </CardTitle>
          <CardDescription>
            使用次数最多的 Agent 排行榜
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={chartData} layout="horizontal">
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis type="number" className="text-xs" />
              <YAxis 
                type="category" 
                dataKey="name" 
                className="text-xs"
                width={100}
              />
              <Tooltip 
                content={<CustomTooltip formatter={(value: number, name: string) => {
                  if (name === 'successRate') return `${value.toFixed(1)}%`;
                  return value.toLocaleString();
                }} />}
              />
              <Bar 
                dataKey="requests" 
                fill={chartColors.primary}
                radius={[0, 4, 4, 0]}
                name="请求次数"
              />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </motion.div>
  );
}

// System health chart
export function SystemHealthChart({ data, loading }: { data?: SystemHealth; loading?: boolean }) {
  if (loading || !data) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-4 w-48" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-64 w-full" />
        </CardContent>
      </Card>
    );
  }

  const healthData = [
    { name: 'API 服务', status: data.apiStatus, value: 1 },
    { name: 'AI 规划师', status: data.aiPlannerStatus, value: 1 },
    { name: '代码生成器', status: data.codeGeneratorStatus, value: 1 },
    { name: '数据库', status: data.databaseStatus, value: 1 },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return chartColors.success;
      case 'degraded': return chartColors.warning;
      case 'down': return chartColors.error;
      default: return chartColors.muted;
    }
  };

  const getStatusBadge = (status: string) => {
    const variant = status === 'healthy' ? 'default' : status === 'degraded' ? 'secondary' : 'destructive';
    const emoji = status === 'healthy' ? '🟢' : status === 'degraded' ? '🟡' : '🔴';
    
    return (
      <Badge variant={variant} className="text-xs">
        {emoji} {status === 'healthy' ? '正常' : status === 'degraded' ? '降级' : '故障'}
      </Badge>
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.3 }}
    >
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            ⚡ 系统健康状态
          </CardTitle>
          <CardDescription>
            各服务组件运行状态监控
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              {healthData.map((item, index) => (
                <div key={index} className="flex items-center justify-between p-3 rounded-lg border">
                  <span className="text-sm font-medium">{item.name}</span>
                  {getStatusBadge(item.status)}
                </div>
              ))}
            </div>
            
            <div className="pt-4 border-t">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">系统正常运行时间</span>
                <span className="font-medium">{data.uptime.toFixed(2)}%</span>
              </div>
              <div className="flex items-center justify-between text-sm mt-2">
                <span className="text-muted-foreground">最后检查时间</span>
                <span className="font-medium">
                  {new Date(data.lastCheck).toLocaleTimeString('zh-CN')}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

// Main dashboard charts component
export function DashboardCharts({ 
  usageHistory, 
  topAgents, 
  systemHealth, 
  loading, 
  className 
}: DashboardChartsProps) {
  return (
    <div className={cn('grid grid-cols-1 lg:grid-cols-2 gap-6', className)}>
      <div className="lg:col-span-2">
        <UsageTrendChart data={usageHistory} loading={loading} />
      </div>
      <TopAgentsChart data={topAgents} loading={loading} />
      <SystemHealthChart data={systemHealth} loading={loading} />
    </div>
  );
}

export default DashboardCharts;
