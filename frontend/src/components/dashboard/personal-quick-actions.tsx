"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { motion } from 'framer-motion';
import { useQuickActions } from '@/lib/dashboard-data';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Plus,
  Play,
  LayoutTemplate,
  History,
  ArrowRight,
  Zap
} from 'lucide-react';
import Link from 'next/link';
import { cn } from '@/lib/utils';

interface PersonalQuickActionsProps {
  className?: string;
}

const iconMap = {
  plus: Plus,
  play: Play,
  template: LayoutTemplate,
  history: History,
  zap: Zap,
};

const colorMap = {
  primary: 'bg-blue-500/10 text-blue-600 border-blue-200 hover:bg-blue-500/20',
  secondary: 'bg-gray-500/10 text-gray-600 border-gray-200 hover:bg-gray-500/20',
  success: 'bg-green-500/10 text-green-600 border-green-200 hover:bg-green-500/20',
  warning: 'bg-yellow-500/10 text-yellow-600 border-yellow-200 hover:bg-yellow-500/20',
  error: 'bg-red-500/10 text-red-600 border-red-200 hover:bg-red-500/20',
};

export function PersonalQuickActions({ className }: PersonalQuickActionsProps) {
  const { data: quickActions, isLoading, error } = useQuickActions();

  if (error) {
    return (
      <Card className={cn('border-red-200 bg-red-50/50', className)}>
        <CardHeader className="p-3">
          <CardTitle className="text-sm text-red-600">快速操作</CardTitle>
          <CardDescription className="text-xs text-red-500">
            加载失败，请稍后重试
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card className={cn('', className)}>
      <CardHeader className="p-3 pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium">快速操作</CardTitle>
          <Zap className="h-4 w-4 text-muted-foreground" />
        </div>
        <CardDescription className="text-xs">
          常用功能快速访问
        </CardDescription>
      </CardHeader>
      <CardContent className="p-3 pt-0">
        {isLoading ? (
          <div className="grid grid-cols-2 gap-2 md:gap-3">
            {Array.from({ length: 4 }).map((_, i) => (
              <Skeleton key={i} className="h-16 md:h-18 rounded-lg" />
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-2 md:gap-3">
            {quickActions?.map((action, index) => {
              const IconComponent = iconMap[action.icon as keyof typeof iconMap] || Plus;
              
              return (
                <motion.div
                  key={action.id}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.2, delay: index * 0.05 }}
                >
                  <Link href={action.href}>
                    <Button
                      variant="outline"
                      className={cn(
                        'h-auto min-h-[44px] p-3 flex flex-col items-center gap-1 text-center relative overflow-hidden transition-all duration-200 touch-manipulation',
                        'hover:scale-105 active:scale-95 focus:ring-2 focus:ring-offset-2',
                        colorMap[action.color],
                        action.disabled && 'opacity-50 cursor-not-allowed'
                      )}
                      disabled={action.disabled}
                    >
                      <IconComponent className="h-4 w-4" />
                      <span className="text-xs font-medium leading-tight">
                        {action.title}
                      </span>
                      {action.badge && (
                        <Badge 
                          variant="secondary" 
                          className="absolute -top-1 -right-1 h-4 px-1 text-[10px]"
                        >
                          {action.badge}
                        </Badge>
                      )}
                    </Button>
                  </Link>
                </motion.div>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

PersonalQuickActions.displayName = 'PersonalQuickActions';
