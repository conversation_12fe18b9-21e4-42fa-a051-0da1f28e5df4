"use client";

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { CompactFavoriteButton } from '@/components/ui/favorite-button';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { useFavorites } from '@/hooks/use-favorites';
import {
  cardStyles,
  typography,
  spacing,
  iconStyles,
  statusStyles,
  combineStyles,
  themeAwareStyles
} from '@/lib/dashboard-styles';
import {
  Zap,
  Play,
  Edit,
  MoreHorizontal,
  Clock,
  Target,
  Activity,
  Star,
  StarOff,
  ExternalLink
} from 'lucide-react';
import Link from 'next/link';

interface AgentData {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'inactive' | 'error';
  lastUsed: string;
  totalRuns: number;
  successRate: number;
  avgResponseTime: number;
  isFavorite: boolean;
  category: string;
  performance: {
    trend: 'up' | 'down' | 'stable';
    score: number;
  };
}

interface ActiveAgentsSectionProps {
  className?: string;
  agents?: AgentData[];
  loading?: boolean;
  onRunAgent?: (agentId: string) => void;
  onEditAgent?: (agentId: string) => void;
  onToggleFavorite?: (agentId: string) => void;
}

const mockAgents: AgentData[] = [
  {
    id: 'agent-1',
    name: '数据分析助手',
    description: '专业的数据分析和可视化工具，支持多种数据格式',
    status: 'active',
    lastUsed: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
    totalRuns: 156,
    successRate: 94.2,
    avgResponseTime: 2340,
    isFavorite: true,
    category: '数据处理',
    performance: { trend: 'up', score: 9.2 }
  },
  {
    id: 'agent-2',
    name: '文档处理助手',
    description: '智能文档处理和格式转换，支持PDF、Word等格式',
    status: 'active',
    lastUsed: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
    totalRuns: 89,
    successRate: 91.0,
    avgResponseTime: 1890,
    isFavorite: false,
    category: '文档处理',
    performance: { trend: 'stable', score: 8.7 }
  },
  {
    id: 'agent-3',
    name: '代码生成助手',
    description: '基于需求自动生成高质量代码，支持多种编程语言',
    status: 'error',
    lastUsed: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
    totalRuns: 234,
    successRate: 87.5,
    avgResponseTime: 3200,
    isFavorite: true,
    category: '开发工具',
    performance: { trend: 'down', score: 7.8 }
  },
  {
    id: 'agent-4',
    name: '客服助手',
    description: '智能客服机器人，提供24/7客户支持服务',
    status: 'active',
    lastUsed: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
    totalRuns: 445,
    successRate: 96.8,
    avgResponseTime: 1200,
    isFavorite: false,
    category: '客户服务',
    performance: { trend: 'up', score: 9.5 }
  }
];

const getStatusConfig = (status: string) => {
  switch (status) {
    case 'active':
      return {
        color: 'text-green-600 dark:text-green-400',
        bg: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
        label: '运行中'
      };
    case 'inactive':
      return {
        color: 'text-gray-600 dark:text-gray-400',
        bg: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
        label: '未激活'
      };
    case 'error':
      return {
        color: 'text-red-600 dark:text-red-400',
        bg: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
        label: '错误'
      };
    default:
      return {
        color: 'text-gray-600 dark:text-gray-400',
        bg: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
        label: '未知'
      };
  }
};

const formatTimeAgo = (timestamp: string) => {
  const now = new Date();
  const time = new Date(timestamp);
  const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));
  
  if (diffInMinutes < 1) return '刚刚';
  if (diffInMinutes < 60) return `${diffInMinutes}分钟前`;
  if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}小时前`;
  return `${Math.floor(diffInMinutes / 1440)}天前`;
};

const AgentCard = ({
  agent,
  onRun,
  onEdit,
  onToggleFavorite
}: {
  agent: AgentData;
  onRun?: (id: string) => void;
  onEdit?: (id: string) => void;
  onToggleFavorite?: (agentId: string, isFavorite?: boolean) => void;
}) => {
  const statusConfig = getStatusConfig(agent.status);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="group"
    >
      <Card className={cn(
        cardStyles.interactive,
        'hover:scale-[1.02] transition-all duration-300'
      )}>
        <CardContent className={spacing.cardPaddingCompact}>
          <div className="space-y-3">
            {/* Header */}
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h3 className={cn(typography.body, 'font-medium text-foreground line-clamp-1')}>
                    {agent.name}
                  </h3>
                  {agent.isFavorite && (
                    <Star className={cn(iconStyles.sm, 'text-yellow-500 fill-current')} />
                  )}
                </div>
                <p className={cn(typography.caption, 'text-muted-foreground line-clamp-2 mb-2')}>
                  {agent.description}
                </p>
                <div className="flex items-center gap-2">
                  <Badge className={statusConfig.bg}>
                    {statusConfig.label}
                  </Badge>
                  <span className={cn(typography.caption, 'text-muted-foreground')}>
                    {agent.category}
                  </span>
                </div>
              </div>
              
              <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                <CompactFavoriteButton
                  agentId={agent.agent_id || agent.id}
                  isFavorite={agent.isFavorite}
                  onToggle={onToggleFavorite}
                />
                {onEdit && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0"
                    onClick={() => onEdit(agent.agent_id || agent.id)}
                  >
                    <Edit className="h-3 w-3" />
                  </Button>
                )}
                {onRun && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0"
                    onClick={() => onRun(agent.agent_id || agent.id)}
                  >
                    <Play className="h-3 w-3" />
                  </Button>
                )}
              </div>
            </div>

            {/* Metrics */}
            <div className="grid grid-cols-3 gap-2 text-center">
              <div>
                <div className={cn(typography.caption, 'font-medium text-foreground')}>
                  {agent.totalRuns}
                </div>
                <div className={cn(typography.caption, 'text-muted-foreground text-xs')}>
                  执行次数
                </div>
              </div>
              <div>
                <div className={cn(typography.caption, 'font-medium text-green-600 dark:text-green-400')}>
                  {agent.successRate}%
                </div>
                <div className={cn(typography.caption, 'text-muted-foreground text-xs')}>
                  成功率
                </div>
              </div>
              <div>
                <div className={cn(typography.caption, 'font-medium text-blue-600 dark:text-blue-400')}>
                  {(agent.avgResponseTime / 1000).toFixed(1)}s
                </div>
                <div className={cn(typography.caption, 'text-muted-foreground text-xs')}>
                  响应时间
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="flex items-center justify-between text-xs text-muted-foreground pt-2 border-t border-border/60">
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                <span>最后使用: {formatTimeAgo(agent.lastUsed)}</span>
              </div>
              <div className="flex items-center gap-1">
                <span>评分: {agent.performance.score}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export function ActiveAgentsSection({
  className,
  agents = [],
  loading = false,
  onRunAgent,
  onEditAgent,
  onToggleFavorite
}: ActiveAgentsSectionProps) {
  const [filter, setFilter] = useState<'all' | 'favorites' | 'active'>('all');

  // Use favorites hook for real-time favorite status
  const { favorites, isFavorite, toggleFavorite } = useFavorites();

  // Use real agents if available, otherwise fall back to mock agents
  const displayAgents = agents.length > 0 ? agents : mockAgents;

  // Merge agent data with favorite status
  const agentsWithFavorites = displayAgents.map(agent => ({
    ...agent,
    isFavorite: isFavorite(agent.agent_id || agent.id)
  }));

  const filteredAgents = agentsWithFavorites.filter(agent => {
    switch (filter) {
      case 'favorites': return agent.isFavorite;
      case 'active': return agent.status === 'active';
      default: return true;
    }
  });

  // Handle favorite toggle with both local and prop callbacks
  const handleToggleFavorite = async (agentId: string, currentIsFavorite?: boolean) => {
    try {
      await toggleFavorite(agentId);
      onToggleFavorite?.(agentId);
    } catch (error) {
      console.error('Failed to toggle favorite:', error);
    }
  };

  if (loading) {
    return (
      <Card className={cn(cardStyles.base, className)}>
        <CardHeader className={spacing.cardHeader}>
          <Skeleton className="h-6 w-32" />
        </CardHeader>
        <CardContent className={spacing.cardContent}>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
            {Array.from({ length: 6 }).map((_, i) => (
              <Card key={i} className={cardStyles.base}>
                <CardContent className={spacing.cardPaddingCompact}>
                  <div className="space-y-3">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-3 w-full" />
                    <Skeleton className="h-3 w-2/3" />
                    <div className="grid grid-cols-3 gap-2">
                      <Skeleton className="h-8" />
                      <Skeleton className="h-8" />
                      <Skeleton className="h-8" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className={cn(cardStyles.elevated, className)}>
        <CardHeader className={spacing.cardHeader}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Zap className={cn(iconStyles.md, 'text-primary')} />
              <CardTitle className={typography.h3}>活跃Agent</CardTitle>
            </div>
            <Button variant="ghost" size="sm" asChild>
              <Link href="/agents">
                <ExternalLink className="h-4 w-4 mr-2" />
                管理全部
              </Link>
            </Button>
          </div>
          
          <Tabs value={filter} onValueChange={(value) => setFilter(value as any)} className="mt-4">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="all">全部 ({agentsWithFavorites.length})</TabsTrigger>
              <TabsTrigger value="favorites">收藏 ({agentsWithFavorites.filter(a => a.isFavorite).length})</TabsTrigger>
              <TabsTrigger value="active">运行中 ({agentsWithFavorites.filter(a => a.status === 'active').length})</TabsTrigger>
            </TabsList>
          </Tabs>
        </CardHeader>
        <CardContent className={spacing.cardContent}>
          {filteredAgents.length === 0 ? (
            <div className="text-center py-8">
              <div className={cn(typography.body, 'text-muted-foreground mb-4')}>
                {filter === 'all' ? '还没有创建任何Agent' : `没有找到${filter === 'favorites' ? '收藏的' : '运行中的'}Agent`}
              </div>
              <Button variant="outline" asChild>
                <Link href="/create">创建第一个Agent</Link>
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
              {filteredAgents.map((agent) => (
                <AgentCard
                  key={agent.agent_id || agent.id}
                  agent={agent}
                  onRun={onRunAgent}
                  onEdit={onEditAgent}
                  onToggleFavorite={handleToggleFavorite}
                />
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
