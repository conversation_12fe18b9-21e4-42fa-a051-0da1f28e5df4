"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import {
  cardStyles,
  typography,
  spacing,
  iconStyles,
  animations,
  statusStyles,
  gradients,
  combineStyles,
  themeAwareStyles,
  accessibilityStyles
} from '@/lib/dashboard-styles';
import { CardLoading } from '@/components/ui/enhanced-loading';
import { 
  TrendingUp, 
  TrendingDown, 
  Minus,
  Activity,
  Users,
  Zap,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle
} from 'lucide-react';

export interface StatCardData {
  title: string;
  value: number | string;
  previousValue?: number;
  change?: number;
  changeType?: 'increase' | 'decrease' | 'neutral';
  icon?: React.ReactNode;
  description?: string;
  trend?: 'up' | 'down' | 'neutral';
  status?: 'success' | 'warning' | 'error' | 'neutral';
  format?: 'number' | 'percentage' | 'currency' | 'time';
  suffix?: string;
  loading?: boolean;
}

interface EnhancedStatsCardProps {
  data: StatCardData;
  className?: string;
  showTrend?: boolean;
  showChange?: boolean;
  animated?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

const iconMap = {
  activity: Activity,
  users: Users,
  zap: Zap,
  clock: Clock,
  check: CheckCircle,
  alert: AlertCircle,
  error: XCircle,
};

const formatValue = (value: number | string, format?: string, suffix?: string): string => {
  if (typeof value === 'string') return value;
  
  switch (format) {
    case 'percentage':
      return `${value.toFixed(1)}%`;
    case 'currency':
      return `$${value.toLocaleString()}`;
    case 'time':
      return `${value}ms`;
    case 'number':
    default:
      const formatted = value.toLocaleString();
      return suffix ? `${formatted}${suffix}` : formatted;
  }
};

const getTrendIcon = (trend?: 'up' | 'down' | 'neutral') => {
  switch (trend) {
    case 'up':
      return <TrendingUp className="h-4 w-4 text-green-500" />;
    case 'down':
      return <TrendingDown className="h-4 w-4 text-red-500" />;
    case 'neutral':
    default:
      return <Minus className="h-4 w-4 text-muted-foreground" />;
  }
};

const getChangeColor = (changeType?: 'increase' | 'decrease' | 'neutral') => {
  switch (changeType) {
    case 'increase':
      return 'text-green-600 dark:text-green-400';
    case 'decrease':
      return 'text-red-600 dark:text-red-400';
    case 'neutral':
    default:
      return 'text-muted-foreground';
  }
};

const getStatusColor = (status?: 'success' | 'warning' | 'error' | 'neutral') => {
  switch (status) {
    case 'success':
      return 'border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-950/20';
    case 'warning':
      return 'border-yellow-200 dark:border-yellow-800 bg-yellow-50 dark:bg-yellow-950/20';
    case 'error':
      return 'border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950/20';
    case 'neutral':
    default:
      return '';
  }
};

const getSizeClasses = (size?: 'sm' | 'md' | 'lg') => {
  switch (size) {
    case 'sm':
      return {
        card: 'p-3',
        title: 'text-xs',
        value: 'text-lg',
        icon: 'h-4 w-4',
        change: 'text-xs',
      };
    case 'lg':
      return {
        card: 'p-6',
        title: 'text-base',
        value: 'text-3xl',
        icon: 'h-8 w-8',
        change: 'text-sm',
      };
    case 'md':
    default:
      return {
        card: 'p-4',
        title: 'text-sm',
        value: 'text-2xl',
        icon: 'h-6 w-6',
        change: 'text-xs',
      };
  }
};

export function EnhancedStatsCard({
  data,
  className,
  showTrend = true,
  showChange = true,
  animated = true,
  size = 'md',
}: EnhancedStatsCardProps) {
  const sizeClasses = getSizeClasses(size);
  
  if (data.loading) {
    return (
      <CardLoading className={combineStyles(
        'min-h-[120px]',
        gradients.neutral,
        className
      )} />
    );
  }

  const cardContent = (
    <Card className={combineStyles(
      cardStyles.elevated,
      'relative overflow-hidden group',
      animations.hoverLift,
      data.status ? gradients[data.status] : gradients.card,
      className
    )}>
      <CardHeader className={combineStyles(
        spacing.cardHeader,
        'flex flex-row items-center justify-between space-y-0'
      )}>
        <CardTitle className={combineStyles(
          typography.caption,
          'font-medium tracking-wide uppercase',
          sizeClasses.title
        )}>
          {data.title}
        </CardTitle>
        <div className={combineStyles(spacing.inlineGap, 'flex items-center')}>
          {showTrend && (
            <div className={animations.wiggle}>
              {getTrendIcon(data.trend)}
            </div>
          )}
          {data.icon && (
            <div className={combineStyles(
              iconStyles.interactive,
              iconStyles.muted,
              sizeClasses.icon,
              animations.button
            )}>
              {data.icon}
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent className={combineStyles(spacing.cardContent, 'space-y-2')}>
        <div className={combineStyles(
          typography.h3,
          'font-bold tracking-tight',
          sizeClasses.value
        )}>
          {formatValue(data.value, data.format, data.suffix)}
        </div>
        
        {data.description && (
          <p className="text-xs text-muted-foreground leading-relaxed">
            {data.description}
          </p>
        )}
        
        {showChange && data.change !== undefined && (
          <div className={cn('flex items-center gap-1', sizeClasses.change)}>
            <span className={getChangeColor(data.changeType)}>
              {data.changeType === 'increase' && '+'}
              {data.change}
              {data.format === 'percentage' ? 'pp' : '%'}
            </span>
            <span className="text-muted-foreground">vs last period</span>
          </div>
        )}
        
        {data.status && data.status !== 'neutral' && (
          <Badge 
            variant={data.status === 'success' ? 'default' : 'destructive'}
            className="text-xs"
          >
            {data.status === 'success' && <CheckCircle className="h-3 w-3 mr-1" />}
            {data.status === 'warning' && <AlertCircle className="h-3 w-3 mr-1" />}
            {data.status === 'error' && <XCircle className="h-3 w-3 mr-1" />}
            {data.status.charAt(0).toUpperCase() + data.status.slice(1)}
          </Badge>
        )}
      </CardContent>
      
      {/* Animated background gradient */}
      {animated && (
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full animate-shimmer" />
      )}
    </Card>
  );

  if (animated) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        {cardContent}
      </motion.div>
    );
  }

  return cardContent;
}

// Predefined stat card configurations
export const createAgentStatsCard = (value: number, previousValue?: number): StatCardData => ({
  title: '活跃 Agents',
  value,
  previousValue,
  change: previousValue ? ((value - previousValue) / previousValue) * 100 : undefined,
  changeType: previousValue ? (value > previousValue ? 'increase' : value < previousValue ? 'decrease' : 'neutral') : undefined,
  icon: <Users className="h-4 w-4" />,
  description: '当前运行中的 Agent 数量',
  trend: previousValue ? (value > previousValue ? 'up' : value < previousValue ? 'down' : 'neutral') : 'neutral',
  status: value > 0 ? 'success' : 'warning',
  format: 'number',
});

export const createRequestsStatsCard = (value: number, previousValue?: number): StatCardData => ({
  title: '今日请求',
  value,
  previousValue,
  change: previousValue ? ((value - previousValue) / previousValue) * 100 : undefined,
  changeType: previousValue ? (value > previousValue ? 'increase' : value < previousValue ? 'decrease' : 'neutral') : undefined,
  icon: <Activity className="h-4 w-4" />,
  description: '今日 API 调用次数',
  trend: previousValue ? (value > previousValue ? 'up' : value < previousValue ? 'down' : 'neutral') : 'neutral',
  status: 'neutral',
  format: 'number',
});

export const createSuccessRateStatsCard = (value: number, previousValue?: number): StatCardData => ({
  title: '成功率',
  value,
  previousValue,
  change: previousValue ? value - previousValue : undefined,
  changeType: previousValue ? (value > previousValue ? 'increase' : value < previousValue ? 'decrease' : 'neutral') : undefined,
  icon: <CheckCircle className="h-4 w-4" />,
  description: 'API 调用成功率',
  trend: previousValue ? (value > previousValue ? 'up' : value < previousValue ? 'down' : 'neutral') : 'neutral',
  status: value >= 95 ? 'success' : value >= 90 ? 'warning' : 'error',
  format: 'percentage',
});

export const createResponseTimeStatsCard = (value: number, previousValue?: number): StatCardData => ({
  title: '平均响应时间',
  value,
  previousValue,
  change: previousValue ? ((value - previousValue) / previousValue) * 100 : undefined,
  changeType: previousValue ? (value < previousValue ? 'increase' : value > previousValue ? 'decrease' : 'neutral') : undefined,
  icon: <Clock className="h-4 w-4" />,
  description: '平均 API 响应时间',
  trend: previousValue ? (value < previousValue ? 'up' : value > previousValue ? 'down' : 'neutral') : 'neutral',
  status: value <= 1000 ? 'success' : value <= 2000 ? 'warning' : 'error',
  format: 'time',
});

export default EnhancedStatsCard;
