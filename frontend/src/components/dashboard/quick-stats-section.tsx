"use client";

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import {
  cardStyles,
  typography,
  spacing,
  iconStyles,
  gradients,
  combineStyles,
  themeAwareStyles
} from '@/lib/dashboard-styles';
import {
  Users,
  Zap,
  CheckCircle,
  Activity,
  TrendingUp,
  TrendingDown,
  Minus
} from 'lucide-react';

interface QuickStatsData {
  totalAgents: number;
  activeAgents: number;
  totalExecutions: number;
  successfulExecutions: number;
  successRate: number;
  recentActivity: number;
  trends: {
    agents: 'up' | 'down' | 'stable';
    executions: 'up' | 'down' | 'stable';
    successRate: 'up' | 'down' | 'stable';
    activity: 'up' | 'down' | 'stable';
  };
}

interface QuickStatsSectionProps {
  className?: string;
  data?: QuickStatsData;
  loading?: boolean;
}

const mockStatsData: QuickStatsData = {
  totalAgents: 12,
  activeAgents: 8,
  totalExecutions: 1247,
  successfulExecutions: 1156,
  successRate: 92.7,
  recentActivity: 23,
  trends: {
    agents: 'up',
    executions: 'up',
    successRate: 'stable',
    activity: 'up'
  }
};

const TrendIcon = ({ trend }: { trend: 'up' | 'down' | 'stable' }) => {
  switch (trend) {
    case 'up':
      return <TrendingUp className={cn(iconStyles.sm, 'text-green-600 dark:text-green-400')} />;
    case 'down':
      return <TrendingDown className={cn(iconStyles.sm, 'text-red-600 dark:text-red-400')} />;
    case 'stable':
      return <Minus className={cn(iconStyles.sm, 'text-gray-600 dark:text-gray-400')} />;
  }
};

const StatCard = ({
  title,
  value,
  subtitle,
  icon: Icon,
  trend,
  gradient,
  delay = 0
}: {
  title: string;
  value: string | number;
  subtitle: string;
  icon: React.ComponentType<any>;
  trend: 'up' | 'down' | 'stable';
  gradient: string;
  delay?: number;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
    >
      <Card className={cn(
        cardStyles.interactive,
        'relative overflow-hidden group hover:scale-[1.02] transition-all duration-300'
      )}>
        <div className={cn('absolute inset-0 opacity-50', gradient)} />
        <CardContent className={cn(spacing.cardPaddingCompact, 'relative')}>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <Icon className={cn(iconStyles.md, 'text-primary')} />
                <span className={cn(typography.caption, 'text-muted-foreground font-medium')}>
                  {title}
                </span>
              </div>
              <div className={cn(typography.h2, 'text-foreground mb-1')}>
                {value}
              </div>
              <div className={cn(typography.caption, 'text-muted-foreground')}>
                {subtitle}
              </div>
            </div>
            <div className="flex items-center gap-1">
              <TrendIcon trend={trend} />
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export function QuickStatsSection({ 
  className, 
  data = mockStatsData, 
  loading = false 
}: QuickStatsSectionProps) {
  if (loading) {
    return (
      <div className={cn('grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4', className)}>
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i} className={cardStyles.base}>
            <CardContent className={spacing.cardPaddingCompact}>
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Skeleton className="h-5 w-5" />
                  <Skeleton className="h-4 w-16" />
                </div>
                <Skeleton className="h-8 w-12" />
                <Skeleton className="h-3 w-20" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className={cn('grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4', className)}>
      <StatCard
        title="总Agent数"
        value={data.totalAgents}
        subtitle={`${data.activeAgents} 个活跃`}
        icon={Zap}
        trend={data.trends.agents}
        gradient={gradients.primary}
        delay={0}
      />
      <StatCard
        title="总执行次数"
        value={data.totalExecutions.toLocaleString()}
        subtitle="累计执行"
        icon={Activity}
        trend={data.trends.executions}
        gradient={gradients.info}
        delay={0.1}
      />
      <StatCard
        title="成功率"
        value={`${data.successRate}%`}
        subtitle={`${data.successfulExecutions} 次成功`}
        icon={CheckCircle}
        trend={data.trends.successRate}
        gradient={gradients.success}
        delay={0.2}
      />
      <StatCard
        title="近期活动"
        value={data.recentActivity}
        subtitle="过去24小时"
        icon={Users}
        trend={data.trends.activity}
        gradient={gradients.warning}
        delay={0.3}
      />
    </div>
  );
}
