"use client";

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import {
  cardStyles,
  typography,
  spacing,
  iconStyles,
  statusStyles,
  combineStyles,
  themeAwareStyles
} from '@/lib/dashboard-styles';
import {
  Activity,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Play,
  Eye,
  ExternalLink,
  MoreHorizontal
} from 'lucide-react';
import Link from 'next/link';

interface ActivityItem {
  id: string;
  type: 'test_execution' | 'agent_created' | 'agent_updated' | 'template_used';
  title: string;
  description: string;
  timestamp: string;
  status: 'success' | 'error' | 'warning' | 'info';
  agentId?: string;
  agentName?: string;
  duration?: number;
  metadata?: Record<string, any>;
}

interface RecentActivitySectionProps {
  className?: string;
  activities?: ActivityItem[];
  loading?: boolean;
  onViewDetails?: (activityId: string) => void;
  onRunAgent?: (agentId: string) => void;
}

const mockActivities: ActivityItem[] = [
  {
    id: '1',
    type: 'test_execution',
    title: 'Agent测试执行',
    description: '数据分析助手完成了客户数据处理任务',
    timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
    status: 'success',
    agentId: 'agent-1',
    agentName: '数据分析助手',
    duration: 2340,
    metadata: { testId: 'test-123' }
  },
  {
    id: '2',
    type: 'agent_created',
    title: '新Agent创建',
    description: '创建了新的文档处理助手',
    timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
    status: 'info',
    agentId: 'agent-2',
    agentName: '文档处理助手'
  },
  {
    id: '3',
    type: 'test_execution',
    title: 'Agent测试失败',
    description: '代码生成助手执行时遇到API限制错误',
    timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
    status: 'error',
    agentId: 'agent-3',
    agentName: '代码生成助手',
    duration: 1200,
    metadata: { error: 'API_LIMIT_EXCEEDED' }
  },
  {
    id: '4',
    type: 'template_used',
    title: '模板使用',
    description: '使用客服助手模板创建了新Agent',
    timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
    status: 'success',
    agentId: 'agent-4',
    agentName: '客服助手'
  },
  {
    id: '5',
    type: 'agent_updated',
    title: 'Agent配置更新',
    description: '更新了营销助手的AI模型配置',
    timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
    status: 'info',
    agentId: 'agent-5',
    agentName: '营销助手'
  }
];

const getStatusConfig = (status: string) => {
  switch (status) {
    case 'success':
      return {
        icon: CheckCircle,
        color: 'text-green-600 dark:text-green-400',
        bg: 'bg-green-50 dark:bg-green-950/20',
        badge: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      };
    case 'error':
      return {
        icon: XCircle,
        color: 'text-red-600 dark:text-red-400',
        bg: 'bg-red-50 dark:bg-red-950/20',
        badge: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      };
    case 'warning':
      return {
        icon: AlertTriangle,
        color: 'text-yellow-600 dark:text-yellow-400',
        bg: 'bg-yellow-50 dark:bg-yellow-950/20',
        badge: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      };
    default:
      return {
        icon: Activity,
        color: 'text-blue-600 dark:text-blue-400',
        bg: 'bg-blue-50 dark:bg-blue-950/20',
        badge: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      };
  }
};

const formatTimeAgo = (timestamp: string) => {
  const now = new Date();
  const time = new Date(timestamp);
  const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));
  
  if (diffInMinutes < 1) return '刚刚';
  if (diffInMinutes < 60) return `${diffInMinutes}分钟前`;
  if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}小时前`;
  return `${Math.floor(diffInMinutes / 1440)}天前`;
};

const ActivityItemCard = ({ 
  activity, 
  onViewDetails, 
  onRunAgent 
}: { 
  activity: ActivityItem;
  onViewDetails?: (id: string) => void;
  onRunAgent?: (agentId: string) => void;
}) => {
  const statusConfig = getStatusConfig(activity.status);
  const StatusIcon = statusConfig.icon;

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.3 }}
      className="group"
    >
      <div className={cn(
        'flex items-start gap-3 p-3 rounded-lg border border-border/60 hover:border-border/80',
        'bg-card/50 hover:bg-card/80 transition-all duration-200',
        'hover:shadow-sm'
      )}>
        <div className={cn(
          'flex-shrink-0 p-2 rounded-lg',
          statusConfig.bg
        )}>
          <StatusIcon className={cn(iconStyles.sm, statusConfig.color)} />
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between gap-2">
            <div className="flex-1 min-w-0">
              <h4 className={cn(typography.body, 'font-medium text-foreground mb-1')}>
                {activity.title}
              </h4>
              <p className={cn(typography.caption, 'text-muted-foreground line-clamp-2 mb-2')}>
                {activity.description}
              </p>
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <Clock className="h-3 w-3" />
                <span>{formatTimeAgo(activity.timestamp)}</span>
                {activity.duration && (
                  <>
                    <span>•</span>
                    <span>{(activity.duration / 1000).toFixed(1)}s</span>
                  </>
                )}
                {activity.agentName && (
                  <>
                    <span>•</span>
                    <span>{activity.agentName}</span>
                  </>
                )}
              </div>
            </div>
            
            <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
              {onViewDetails && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => onViewDetails(activity.id)}
                >
                  <Eye className="h-3 w-3" />
                </Button>
              )}
              {activity.agentId && onRunAgent && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => onRunAgent(activity.agentId!)}
                >
                  <Play className="h-3 w-3" />
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export function RecentActivitySection({ 
  className, 
  activities = mockActivities, 
  loading = false,
  onViewDetails,
  onRunAgent
}: RecentActivitySectionProps) {
  if (loading) {
    return (
      <Card className={cn(cardStyles.base, className)}>
        <CardHeader className={spacing.cardHeader}>
          <Skeleton className="h-6 w-32" />
        </CardHeader>
        <CardContent className={spacing.cardContent}>
          <div className="space-y-3">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-start gap-3">
                <Skeleton className="h-10 w-10 rounded-lg" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-full" />
                  <Skeleton className="h-3 w-1/2" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className={cn(cardStyles.elevated, className)}>
        <CardHeader className={spacing.cardHeader}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Activity className={cn(iconStyles.md, 'text-primary')} />
              <CardTitle className={typography.h3}>最近活动</CardTitle>
            </div>
            <Button variant="ghost" size="sm" asChild>
              <Link href="/history">
                <ExternalLink className="h-4 w-4 mr-2" />
                查看全部
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent className={spacing.cardContent}>
          <div className="space-y-3">
            {activities.slice(0, 5).map((activity) => (
              <ActivityItemCard
                key={activity.id}
                activity={activity}
                onViewDetails={onViewDetails}
                onRunAgent={onRunAgent}
              />
            ))}
            {activities.length === 0 && (
              <div className="text-center py-8">
                <div className={cn(typography.body, 'text-muted-foreground mb-4')}>
                  暂无最近活动
                </div>
                <Button variant="outline" asChild>
                  <Link href="/create">创建第一个Agent</Link>
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
