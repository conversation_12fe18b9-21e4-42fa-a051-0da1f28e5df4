"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { 
  Plus, 
  Play, 
  Settings, 
  AlertTriangle,
  User,
  Bot,
  Activity,
  ExternalLink,
  MoreHorizontal
} from 'lucide-react';
import { ActivityItem } from '@/lib/dashboard-data';

interface RecentActivityProps {
  activities?: ActivityItem[];
  loading?: boolean;
  className?: string;
  maxItems?: number;
  showViewAll?: boolean;
  onViewAll?: () => void;
  onActivityClick?: (activity: ActivityItem) => void;
}

const getActivityIcon = (type: ActivityItem['type']) => {
  switch (type) {
    case 'agent_created':
      return <Plus className="h-4 w-4 text-green-600" />;
    case 'agent_executed':
      return <Play className="h-4 w-4 text-blue-600" />;
    case 'agent_updated':
      return <Settings className="h-4 w-4 text-orange-600" />;
    case 'system_event':
      return <AlertTriangle className="h-4 w-4 text-purple-600" />;
    default:
      return <Activity className="h-4 w-4 text-gray-600" />;
  }
};

const getActivityColor = (type: ActivityItem['type']) => {
  switch (type) {
    case 'agent_created':
      return 'bg-green-50 dark:bg-green-950/20 border-green-200 dark:border-green-800';
    case 'agent_executed':
      return 'bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800';
    case 'agent_updated':
      return 'bg-orange-50 dark:bg-orange-950/20 border-orange-200 dark:border-orange-800';
    case 'system_event':
      return 'bg-purple-50 dark:bg-purple-950/20 border-purple-200 dark:border-purple-800';
    default:
      return 'bg-gray-50 dark:bg-gray-950/20 border-gray-200 dark:border-gray-800';
  }
};

const getActivityBadge = (type: ActivityItem['type']) => {
  switch (type) {
    case 'agent_created':
      return <Badge variant="secondary" className="text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">创建</Badge>;
    case 'agent_executed':
      return <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">执行</Badge>;
    case 'agent_updated':
      return <Badge variant="secondary" className="text-xs bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">更新</Badge>;
    case 'system_event':
      return <Badge variant="secondary" className="text-xs bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">系统</Badge>;
    default:
      return <Badge variant="secondary" className="text-xs">未知</Badge>;
  }
};

function ActivityItemComponent({ 
  activity, 
  onClick,
  index 
}: { 
  activity: ActivityItem; 
  onClick?: (activity: ActivityItem) => void;
  index: number;
}) {
  const timeAgo = formatDistanceToNow(new Date(activity.timestamp), { 
    addSuffix: true, 
    locale: zhCN 
  });

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.3, delay: index * 0.05 }}
      className={cn(
        'flex items-start space-x-3 p-3 rounded-lg border transition-all duration-200 hover:shadow-sm cursor-pointer',
        getActivityColor(activity.type)
      )}
      onClick={() => onClick?.(activity)}
    >
      <div className="flex-shrink-0 mt-0.5">
        <div className="flex items-center justify-center w-8 h-8 rounded-full bg-background border">
          {getActivityIcon(activity.type)}
        </div>
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between mb-1">
          <div className="flex items-center gap-2">
            <h4 className="text-sm font-medium text-foreground truncate">
              {activity.title}
            </h4>
            {getActivityBadge(activity.type)}
          </div>
          <time className="text-xs text-muted-foreground flex-shrink-0">
            {timeAgo}
          </time>
        </div>
        
        <p className="text-sm text-muted-foreground line-clamp-2 leading-relaxed">
          {activity.description}
        </p>
        
        {activity.metadata && (
          <div className="flex items-center gap-2 mt-2">
            {activity.agentId && (
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <Bot className="h-3 w-3" />
                <span>Agent ID: {activity.agentId}</span>
              </div>
            )}
            {activity.userId && (
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <User className="h-3 w-3" />
                <span>用户: {activity.userId}</span>
              </div>
            )}
          </div>
        )}
      </div>
      
      <div className="flex-shrink-0">
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <ExternalLink className="h-3 w-3" />
        </Button>
      </div>
    </motion.div>
  );
}

function ActivitySkeleton({ index }: { index: number }) {
  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.3, delay: index * 0.05 }}
      className="flex items-start space-x-3 p-3 rounded-lg border"
    >
      <Skeleton className="h-8 w-8 rounded-full flex-shrink-0" />
      <div className="flex-1 space-y-2">
        <div className="flex items-center justify-between">
          <Skeleton className="h-4 w-32" />
          <Skeleton className="h-3 w-16" />
        </div>
        <Skeleton className="h-3 w-full" />
        <Skeleton className="h-3 w-3/4" />
      </div>
    </motion.div>
  );
}

export function RecentActivity({
  activities,
  loading = false,
  className,
  maxItems = 5,
  showViewAll = true,
  onViewAll,
  onActivityClick,
}: RecentActivityProps) {
  const displayActivities = activities?.slice(0, maxItems) || [];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.4 }}
    >
      <Card className={cn('relative overflow-hidden', className)}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                📋 最近活动
              </CardTitle>
              <CardDescription>
                系统和用户的最新操作记录
              </CardDescription>
            </div>
            {showViewAll && onViewAll && (
              <Button variant="outline" size="sm" onClick={onViewAll}>
                查看全部
              </Button>
            )}
          </div>
        </CardHeader>
        
        <CardContent className="space-y-3">
          {loading ? (
            // Loading skeleton
            Array.from({ length: maxItems }).map((_, index) => (
              <ActivitySkeleton key={index} index={index} />
            ))
          ) : displayActivities.length > 0 ? (
            // Activity items
            displayActivities.map((activity, index) => (
              <ActivityItemComponent
                key={activity.id}
                activity={activity}
                onClick={onActivityClick}
                index={index}
              />
            ))
          ) : (
            // Empty state
            <div className="text-center py-8">
              <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">
                暂无活动记录
              </h3>
              <p className="text-sm text-muted-foreground">
                当有新的系统活动时，它们将显示在这里
              </p>
            </div>
          )}
        </CardContent>
        
        {/* Animated background gradient */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full animate-shimmer" />
      </Card>
    </motion.div>
  );
}

// Compact version for smaller spaces
export function CompactRecentActivity({
  activities,
  loading = false,
  className,
  maxItems = 3,
}: Omit<RecentActivityProps, 'showViewAll' | 'onViewAll'>) {
  const displayActivities = activities?.slice(0, maxItems) || [];

  return (
    <div className={cn('space-y-2', className)}>
      <h3 className="text-sm font-medium text-foreground mb-3">最近活动</h3>
      
      {loading ? (
        Array.from({ length: maxItems }).map((_, index) => (
          <div key={index} className="flex items-center space-x-2 p-2 rounded border">
            <Skeleton className="h-6 w-6 rounded-full" />
            <div className="flex-1 space-y-1">
              <Skeleton className="h-3 w-24" />
              <Skeleton className="h-2 w-16" />
            </div>
          </div>
        ))
      ) : displayActivities.length > 0 ? (
        displayActivities.map((activity, index) => (
          <motion.div
            key={activity.id}
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.2, delay: index * 0.05 }}
            className="flex items-center space-x-2 p-2 rounded border hover:bg-muted/50 transition-colors"
          >
            <div className="flex-shrink-0">
              {getActivityIcon(activity.type)}
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-xs font-medium text-foreground truncate">
                {activity.title}
              </p>
              <p className="text-xs text-muted-foreground">
                {formatDistanceToNow(new Date(activity.timestamp), { 
                  addSuffix: true, 
                  locale: zhCN 
                })}
              </p>
            </div>
          </motion.div>
        ))
      ) : (
        <p className="text-xs text-muted-foreground text-center py-4">
          暂无活动记录
        </p>
      )}
    </div>
  );
}

export default RecentActivity;
