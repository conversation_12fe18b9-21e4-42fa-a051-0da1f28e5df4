"use client";

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import {
  cardStyles,
  typography,
  spacing,
  iconStyles,
  statusStyles,
  gradients,
  combineStyles,
  themeAwareStyles
} from '@/lib/dashboard-styles';
import {
  Shield,
  Wifi,
  Database,
  Key,
  Server,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  RefreshCw
} from 'lucide-react';

interface SystemHealthData {
  apiStatus: 'healthy' | 'degraded' | 'down';
  databaseStatus: 'healthy' | 'degraded' | 'down';
  aiServiceStatus: 'healthy' | 'degraded' | 'down';
  apiKeysConfigured: number;
  totalApiKeys: number;
  uptime: number;
  lastCheck: string;
  responseTime: number;
}

interface SystemHealthSectionProps {
  className?: string;
  data?: SystemHealthData;
  loading?: boolean;
  onRefresh?: () => void;
}

const mockHealthData: SystemHealthData = {
  apiStatus: 'healthy',
  databaseStatus: 'healthy',
  aiServiceStatus: 'healthy',
  apiKeysConfigured: 3,
  totalApiKeys: 5,
  uptime: 99.8,
  lastCheck: new Date().toISOString(),
  responseTime: 245
};

const StatusIndicator = ({ 
  status, 
  label, 
  icon: Icon 
}: { 
  status: 'healthy' | 'degraded' | 'down'; 
  label: string;
  icon: React.ComponentType<any>;
}) => {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'healthy':
        return {
          color: 'text-green-600 dark:text-green-400',
          bg: 'bg-green-50 dark:bg-green-950/20',
          border: 'border-green-200 dark:border-green-800',
          badge: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
        };
      case 'degraded':
        return {
          color: 'text-yellow-600 dark:text-yellow-400',
          bg: 'bg-yellow-50 dark:bg-yellow-950/20',
          border: 'border-yellow-200 dark:border-yellow-800',
          badge: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
        };
      case 'down':
        return {
          color: 'text-red-600 dark:text-red-400',
          bg: 'bg-red-50 dark:bg-red-950/20',
          border: 'border-red-200 dark:border-red-800',
          badge: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
        };
      default:
        return {
          color: 'text-gray-600 dark:text-gray-400',
          bg: 'bg-gray-50 dark:bg-gray-950/20',
          border: 'border-gray-200 dark:border-gray-800',
          badge: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
        };
    }
  };

  const config = getStatusConfig(status);

  return (
    <div className={cn(
      'flex items-center justify-between p-3 rounded-lg border',
      config.bg,
      config.border
    )}>
      <div className="flex items-center gap-3">
        <Icon className={cn(iconStyles.md, config.color)} />
        <span className={cn(typography.body, themeAwareStyles.adaptiveText)}>
          {label}
        </span>
      </div>
      <Badge className={config.badge}>
        {status === 'healthy' ? '正常' : status === 'degraded' ? '降级' : '故障'}
      </Badge>
    </div>
  );
};

export function SystemHealthSection({ 
  className, 
  data = mockHealthData, 
  loading = false,
  onRefresh 
}: SystemHealthSectionProps) {
  if (loading) {
    return (
      <Card className={cn(cardStyles.base, className)}>
        <CardHeader className={spacing.cardHeader}>
          <div className="flex items-center justify-between">
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-8 w-20" />
          </div>
        </CardHeader>
        <CardContent className={spacing.cardContent}>
          <div className="space-y-3">
            {Array.from({ length: 4 }).map((_, i) => (
              <Skeleton key={i} className="h-12 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const overallStatus = data.apiStatus === 'healthy' && 
                       data.databaseStatus === 'healthy' && 
                       data.aiServiceStatus === 'healthy' 
                       ? 'healthy' : 'degraded';

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className={cn(cardStyles.elevated, className)}>
        <CardHeader className={spacing.cardHeader}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Shield className={cn(iconStyles.md, 'text-primary')} />
              <CardTitle className={typography.h3}>系统健康状态</CardTitle>
            </div>
            <div className="flex items-center gap-2">
              <Badge 
                className={cn(
                  overallStatus === 'healthy' 
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                    : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                )}
              >
                {overallStatus === 'healthy' ? '系统正常' : '部分降级'}
              </Badge>
              {onRefresh && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onRefresh}
                  className="h-8 w-8 p-0"
                >
                  <RefreshCw className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className={spacing.cardContent}>
          <div className="space-y-3">
            <StatusIndicator
              status={data.apiStatus}
              label="API 服务"
              icon={Server}
            />
            <StatusIndicator
              status={data.databaseStatus}
              label="数据库"
              icon={Database}
            />
            <StatusIndicator
              status={data.aiServiceStatus}
              label="AI 服务"
              icon={Wifi}
            />
            
            {/* Additional Metrics */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 mt-4 pt-4 border-t border-border/60">
              <div className="text-center">
                <div className={cn(typography.h4, 'text-primary')}>
                  {data.apiKeysConfigured}/{data.totalApiKeys}
                </div>
                <div className={cn(typography.caption, 'text-muted-foreground')}>
                  API密钥配置
                </div>
              </div>
              <div className="text-center">
                <div className={cn(typography.h4, 'text-green-600 dark:text-green-400')}>
                  {data.uptime}%
                </div>
                <div className={cn(typography.caption, 'text-muted-foreground')}>
                  系统可用性
                </div>
              </div>
              <div className="text-center">
                <div className={cn(typography.h4, 'text-blue-600 dark:text-blue-400')}>
                  {data.responseTime}ms
                </div>
                <div className={cn(typography.caption, 'text-muted-foreground')}>
                  响应时间
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
