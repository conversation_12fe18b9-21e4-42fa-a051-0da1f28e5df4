"use client";

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { motion, AnimatePresence } from 'framer-motion';
import { useRecentActivity } from '@/lib/dashboard-data';
import { Skeleton } from '@/components/ui/skeleton';
import { Progress } from '@/components/ui/progress';
import {
  Activity,
  ArrowRight,
  Play,
  Plus,
  Settings,
  LayoutTemplate,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Info,
  Search,
  Filter,
  RefreshCw
} from 'lucide-react';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import {
  cardStyles,
  typography,
  spacing,
  iconStyles,
  animations,
  statusStyles,
  combineStyles
} from '@/lib/dashboard-styles';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

interface AdvancedActivityFeedProps {
  className?: string;
}

const activityIcons = {
  test_execution: Play,
  agent_created: Plus,
  agent_updated: Settings,
  template_used: LayoutTemplate,
};

const statusIcons = {
  success: CheckCircle,
  error: XCircle,
  warning: AlertTriangle,
  info: Info,
};

const statusColors = {
  success: 'bg-green-50/80 border-green-200/60 text-green-700 dark:bg-green-950/30 dark:border-green-800/40 dark:text-green-300',
  error: 'bg-red-50/80 border-red-200/60 text-red-700 dark:bg-red-950/30 dark:border-red-800/40 dark:text-red-300',
  warning: 'bg-yellow-50/80 border-yellow-200/60 text-yellow-700 dark:bg-yellow-950/30 dark:border-yellow-800/40 dark:text-yellow-300',
  info: 'bg-blue-50/80 border-blue-200/60 text-blue-700 dark:bg-blue-950/30 dark:border-blue-800/40 dark:text-blue-300',
};

const statusIconColors = {
  success: 'text-green-600 dark:text-green-400',
  error: 'text-red-600 dark:text-red-400',
  warning: 'text-yellow-600 dark:text-yellow-400',
  info: 'text-blue-600 dark:text-blue-400',
};

const filterOptions = [
  { value: 'all', label: '全部' },
  { value: 'test_execution', label: '测试执行' },
  { value: 'agent_created', label: 'Agent 创建' },
  { value: 'agent_updated', label: 'Agent 更新' },
  { value: 'template_used', label: '模板使用' },
];

export function AdvancedActivityFeed({ className }: AdvancedActivityFeedProps) {
  const { data: activities, isLoading, error, refetch } = useRecentActivity();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Manual refresh with visual feedback
  const handleRefresh = async () => {
    if (isRefreshing) return;

    setIsRefreshing(true);
    try {
      await refetch();
      // Show success feedback briefly
      setTimeout(() => setIsRefreshing(false), 800);
    } catch (error) {
      console.error('Failed to refresh activities:', error);
      setIsRefreshing(false);
    }
  };

  // Filter and search activities
  const filteredActivities = useMemo(() => {
    if (!activities) return [];

    return activities.filter(activity => {
      const matchesSearch = searchTerm === '' || 
        activity.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        activity.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        activity.agentName?.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesFilter = selectedFilter === 'all' || activity.type === selectedFilter;

      return matchesSearch && matchesFilter;
    });
  }, [activities, searchTerm, selectedFilter]);

  if (error) {
    return (
      <Card className={cn('border-red-200 bg-red-50/50', className)}>
        <CardHeader className="p-3">
          <CardTitle className="text-sm text-red-600">最近活动</CardTitle>
          <CardDescription className="text-xs text-red-500">
            加载失败，请稍后重试
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card className={combineStyles(
      cardStyles.elevated,
      animations.fadeIn,
      'overflow-hidden relative',
      'bg-gradient-to-br from-card/95 to-card/85',
      className
    )}>
      {/* Subtle background pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5 pointer-events-none"></div>

      <CardHeader className={combineStyles(
        spacing.cardHeader,
        'border-b border-border/40 bg-muted/10 backdrop-blur-sm relative z-10'
      )}>
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <div className="relative">
              <Activity className={combineStyles(
                iconStyles.sm,
                'text-primary',
                animations.wiggle
              )} />
              <div className="absolute -top-1 -right-1 w-2 h-2 bg-green-500 rounded-full animate-pulse shadow-sm"></div>
            </div>
            <CardTitle className={combineStyles(
              typography.h4,
              'bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent'
            )}>
              活动动态
            </CardTitle>
            <Badge variant="outline" className={combineStyles(
              'text-xs px-2 h-5 font-medium',
              'bg-green-50/80 text-green-700 border-green-200/60',
              'dark:bg-green-950/30 dark:text-green-300 dark:border-green-800/40',
              'shadow-sm'
            )}>
              实时
            </Badge>
          </div>
          <div className={combineStyles(spacing.inlineGap, 'flex items-center')}>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing}
              className={combineStyles(
                'h-8 w-8 p-0 rounded-full',
                'hover:bg-primary/10 hover:text-primary hover:scale-110',
                'transition-all duration-200 touch-manipulation',
                isRefreshing && 'animate-pulse'
              )}
            >
              <RefreshCw className={combineStyles(
                iconStyles.sm,
                isRefreshing && "animate-spin",
                'transition-transform duration-200'
              )} />
            </Button>
            <Link href="/test-history">
              <Button
                variant="ghost"
                size="sm"
                className={combineStyles(
                  'h-8 px-3 text-xs font-medium',
                  'hover:bg-muted/60 hover:text-foreground',
                  'transition-all duration-200 touch-manipulation',
                  'border border-transparent hover:border-border/40'
                )}
              >
                查看全部
                <ArrowRight className={combineStyles(
                  iconStyles.xs,
                  'ml-1 transition-transform duration-200 group-hover:translate-x-0.5'
                )} />
              </Button>
            </Link>
          </div>
        </div>

        {/* Search and Filter - Enhanced Design */}
        <div className="flex flex-col sm:flex-row gap-3">
          <div className="relative flex-1">
            <Search className={combineStyles(
              'absolute left-3 top-1/2 transform -translate-y-1/2',
              iconStyles.sm,
              'text-muted-foreground transition-colors duration-200'
            )} />
            <Input
              placeholder="搜索活动..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={combineStyles(
                'h-10 pl-10 pr-4 text-sm touch-manipulation',
                'border-border/60 bg-background/50 backdrop-blur-sm',
                'focus:border-primary/60 focus:bg-background',
                'transition-all duration-200'
              )}
            />
          </div>
          <select
            value={selectedFilter}
            onChange={(e) => setSelectedFilter(e.target.value)}
            className={combineStyles(
              'h-10 px-3 text-sm border rounded-md touch-manipulation min-w-[120px]',
              'border-border/60 bg-background/50 backdrop-blur-sm',
              'focus:border-primary/60 focus:bg-background',
              'transition-all duration-200 cursor-pointer'
            )}
          >
            {filterOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        <CardDescription className={combineStyles(
          typography.caption,
          'flex items-center justify-between'
        )}>
          <span>实时活动动态</span>
          <Badge variant="secondary" className="text-xs px-2 h-5">
            {filteredActivities.length} 项
          </Badge>
        </CardDescription>
      </CardHeader>
      <CardContent className="p-4 pt-0 relative z-10">
        {isLoading ? (
          <div className="space-y-3">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="flex items-start gap-3">
                <Skeleton className="h-6 w-6 rounded-full mt-0.5" />
                <div className="flex-1 space-y-1">
                  <Skeleton className="h-3 w-32" />
                  <Skeleton className="h-2 w-24" />
                  <Skeleton className="h-2 w-16" />
                </div>
              </div>
            ))}
          </div>
        ) : filteredActivities && filteredActivities.length > 0 ? (
          <div className="space-y-4">
            <AnimatePresence>
              {filteredActivities.slice(0, 5).map((activity, index) => {
                const ActivityIcon = activityIcons[activity.type] || Activity;
                const StatusIcon = statusIcons[activity.status] || Info;
                
                return (
                  <motion.div
                    key={activity.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    className={combineStyles(
                      'flex items-start gap-4 group p-4 rounded-xl',
                      'bg-card/40 border border-border/40 backdrop-blur-sm',
                      'hover:bg-card/60 hover:shadow-md hover:border-border/60 hover:-translate-y-0.5',
                      'transition-all duration-300 cursor-pointer',
                      'relative overflow-hidden'
                    )}
                  >
                    <div className="relative">
                      <div className={combineStyles(
                        'h-10 w-10 rounded-xl flex items-center justify-center border-2 shadow-sm',
                        'transition-all duration-300 group-hover:scale-110 group-hover:rotate-3',
                        statusColors[activity.status]
                      )}>
                        <StatusIcon className={combineStyles(
                          iconStyles.sm,
                          statusIconColors[activity.status],
                          'transition-all duration-300 group-hover:scale-110'
                        )} />
                      </div>
                      {/* Activity type indicator */}
                      <div className="absolute -bottom-1 -right-1 h-4 w-4 bg-background rounded-full border border-border/40 flex items-center justify-center">
                        <ActivityIcon className={combineStyles(iconStyles.xs, 'text-muted-foreground')} />
                      </div>
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className={combineStyles(
                          typography.caption,
                          'font-semibold truncate text-foreground/90'
                        )}>
                          {activity.title}
                        </h4>
                        {activity.duration && (
                          <Badge variant="outline" className={combineStyles(
                            'text-xs px-2 h-5 font-medium',
                            'bg-muted/50 text-muted-foreground border-border/40',
                            'flex items-center gap-1'
                          )}>
                            <Clock className={iconStyles.xs} />
                            {Math.round(activity.duration / 1000)}s
                          </Badge>
                        )}
                      </div>

                      <p className={combineStyles(
                        'text-xs text-muted-foreground/80 truncate mb-3 leading-relaxed'
                      )}>
                        {activity.description}
                      </p>

                      {/* Enhanced Progress indicator for test executions */}
                      {activity.type === 'test_execution' && activity.duration && (
                        <div className="mb-3">
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-xs text-muted-foreground">执行进度</span>
                            <span className="text-xs font-medium text-foreground/80">
                              {activity.status === 'success' ? '100%' : '0%'}
                            </span>
                          </div>
                          <Progress
                            value={activity.status === 'success' ? 100 : 0}
                            className="h-2 bg-muted/50"
                          />
                        </div>
                      )}

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className={combineStyles(
                            'text-xs text-muted-foreground/80 font-medium'
                          )}>
                            {formatDistanceToNow(new Date(activity.timestamp), {
                              addSuffix: true,
                              locale: zhCN
                            })}
                          </span>
                        </div>

                        <div className="flex items-center gap-1">
                          {activity.agentName && (
                            <Badge variant="secondary" className={combineStyles(
                              'text-xs px-2 h-5 font-medium',
                              'bg-primary/10 text-primary border-primary/20'
                            )}>
                              {activity.agentName}
                            </Badge>
                          )}

                          {activity.duration && activity.duration < 2000 && (
                            <Badge variant="outline" className={combineStyles(
                              'text-xs px-2 h-5 font-medium',
                              'bg-green-50/80 text-green-700 border-green-200/60',
                              'dark:bg-green-950/30 dark:text-green-300 dark:border-green-800/40'
                            )}>
                              快速
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    {/* Action button */}
                    <div className="flex flex-col items-center gap-2">
                      {activity.agentId && (
                        <Link href={`/agents/${activity.agentId}`}>
                          <Button
                            variant="ghost"
                            size="sm"
                            className={combineStyles(
                              'h-8 w-8 p-0 rounded-full',
                              'opacity-0 group-hover:opacity-100',
                              'hover:bg-primary/10 hover:text-primary hover:scale-110',
                              'transition-all duration-300'
                            )}
                          >
                            <ArrowRight className={iconStyles.sm} />
                          </Button>
                        </Link>
                      )}

                      {/* Status indicator dot */}
                      <div className={combineStyles(
                        'w-2 h-2 rounded-full',
                        activity.status === 'success' ? 'bg-green-500' :
                        activity.status === 'error' ? 'bg-red-500' :
                        activity.status === 'warning' ? 'bg-yellow-500' : 'bg-blue-500',
                        'opacity-60 group-hover:opacity-100 transition-opacity duration-300'
                      )} />
                    </div>
                  </motion.div>
                );
              })}
            </AnimatePresence>
          </div>
        ) : (
          <div className="text-center py-8">
            <div className="relative mb-4">
              <Activity className={combineStyles(
                'h-12 w-12 text-muted-foreground/40 mx-auto',
                animations.pulse
              )} />
              <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-transparent rounded-full blur-xl"></div>
            </div>
            <h3 className={combineStyles(
              typography.h4,
              'text-muted-foreground mb-2'
            )}>
              {searchTerm || selectedFilter !== 'all' ? '没有找到匹配的活动' : '暂无活动记录'}
            </h3>
            <p className={combineStyles(
              typography.caption,
              'text-muted-foreground/80 mb-4'
            )}>
              {searchTerm || selectedFilter !== 'all'
                ? '尝试调整搜索条件或过滤器'
                : '开始创建和测试 Agent 来查看活动记录'
              }
            </p>
            {(!searchTerm && selectedFilter === 'all') && (
              <Link href="/agents/create">
                <Button
                  variant="outline"
                  size="sm"
                  className={combineStyles(
                    'h-9 px-4 text-sm font-medium',
                    'hover:bg-primary hover:text-primary-foreground',
                    'transition-all duration-200'
                  )}
                >
                  <Plus className={combineStyles(iconStyles.sm, 'mr-2')} />
                  创建第一个 Agent
                </Button>
              </Link>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

AdvancedActivityFeed.displayName = 'AdvancedActivityFeed';
