"use client";

import React from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

interface DashboardSkeletonProps {
  className?: string;
}

// Individual skeleton components
export function StatsCardSkeleton({ className }: { className?: string }) {
  return (
    <Card className={cn('relative overflow-hidden', className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <Skeleton className="h-4 w-24" />
        <Skeleton className="h-6 w-6 rounded-full" />
      </CardHeader>
      <CardContent className="space-y-2">
        <Skeleton className="h-8 w-16" />
        <Skeleton className="h-3 w-32" />
        <Skeleton className="h-3 w-20" />
      </CardContent>
      {/* Animated shimmer effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full animate-shimmer" />
    </Card>
  );
}

export function ChartCardSkeleton({ 
  className, 
  height = 300,
  title = true,
  description = true 
}: { 
  className?: string;
  height?: number;
  title?: boolean;
  description?: boolean;
}) {
  return (
    <Card className={cn('relative overflow-hidden', className)}>
      <CardHeader className="space-y-2">
        {title && <Skeleton className="h-6 w-32" />}
        {description && <Skeleton className="h-4 w-48" />}
      </CardHeader>
      <CardContent>
        <Skeleton className={`w-full`} style={{ height: `${height}px` }} />
      </CardContent>
      {/* Animated shimmer effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full animate-shimmer" />
    </Card>
  );
}

export function ActivityItemSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn('flex items-start space-x-3 p-3 rounded-lg border', className)}>
      <Skeleton className="h-8 w-8 rounded-full flex-shrink-0" />
      <div className="flex-1 space-y-2">
        <div className="flex items-center justify-between">
          <Skeleton className="h-4 w-32" />
          <Skeleton className="h-3 w-16" />
        </div>
        <Skeleton className="h-3 w-full" />
        <Skeleton className="h-3 w-3/4" />
      </div>
    </div>
  );
}

export function QuickActionSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn('space-y-3 p-4 rounded-lg border', className)}>
      <div className="flex items-center gap-2">
        <Skeleton className="h-5 w-5 rounded" />
        <Skeleton className="h-5 w-24" />
      </div>
      <Skeleton className="h-4 w-full" />
      <Skeleton className="h-4 w-3/4" />
      <Skeleton className="h-9 w-full" />
    </div>
  );
}

// Main dashboard skeleton
export function DashboardSkeleton({ className }: DashboardSkeletonProps) {
  return (
    <div className={cn('space-y-8', className)}>
      {/* Hero Section Skeleton */}
      <div className="text-center space-y-4">
        <Skeleton className="h-10 w-64 mx-auto" />
        <Skeleton className="h-6 w-96 mx-auto" />
        <div className="flex justify-center gap-4">
          <Skeleton className="h-11 w-32" />
          <Skeleton className="h-11 w-32" />
        </div>
      </div>



      {/* Charts Section Skeleton - Single Column */}
      <div className="space-y-6">
        {/* Usage Trend Chart */}
        <ChartCardSkeleton height={300} />

        {/* Top Agents Chart */}
        <ChartCardSkeleton height={300} />

        {/* System Health Chart */}
        <ChartCardSkeleton height={300} />
      </div>

      {/* Quick Actions and Recent Activity - Single Column */}
      <div className="space-y-6">
        {/* Quick Actions Skeleton */}
        <Card className="relative overflow-hidden">
          <CardHeader>
            <Skeleton className="h-6 w-24" />
            <Skeleton className="h-4 w-48" />
          </CardHeader>
          <CardContent className="grid grid-cols-1 gap-4">
            <QuickActionSkeleton />
            <QuickActionSkeleton />
          </CardContent>
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full animate-shimmer" />
        </Card>

        {/* Recent Activity Skeleton */}
        <Card className="relative overflow-hidden">
          <CardHeader>
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-4 w-40" />
          </CardHeader>
          <CardContent className="space-y-3">
            {Array.from({ length: 5 }).map((_, index) => (
              <ActivityItemSkeleton key={index} />
            ))}
          </CardContent>
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full animate-shimmer" />
        </Card>
      </div>

      {/* System Status Skeleton */}
      <Card className="relative overflow-hidden">
        <CardHeader>
          <Skeleton className="h-6 w-24" />
        </CardHeader>
        <CardContent className="space-y-3">
          {Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="flex items-center justify-between">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-6 w-16 rounded-full" />
            </div>
          ))}
        </CardContent>
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full animate-shimmer" />
      </Card>
    </div>
  );
}

// Compact skeleton for smaller spaces
export function CompactDashboardSkeleton({ className }: DashboardSkeletonProps) {
  return (
    <div className={cn('space-y-6', className)}>


      {/* Single Chart */}
      <ChartCardSkeleton height={250} />

      {/* Quick Info - Single Column */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card className="relative overflow-hidden">
          <CardHeader>
            <Skeleton className="h-5 w-20" />
          </CardHeader>
          <CardContent className="space-y-2">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="flex justify-between">
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-4 w-12" />
              </div>
            ))}
          </CardContent>
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full animate-shimmer" />
        </Card>

        <Card className="relative overflow-hidden">
          <CardHeader>
            <Skeleton className="h-5 w-24" />
          </CardHeader>
          <CardContent className="space-y-2">
            {Array.from({ length: 3 }).map((_, index) => (
              <ActivityItemSkeleton key={index} />
            ))}
          </CardContent>
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full animate-shimmer" />
        </Card>
      </div>
    </div>
  );
}

// Loading states for specific sections
export function StatsGridSkeleton({ count = 4, className }: { count?: number; className?: string }) {
  return (
    <div className={cn('grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4', className)}>
      {Array.from({ length: count }).map((_, index) => (
        <StatsCardSkeleton key={index} />
      ))}
    </div>
  );
}

export function ChartsGridSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn('space-y-6', className)}>
      <ChartCardSkeleton height={300} />
      <ChartCardSkeleton height={300} />
      <ChartCardSkeleton height={300} />
    </div>
  );
}

export default DashboardSkeleton;
