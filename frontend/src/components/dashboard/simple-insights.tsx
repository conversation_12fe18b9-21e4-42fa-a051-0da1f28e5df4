"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import { 
  Lightbulb,
  TrendingUp,
  Clock,
  Target,
  Zap,
  AlertTriangle,
  CheckCircle,
  Info,
  X,
  ArrowRight,
  Star,
  Settings
} from 'lucide-react';

// Simple Insights Types
interface SimpleInsight {
  id: string;
  type: 'tip' | 'optimization' | 'warning' | 'success';
  title: string;
  description: string;
  action?: {
    label: string;
    href?: string;
    onClick?: () => void;
  };
  priority: 'high' | 'medium' | 'low';
  category: 'performance' | 'usage' | 'setup' | 'best-practice';
  dismissible: boolean;
}

interface SimpleInsightsProps {
  className?: string;
  insights?: SimpleInsight[];
  loading?: boolean;
  onDismiss?: (insightId: string) => void;
  onAction?: (insightId: string, action: string) => void;
}

// Mock insights for personal users
const mockInsights: SimpleInsight[] = [
  {
    id: '1',
    type: 'tip',
    title: '创建更多Agent提升效率',
    description: '您目前只有3个Agent。考虑为不同任务创建专门的Agent，比如代码审查、文档生成等。',
    action: {
      label: '创建Agent',
      href: '/create'
    },
    priority: 'medium',
    category: 'usage',
    dismissible: true
  },
  {
    id: '2',
    type: 'optimization',
    title: '优化Agent响应时间',
    description: '您的"文档生成器"平均响应时间为2.1秒，可以通过调整模型参数来提升速度。',
    action: {
      label: '优化设置',
      href: '/manage'
    },
    priority: 'high',
    category: 'performance',
    dismissible: true
  },
  {
    id: '3',
    type: 'success',
    title: '本周使用量增长25%',
    description: '您的Agent使用频率持续增长，说明AI助手正在有效提升您的工作效率！',
    priority: 'low',
    category: 'usage',
    dismissible: true
  },
  {
    id: '4',
    type: 'warning',
    title: '设置AI密钥',
    description: '您还没有配置AI模型的AI密钥，这会影响Agent的正常运行。',
    action: {
      label: '配置密钥',
      href: '/api-keys'
    },
    priority: 'high',
    category: 'setup',
    dismissible: false
  }
];

function InsightCard({ insight, onDismiss, onAction }: {
  insight: SimpleInsight;
  onDismiss?: (id: string) => void;
  onAction?: (id: string, action: string) => void;
}) {
  const getTypeConfig = (type: string) => {
    switch (type) {
      case 'tip':
        return {
          icon: Lightbulb,
          color: 'bg-blue-50 border-blue-200 dark:bg-blue-950/20 dark:border-blue-800',
          iconColor: 'text-blue-600 dark:text-blue-400',
          badgeVariant: 'secondary' as const
        };
      case 'optimization':
        return {
          icon: TrendingUp,
          color: 'bg-purple-50 border-purple-200 dark:bg-purple-950/20 dark:border-purple-800',
          iconColor: 'text-purple-600 dark:text-purple-400',
          badgeVariant: 'secondary' as const
        };
      case 'warning':
        return {
          icon: AlertTriangle,
          color: 'bg-yellow-50 border-yellow-200 dark:bg-yellow-950/20 dark:border-yellow-800',
          iconColor: 'text-yellow-600 dark:text-yellow-400',
          badgeVariant: 'destructive' as const
        };
      case 'success':
        return {
          icon: CheckCircle,
          color: 'bg-green-50 border-green-200 dark:bg-green-950/20 dark:border-green-800',
          iconColor: 'text-green-600 dark:text-green-400',
          badgeVariant: 'default' as const
        };
      default:
        return {
          icon: Info,
          color: 'bg-gray-50 border-gray-200 dark:bg-gray-950/20 dark:border-gray-800',
          iconColor: 'text-gray-600 dark:text-gray-400',
          badgeVariant: 'outline' as const
        };
    }
  };

  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case 'high': return '重要';
      case 'medium': return '中等';
      case 'low': return '一般';
      default: return '';
    }
  };

  const config = getTypeConfig(insight.type);
  const IconComponent = config.icon;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      <Card className={cn("relative group", config.color)}>
        <CardContent className="p-3 sm:p-4">
          <div className="flex items-start gap-2 sm:gap-3">
            <div className={cn("p-2 rounded-lg bg-white/50 dark:bg-black/20 flex-shrink-0")}>
              <IconComponent className={cn("h-3 w-3 sm:h-4 sm:w-4", config.iconColor)} />
            </div>

            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-2">
                <h3 className="font-medium text-xs sm:text-sm">{insight.title}</h3>
                {insight.priority !== 'low' && (
                  <Badge variant={config.badgeVariant} className="text-xs">
                    {getPriorityLabel(insight.priority)}
                  </Badge>
                )}
              </div>

              <p className="text-xs sm:text-sm text-muted-foreground mb-3 leading-relaxed">
                {insight.description}
              </p>

              {insight.action && (
                <Button
                  size="sm"
                  variant="outline"
                  className="text-xs min-h-[32px]"
                  onClick={() => {
                    if (insight.action?.onClick) {
                      insight.action.onClick();
                    } else if (onAction) {
                      onAction(insight.id, insight.action.label);
                    }
                  }}
                >
                  {insight.action.label}
                  <ArrowRight className="h-3 w-3 ml-1" />
                </Button>
              )}
            </div>

            {insight.dismissible && onDismiss && (
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 opacity-100 sm:opacity-0 sm:group-hover:opacity-100 transition-opacity flex-shrink-0"
                onClick={() => onDismiss(insight.id)}
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

function InsightsSummary({ insights }: { insights: SimpleInsight[] }) {
  const highPriorityCount = insights.filter(i => i.priority === 'high').length;
  const optimizationCount = insights.filter(i => i.type === 'optimization').length;
  const setupCount = insights.filter(i => i.category === 'setup').length;

  return (
    <div className="grid grid-cols-3 gap-2 sm:gap-4 mb-4 sm:mb-6">
      <div className="text-center p-2 sm:p-3 rounded-lg bg-muted/50">
        <div className="text-base sm:text-lg font-bold text-red-600 dark:text-red-400">{highPriorityCount}</div>
        <div className="text-xs text-muted-foreground">重要建议</div>
      </div>
      <div className="text-center p-2 sm:p-3 rounded-lg bg-muted/50">
        <div className="text-base sm:text-lg font-bold text-purple-600 dark:text-purple-400">{optimizationCount}</div>
        <div className="text-xs text-muted-foreground">优化机会</div>
      </div>
      <div className="text-center p-2 sm:p-3 rounded-lg bg-muted/50">
        <div className="text-base sm:text-lg font-bold text-blue-600 dark:text-blue-400">{setupCount}</div>
        <div className="text-xs text-muted-foreground">配置项</div>
      </div>
    </div>
  );
}

export function SimpleInsights({ 
  className, 
  insights = mockInsights, 
  loading = false,
  onDismiss,
  onAction 
}: SimpleInsightsProps) {
  const [dismissedInsights, setDismissedInsights] = useState<Set<string>>(new Set());

  const handleDismiss = (insightId: string) => {
    setDismissedInsights(prev => new Set([...prev, insightId]));
    onDismiss?.(insightId);
  };

  const handleAction = (insightId: string, action: string) => {
    console.log('Insight action:', insightId, action);
    onAction?.(insightId, action);
  };

  const visibleInsights = insights.filter(insight => !dismissedInsights.has(insight.id));

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <div className="h-6 bg-muted animate-pulse rounded" />
          <div className="h-4 bg-muted animate-pulse rounded w-2/3" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-3 gap-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="h-16 bg-muted animate-pulse rounded-lg" />
              ))}
            </div>
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="h-24 bg-muted animate-pulse rounded-lg" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Star className="h-5 w-5" />
          智能建议
        </CardTitle>
        <CardDescription>
          基于您的使用模式提供个性化建议
        </CardDescription>
      </CardHeader>

      <CardContent>
        {visibleInsights.length === 0 ? (
          <div className="text-center py-8">
            <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <h3 className="font-medium mb-2">太棒了！</h3>
            <p className="text-sm text-muted-foreground">
              目前没有需要关注的建议，您的Agent运行状况良好。
            </p>
          </div>
        ) : (
          <>
            <InsightsSummary insights={visibleInsights} />
            
            <div className="space-y-3">
              {visibleInsights
                .sort((a, b) => {
                  // Sort by priority: high > medium > low
                  const priorityOrder = { high: 3, medium: 2, low: 1 };
                  return priorityOrder[b.priority] - priorityOrder[a.priority];
                })
                .map((insight) => (
                  <InsightCard
                    key={insight.id}
                    insight={insight}
                    onDismiss={handleDismiss}
                    onAction={handleAction}
                  />
                ))}
            </div>

            {dismissedInsights.size > 0 && (
              <div className="pt-4 border-t mt-4">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs"
                  onClick={() => setDismissedInsights(new Set())}
                >
                  显示已忽略的建议 ({dismissedInsights.size})
                </Button>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
