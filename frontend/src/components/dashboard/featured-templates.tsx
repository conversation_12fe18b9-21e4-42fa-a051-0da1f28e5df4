"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { motion } from 'framer-motion';
import { useFeaturedTemplates } from '@/lib/dashboard-data';
import { Skeleton } from '@/components/ui/skeleton';
import {
  LayoutTemplate,
  Star,
  ArrowRight,
  Users,
  TrendingUp
} from 'lucide-react';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import {
  cardStyles,
  typography,
  spacing,
  iconStyles,
  animations,
  combineStyles
} from '@/lib/dashboard-styles';

interface FeaturedTemplatesProps {
  className?: string;
}

const difficultyColors = {
  '简单': 'bg-green-100 text-green-700 border-green-200',
  '中等': 'bg-yellow-100 text-yellow-700 border-yellow-200',
  '困难': 'bg-red-100 text-red-700 border-red-200',
};

export function FeaturedTemplates({ className }: FeaturedTemplatesProps) {
  const { data: templates, isLoading, error } = useFeaturedTemplates();

  if (error) {
    return (
      <Card className={cn('border-red-200 bg-red-50/50', className)}>
        <CardHeader className="p-3">
          <CardTitle className="text-sm text-red-600">精选模板</CardTitle>
          <CardDescription className="text-xs text-red-500">
            加载失败，请稍后重试
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card className={combineStyles(cardStyles.elevated, animations.fadeIn, className)}>
      <CardHeader className={spacing.cardHeader}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <LayoutTemplate className="h-4 w-4 text-muted-foreground" />
            <CardTitle className="text-sm font-medium">精选模板</CardTitle>
          </div>
          <Link href="/templates">
            <Button variant="ghost" size="sm" className="h-6 px-2 text-xs">
              查看全部
              <ArrowRight className="h-3 w-3 ml-1" />
            </Button>
          </Link>
        </div>
        <CardDescription className="text-xs">
          快速开始，一键部署
        </CardDescription>
      </CardHeader>
      <CardContent className="p-3 pt-0">
        {isLoading ? (
          <div className="space-y-2">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="flex items-center gap-3 p-2 rounded-lg border">
                <Skeleton className="h-8 w-8 rounded" />
                <div className="flex-1 space-y-1">
                  <Skeleton className="h-3 w-24" />
                  <Skeleton className="h-2 w-32" />
                </div>
                <Skeleton className="h-4 w-8" />
              </div>
            ))}
          </div>
        ) : (
          <div className="space-y-2">
            {templates?.slice(0, 3).map((template, index) => (
              <motion.div
                key={template.id}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.2, delay: index * 0.05 }}
              >
                <Link href={`/templates/${template.id}`}>
                  <div className={combineStyles(
                    'flex items-center gap-4 p-4 rounded-lg border group',
                    'hover:bg-muted/50 hover:border-border/80 hover:shadow-md',
                    'transition-all duration-300 cursor-pointer touch-manipulation min-h-[56px]',
                    animations.hoverLift
                  )}>
                    <div className={combineStyles(
                      'h-12 w-12 rounded-lg bg-gradient-to-br from-blue-500 via-purple-500 to-purple-600',
                      'flex items-center justify-center text-white font-semibold shadow-lg',
                      'transition-transform duration-300 group-hover:scale-110 group-hover:rotate-3'
                    )}>
                      {template.name.charAt(0)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="text-xs font-medium truncate">
                          {template.name}
                        </h4>
                        <Badge 
                          variant="outline" 
                          className={cn(
                            'text-[10px] px-1 h-4',
                            difficultyColors[template.difficulty as keyof typeof difficultyColors] || 
                            'bg-gray-100 text-gray-700 border-gray-200'
                          )}
                        >
                          {template.difficulty}
                        </Badge>
                      </div>
                      <p className="text-[10px] text-muted-foreground truncate">
                        {template.description}
                      </p>
                      <div className="flex items-center gap-2 mt-1">
                        <div className="flex items-center gap-1">
                          <Star className="h-2.5 w-2.5 text-yellow-500 fill-current" />
                          <span className="text-[10px] text-muted-foreground">
                            {template.rating.toFixed(1)}
                          </span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Users className="h-2.5 w-2.5 text-muted-foreground" />
                          <span className="text-[10px] text-muted-foreground">
                            {template.usageCount}
                          </span>
                        </div>
                      </div>
                    </div>
                    <ArrowRight className="h-3 w-3 text-muted-foreground group-hover:text-foreground transition-colors" />
                  </div>
                </Link>
              </motion.div>
            ))}

          </div>
        )}
      </CardContent>
    </Card>
  );
}

FeaturedTemplates.displayName = 'FeaturedTemplates';
