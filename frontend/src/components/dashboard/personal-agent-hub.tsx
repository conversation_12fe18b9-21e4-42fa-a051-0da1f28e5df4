"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import { 
  Plus, 
  Play, 
  Pause, 
  Settings, 
  MoreHorizontal,
  Clock,
  CheckCircle,
  AlertCircle,
  Zap,
  Star,
  Edit,
  Trash2
} from 'lucide-react';
import Link from 'next/link';

// Personal Agent Hub Types
interface PersonalAgent {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'inactive' | 'error';
  lastUsed: string;
  totalRuns: number;
  successRate: number;
  avgResponseTime: number;
  isFavorite: boolean;
  category: string;
}

interface PersonalAgentHubProps {
  className?: string;
  agents?: PersonalAgent[];
  loading?: boolean;
}

// Mock data for development
const mockAgents: PersonalAgent[] = [
  {
    id: '1',
    name: '代码审查助手',
    description: '帮助审查和优化代码质量',
    status: 'active',
    lastUsed: '2小时前',
    totalRuns: 45,
    successRate: 96,
    avgResponseTime: 1200,
    isFavorite: true,
    category: '开发工具'
  },
  {
    id: '2',
    name: '文档生成器',
    description: '自动生成技术文档和API说明',
    status: 'active',
    lastUsed: '1天前',
    totalRuns: 23,
    successRate: 89,
    avgResponseTime: 2100,
    isFavorite: false,
    category: '文档工具'
  },
  {
    id: '3',
    name: '内容创作助手',
    description: '协助创作博客文章和技术内容',
    status: 'inactive',
    lastUsed: '3天前',
    totalRuns: 12,
    successRate: 92,
    avgResponseTime: 1800,
    isFavorite: true,
    category: '创作工具'
  }
];

function AgentCard({ agent, onToggleFavorite, onEdit, onDelete, onRun }: {
  agent: PersonalAgent;
  onToggleFavorite: (id: string) => void;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
  onRun: (id: string) => void;
}) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'inactive': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      case 'error': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="h-3 w-3" />;
      case 'inactive': return <Pause className="h-3 w-3" />;
      case 'error': return <AlertCircle className="h-3 w-3" />;
      default: return <Clock className="h-3 w-3" />;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="group hover:shadow-md transition-all duration-200 relative">
        <CardHeader className="pb-3 p-4 sm:p-6">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <CardTitle className="text-sm sm:text-base font-medium truncate">{agent.name}</CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 opacity-100 sm:opacity-0 sm:group-hover:opacity-100 transition-opacity flex-shrink-0"
                  onClick={() => onToggleFavorite(agent.id)}
                >
                  <Star className={cn("h-3 w-3", agent.isFavorite && "fill-yellow-400 text-yellow-400")} />
                </Button>
              </div>
              <CardDescription className="text-xs sm:text-sm line-clamp-2">{agent.description}</CardDescription>
              <div className="flex flex-wrap items-center gap-2 mt-2">
                <Badge variant="secondary" className={cn("text-xs", getStatusColor(agent.status))}>
                  {getStatusIcon(agent.status)}
                  <span className="ml-1">{agent.status === 'active' ? '运行中' : agent.status === 'inactive' ? '已停止' : '错误'}</span>
                </Badge>
                <Badge variant="outline" className="text-xs">{agent.category}</Badge>
              </div>
            </div>
          </div>
        </CardHeader>

        <CardContent className="pt-0 p-4 sm:p-6">
          <div className="space-y-3 sm:space-y-4">
            {/* Quick Stats */}
            <div className="grid grid-cols-3 gap-2 sm:gap-3 text-sm">
              <div className="text-center">
                <div className="font-medium text-sm sm:text-base">{agent.totalRuns}</div>
                <div className="text-xs text-muted-foreground">总运行</div>
              </div>
              <div className="text-center">
                <div className="font-medium text-sm sm:text-base">{agent.successRate}%</div>
                <div className="text-xs text-muted-foreground">成功率</div>
              </div>
              <div className="text-center">
                <div className="font-medium text-sm sm:text-base">{agent.avgResponseTime}ms</div>
                <div className="text-xs text-muted-foreground">响应时间</div>
              </div>
            </div>

            {/* Success Rate Progress */}
            <div>
              <div className="flex justify-between text-xs mb-1">
                <span className="text-muted-foreground">性能表现</span>
                <span className="font-medium">{agent.successRate}%</span>
              </div>
              <Progress value={agent.successRate} className="h-2" />
            </div>

            {/* Last Used */}
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>最后使用: {agent.lastUsed}</span>
            </div>

            {/* Action Buttons - Mobile Optimized */}
            <div className="flex gap-2 pt-2">
              <Button
                size="sm"
                className="flex-1 min-h-[40px] text-xs sm:text-sm"
                onClick={() => onRun(agent.id)}
                disabled={agent.status === 'error'}
              >
                <Play className="h-3 w-3 mr-1" />
                运行
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="min-h-[40px] min-w-[40px] p-2"
                onClick={() => onEdit(agent.id)}
              >
                <Edit className="h-3 w-3" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="min-h-[40px] min-w-[40px] p-2"
                onClick={() => onDelete(agent.id)}
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

export function PersonalAgentHub({ className, agents = mockAgents, loading = false }: PersonalAgentHubProps) {
  const [filter, setFilter] = useState<'all' | 'favorites' | 'active'>('all');

  const filteredAgents = agents.filter(agent => {
    switch (filter) {
      case 'favorites': return agent.isFavorite;
      case 'active': return agent.status === 'active';
      default: return true;
    }
  });

  const handleToggleFavorite = (id: string) => {
    console.log('Toggle favorite:', id);
    // In production, this would update the agent's favorite status
  };

  const handleEdit = (id: string) => {
    console.log('Edit agent:', id);
    // In production, this would navigate to edit page
  };

  const handleDelete = (id: string) => {
    console.log('Delete agent:', id);
    // In production, this would show confirmation dialog
  };

  const handleRun = (id: string) => {
    console.log('Run agent:', id);
    // In production, this would navigate to test page
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <div className="h-6 bg-muted animate-pulse rounded" />
          <div className="h-4 bg-muted animate-pulse rounded w-2/3" />
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="h-48 bg-muted animate-pulse rounded-lg" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              我的Agent
            </CardTitle>
            <CardDescription>
              管理和运行您的个人AI助手
            </CardDescription>
          </div>
          <Button asChild>
            <Link href="/create">
              <Plus className="h-4 w-4 mr-2" />
              创建Agent
            </Link>
          </Button>
        </div>

        {/* Filter Tabs - Mobile Optimized */}
        <div className="flex flex-wrap gap-2 pt-4">
          <Button
            variant={filter === 'all' ? 'default' : 'outline'}
            size="sm"
            className="text-xs sm:text-sm min-h-[36px] px-3 sm:px-4"
            onClick={() => setFilter('all')}
          >
            全部 ({agents.length})
          </Button>
          <Button
            variant={filter === 'favorites' ? 'default' : 'outline'}
            size="sm"
            className="text-xs sm:text-sm min-h-[36px] px-3 sm:px-4"
            onClick={() => setFilter('favorites')}
          >
            收藏 ({agents.filter(a => a.isFavorite).length})
          </Button>
          <Button
            variant={filter === 'active' ? 'default' : 'outline'}
            size="sm"
            className="text-xs sm:text-sm min-h-[36px] px-3 sm:px-4"
            onClick={() => setFilter('active')}
          >
            运行中 ({agents.filter(a => a.status === 'active').length})
          </Button>
        </div>
      </CardHeader>

      <CardContent>
        {filteredAgents.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-muted-foreground mb-4">
              {filter === 'all' ? '还没有创建任何Agent' : `没有找到${filter === 'favorites' ? '收藏的' : '运行中的'}Agent`}
            </div>
            <Button asChild variant="outline">
              <Link href="/create">创建第一个Agent</Link>
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4">
            {filteredAgents.map((agent) => (
              <AgentCard
                key={agent.id}
                agent={agent}
                onToggleFavorite={handleToggleFavorite}
                onEdit={handleEdit}
                onDelete={handleDelete}
                onRun={handleRun}
              />
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
