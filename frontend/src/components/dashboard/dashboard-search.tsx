"use client";

import React, { useState, useCallback, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';
import { useDebounce } from '@/hooks/use-performance';
import { 
  Search, 
  Filter, 
  X, 
  Calendar,
  Tag,
  User,
  Activity,
  TrendingUp,
  Download,
  RefreshCw,
  Settings
} from 'lucide-react';

export interface SearchFilters {
  query: string;
  dateRange: 'today' | 'week' | 'month' | 'all';
  category: 'all' | 'agents' | 'activity' | 'stats';
  status: 'all' | 'active' | 'inactive' | 'error';
  sortBy: 'date' | 'name' | 'usage' | 'performance';
  sortOrder: 'asc' | 'desc';
}

interface DashboardSearchProps {
  onFiltersChange: (filters: SearchFilters) => void;
  onExport?: () => void;
  onRefresh?: () => void;
  className?: string;
  loading?: boolean;
}

const defaultFilters: SearchFilters = {
  query: '',
  dateRange: 'all',
  category: 'all',
  status: 'all',
  sortBy: 'date',
  sortOrder: 'desc',
};

const dateRangeOptions = [
  { value: 'today', label: '今天', icon: '📅' },
  { value: 'week', label: '本周', icon: '📆' },
  { value: 'month', label: '本月', icon: '🗓️' },
  { value: 'all', label: '全部', icon: '📊' },
];

const categoryOptions = [
  { value: 'all', label: '全部', icon: '🔍' },
  { value: 'agents', label: 'Agents', icon: '🤖' },
  { value: 'activity', label: '活动', icon: '📋' },
  { value: 'stats', label: '统计', icon: '📈' },
];

const statusOptions = [
  { value: 'all', label: '全部状态', icon: '⚪' },
  { value: 'active', label: '活跃', icon: '🟢' },
  { value: 'inactive', label: '非活跃', icon: '🟡' },
  { value: 'error', label: '错误', icon: '🔴' },
];

const sortOptions = [
  { value: 'date', label: '日期' },
  { value: 'name', label: '名称' },
  { value: 'usage', label: '使用量' },
  { value: 'performance', label: '性能' },
];

export function DashboardSearch({
  onFiltersChange,
  onExport,
  onRefresh,
  className,
  loading = false,
}: DashboardSearchProps) {
  const [filters, setFilters] = useState<SearchFilters>(defaultFilters);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [quickSearchOpen, setQuickSearchOpen] = useState(false);

  // Debounce search query for performance
  const debouncedQuery = useDebounce(filters.query, 300);

  // Update filters and notify parent
  const updateFilters = useCallback((newFilters: Partial<SearchFilters>) => {
    setFilters(prevFilters => {
      const updatedFilters = { ...prevFilters, ...newFilters };
      onFiltersChange(updatedFilters);
      return updatedFilters;
    });
  }, [onFiltersChange]);

  // Effect to handle debounced query changes
  React.useEffect(() => {
    if (debouncedQuery !== filters.query) {
      updateFilters({ query: debouncedQuery });
    }
  }, [debouncedQuery, filters.query, updateFilters]);

  // Clear all filters
  const clearFilters = useCallback(() => {
    setFilters(defaultFilters);
    onFiltersChange(defaultFilters);
  }, [onFiltersChange]);

  // Get active filter count
  const activeFilterCount = useMemo(() => {
    let count = 0;
    if (filters.query) count++;
    if (filters.dateRange !== 'all') count++;
    if (filters.category !== 'all') count++;
    if (filters.status !== 'all') count++;
    if (filters.sortBy !== 'date' || filters.sortOrder !== 'desc') count++;
    return count;
  }, [filters]);

  // Quick search suggestions
  const quickSearchSuggestions = [
    { label: '今日活跃 Agents', filters: { category: 'agents', status: 'active', dateRange: 'today' } },
    { label: '本周新创建', filters: { dateRange: 'week', sortBy: 'date' } },
    { label: '高使用量 Agents', filters: { category: 'agents', sortBy: 'usage', sortOrder: 'desc' } },
    { label: '系统错误', filters: { status: 'error', category: 'activity' } },
  ];

  return (
    <Card className={cn('relative overflow-hidden', className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              搜索与筛选
            </CardTitle>
            <CardDescription>
              快速查找和筛选仪表板数据
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {onRefresh && (
              <Button
                variant="outline"
                size="sm"
                onClick={onRefresh}
                disabled={loading}
                className="h-8"
              >
                <RefreshCw className={cn("h-4 w-4", loading && "animate-spin")} />
              </Button>
            )}
            {onExport && (
              <Button
                variant="outline"
                size="sm"
                onClick={onExport}
                className="h-8"
              >
                <Download className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Search Input */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索 Agents、活动或统计数据..."
            value={filters.query}
            onChange={(e) => setFilters(prev => ({ ...prev, query: e.target.value }))}
            className="pl-10 pr-10"
          />
          {filters.query && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => updateFilters({ query: '' })}
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>

        {/* Quick Search */}
        <div className="flex items-center gap-2">
          <Popover open={quickSearchOpen} onOpenChange={setQuickSearchOpen}>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm" className="h-8">
                <TrendingUp className="h-3 w-3 mr-1" />
                快速搜索
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-64 p-0" align="start">
              <Command>
                <CommandInput placeholder="搜索快捷方式..." />
                <CommandList>
                  <CommandEmpty>未找到快捷方式</CommandEmpty>
                  <CommandGroup>
                    {quickSearchSuggestions.map((suggestion, index) => (
                      <CommandItem
                        key={index}
                        onSelect={() => {
                          updateFilters(suggestion.filters);
                          setQuickSearchOpen(false);
                        }}
                      >
                        {suggestion.label}
                      </CommandItem>
                    ))}
                  </CommandGroup>
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>

          {/* Filter Toggle */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsFilterOpen(!isFilterOpen)}
            className="h-8"
          >
            <Filter className="h-3 w-3 mr-1" />
            筛选
            {activeFilterCount > 0 && (
              <Badge variant="secondary" className="ml-1 h-4 w-4 p-0 text-xs">
                {activeFilterCount}
              </Badge>
            )}
          </Button>

          {/* Clear Filters */}
          {activeFilterCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFilters}
              className="h-8 text-muted-foreground"
            >
              清除
            </Button>
          )}
        </div>

        {/* Advanced Filters */}
        <AnimatePresence>
          {isFilterOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.2 }}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 pt-4 border-t"
            >
              {/* Date Range */}
              <div className="space-y-2">
                <label className="text-sm font-medium">时间范围</label>
                <Select
                  value={filters.dateRange}
                  onValueChange={(value) => updateFilters({ dateRange: value as SearchFilters['dateRange'] })}
                >
                  <SelectTrigger className="h-8">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {dateRangeOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <span className="flex items-center gap-2">
                          <span>{option.icon}</span>
                          {option.label}
                        </span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Category */}
              <div className="space-y-2">
                <label className="text-sm font-medium">类别</label>
                <Select
                  value={filters.category}
                  onValueChange={(value) => updateFilters({ category: value as SearchFilters['category'] })}
                >
                  <SelectTrigger className="h-8">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {categoryOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <span className="flex items-center gap-2">
                          <span>{option.icon}</span>
                          {option.label}
                        </span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Status */}
              <div className="space-y-2">
                <label className="text-sm font-medium">状态</label>
                <Select
                  value={filters.status}
                  onValueChange={(value) => updateFilters({ status: value as SearchFilters['status'] })}
                >
                  <SelectTrigger className="h-8">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {statusOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <span className="flex items-center gap-2">
                          <span>{option.icon}</span>
                          {option.label}
                        </span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Sort */}
              <div className="space-y-2">
                <label className="text-sm font-medium">排序</label>
                <div className="flex gap-1">
                  <Select
                    value={filters.sortBy}
                    onValueChange={(value) => updateFilters({ sortBy: value as SearchFilters['sortBy'] })}
                  >
                    <SelectTrigger className="h-8 flex-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {sortOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => updateFilters({ 
                      sortOrder: filters.sortOrder === 'asc' ? 'desc' : 'asc' 
                    })}
                    className="h-8 w-8 p-0"
                  >
                    {filters.sortOrder === 'asc' ? '↑' : '↓'}
                  </Button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Active Filters Display */}
        {activeFilterCount > 0 && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex flex-wrap gap-2"
          >
            {filters.query && (
              <Badge variant="secondary" className="text-xs">
                搜索: {filters.query}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => updateFilters({ query: '' })}
                  className="ml-1 h-3 w-3 p-0"
                >
                  <X className="h-2 w-2" />
                </Button>
              </Badge>
            )}
            {filters.dateRange !== 'all' && (
              <Badge variant="secondary" className="text-xs">
                {dateRangeOptions.find(o => o.value === filters.dateRange)?.label}
              </Badge>
            )}
            {filters.category !== 'all' && (
              <Badge variant="secondary" className="text-xs">
                {categoryOptions.find(o => o.value === filters.category)?.label}
              </Badge>
            )}
            {filters.status !== 'all' && (
              <Badge variant="secondary" className="text-xs">
                {statusOptions.find(o => o.value === filters.status)?.label}
              </Badge>
            )}
          </motion.div>
        )}
      </CardContent>

      {/* Animated background gradient */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full animate-shimmer" />
    </Card>
  );
}

export default DashboardSearch;
