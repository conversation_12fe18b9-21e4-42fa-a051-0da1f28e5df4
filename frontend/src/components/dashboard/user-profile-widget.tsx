"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { motion } from 'framer-motion';
import { useUserProfile } from '@/lib/dashboard-data';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  User, 
  Settings,
  ArrowRight,
  Calendar,
  Activity,
  Zap
} from 'lucide-react';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import {
  cardStyles,
  typography,
  spacing,
  iconStyles,
  animations,
  gradients,
  combineStyles
} from '@/lib/dashboard-styles';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

interface UserProfileWidgetProps {
  className?: string;
}

const roleColors = {
  admin: 'bg-purple-100 text-purple-700 border-purple-200',
  user: 'bg-blue-100 text-blue-700 border-blue-200',
  moderator: 'bg-green-100 text-green-700 border-green-200',
};

export function UserProfileWidget({ className }: UserProfileWidgetProps) {
  const { data: profile, isLoading, error } = useUserProfile();

  if (error) {
    return (
      <Card className={cn('border-red-200 bg-red-50/50', className)}>
        <CardHeader className="p-3">
          <CardTitle className="text-sm text-red-600">用户信息</CardTitle>
          <CardDescription className="text-xs text-red-500">
            加载失败，请稍后重试
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card className={combineStyles(cardStyles.gradient, animations.fadeIn, className)}>
      <CardHeader className={spacing.cardHeader}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <User className="h-4 w-4 text-muted-foreground" />
            <CardTitle className="text-sm font-medium">个人信息</CardTitle>
          </div>
          <Link href="/profile">
            <Button variant="ghost" size="sm" className="h-6 px-2 text-xs">
              设置
              <Settings className="h-3 w-3 ml-1" />
            </Button>
          </Link>
        </div>
      </CardHeader>
      <CardContent className="p-3 pt-0">
        {isLoading ? (
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <Skeleton className="h-10 w-10 rounded-full" />
              <div className="flex-1 space-y-1">
                <Skeleton className="h-3 w-24" />
                <Skeleton className="h-2 w-32" />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-2">
              <Skeleton className="h-8 rounded" />
              <Skeleton className="h-8 rounded" />
            </div>
          </div>
        ) : profile ? (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-3"
          >
            {/* User Info - Enhanced Styling */}
            <div className={combineStyles(spacing.inlineGap, 'flex items-center')}>
              <div className="relative">
                <Avatar className={combineStyles(
                  'h-14 w-14 ring-2 ring-border/20 ring-offset-2 ring-offset-background',
                  'transition-all duration-300 hover:ring-primary/30 hover:scale-105'
                )}>
                  <AvatarImage src={profile.avatar} alt={profile.name} />
                  <AvatarFallback className={combineStyles(
                    'bg-gradient-to-br from-blue-500 via-purple-500 to-purple-600',
                    'text-white font-semibold shadow-lg',
                    typography.button
                  )}>
                    {profile.name.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="absolute -bottom-1 -right-1 h-4 w-4 bg-green-500 rounded-full border-2 border-background shadow-sm"></div>
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h3 className="text-sm font-medium truncate">
                    {profile.name}
                  </h3>
                  <Badge 
                    variant="outline" 
                    className={cn(
                      'text-[10px] px-1 h-4',
                      roleColors[profile.role as keyof typeof roleColors] || 
                      'bg-gray-100 text-gray-700 border-gray-200'
                    )}
                  >
                    {profile.role === 'admin' ? '管理员' : 
                     profile.role === 'moderator' ? '版主' : '用户'}
                  </Badge>
                </div>
                <p className="text-xs text-muted-foreground truncate">
                  {profile.email}
                </p>
              </div>
            </div>

            {/* Stats - Mobile Optimized */}
            <div className="grid grid-cols-2 gap-3">
              <div className="bg-muted/50 rounded-lg p-3 text-center min-h-[48px] flex flex-col justify-center">
                <div className="flex items-center justify-center gap-1 mb-1">
                  <Zap className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-600">
                    {profile.totalAgents}
                  </span>
                </div>
                <p className="text-xs text-muted-foreground">我的 Agent</p>
              </div>

              <div className="bg-muted/50 rounded-lg p-3 text-center min-h-[48px] flex flex-col justify-center">
                <div className="flex items-center justify-center gap-1 mb-1">
                  <Activity className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium text-green-600">
                    {profile.totalTests}
                  </span>
                </div>
                <p className="text-xs text-muted-foreground">测试次数</p>
              </div>
            </div>

            {/* Last Login */}
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <Calendar className="h-3 w-3" />
              <span>
                上次登录: {formatDistanceToNow(new Date(profile.lastLogin), { 
                  addSuffix: true,
                  locale: zhCN 
                })}
              </span>
            </div>

            {/* Quick Actions - Mobile Optimized */}
            <div className="flex gap-3">
              <Link href="/profile" className="flex-1">
                <Button variant="outline" size="sm" className="w-full h-10 text-sm touch-manipulation">
                  编辑资料
                </Button>
              </Link>
              <Link href="/settings" className="flex-1">
                <Button variant="outline" size="sm" className="w-full h-10 text-sm touch-manipulation">
                  系统设置
                </Button>
              </Link>
            </div>
          </motion.div>
        ) : (
          <div className="text-center py-4">
            <User className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-xs text-muted-foreground">无法加载用户信息</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

UserProfileWidget.displayName = 'UserProfileWidget';
