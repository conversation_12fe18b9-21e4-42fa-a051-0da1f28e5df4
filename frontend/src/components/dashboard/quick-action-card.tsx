"use client";

import React from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { 
  buttonStyles,
  quickActionThemes,
  animations,
  iconStyles,
  typography,
  combineStyles,
  accessibilityStyles
} from '@/lib/dashboard-styles';

interface QuickActionCardProps {
  action: {
    id: string;
    title: string;
    href: string;
    icon: string;
    color: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
    disabled?: boolean;
    badge?: string | number;
    description?: string;
  };
  index: number;
  IconComponent: React.ComponentType<{ className?: string }>;
  className?: string;
}

export function QuickActionCard({ 
  action, 
  index, 
  IconComponent, 
  className 
}: QuickActionCardProps) {
  const theme = quickActionThemes[action.color];
  
  const cardContent = (
    <motion.div
      initial={{ opacity: 0, scale: 0.9, y: 20 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      transition={{ 
        duration: 0.4, 
        delay: index * 0.08,
        ease: [0.4, 0, 0.2, 1]
      }}
      className={combineStyles(
        'relative group',
        accessibilityStyles.reducedMotion,
        className
      )}
    >
      <div
        className={combineStyles(
          buttonStyles.quickActionCard,
          theme.base,
          theme.hover,
          'quick-action-card quick-action-ripple quick-action-gradient quick-action-performance',
          action.disabled && buttonStyles.quickActionDisabled,
          accessibilityStyles.focusRing,
          accessibilityStyles.highContrast
        )}
        role="button"
        tabIndex={action.disabled ? -1 : 0}
        aria-label={`${action.title}${action.description ? ` - ${action.description}` : ''}`}
        aria-disabled={action.disabled}
      >
        {/* Status indicator */}
        <div className={combineStyles(
          'quick-action-status',
          action.disabled ? 'disabled' : 'available'
        )} />
        
        {/* Badge */}
        {action.badge && !action.disabled && (
          <Badge
            variant="secondary"
            className={combineStyles(
              'absolute -top-2 -right-2 h-5 px-2 text-xs font-medium',
              'bg-primary text-primary-foreground shadow-sm',
              'quick-action-badge z-10'
            )}
          >
            {action.badge}
          </Badge>
        )}
        
        {/* Icon */}
        <div className={combineStyles(
          'relative z-10 flex items-center justify-center',
          'w-8 h-8 mb-1'
        )}>
          <IconComponent
            className={combineStyles(
              iconStyles.lg,
              theme.icon,
              'quick-action-icon drop-shadow-sm'
            )}
          />
        </div>
        
        {/* Title */}
        <span className={combineStyles(
          typography.button,
          'relative z-10 text-center font-medium leading-tight',
          'quick-action-text'
        )}>
          {action.title}
        </span>
        
        {/* Description (if provided) */}
        {action.description && (
          <span className={combineStyles(
            'text-xs text-muted-foreground mt-1 text-center leading-tight',
            'transition-colors duration-300 opacity-0 group-hover:opacity-100',
            'transform translate-y-1 group-hover:translate-y-0'
          )}>
            {action.description}
          </span>
        )}
        
        {/* Hover gradient overlay */}
        <div className={combineStyles(
          'absolute inset-0 rounded-xl opacity-0 transition-opacity duration-300',
          'bg-gradient-to-br from-white/5 to-transparent',
          'group-hover:opacity-100 pointer-events-none'
        )} />
        
        {/* Focus indicator */}
        <div className={combineStyles(
          'absolute inset-0 rounded-xl border-2 border-transparent',
          'transition-colors duration-200',
          'focus-within:border-primary focus-within:shadow-lg focus-within:shadow-primary/20'
        )} />
      </div>
    </motion.div>
  );

  if (action.disabled) {
    return cardContent;
  }

  return (
    <Link href={action.href} className="block">
      {cardContent}
    </Link>
  );
}

// 专门的快速操作网格容器
export function QuickActionGrid({
  children,
  className
}: {
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <div className={combineStyles(
      'quick-action-grid',
      className
    )}>
      {children}
    </div>
  );
}

// 快速操作部分标题
export function QuickActionSection({ 
  title, 
  children, 
  className 
}: { 
  title: string; 
  children: React.ReactNode; 
  className?: string; 
}) {
  return (
    <div className={combineStyles('space-y-3', className)}>
      <h3 className={combineStyles(
        typography.h4,
        'text-muted-foreground font-medium tracking-wide'
      )}>
        {title}
      </h3>
      {children}
    </div>
  );
}

QuickActionCard.displayName = 'QuickActionCard';
QuickActionGrid.displayName = 'QuickActionGrid';
QuickActionSection.displayName = 'QuickActionSection';
