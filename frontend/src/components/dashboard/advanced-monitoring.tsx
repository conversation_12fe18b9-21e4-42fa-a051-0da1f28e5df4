"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Cpu, 
  MemoryStick,
  Zap,
  TrendingUp,
  TrendingDown,
  Wifi,
  WifiOff,
  Bell,
  BellOff,
  Settings,
  RefreshCw,
  Eye,
  EyeOff
} from 'lucide-react';
import { 
  LineChart,
  Line,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';
import { 
  useMonitoringData, 
  useAgentHealth, 
  useAlertManagement,
  useRealTimeMonitoring,
  AgentHealthMetrics,
  AgentAlert,
  getHealthStatus,
  analyzePerformanceTrends
} from '@/lib/monitoring-system';

interface AdvancedMonitoringProps {
  className?: string;
  agentId?: string;
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'healthy': return <CheckCircle className="h-4 w-4 text-green-500" />;
    case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    case 'critical': return <AlertTriangle className="h-4 w-4 text-red-500" />;
    case 'offline': return <WifiOff className="h-4 w-4 text-gray-500" />;
    default: return <Activity className="h-4 w-4 text-gray-500" />;
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'healthy': return 'border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-950/20';
    case 'warning': return 'border-yellow-200 dark:border-yellow-800 bg-yellow-50 dark:bg-yellow-950/20';
    case 'critical': return 'border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950/20';
    case 'offline': return 'border-gray-200 dark:border-gray-800 bg-gray-50 dark:bg-gray-950/20';
    default: return '';
  }
};

function AgentHealthCard({ health }: { health: AgentHealthMetrics }) {
  const [isExpanded, setIsExpanded] = useState(false);
  const healthAnalysis = getHealthStatus(health);

  return (
    <motion.div
      layout
      className={cn(
        'p-4 rounded-lg border transition-all duration-200 hover:shadow-md cursor-pointer',
        getStatusColor(health.status)
      )}
      onClick={() => setIsExpanded(!isExpanded)}
    >
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-3">
          {getStatusIcon(health.status)}
          <div>
            <h4 className="font-medium text-sm">{health.agentName}</h4>
            <p className="text-xs text-muted-foreground">
              健康评分: {healthAnalysis.score}/100
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Badge variant={health.status === 'healthy' ? 'default' : 'destructive'}>
            {health.status === 'healthy' ? '正常' : 
             health.status === 'warning' ? '警告' : 
             health.status === 'critical' ? '严重' : '离线'}
          </Badge>
          {health.alerts.length > 0 && (
            <Badge variant="destructive" className="text-xs">
              {health.alerts.length} 警报
            </Badge>
          )}
        </div>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-3">
        <div className="text-center">
          <div className="text-xs text-muted-foreground">响应时间</div>
          <div className="font-medium text-sm">{health.responseTime.current.toFixed(0)}ms</div>
        </div>
        <div className="text-center">
          <div className="text-xs text-muted-foreground">成功率</div>
          <div className="font-medium text-sm">{health.successRate.current.toFixed(1)}%</div>
        </div>
        <div className="text-center">
          <div className="text-xs text-muted-foreground">吞吐量</div>
          <div className="font-medium text-sm">{health.throughput.requestsPerMinute.toFixed(0)}/min</div>
        </div>
        <div className="text-center">
          <div className="text-xs text-muted-foreground">可用性</div>
          <div className="font-medium text-sm">{health.uptime.toFixed(1)}%</div>
        </div>
      </div>

      {isExpanded && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="space-y-4 pt-4 border-t border-border/50"
        >
          {/* Resource Usage */}
          <div>
            <h5 className="text-sm font-medium mb-2">资源使用情况</h5>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Cpu className="h-3 w-3" />
                  <span className="text-xs">CPU</span>
                </div>
                <span className="text-xs font-medium">{health.resourceUsage.cpu.toFixed(1)}%</span>
              </div>
              <Progress value={health.resourceUsage.cpu} className="h-1" />
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <MemoryStick className="h-3 w-3" />
                  <span className="text-xs">内存</span>
                </div>
                <span className="text-xs font-medium">{health.resourceUsage.memory.toFixed(1)}%</span>
              </div>
              <Progress value={health.resourceUsage.memory} className="h-1" />
            </div>
          </div>

          {/* Performance Metrics */}
          <div>
            <h5 className="text-sm font-medium mb-2">性能指标</h5>
            <div className="grid grid-cols-2 gap-3 text-xs">
              <div>
                <span className="text-muted-foreground">P95响应时间:</span>
                <span className="font-medium ml-1">{health.responseTime.p95.toFixed(0)}ms</span>
              </div>
              <div>
                <span className="text-muted-foreground">错误率:</span>
                <span className="font-medium ml-1">{health.errorRate.toFixed(2)}%</span>
              </div>
              <div>
                <span className="text-muted-foreground">Token使用:</span>
                <span className="font-medium ml-1">{health.resourceUsage.tokens.toLocaleString()}</span>
              </div>
              <div>
                <span className="text-muted-foreground">最后检查:</span>
                <span className="font-medium ml-1">
                  {new Date(health.lastHealthCheck).toLocaleTimeString('zh-CN')}
                </span>
              </div>
            </div>
          </div>

          {/* Health Issues */}
          {healthAnalysis.issues.length > 0 && (
            <div>
              <h5 className="text-sm font-medium mb-2">发现的问题</h5>
              <div className="space-y-1">
                {healthAnalysis.issues.map((issue, index) => (
                  <div key={index} className="flex items-center gap-2 text-xs">
                    <AlertTriangle className="h-3 w-3 text-yellow-500" />
                    <span>{issue}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Active Alerts */}
          {health.alerts.length > 0 && (
            <div>
              <h5 className="text-sm font-medium mb-2">活跃警报</h5>
              <div className="space-y-2">
                {health.alerts.map((alert) => (
                  <AlertCard key={alert.id} alert={alert} compact />
                ))}
              </div>
            </div>
          )}
        </motion.div>
      )}
    </motion.div>
  );
}

function AlertCard({ alert, compact = false }: { alert: AgentAlert; compact?: boolean }) {
  const { acknowledgeAlert, resolveAlert } = useAlertManagement();

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'border-red-500 bg-red-50 dark:bg-red-950/20';
      case 'high': return 'border-orange-500 bg-orange-50 dark:bg-orange-950/20';
      case 'medium': return 'border-yellow-500 bg-yellow-50 dark:bg-yellow-950/20';
      case 'low': return 'border-blue-500 bg-blue-50 dark:bg-blue-950/20';
      default: return '';
    }
  };

  return (
    <div className={cn(
      'p-3 rounded-lg border-l-4',
      getSeverityColor(alert.severity),
      compact && 'p-2'
    )}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <h5 className={cn('font-medium', compact ? 'text-xs' : 'text-sm')}>
              {alert.title}
            </h5>
            <Badge variant="outline" className="text-xs">
              {alert.severity === 'critical' ? '严重' : 
               alert.severity === 'high' ? '高' : 
               alert.severity === 'medium' ? '中' : '低'}
            </Badge>
          </div>
          <p className={cn('text-muted-foreground', compact ? 'text-xs' : 'text-sm')}>
            {alert.message}
          </p>
          {!compact && (
            <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
              <span>阈值: {alert.threshold}</span>
              <span>当前值: {alert.currentValue}</span>
              <span>触发时间: {new Date(alert.triggeredAt).toLocaleTimeString('zh-CN')}</span>
            </div>
          )}
        </div>
        
        {!compact && (
          <div className="flex items-center gap-1 ml-2">
            {!alert.acknowledged && (
              <Button
                size="sm"
                variant="outline"
                onClick={() => acknowledgeAlert(alert.id)}
                className="text-xs h-6"
              >
                <Eye className="h-3 w-3" />
              </Button>
            )}
            <Button
              size="sm"
              variant="outline"
              onClick={() => resolveAlert(alert.id)}
              className="text-xs h-6"
            >
              <CheckCircle className="h-3 w-3" />
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}

function PerformanceTrendsChart({ trends }: { trends: any[] }) {
  const analysis = analyzePerformanceTrends(trends);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-sm flex items-center gap-2">
          <TrendingUp className="h-4 w-4" />
          性能趋势 (24小时)
        </CardTitle>
        {analysis && (
          <CardDescription className="text-xs">
            响应时间变化: {analysis.responseTimeChange > 0 ? '+' : ''}{analysis.responseTimeChange.toFixed(1)}%
            {' | '}
            成功率变化: {analysis.successRateChange > 0 ? '+' : ''}{analysis.successRateChange.toFixed(1)}%
          </CardDescription>
        )}
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={200}>
          <AreaChart data={trends}>
            <defs>
              <linearGradient id="responseTimeGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.3} />
                <stop offset="95%" stopColor="#3b82f6" stopOpacity={0} />
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
            <XAxis 
              dataKey="timestamp" 
              className="text-xs"
              tickFormatter={(value) => new Date(value).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}
            />
            <YAxis className="text-xs" />
            <Tooltip 
              labelFormatter={(value) => new Date(value).toLocaleString('zh-CN')}
              formatter={(value: number, name: string) => {
                if (name === 'responseTime') return [`${value.toFixed(0)}ms`, '响应时间'];
                if (name === 'successRate') return [`${value.toFixed(1)}%`, '成功率'];
                if (name === 'throughput') return [`${value.toFixed(0)}/min`, '吞吐量'];
                return [value, name];
              }}
            />
            <Area
              type="monotone"
              dataKey="responseTime"
              stroke="#3b82f6"
              fillOpacity={1}
              fill="url(#responseTimeGradient)"
            />
            <Line
              type="monotone"
              dataKey="successRate"
              stroke="#10b981"
              strokeWidth={2}
              dot={false}
            />
          </AreaChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}

export function AdvancedMonitoring({ className, agentId }: AdvancedMonitoringProps) {
  const { data: monitoringData, isLoading, refetch } = useMonitoringData();
  const agentHealth = useAgentHealth(agentId);
  const { isConnected } = useRealTimeMonitoring();

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Activity className="h-5 w-5 animate-pulse" />
            <CardTitle>实时监控</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="h-20 bg-muted animate-pulse rounded-lg" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const systemMetrics = monitoringData?.systemMetrics;
  const allAgentHealth = monitoringData?.agentHealth || [];
  const activeAlerts = monitoringData?.activeAlerts || [];
  const performanceTrends = monitoringData?.performanceTrends || [];

  return (
    <Card className={cn('relative overflow-hidden', className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Activity className="h-5 w-5 text-blue-600" />
            <CardTitle>实时监控</CardTitle>
            <div className="flex items-center gap-1">
              {isConnected ? (
                <Wifi className="h-4 w-4 text-green-500" />
              ) : (
                <WifiOff className="h-4 w-4 text-red-500" />
              )}
              <span className="text-xs text-muted-foreground">
                {isConnected ? '已连接' : '连接中断'}
              </span>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-xs">
              {activeAlerts.length} 活跃警报
            </Badge>
            <Button
              size="sm"
              variant="outline"
              onClick={() => refetch()}
              className="h-8"
            >
              <RefreshCw className="h-3 w-3" />
            </Button>
          </div>
        </div>
        <CardDescription>
          Agent健康状态监控和性能分析
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview" className="text-xs">
              总览
            </TabsTrigger>
            <TabsTrigger value="agents" className="text-xs">
              Agent状态 ({allAgentHealth.length})
            </TabsTrigger>
            <TabsTrigger value="alerts" className="text-xs">
              警报 ({activeAlerts.length})
            </TabsTrigger>
            <TabsTrigger value="trends" className="text-xs">
              趋势分析
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {systemMetrics && (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-3 rounded-lg border">
                  <div className="text-2xl font-bold text-green-600">{systemMetrics.healthyAgents}</div>
                  <div className="text-xs text-muted-foreground">健康Agent</div>
                </div>
                <div className="text-center p-3 rounded-lg border">
                  <div className="text-2xl font-bold text-yellow-600">{systemMetrics.warningAgents}</div>
                  <div className="text-xs text-muted-foreground">警告Agent</div>
                </div>
                <div className="text-center p-3 rounded-lg border">
                  <div className="text-2xl font-bold text-red-600">{systemMetrics.criticalAgents}</div>
                  <div className="text-xs text-muted-foreground">严重Agent</div>
                </div>
                <div className="text-center p-3 rounded-lg border">
                  <div className="text-2xl font-bold">{systemMetrics.averageResponseTime.toFixed(0)}ms</div>
                  <div className="text-xs text-muted-foreground">平均响应时间</div>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="agents" className="space-y-3">
            <div className="space-y-3">
              {agentId && agentHealth ? (
                <AgentHealthCard health={agentHealth} />
              ) : (
                allAgentHealth.map((health) => (
                  <AgentHealthCard key={health.agentId} health={health} />
                ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="alerts" className="space-y-3">
            {activeAlerts.length > 0 ? (
              <div className="space-y-3">
                {activeAlerts.map((alert) => (
                  <AlertCard key={alert.id} alert={alert} />
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-foreground mb-2">
                  暂无活跃警报
                </h3>
                <p className="text-sm text-muted-foreground">
                  所有系统运行正常
                </p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="trends" className="space-y-4">
            <PerformanceTrendsChart trends={performanceTrends} />
          </TabsContent>
        </Tabs>
      </CardContent>

      {/* Animated background gradient */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-500/5 to-transparent -translate-x-full animate-shimmer" />
    </Card>
  );
}

export default AdvancedMonitoring;
