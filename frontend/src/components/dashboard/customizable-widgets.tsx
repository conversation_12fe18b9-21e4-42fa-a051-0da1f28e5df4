"use client";

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { cn } from '@/lib/utils';
import { motion, Reorder } from 'framer-motion';
import { 
  Settings, 
  GripVertical, 
  Eye, 
  EyeOff, 
  Plus,
  Trash2,
  BarChart3,
  PieChart,
  LineChart,
  Activity,
  Users,
  Clock,
  Zap,
  TrendingUp
} from 'lucide-react';

export interface WidgetConfig {
  id: string;
  type: 'stats' | 'chart' | 'activity' | 'custom';
  title: string;
  description?: string;
  visible: boolean;
  position: number;
  size: 'sm' | 'md' | 'lg' | 'xl';
  settings: Record<string, any>;
}

interface CustomizableWidgetsProps {
  widgets: WidgetConfig[];
  onWidgetsChange: (widgets: WidgetConfig[]) => void;
  className?: string;
}

const widgetTypes = [
  {
    type: 'stats',
    name: '统计卡片',
    icon: BarChart3,
    description: '显示关键指标和数据',
    defaultSettings: {
      metric: 'agents',
      showTrend: true,
      showChange: true,
    },
  },
  {
    type: 'chart',
    name: '图表',
    icon: LineChart,
    description: '数据可视化图表',
    defaultSettings: {
      chartType: 'line',
      timeRange: 'week',
      dataSource: 'usage',
    },
  },
  {
    type: 'activity',
    name: '活动列表',
    icon: Activity,
    description: '最近的系统活动',
    defaultSettings: {
      maxItems: 5,
      showTimestamp: true,
      filterType: 'all',
    },
  },
];

const sizeOptions = [
  { value: 'sm', label: '小', cols: 1 },
  { value: 'md', label: '中', cols: 2 },
  { value: 'lg', label: '大', cols: 3 },
  { value: 'xl', label: '特大', cols: 4 },
];

const metricOptions = [
  { value: 'agents', label: '活跃 Agents', icon: Users },
  { value: 'requests', label: '请求数量', icon: Activity },
  { value: 'success_rate', label: '成功率', icon: Zap },
  { value: 'response_time', label: '响应时间', icon: Clock },
];

const chartTypeOptions = [
  { value: 'line', label: '折线图', icon: LineChart },
  { value: 'bar', label: '柱状图', icon: BarChart3 },
  { value: 'pie', label: '饼图', icon: PieChart },
  { value: 'area', label: '面积图', icon: TrendingUp },
];

function WidgetConfigDialog({ 
  widget, 
  onSave, 
  onCancel 
}: { 
  widget: WidgetConfig | null; 
  onSave: (widget: WidgetConfig) => void;
  onCancel: () => void;
}) {
  const [config, setConfig] = useState<WidgetConfig>(
    widget || {
      id: `widget_${Date.now()}`,
      type: 'stats',
      title: '新建组件',
      visible: true,
      position: 0,
      size: 'md',
      settings: {},
    }
  );

  const handleSave = () => {
    onSave(config);
  };

  const updateSettings = (key: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      settings: { ...prev.settings, [key]: value },
    }));
  };

  const selectedWidgetType = widgetTypes.find(t => t.type === config.type);

  return (
    <DialogContent className="max-w-md">
      <DialogHeader>
        <DialogTitle>
          {widget ? '编辑组件' : '添加组件'}
        </DialogTitle>
        <DialogDescription>
          配置仪表板组件的显示和行为
        </DialogDescription>
      </DialogHeader>

      <div className="space-y-4">
        {/* Widget Type */}
        <div className="space-y-2">
          <label className="text-sm font-medium">组件类型</label>
          <Select
            value={config.type}
            onValueChange={(value) => setConfig(prev => ({ 
              ...prev, 
              type: value as WidgetConfig['type'],
              settings: widgetTypes.find(t => t.type === value)?.defaultSettings || {},
            }))}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {widgetTypes.map((type) => (
                <SelectItem key={type.type} value={type.type}>
                  <div className="flex items-center gap-2">
                    <type.icon className="h-4 w-4" />
                    <div>
                      <div className="font-medium">{type.name}</div>
                      <div className="text-xs text-muted-foreground">{type.description}</div>
                    </div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Title */}
        <div className="space-y-2">
          <label className="text-sm font-medium">标题</label>
          <input
            type="text"
            value={config.title}
            onChange={(e) => setConfig(prev => ({ ...prev, title: e.target.value }))}
            className="w-full px-3 py-2 border rounded-md text-sm"
            placeholder="输入组件标题"
          />
        </div>

        {/* Size */}
        <div className="space-y-2">
          <label className="text-sm font-medium">尺寸</label>
          <Select
            value={config.size}
            onValueChange={(value) => setConfig(prev => ({ ...prev, size: value as WidgetConfig['size'] }))}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {sizeOptions.map((size) => (
                <SelectItem key={size.value} value={size.value}>
                  {size.label} ({size.cols} 列)
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Type-specific settings */}
        {config.type === 'stats' && (
          <div className="space-y-2">
            <label className="text-sm font-medium">指标类型</label>
            <Select
              value={config.settings.metric || 'agents'}
              onValueChange={(value) => updateSettings('metric', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {metricOptions.map((metric) => (
                  <SelectItem key={metric.value} value={metric.value}>
                    <div className="flex items-center gap-2">
                      <metric.icon className="h-4 w-4" />
                      {metric.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {config.type === 'chart' && (
          <div className="space-y-2">
            <label className="text-sm font-medium">图表类型</label>
            <Select
              value={config.settings.chartType || 'line'}
              onValueChange={(value) => updateSettings('chartType', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {chartTypeOptions.map((chart) => (
                  <SelectItem key={chart.value} value={chart.value}>
                    <div className="flex items-center gap-2">
                      <chart.icon className="h-4 w-4" />
                      {chart.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Visibility */}
        <div className="flex items-center justify-between">
          <label className="text-sm font-medium">显示组件</label>
          <Switch
            checked={config.visible}
            onCheckedChange={(checked) => setConfig(prev => ({ ...prev, visible: checked }))}
          />
        </div>
      </div>

      <div className="flex justify-end gap-2 pt-4">
        <Button variant="outline" onClick={onCancel}>
          取消
        </Button>
        <Button onClick={handleSave}>
          保存
        </Button>
      </div>
    </DialogContent>
  );
}

function WidgetItem({ 
  widget, 
  onEdit, 
  onToggleVisibility, 
  onDelete 
}: { 
  widget: WidgetConfig;
  onEdit: (widget: WidgetConfig) => void;
  onToggleVisibility: (id: string) => void;
  onDelete: (id: string) => void;
}) {
  const widgetType = widgetTypes.find(t => t.type === widget.type);
  const Icon = widgetType?.icon || Activity;

  return (
    <motion.div
      layout
      className={cn(
        'flex items-center justify-between p-3 rounded-lg border bg-card',
        !widget.visible && 'opacity-50'
      )}
    >
      <div className="flex items-center gap-3">
        <GripVertical className="h-4 w-4 text-muted-foreground cursor-grab" />
        <Icon className="h-4 w-4" />
        <div>
          <div className="font-medium text-sm">{widget.title}</div>
          <div className="text-xs text-muted-foreground">
            {widgetType?.name} • {sizeOptions.find(s => s.value === widget.size)?.label}
          </div>
        </div>
      </div>

      <div className="flex items-center gap-1">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onToggleVisibility(widget.id)}
          className="h-8 w-8 p-0"
        >
          {widget.visible ? <Eye className="h-3 w-3" /> : <EyeOff className="h-3 w-3" />}
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onEdit(widget)}
          className="h-8 w-8 p-0"
        >
          <Settings className="h-3 w-3" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onDelete(widget.id)}
          className="h-8 w-8 p-0 text-destructive hover:text-destructive"
        >
          <Trash2 className="h-3 w-3" />
        </Button>
      </div>
    </motion.div>
  );
}

export function CustomizableWidgets({
  widgets,
  onWidgetsChange,
  className,
}: CustomizableWidgetsProps) {
  const [editingWidget, setEditingWidget] = useState<WidgetConfig | null>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);

  const handleReorder = useCallback((newOrder: WidgetConfig[]) => {
    const reorderedWidgets = newOrder.map((widget, index) => ({
      ...widget,
      position: index,
    }));
    onWidgetsChange(reorderedWidgets);
  }, [onWidgetsChange]);

  const handleToggleVisibility = useCallback((id: string) => {
    const updatedWidgets = widgets.map(widget =>
      widget.id === id ? { ...widget, visible: !widget.visible } : widget
    );
    onWidgetsChange(updatedWidgets);
  }, [widgets, onWidgetsChange]);

  const handleDelete = useCallback((id: string) => {
    const updatedWidgets = widgets.filter(widget => widget.id !== id);
    onWidgetsChange(updatedWidgets);
  }, [widgets, onWidgetsChange]);

  const handleSaveWidget = useCallback((widget: WidgetConfig) => {
    const existingIndex = widgets.findIndex(w => w.id === widget.id);
    if (existingIndex >= 0) {
      // Update existing widget
      const updatedWidgets = [...widgets];
      updatedWidgets[existingIndex] = widget;
      onWidgetsChange(updatedWidgets);
    } else {
      // Add new widget
      const newWidget = { ...widget, position: widgets.length };
      onWidgetsChange([...widgets, newWidget]);
    }
    setEditingWidget(null);
    setIsAddDialogOpen(false);
  }, [widgets, onWidgetsChange]);

  const visibleWidgets = widgets.filter(w => w.visible);
  const hiddenWidgets = widgets.filter(w => !w.visible);

  return (
    <Card className={cn('relative overflow-hidden', className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              自定义组件
            </CardTitle>
            <CardDescription>
              管理仪表板组件的显示和布局
            </CardDescription>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-1" />
                添加组件
              </Button>
            </DialogTrigger>
            <WidgetConfigDialog
              widget={null}
              onSave={handleSaveWidget}
              onCancel={() => setIsAddDialogOpen(false)}
            />
          </Dialog>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Visible Widgets */}
        {visibleWidgets.length > 0 && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium">显示的组件</h3>
              <Badge variant="secondary">{visibleWidgets.length}</Badge>
            </div>
            <Reorder.Group
              axis="y"
              values={visibleWidgets}
              onReorder={handleReorder}
              className="space-y-2"
            >
              {visibleWidgets.map((widget) => (
                <Reorder.Item key={widget.id} value={widget}>
                  <WidgetItem
                    widget={widget}
                    onEdit={setEditingWidget}
                    onToggleVisibility={handleToggleVisibility}
                    onDelete={handleDelete}
                  />
                </Reorder.Item>
              ))}
            </Reorder.Group>
          </div>
        )}

        {/* Hidden Widgets */}
        {hiddenWidgets.length > 0 && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium">隐藏的组件</h3>
              <Badge variant="outline">{hiddenWidgets.length}</Badge>
            </div>
            <div className="space-y-2">
              {hiddenWidgets.map((widget) => (
                <WidgetItem
                  key={widget.id}
                  widget={widget}
                  onEdit={setEditingWidget}
                  onToggleVisibility={handleToggleVisibility}
                  onDelete={handleDelete}
                />
              ))}
            </div>
          </div>
        )}

        {widgets.length === 0 && (
          <div className="text-center py-8">
            <Settings className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">
              暂无自定义组件
            </h3>
            <p className="text-sm text-muted-foreground mb-4">
              添加组件来个性化你的仪表板
            </p>
            <Button onClick={() => setIsAddDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-1" />
              添加第一个组件
            </Button>
          </div>
        )}
      </CardContent>

      {/* Edit Dialog */}
      {editingWidget && (
        <Dialog open={!!editingWidget} onOpenChange={() => setEditingWidget(null)}>
          <WidgetConfigDialog
            widget={editingWidget}
            onSave={handleSaveWidget}
            onCancel={() => setEditingWidget(null)}
          />
        </Dialog>
      )}

      {/* Animated background gradient */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full animate-shimmer" />
    </Card>
  );
}

export default CustomizableWidgets;
