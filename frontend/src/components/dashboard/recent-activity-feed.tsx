"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { motion } from 'framer-motion';
import { useRecentActivity } from '@/lib/dashboard-data';
import { Skeleton } from '@/components/ui/skeleton';
import { Progress } from '@/components/ui/progress';
import { 
  Activity, 
  ArrowRight,
  Play,
  Plus,
  Settings,
  Template,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Info
} from 'lucide-react';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

interface RecentActivityFeedProps {
  className?: string;
}

const activityIcons = {
  test_execution: Play,
  agent_created: Plus,
  agent_updated: Settings,
  template_used: Template,
};

const statusIcons = {
  success: CheckCircle,
  error: XCircle,
  warning: AlertTriangle,
  info: Info,
};

const statusColors = {
  success: 'text-green-600 bg-green-50 border-green-200',
  error: 'text-red-600 bg-red-50 border-red-200',
  warning: 'text-yellow-600 bg-yellow-50 border-yellow-200',
  info: 'text-blue-600 bg-blue-50 border-blue-200',
};

export function RecentActivityFeed({ className }: RecentActivityFeedProps) {
  const { data: activities, isLoading, error, refetch } = useRecentActivity();

  // Auto-refresh activities every 30 seconds
  React.useEffect(() => {
    const interval = setInterval(() => {
      refetch();
    }, 30000);

    return () => clearInterval(interval);
  }, []); // Remove refetch from dependencies to prevent constant re-creation

  if (error) {
    return (
      <Card className={cn('border-red-200 bg-red-50/50', className)}>
        <CardHeader className="p-3">
          <CardTitle className="text-sm text-red-600">最近活动</CardTitle>
          <CardDescription className="text-xs text-red-500">
            加载失败，请稍后重试
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card className={cn('', className)}>
      <CardHeader className="p-3 pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="relative">
              <Activity className="h-4 w-4 text-muted-foreground" />
              {/* Real-time indicator */}
              <div className="absolute -top-1 -right-1 h-2 w-2 bg-green-500 rounded-full animate-pulse" />
            </div>
            <CardTitle className="text-sm font-medium">最近活动</CardTitle>
            <Badge variant="outline" className="text-[10px] px-1 h-4 bg-green-50 text-green-600 border-green-200">
              实时
            </Badge>
          </div>
          <Link href="/test-history">
            <Button variant="ghost" size="sm" className="h-6 px-2 text-xs">
              查看全部
              <ArrowRight className="h-3 w-3 ml-1" />
            </Button>
          </Link>
        </div>
        <CardDescription className="text-xs">
          实时活动动态
        </CardDescription>
      </CardHeader>
      <CardContent className="p-3 pt-0">
        {isLoading ? (
          <div className="space-y-3">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="flex items-start gap-3">
                <Skeleton className="h-6 w-6 rounded-full mt-0.5" />
                <div className="flex-1 space-y-1">
                  <Skeleton className="h-3 w-32" />
                  <Skeleton className="h-2 w-24" />
                  <Skeleton className="h-2 w-16" />
                </div>
              </div>
            ))}
          </div>
        ) : activities && activities.length > 0 ? (
          <div className="space-y-3">
            {activities.slice(0, 5).map((activity, index) => {
              const ActivityIcon = activityIcons[activity.type] || Activity;
              const StatusIcon = statusIcons[activity.status] || Info;
              
              return (
                <motion.div
                  key={activity.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.2, delay: index * 0.05 }}
                  className="flex items-start gap-3 group"
                >
                  <div className={cn(
                    'h-6 w-6 rounded-full flex items-center justify-center border',
                    statusColors[activity.status]
                  )}>
                    <StatusIcon className="h-3 w-3" />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="text-xs font-medium truncate">
                        {activity.title}
                      </h4>
                      {activity.duration && (
                        <Badge variant="outline" className="text-[10px] px-1 h-4">
                          <Clock className="h-2 w-2 mr-1" />
                          {Math.round(activity.duration / 1000)}s
                        </Badge>
                      )}
                    </div>

                    <p className="text-[10px] text-muted-foreground truncate mb-2">
                      {activity.description}
                    </p>

                    {/* Progress indicator for test executions */}
                    {activity.type === 'test_execution' && activity.duration && (
                      <div className="mb-2">
                        <div className="flex items-center justify-between text-[10px] text-muted-foreground mb-1">
                          <span>执行进度</span>
                          <span>{activity.status === 'success' ? '100%' : '失败'}</span>
                        </div>
                        <Progress
                          value={activity.status === 'success' ? 100 : 0}
                          className="h-1"
                        />
                      </div>
                    )}

                    <div className="flex items-center gap-2">
                      <span className="text-[10px] text-muted-foreground">
                        {formatDistanceToNow(new Date(activity.timestamp), {
                          addSuffix: true,
                          locale: zhCN
                        })}
                      </span>

                      {activity.agentName && (
                        <Badge variant="secondary" className="text-[10px] px-1 h-4">
                          {activity.agentName}
                        </Badge>
                      )}

                      {/* Performance indicator */}
                      {activity.duration && activity.duration < 2000 && (
                        <Badge variant="outline" className="text-[10px] px-1 h-4 bg-green-50 text-green-600 border-green-200">
                          快速
                        </Badge>
                      )}
                    </div>
                  </div>
                  
                  {activity.agentId && (
                    <Link href={`/agents/${activity.agentId}`}>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <ArrowRight className="h-3 w-3" />
                      </Button>
                    </Link>
                  )}
                </motion.div>
              );
            })}
            
            {activities.length === 0 && (
              <div className="text-center py-4">
                <Activity className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-xs text-muted-foreground">暂无活动记录</p>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-4">
            <Activity className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-xs text-muted-foreground">暂无活动记录</p>
            <Link href="/agents/create">
              <Button variant="outline" size="sm" className="mt-2 h-7 text-xs">
                创建第一个 Agent
              </Button>
            </Link>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

RecentActivityFeed.displayName = 'RecentActivityFeed';
