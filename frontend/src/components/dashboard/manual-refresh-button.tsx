"use client";

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { RefreshCw, CheckCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ManualRefreshButtonProps {
  onRefresh: () => Promise<boolean | void>;
  isRefreshing?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'ghost' | 'outline' | 'default';
  className?: string;
  showSuccessIndicator?: boolean;
  tooltipText?: string;
}

export function ManualRefreshButton({
  onRefresh,
  isRefreshing = false,
  size = 'sm',
  variant = 'ghost',
  className,
  showSuccessIndicator = true,
  tooltipText = '手动刷新数据'
}: ManualRefreshButtonProps) {
  const [showSuccess, setShowSuccess] = useState(false);

  const handleRefresh = async () => {
    if (isRefreshing) return;

    try {
      const result = await onRefresh();
      
      if (showSuccessIndicator && result !== false) {
        setShowSuccess(true);
        setTimeout(() => setShowSuccess(false), 2000);
      }
    } catch (error) {
      console.error('Refresh failed:', error);
    }
  };

  const sizeClasses = {
    sm: 'h-8 w-8 p-0',
    md: 'h-10 w-10 p-0',
    lg: 'h-12 w-12 p-0'
  };

  const iconSizes = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant={variant}
            className={cn(
              sizeClasses[size],
              'touch-manipulation transition-all duration-200',
              'hover:scale-105 active:scale-95',
              className
            )}
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            {showSuccess ? (
              <CheckCircle className={cn(iconSizes[size], 'text-green-600')} />
            ) : (
              <RefreshCw 
                className={cn(
                  iconSizes[size],
                  isRefreshing && 'animate-spin',
                  'transition-transform duration-200'
                )} 
              />
            )}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p className="text-xs">
            {isRefreshing ? '正在刷新...' : showSuccess ? '刷新成功!' : tooltipText}
          </p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

ManualRefreshButton.displayName = 'ManualRefreshButton';
