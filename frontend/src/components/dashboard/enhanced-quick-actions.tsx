"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { motion } from 'framer-motion';
import { useQuickActions } from '@/lib/dashboard-data';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Plus,
  Play,
  LayoutTemplate,
  History,
  Zap,
  Sparkles,
  Settings,
  BarChart3
} from 'lucide-react';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import {
  cardStyles,
  typography,
  spacing,
  buttonStyles,
  iconStyles,
  animations,
  combineStyles
} from '@/lib/dashboard-styles';
import {
  QuickActionCard,
  QuickActionGrid
} from './quick-action-card';

interface EnhancedQuickActionsProps {
  className?: string;
}

const iconMap = {
  plus: Plus,
  play: Play,
  template: LayoutTemplate,
  history: History,
  zap: Zap,
  sparkles: Sparkles,
  settings: Settings,
  chart: BarChart3,
};

// Extended quick actions with more options
const extendedActions = [
  {
    id: 'manage-settings',
    title: '系统设置',
    description: '管理系统配置',
    icon: 'settings',
    href: '/settings',
    color: 'primary' as const,
  },
];

export function EnhancedQuickActions({ className }: EnhancedQuickActionsProps) {
  const { data: quickActions, isLoading, error } = useQuickActions();

  if (isLoading) {
    return (
      <Card className={cn('', className)}>
        <CardHeader className="p-3 pb-2">
          <div className="flex items-center justify-between">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-6 w-16" />
          </div>
        </CardHeader>
        <CardContent className="p-3 pt-0">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {[1, 2, 3, 4].map((i) => (
              <Skeleton key={i} className="h-16 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={cn('border-red-200 bg-red-50/50', className)}>
        <CardHeader className="p-3">
          <CardTitle className="text-sm text-red-600">快速操作</CardTitle>
          <CardDescription className="text-xs text-red-500">
            加载失败，请稍后重试
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  // 合并所有操作，不区分主要和扩展
  const allActions = [...(quickActions || []), ...extendedActions];

  return (
    <Card className={combineStyles(
      cardStyles.elevated,
      animations.fadeIn,
      'gpu-accelerated performance-monitor respect-motion-preference',
      className
    )}>
      <CardHeader className={spacing.cardHeader}>
        <div className="flex items-center">
          <div className={combineStyles(spacing.inlineGap, 'flex items-center')}>
            <Sparkles className={combineStyles(iconStyles.sm, iconStyles.muted, animations.wiggle)} />
            <CardTitle className={typography.h4}>快速操作</CardTitle>
          </div>
        </div>
      </CardHeader>

      <CardContent className={spacing.cardContent}>
        {/* 统一的快速操作网格 */}
        <QuickActionGrid>
          {allActions.map((action, index) => {
            const IconComponent = iconMap[action.icon as keyof typeof iconMap] || Plus;

            return (
              <QuickActionCard
                key={action.id}
                action={action}
                index={index}
                IconComponent={IconComponent}
              />
            );
          })}
        </QuickActionGrid>
      </CardContent>
    </Card>
  );
}

EnhancedQuickActions.displayName = 'EnhancedQuickActions';
