/**
 * Integration tests for favorites functionality
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { FavoriteButton } from '@/components/ui/favorite-button';
import { useFavorites } from '@/hooks/use-favorites';
import { favoritesApi } from '@/lib/api/favorites';

// Mock the API
jest.mock('@/lib/api/favorites');
const mockFavoritesApi = favoritesApi as jest.Mocked<typeof favoritesApi>;

// Mock the toast hook
jest.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: jest.fn(),
  }),
}));

// Test wrapper with React Query
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('Favorites Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('FavoriteButton Component', () => {
    it('renders correctly with favorite state', () => {
      const mockOnToggle = jest.fn();

      render(
        <TestWrapper>
          <FavoriteButton
            agentId="test-agent-1"
            isFavorite={true}
            onToggle={mockOnToggle}
          />
        </TestWrapper>
      );

      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      expect(button).toHaveAttribute('aria-label', '取消收藏');
    });

    it('renders correctly with non-favorite state', () => {
      const mockOnToggle = jest.fn();

      render(
        <TestWrapper>
          <FavoriteButton
            agentId="test-agent-1"
            isFavorite={false}
            onToggle={mockOnToggle}
          />
        </TestWrapper>
      );

      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      expect(button).toHaveAttribute('aria-label', '添加到收藏');
    });

    it('calls onToggle when clicked', async () => {
      const mockOnToggle = jest.fn().mockResolvedValue({
        success: true,
        is_favorite: true,
        message: 'Added to favorites',
        agent_id: 'test-agent-1',
      });

      render(
        <TestWrapper>
          <FavoriteButton
            agentId="test-agent-1"
            isFavorite={false}
            onToggle={mockOnToggle}
          />
        </TestWrapper>
      );

      const button = screen.getByRole('button');
      fireEvent.click(button);

      await waitFor(() => {
        expect(mockOnToggle).toHaveBeenCalledWith('test-agent-1', false);
      });
    });

    it('shows loading state during toggle', async () => {
      const mockOnToggle = jest.fn().mockImplementation(
        () => new Promise(resolve => setTimeout(resolve, 100))
      );

      render(
        <TestWrapper>
          <FavoriteButton
            agentId="test-agent-1"
            isFavorite={false}
            onToggle={mockOnToggle}
          />
        </TestWrapper>
      );

      const button = screen.getByRole('button');
      fireEvent.click(button);

      // Should show loading spinner
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    });
  });

  describe('useFavorites Hook', () => {
    const TestComponent = () => {
      const { favorites, isLoading, toggleFavorite, isFavorite } = useFavorites();

      return (
        <div>
          <div data-testid="loading">{isLoading ? 'Loading' : 'Loaded'}</div>
          <div data-testid="favorites-count">{favorites.length}</div>
          <div data-testid="is-favorite-test">
            {isFavorite('test-agent-1') ? 'Is Favorite' : 'Not Favorite'}
          </div>
          <button
            data-testid="toggle-button"
            onClick={() => toggleFavorite('test-agent-1')}
          >
            Toggle
          </button>
        </div>
      );
    };

    it('fetches favorites on mount', async () => {
      const mockFavorites = [
        {
          agent_id: 'test-agent-1',
          name: 'Test Agent',
          description: 'Test Description',
          status: 'active',
          agent_type: 'single',
          created_at: '2024-01-01T00:00:00Z',
          usage_count: 5,
          favorite_id: 1,
          favorite_uuid: 'uuid-1',
          favorited_at: '2024-01-01T00:00:00Z',
        },
      ];

      mockFavoritesApi.getFavorites.mockResolvedValue(mockFavorites);

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('Loaded');
        expect(screen.getByTestId('favorites-count')).toHaveTextContent('1');
        expect(screen.getByTestId('is-favorite-test')).toHaveTextContent('Is Favorite');
      });

      expect(mockFavoritesApi.getFavorites).toHaveBeenCalledWith(true);
    });

    it('toggles favorite status', async () => {
      const mockFavorites = [];
      const mockToggleResponse = {
        success: true,
        is_favorite: true,
        message: 'Added to favorites',
        agent_id: 'test-agent-1',
        favorite_id: 1,
      };

      mockFavoritesApi.getFavorites.mockResolvedValue(mockFavorites);
      mockFavoritesApi.toggleFavorite.mockResolvedValue(mockToggleResponse);

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('Loaded');
      });

      const toggleButton = screen.getByTestId('toggle-button');
      fireEvent.click(toggleButton);

      await waitFor(() => {
        expect(mockFavoritesApi.toggleFavorite).toHaveBeenCalledWith('test-agent-1');
      });
    });
  });

  describe('API Integration', () => {
    it('handles API errors gracefully', async () => {
      const mockError = new Error('API Error');
      mockFavoritesApi.getFavorites.mockRejectedValue(mockError);

      const TestComponent = () => {
        const { error } = useFavorites();
        return <div data-testid="error">{error?.message || 'No Error'}</div>;
      };

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('API Error');
      });
    });

    it('handles toggle errors gracefully', async () => {
      const mockFavorites = [];
      const mockError = new Error('Toggle Error');

      mockFavoritesApi.getFavorites.mockResolvedValue(mockFavorites);
      mockFavoritesApi.toggleFavorite.mockRejectedValue(mockError);

      const TestComponent = () => {
        const { toggleFavorite } = useFavorites();
        const [error, setError] = React.useState<string | null>(null);

        const handleToggle = async () => {
          try {
            await toggleFavorite('test-agent-1');
          } catch (err) {
            setError(err instanceof Error ? err.message : 'Unknown error');
          }
        };

        return (
          <div>
            <button data-testid="toggle-button" onClick={handleToggle}>
              Toggle
            </button>
            <div data-testid="error">{error || 'No Error'}</div>
          </div>
        );
      };

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      const toggleButton = screen.getByTestId('toggle-button');
      fireEvent.click(toggleButton);

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('Toggle Error');
      });
    });
  });
});
