"use client";

import React, { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Edit,
  Star,
  Users,
  Calendar,
  Tag,
  TrendingUp,
  Code,
  History,
  Share,
  Download,
  Eye,
  GitBranch
} from "lucide-react";
import { Template, TemplateDifficulty, TemplateVisibility } from "@/lib/types";
import { TemplateShareDialog } from "./TemplateShareDialog";

interface TemplatePreviewProps {
  template: Template;
  isOpen: boolean;
  onClose: () => void;
  onEdit?: () => void;
  onCreateAgent?: () => void;
  onShare?: () => void;
  onViewVersions?: () => void;
  versions?: Template[];
}

const getDifficultyColor = (difficulty: TemplateDifficulty): string => {
  switch (difficulty) {
    case "beginner":
      return "bg-green-100 text-green-800 border-green-200";
    case "intermediate":
      return "bg-blue-100 text-blue-800 border-blue-200";
    case "advanced":
      return "bg-orange-100 text-orange-800 border-orange-200";
    case "expert":
      return "bg-red-100 text-red-800 border-red-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
};

const getVisibilityIcon = (visibility: string) => {
  switch (visibility) {
    case "public":
      return "🌐";
    case "featured":
      return "⭐";
    case "shared":
      return "👥";
    case "private":
    default:
      return "🔒";
  }
};

const getCategoryIcon = (category: string) => {
  const icons: Record<string, string> = {
    business: "💼",
    technical: "⚙️",
    creative: "🎨",
    analysis: "📊",
    support: "🛠️",
    education: "📚",
    investigation: "🔍",
    consulting: "💡",
    research: "🔬",
    customer_service: "📞",
    marketing: "📢",
    sales: "💰",
    healthcare: "🏥",
    finance: "💳",
    legal: "⚖️",
    other: "📋"
  };
  return icons[category] || "📋";
};

export const TemplatePreview: React.FC<TemplatePreviewProps> = ({
  template,
  isOpen,
  onClose,
  onEdit,
  onCreateAgent,
  onShare,
  onViewVersions,
  versions = []
}) => {
  const [activeTab, setActiveTab] = useState("overview");
  const [shareDialogOpen, setShareDialogOpen] = useState(false);
  const [currentTemplate, setCurrentTemplate] = useState(template);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    });
  };

  const handleShare = () => {
    if (currentTemplate.can_edit) {
      setShareDialogOpen(true);
    } else {
      // For non-editable templates, just copy the link
      const url = `${window.location.origin}/templates/${currentTemplate.template_id}`;
      navigator.clipboard.writeText(url).then(() => {
        // You might want to show a toast here
        console.log("Link copied to clipboard");
      });
    }
    onShare?.();
  };

  const handleVisibilityChange = (visibility: TemplateVisibility) => {
    setCurrentTemplate(prev => ({ ...prev, visibility }));
  };

  // Update current template when prop changes
  React.useEffect(() => {
    setCurrentTemplate(template);
  }, [template]);

  const renderTeamStructure = (teamStructure: any) => {
    if (!teamStructure || typeof teamStructure !== "object") {
      return <p className="text-muted-foreground">暂无团队结构信息</p>;
    }

    const teamMembers = teamStructure.team_members || [];
    const workflow = teamStructure.workflow || {};

    return (
      <div className="space-y-4">
        {teamStructure.team_name && (
          <div>
            <h4 className="font-semibold mb-2">团队名称</h4>
            <p>{teamStructure.team_name}</p>
          </div>
        )}

        {teamMembers.length > 0 && (
          <div>
            <h4 className="font-semibold mb-2">团队成员</h4>
            <div className="space-y-3">
              {teamMembers.map((member: any, index: number) => (
                <Card key={index} className="p-3">
                  <div className="flex items-start justify-between mb-2">
                    <h5 className="font-medium">{member.name}</h5>
                    <Badge variant="outline">{member.role}</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">
                    {member.description}
                  </p>
                  {member.capabilities && member.capabilities.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {member.capabilities.map((capability: string, capIndex: number) => (
                        <Badge key={capIndex} variant="secondary" className="text-xs">
                          {capability}
                        </Badge>
                      ))}
                    </div>
                  )}
                </Card>
              ))}
            </div>
          </div>
        )}

        {workflow.steps && workflow.steps.length > 0 && (
          <div>
            <h4 className="font-semibold mb-2">工作流程</h4>
            <div className="space-y-2">
              {workflow.steps.map((step: any, index: number) => (
                <div key={index} className="flex items-start gap-3 p-2 border rounded">
                  <div className="flex-shrink-0 w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm">
                    {index + 1}
                  </div>
                  <div className="flex-1">
                    <h6 className="font-medium">{step.name}</h6>
                    <p className="text-sm text-muted-foreground">{step.description}</p>
                    {step.assignee && (
                      <p className="text-xs text-muted-foreground mt-1">
                        负责人: {step.assignee}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <span className="text-2xl">{getCategoryIcon(template.category)}</span>
                <Badge 
                  variant="outline" 
                  className={getDifficultyColor(template.difficulty)}
                >
                  {template.difficulty}
                </Badge>
                <span className="text-lg">{getVisibilityIcon(template.visibility)}</span>
                {template.is_owner && (
                  <Badge variant="outline" className="text-xs">
                    我的模板
                  </Badge>
                )}
              </div>
              <DialogTitle className="text-2xl">{template.name}</DialogTitle>
              <DialogDescription className="text-base mt-2">
                {template.description}
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-2 mb-4">
          {onCreateAgent && (
            <Button onClick={onCreateAgent} variant="default" className="flex-1 sm:flex-none">
              <Users className="h-4 w-4 mr-2" />
              创建Agent
            </Button>
          )}
          {template.can_edit && (
            <Button variant="outline" onClick={onEdit}>
              <Edit className="h-4 w-4 mr-2" />
              编辑
            </Button>
          )}
          {versions.length > 0 && (
            <Button variant="outline" onClick={onViewVersions}>
              <GitBranch className="h-4 w-4 mr-2" />
              版本 ({versions.length})
            </Button>
          )}
          <Button variant="outline" onClick={handleShare}>
            <Share className="h-4 w-4 mr-2" />
            {currentTemplate.can_edit ? "分享设置" : "复制链接"}
          </Button>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="text-center p-3 bg-muted rounded-lg">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Users className="h-4 w-4" />
              <span className="font-semibold">{template.usage_count}</span>
            </div>
            <p className="text-xs text-muted-foreground">使用次数</p>
          </div>
          
          {template.rating && (
            <div className="text-center p-3 bg-muted rounded-lg">
              <div className="flex items-center justify-center gap-1 mb-1">
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <span className="font-semibold">{template.rating.toFixed(1)}</span>
              </div>
              <p className="text-xs text-muted-foreground">
                评分 ({template.rating_count})
              </p>
            </div>
          )}
          
          <div className="text-center p-3 bg-muted rounded-lg">
            <div className="flex items-center justify-center gap-1 mb-1">
              <TrendingUp className="h-4 w-4" />
              <span className="font-semibold">v{template.version}</span>
            </div>
            <p className="text-xs text-muted-foreground">版本</p>
          </div>
          
          <div className="text-center p-3 bg-muted rounded-lg">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Calendar className="h-4 w-4" />
              <span className="font-semibold text-xs">
                {formatDate(template.created_at).split(" ")[0]}
              </span>
            </div>
            <p className="text-xs text-muted-foreground">创建时间</p>
          </div>
        </div>

        {/* Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">概览</TabsTrigger>
            <TabsTrigger value="content">内容</TabsTrigger>
            <TabsTrigger value="structure">结构</TabsTrigger>
            <TabsTrigger value="metadata">元数据</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {/* Tags */}
            {template.tags && template.tags.length > 0 && (
              <div>
                <h4 className="font-semibold mb-2 flex items-center gap-2">
                  <Tag className="h-4 w-4" />
                  标签
                </h4>
                <div className="flex flex-wrap gap-2">
                  {template.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Use Case */}
            {template.use_case && (
              <div>
                <h4 className="font-semibold mb-2">使用场景</h4>
                <Card className="p-4">
                  <p className="text-sm">{template.use_case}</p>
                </Card>
              </div>
            )}

            {/* Team Members */}
            {template.team_structure_template?.team_members && (
              <div>
                <h4 className="font-semibold mb-2 flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  团队成员 ({template.team_structure_template.team_members.length})
                </h4>
                <div className="space-y-3">
                  {template.team_structure_template.team_members.map((member: any, index: number) => (
                    <Card key={index} className="p-4">
                      <div className="flex items-start gap-3">
                        <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                          <Users className="h-5 w-5 text-primary" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h5 className="font-medium">{member.name}</h5>
                            <Badge variant="outline" className="text-xs">
                              {member.role}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground mb-2">
                            {member.description}
                          </p>
                          {member.capabilities && member.capabilities.length > 0 && (
                            <div className="flex flex-wrap gap-1">
                              {member.capabilities.slice(0, 3).map((capability: string, capIndex: number) => (
                                <Badge key={capIndex} variant="secondary" className="text-xs">
                                  {capability}
                                </Badge>
                              ))}
                              {member.capabilities.length > 3 && (
                                <Badge variant="secondary" className="text-xs">
                                  +{member.capabilities.length - 3}
                                </Badge>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {/* Workflow */}
            {template.team_structure_template?.workflow?.steps && (
              <div>
                <h4 className="font-semibold mb-2 flex items-center gap-2">
                  <GitBranch className="h-4 w-4" />
                  工作流程 ({template.team_structure_template.workflow.steps.length} 步骤)
                </h4>
                <div className="space-y-3">
                  {template.team_structure_template.workflow.steps.map((step: any, index: number) => (
                    <Card key={index} className="p-4">
                      <div className="flex items-start gap-3">
                        <div className="w-8 h-8 rounded-full bg-blue-100 text-blue-800 flex items-center justify-center text-sm font-medium">
                          {index + 1}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h5 className="font-medium">{step.name}</h5>
                            {step.assignee && (
                              <Badge variant="outline" className="text-xs">
                                负责人: {step.assignee}
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground mb-2">
                            {step.description}
                          </p>
                          {step.dependencies && step.dependencies.length > 0 && (
                            <div className="text-xs text-muted-foreground">
                              依赖: {step.dependencies.join(", ")}
                            </div>
                          )}
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {/* Template Readiness */}
            {template.team_structure_template && (
              <div>
                <h4 className="font-semibold mb-2">部署就绪状态</h4>
                <Card className="p-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">团队成员配置</span>
                      <Badge variant={template.team_structure_template.team_members ? "default" : "secondary"}>
                        {template.team_structure_template.team_members ? "✓ 完整" : "✗ 缺失"}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">工作流程定义</span>
                      <Badge variant={template.team_structure_template.workflow?.steps ? "default" : "secondary"}>
                        {template.team_structure_template.workflow?.steps ? "✓ 完整" : "✗ 缺失"}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">可直接部署</span>
                      <Badge variant={
                        template.team_structure_template.team_members &&
                        template.team_structure_template.workflow?.steps ? "default" : "destructive"
                      }>
                        {template.team_structure_template.team_members &&
                         template.team_structure_template.workflow?.steps ? "✓ 是" : "✗ 否"}
                      </Badge>
                    </div>
                  </div>
                </Card>
              </div>
            )}

            {/* Examples */}
            {(template.example_input || template.expected_output) && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {template.example_input && (
                  <div>
                    <h4 className="font-semibold mb-2">示例输入</h4>
                    <Card className="p-4">
                      <pre className="text-sm whitespace-pre-wrap">
                        {template.example_input}
                      </pre>
                    </Card>
                  </div>
                )}
                
                {template.expected_output && (
                  <div>
                    <h4 className="font-semibold mb-2">预期输出</h4>
                    <Card className="p-4">
                      <pre className="text-sm whitespace-pre-wrap">
                        {template.expected_output}
                      </pre>
                    </Card>
                  </div>
                )}
              </div>
            )}

            {/* Author Info */}
            {template.author_name && (
              <div>
                <h4 className="font-semibold mb-2">作者信息</h4>
                <div className="flex items-center gap-2">
                  <span>{template.author_name}</span>
                  <span className="text-muted-foreground">•</span>
                  <span className="text-sm text-muted-foreground">
                    创建于 {formatDate(template.created_at)}
                  </span>
                  {template.updated_at && (
                    <>
                      <span className="text-muted-foreground">•</span>
                      <span className="text-sm text-muted-foreground">
                        更新于 {formatDate(template.updated_at)}
                      </span>
                    </>
                  )}
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="content" className="space-y-4">
            <div>
              <h4 className="font-semibold mb-2 flex items-center gap-2">
                <Code className="h-4 w-4" />
                提示词模板
              </h4>
              <Card className="p-4">
                <pre className="text-sm whitespace-pre-wrap font-mono">
                  {template.prompt_template}
                </pre>
              </Card>
            </div>

            {template.default_config && Object.keys(template.default_config).length > 0 && (
              <div>
                <h4 className="font-semibold mb-2">默认配置</h4>
                <Card className="p-4">
                  <pre className="text-sm">
                    {JSON.stringify(template.default_config, null, 2)}
                  </pre>
                </Card>
              </div>
            )}
          </TabsContent>

          <TabsContent value="structure" className="space-y-4">
            <div>
              <h4 className="font-semibold mb-2 flex items-center gap-2">
                <Users className="h-4 w-4" />
                团队结构
              </h4>
              {renderTeamStructure(template.team_structure_template)}
            </div>
          </TabsContent>

          <TabsContent value="metadata" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card className="p-4">
                <h4 className="font-semibold mb-3">基本信息</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">模板ID:</span>
                    <span className="font-mono">{template.template_id}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">分类:</span>
                    <span>{template.category}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">难度:</span>
                    <span>{template.difficulty}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">可见性:</span>
                    <span>{template.visibility}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">状态:</span>
                    <span>{template.status}</span>
                  </div>
                </div>
              </Card>

              <Card className="p-4">
                <h4 className="font-semibold mb-3">关联信息</h4>
                <div className="space-y-2 text-sm">
                  {template.source_agent_id && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">源Agent:</span>
                      <span className="font-mono">{template.source_agent_id}</span>
                    </div>
                  )}
                  {template.parent_template_id && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">父模板:</span>
                      <span className="font-mono">{template.parent_template_id}</span>
                    </div>
                  )}
                  {template.keywords && template.keywords.length > 0 && (
                    <div>
                      <span className="text-muted-foreground">关键词:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {template.keywords.map((keyword, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {keyword}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </Card>
            </div>

            {template.metadata && Object.keys(template.metadata).length > 0 && (
              <Card className="p-4">
                <h4 className="font-semibold mb-3">扩展元数据</h4>
                <pre className="text-sm bg-muted p-3 rounded">
                  {JSON.stringify(template.metadata, null, 2)}
                </pre>
              </Card>
            )}
          </TabsContent>
        </Tabs>

        {/* Share Dialog */}
        {currentTemplate.can_edit && (
          <TemplateShareDialog
            template={currentTemplate}
            isOpen={shareDialogOpen}
            onClose={() => setShareDialogOpen(false)}
            onVisibilityChange={handleVisibilityChange}
          />
        )}
      </DialogContent>
    </Dialog>
  );
};
