"use client";

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { 
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Save,
  X,
  Plus,
  Tag,
  Eye,
  Code,
  Users
} from "lucide-react";
import { Switch } from "@/components/ui/switch";
import {
  Template,
  TemplateCreateRequest,
  TemplateUpdateRequest,
  TemplateCategory,
  TemplateDifficulty,
  TemplateVisibility,
  TemplateStatus,
  Agent
} from "@/lib/types";
import { transformAgentToTemplateFormData } from "@/lib/utils";

const templateFormSchema = z.object({
  name: z.string()
    .min(3, "模板名称至少需要3个字符")
    .max(255, "模板名称不能超过255个字符")
    .regex(/^[a-zA-Z0-9\u4e00-\u9fa5\s\-_]+$/, "模板名称只能包含字母、数字、中文、空格、连字符和下划线"),
  description: z.string()
    .min(10, "描述至少需要10个字符，请详细说明模板的功能和用途")
    .max(2000, "描述不能超过2000个字符")
    .refine((val) => val.trim().length >= 10, "描述不能只包含空格，请提供有意义的内容"),
  category: z.string()
    .min(1, "请选择一个分类，这有助于其他用户找到您的模板")
    .refine((val) => val !== "", "分类是必填项，请从下拉列表中选择"),
  difficulty: z.string()
    .min(1, "请选择难度级别，这有助于用户了解模板的复杂程度")
    .refine((val) => val !== "", "难度是必填项，请从下拉列表中选择"),
  visibility: z.string().optional(),
  status: z.string().optional(),
  prompt_template: z.string()
    .min(20, "提示词模板至少需要20个字符，请提供详细的指令")
    .max(5000, "提示词模板不能超过5000个字符")
    .refine((val) => val.trim().length >= 20, "提示词模板不能只包含空格，请提供有效的指令内容"),
  use_case: z.string()
    .optional()
    .refine((val) => !val || val.trim().length >= 5, "如果填写使用场景，请至少提供5个字符的描述"),
  tags: z.array(z.string()).optional(),
  keywords: z.array(z.string()).optional(),
});

type TemplateFormData = z.infer<typeof templateFormSchema>;

interface TemplateFormProps {
  template?: Template;
  sourceAgent?: Agent;
  onSubmit: (data: TemplateCreateRequest | TemplateUpdateRequest) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  categories?: Array<{ value: string; label: string }>;
  difficulties?: Array<{ value: string; label: string }>;
  mode: "create" | "edit";
  onToast?: (message: { title: string; description: string; variant?: "default" | "destructive" }) => void;
}

export const TemplateForm: React.FC<TemplateFormProps> = ({
  template,
  sourceAgent,
  onSubmit,
  onCancel,
  isLoading = false,
  categories = [],
  difficulties = [],
  mode,
  onToast
}) => {
  const [tagInput, setTagInput] = useState("");
  const [keywordInput, setKeywordInput] = useState("");
  const [previewMode, setPreviewMode] = useState(false);

  // Transform agent data to template form data if sourceAgent is provided
  const getDefaultValues = () => {
    if (sourceAgent) {
      const agentTemplateData = transformAgentToTemplateFormData(sourceAgent);
      return {
        name: agentTemplateData.name || "",
        description: agentTemplateData.description || "",
        category: agentTemplateData.category || "",
        difficulty: agentTemplateData.difficulty || "",
        visibility: agentTemplateData.visibility || "private",
        status: agentTemplateData.status || "active",
        prompt_template: agentTemplateData.prompt_template || "",
        use_case: agentTemplateData.use_case || "",
        tags: agentTemplateData.tags || [],
        keywords: agentTemplateData.keywords || [],
      };
    }

    if (template) {
      return {
        name: template.name || "",
        description: template.description || "",
        category: template.category || "",
        difficulty: template.difficulty || "",
        visibility: template.visibility || "private",
        status: template.status || "active",
        prompt_template: template.prompt_template || "",
        use_case: template.use_case || "",
        tags: template.tags || [],
        keywords: template.keywords || [],
      };
    }

    return {
      name: "",
      description: "",
      category: "",
      difficulty: "",
      visibility: "private",
      status: "active",
      prompt_template: "",
      use_case: "",
      tags: [],
      keywords: [],
    };
  };

  const form = useForm<TemplateFormData>({
    resolver: zodResolver(templateFormSchema),
    defaultValues: getDefaultValues()
  });

  // Reset form when sourceAgent or template changes
  useEffect(() => {
    form.reset(getDefaultValues());
  }, [sourceAgent, template]);

  const watchedTags = form.watch("tags") || [];
  const watchedKeywords = form.watch("keywords") || [];

  // 监听表单字段变化，提供实时验证反馈
  const watchedName = form.watch("name");
  const watchedDescription = form.watch("description");
  const watchedCategory = form.watch("category");
  const watchedDifficulty = form.watch("difficulty");
  const watchedPromptTemplate = form.watch("prompt_template");

  // 计算表单完成度
  const getFormCompleteness = () => {
    const requiredFields = [
      { name: "name", value: watchedName, label: "模板名称" },
      { name: "description", value: watchedDescription, label: "描述", minLength: 10 },
      { name: "category", value: watchedCategory, label: "分类" },
      { name: "difficulty", value: watchedDifficulty, label: "难度" },
      { name: "prompt_template", value: watchedPromptTemplate, label: "提示词模板", minLength: 20 }
    ];

    const completedFields = requiredFields.filter(field => {
      if (!field.value?.trim()) return false;
      if (field.minLength && field.value.trim().length < field.minLength) return false;
      return true;
    });

    return {
      completed: completedFields.length,
      total: requiredFields.length,
      percentage: Math.round((completedFields.length / requiredFields.length) * 100),
      missingFields: requiredFields.filter(field => {
        if (!field.value?.trim()) return true;
        if (field.minLength && field.value.trim().length < field.minLength) return true;
        return false;
      })
    };
  };

  const formCompleteness = getFormCompleteness();

  const handleAddTag = () => {
    if (tagInput.trim() && !watchedTags.includes(tagInput.trim())) {
      form.setValue("tags", [...watchedTags, tagInput.trim()]);
      setTagInput("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    form.setValue("tags", watchedTags.filter(tag => tag !== tagToRemove));
  };

  const handleAddKeyword = () => {
    if (keywordInput.trim() && !watchedKeywords.includes(keywordInput.trim())) {
      form.setValue("keywords", [...watchedKeywords, keywordInput.trim()]);
      setKeywordInput("");
    }
  };

  const handleRemoveKeyword = (keywordToRemove: string) => {
    form.setValue("keywords", watchedKeywords.filter(keyword => keyword !== keywordToRemove));
  };

  const validateFormData = (data: TemplateFormData): string[] => {
    const errors: string[] = [];

    // 验证必填字段
    if (!data.name?.trim()) {
      errors.push("模板名称是必填项");
    } else if (data.name.trim().length < 3) {
      errors.push("模板名称至少需要3个字符");
    }

    if (!data.description?.trim()) {
      errors.push("描述是必填项");
    } else if (data.description.trim().length < 10) {
      errors.push("描述至少需要10个字符，请详细说明模板的功能和用途");
    }

    if (!data.category) {
      errors.push("请选择模板分类");
    }

    if (!data.difficulty) {
      errors.push("请选择难度级别");
    }

    if (!data.prompt_template?.trim()) {
      errors.push("提示词模板是必填项");
    } else if (data.prompt_template.trim().length < 20) {
      errors.push("提示词模板至少需要20个字符，请提供详细的指令");
    }

    // 针对从Agent创建模板的智能验证（仅警告，不阻止提交）
    if (sourceAgent) {
      // 检查名称是否过于简单（但不阻止提交）
      if (data.name === sourceAgent.team_name) {
        // 这是一个建议，不是错误
        console.warn("建议：模板名称与Agent名称相同，建议使用更具描述性的名称");
      }

      // 检查描述是否需要丰富（但不阻止提交）
      if (data.description === sourceAgent.description) {
        // 这是一个建议，不是错误
        console.warn("建议：可以丰富模板描述，添加更多关于使用方法和适用场景的信息");
      }
    }

    return errors;
  };

  const handleFormSubmit = async (data: TemplateFormData) => {
    try {
      // 执行额外的客户端验证
      const validationErrors = validateFormData(data);

      if (validationErrors.length > 0) {
        // 显示验证错误
        validationErrors.forEach(error => {
          console.warn("Validation error:", error);
        });

        // 创建更友好的错误提示
        const errorMessage = `请修正以下问题：\n\n${validationErrors.map((error, index) => `${index + 1}. ${error}`).join('\n')}`;

        // 使用toast显示错误
        if (onToast) {
          onToast({
            title: "表单验证失败",
            description: "请完成所有必填字段后再提交",
            variant: "destructive"
          });
        }
        return;
      }

      const submitData = {
        ...data,
        team_structure_template: sourceAgent?.team_plan || template?.team_structure_template || {},
        default_config: template?.default_config || {},
      };

      await onSubmit(submitData);
    } catch (error) {
      console.error("Failed to submit template:", error);

      // 更详细的错误处理
      let errorMessage = "提交失败，请重试";

      if (error instanceof Error) {
        if (error.message.includes('network') || error.message.includes('fetch')) {
          errorMessage = "网络连接失败，请检查网络连接后重试";
        } else if (error.message.includes('validation')) {
          errorMessage = "数据验证失败，请检查输入内容";
        } else if (error.message.includes('duplicate') || error.message.includes('exists')) {
          errorMessage = "模板名称已存在，请使用不同的名称";
        } else {
          errorMessage = `提交失败：${error.message}`;
        }
      }

      // 使用toast显示错误而不是alert
      if (onToast) {
        onToast({
          title: "提交失败",
          description: errorMessage,
          variant: "destructive"
        });
      }
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">
            {mode === "create" ? "创建模板" : "编辑模板"}
          </h2>
          {sourceAgent && (
            <p className="text-sm text-muted-foreground mt-1">
              基于Agent "{sourceAgent.team_name}" 创建模板
            </p>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={() => setPreviewMode(!previewMode)}
          >
            <Eye className="h-4 w-4 mr-2" />
            {previewMode ? "编辑" : "预览"}
          </Button>
          <Button type="button" variant="outline" onClick={onCancel}>
            取消
          </Button>
        </div>
      </div>



      {previewMode ? (
        <Card>
          <CardHeader>
            <CardTitle>模板预览</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-semibold text-lg">{form.watch("name") || "模板名称"}</h3>
              <p className="text-muted-foreground mt-1">
                {form.watch("description") || "模板描述"}
              </p>
            </div>
            
            <div className="flex gap-2">
              <Badge variant="outline">{form.watch("category") || "分类"}</Badge>
              <Badge variant="outline">{form.watch("difficulty") || "难度"}</Badge>
              <Badge variant="outline">{form.watch("visibility") || "可见性"}</Badge>
            </div>

            {watchedTags.length > 0 && (
              <div>
                <Label className="text-sm font-medium">标签</Label>
                <div className="flex flex-wrap gap-1 mt-1">
                  {watchedTags.map((tag, index) => (
                    <Badge key={index} variant="secondary">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {form.watch("use_case") && (
              <div>
                <Label className="text-sm font-medium">使用场景</Label>
                <p className="text-sm text-muted-foreground mt-1">
                  {form.watch("use_case")}
                </p>
              </div>
            )}

            <div>
              <Label className="text-sm font-medium">提示词模板</Label>
              <div className="mt-1 p-3 bg-muted rounded-md">
                <pre className="text-sm whitespace-pre-wrap">
                  {form.watch("prompt_template") || "提示词模板内容"}
                </pre>
              </div>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleFormSubmit)} className="space-y-6">
            {/* Form Progress Indicator */}
            {mode === "create" && (
              <Card className="border-l-4 border-l-blue-500">
                <CardContent className="pt-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">表单完成度</span>
                    <span className="text-sm text-muted-foreground">
                      {formCompleteness.completed}/{formCompleteness.total} 必填项
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${formCompleteness.percentage}%` }}
                    ></div>
                  </div>
                  {formCompleteness.missingFields.length > 0 && (
                    <div className="text-xs text-muted-foreground">
                      还需要完成: {formCompleteness.missingFields.map(f => f.label).join("、")}
                    </div>
                  )}
                  {formCompleteness.percentage === 100 && (
                    <div className="text-xs text-green-600 font-medium">
                      ✓ 所有必填项已完成，可以提交模板
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  基本信息
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium flex items-center gap-1">
                          模板名称
                          <span className="text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={sourceAgent ? `${sourceAgent.team_name} 模板` : "输入模板名称，如：内容创作团队模板"}
                            {...field}
                          />
                        </FormControl>
                        <FormDescription className="text-xs text-muted-foreground">
                          {sourceAgent
                            ? "已根据Agent名称自动填充，您可以修改为更合适的模板名称"
                            : "为模板起一个清晰、描述性的名称，便于其他用户理解和搜索"
                          }
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="category"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium flex items-center gap-1">
                          分类
                          <span className="text-red-500">*</span>
                        </FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger className={!field.value ? "text-muted-foreground" : ""}>
                              <SelectValue placeholder="请选择模板分类" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {categories.map((category) => (
                              <SelectItem key={category.value} value={category.value}>
                                {category.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription className="text-xs text-muted-foreground">
                          {sourceAgent
                            ? "已根据Agent类型自动选择，您可以调整为更合适的分类"
                            : "选择最符合模板用途的分类，帮助用户快速找到相关模板"
                          }
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium flex items-center gap-1">
                        描述
                        <span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder={sourceAgent
                            ? "已根据Agent描述自动填充，请根据需要调整或补充更多详细信息..."
                            : "详细描述模板的功能、用途、适用场景和特点，至少10个字符"
                          }
                          className="min-h-[120px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription className="text-xs text-muted-foreground">
                        {sourceAgent
                          ? "描述已从Agent信息中提取，您可以添加更多关于模板使用方法、适用场景的说明"
                          : "提供详细的模板描述，包括功能特点、适用场景、使用方法等，帮助用户了解模板价值"
                        }
                        <span className="block mt-1 text-xs">
                          当前字符数: {field.value?.length || 0} / 2000 (最少需要10个字符)
                        </span>
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="difficulty"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium flex items-center gap-1">
                          难度
                          <span className="text-red-500">*</span>
                        </FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger className={!field.value ? "text-muted-foreground" : ""}>
                              <SelectValue placeholder="选择难度级别" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {difficulties.map((difficulty) => (
                              <SelectItem key={difficulty.value} value={difficulty.value}>
                                {difficulty.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription className="text-xs text-muted-foreground">
                          {sourceAgent
                            ? "已根据Agent复杂度自动选择，您可以根据实际使用难度调整"
                            : "选择模板的使用难度，帮助用户评估是否适合自己的技能水平"
                          }
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="visibility"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>可见性</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="private">私有</SelectItem>
                            <SelectItem value="public">公开</SelectItem>
                            <SelectItem value="shared">共享</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>状态</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="draft">草稿</SelectItem>
                            <SelectItem value="active">活跃</SelectItem>
                            <SelectItem value="archived">归档</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Content */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Code className="h-5 w-5" />
                  模板内容
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="prompt_template"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium flex items-center gap-1">
                        提示词模板
                        <span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder={sourceAgent
                            ? "已根据Agent的提示词自动填充，您可以根据需要进行调整和优化..."
                            : "输入详细的提示词模板内容，定义AI团队的行为和响应模式，至少20个字符"
                          }
                          className="min-h-[200px] font-mono text-sm"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription className="text-xs text-muted-foreground">
                        {sourceAgent
                          ? "提示词已从Agent配置中提取，您可以进一步优化以提高模板的通用性和效果"
                          : "编写清晰、具体的提示词，定义AI团队的角色、任务和响应方式。好的提示词是模板成功的关键"
                        }
                        <span className="block mt-1 text-xs">
                          当前字符数: {field.value?.length || 0} / 5000 (最少需要20个字符)
                        </span>
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="use_case"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>使用场景</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="描述模板的典型使用场景和应用领域"
                          className="min-h-[80px]"
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />


              </CardContent>
            </Card>

            {/* Tags and Keywords */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Tag className="h-5 w-5" />
                  标签和关键词
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Tags */}
                <div className="space-y-2">
                  <Label>标签</Label>
                  <div className="flex gap-2">
                    <Input
                      placeholder="添加标签"
                      value={tagInput}
                      onChange={(e) => setTagInput(e.target.value)}
                      onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), handleAddTag())}
                    />
                    <Button type="button" onClick={handleAddTag}>
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  {watchedTags.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {watchedTags.map((tag, index) => (
                        <Badge key={index} variant="secondary" className="cursor-pointer">
                          {tag}
                          <X 
                            className="ml-1 h-3 w-3" 
                            onClick={() => handleRemoveTag(tag)}
                          />
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>

                {/* Keywords */}
                <div className="space-y-2">
                  <Label>搜索关键词</Label>
                  <div className="flex gap-2">
                    <Input
                      placeholder="添加搜索关键词"
                      value={keywordInput}
                      onChange={(e) => setKeywordInput(e.target.value)}
                      onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), handleAddKeyword())}
                    />
                    <Button type="button" onClick={handleAddKeyword}>
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  {watchedKeywords.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {watchedKeywords.map((keyword, index) => (
                        <Badge key={index} variant="outline" className="cursor-pointer">
                          {keyword}
                          <X 
                            className="ml-1 h-3 w-3" 
                            onClick={() => handleRemoveKeyword(keyword)}
                          />
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Submit Buttons */}
            <div className="flex justify-end gap-2">
              <Button type="button" variant="outline" onClick={onCancel}>
                取消
              </Button>
              <Button
                type="submit"
                disabled={isLoading || (mode === "create" && formCompleteness.percentage < 100)}
                className={formCompleteness.percentage === 100 ? "bg-green-600 hover:bg-green-700" : ""}
              >
                <Save className="h-4 w-4 mr-2" />
                {isLoading
                  ? "保存中..."
                  : formCompleteness.percentage < 100 && mode === "create"
                    ? `完成必填项 (${formCompleteness.completed}/${formCompleteness.total})`
                    : mode === "create"
                      ? "创建模板"
                      : "保存更改"
                }
              </Button>
            </div>

            {/* 表单验证提示 */}
            {mode === "create" && formCompleteness.percentage < 100 && (
              <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-yellow-800">
                      请完成所有必填字段
                    </h3>
                    <div className="mt-2 text-sm text-yellow-700">
                      <ul className="list-disc pl-5 space-y-1">
                        {formCompleteness.missingFields.map((field, index) => (
                          <li key={index}>
                            {field.label}
                            {field.minLength && ` (至少${field.minLength}个字符)`}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </form>
        </Form>
      )}
    </div>
  );
};
