"use client";

import React from "react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Eye,
  Star,
  Users,
  Calendar,
  Tag,
  TrendingUp
} from "lucide-react";
import { TemplateListItem, TemplateDifficulty } from "@/lib/types";

interface TemplateCardProps {
  template: TemplateListItem;
  onView?: (template: TemplateListItem) => void;
  onEdit?: (template: TemplateListItem) => void;
  onDelete?: (template: TemplateListItem) => void;
  onCreateAgent?: (template: TemplateListItem) => void;
  showActions?: boolean;
  compact?: boolean;
}

const getDifficultyColor = (difficulty: TemplateDifficulty): string => {
  switch (difficulty) {
    case "beginner":
      return "bg-green-100 text-green-800 border-green-200";
    case "intermediate":
      return "bg-blue-100 text-blue-800 border-blue-200";
    case "advanced":
      return "bg-orange-100 text-orange-800 border-orange-200";
    case "expert":
      return "bg-red-100 text-red-800 border-red-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
};

const getVisibilityIcon = (visibility: string) => {
  switch (visibility) {
    case "public":
      return "🌐";
    case "featured":
      return "⭐";
    case "shared":
      return "👥";
    case "private":
    default:
      return "🔒";
  }
};

const getCategoryIcon = (category: string) => {
  const icons: Record<string, string> = {
    business: "💼",
    technical: "⚙️",
    creative: "🎨",
    analysis: "📊",
    support: "🛠️",
    education: "📚",
    investigation: "🔍",
    consulting: "💡",
    research: "🔬",
    customer_service: "📞",
    marketing: "📢",
    sales: "💰",
    healthcare: "🏥",
    finance: "💳",
    legal: "⚖️",
    other: "📋"
  };
  return icons[category] || "📋";
};

export const TemplateCard: React.FC<TemplateCardProps> = ({
  template,
  onView,
  onEdit,
  onDelete,
  onCreateAgent,
  showActions = true,
  compact = false
}) => {
  const handleAction = (action: string, e: React.MouseEvent) => {
    e.stopPropagation();
    switch (action) {
      case "view":
        onView?.(template);
        break;
      case "edit":
        onEdit?.(template);
        break;
      case "delete":
        onDelete?.(template);
        break;
      case "createAgent":
        onCreateAgent?.(template);
        break;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "short",
      day: "numeric"
    });
  };

  return (
    <Card
      className={`hover:shadow-lg transition-all duration-200 hover:border-primary/50 cursor-pointer group ${
        compact ? "h-auto" : "h-full"
      }`}
      onClick={() => onView?.(template)}
    >
      <CardContent className="p-6 flex flex-col h-full">
        <div className="space-y-4 flex-1">
          {/* Header */}
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-lg">{getCategoryIcon(template.category)}</span>
              <Badge variant="outline" className="text-xs">
                {template.category}
              </Badge>
              <Badge
                variant="outline"
                className={`text-xs ${getDifficultyColor(template.difficulty)}`}
              >
                {template.difficulty}
              </Badge>
              <span className="text-sm">{getVisibilityIcon(template.visibility)}</span>
            </div>
          </div>

          {/* Content */}
          <div>
            <h3 className="font-semibold text-lg group-hover:text-primary transition-colors">
              {template.name}
            </h3>
            <p className={`text-sm text-muted-foreground mt-2 ${compact ? "line-clamp-2" : "line-clamp-3"}`}>
              {template.description}
            </p>
          </div>

          {/* Tags */}
          {template.tags && template.tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {template.tags.slice(0, compact ? 3 : 3).map((tag, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {template.tags.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{template.tags.length - 3}
                </Badge>
              )}
            </div>
          )}

          {/* Use case */}
          {template.use_case && !compact && (
            <div className="p-3 bg-muted/50 rounded-md">
              <p className="text-sm text-muted-foreground line-clamp-2">
                💡 {template.use_case}
              </p>
            </div>
          )}

          {/* Stats Grid */}
          <div className={`grid ${compact ? "grid-cols-2" : "grid-cols-3"} gap-3 text-center text-sm`}>
            <div className="space-y-1">
              <div className="flex items-center justify-center space-x-1">
                <Users className="h-3 w-3 text-blue-500" />
                <span className="font-medium">
                  {(template as any).team_structure_template?.team_members?.length || 0}
                </span>
              </div>
              <p className="text-xs text-muted-foreground">成员</p>
            </div>
            <div className="space-y-1">
              <div className="flex items-center justify-center space-x-1">
                <TrendingUp className="h-3 w-3 text-green-500" />
                <span className="font-medium">{template.usage_count || 0}</span>
              </div>
              <p className="text-xs text-muted-foreground">使用</p>
            </div>
            {!compact && template.rating && (
              <div className="space-y-1">
                <div className="flex items-center justify-center space-x-1">
                  <Star className="h-3 w-3 text-yellow-500" />
                  <span className="font-medium">{template.rating.toFixed(1)}</span>
                </div>
                <p className="text-xs text-muted-foreground">评分</p>
              </div>
            )}
          </div>

          {/* Template Readiness & Team Info */}
          {!compact && (template as any).team_structure_template && (
            <div className="space-y-2 p-3 bg-muted/30 rounded-md">
              <div className="flex items-center justify-between text-xs">
                <span className="text-muted-foreground">工作流程</span>
                <Badge variant="outline" className="text-xs">
                  {(template as any).team_structure_template.workflow?.steps?.length || 0} 步骤
                </Badge>
              </div>
              <div className="flex items-center justify-between text-xs">
                <span className="text-muted-foreground">部署状态</span>
                <Badge
                  variant={
                    (template as any).team_structure_template.team_members?.length > 0 &&
                    (template as any).team_structure_template.workflow?.steps?.length > 0
                      ? "default" : "secondary"
                  }
                  className="text-xs"
                >
                  {(template as any).team_structure_template.team_members?.length > 0 &&
                   (template as any).team_structure_template.workflow?.steps?.length > 0
                    ? "🚀 即用" : "⚙️ 需配置"}
                </Badge>
              </div>
            </div>
          )}

          {/* Author & Date */}
          {(template.author_name || template.created_at) && (
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              {template.author_name && (
                <div className="flex items-center gap-2">
                  <span>作者: {template.author_name}</span>
                  {template.is_owner && (
                    <Badge variant="outline" className="text-xs">
                      我的模板
                    </Badge>
                  )}
                </div>
              )}
              {template.created_at && (
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  <span>{formatDate(template.created_at)}</span>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Actions */}
        {showActions && (
          <div className="flex items-center space-x-2 pt-4 mt-auto">
            {onCreateAgent && (
              <Button
                className="flex-1"
                onClick={(e) => handleAction("createAgent", e)}
              >
                <Users className="h-4 w-4 mr-2" />
                创建Agent
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              className="px-3"
              onClick={(e) => handleAction("view", e)}
            >
              <Eye className="h-4 w-4 mr-2" />
              查看详情
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
