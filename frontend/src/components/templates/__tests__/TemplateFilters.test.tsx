/**
 * Tests for TemplateFilters component
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { TemplateFilters } from '../TemplateFilters';
import { TemplateFilters as ITemplateFilters } from '@/lib/types';

// Mock the icons
jest.mock('lucide-react', () => ({
  Search: () => <div data-testid="search-icon" />,
  Filter: () => <div data-testid="filter-icon" />,
  X: () => <div data-testid="x-icon" />,
  Tag: () => <div data-testid="tag-icon" />,
  Grid: () => <div data-testid="grid-icon" />,
  List: () => <div data-testid="list-icon" />,
  SortAsc: () => <div data-testid="sort-asc-icon" />,
  SortDesc: () => <div data-testid="sort-desc-icon" />,
}));

const mockCategories = [
  { value: 'business', label: 'Business' },
  { value: 'technical', label: 'Technical' },
  { value: 'creative', label: 'Creative' },
];

const mockDifficulties = [
  { value: 'beginner', label: 'Beginner' },
  { value: 'intermediate', label: 'Intermediate' },
  { value: 'advanced', label: 'Advanced' },
];

const mockPopularTags = [
  { tag: 'automation', count: 15 },
  { tag: 'testing', count: 12 },
  { tag: 'api', count: 8 },
];

describe('TemplateFilters', () => {
  const mockOnFiltersChange = jest.fn();
  const mockOnSearch = jest.fn();
  const mockOnViewModeChange = jest.fn();
  const mockOnSortChange = jest.fn();

  const defaultProps = {
    filters: {} as ITemplateFilters,
    onFiltersChange: mockOnFiltersChange,
    onSearch: mockOnSearch,
    viewMode: 'grid' as const,
    onViewModeChange: mockOnViewModeChange,
    sortBy: 'created_at',
    sortOrder: 'desc' as const,
    onSortChange: mockOnSortChange,
    categories: mockCategories,
    difficulties: mockDifficulties,
    popularTags: mockPopularTags,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders search input', () => {
    render(<TemplateFilters {...defaultProps} />);
    
    expect(screen.getByPlaceholderText('搜索模板名称、描述、标签...')).toBeInTheDocument();
  });

  it('renders view mode toggle buttons', () => {
    render(<TemplateFilters {...defaultProps} />);
    
    expect(screen.getByTestId('grid-icon')).toBeInTheDocument();
    expect(screen.getByTestId('list-icon')).toBeInTheDocument();
  });

  it('renders sort controls', () => {
    render(<TemplateFilters {...defaultProps} />);
    
    expect(screen.getByTestId('sort-desc-icon')).toBeInTheDocument();
  });

  it('calls onSearch when search form is submitted', async () => {
    const user = userEvent.setup();
    render(<TemplateFilters {...defaultProps} />);
    
    const searchInput = screen.getByPlaceholderText('搜索模板名称、描述、标签...');
    await user.type(searchInput, 'test query');
    await user.keyboard('{Enter}');
    
    expect(mockOnSearch).toHaveBeenCalledWith('test query');
  });

  it('calls onViewModeChange when view mode is changed', async () => {
    const user = userEvent.setup();
    render(<TemplateFilters {...defaultProps} />);
    
    const listButton = screen.getByTestId('list-icon').closest('button');
    await user.click(listButton!);
    
    expect(mockOnViewModeChange).toHaveBeenCalledWith('list');
  });

  it('calls onSortChange when sort order is toggled', async () => {
    const user = userEvent.setup();
    render(<TemplateFilters {...defaultProps} />);
    
    const sortButton = screen.getByTestId('sort-desc-icon').closest('button');
    await user.click(sortButton!);
    
    expect(mockOnSortChange).toHaveBeenCalledWith('created_at', 'asc');
  });

  it('shows filter panel when filter button is clicked', async () => {
    const user = userEvent.setup();
    render(<TemplateFilters {...defaultProps} />);
    
    const filterButton = screen.getByText('筛选');
    await user.click(filterButton);
    
    await waitFor(() => {
      expect(screen.getByText('筛选条件')).toBeInTheDocument();
    });
  });

  it('displays categories in filter panel', async () => {
    const user = userEvent.setup();
    render(<TemplateFilters {...defaultProps} />);
    
    const filterButton = screen.getByText('筛选');
    await user.click(filterButton);
    
    await waitFor(() => {
      expect(screen.getByText('分类')).toBeInTheDocument();
      expect(screen.getByText('全部分类')).toBeInTheDocument();
    });
  });

  it('displays difficulties in filter panel', async () => {
    const user = userEvent.setup();
    render(<TemplateFilters {...defaultProps} />);
    
    const filterButton = screen.getByText('筛选');
    await user.click(filterButton);
    
    await waitFor(() => {
      expect(screen.getByText('难度')).toBeInTheDocument();
      expect(screen.getByText('全部难度')).toBeInTheDocument();
    });
  });

  it('displays popular tags when provided', async () => {
    const user = userEvent.setup();
    render(<TemplateFilters {...defaultProps} />);
    
    const filterButton = screen.getByText('筛选');
    await user.click(filterButton);
    
    await waitFor(() => {
      expect(screen.getByText('热门标签')).toBeInTheDocument();
      expect(screen.getByText('automation')).toBeInTheDocument();
      expect(screen.getByText('(15)')).toBeInTheDocument();
    });
  });

  it('calls onFiltersChange when category is selected', async () => {
    const user = userEvent.setup();
    render(<TemplateFilters {...defaultProps} />);
    
    const filterButton = screen.getByText('筛选');
    await user.click(filterButton);
    
    await waitFor(async () => {
      const categorySelect = screen.getByDisplayValue('选择分类');
      await user.click(categorySelect);
      
      await waitFor(async () => {
        const businessOption = screen.getByText('Business');
        await user.click(businessOption);
        
        expect(mockOnFiltersChange).toHaveBeenCalledWith({
          category: 'business'
        });
      });
    });
  });

  it('calls onFiltersChange when difficulty is selected', async () => {
    const user = userEvent.setup();
    render(<TemplateFilters {...defaultProps} />);
    
    const filterButton = screen.getByText('筛选');
    await user.click(filterButton);
    
    await waitFor(async () => {
      const difficultySelect = screen.getByDisplayValue('选择难度');
      await user.click(difficultySelect);
      
      await waitFor(async () => {
        const beginnerOption = screen.getByText('Beginner');
        await user.click(beginnerOption);
        
        expect(mockOnFiltersChange).toHaveBeenCalledWith({
          difficulty: 'beginner'
        });
      });
    });
  });

  it('toggles tag selection when tag is clicked', async () => {
    const user = userEvent.setup();
    render(<TemplateFilters {...defaultProps} />);
    
    const filterButton = screen.getByText('筛选');
    await user.click(filterButton);
    
    await waitFor(async () => {
      const automationTag = screen.getByText('automation');
      await user.click(automationTag);
      
      expect(mockOnFiltersChange).toHaveBeenCalledWith({
        tags: 'automation'
      });
    });
  });

  it('shows selected tags section when tags are selected', async () => {
    const user = userEvent.setup();
    const filtersWithTags = { tags: 'automation,testing' };
    
    render(<TemplateFilters {...defaultProps} filters={filtersWithTags} />);
    
    const filterButton = screen.getByText('筛选');
    await user.click(filterButton);
    
    await waitFor(() => {
      expect(screen.getByText('已选标签')).toBeInTheDocument();
    });
  });

  it('shows clear filters button when filters are active', async () => {
    const user = userEvent.setup();
    const activeFilters = { category: 'business', difficulty: 'intermediate' };
    
    render(<TemplateFilters {...defaultProps} filters={activeFilters} />);
    
    const filterButton = screen.getByText('筛选');
    await user.click(filterButton);
    
    await waitFor(() => {
      expect(screen.getByText('清除筛选')).toBeInTheDocument();
    });
  });

  it('clears all filters when clear button is clicked', async () => {
    const user = userEvent.setup();
    const activeFilters = { category: 'business', difficulty: 'intermediate' };
    
    render(<TemplateFilters {...defaultProps} filters={activeFilters} />);
    
    const filterButton = screen.getByText('筛选');
    await user.click(filterButton);
    
    await waitFor(async () => {
      const clearButton = screen.getByText('清除筛选');
      await user.click(clearButton);
      
      expect(mockOnFiltersChange).toHaveBeenCalledWith({});
    });
  });

  it('shows active filter indicator on filter button', () => {
    const activeFilters = { category: 'business' };
    
    render(<TemplateFilters {...defaultProps} filters={activeFilters} />);
    
    const filterButton = screen.getByText('筛选');
    expect(filterButton.querySelector('[data-testid="filter-icon"]')).toBeInTheDocument();
    expect(screen.getByText('!')).toBeInTheDocument(); // Active indicator
  });

  it('handles my_templates filter toggle', async () => {
    const user = userEvent.setup();
    render(<TemplateFilters {...defaultProps} />);
    
    const filterButton = screen.getByText('筛选');
    await user.click(filterButton);
    
    await waitFor(async () => {
      const scopeSelect = screen.getByDisplayValue('全部模板');
      await user.click(scopeSelect);
      
      await waitFor(async () => {
        const myTemplatesOption = screen.getByText('我的模板');
        await user.click(myTemplatesOption);
        
        expect(mockOnFiltersChange).toHaveBeenCalledWith({
          my_templates: true
        });
      });
    });
  });

  it('limits displayed popular tags to 15', async () => {
    const user = userEvent.setup();
    const manyTags = Array.from({ length: 20 }, (_, i) => ({
      tag: `tag${i}`,
      count: 10 - i
    }));
    
    render(<TemplateFilters {...defaultProps} popularTags={manyTags} />);
    
    const filterButton = screen.getByText('筛选');
    await user.click(filterButton);
    
    await waitFor(() => {
      // Should only show first 15 tags
      expect(screen.getByText('tag0')).toBeInTheDocument();
      expect(screen.getByText('tag14')).toBeInTheDocument();
      expect(screen.queryByText('tag15')).not.toBeInTheDocument();
    });
  });

  it('updates selected tags when filters prop changes', () => {
    const { rerender } = render(<TemplateFilters {...defaultProps} />);
    
    // Update with tags filter
    const filtersWithTags = { tags: 'automation,testing' };
    rerender(<TemplateFilters {...defaultProps} filters={filtersWithTags} />);
    
    // Should update internal state to reflect the tags
    expect(screen.getByText('筛选')).toBeInTheDocument();
  });

  it('handles sort by change', async () => {
    const user = userEvent.setup();
    render(<TemplateFilters {...defaultProps} />);
    
    const sortSelect = screen.getByDisplayValue('创建时间');
    await user.click(sortSelect);
    
    await waitFor(async () => {
      const usageOption = screen.getByText('使用次数');
      await user.click(usageOption);
      
      expect(mockOnSortChange).toHaveBeenCalledWith('usage_count', 'desc');
    });
  });

  it('shows correct sort icon based on sort order', () => {
    const { rerender } = render(<TemplateFilters {...defaultProps} />);
    
    expect(screen.getByTestId('sort-desc-icon')).toBeInTheDocument();
    
    // Change to ascending order
    rerender(<TemplateFilters {...defaultProps} sortOrder="asc" />);
    
    expect(screen.getByTestId('sort-asc-icon')).toBeInTheDocument();
  });
});
