/**
 * Tests for TemplateCard component
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { TemplateCard } from '../TemplateCard';
import { TemplateListItem } from '@/lib/types';

// Mock the icons
jest.mock('lucide-react', () => ({
  Edit: () => <div data-testid="edit-icon" />,
  Eye: () => <div data-testid="eye-icon" />,
  MoreVertical: () => <div data-testid="more-vertical-icon" />,
  Star: () => <div data-testid="star-icon" />,
  Trash2: () => <div data-testid="trash-icon" />,
  Users: () => <div data-testid="users-icon" />,
  Calendar: () => <div data-testid="calendar-icon" />,
  Tag: () => <div data-testid="tag-icon" />,
  TrendingUp: () => <div data-testid="trending-up-icon" />,
}));

const mockTemplate: TemplateListItem = {
  id: 1,
  template_id: 'template_123',
  name: 'Test Template',
  description: 'This is a test template for unit testing',
  category: 'technical',
  difficulty: 'intermediate',
  visibility: 'public',
  status: 'active',
  tags: ['test', 'automation', 'unit-test'],
  usage_count: 42,
  rating: 4.5,
  rating_count: 10,
  version: '1.0.0',
  author_name: 'Test Author',
  use_case: 'Testing template functionality',
  created_at: '2025-01-01T00:00:00Z',
  is_owner: true,
  can_edit: true,
};

const mockTemplateNotOwner: TemplateListItem = {
  ...mockTemplate,
  template_id: 'template_456',
  name: 'Public Template',
  is_owner: false,
  can_edit: false,
};

describe('TemplateCard', () => {
  const mockOnView = jest.fn();
  const mockOnEdit = jest.fn();

  const mockOnDelete = jest.fn();


  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders template information correctly', () => {
    render(
      <TemplateCard
        template={mockTemplate}
        onView={mockOnView}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    );

    expect(screen.getByText('Test Template')).toBeInTheDocument();
    expect(screen.getByText('This is a test template for unit testing')).toBeInTheDocument();
    expect(screen.getByText('intermediate')).toBeInTheDocument();
    expect(screen.getByText('42')).toBeInTheDocument();
    expect(screen.getByText('4.5')).toBeInTheDocument();
    expect(screen.getByText('v1.0.0')).toBeInTheDocument();
    expect(screen.getByText('Test Author')).toBeInTheDocument();
  });

  it('displays tags correctly', () => {
    render(
      <TemplateCard
        template={mockTemplate}
        onView={mockOnView}
      />
    );

    expect(screen.getByText('test')).toBeInTheDocument();
    expect(screen.getByText('automation')).toBeInTheDocument();
    expect(screen.getByText('unit-test')).toBeInTheDocument();
  });

  it('shows use case when provided', () => {
    render(
      <TemplateCard
        template={mockTemplate}
        onView={mockOnView}
      />
    );

    expect(screen.getByText('💡 Testing template functionality')).toBeInTheDocument();
  });

  it('shows owner badge for owned templates', () => {
    render(
      <TemplateCard
        template={mockTemplate}
        onView={mockOnView}
      />
    );

    expect(screen.getByText('我的模板')).toBeInTheDocument();
  });

  it('does not show owner badge for non-owned templates', () => {
    render(
      <TemplateCard
        template={mockTemplateNotOwner}
        onView={mockOnView}
      />
    );

    expect(screen.queryByText('我的模板')).not.toBeInTheDocument();
  });

  it('calls onView when card is clicked', () => {
    render(
      <TemplateCard
        template={mockTemplate}
        onView={mockOnView}
      />
    );

    fireEvent.click(screen.getByRole('button', { name: /test template/i }));
    expect(mockOnView).toHaveBeenCalledWith(mockTemplate);
  });



  it('shows edit option in dropdown for editable templates', async () => {
    render(
      <TemplateCard
        template={mockTemplate}
        onView={mockOnView}
        onEdit={mockOnEdit}

        showActions={true}
      />
    );

    // Click the more options button
    const moreButton = screen.getByTestId('more-vertical-icon').closest('button');
    fireEvent.click(moreButton!);

    await waitFor(() => {
      expect(screen.getByText('编辑')).toBeInTheDocument();
    });
  });

  it('does not show edit option for non-editable templates', async () => {
    render(
      <TemplateCard
        template={mockTemplateNotOwner}
        onView={mockOnView}
        showActions={true}
      />
    );

    // Click the more options button
    const moreButton = screen.getByTestId('more-vertical-icon').closest('button');
    fireEvent.click(moreButton!);

    await waitFor(() => {
      expect(screen.queryByText('编辑')).not.toBeInTheDocument();
    });
  });

  it('shows delete option for editable templates', async () => {
    render(
      <TemplateCard
        template={mockTemplate}
        onView={mockOnView}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        showActions={true}
      />
    );

    // Click the more options button
    const moreButton = screen.getByTestId('more-vertical-icon').closest('button');
    fireEvent.click(moreButton!);

    await waitFor(() => {
      expect(screen.getByText('删除')).toBeInTheDocument();
    });
  });

  it('calls onEdit when edit is clicked', async () => {
    render(
      <TemplateCard
        template={mockTemplate}
        onView={mockOnView}
        onEdit={mockOnEdit}
        showActions={true}
      />
    );

    // Click the more options button
    const moreButton = screen.getByTestId('more-vertical-icon').closest('button');
    fireEvent.click(moreButton!);

    await waitFor(() => {
      const editButton = screen.getByText('编辑');
      fireEvent.click(editButton);
      expect(mockOnEdit).toHaveBeenCalledWith(mockTemplate);
    });
  });



  it('calls onDelete when delete is clicked', async () => {
    render(
      <TemplateCard
        template={mockTemplate}
        onView={mockOnView}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        showActions={true}
      />
    );

    // Click the more options button
    const moreButton = screen.getByTestId('more-vertical-icon').closest('button');
    fireEvent.click(moreButton!);

    await waitFor(() => {
      const deleteButton = screen.getByText('删除');
      fireEvent.click(deleteButton);
      expect(mockOnDelete).toHaveBeenCalledWith(mockTemplate);
    });
  });

  it('renders in compact mode', () => {
    render(
      <TemplateCard
        template={mockTemplate}
        onView={mockOnView}
        compact={true}
      />
    );

    // In compact mode, use case should not be shown
    expect(screen.queryByText('💡 Testing template functionality')).not.toBeInTheDocument();
    
    // Should still show basic information
    expect(screen.getByText('Test Template')).toBeInTheDocument();
  });

  it('limits tags in compact mode', () => {
    const templateWithManyTags: TemplateListItem = {
      ...mockTemplate,
      tags: ['tag1', 'tag2', 'tag3', 'tag4', 'tag5'],
    };

    render(
      <TemplateCard
        template={templateWithManyTags}
        onView={mockOnView}
        compact={true}
      />
    );

    // Should show first 3 tags plus "+2" indicator
    expect(screen.getByText('tag1')).toBeInTheDocument();
    expect(screen.getByText('tag2')).toBeInTheDocument();
    expect(screen.getByText('tag3')).toBeInTheDocument();
    expect(screen.getByText('+2')).toBeInTheDocument();
    expect(screen.queryByText('tag4')).not.toBeInTheDocument();
  });

  it('handles missing optional data gracefully', () => {
    const minimalTemplate: TemplateListItem = {
      id: 1,
      template_id: 'minimal_123',
      name: 'Minimal Template',
      description: 'Minimal description',
      category: 'other',
      difficulty: 'beginner',
      visibility: 'private',
      status: 'active',
      tags: [],
      usage_count: 0,
      rating_count: 0,
      version: '1.0.0',
      created_at: '2025-01-01T00:00:00Z',
      is_owner: false,
      can_edit: false,
    };

    render(
      <TemplateCard
        template={minimalTemplate}
        onView={mockOnView}
      />
    );

    expect(screen.getByText('Minimal Template')).toBeInTheDocument();
    expect(screen.getByText('0')).toBeInTheDocument(); // usage count
    expect(screen.queryByTestId('star-icon')).not.toBeInTheDocument(); // no rating
  });

  it('does not show actions when showActions is false', () => {
    render(
      <TemplateCard
        template={mockTemplate}
        onView={mockOnView}
        showActions={false}
      />
    );

    expect(screen.queryByTestId('more-vertical-icon')).not.toBeInTheDocument();
  });

  it('formats date correctly', () => {
    render(
      <TemplateCard
        template={mockTemplate}
        onView={mockOnView}
      />
    );

    // Should format the date in Chinese locale
    expect(screen.getByText('2025年1月1日')).toBeInTheDocument();
  });

  it('applies correct difficulty colors', () => {
    const beginnerTemplate: TemplateListItem = {
      ...mockTemplate,
      difficulty: 'beginner',
    };

    render(
      <TemplateCard
        template={beginnerTemplate}
        onView={mockOnView}
      />
    );

    const difficultyBadge = screen.getByText('beginner');
    expect(difficultyBadge).toHaveClass('bg-green-100', 'text-green-800');
  });
});
