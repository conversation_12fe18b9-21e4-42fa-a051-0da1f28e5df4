"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { 
  Search, 
  Star, 
  Users, 
  Calendar,
  Tag,
  TrendingUp,
  X
} from "lucide-react";
import { api } from "@/lib/api";
import { 
  TemplateListItem, 
  TemplateCategory, 
  TemplateDifficulty 
} from "@/lib/types";

interface TemplateSelectorProps {
  onTemplateSelect: (template: TemplateListItem) => void;
  selectedTemplate?: TemplateListItem | null;
  trigger?: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

const getDifficultyColor = (difficulty: TemplateDifficulty): string => {
  switch (difficulty) {
    case "beginner":
      return "bg-green-100 text-green-800 border-green-200";
    case "intermediate":
      return "bg-blue-100 text-blue-800 border-blue-200";
    case "advanced":
      return "bg-orange-100 text-orange-800 border-orange-200";
    case "expert":
      return "bg-red-100 text-red-800 border-red-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
};

const getCategoryIcon = (category: string) => {
  const icons: Record<string, string> = {
    business: "💼",
    technical: "⚙️",
    creative: "🎨",
    analysis: "📊",
    support: "🛠️",
    education: "📚",
    investigation: "🔍",
    consulting: "💡",
    research: "🔬",
    customer_service: "📞",
    marketing: "📢",
    sales: "💰",
    healthcare: "🏥",
    finance: "💳",
    legal: "⚖️",
    other: "📋"
  };
  return icons[category] || "📋";
};

export const TemplateSelector: React.FC<TemplateSelectorProps> = ({
  onTemplateSelect,
  selectedTemplate,
  trigger,
  open,
  onOpenChange
}) => {
  const [templates, setTemplates] = useState<TemplateListItem[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<TemplateListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>("all");
  const [categories, setCategories] = useState<Array<{ value: string; label: string }>>([]);
  const [difficulties, setDifficulties] = useState<Array<{ value: string; label: string }>>([]);

  // Load templates and metadata
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        
        const [templatesRes, categoriesRes, difficultiesRes] = await Promise.all([
          // Use the templates API to get all templates
          api.templates.list({ visibility: "public" }),
          api.templates.getCategories(),
          api.templates.getDifficulties()
        ]);

        // Handle new API response format
        if (templatesRes.data) {
          const templates = Array.isArray(templatesRes.data)
            ? templatesRes.data
            : templatesRes.data.templates || [];
          setTemplates(templates);
          setFilteredTemplates(templates);
        } else if (templatesRes.success && templatesRes.data) {
          // Handle old API response format
          setTemplates(templatesRes.data);
          setFilteredTemplates(templatesRes.data);
        }
        
        if (categoriesRes.success && categoriesRes.data) {
          setCategories(categoriesRes.data);
        }
        
        if (difficultiesRes.success && difficultiesRes.data) {
          setDifficulties(difficultiesRes.data);
        }
      } catch (error) {
        console.error("Failed to load templates:", error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Apply filters
  useEffect(() => {
    let filtered = [...templates];

    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(template => 
        template.name.toLowerCase().includes(query) ||
        template.description.toLowerCase().includes(query) ||
        template.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Category filter
    if (selectedCategory && selectedCategory !== "all") {
      filtered = filtered.filter(template => template.category === selectedCategory);
    }

    // Difficulty filter
    if (selectedDifficulty && selectedDifficulty !== "all") {
      filtered = filtered.filter(template => template.difficulty === selectedDifficulty);
    }

    // Sort by usage count and rating
    filtered.sort((a, b) => {
      const scoreA = (a.rating || 0) * a.usage_count;
      const scoreB = (b.rating || 0) * b.usage_count;
      return scoreB - scoreA;
    });

    setFilteredTemplates(filtered);
  }, [templates, searchQuery, selectedCategory, selectedDifficulty]);

  const handleTemplateSelect = (template: TemplateListItem) => {
    onTemplateSelect(template);
    onOpenChange?.(false);
  };

  const clearFilters = () => {
    setSearchQuery("");
    setSelectedCategory("all");
    setSelectedDifficulty("all");
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "short",
      day: "numeric"
    });
  };

  const renderSkeleton = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {Array.from({ length: 6 }).map((_, index) => (
        <div key={index} className="space-y-3">
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
        </div>
      ))}
    </div>
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>选择模板</DialogTitle>
          <DialogDescription>
            从现有模板中选择一个作为创建Agent的起点
          </DialogDescription>
        </DialogHeader>

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4 py-4 border-b">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索模板..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="分类" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部分类</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category.value} value={category.value}>
                  {category.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={selectedDifficulty} onValueChange={setSelectedDifficulty}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="难度" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部难度</SelectItem>
              {difficulties.map((difficulty) => (
                <SelectItem key={difficulty.value} value={difficulty.value}>
                  {difficulty.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {(searchQuery || (selectedCategory && selectedCategory !== "all") || (selectedDifficulty && selectedDifficulty !== "all")) && (
            <Button variant="outline" onClick={clearFilters}>
              <X className="h-4 w-4 mr-2" />
              清除
            </Button>
          )}
        </div>

        {/* Templates Grid */}
        <div className="flex-1 overflow-y-auto">
          {loading ? (
            renderSkeleton()
          ) : filteredTemplates.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">📋</div>
              <h3 className="text-lg font-semibold mb-2">暂无模板</h3>
              <p className="text-muted-foreground">
                {searchQuery || selectedCategory || selectedDifficulty
                  ? "没有找到符合条件的模板，请尝试调整筛选条件"
                  : "还没有任何公开模板"
                }
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-1">
              {filteredTemplates.map((template) => (
                <Card 
                  key={template.template_id} 
                  className={`cursor-pointer hover:shadow-lg transition-all duration-200 ${
                    selectedTemplate?.template_id === template.template_id 
                      ? "ring-2 ring-primary" 
                      : ""
                  }`}
                  onClick={() => handleTemplateSelect(template)}
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="text-lg">{getCategoryIcon(template.category)}</span>
                          <Badge 
                            variant="outline" 
                            className={getDifficultyColor(template.difficulty)}
                          >
                            {template.difficulty}
                          </Badge>
                        </div>
                        <CardTitle className="text-lg leading-tight mb-1">
                          {template.name}
                        </CardTitle>
                        <CardDescription className="line-clamp-2">
                          {template.description}
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="pt-0">
                    {/* Tags */}
                    {template.tags && template.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1 mb-3">
                        {template.tags.slice(0, 3).map((tag, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            <Tag className="mr-1 h-3 w-3" />
                            {tag}
                          </Badge>
                        ))}
                        {template.tags.length > 3 && (
                          <Badge variant="secondary" className="text-xs">
                            +{template.tags.length - 3}
                          </Badge>
                        )}
                      </div>
                    )}

                    {/* Template readiness info */}
                    {template.team_structure_template && (
                      <div className="flex items-center justify-between text-xs text-muted-foreground mb-3">
                        <span>团队: {template.team_structure_template.team_members?.length || 0} 人</span>
                        <span>流程: {template.team_structure_template.workflow?.steps?.length || 0} 步</span>
                        <Badge
                          variant={template.ready_to_deploy ? "default" : "secondary"}
                          className="text-xs"
                        >
                          {template.ready_to_deploy ? "🚀 即用" : "⚙️ 需配置"}
                        </Badge>
                      </div>
                    )}

                    {/* Stats */}
                    <div className="flex items-center justify-between text-sm text-muted-foreground">
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-1">
                          <Users className="h-4 w-4" />
                          <span>{template.usage_count || 0}</span>
                        </div>
                        {template.rating && (
                          <div className="flex items-center gap-1">
                            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                            <span>{template.rating.toFixed(1)}</span>
                          </div>
                        )}
                        <div className="flex items-center gap-1">
                          <TrendingUp className="h-4 w-4" />
                          <span>v{template.version || "1.0"}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        <span>{formatDate(template.created_at)}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-between items-center pt-4 border-t">
          <div className="text-sm text-muted-foreground">
            {filteredTemplates.length} 个模板
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange?.(false)}>
              取消
            </Button>
            {selectedTemplate && (
              <Button onClick={() => handleTemplateSelect(selectedTemplate)}>
                使用选中模板
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
