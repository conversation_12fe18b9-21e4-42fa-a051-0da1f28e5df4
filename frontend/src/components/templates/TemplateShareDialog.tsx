"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { 
  Copy, 
  Share, 
  Globe, 
  Lock, 
  Users, 
  Star,
  Check
} from "lucide-react";
import { api } from "@/lib/api";
import { Template, TemplateVisibility } from "@/lib/types";

interface TemplateShareDialogProps {
  template: Template;
  isOpen: boolean;
  onClose: () => void;
  onVisibilityChange?: (visibility: TemplateVisibility) => void;
}

const visibilityOptions = [
  {
    value: "private" as TemplateVisibility,
    label: "私有",
    description: "只有您可以查看和使用此模板",
    icon: Lock,
    color: "text-gray-600"
  },
  {
    value: "public" as TemplateVisibility,
    label: "公开",
    description: "所有用户都可以查看和使用此模板",
    icon: Globe,
    color: "text-blue-600"
  },
  {
    value: "shared" as TemplateVisibility,
    label: "共享",
    description: "通过链接分享给特定用户",
    icon: Users,
    color: "text-green-600"
  },
  {
    value: "featured" as TemplateVisibility,
    label: "精选",
    description: "管理员精选的高质量模板",
    icon: Star,
    color: "text-yellow-600"
  }
];

export const TemplateShareDialog: React.FC<TemplateShareDialogProps> = ({
  template,
  isOpen,
  onClose,
  onVisibilityChange
}) => {
  const { toast } = useToast();
  const [selectedVisibility, setSelectedVisibility] = useState<TemplateVisibility>(template.visibility);
  const [loading, setLoading] = useState(false);
  const [copied, setCopied] = useState(false);

  const templateUrl = `${window.location.origin}/templates/${template.template_id}`;

  const handleVisibilityChange = async () => {
    if (selectedVisibility === template.visibility) {
      onClose();
      return;
    }

    try {
      setLoading(true);
      
      const response = await api.templates.share(template.template_id, selectedVisibility);
      
      if (response.success) {
        toast({
          title: "分享设置已更新",
          description: response.data?.message || "模板分享设置已成功更新",
        });
        
        onVisibilityChange?.(selectedVisibility);
        onClose();
      } else {
        throw new Error(response.error?.message || "Failed to update sharing settings");
      }
    } catch (err) {
      toast({
        title: "更新失败",
        description: err instanceof Error ? err.message : "Failed to update sharing settings",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(templateUrl);
      setCopied(true);
      toast({
        title: "链接已复制",
        description: "模板链接已复制到剪贴板",
      });
      
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      toast({
        title: "复制失败",
        description: "无法复制链接到剪贴板",
        variant: "destructive",
      });
    }
  };

  const currentOption = visibilityOptions.find(opt => opt.value === selectedVisibility);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Share className="h-5 w-5" />
            分享模板
          </DialogTitle>
          <DialogDescription>
            设置模板的可见性和分享权限
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Template Info */}
          <div className="p-4 bg-muted rounded-lg">
            <h4 className="font-medium mb-1">{template.name}</h4>
            <p className="text-sm text-muted-foreground line-clamp-2">
              {template.description}
            </p>
          </div>

          {/* Visibility Settings */}
          <div className="space-y-3">
            <Label>可见性设置</Label>
            <Select value={selectedVisibility} onValueChange={(value: TemplateVisibility) => setSelectedVisibility(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {visibilityOptions.map((option) => {
                  const IconComponent = option.icon;
                  return (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        <IconComponent className={`h-4 w-4 ${option.color}`} />
                        <div>
                          <div className="font-medium">{option.label}</div>
                          <div className="text-xs text-muted-foreground">
                            {option.description}
                          </div>
                        </div>
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>

            {/* Current Setting Description */}
            {currentOption && (
              <Alert>
                <currentOption.icon className={`h-4 w-4 ${currentOption.color}`} />
                <AlertDescription>
                  <strong>{currentOption.label}:</strong> {currentOption.description}
                </AlertDescription>
              </Alert>
            )}
          </div>

          {/* Share Link */}
          {(selectedVisibility === "public" || selectedVisibility === "shared" || selectedVisibility === "featured") && (
            <div className="space-y-3">
              <Label>分享链接</Label>
              <div className="flex gap-2">
                <Input
                  value={templateUrl}
                  readOnly
                  className="font-mono text-sm"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCopyLink}
                  className="flex-shrink-0"
                >
                  {copied ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                任何拥有此链接的用户都可以查看和使用此模板
              </p>
            </div>
          )}

          {/* Usage Stats */}
          <div className="grid grid-cols-2 gap-4 text-center">
            <div className="p-3 bg-muted rounded-lg">
              <div className="text-lg font-semibold">{template.usage_count}</div>
              <div className="text-xs text-muted-foreground">使用次数</div>
            </div>
            <div className="p-3 bg-muted rounded-lg">
              <div className="text-lg font-semibold">
                {template.rating ? template.rating.toFixed(1) : "N/A"}
              </div>
              <div className="text-xs text-muted-foreground">
                评分 ({template.rating_count})
              </div>
            </div>
          </div>

          {/* Warning for Featured */}
          {selectedVisibility === "featured" && template.visibility !== "featured" && (
            <Alert>
              <Star className="h-4 w-4" />
              <AlertDescription>
                <strong>注意:</strong> 精选模板需要管理员审核。您的模板将被提交审核，审核通过后才会显示为精选模板。
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            取消
          </Button>
          <Button 
            onClick={handleVisibilityChange} 
            disabled={loading || selectedVisibility === template.visibility}
          >
            {loading ? "更新中..." : "保存设置"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
