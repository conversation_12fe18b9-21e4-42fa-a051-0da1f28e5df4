"use client";

import React from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Bo<PERSON>,
  Users,
  TestTube,
  Edit,
  Trash2,
  Star,
  StarOff,
  MoreHorizontal,
  Play,
  Settings,
  Copy,
  Zap,
  TrendingUp,
  Clock,
} from "lucide-react";
import Link from "next/link";

interface Agent {
  id: string;
  name: string;
  description: string;
  status: string;
  usage_count: number;
  success_rate: number;
  last_used: string;
  team_members: Array<{ role: string; name: string }>;
  tags: string[];
  favorite: boolean;
}

interface NewAgentCardProps {
  agents: Agent[];
  selectedAgents: string[];
  onAgentSelect: (agentId: string) => void;
  onSelectAll: () => void;
}

export function NewAgentCard({ agents, selectedAgents, onAgentSelect, onSelectAll }: NewAgentCardProps) {
  const handleToggleFavorite = (agentId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    // Handle favorite toggle logic here
    console.log("Toggle favorite for agent:", agentId);
  };

  const handleAgentAction = (action: string, agentId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    console.log(`${action} agent:`, agentId);
    // Handle agent actions here
  };

  return (
    <div className="space-y-4">
      {/* Select All */}
      {agents.length > 0 && (
        <div className="flex items-center space-x-2">
          <Checkbox
            checked={selectedAgents.length === agents.length}
            onCheckedChange={onSelectAll}
          />
          <span className="text-sm text-muted-foreground">
            全选 ({agents.length} 个Agent)
          </span>
        </div>
      )}

      {/* Agent Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {agents.map((agent, index) => (
          <motion.div
            key={agent.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: index * 0.05 }}
          >
            <Card className="group hover:shadow-lg transition-all duration-200 hover:border-primary/50 relative">
              {/* Selection Checkbox */}
              <div className="absolute top-4 left-4 z-10">
                <Checkbox
                  checked={selectedAgents.includes(agent.id)}
                  onCheckedChange={() => onAgentSelect(agent.id)}
                  className="bg-background border-2"
                />
              </div>

              {/* Favorite Star */}
              <div className="absolute top-4 right-4 z-10">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 hover:bg-background/80"
                  onClick={(e) => handleToggleFavorite(agent.id, e)}
                >
                  {agent.favorite ? (
                    <Star className="h-4 w-4 text-yellow-500 fill-current" />
                  ) : (
                    <StarOff className="h-4 w-4 text-muted-foreground" />
                  )}
                </Button>
              </div>

              <CardHeader className="pt-12 pb-4">
                <div className="flex items-start space-x-3">
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Bot className="h-6 w-6 text-primary" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <CardTitle className="text-lg group-hover:text-primary transition-colors line-clamp-1">
                      {agent.name}
                    </CardTitle>
                    <div className="flex items-center space-x-2 mt-1">
                      <Badge
                        variant={agent.status === "active" ? "default" : "secondary"}
                        className="text-xs"
                      >
                        {agent.status === "active" ? "活跃" : "未活跃"}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        {agent.last_used}
                      </span>
                    </div>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                {/* Description */}
                <CardDescription className="line-clamp-2">
                  {agent.description}
                </CardDescription>

                {/* Stats */}
                <div className="grid grid-cols-3 gap-3 text-center">
                  <div className="space-y-1">
                    <div className="flex items-center justify-center space-x-1">
                      <Zap className="h-3 w-3 text-blue-500" />
                      <span className="text-sm font-medium">{agent.usage_count}</span>
                    </div>
                    <p className="text-xs text-muted-foreground">使用次数</p>
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center justify-center space-x-1">
                      <TrendingUp className="h-3 w-3 text-green-500" />
                      <span className="text-sm font-medium">{agent.success_rate}%</span>
                    </div>
                    <p className="text-xs text-muted-foreground">成功率</p>
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center justify-center space-x-1">
                      <Users className="h-3 w-3 text-purple-500" />
                      <span className="text-sm font-medium">{agent.team_members.length}</span>
                    </div>
                    <p className="text-xs text-muted-foreground">成员</p>
                  </div>
                </div>

                {/* Tags */}
                <div className="flex flex-wrap gap-1">
                  {agent.tags.slice(0, 3).map((tag, idx) => (
                    <Badge key={idx} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {agent.tags.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{agent.tags.length - 3}
                    </Badge>
                  )}
                </div>

                {/* Actions */}
                <div className="flex items-center justify-between pt-2">
                  <Button asChild size="sm" className="flex-1 mr-2">
                    <Link href={`/newsite/test?agent=${agent.id}`}>
                      <TestTube className="h-4 w-4 mr-1" />
                      测试
                    </Link>
                  </Button>
                  
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={(e) => handleAgentAction("edit", agent.id, e)}>
                        <Edit className="h-4 w-4 mr-2" />
                        编辑
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={(e) => handleAgentAction("duplicate", agent.id, e)}>
                        <Copy className="h-4 w-4 mr-2" />
                        复制
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={(e) => handleAgentAction("settings", agent.id, e)}>
                        <Settings className="h-4 w-4 mr-2" />
                        设置
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        onClick={(e) => handleAgentAction("delete", agent.id, e)}
                        className="text-red-600"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        删除
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  );
}
