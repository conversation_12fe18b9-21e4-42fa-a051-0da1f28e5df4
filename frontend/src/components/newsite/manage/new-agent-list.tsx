"use client";

import React from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { DeleteConfirmDialog } from "@/components/common/confirm-dialog";
import {
  Bot,
  Users,
  TestTube,
  Edit,
  Trash2,
  Star,
  StarOff,
  MoreHorizontal,
  Settings,
  Copy,
  Zap,
  TrendingUp,
  Clock,
} from "lucide-react";
import Link from "next/link";

interface Agent {
  id: string;
  name: string;
  description: string;
  status: string;
  usage_count: number;
  success_rate: number;
  last_used: string;
  team_members: Array<{ role: string; name: string }>;
  tags: string[];
  favorite: boolean;
  created_at: string;
}

interface NewAgentListProps {
  agents: Agent[];
  selectedAgents: string[];
  onAgentSelect: (agentId: string) => void;
  onSelectAll: () => void;
}

export function NewAgentList({ agents, selectedAgents, onAgentSelect, onSelectAll }: NewAgentListProps) {
  const handleToggleFavorite = (agentId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    console.log("Toggle favorite for agent:", agentId);
  };

  const handleAgentAction = (action: string, agentId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    console.log(`${action} agent:`, agentId);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      {agents.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-4">
              <Checkbox
                checked={selectedAgents.length === agents.length}
                onCheckedChange={onSelectAll}
              />
              <div className="grid grid-cols-12 gap-4 flex-1 text-sm font-medium text-muted-foreground">
                <div className="col-span-4">Agent名称</div>
                <div className="col-span-2 text-center">状态</div>
                <div className="col-span-2 text-center">使用次数</div>
                <div className="col-span-2 text-center">成功率</div>
                <div className="col-span-1 text-center">成员</div>
                <div className="col-span-1 text-center">操作</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Agent List */}
      <div className="space-y-2">
        {agents.map((agent, index) => (
          <motion.div
            key={agent.id}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.02 }}
          >
            <Card className="hover:shadow-md transition-all duration-200 hover:border-primary/50">
              <CardContent className="p-4">
                <div className="flex items-center space-x-4">
                  {/* Selection */}
                  <Checkbox
                    checked={selectedAgents.includes(agent.id)}
                    onCheckedChange={() => onAgentSelect(agent.id)}
                  />

                  {/* Content Grid */}
                  <div className="grid grid-cols-12 gap-4 flex-1 items-center">
                    {/* Agent Info */}
                    <div className="col-span-4 flex items-center space-x-3">
                      <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                        <Bot className="h-5 w-5 text-primary" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <h3 className="font-medium truncate">{agent.name}</h3>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0"
                            onClick={(e) => handleToggleFavorite(agent.id, e)}
                          >
                            {agent.favorite ? (
                              <Star className="h-3 w-3 text-yellow-500 fill-current" />
                            ) : (
                              <StarOff className="h-3 w-3 text-muted-foreground" />
                            )}
                          </Button>
                        </div>
                        <p className="text-sm text-muted-foreground truncate">
                          {agent.description}
                        </p>
                        <div className="flex items-center space-x-2 mt-1">
                          {agent.tags.slice(0, 2).map((tag, idx) => (
                            <Badge key={idx} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                          {agent.tags.length > 2 && (
                            <span className="text-xs text-muted-foreground">
                              +{agent.tags.length - 2}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Status */}
                    <div className="col-span-2 text-center">
                      <Badge
                        variant={agent.status === "active" ? "default" : "secondary"}
                        className="text-xs"
                      >
                        {agent.status === "active" ? "活跃" : "未活跃"}
                      </Badge>
                      <p className="text-xs text-muted-foreground mt-1">
                        {agent.last_used}
                      </p>
                    </div>

                    {/* Usage Count */}
                    <div className="col-span-2 text-center">
                      <div className="flex items-center justify-center space-x-1">
                        <Zap className="h-3 w-3 text-blue-500" />
                        <span className="font-medium">{agent.usage_count}</span>
                      </div>
                      <p className="text-xs text-muted-foreground">次调用</p>
                    </div>

                    {/* Success Rate */}
                    <div className="col-span-2 text-center">
                      <div className="flex items-center justify-center space-x-1">
                        <TrendingUp className="h-3 w-3 text-green-500" />
                        <span className="font-medium">{agent.success_rate}%</span>
                      </div>
                      <p className="text-xs text-muted-foreground">成功率</p>
                    </div>

                    {/* Team Members */}
                    <div className="col-span-1 text-center">
                      <div className="flex items-center justify-center space-x-1">
                        <Users className="h-3 w-3 text-purple-500" />
                        <span className="font-medium">{agent.team_members.length}</span>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="col-span-1 text-center">
                      <div className="flex items-center justify-center space-x-1">
                        <Button asChild size="sm" variant="outline" className="h-8">
                          <Link href={`/newsite/test?agent=${agent.id}`}>
                            <TestTube className="h-3 w-3" />
                          </Link>
                        </Button>
                        
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-3 w-3" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={(e) => handleAgentAction("edit", agent.id, e)}>
                              <Edit className="h-4 w-4 mr-2" />
                              编辑
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={(e) => handleAgentAction("duplicate", agent.id, e)}>
                              <Copy className="h-4 w-4 mr-2" />
                              复制
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={(e) => handleAgentAction("settings", agent.id, e)}>
                              <Settings className="h-4 w-4 mr-2" />
                              设置
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DeleteConfirmDialog
                              trigger={
                                <DropdownMenuItem
                                  onSelect={(e) => e.preventDefault()}
                                  className="text-red-600"
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  删除
                                </DropdownMenuItem>
                              }
                              itemName={`${agent.name} (ID: ${agent.id})`}
                              onConfirm={() => handleAgentAction("delete", agent.id)}
                            />
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Expanded Info (Mobile) */}
                <div className="md:hidden mt-4 pt-4 border-t space-y-2">
                  <div className="grid grid-cols-3 gap-4 text-center text-sm">
                    <div>
                      <div className="flex items-center justify-center space-x-1">
                        <Zap className="h-3 w-3 text-blue-500" />
                        <span className="font-medium">{agent.usage_count}</span>
                      </div>
                      <p className="text-xs text-muted-foreground">使用次数</p>
                    </div>
                    <div>
                      <div className="flex items-center justify-center space-x-1">
                        <TrendingUp className="h-3 w-3 text-green-500" />
                        <span className="font-medium">{agent.success_rate}%</span>
                      </div>
                      <p className="text-xs text-muted-foreground">成功率</p>
                    </div>
                    <div>
                      <div className="flex items-center justify-center space-x-1">
                        <Users className="h-3 w-3 text-purple-500" />
                        <span className="font-medium">{agent.team_members.length}</span>
                      </div>
                      <p className="text-xs text-muted-foreground">团队成员</p>
                    </div>
                  </div>
                  <div className="flex justify-center space-x-2">
                    <Button asChild size="sm">
                      <Link href={`/newsite/test?agent=${agent.id}`}>
                        <TestTube className="h-4 w-4 mr-1" />
                        测试
                      </Link>
                    </Button>
                    <Button variant="outline" size="sm" onClick={(e) => handleAgentAction("edit", agent.id, e)}>
                      <Edit className="h-4 w-4 mr-1" />
                      编辑
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  );
}
