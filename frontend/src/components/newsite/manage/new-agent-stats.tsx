"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import {
  Bo<PERSON>,
  Zap,
  TrendingUp,
  Clock,
  Users,
  CheckCircle,
} from "lucide-react";

interface Agent {
  id: string;
  name: string;
  status: string;
  usage_count: number;
  success_rate: number;
  team_members: Array<{ role: string; name: string }>;
  last_used: string;
}

interface NewAgentStatsProps {
  agents: Agent[];
}

export function NewAgentStats({ agents }: NewAgentStatsProps) {
  const totalAgents = agents.length;
  const activeAgents = agents.filter(agent => agent.status === "active").length;
  const totalUsage = agents.reduce((sum, agent) => sum + agent.usage_count, 0);
  const averageSuccessRate = agents.length > 0 
    ? Math.round(agents.reduce((sum, agent) => sum + agent.success_rate, 0) / agents.length)
    : 0;
  const totalTeamMembers = agents.reduce((sum, agent) => sum + agent.team_members.length, 0);
  const recentlyUsed = agents.filter(agent => {
    const lastUsed = agent.last_used;
    return lastUsed.includes("小时前") || lastUsed.includes("分钟前");
  }).length;

  const stats = [
    {
      title: "总Agent数",
      value: totalAgents,
      description: `${activeAgents} 个活跃`,
      icon: Bot,
      color: "text-blue-600",
      bgColor: "bg-blue-100 dark:bg-blue-900/20",
    },
    {
      title: "总使用次数",
      value: totalUsage,
      description: "累计调用",
      icon: Zap,
      color: "text-green-600",
      bgColor: "bg-green-100 dark:bg-green-900/20",
    },
    {
      title: "平均成功率",
      value: `${averageSuccessRate}%`,
      description: "执行成功率",
      icon: TrendingUp,
      color: "text-purple-600",
      bgColor: "bg-purple-100 dark:bg-purple-900/20",
    },
    {
      title: "团队成员",
      value: totalTeamMembers,
      description: "总AI成员数",
      icon: Users,
      color: "text-orange-600",
      bgColor: "bg-orange-100 dark:bg-orange-900/20",
    },
    {
      title: "最近活跃",
      value: recentlyUsed,
      description: "24小时内使用",
      icon: Clock,
      color: "text-indigo-600",
      bgColor: "bg-indigo-100 dark:bg-indigo-900/20",
    },
    {
      title: "活跃率",
      value: `${totalAgents > 0 ? Math.round((activeAgents / totalAgents) * 100) : 0}%`,
      description: "Agent活跃比例",
      icon: CheckCircle,
      color: "text-emerald-600",
      bgColor: "bg-emerald-100 dark:bg-emerald-900/20",
    },
  ];

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
      {stats.map((stat, index) => (
        <Card key={index} className="hover:shadow-md transition-shadow">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`h-5 w-5 ${stat.color}`} />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-muted-foreground truncate">
                  {stat.title}
                </p>
                <p className="text-xl font-bold">
                  {stat.value}
                </p>
                <p className="text-xs text-muted-foreground truncate">
                  {stat.description}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
