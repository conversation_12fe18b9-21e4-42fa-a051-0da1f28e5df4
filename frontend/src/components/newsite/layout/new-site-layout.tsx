"use client";

import React from "react";
import { NewSidebar } from "./new-sidebar";
import { NewHeader } from "./new-header";
import { ProtectedRoute } from "@/components/auth/protected-route";
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar";

interface NewSiteLayoutProps {
  children: React.ReactNode;
  breadcrumbs?: {
    label: string;
    href?: string;
  }[];
}

export function NewSiteLayout({ children, breadcrumbs }: NewSiteLayoutProps) {
  return (
    <ProtectedRoute>
      <SidebarProvider>
        <NewSidebar />
        <SidebarInset>
          <NewHeader breadcrumbs={breadcrumbs} />
          <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
            {children}
          </div>
        </SidebarInset>
      </SidebarProvider>
    </ProtectedRoute>
  );
}
