"use client";

import React from "react";
import { usePathname } from "next/navigation";
import { useAuth } from "@/lib/auth";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar";
import { NewNavMain } from "./new-nav-main";
import { NewNavUser } from "./new-nav-user";
import { NewTeamSwitcher } from "./new-team-switcher";
import {
  Bot,
  Sparkles,
  TestTube,
  FolderOpen,
  Settings,
  BarChart3,
  Home,
  Zap,
  BookTemplate,
  Key,
} from "lucide-react";

// Navigation configuration focused on core Meta-Agent functionality
const getNavigationData = (isAdmin: boolean, pathname: string) => {
  const navItems = [
    {
      title: "概览",
      url: "#",
      icon: Home,
      items: [
        {
          title: "仪表板",
          url: "/newsite",
          description: "查看概览和快速操作",
        },
      ],
    },
    {
      title: "核心功能",
      url: "#",
      icon: Zap,
      items: [
        {
          title: "创建Agent",
          url: "/newsite/create",
          description: "AI驱动的智能团队生成",
          icon: Spark<PERSON>,
        },
        {
          title: "测试Agent",
          url: "/newsite/test",
          description: "实时测试和调试",
          icon: TestTube,
        },
      ],
    },
    {
      title: "管理",
      url: "#",
      icon: FolderOpen,
      items: [
        {
          title: "Agent管理",
          url: "/newsite/manage",
          description: "管理你的AI团队",
          icon: Bot,
        },
        {
          title: "模板库",
          url: "/newsite/templates",
          description: "预制模板和社区分享",
          icon: BookTemplate,
        },
      ],
    },
    {
      title: "设置",
      url: "#",
      icon: Settings,
      items: [
        // System settings only for admin users
        ...(isAdmin ? [{
          title: "系统配置",
          url: "/newsite/settings",
          description: "系统级配置管理",
          icon: Settings,
        }] : []),
        // API keys for all authenticated users
        {
          title: "API密钥",
          url: "/newsite/api-keys",
          description: "管理API访问密钥",
          icon: Key,
        },
      ],
    },
  ];

  // Determine which section should be active based on current pathname
  const navMainWithActiveState = navItems.map(item => ({
    ...item,
    isActive: item.items?.some(subItem => pathname.startsWith(subItem.url)) || false
  }));

  return {
    teams: [
      {
        name: "Meta-Agent",
        logo: Bot,
        plan: "New Interface",
      },
    ],
    navMain: navMainWithActiveState,
  };
};

export function NewSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { user } = useAuth();
  const pathname = usePathname();

  // Check if user is admin
  const isAdmin = user?.role === 'admin';

  // Get navigation data based on user role and current pathname
  const data = getNavigationData(isAdmin, pathname);

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <NewTeamSwitcher teams={data.teams} />
      </SidebarHeader>
      <SidebarContent>
        <NewNavMain items={data.navMain} />
      </SidebarContent>
      <SidebarFooter>
        <NewNavUser />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
