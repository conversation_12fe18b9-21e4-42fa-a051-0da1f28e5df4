"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Sparkles,
  Lightbulb,
  Users,
  Target,
} from "lucide-react";

interface NewAgentFormProps {
  onSubmit: (data: { description: string; options?: any }) => void;
  initialDescription?: string;
  disabled?: boolean;
}

const examplePrompts = [
  {
    title: "内容创作团队",
    description: "我需要一个专业的内容创作团队，能够帮我写文章、制作社交媒体内容和营销文案",
    category: "创作",
  },
  {
    title: "数据分析专家",
    description: "我需要一个数据分析团队，能够处理销售数据、生成报告和提供业务洞察",
    category: "分析",
  },
  {
    title: "客服助手",
    description: "我需要一个智能客服团队，能够回答常见问题、处理投诉和提供技术支持",
    category: "服务",
  },
  {
    title: "产品开发顾问",
    description: "我需要一个产品开发团队，能够帮我进行市场调研、功能设计和用户体验优化",
    category: "开发",
  },
];

export function NewAgentForm({ onSubmit, initialDescription = "", disabled = false }: NewAgentFormProps) {
  const [description, setDescription] = useState(initialDescription);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!description.trim()) return;

    onSubmit({ description: description.trim() });
  };

  const handleExampleClick = (example: typeof examplePrompts[0]) => {
    setDescription(example.description);
  };

  const isValid = description.trim().length >= 10;

  return (
    <div className="space-y-6">
      {/* Main Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5 text-primary" />
            <span>描述您的需求</span>
          </CardTitle>
          <CardDescription>
            详细描述您希望AI团队帮您完成的任务，我们的AI规划师将为您设计最适合的团队配置
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="description" className="text-base font-medium">
                团队任务描述 *
              </Label>
              <Textarea
                id="description"
                placeholder="例如：我需要一个内容创作团队，能够帮我写高质量的技术博客文章，包括研究主题、撰写内容、优化SEO等..."
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                disabled={disabled}
                className="min-h-[120px] resize-none"
                maxLength={1000}
              />
              <div className="flex justify-between items-center text-sm text-muted-foreground">
                <span>
                  {description.length < 10 ? "至少需要10个字符" : "描述越详细，AI规划越精准"}
                </span>
                <span>{description.length}/1000</span>
              </div>
            </div>



            <Button
              type="submit"
              disabled={!isValid || disabled}
              className="w-full min-h-[44px]"
              size="lg"
            >
              <Sparkles className="h-4 w-4 mr-2" />
              开始AI规划
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Example Prompts */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Lightbulb className="h-5 w-5 text-primary" />
            <span>灵感示例</span>
          </CardTitle>
          <CardDescription>
            点击下方示例快速开始，或参考这些例子来描述您的需求
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {examplePrompts.map((example, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <Card
                  className="cursor-pointer hover:shadow-md transition-all duration-200 hover:border-primary/50"
                  onClick={() => handleExampleClick(example)}
                >
                  <CardContent className="p-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">{example.title}</h4>
                        <Badge variant="secondary" className="text-xs">
                          {example.category}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground line-clamp-3">
                        {example.description}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
