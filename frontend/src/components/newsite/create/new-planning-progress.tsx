"use client";

import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import {
  Brain,
  Users,
  Cog,
  CheckCircle,
  X,
  Sparkles,
} from "lucide-react";

interface NewPlanningProgressProps {
  description: string;
  progress?: number;
  onCancel: () => void;
}

const planningSteps = [
  {
    id: "analyze",
    title: "分析需求",
    description: "理解您的具体需求和目标",
    icon: Brain,
    duration: 1000,
  },
  {
    id: "design",
    title: "设计团队",
    description: "规划最适合的团队结构和角色",
    icon: Users,
    duration: 1500,
  },
  {
    id: "configure",
    title: "配置工作流",
    description: "设计高效的协作流程",
    icon: Cog,
    duration: 1000,
  },
  {
    id: "optimize",
    title: "优化方案",
    description: "完善团队配置和参数",
    icon: Sparkles,
    duration: 500,
  },
];

export function NewPlanningProgress({ description, progress, onCancel }: NewPlanningProgressProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [animatedProgress, setAnimatedProgress] = useState(progress || 0);

  useEffect(() => {
    if (progress !== undefined) {
      setAnimatedProgress(progress);
      return;
    }

    // Simulate planning progress
    const timer = setTimeout(() => {
      if (currentStep < planningSteps.length - 1) {
        setCompletedSteps(prev => [...prev, currentStep]);
        setCurrentStep(prev => prev + 1);
        setAnimatedProgress(((currentStep + 1) / planningSteps.length) * 100);
      } else {
        setCompletedSteps(prev => [...prev, currentStep]);
        setAnimatedProgress(100);
      }
    }, planningSteps[currentStep]?.duration || 1000);

    return () => clearTimeout(timer);
  }, [currentStep, progress]);

  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader className="text-center">
        <CardTitle className="flex items-center justify-center space-x-2">
          <Brain className="h-6 w-6 text-primary animate-pulse" />
          <span>AI正在规划您的团队</span>
        </CardTitle>
        <CardDescription>
          我们的AI规划师正在为您设计最适合的团队配置
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="font-medium">规划进度</span>
            <span className="text-muted-foreground">{Math.round(animatedProgress)}%</span>
          </div>
          <Progress value={animatedProgress} className="h-2" />
        </div>

        {/* Current Task Description */}
        <div className="bg-muted/50 rounded-lg p-4">
          <h4 className="font-medium text-sm mb-2">您的需求</h4>
          <p className="text-sm text-muted-foreground line-clamp-3">
            {description}
          </p>
        </div>

        {/* Planning Steps */}
        <div className="space-y-4">
          {planningSteps.map((step, index) => {
            const isCompleted = completedSteps.includes(index);
            const isCurrent = index === currentStep && !isCompleted;
            const isPending = index > currentStep;

            return (
              <motion.div
                key={step.id}
                className={`flex items-center space-x-4 p-3 rounded-lg transition-all duration-300 ${
                  isCompleted
                    ? "bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800"
                    : isCurrent
                    ? "bg-primary/5 border border-primary/20"
                    : "bg-muted/30"
                }`}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <div
                  className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${
                    isCompleted
                      ? "bg-green-500 text-white"
                      : isCurrent
                      ? "bg-primary text-primary-foreground"
                      : "bg-muted text-muted-foreground"
                  }`}
                >
                  {isCompleted ? (
                    <CheckCircle className="h-5 w-5" />
                  ) : (
                    <step.icon className={`h-5 w-5 ${isCurrent ? "animate-pulse" : ""}`} />
                  )}
                </div>
                <div className="flex-1">
                  <h4 className={`font-medium ${isCompleted ? "text-green-700 dark:text-green-300" : ""}`}>
                    {step.title}
                  </h4>
                  <p className="text-sm text-muted-foreground">
                    {step.description}
                  </p>
                </div>
                {isCurrent && (
                  <div className="flex-shrink-0">
                    <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                  </div>
                )}
              </motion.div>
            );
          })}
        </div>

        {/* AI Thinking Animation */}
        <motion.div
          className="text-center py-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.5 }}
        >
          <div className="flex items-center justify-center space-x-1">
            <span className="text-sm text-muted-foreground">AI正在思考</span>
            <div className="flex space-x-1">
              {[0, 1, 2].map((i) => (
                <motion.div
                  key={i}
                  className="w-1 h-1 bg-primary rounded-full"
                  animate={{
                    scale: [1, 1.5, 1],
                    opacity: [0.5, 1, 0.5],
                  }}
                  transition={{
                    duration: 1,
                    repeat: Infinity,
                    delay: i * 0.2,
                  }}
                />
              ))}
            </div>
          </div>
        </motion.div>

        {/* Cancel Button */}
        <div className="flex justify-center pt-4">
          <Button variant="outline" onClick={onCancel} className="min-h-[44px]">
            <X className="h-4 w-4 mr-2" />
            取消规划
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
