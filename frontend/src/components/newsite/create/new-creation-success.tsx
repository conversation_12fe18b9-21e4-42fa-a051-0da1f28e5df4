"use client";

import React from "react";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  CheckCircle,
  Sparkles,
  TestTube,
  Settings,
  ArrowRight,
  Bot,
  Zap,
  Share,
} from "lucide-react";
import Link from "next/link";

interface NewCreationSuccessProps {
  agent: {
    id: string;
    name: string;
    description: string;
    status: string;
  };
  onStartOver: () => void;
}

export function NewCreationSuccess({ agent, onStartOver }: NewCreationSuccessProps) {
  return (
    <div className="max-w-2xl mx-auto space-y-6">
      {/* Success Header */}
      <motion.div
        className="text-center space-y-4"
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.6 }}
      >
        <motion.div
          className="flex justify-center"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.6, delay: 0.2, type: "spring", stiffness: 200 }}
        >
          <div className="w-20 h-20 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
            <CheckCircle className="h-10 w-10 text-green-500" />
          </div>
        </motion.div>
        <div className="space-y-2">
          <h2 className="text-3xl font-bold text-green-700 dark:text-green-300">
            创建成功！
          </h2>
          <p className="text-lg text-muted-foreground">
            您的AI团队已准备就绪，可以开始工作了
          </p>
        </div>
      </motion.div>

      {/* Agent Info Card */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.3 }}
      >
        <Card className="border-green-200 dark:border-green-800">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center space-x-2">
              <Bot className="h-6 w-6 text-primary" />
              <span>{agent.name}</span>
            </CardTitle>
            <CardDescription>{agent.description}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-center space-x-4">
              <Badge variant="default" className="flex items-center space-x-1">
                <Zap className="h-3 w-3" />
                <span>已激活</span>
              </Badge>
              <Badge variant="secondary">
                ID: {agent.id}
              </Badge>
            </div>
            
            <div className="bg-green-50 dark:bg-green-950/20 rounded-lg p-4 text-center">
              <p className="text-sm text-green-700 dark:text-green-300 font-medium">
                🎉 恭喜！您的AI团队已成功部署并可以立即使用
              </p>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="text-center">接下来您可以</CardTitle>
            <CardDescription className="text-center">
              选择下一步操作来充分利用您的新AI团队
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button asChild className="w-full justify-start min-h-[44px]" size="lg">
              <Link href={`/newsite/test?agent=${agent.id}`}>
                <TestTube className="h-4 w-4 mr-3" />
                立即测试团队
                <ArrowRight className="h-4 w-4 ml-auto" />
              </Link>
            </Button>
            
            <Button variant="outline" asChild className="w-full justify-start min-h-[44px]" size="lg">
              <Link href="/newsite/manage">
                <Settings className="h-4 w-4 mr-3" />
                管理所有Agent
                <ArrowRight className="h-4 w-4 ml-auto" />
              </Link>
            </Button>
            
            <Button variant="outline" asChild className="w-full justify-start min-h-[44px]" size="lg">
              <Link href="/newsite/templates">
                <Share className="h-4 w-4 mr-3" />
                分享为模板
                <ArrowRight className="h-4 w-4 ml-auto" />
              </Link>
            </Button>
          </CardContent>
        </Card>
      </motion.div>

      {/* Additional Actions */}
      <motion.div
        className="flex flex-col sm:flex-row justify-center gap-4 pt-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.5 }}
      >
        <Button variant="outline" onClick={onStartOver} className="min-h-[44px]">
          <Sparkles className="h-4 w-4 mr-2" />
          创建另一个Agent
        </Button>
        
        <Button variant="outline" asChild className="min-h-[44px]">
          <Link href="/newsite">
            返回首页
          </Link>
        </Button>
      </motion.div>

      {/* Success Animation */}
      <motion.div
        className="flex justify-center pt-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1, delay: 0.6 }}
      >
        <div className="flex space-x-1">
          {[0, 1, 2, 3, 4].map((i) => (
            <motion.div
              key={i}
              className="w-2 h-2 bg-green-500 rounded-full"
              animate={{
                scale: [1, 1.5, 1],
                opacity: [0.5, 1, 0.5],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                delay: i * 0.1,
              }}
            />
          ))}
        </div>
      </motion.div>
    </div>
  );
}
