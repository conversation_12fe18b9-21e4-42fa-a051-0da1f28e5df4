"use client";

import React from "react";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  CheckCircle,
  Users,
  ArrowRight,
  Edit,
  X,
  Sparkles,
  Target,
  Workflow,
} from "lucide-react";

interface NewPlanReviewProps {
  plan: {
    team_name: string;
    description: string;
    team_members: Array<{
      role: string;
      name: string;
      description: string;
    }>;
    workflow: string[];
  };
  description: string;
  onConfirm: () => void;
  onReject: () => void;
  loading?: boolean;
}

export function NewPlanReview({ plan, description, onConfirm, onReject, loading = false }: NewPlanReviewProps) {
  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <motion.div
        className="text-center space-y-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="flex items-center justify-center space-x-2">
          <CheckCircle className="h-8 w-8 text-green-500" />
          <h2 className="text-2xl font-bold">团队方案已生成</h2>
        </div>
        <p className="text-muted-foreground">
          请查看AI为您设计的团队配置，确认无误后即可创建
        </p>
      </motion.div>

      {/* Original Request */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Target className="h-5 w-5 text-primary" />
              <span>您的原始需求</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">{description}</p>
          </CardContent>
        </Card>
      </motion.div>

      {/* Team Overview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Sparkles className="h-5 w-5 text-primary" />
              <span>团队概览</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="text-xl font-semibold">{plan.team_name}</h3>
              <p className="text-muted-foreground mt-1">{plan.description}</p>
            </div>
            <div className="flex items-center space-x-4 text-sm">
              <Badge variant="secondary" className="flex items-center space-x-1">
                <Users className="h-3 w-3" />
                <span>{plan.team_members.length} 名成员</span>
              </Badge>
              <Badge variant="secondary" className="flex items-center space-x-1">
                <Workflow className="h-3 w-3" />
                <span>{plan.workflow.length} 个步骤</span>
              </Badge>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Team Members */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.3 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="h-5 w-5 text-primary" />
              <span>团队成员</span>
            </CardTitle>
            <CardDescription>
              AI为您配置的专业团队成员
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {plan.team_members.map((member, index) => (
                <motion.div
                  key={index}
                  className="p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.4, delay: 0.1 * index }}
                >
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">{member.name}</h4>
                      <Badge variant="outline" className="text-xs">
                        {member.role}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {member.description}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Workflow */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Workflow className="h-5 w-5 text-primary" />
              <span>工作流程</span>
            </CardTitle>
            <CardDescription>
              团队协作的标准化流程
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {plan.workflow.map((step, index) => (
                <motion.div
                  key={index}
                  className="flex items-center space-x-4"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.4, delay: 0.1 * index }}
                >
                  <div className="flex-shrink-0 w-8 h-8 bg-primary/10 text-primary rounded-full flex items-center justify-center text-sm font-medium">
                    {index + 1}
                  </div>
                  <p className="text-sm">{step}</p>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Action Buttons */}
      <motion.div
        className="flex flex-col sm:flex-row justify-center gap-4 pt-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.5 }}
      >
        <Button
          variant="outline"
          onClick={onReject}
          disabled={loading}
          className="min-h-[44px] w-full sm:w-auto"
        >
          <X className="h-4 w-4 mr-2" />
          重新规划
        </Button>
        <Button
          onClick={onConfirm}
          disabled={loading}
          className="min-h-[44px] w-full sm:w-auto"
        >
          {loading ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
              创建中...
            </>
          ) : (
            <>
              <CheckCircle className="h-4 w-4 mr-2" />
              确认创建
            </>
          )}
        </Button>
      </motion.div>
    </div>
  );
}
