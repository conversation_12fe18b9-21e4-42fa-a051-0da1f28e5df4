"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  History,
  Search,
  Filter,
  CheckCircle,
  AlertCircle,
  Clock,
  Play,
  Eye,
  MoreHorizontal,
} from "lucide-react";

interface TestSession {
  id: string;
  agentId: string;
  agentName: string;
  input: string;
  status: "idle" | "running" | "completed" | "error";
  startTime: Date;
  endTime?: Date;
  results?: any;
  error?: string;
}

interface NewTestHistoryProps {
  history: TestSession[];
  onRerun: (session: TestSession) => void;
}

export function NewTestHistory({ history, onRerun }: NewTestHistoryProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [agentFilter, setAgentFilter] = useState("all");

  // Get unique agents for filter
  const uniqueAgents = Array.from(new Set(history.map(session => session.agentName)));

  // Filter history based on search and filters
  const filteredHistory = history.filter(session => {
    const matchesSearch = session.input.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         session.agentName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || session.status === statusFilter;
    const matchesAgent = agentFilter === "all" || session.agentName === agentFilter;
    
    return matchesSearch && matchesStatus && matchesAgent;
  });

  const formatDuration = (session: TestSession) => {
    if (!session.endTime) return "进行中";
    const duration = session.endTime.getTime() - session.startTime.getTime();
    return `${(duration / 1000).toFixed(1)}s`;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "error":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case "running":
        return <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />;
      default:
        return <Clock className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge variant="default" className="bg-green-500">完成</Badge>;
      case "error":
        return <Badge variant="destructive">失败</Badge>;
      case "running":
        return <Badge variant="secondary">进行中</Badge>;
      default:
        return <Badge variant="outline">未知</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <History className="h-5 w-5 text-primary" />
            <span>测试历史</span>
          </CardTitle>
          <CardDescription>
            查看所有测试记录，重新运行或分析结果
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索测试记录..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-32">
                <SelectValue placeholder="状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部状态</SelectItem>
                <SelectItem value="completed">已完成</SelectItem>
                <SelectItem value="error">失败</SelectItem>
                <SelectItem value="running">进行中</SelectItem>
              </SelectContent>
            </Select>
            <Select value={agentFilter} onValueChange={setAgentFilter}>
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue placeholder="Agent" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部Agent</SelectItem>
                {uniqueAgents.map(agent => (
                  <SelectItem key={agent} value={agent}>{agent}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Results Count */}
          <div className="flex items-center justify-between mb-4">
            <p className="text-sm text-muted-foreground">
              找到 {filteredHistory.length} 条记录
            </p>
            {(searchTerm || statusFilter !== "all" || agentFilter !== "all") && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setSearchTerm("");
                  setStatusFilter("all");
                  setAgentFilter("all");
                }}
              >
                清除筛选
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* History List */}
      {filteredHistory.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
              <History className="h-8 w-8 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-medium mb-2">
              {history.length === 0 ? "还没有测试记录" : "没有找到匹配的记录"}
            </h3>
            <p className="text-muted-foreground mb-4">
              {history.length === 0 
                ? "开始您的第一次Agent测试吧！" 
                : "尝试调整搜索条件或筛选器"
              }
            </p>
            {history.length === 0 && (
              <Button asChild>
                <a href="/newsite/test">
                  <Play className="h-4 w-4 mr-2" />
                  开始测试
                </a>
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredHistory.map((session, index) => (
            <motion.div
              key={session.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: index * 0.05 }}
            >
              <Card className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0 space-y-3">
                      {/* Header */}
                      <div className="flex items-center space-x-3">
                        {getStatusIcon(session.status)}
                        <div className="flex-1 min-w-0">
                          <h3 className="font-medium truncate">{session.agentName}</h3>
                          <p className="text-sm text-muted-foreground">
                            {session.startTime.toLocaleString()}
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          {getStatusBadge(session.status)}
                          <Badge variant="outline" className="text-xs">
                            {formatDuration(session)}
                          </Badge>
                        </div>
                      </div>

                      {/* Input Preview */}
                      <div className="bg-muted/50 rounded-lg p-3">
                        <p className="text-sm line-clamp-2">{session.input}</p>
                      </div>

                      {/* Error Message */}
                      {session.error && (
                        <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
                          <p className="text-sm text-red-700 dark:text-red-300">
                            错误: {session.error}
                          </p>
                        </div>
                      )}
                    </div>

                    {/* Actions */}
                    <div className="flex items-center space-x-2 ml-4">
                      {session.status === "completed" && (
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-1" />
                          查看
                        </Button>
                      )}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onRerun(session)}
                      >
                        <Play className="h-4 w-4 mr-1" />
                        重新运行
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );
}
