"use client";

import React from "react";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  CheckCircle,
  Copy,
  Download,
  Share,
  Clock,
  Zap,
  DollarSign,
  RotateCcw,
  ExternalLink,
} from "lucide-react";

interface TestSession {
  id: string;
  agentId: string;
  agentName: string;
  input: string;
  status: "idle" | "running" | "completed" | "error";
  startTime: Date;
  endTime?: Date;
  results?: {
    output: string;
    metadata: {
      totalDuration: number;
      tokensUsed: number;
      cost: number;
    };
  };
}

interface NewTestResultsProps {
  session: TestSession;
  onNewTest: () => void;
}

export function NewTestResults({ session, onNewTest }: NewTestResultsProps) {
  const { results } = session;
  
  if (!results) return null;

  const formatDuration = (ms: number) => {
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const handleCopyResult = async () => {
    try {
      await navigator.clipboard.writeText(results.output);
      // You could add a toast notification here
    } catch (err) {
      console.error("Failed to copy text:", err);
    }
  };

  const handleDownload = () => {
    const blob = new Blob([results.output], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `test-result-${session.id}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Success Header */}
      <motion.div
        className="text-center space-y-4"
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.6 }}
      >
        <motion.div
          className="flex justify-center"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.6, delay: 0.2, type: "spring", stiffness: 200 }}
        >
          <div className="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>
        </motion.div>
        <div className="space-y-2">
          <h2 className="text-2xl font-bold text-green-700 dark:text-green-300">
            测试完成！
          </h2>
          <p className="text-muted-foreground">
            AI团队已成功完成任务，以下是执行结果
          </p>
        </div>
      </motion.div>

      {/* Test Summary */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.3 }}
      >
        <Card className="border-green-200 dark:border-green-800">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Zap className="h-5 w-5 text-primary" />
              <span>执行摘要</span>
            </CardTitle>
            <CardDescription>
              测试执行的基本信息和性能指标
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <Clock className="h-6 w-6 text-primary mx-auto mb-2" />
                <div className="text-2xl font-bold">
                  {formatDuration(results.metadata.totalDuration)}
                </div>
                <div className="text-sm text-muted-foreground">执行时间</div>
              </div>
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <Zap className="h-6 w-6 text-primary mx-auto mb-2" />
                <div className="text-2xl font-bold">
                  {results.metadata.tokensUsed.toLocaleString()}
                </div>
                <div className="text-sm text-muted-foreground">Token使用</div>
              </div>
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <DollarSign className="h-6 w-6 text-primary mx-auto mb-2" />
                <div className="text-2xl font-bold">
                  ${results.metadata.cost.toFixed(3)}
                </div>
                <div className="text-sm text-muted-foreground">估算成本</div>
              </div>
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <CheckCircle className="h-6 w-6 text-green-500 mx-auto mb-2" />
                <div className="text-2xl font-bold text-green-600">成功</div>
                <div className="text-sm text-muted-foreground">执行状态</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Test Input */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        <Card>
          <CardHeader>
            <CardTitle>测试输入</CardTitle>
            <CardDescription>
              您提交给AI团队的原始任务
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-muted/50 rounded-lg p-4">
              <p className="text-sm">{session.input}</p>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Test Output */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.5 }}
      >
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>执行结果</CardTitle>
                <CardDescription>
                  AI团队协作生成的最终输出
                </CardDescription>
              </div>
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm" onClick={handleCopyResult}>
                  <Copy className="h-4 w-4 mr-1" />
                  复制
                </Button>
                <Button variant="outline" size="sm" onClick={handleDownload}>
                  <Download className="h-4 w-4 mr-1" />
                  下载
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="bg-background border rounded-lg p-4 max-h-96 overflow-y-auto">
              <div className="prose prose-sm dark:prose-invert max-w-none">
                <pre className="whitespace-pre-wrap text-sm leading-relaxed">
                  {results.output}
                </pre>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Action Buttons */}
      <motion.div
        className="flex flex-col sm:flex-row justify-center gap-4 pt-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.6 }}
      >
        <Button onClick={onNewTest} className="min-h-[44px]">
          <RotateCcw className="h-4 w-4 mr-2" />
          新建测试
        </Button>
        
        <Button variant="outline" className="min-h-[44px]">
          <Share className="h-4 w-4 mr-2" />
          分享结果
        </Button>
        
        <Button variant="outline" asChild className="min-h-[44px]">
          <a href="/newsite/manage">
            <ExternalLink className="h-4 w-4 mr-2" />
            管理Agent
          </a>
        </Button>
      </motion.div>

      {/* Success Animation */}
      <motion.div
        className="flex justify-center pt-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1, delay: 0.7 }}
      >
        <div className="flex space-x-1">
          {[0, 1, 2, 3, 4].map((i) => (
            <motion.div
              key={i}
              className="w-2 h-2 bg-green-500 rounded-full"
              animate={{
                scale: [1, 1.5, 1],
                opacity: [0.5, 1, 0.5],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                delay: i * 0.1,
              }}
            />
          ))}
        </div>
      </motion.div>
    </div>
  );
}
