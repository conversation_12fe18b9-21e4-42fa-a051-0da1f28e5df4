"use client";

import React from "react";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Play,
  Square,
  CheckCircle,
  Clock,
  AlertCircle,
  Zap,
  Users,
  RotateCcw,
} from "lucide-react";

interface TestSession {
  id: string;
  agentId: string;
  agentName: string;
  input: string;
  status: "idle" | "running" | "completed" | "error";
  startTime: Date;
  endTime?: Date;
  stages?: Array<{
    name: string;
    status: "pending" | "running" | "completed" | "error";
    output?: string;
    duration?: number;
  }>;
}

interface NewTestExecutionProps {
  session: TestSession;
  onStop: () => void;
  onReset: () => void;
}

export function NewTestExecution({ session, onStop, onReset }: NewTestExecutionProps) {
  const { stages = [] } = session;
  const completedStages = stages.filter(stage => stage.status === "completed").length;
  const progress = (completedStages / stages.length) * 100;
  const currentStage = stages.find(stage => stage.status === "running");
  const isRunning = session.status === "running";

  const formatDuration = (ms: number) => {
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const getElapsedTime = () => {
    const now = session.endTime || new Date();
    const elapsed = now.getTime() - session.startTime.getTime();
    return formatDuration(elapsed);
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Execution Header */}
      <Card className={`border-2 ${
        session.status === "running" ? "border-blue-200 dark:border-blue-800" :
        session.status === "completed" ? "border-green-200 dark:border-green-800" :
        session.status === "error" ? "border-red-200 dark:border-red-800" :
        "border-border"
      }`}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <CardTitle className="flex items-center space-x-2">
                {isRunning ? (
                  <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                ) : session.status === "completed" ? (
                  <CheckCircle className="h-6 w-6 text-green-500" />
                ) : session.status === "error" ? (
                  <AlertCircle className="h-6 w-6 text-red-500" />
                ) : (
                  <Clock className="h-6 w-6 text-muted-foreground" />
                )}
                <span>
                  {isRunning ? "执行中" : 
                   session.status === "completed" ? "执行完成" :
                   session.status === "error" ? "执行失败" : "准备执行"}
                </span>
              </CardTitle>
              <CardDescription>
                Agent: {session.agentName} | 开始时间: {session.startTime.toLocaleTimeString()}
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant={isRunning ? "default" : "secondary"}>
                {getElapsedTime()}
              </Badge>
              {isRunning && (
                <Button variant="outline" size="sm" onClick={onStop}>
                  <Square className="h-4 w-4 mr-1" />
                  停止
                </Button>
              )}
              {!isRunning && (
                <Button variant="outline" size="sm" onClick={onReset}>
                  <RotateCcw className="h-4 w-4 mr-1" />
                  重新测试
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="font-medium">执行进度</span>
              <span className="text-muted-foreground">
                {completedStages}/{stages.length} 步骤完成 ({Math.round(progress)}%)
              </span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>

          {/* Current Task */}
          {currentStage && (
            <motion.div
              className="bg-blue-50 dark:bg-blue-950/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
                <div>
                  <h4 className="font-medium text-blue-700 dark:text-blue-300">
                    正在执行: {currentStage.name}
                  </h4>
                  <p className="text-sm text-blue-600 dark:text-blue-400">
                    AI团队正在协作处理您的请求...
                  </p>
                </div>
              </div>
            </motion.div>
          )}

          {/* Input Display */}
          <div className="bg-muted/50 rounded-lg p-4">
            <h4 className="font-medium text-sm mb-2 flex items-center space-x-2">
              <Users className="h-4 w-4" />
              <span>测试输入</span>
            </h4>
            <p className="text-sm text-muted-foreground">{session.input}</p>
          </div>
        </CardContent>
      </Card>

      {/* Execution Stages */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Zap className="h-5 w-5 text-primary" />
            <span>执行阶段</span>
          </CardTitle>
          <CardDescription>
            AI团队的协作执行过程
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {stages.map((stage, index) => {
              const isCompleted = stage.status === "completed";
              const isCurrent = stage.status === "running";
              const isError = stage.status === "error";
              const isPending = stage.status === "pending";

              return (
                <motion.div
                  key={index}
                  className={`flex items-start space-x-4 p-4 rounded-lg transition-all duration-300 ${
                    isCompleted
                      ? "bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800"
                      : isCurrent
                      ? "bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800"
                      : isError
                      ? "bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800"
                      : "bg-muted/30"
                  }`}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                >
                  <div
                    className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${
                      isCompleted
                        ? "bg-green-500 text-white"
                        : isCurrent
                        ? "bg-blue-500 text-white"
                        : isError
                        ? "bg-red-500 text-white"
                        : "bg-muted text-muted-foreground"
                    }`}
                  >
                    {isCompleted ? (
                      <CheckCircle className="h-5 w-5" />
                    ) : isCurrent ? (
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    ) : isError ? (
                      <AlertCircle className="h-5 w-5" />
                    ) : (
                      <span className="text-sm font-medium">{index + 1}</span>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h4 className={`font-medium ${
                        isCompleted ? "text-green-700 dark:text-green-300" :
                        isCurrent ? "text-blue-700 dark:text-blue-300" :
                        isError ? "text-red-700 dark:text-red-300" : ""
                      }`}>
                        {stage.name}
                      </h4>
                      {stage.duration && (
                        <Badge variant="outline" className="text-xs">
                          {formatDuration(stage.duration)}
                        </Badge>
                      )}
                    </div>
                    {stage.output && (
                      <p className="text-sm text-muted-foreground mt-1">
                        {stage.output}
                      </p>
                    )}
                    {isCurrent && (
                      <p className="text-sm text-blue-600 dark:text-blue-400 mt-1">
                        正在处理中...
                      </p>
                    )}
                  </div>
                </motion.div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Real-time Status */}
      <motion.div
        className="text-center py-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.5 }}
      >
        {isRunning && (
          <div className="flex items-center justify-center space-x-2 text-sm text-muted-foreground">
            <span>AI团队正在协作中</span>
            <div className="flex space-x-1">
              {[0, 1, 2].map((i) => (
                <motion.div
                  key={i}
                  className="w-1 h-1 bg-primary rounded-full"
                  animate={{
                    scale: [1, 1.5, 1],
                    opacity: [0.5, 1, 0.5],
                  }}
                  transition={{
                    duration: 1,
                    repeat: Infinity,
                    delay: i * 0.2,
                  }}
                />
              ))}
            </div>
          </div>
        )}
      </motion.div>
    </div>
  );
}
