"use client";

import React from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Bo<PERSON>,
  <PERSON>,
  Play,
  Sparkles,
  ArrowR<PERSON>,
} from "lucide-react";

interface Agent {
  id: string;
  name: string;
  description: string;
  status: string;
  team_members: Array<{
    role: string;
    name: string;
  }>;
}

interface NewAgentSelectorProps {
  agents: Agent[];
  onSelect: (agent: Agent) => void;
}

export function NewAgentSelector({ agents, onSelect }: NewAgentSelectorProps) {
  return (
    <Card>
      <CardHeader className="text-center">
        <CardTitle className="flex items-center justify-center space-x-2">
          <Bot className="h-6 w-6 text-primary" />
          <span>选择要测试的Agent</span>
        </CardTitle>
        <CardDescription>
          选择一个AI团队开始测试，或者先创建一个新的Agent
        </CardDescription>
      </CardHeader>
      <CardContent>
        {agents.length === 0 ? (
          <div className="text-center py-12 space-y-4">
            <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto">
              <Bot className="h-8 w-8 text-muted-foreground" />
            </div>
            <div className="space-y-2">
              <h3 className="text-lg font-medium">还没有Agent</h3>
              <p className="text-muted-foreground">
                您还没有创建任何AI团队，先创建一个吧！
              </p>
            </div>
            <Button asChild>
              <a href="/newsite/create">
                <Sparkles className="h-4 w-4 mr-2" />
                创建第一个Agent
              </a>
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {agents.map((agent, index) => (
              <motion.div
                key={agent.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <Card
                  className="cursor-pointer hover:shadow-md transition-all duration-200 hover:border-primary/50 group"
                  onClick={() => onSelect(agent)}
                >
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      {/* Agent Header */}
                      <div className="flex items-start justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                            <Bot className="h-5 w-5 text-primary" />
                          </div>
                          <div className="flex-1">
                            <h3 className="font-semibold group-hover:text-primary transition-colors">
                              {agent.name}
                            </h3>
                            <p className="text-sm text-muted-foreground line-clamp-2">
                              {agent.description}
                            </p>
                          </div>
                        </div>
                        <Badge
                          variant={agent.status === "active" ? "default" : "secondary"}
                          className="text-xs"
                        >
                          {agent.status === "active" ? "活跃" : "未活跃"}
                        </Badge>
                      </div>

                      {/* Team Members */}
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                          <Users className="h-4 w-4" />
                          <span>团队成员 ({agent.team_members.length})</span>
                        </div>
                        <div className="flex flex-wrap gap-1">
                          {agent.team_members.slice(0, 3).map((member, idx) => (
                            <Badge key={idx} variant="outline" className="text-xs">
                              {member.role}
                            </Badge>
                          ))}
                          {agent.team_members.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{agent.team_members.length - 3}
                            </Badge>
                          )}
                        </div>
                      </div>

                      {/* Action Button */}
                      <div className="flex justify-between items-center pt-2">
                        <span className="text-xs text-muted-foreground">
                          点击选择此Agent
                        </span>
                        <div className="flex items-center space-x-1 text-primary group-hover:translate-x-1 transition-transform">
                          <Play className="h-4 w-4" />
                          <ArrowRight className="h-4 w-4" />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        )}

        {/* Quick Actions */}
        {agents.length > 0 && (
          <div className="flex justify-center pt-6 border-t mt-6">
            <Button variant="outline" asChild>
              <a href="/newsite/create">
                <Sparkles className="h-4 w-4 mr-2" />
                创建新Agent
              </a>
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
