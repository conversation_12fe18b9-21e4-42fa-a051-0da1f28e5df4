"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Bot,
  Play,
  Settings,
  ChevronDown,
  Users,
  Zap,
  Key,
  RotateCcw,
} from "lucide-react";

interface NewTestFormProps {
  agent: {
    id: string;
    name: string;
    description: string;
    team_members: Array<{
      role: string;
      name: string;
    }>;
  };
  onStart: (input: string, config?: any) => void;
  onAgentChange: () => void;
  disabled?: boolean;
}

const exampleInputs = [
  "帮我写一篇关于人工智能发展趋势的技术博客文章",
  "分析我们公司上个月的销售数据，找出增长机会",
  "为我们的新产品设计一个营销策略",
  "回答客户关于产品退换货政策的常见问题",
];

export function NewTestForm({ agent, onStart, onAgentChange, disabled = false }: NewTestFormProps) {
  const [input, setInput] = useState("");
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [aiModel, setAiModel] = useState("gpt-4");
  const [customModelName, setCustomModelName] = useState("");
  const [customBaseUrl, setCustomBaseUrl] = useState("");
  const [temperature, setTemperature] = useState("0.7");
  const [apiKey, setApiKey] = useState("default");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim()) return;

    const config = showAdvanced ? {
      model: customModelName.trim() || aiModel,
      baseUrl: customBaseUrl.trim() || undefined,
      temperature: parseFloat(temperature),
      apiKey: apiKey === "default" ? undefined : apiKey,
    } : undefined;

    onStart(input.trim(), config);
  };

  const handleExampleClick = (example: string) => {
    setInput(example);
  };

  const isValid = input.trim().length >= 5;

  return (
    <div className="space-y-6">
      {/* Selected Agent Info */}
      <Card className="border-primary/20">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                <Bot className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h3 className="font-semibold">{agent.name}</h3>
                <p className="text-sm text-muted-foreground">{agent.description}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="flex items-center space-x-1">
                <Users className="h-3 w-3" />
                <span>{agent.team_members.length} 成员</span>
              </Badge>
              <Button variant="outline" size="sm" onClick={onAgentChange}>
                <RotateCcw className="h-4 w-4 mr-1" />
                切换
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Input Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Play className="h-5 w-5 text-primary" />
            <span>测试输入</span>
          </CardTitle>
          <CardDescription>
            输入您希望AI团队处理的任务或问题
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="test-input" className="text-base font-medium">
                任务描述 *
              </Label>
              <Textarea
                id="test-input"
                placeholder="例如：帮我写一篇关于人工智能发展趋势的技术博客文章..."
                value={input}
                onChange={(e) => setInput(e.target.value)}
                disabled={disabled}
                className="min-h-[100px] resize-none"
                maxLength={2000}
              />
              <div className="flex justify-between items-center text-sm text-muted-foreground">
                <span>
                  {input.length < 5 ? "至少需要5个字符" : "描述越详细，结果越精准"}
                </span>
                <span>{input.length}/2000</span>
              </div>
            </div>

            {/* Advanced Configuration */}
            <Collapsible open={showAdvanced} onOpenChange={setShowAdvanced}>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                  <span className="flex items-center space-x-2">
                    <Settings className="h-4 w-4" />
                    <span>高级配置</span>
                  </span>
                  <ChevronDown className={`h-4 w-4 transition-transform ${showAdvanced ? "rotate-180" : ""}`} />
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="space-y-4 pt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="ai-model">AI模型覆盖</Label>
                    <Select value={aiModel} onValueChange={setAiModel}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="gpt-4">GPT-4</SelectItem>
                        <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                        <SelectItem value="claude-3">Claude 3</SelectItem>
                        <SelectItem value="custom">自定义模型</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="temperature">创造性</Label>
                    <Select value={temperature} onValueChange={setTemperature}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="0.3">保守 (0.3)</SelectItem>
                        <SelectItem value="0.7">平衡 (0.7)</SelectItem>
                        <SelectItem value="1.0">创新 (1.0)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Custom Model Name */}
                <div className="space-y-2">
                  <Label htmlFor="custom-model">自定义模型名称 (可选)</Label>
                  <Input
                    id="custom-model"
                    value={customModelName}
                    onChange={(e) => setCustomModelName(e.target.value)}
                    placeholder="例如：gpt-4-turbo-preview, claude-3-opus-20240229"
                    className="w-full"
                  />
                  <p className="text-xs text-muted-foreground">
                    如果填写，将覆盖上面选择的模型。支持任何自定义模型名称。
                  </p>
                </div>

                {/* Custom Base URL */}
                <div className="space-y-2">
                  <Label htmlFor="custom-base-url">自定义API基础URL (可选)</Label>
                  <Input
                    id="custom-base-url"
                    value={customBaseUrl}
                    onChange={(e) => setCustomBaseUrl(e.target.value)}
                    placeholder="例如：https://api.openai.com/v1, https://your-proxy.com/v1"
                    className="w-full"
                  />
                  <p className="text-xs text-muted-foreground">
                    自定义API端点地址，支持代理服务器或自托管解决方案。
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="api-key" className="flex items-center space-x-2">
                    <Key className="h-4 w-4" />
                    <span>AI密钥 (可选)</span>
                  </Label>
                  <Select value={apiKey} onValueChange={setApiKey}>
                    <SelectTrigger>
                      <SelectValue placeholder="使用默认密钥或选择自定义密钥" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="default">使用默认密钥</SelectItem>
                      <SelectItem value="key1">OpenAI Key 1</SelectItem>
                      <SelectItem value="key2">Claude Key 1</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CollapsibleContent>
            </Collapsible>

            <Button
              type="submit"
              disabled={!isValid || disabled}
              className="w-full min-h-[44px]"
              size="lg"
            >
              {disabled ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  测试进行中...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  开始测试
                </>
              )}
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Example Inputs */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Zap className="h-5 w-5 text-primary" />
            <span>示例输入</span>
          </CardTitle>
          <CardDescription>
            点击下方示例快速开始测试
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {exampleInputs.map((example, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <Card
                  className="cursor-pointer hover:shadow-sm transition-all duration-200 hover:border-primary/50"
                  onClick={() => handleExampleClick(example)}
                >
                  <CardContent className="p-3">
                    <p className="text-sm line-clamp-2">{example}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
