"use client"

import { useState, useEffect } from 'react'
import { useAuth } from '@/lib/auth'
import { useRouter, useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Checkbox } from '@/components/ui/checkbox'
import { EyeIcon, EyeOffIcon, Shield, ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import { ThemeToggle } from '@/components/ui/theme-toggle'
import TwoFactorAPI from '@/lib/api/two-factor'
import { TwoFactorVerificationDialog } from './two-factor-verification-dialog'
import { useTwoFactorVerification } from '@/hooks/use-two-factor-verification'

export function LoginForm() {
  const { login, isLoading } = useAuth()
  const router = useRouter()
  const searchParams = useSearchParams()
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [error, setError] = useState('')
  const [rememberMe, setRememberMe] = useState(false)
  const [redirectTo, setRedirectTo] = useState('/')
  const [debugInfo, setDebugInfo] = useState('')

  // Get redirect URL from search params
  useEffect(() => {
    const redirect = searchParams.get('redirect') || searchParams.get('returnUrl') || '/'
    setRedirectTo(redirect)
    const debug = `Redirect URL set to: ${redirect}`
    console.log(debug)
    setDebugInfo(debug)
  }, [searchParams])

  // 2FA verification hook
  const twoFactorVerification = useTwoFactorVerification({
    onSuccess: (result) => {
      // Store the token and user data with correct keys
      localStorage.setItem('auth_token', result.tokens.access_token)
      localStorage.setItem('user_data', JSON.stringify(result.user))

      console.log('2FA login successful, redirecting to:', redirectTo)
      console.log('Using router.replace with:', redirectTo)
      router.replace(redirectTo)
    },
    onError: (error) => {
      console.error('2FA verification error:', error)
    },
    verifyFunction: async (tempSessionId, data) => {
      return await TwoFactorAPI.verifyLogin({
        temp_session_id: tempSessionId,
        totp_code: data.totp_code,
        backup_code: data.backup_code,
        remember_me: rememberMe,
      })
    },
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')

    const debugMsg = `Login form submitted: email=${email}, redirectTo=${redirectTo}`
    console.log(debugMsg)
    setDebugInfo(debugMsg)

    try {
      // Try login - this will either complete login or return 2FA requirement
      const response = await login(email, password, rememberMe)
      console.log('Login response:', response)

      // Check if response indicates 2FA is required
      if (response && typeof response === 'object' && 'requires_2fa' in response) {
        console.log('2FA required, opening verification dialog')
        setDebugInfo('2FA required, opening verification dialog')
        twoFactorVerification.actions.openVerification(response.user_id)
      } else {
        // Normal login successful
        const successMsg = `Login successful, redirecting to: ${redirectTo}`
        console.log(successMsg)
        setDebugInfo(successMsg)
        console.log('Using router.replace with:', redirectTo)
        router.replace(redirectTo)
      }
    } catch (err: any) {
      console.error('Login error:', err)
      const errorMsg = err instanceof Error ? err.message : 'Login failed'
      setError(errorMsg)
      setDebugInfo(`Login error: ${errorMsg}`)
    }
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-background px-4 md:px-0">
      <div className="absolute top-4 right-4">
        <ThemeToggle />
      </div>
      <Card className="w-full max-w-md mobile-modal">
        <CardHeader className="space-y-1 pb-4 md:pb-6">
          <div className="flex items-center justify-center mb-3 md:mb-4">
            <div className="flex h-10 md:h-12 w-10 md:w-12 items-center justify-center rounded-lg bg-primary text-primary-foreground">
              <span className="text-base md:text-lg font-bold">M</span>
            </div>
          </div>
          <CardTitle className="text-xl md:text-2xl text-center">Welcome back</CardTitle>
          <CardDescription className="text-center text-sm md:text-base mobile-text-base">
            Sign in to your Meta-Agent account
          </CardDescription>
        </CardHeader>
        <CardContent className="mobile-card-spacing">
          <form onSubmit={handleSubmit} className="space-y-3 md:space-y-4">
            {error && (
              <Alert variant="destructive">
                <AlertDescription className="text-sm mobile-text-sm">{error}</AlertDescription>
              </Alert>
            )}

            {debugInfo && (
              <Alert>
                <AlertDescription className="text-sm mobile-text-sm">Debug: {debugInfo}</AlertDescription>
              </Alert>
            )}

            <div className="space-y-2">
              <Label htmlFor="email" className="text-sm mobile-text-sm">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="mobile-form-element"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="password" className="text-sm mobile-text-sm">Password</Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="demo123"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className="mobile-form-element pr-10"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent touch-target"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOffIcon className="h-4 w-4" />
                  ) : (
                    <EyeIcon className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="remember-me"
                checked={rememberMe}
                onCheckedChange={(checked) => setRememberMe(checked === true)}
              />
              <Label htmlFor="remember-me" className="text-sm mobile-text-sm">
                Remember me
              </Label>
            </div>

            <Button
              type="submit"
              className="w-full touch-target min-h-[44px] md:min-h-[36px] mobile-button-spacing"
              disabled={isLoading}
            >
              {isLoading ? 'Signing in...' : 'Sign in'}
            </Button>
          </form>

          <div className="mt-4 md:mt-6 text-center text-xs md:text-sm mobile-text-sm">
            <span className="text-muted-foreground">还没有账户？</span>
            <Button variant="link" className="p-0 ml-1 touch-target" asChild>
              <Link href="/register">立即注册</Link>
            </Button>
          </div>

          <div className="mt-3 md:mt-4 text-center text-xs md:text-sm text-muted-foreground mobile-text-sm">
            <p>Demo credentials:</p>
            <p>Email: <EMAIL></p>
            <p>Password: demo123</p>
          </div>
        </CardContent>
      </Card>

      {/* 2FA Verification Dialog */}
      <TwoFactorVerificationDialog
        open={twoFactorVerification.state.isOpen}
        onOpenChange={twoFactorVerification.actions.closeVerification}
        onVerify={twoFactorVerification.actions.verify}
        loading={twoFactorVerification.state.loading}
        error={twoFactorVerification.state.error || undefined}
        title="Complete Sign In"
        description="Enter your verification code to complete sign in"
      />
    </div>
  )
}
