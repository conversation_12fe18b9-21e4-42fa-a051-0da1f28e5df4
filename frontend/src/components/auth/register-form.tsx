"use client"

import { useState } from 'react'
import { useAuth } from '@/lib/auth'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import { EyeIcon, EyeOffIcon, CheckIcon, XIcon } from 'lucide-react'
import Link from 'next/link'

interface PasswordStrength {
  score: number
  feedback: string[]
  isValid: boolean
}

function calculatePasswordStrength(password: string): PasswordStrength {
  const feedback: string[] = []
  let score = 0

  if (password.length >= 8) {
    score += 25
  } else {
    feedback.push('至少8个字符')
  }

  if (/[a-z]/.test(password)) {
    score += 25
  } else {
    feedback.push('包含小写字母')
  }

  if (/[A-Z]/.test(password)) {
    score += 25
  } else {
    feedback.push('包含大写字母')
  }

  if (/[0-9]/.test(password)) {
    score += 25
  } else {
    feedback.push('包含数字')
  }

  return {
    score,
    feedback,
    isValid: score >= 75
  }
}

export function RegisterForm() {
  const { register, isLoading } = useAuth()
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: ''
  })
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [error, setError] = useState('')
  const [isRegistered, setIsRegistered] = useState(false)

  const passwordStrength = calculatePasswordStrength(formData.password)
  const passwordsMatch = formData.password === formData.confirmPassword && formData.confirmPassword !== ''

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')

    // Validate form
    if (!formData.name.trim()) {
      setError('请输入姓名')
      return
    }

    if (!formData.email.trim()) {
      setError('请输入邮箱')
      return
    }

    if (!passwordStrength.isValid) {
      setError('密码强度不够')
      return
    }

    if (!passwordsMatch) {
      setError('密码不匹配')
      return
    }

    try {
      await register(formData)
      setIsRegistered(true)
    } catch (err) {
      setError(err instanceof Error ? err.message : '注册失败')
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  if (isRegistered) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-background px-4 md:px-0">
        <Card className="w-full max-w-md mobile-modal">
          <CardHeader className="space-y-1 text-center pb-4 md:pb-6">
            <div className="flex items-center justify-center mb-3 md:mb-4">
              <div className="flex h-10 md:h-12 w-10 md:w-12 items-center justify-center rounded-lg bg-green-500 text-white">
                <CheckIcon className="h-5 md:h-6 w-5 md:w-6" />
              </div>
            </div>
            <CardTitle className="text-xl md:text-2xl">注册成功！</CardTitle>
            <CardDescription className="text-sm md:text-base mobile-text-base">
              欢迎加入 Meta-Agent！您的账户已创建成功。
            </CardDescription>
          </CardHeader>
          <CardContent className="mobile-card-spacing">
            <div className="space-y-3 md:space-y-4 text-center">
              <p className="text-xs md:text-sm text-muted-foreground mobile-text-sm">
                您现在可以开始使用 Meta-Agent 创建和管理您的 AI Agent 了。
              </p>
              <Button
                className="w-full touch-target min-h-[44px] md:min-h-[36px] mobile-button-spacing"
                asChild
              >
                <Link href="/">
                  开始使用
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-background px-4 md:px-0">
      <Card className="w-full max-w-md mobile-modal">
        <CardHeader className="space-y-1 pb-4 md:pb-6">
          <div className="flex items-center justify-center mb-3 md:mb-4">
            <div className="flex h-10 md:h-12 w-10 md:w-12 items-center justify-center rounded-lg bg-primary text-primary-foreground">
              <span className="text-base md:text-lg font-bold">M</span>
            </div>
          </div>
          <CardTitle className="text-xl md:text-2xl text-center">创建账户</CardTitle>
          <CardDescription className="text-center text-sm md:text-base mobile-text-base">
            加入 Meta-Agent，开始您的 AI Agent 之旅
          </CardDescription>
        </CardHeader>
        <CardContent className="mobile-card-spacing">
          <form onSubmit={handleSubmit} className="space-y-3 md:space-y-4">
            {error && (
              <Alert variant="destructive">
                <AlertDescription className="text-sm mobile-text-sm">{error}</AlertDescription>
              </Alert>
            )}

            <div className="space-y-2">
              <Label htmlFor="name" className="text-sm mobile-text-sm">姓名</Label>
              <Input
                id="name"
                type="text"
                placeholder="请输入您的姓名"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                required
                className="mobile-form-element"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="email" className="text-sm mobile-text-sm">邮箱</Label>
              <Input
                id="email"
                type="email"
                placeholder="请输入您的邮箱"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                required
                className="mobile-form-element"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="password" className="text-sm mobile-text-sm">密码</Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="请输入密码"
                  value={formData.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  required
                  className="mobile-form-element pr-10"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent touch-target"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOffIcon className="h-4 w-4" />
                  ) : (
                    <EyeIcon className="h-4 w-4" />
                  )}
                </Button>
              </div>

              {formData.password && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-xs md:text-sm mobile-text-sm">
                    <span>密码强度</span>
                    <span className={passwordStrength.isValid ? 'text-green-600' : 'text-red-600'}>
                      {passwordStrength.score >= 75 ? '强' : passwordStrength.score >= 50 ? '中' : '弱'}
                    </span>
                  </div>
                  <Progress value={passwordStrength.score} className="h-2" />
                  {passwordStrength.feedback.length > 0 && (
                    <ul className="text-xs text-muted-foreground space-y-1 mobile-text-sm">
                      {passwordStrength.feedback.map((item, index) => (
                        <li key={index} className="flex items-center gap-1">
                          <XIcon className="h-3 w-3 text-red-500" />
                          {item}
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmPassword" className="text-sm mobile-text-sm">确认密码</Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  type={showConfirmPassword ? 'text' : 'password'}
                  placeholder="请再次输入密码"
                  value={formData.confirmPassword}
                  onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                  required
                  className="mobile-form-element pr-10"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent touch-target"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <EyeOffIcon className="h-4 w-4" />
                  ) : (
                    <EyeIcon className="h-4 w-4" />
                  )}
                </Button>
              </div>

              {formData.confirmPassword && (
                <div className="flex items-center gap-1 text-xs mobile-text-sm">
                  {passwordsMatch ? (
                    <>
                      <CheckIcon className="h-3 w-3 text-green-500" />
                      <span className="text-green-600">密码匹配</span>
                    </>
                  ) : (
                    <>
                      <XIcon className="h-3 w-3 text-red-500" />
                      <span className="text-red-600">密码不匹配</span>
                    </>
                  )}
                </div>
              )}
            </div>

            <Button
              type="submit"
              className="w-full touch-target min-h-[44px] md:min-h-[36px] mobile-button-spacing"
              disabled={isLoading || !passwordStrength.isValid || !passwordsMatch}
            >
              {isLoading ? '注册中...' : '创建账户'}
            </Button>
          </form>

          <div className="mt-4 md:mt-6 text-center text-xs md:text-sm mobile-text-sm">
            <span className="text-muted-foreground">已有账户？</span>
            <Button variant="link" className="p-0 ml-1 touch-target" asChild>
              <Link href="/login">立即登录</Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
