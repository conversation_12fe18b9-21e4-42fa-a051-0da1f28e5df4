"use client";

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Shield, AlertTriangle, ArrowLeft } from 'lucide-react';
import { TwoFactorErrorHandler } from './two-factor-error-handler';

interface TwoFactorVerificationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onVerify: (data: { totp_code?: string; backup_code?: string }) => Promise<void>;
  loading?: boolean;
  error?: string;
  title?: string;
  description?: string;
  showBackButton?: boolean;
  onBack?: () => void;
}

export function TwoFactorVerificationDialog({
  open,
  onOpenChange,
  onVerify,
  loading = false,
  error,
  title = "Two-Factor Authentication",
  description = "Enter your verification code to continue",
  showBackButton = false,
  onBack,
}: TwoFactorVerificationDialogProps) {
  const [totpCode, setTotpCode] = useState('');
  const [backupCode, setBackupCode] = useState('');
  const [useBackupCode, setUseBackupCode] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!totpCode && !backupCode) {
      return;
    }

    try {
      await onVerify({
        totp_code: totpCode || undefined,
        backup_code: backupCode || undefined,
      });
      
      // Reset form on success
      setTotpCode('');
      setBackupCode('');
      setUseBackupCode(false);
    } catch (error) {
      // Error handling is done by parent component
    }
  };

  const handleToggleBackupCode = () => {
    setUseBackupCode(!useBackupCode);
    setTotpCode('');
    setBackupCode('');
  };

  const handleClose = () => {
    setTotpCode('');
    setBackupCode('');
    setUseBackupCode(false);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-primary" />
            {title}
          </DialogTitle>
          <DialogDescription>
            {description}
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <TwoFactorErrorHandler
              error={error}
              onRetry={() => {
                setTotpCode('');
                setBackupCode('');
              }}
            />
          )}

          {!useBackupCode ? (
            <div className="space-y-2">
              <Label htmlFor="totp-code">
                Authenticator Code
              </Label>
              <Input
                id="totp-code"
                type="text"
                placeholder="Enter 6-digit code"
                value={totpCode}
                onChange={(e) => setTotpCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                maxLength={6}
                className="text-center text-lg tracking-widest"
                autoFocus
              />
              <p className="text-xs text-muted-foreground">
                Enter the 6-digit code from your authenticator app
              </p>
            </div>
          ) : (
            <div className="space-y-2">
              <Label htmlFor="backup-code">
                Backup Code
              </Label>
              <Input
                id="backup-code"
                type="text"
                placeholder="Enter 8-character backup code"
                value={backupCode}
                onChange={(e) => setBackupCode(e.target.value.toUpperCase().slice(0, 8))}
                maxLength={8}
                className="text-center text-lg tracking-widest"
                autoFocus
              />
              <p className="text-xs text-muted-foreground">
                Enter one of your 8-character backup codes
              </p>
            </div>
          )}

          <div className="text-center">
            <Button
              type="button"
              variant="link"
              size="sm"
              onClick={handleToggleBackupCode}
              className="text-xs"
            >
              {useBackupCode 
                ? "Use authenticator app instead" 
                : "Use backup code instead"
              }
            </Button>
          </div>

          <div className="flex gap-2">
            {showBackButton && (
              <Button
                type="button"
                variant="outline"
                onClick={onBack}
                className="flex-1"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
            )}
            <Button
              type="submit"
              disabled={loading || (!totpCode && !backupCode)}
              className="flex-1"
            >
              {loading ? 'Verifying...' : 'Verify'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
