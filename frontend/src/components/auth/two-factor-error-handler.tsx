"use client";

import React from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw, HelpCircle } from 'lucide-react';

interface TwoFactorErrorHandlerProps {
  error: string;
  onRetry?: () => void;
  onHelp?: () => void;
  className?: string;
}

export function TwoFactorErrorHandler({
  error,
  onRetry,
  onHelp,
  className,
}: TwoFactorErrorHandlerProps) {
  // Parse common error types and provide helpful messages
  const getErrorInfo = (errorMessage: string) => {
    const lowerError = errorMessage.toLowerCase();
    
    if (lowerError.includes('invalid verification code') || lowerError.includes('invalid code')) {
      return {
        title: 'Invalid Verification Code',
        message: 'The verification code you entered is incorrect. Please check your authenticator app and try again.',
        suggestions: [
          'Make sure your device time is synchronized',
          'Check that you\'re using the correct authenticator app',
          'Try using a backup code if available',
        ],
      };
    }
    
    if (lowerError.includes('expired') || lowerError.includes('timeout')) {
      return {
        title: 'Code Expired',
        message: 'The verification code has expired. Please generate a new code and try again.',
        suggestions: [
          'Verification codes are only valid for 30 seconds',
          'Generate a fresh code from your authenticator app',
        ],
      };
    }
    
    if (lowerError.includes('backup code')) {
      return {
        title: 'Invalid Backup Code',
        message: 'The backup code you entered is incorrect or has already been used.',
        suggestions: [
          'Each backup code can only be used once',
          'Check your saved backup codes carefully',
          'Try using your authenticator app instead',
        ],
      };
    }
    
    if (lowerError.includes('rate limit') || lowerError.includes('too many')) {
      return {
        title: 'Too Many Attempts',
        message: 'You\'ve made too many verification attempts. Please wait a moment before trying again.',
        suggestions: [
          'Wait a few minutes before attempting again',
          'Make sure you\'re entering the correct code',
        ],
      };
    }
    
    // Default error handling
    return {
      title: 'Verification Failed',
      message: errorMessage,
      suggestions: [
        'Double-check your verification code',
        'Ensure your authenticator app is working correctly',
        'Try using a backup code if available',
      ],
    };
  };

  const errorInfo = getErrorInfo(error);

  return (
    <Alert variant="destructive" className={className}>
      <AlertTriangle className="h-4 w-4" />
      <AlertDescription className="space-y-3">
        <div>
          <div className="font-semibold">{errorInfo.title}</div>
          <div className="text-sm mt-1">{errorInfo.message}</div>
        </div>
        
        {errorInfo.suggestions.length > 0 && (
          <div>
            <div className="text-sm font-medium mb-1">Suggestions:</div>
            <ul className="text-sm space-y-1 ml-4">
              {errorInfo.suggestions.map((suggestion, index) => (
                <li key={index} className="list-disc">
                  {suggestion}
                </li>
              ))}
            </ul>
          </div>
        )}
        
        <div className="flex gap-2 pt-2">
          {onRetry && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRetry}
              className="h-8"
            >
              <RefreshCw className="h-3 w-3 mr-1" />
              Try Again
            </Button>
          )}
          {onHelp && (
            <Button
              variant="outline"
              size="sm"
              onClick={onHelp}
              className="h-8"
            >
              <HelpCircle className="h-3 w-3 mr-1" />
              Get Help
            </Button>
          )}
        </div>
      </AlertDescription>
    </Alert>
  );
}
