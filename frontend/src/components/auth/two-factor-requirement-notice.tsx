"use client";

import React from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Shield, Info, AlertTriangle } from 'lucide-react';
import Link from 'next/link';

interface TwoFactorRequirementNoticeProps {
  operationName: string;
  variant?: 'info' | 'warning';
  showSetupLink?: boolean;
  className?: string;
}

export function TwoFactorRequirementNotice({
  operationName,
  variant = 'info',
  showSetupLink = true,
  className,
}: TwoFactorRequirementNoticeProps) {
  const Icon = variant === 'warning' ? AlertTriangle : Info;
  const alertVariant = variant === 'warning' ? 'destructive' : 'default';

  return (
    <Alert variant={alertVariant} className={className}>
      <Icon className="h-4 w-4" />
      <AlertDescription className="space-y-2">
        <div>
          <strong>Two-Factor Authentication Required</strong>
        </div>
        <div>
          This operation ({operationName}) requires two-factor authentication for security. 
          You'll need to enter your verification code to proceed.
        </div>
        {showSetupLink && (
          <div className="flex items-center gap-2 mt-3">
            <Shield className="h-4 w-4" />
            <span className="text-sm">
              Don't have 2FA enabled?{' '}
              <Button variant="link" className="p-0 h-auto text-sm" asChild>
                <Link href="/account?tab=security">
                  Set it up now
                </Link>
              </Button>
            </span>
          </div>
        )}
      </AlertDescription>
    </Alert>
  );
}
