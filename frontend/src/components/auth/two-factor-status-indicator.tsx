"use client";

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Shield, ShieldCheck, ShieldAlert } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TwoFactorStatusIndicatorProps {
  isEnabled: boolean;
  className?: string;
  showIcon?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export function TwoFactorStatusIndicator({
  isEnabled,
  className,
  showIcon = true,
  size = 'md',
}: TwoFactorStatusIndicatorProps) {
  const iconSize = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5',
  }[size];

  const badgeSize = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-2.5 py-1',
    lg: 'text-base px-3 py-1.5',
  }[size];

  if (isEnabled) {
    return (
      <Badge 
        variant="default" 
        className={cn(
          "bg-green-100 text-green-800 border-green-200 hover:bg-green-100",
          "dark:bg-green-900 dark:text-green-100 dark:border-green-800",
          badgeSize,
          className
        )}
      >
        {showIcon && <ShieldCheck className={cn(iconSize, "mr-1")} />}
        2FA Enabled
      </Badge>
    );
  }

  return (
    <Badge 
      variant="secondary" 
      className={cn(
        "bg-yellow-100 text-yellow-800 border-yellow-200 hover:bg-yellow-100",
        "dark:bg-yellow-900 dark:text-yellow-100 dark:border-yellow-800",
        badgeSize,
        className
      )}
    >
      {showIcon && <ShieldAlert className={cn(iconSize, "mr-1")} />}
      2FA Disabled
    </Badge>
  );
}
