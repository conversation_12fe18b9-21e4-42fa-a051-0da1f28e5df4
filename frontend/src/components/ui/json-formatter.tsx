/**
 * JSON Formatter Component
 * Intelligently detects and formats JSON content with syntax highlighting
 */

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Copy, Eye, EyeOff } from 'lucide-react';
import { StreamingMarkdownRenderer } from './markdown-renderer';

interface JsonFormatterProps {
  content: string;
  className?: string;
  isStreaming?: boolean;
  maxHeight?: string;
}

// Helper function to detect if content is JSON
const isJsonString = (str: string): boolean => {
  try {
    const trimmed = str.trim();
    if (!trimmed.startsWith('{') && !trimmed.startsWith('[')) {
      return false;
    }
    JSON.parse(trimmed);
    return true;
  } catch {
    return false;
  }
};

// Helper function to format JSON with syntax highlighting
const formatJson = (jsonString: string): string => {
  try {
    const parsed = JSON.parse(jsonString);
    return JSON.stringify(parsed, null, 2);
  } catch {
    return jsonString;
  }
};

// Component for rendering formatted JSON
const JsonDisplay: React.FC<{ content: string; className?: string }> = ({ content, className }) => {
  const [copied, setCopied] = useState(false);
  const [collapsed, setCollapsed] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(content);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  };

  const formattedJson = formatJson(content);
  const lines = formattedJson.split('\n');
  const shouldShowCollapse = lines.length > 20;

  return (
    <div className={cn("relative", className)}>
      {/* Header with controls */}
      <div className="flex items-center justify-between mb-2 text-xs text-muted-foreground">
        <span className="flex items-center gap-1">
          <span className="w-2 h-2 bg-green-500 rounded-full"></span>
          JSON ({lines.length} 行)
        </span>
        <div className="flex items-center gap-1">
          {shouldShowCollapse && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setCollapsed(!collapsed)}
              className="h-6 px-2 text-xs"
            >
              {collapsed ? <Eye className="w-3 h-3" /> : <EyeOff className="w-3 h-3" />}
              {collapsed ? '展开' : '折叠'}
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCopy}
            className="h-6 px-2 text-xs"
          >
            <Copy className="w-3 h-3" />
            {copied ? '已复制' : '复制'}
          </Button>
        </div>
      </div>

      {/* JSON content */}
      <div className={cn(
        "bg-gray-50 dark:bg-gray-900 border rounded-lg overflow-hidden",
        "font-mono text-sm"
      )}>
        <pre className={cn(
          "p-4 whitespace-pre-wrap break-words",
          collapsed && shouldShowCollapse ? "max-h-40 overflow-y-hidden" : ""
        )}>
          <code className="text-gray-800 dark:text-gray-200">
            {collapsed && shouldShowCollapse
              ? lines.slice(0, 10).join('\n') + '\n...'
              : formattedJson
            }
          </code>
        </pre>
      </div>

      {collapsed && shouldShowCollapse && (
        <div className="text-center mt-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCollapsed(false)}
            className="text-xs"
          >
            显示全部 {lines.length} 行
          </Button>
        </div>
      )}
    </div>
  );
};

export function JsonFormatter({ content, className, isStreaming = false, maxHeight = "" }: JsonFormatterProps) {
  // Check if content is JSON
  const isJson = !isStreaming && isJsonString(content);

  if (isJson) {
    return (
      <div className={cn("space-y-2", className)}>
        <JsonDisplay content={content} className={maxHeight} />
      </div>
    );
  }

  // Fallback to markdown renderer for non-JSON content
  return (
    <div className={cn("bg-gray-50 dark:bg-gray-900 rounded-lg p-4", className)}>
      <StreamingMarkdownRenderer
        content={content}
        isStreaming={isStreaming}
        className="text-sm"
      />
    </div>
  );
}

export default JsonFormatter;
