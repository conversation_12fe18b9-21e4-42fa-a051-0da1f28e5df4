"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { 
  animations, 
  iconStyles, 
  combineStyles,
  themeAwareStyles 
} from '@/lib/dashboard-styles';

interface EnhancedLoadingProps {
  variant?: 'spinner' | 'dots' | 'pulse' | 'skeleton' | 'shimmer';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  text?: string;
  fullScreen?: boolean;
}

const LoadingSpinner = ({ size = 'md', className }: { size: string; className?: string }) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6', 
    lg: 'h-8 w-8'
  };

  return (
    <motion.div
      className={combineStyles(
        sizeClasses[size as keyof typeof sizeClasses],
        'border-2 border-primary/20 border-t-primary rounded-full',
        themeAwareStyles.adaptiveText,
        className
      )}
      animate={{ rotate: 360 }}
      transition={{
        duration: 1,
        repeat: Infinity,
        ease: "linear"
      }}
    />
  );
};

const LoadingDots = ({ size = 'md', className }: { size: string; className?: string }) => {
  const dotSizes = {
    sm: 'h-1 w-1',
    md: 'h-2 w-2',
    lg: 'h-3 w-3'
  };

  const dotSize = dotSizes[size as keyof typeof dotSizes];

  return (
    <div className={combineStyles('flex items-center gap-1', className)}>
      {[0, 1, 2].map((index) => (
        <motion.div
          key={index}
          className={combineStyles(
            dotSize,
            'bg-primary rounded-full',
            themeAwareStyles.adaptiveBackground
          )}
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            delay: index * 0.2,
            ease: "easeInOut"
          }}
        />
      ))}
    </div>
  );
};

const LoadingPulse = ({ size = 'md', className }: { size: string; className?: string }) => {
  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-12 w-12',
    lg: 'h-16 w-16'
  };

  return (
    <motion.div
      className={combineStyles(
        sizeClasses[size as keyof typeof sizeClasses],
        'bg-primary/20 rounded-full',
        themeAwareStyles.adaptiveBackground,
        className
      )}
      animate={{
        scale: [1, 1.1, 1],
        opacity: [0.3, 0.8, 0.3]
      }}
      transition={{
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }}
    />
  );
};

const LoadingSkeleton = ({ className }: { className?: string }) => {
  return (
    <div className={combineStyles('space-y-3', className)}>
      <motion.div
        className={combineStyles(
          'h-4 bg-muted rounded',
          themeAwareStyles.adaptiveCard,
          animations.pulse
        )}
        style={{ width: '75%' }}
      />
      <motion.div
        className={combineStyles(
          'h-4 bg-muted rounded',
          themeAwareStyles.adaptiveCard,
          animations.pulse
        )}
        style={{ width: '50%' }}
      />
      <motion.div
        className={combineStyles(
          'h-4 bg-muted rounded',
          themeAwareStyles.adaptiveCard,
          animations.pulse
        )}
        style={{ width: '60%' }}
      />
    </div>
  );
};

const LoadingShimmer = ({ className }: { className?: string }) => {
  return (
    <div className={combineStyles('relative overflow-hidden', className)}>
      <div className={combineStyles(
        'h-20 bg-muted rounded-lg',
        themeAwareStyles.adaptiveCard
      )} />
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
        animate={{
          x: ['-100%', '100%']
        }}
        transition={{
          duration: 1.5,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
    </div>
  );
};

export function EnhancedLoading({
  variant = 'spinner',
  size = 'md',
  className,
  text,
  fullScreen = false
}: EnhancedLoadingProps) {
  const renderLoading = () => {
    switch (variant) {
      case 'dots':
        return <LoadingDots size={size} />;
      case 'pulse':
        return <LoadingPulse size={size} />;
      case 'skeleton':
        return <LoadingSkeleton />;
      case 'shimmer':
        return <LoadingShimmer />;
      default:
        return <LoadingSpinner size={size} />;
    }
  };

  const content = (
    <div className={combineStyles(
      'flex flex-col items-center justify-center gap-3',
      fullScreen ? 'min-h-screen' : 'py-8',
      className
    )}>
      {renderLoading()}
      {text && (
        <motion.p
          className={combineStyles(
            'text-sm text-muted-foreground',
            themeAwareStyles.adaptiveTextMuted
          )}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          {text}
        </motion.p>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <div className={combineStyles(
        'fixed inset-0 z-50 flex items-center justify-center',
        'bg-background/80 backdrop-blur-sm',
        themeAwareStyles.adaptiveBackground
      )}>
        {content}
      </div>
    );
  }

  return content;
}

// 专门为仪表板优化的加载组件
export function DashboardLoading({ 
  section, 
  className 
}: { 
  section?: string; 
  className?: string; 
}) {
  return (
    <EnhancedLoading
      variant="shimmer"
      text={section ? `正在加载${section}...` : '正在加载...'}
      className={className}
    />
  );
}

// 卡片加载状态
export function CardLoading({ className }: { className?: string }) {
  return (
    <div className={combineStyles(
      'p-4 border rounded-lg',
      themeAwareStyles.adaptiveCard,
      themeAwareStyles.adaptiveBorder,
      className
    )}>
      <EnhancedLoading variant="skeleton" />
    </div>
  );
}

EnhancedLoading.displayName = 'EnhancedLoading';
DashboardLoading.displayName = 'DashboardLoading';
CardLoading.displayName = 'CardLoading';
