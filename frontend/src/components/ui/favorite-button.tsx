/**
 * Favorite button component for toggling agent favorites
 */

import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { <PERSON>, StarOff, Loader2 } from 'lucide-react';
import { FavoriteButtonProps } from '@/types/favorites';

export function FavoriteButton({
  agentId,
  isFavorite,
  onToggle,
  disabled = false,
  size = 'md',
  variant = 'ghost',
  className
}: FavoriteButtonProps) {
  const [isLoading, setIsLoading] = React.useState(false);

  const handleClick = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (disabled || isLoading) return;

    setIsLoading(true);
    try {
      await onToggle?.(agentId, isFavorite);
    } catch (error) {
      console.error('Failed to toggle favorite:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'h-7 w-7 p-0';
      case 'lg':
        return 'h-10 w-10 p-0';
      default:
        return 'h-8 w-8 p-0';
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'sm':
        return 'h-3 w-3';
      case 'lg':
        return 'h-5 w-5';
      default:
        return 'h-4 w-4';
    }
  };

  const tooltipText = isFavorite ? '取消收藏' : '添加到收藏';

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant={variant}
            size="sm"
            onClick={handleClick}
            disabled={disabled || isLoading}
            className={cn(
              getSizeClasses(),
              'transition-all duration-200',
              isFavorite && 'text-yellow-500 hover:text-yellow-600',
              !isFavorite && 'text-muted-foreground hover:text-foreground',
              disabled && 'opacity-50 cursor-not-allowed',
              className
            )}
            aria-label={tooltipText}
          >
            {isLoading ? (
              <Loader2 className={cn(getIconSize(), 'animate-spin')} />
            ) : isFavorite ? (
              <Star className={cn(getIconSize(), 'fill-current')} />
            ) : (
              <StarOff className={getIconSize()} />
            )}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{tooltipText}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

/**
 * Compact favorite button for use in lists
 */
export function CompactFavoriteButton({
  agentId,
  isFavorite,
  onToggle,
  disabled = false,
  className
}: Omit<FavoriteButtonProps, 'size' | 'variant'>) {
  return (
    <FavoriteButton
      agentId={agentId}
      isFavorite={isFavorite}
      onToggle={onToggle}
      disabled={disabled}
      size="sm"
      variant="ghost"
      className={cn('hover:bg-transparent', className)}
    />
  );
}

/**
 * Favorite button with text label
 */
export function FavoriteButtonWithLabel({
  agentId,
  isFavorite,
  onToggle,
  disabled = false,
  size = 'md',
  className
}: FavoriteButtonProps) {
  const [isLoading, setIsLoading] = React.useState(false);

  const handleClick = async () => {
    if (disabled || isLoading) return;

    setIsLoading(true);
    try {
      await onToggle?.(agentId, isFavorite);
    } catch (error) {
      console.error('Failed to toggle favorite:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getButtonSize = () => {
    switch (size) {
      case 'sm':
        return 'sm';
      case 'lg':
        return 'lg';
      default:
        return 'default';
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'sm':
        return 'h-3 w-3';
      case 'lg':
        return 'h-5 w-5';
      default:
        return 'h-4 w-4';
    }
  };

  return (
    <Button
      variant={isFavorite ? 'default' : 'outline'}
      size={getButtonSize()}
      onClick={handleClick}
      disabled={disabled || isLoading}
      className={cn(
        'gap-2 transition-all duration-200',
        isFavorite && 'bg-yellow-500 hover:bg-yellow-600 text-white',
        disabled && 'opacity-50 cursor-not-allowed',
        className
      )}
    >
      {isLoading ? (
        <Loader2 className={cn(getIconSize(), 'animate-spin')} />
      ) : isFavorite ? (
        <Star className={cn(getIconSize(), 'fill-current')} />
      ) : (
        <StarOff className={getIconSize()} />
      )}
      {isFavorite ? '已收藏' : '收藏'}
    </Button>
  );
}

/**
 * Animated favorite button with heart-like animation
 */
export function AnimatedFavoriteButton({
  agentId,
  isFavorite,
  onToggle,
  disabled = false,
  size = 'md',
  className
}: FavoriteButtonProps) {
  const [isLoading, setIsLoading] = React.useState(false);
  const [isAnimating, setIsAnimating] = React.useState(false);

  const handleClick = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (disabled || isLoading) return;

    setIsLoading(true);
    setIsAnimating(true);

    try {
      await onToggle?.(agentId, isFavorite);
      // Keep animation for a bit longer for visual feedback
      setTimeout(() => setIsAnimating(false), 300);
    } catch (error) {
      console.error('Failed to toggle favorite:', error);
      setIsAnimating(false);
    } finally {
      setIsLoading(false);
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'h-7 w-7 p-0';
      case 'lg':
        return 'h-10 w-10 p-0';
      default:
        return 'h-8 w-8 p-0';
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'sm':
        return 'h-3 w-3';
      case 'lg':
        return 'h-5 w-5';
      default:
        return 'h-4 w-4';
    }
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClick}
            disabled={disabled || isLoading}
            className={cn(
              getSizeClasses(),
              'transition-all duration-200 relative',
              isFavorite && 'text-yellow-500 hover:text-yellow-600',
              !isFavorite && 'text-muted-foreground hover:text-foreground',
              disabled && 'opacity-50 cursor-not-allowed',
              isAnimating && 'scale-110',
              className
            )}
            aria-label={isFavorite ? '取消收藏' : '添加到收藏'}
          >
            {isLoading ? (
              <Loader2 className={cn(getIconSize(), 'animate-spin')} />
            ) : (
              <Star 
                className={cn(
                  getIconSize(),
                  'transition-all duration-300',
                  isFavorite && 'fill-current scale-110',
                  isAnimating && 'animate-pulse'
                )} 
              />
            )}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{isFavorite ? '取消收藏' : '添加到收藏'}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
