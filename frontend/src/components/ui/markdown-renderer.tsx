/**
 * Markdown Renderer Component
 * Renders markdown content with syntax highlighting and theme support
 */

import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import { cn } from '@/lib/utils';

// Import highlight.js styles
import 'highlight.js/styles/github.css';
import 'highlight.js/styles/github-dark.css';

interface MarkdownRendererProps {
  content: string;
  className?: string;
  streaming?: boolean;
}

export function MarkdownRenderer({ content, className, streaming = false }: MarkdownRendererProps) {
  return (
    <div className={cn(
      "prose prose-sm max-w-none",
      "dark:prose-invert",
      "prose-headings:font-semibold",
      "prose-h1:text-xl prose-h2:text-lg prose-h3:text-base",
      "prose-p:leading-relaxed prose-p:my-2",
      "prose-ul:my-2 prose-ol:my-2",
      "prose-li:my-1",
      "prose-code:bg-muted prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-code:text-sm",
      "prose-pre:bg-muted prose-pre:border prose-pre:rounded-lg prose-pre:p-4",
      "prose-blockquote:border-l-4 prose-blockquote:border-border prose-blockquote:pl-4 prose-blockquote:italic",
      "prose-table:border prose-table:border-border",
      "prose-th:border prose-th:border-border prose-th:bg-muted prose-th:px-3 prose-th:py-2",
      "prose-td:border prose-td:border-border prose-td:px-3 prose-td:py-2",
      "prose-a:text-primary prose-a:no-underline hover:prose-a:underline",
      "prose-strong:font-semibold",
      "prose-em:italic",
      streaming && "animate-pulse",
      className
    )}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeHighlight]}
        components={{
          // Custom components for better styling
          h1: ({ children, ...props }) => (
            <h1 className="text-xl font-semibold mb-3 mt-4 first:mt-0" {...props}>
              {children}
            </h1>
          ),
          h2: ({ children, ...props }) => (
            <h2 className="text-lg font-semibold mb-2 mt-3 first:mt-0" {...props}>
              {children}
            </h2>
          ),
          h3: ({ children, ...props }) => (
            <h3 className="text-base font-semibold mb-2 mt-3 first:mt-0" {...props}>
              {children}
            </h3>
          ),
          p: ({ children, ...props }) => (
            <p className="leading-relaxed my-2 first:mt-0 last:mb-0" {...props}>
              {children}
            </p>
          ),
          ul: ({ children, ...props }) => (
            <ul className="list-disc list-inside my-2 space-y-1" {...props}>
              {children}
            </ul>
          ),
          ol: ({ children, ...props }) => (
            <ol className="list-decimal list-inside my-2 space-y-1" {...props}>
              {children}
            </ol>
          ),
          li: ({ children, ...props }) => (
            <li className="my-1" {...props}>
              {children}
            </li>
          ),
          code: ({ inline, className, children, ...props }) => {
            if (inline) {
              return (
                <code 
                  className="bg-muted px-1 py-0.5 rounded text-sm font-mono" 
                  {...props}
                >
                  {children}
                </code>
              );
            }
            return (
              <code 
                className={cn("font-mono text-sm", className)} 
                {...props}
              >
                {children}
              </code>
            );
          },
          pre: ({ children, ...props }) => (
            <pre
              className="bg-muted border rounded-lg p-4 whitespace-pre-wrap break-words my-3"
              {...props}
            >
              {children}
            </pre>
          ),
          blockquote: ({ children, ...props }) => (
            <blockquote 
              className="border-l-4 border-border pl-4 italic my-3 text-muted-foreground" 
              {...props}
            >
              {children}
            </blockquote>
          ),
          table: ({ children, ...props }) => (
            <div className="overflow-x-auto my-3">
              <table 
                className="min-w-full border border-border rounded-lg" 
                {...props}
              >
                {children}
              </table>
            </div>
          ),
          thead: ({ children, ...props }) => (
            <thead className="bg-muted" {...props}>
              {children}
            </thead>
          ),
          th: ({ children, ...props }) => (
            <th 
              className="border border-border px-3 py-2 text-left font-semibold" 
              {...props}
            >
              {children}
            </th>
          ),
          td: ({ children, ...props }) => (
            <td 
              className="border border-border px-3 py-2" 
              {...props}
            >
              {children}
            </td>
          ),
          a: ({ children, href, ...props }) => (
            <a 
              href={href}
              className="text-primary hover:underline"
              target="_blank"
              rel="noopener noreferrer"
              {...props}
            >
              {children}
            </a>
          ),
          strong: ({ children, ...props }) => (
            <strong className="font-semibold" {...props}>
              {children}
            </strong>
          ),
          em: ({ children, ...props }) => (
            <em className="italic" {...props}>
              {children}
            </em>
          ),
          hr: ({ ...props }) => (
            <hr className="border-border my-4" {...props} />
          ),
        }}
      >
        {content}
      </ReactMarkdown>
      {streaming && (
        <span className="inline-block w-2 h-4 bg-primary animate-pulse ml-1" />
      )}
    </div>
  );
}

// Streaming Markdown Renderer for real-time content
export function StreamingMarkdownRenderer({ 
  content, 
  className, 
  isStreaming = false 
}: { 
  content: string; 
  className?: string; 
  isStreaming?: boolean; 
}) {
  return (
    <MarkdownRenderer 
      content={content} 
      className={className} 
      streaming={isStreaming} 
    />
  );
}

export default MarkdownRenderer;
