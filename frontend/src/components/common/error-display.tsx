"use client";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

interface ErrorDisplayProps {
  title?: string;
  message: string;
  description?: string;
  onRetry?: () => void;
  retryText?: string;
}

export function ErrorDisplay({ 
  title = "出现错误", 
  message, 
  description,
  onRetry,
  retryText = "重试"
}: ErrorDisplayProps) {
  return (
    <Alert variant="destructive">
      <span className="text-lg">❌</span>
      <AlertDescription>
        <div className="space-y-2">
          <div>
            <strong>{title}</strong>
            <p className="mt-1">{message}</p>
            {description && (
              <p className="text-sm text-muted-foreground mt-1">{description}</p>
            )}
          </div>
          {onRetry && (
            <Button variant="outline" size="sm" onClick={onRetry}>
              {retryText}
            </Button>
          )}
        </div>
      </AlertDescription>
    </Alert>
  );
}

interface ErrorCardProps {
  title?: string;
  message: string;
  description?: string;
  onRetry?: () => void;
  onGoBack?: () => void;
  retryText?: string;
  goBackText?: string;
}

export function ErrorCard({
  title = "出现错误",
  message,
  description,
  onRetry,
  onGoBack,
  retryText = "重试",
  goBackText = "返回"
}: ErrorCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <span className="text-2xl">❌</span>
          {title}
        </CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-muted-foreground">{message}</p>
        <div className="flex gap-2">
          {onRetry && (
            <Button onClick={onRetry}>
              {retryText}
            </Button>
          )}
          {onGoBack && (
            <Button variant="outline" onClick={onGoBack}>
              {goBackText}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

interface ErrorBoundaryFallbackProps {
  error: Error;
  resetError: () => void;
}

export function ErrorBoundaryFallback({ error, resetError }: ErrorBoundaryFallbackProps) {
  return (
    <div className="min-h-[400px] flex items-center justify-center">
      <Card className="max-w-md">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <span className="text-2xl">💥</span>
            应用出现错误
          </CardTitle>
          <CardDescription>
            很抱歉，应用遇到了意外错误
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-muted p-3 rounded text-sm">
            <strong>错误信息:</strong>
            <pre className="mt-1 text-xs overflow-auto">
              {error.message}
            </pre>
          </div>
          <div className="flex gap-2">
            <Button onClick={resetError}>
              重新加载
            </Button>
            <Button variant="outline" onClick={() => window.location.reload()}>
              刷新页面
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

interface NetworkErrorProps {
  onRetry?: () => void;
}

export function NetworkError({ onRetry }: NetworkErrorProps) {
  return (
    <ErrorCard
      title="网络连接错误"
      message="无法连接到服务器，请检查网络连接"
      description="这可能是临时的网络问题"
      onRetry={onRetry}
      retryText="重新连接"
    />
  );
}

interface NotFoundErrorProps {
  resource?: string;
  onGoBack?: () => void;
}

export function NotFoundError({ resource = "页面", onGoBack }: NotFoundErrorProps) {
  return (
    <ErrorCard
      title="未找到"
      message={`请求的${resource}不存在或已被删除`}
      description="请检查URL是否正确，或联系管理员"
      onGoBack={onGoBack}
      goBackText="返回首页"
    />
  );
}

interface PermissionErrorProps {
  action?: string;
  onGoBack?: () => void;
}

export function PermissionError({ action = "访问此资源", onGoBack }: PermissionErrorProps) {
  return (
    <ErrorCard
      title="权限不足"
      message={`你没有权限${action}`}
      description="请联系管理员获取相应权限"
      onGoBack={onGoBack}
      goBackText="返回"
    />
  );
}
