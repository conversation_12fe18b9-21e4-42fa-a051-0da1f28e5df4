"use client";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

interface EmptyStateProps {
  icon?: string;
  title: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  secondaryAction?: {
    label: string;
    onClick: () => void;
  };
}

export function EmptyState({ 
  icon = "📭", 
  title, 
  description, 
  action, 
  secondaryAction 
}: EmptyStateProps) {
  return (
    <div className="flex flex-col items-center justify-center py-12 space-y-4">
      <div className="text-6xl mb-4">{icon}</div>
      <div className="text-center space-y-2">
        <h3 className="text-lg font-semibold">{title}</h3>
        {description && (
          <p className="text-muted-foreground max-w-md">{description}</p>
        )}
      </div>
      {(action || secondaryAction) && (
        <div className="flex gap-2 mt-4">
          {action && (
            <Button onClick={action.onClick}>
              {action.label}
            </Button>
          )}
          {secondaryAction && (
            <Button variant="outline" onClick={secondaryAction.onClick}>
              {secondaryAction.label}
            </Button>
          )}
        </div>
      )}
    </div>
  );
}

export function EmptyAgentList({ onCreate }: { onCreate?: () => void }) {
  return (
    <EmptyState
      icon="🤖"
      title="暂无Agent"
      description="你还没有创建任何Agent团队。点击下方按钮开始创建你的第一个Agent。"
      action={onCreate ? {
        label: "创建第一个Agent",
        onClick: onCreate
      } : undefined}
    />
  );
}

export function EmptyTestHistory({ onStartTest }: { onStartTest?: () => void }) {
  return (
    <EmptyState
      icon="📝"
      title="暂无测试记录"
      description="开始测试Agent后，测试记录将显示在这里。"
      action={onStartTest ? {
        label: "开始测试",
        onClick: onStartTest
      } : undefined}
    />
  );
}

export function EmptySearchResults({ query, onClear }: { query: string; onClear?: () => void }) {
  return (
    <EmptyState
      icon="🔍"
      title="未找到匹配结果"
      description={`没有找到与"${query}"相关的内容，请尝试其他关键词。`}
      action={onClear ? {
        label: "清除搜索",
        onClick: onClear
      } : undefined}
    />
  );
}

export function EmptyTemplateLibrary({ onCreate }: { onCreate?: () => void }) {
  return (
    <EmptyState
      icon="📚"
      title="模板库为空"
      description="暂时没有可用的Agent模板，你可以创建自定义Agent。"
      action={onCreate ? {
        label: "创建自定义Agent",
        onClick: onCreate
      } : undefined}
    />
  );
}

interface EmptyCardProps {
  children: React.ReactNode;
}

export function EmptyCard({ children }: EmptyCardProps) {
  return (
    <Card>
      <CardContent className="pt-6">
        {children}
      </CardContent>
    </Card>
  );
}
