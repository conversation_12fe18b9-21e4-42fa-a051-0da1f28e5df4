"use client";

import { useState, useEffect } from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface ToastProps {
  id: string;
  title?: string;
  message: string;
  type?: "success" | "error" | "warning" | "info";
  duration?: number;
  onClose: (id: string) => void;
}

export function Toast({ id, title, message, type = "info", duration = 5000, onClose }: ToastProps) {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        setIsVisible(false);
        setTimeout(() => onClose(id), 300); // Wait for animation
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [duration, id, onClose]);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(() => onClose(id), 300);
  };

  const getIcon = () => {
    switch (type) {
      case "success":
        return "✅";
      case "error":
        return "❌";
      case "warning":
        return "⚠️";
      default:
        return "ℹ️";
    }
  };

  const getVariant = () => {
    switch (type) {
      case "error":
        return "destructive";
      default:
        return "default";
    }
  };

  return (
    <div
      className={cn(
        "transition-all duration-300 ease-in-out",
        isVisible ? "opacity-100 translate-x-0" : "opacity-0 translate-x-full"
      )}
    >
      <Alert variant={getVariant()} className="relative pr-12">
        <span className="text-lg">{getIcon()}</span>
        <AlertDescription>
          {title && <div className="font-semibold">{title}</div>}
          <div>{message}</div>
        </AlertDescription>
        <Button
          variant="ghost"
          size="sm"
          className="absolute top-2 right-2 h-6 w-6 p-0"
          onClick={handleClose}
        >
          ×
        </Button>
      </Alert>
    </div>
  );
}

interface ToastContainerProps {
  toasts: Array<{
    id: string;
    title?: string;
    message: string;
    type?: "success" | "error" | "warning" | "info";
    duration?: number;
  }>;
  onRemove: (id: string) => void;
}

export function ToastContainer({ toasts, onRemove }: ToastContainerProps) {
  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
      {toasts.map((toast) => (
        <Toast
          key={toast.id}
          id={toast.id}
          title={toast.title}
          message={toast.message}
          type={toast.type}
          duration={toast.duration}
          onClose={onRemove}
        />
      ))}
    </div>
  );
}

// Toast hook for managing toasts
export function useToast() {
  const [toasts, setToasts] = useState<Array<{
    id: string;
    title?: string;
    message: string;
    type?: "success" | "error" | "warning" | "info";
    duration?: number;
  }>>([]);

  const addToast = (toast: {
    title?: string;
    message: string;
    type?: "success" | "error" | "warning" | "info";
    duration?: number;
  }) => {
    const id = Math.random().toString(36).substr(2, 9);
    setToasts((prev) => [...prev, { ...toast, id }]);
  };

  const removeToast = (id: string) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id));
  };

  const success = (message: string, title?: string) => {
    addToast({ message, title, type: "success" });
  };

  const error = (message: string, title?: string) => {
    addToast({ message, title, type: "error", duration: 7000 });
  };

  const warning = (message: string, title?: string) => {
    addToast({ message, title, type: "warning" });
  };

  const info = (message: string, title?: string) => {
    addToast({ message, title, type: "info" });
  };

  return {
    toasts,
    addToast,
    removeToast,
    success,
    error,
    warning,
    info,
  };
}
