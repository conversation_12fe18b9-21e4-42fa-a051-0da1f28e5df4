"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface LoadingStep {
  id: string;
  title: string;
  description: string;
  estimatedTime: number; // in seconds
}

interface EnhancedLoadingProps {
  steps: LoadingStep[];
  currentStepId: string;
  progress: number;
  onCancel?: () => void;
  className?: string;
}

export function EnhancedLoading({ 
  steps, 
  currentStepId, 
  progress, 
  onCancel,
  className 
}: EnhancedLoadingProps) {
  const [elapsedTime, setElapsedTime] = useState(0);
  const currentStepIndex = steps.findIndex(step => step.id === currentStepId);
  const currentStep = steps[currentStepIndex];

  useEffect(() => {
    const interval = setInterval(() => {
      setElapsedTime(prev => prev + 1);
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const totalEstimatedTime = steps.reduce((sum, step) => sum + step.estimatedTime, 0);
  const completedTime = steps.slice(0, currentStepIndex).reduce((sum, step) => sum + step.estimatedTime, 0);
  const estimatedProgress = Math.min(100, ((completedTime + (currentStep?.estimatedTime || 0) * (progress / 100)) / totalEstimatedTime) * 100);

  return (
    <Card className={cn("w-full max-w-md mx-auto", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <div className="animate-spin rounded-full h-5 w-5 border-2 border-primary border-t-transparent"></div>
          {currentStep?.title || "处理中..."}
        </CardTitle>
        <CardDescription>
          {currentStep?.description || "请稍候，正在处理您的请求"}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Overall Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>总体进度</span>
            <span>{Math.round(estimatedProgress)}%</span>
          </div>
          <Progress value={estimatedProgress} className="h-2" />
        </div>

        {/* Current Step Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>当前步骤</span>
            <span>{progress}%</span>
          </div>
          <Progress value={progress} className="h-1" />
        </div>

        {/* Steps List */}
        <div className="space-y-2">
          {steps.map((step, index) => (
            <div
              key={step.id}
              className={cn(
                "flex items-center gap-3 p-2 rounded text-sm",
                index < currentStepIndex && "text-green-600 bg-green-50",
                index === currentStepIndex && "text-primary bg-primary/10",
                index > currentStepIndex && "text-muted-foreground"
              )}
            >
              <div className={cn(
                "w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium",
                index < currentStepIndex && "bg-green-500 text-white",
                index === currentStepIndex && "bg-primary text-primary-foreground",
                index > currentStepIndex && "bg-muted text-muted-foreground"
              )}>
                {index < currentStepIndex ? "✓" : index + 1}
              </div>
              <div className="flex-1">
                <div className="font-medium">{step.title}</div>
                <div className="text-xs text-muted-foreground">{step.description}</div>
              </div>
              <div className="text-xs text-muted-foreground">
                ~{step.estimatedTime}s
              </div>
            </div>
          ))}
        </div>

        {/* Time Info */}
        <div className="flex justify-between text-xs text-muted-foreground pt-2 border-t">
          <span>已用时间: {formatTime(elapsedTime)}</span>
          <span>预计剩余: {formatTime(Math.max(0, totalEstimatedTime - elapsedTime))}</span>
        </div>

        {/* Cancel Button */}
        {onCancel && (
          <Button variant="outline" onClick={onCancel} className="w-full">
            取消操作
          </Button>
        )}
      </CardContent>
    </Card>
  );
}

// Agent Creation Loading Component
export function AgentCreationLoading({ 
  currentStep, 
  progress, 
  onCancel 
}: { 
  currentStep: string; 
  progress: number; 
  onCancel?: () => void; 
}) {
  const steps: LoadingStep[] = [
    {
      id: "planning",
      title: "AI规划中",
      description: "分析需求并设计团队结构",
      estimatedTime: 30
    },
    {
      id: "generating",
      title: "生成代码",
      description: "根据规划生成Agent代码",
      estimatedTime: 60
    },
    {
      id: "loading",
      title: "部署Agent",
      description: "编译并部署到运行环境",
      estimatedTime: 45
    },
    {
      id: "testing",
      title: "功能测试",
      description: "验证Agent功能正常",
      estimatedTime: 15
    }
  ];

  return (
    <EnhancedLoading
      steps={steps}
      currentStepId={currentStep}
      progress={progress}
      onCancel={onCancel}
    />
  );
}

// Simple Loading Spinner with Message
interface SimpleLoadingProps {
  message?: string;
  size?: "sm" | "md" | "lg";
  className?: string;
}

export function SimpleLoading({ 
  message = "加载中...", 
  size = "md",
  className 
}: SimpleLoadingProps) {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-6 w-6",
    lg: "h-8 w-8"
  };

  return (
    <div className={cn("flex items-center justify-center gap-3", className)}>
      <div className={cn(
        "animate-spin rounded-full border-2 border-primary border-t-transparent",
        sizeClasses[size]
      )} />
      <span className="text-muted-foreground">{message}</span>
    </div>
  );
}

// Loading Overlay for existing content
interface LoadingOverlayProps {
  isLoading: boolean;
  message?: string;
  children: React.ReactNode;
}

export function LoadingOverlay({ 
  isLoading, 
  message = "加载中...", 
  children 
}: LoadingOverlayProps) {
  return (
    <div className="relative">
      {children}
      {isLoading && (
        <div className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
          <SimpleLoading message={message} size="lg" />
        </div>
      )}
    </div>
  );
}
