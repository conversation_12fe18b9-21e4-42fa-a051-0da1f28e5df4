"use client";

import { useEffect } from "react";
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface Shortcut {
  keys: string[];
  description: string;
  action: () => void;
  category: string;
}

interface KeyboardShortcutsProps {
  shortcuts: Shortcut[];
}

export function KeyboardShortcuts({ shortcuts }: KeyboardShortcutsProps) {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // 检查是否在输入框中
      const target = event.target as HTMLElement;
      if (target.tagName === "INPUT" || target.tagName === "TEXTAREA" || target.contentEditable === "true") {
        return;
      }

      const pressedKeys = [];
      if (event.ctrlKey || event.metaKey) pressedKeys.push("Ctrl");
      if (event.shiftKey) pressedKeys.push("Shift");
      if (event.altKey) pressedKeys.push("Alt");
      
      const key = event.key.toLowerCase();
      if (!["control", "shift", "alt", "meta"].includes(key)) {
        pressedKeys.push(key);
      }

      const pressedKeysStr = pressedKeys.join("+");

      // 查找匹配的快捷键
      const matchedShortcut = shortcuts.find(shortcut => {
        const shortcutKeys = shortcut.keys.join("+").toLowerCase();
        return shortcutKeys === pressedKeysStr;
      });

      if (matchedShortcut) {
        event.preventDefault();
        matchedShortcut.action();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [shortcuts]);

  const groupedShortcuts = shortcuts.reduce((acc, shortcut) => {
    if (!acc[shortcut.category]) {
      acc[shortcut.category] = [];
    }
    acc[shortcut.category].push(shortcut);
    return acc;
  }, {} as Record<string, Shortcut[]>);

  const formatKeys = (keys: string[]) => {
    return keys.map(key => {
      switch (key.toLowerCase()) {
        case "ctrl":
          return "⌘";
        case "shift":
          return "⇧";
        case "alt":
          return "⌥";
        case "enter":
          return "↵";
        case "escape":
          return "⎋";
        case "backspace":
          return "⌫";
        case "delete":
          return "⌦";
        case "tab":
          return "⇥";
        case "space":
          return "␣";
        default:
          return key.toUpperCase();
      }
    }).join(" + ");
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm" className="text-muted-foreground">
          ⌨️ 快捷键
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>键盘快捷键</DialogTitle>
          <DialogDescription>
            使用这些快捷键提高操作效率
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-6">
          {Object.entries(groupedShortcuts).map(([category, categoryShortcuts]) => (
            <div key={category}>
              <h3 className="font-semibold mb-3">{category}</h3>
              <div className="space-y-2">
                {categoryShortcuts.map((shortcut, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm">{shortcut.description}</span>
                    <Badge variant="outline" className="font-mono">
                      {formatKeys(shortcut.keys)}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
}

// 预定义的快捷键配置
export const defaultShortcuts: Shortcut[] = [
  {
    keys: ["Ctrl", "k"],
    description: "快速搜索",
    action: () => {
      const searchInput = document.querySelector('input[placeholder*="搜索"]') as HTMLInputElement;
      if (searchInput) {
        searchInput.focus();
      }
    },
    category: "导航"
  },
  {
    keys: ["Ctrl", "n"],
    description: "创建新Agent",
    action: () => {
      window.location.href = "/create";
    },
    category: "操作"
  },
  {
    keys: ["Ctrl", "m"],
    description: "Agent管理",
    action: () => {
      window.location.href = "/manage";
    },
    category: "导航"
  },
  {
    keys: ["Ctrl", "t"],
    description: "测试中心",
    action: () => {
      window.location.href = "/test";
    },
    category: "导航"
  },
  {
    keys: ["Ctrl", ","],
    description: "系统设置",
    action: () => {
      window.location.href = "/settings";
    },
    category: "导航"
  },
  {
    keys: ["?"],
    description: "显示快捷键帮助",
    action: () => {
      // 这个会被组件内部处理
    },
    category: "帮助"
  },
  {
    keys: ["Escape"],
    description: "关闭对话框/取消操作",
    action: () => {
      // 这个通常由各个组件自己处理
    },
    category: "通用"
  }
];

// Hook for using keyboard shortcuts
export function useKeyboardShortcuts(customShortcuts: Shortcut[] = []) {
  const allShortcuts = [...defaultShortcuts, ...customShortcuts];
  
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // 显示快捷键帮助
      if (event.key === "?" && !event.ctrlKey && !event.metaKey && !event.shiftKey && !event.altKey) {
        const target = event.target as HTMLElement;
        if (target.tagName !== "INPUT" && target.tagName !== "TEXTAREA" && target.contentEditable !== "true") {
          event.preventDefault();
          // 触发快捷键对话框显示
          const shortcutButton = document.querySelector('[data-shortcut-trigger]') as HTMLButtonElement;
          if (shortcutButton) {
            shortcutButton.click();
          }
        }
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, []);

  return allShortcuts;
}
