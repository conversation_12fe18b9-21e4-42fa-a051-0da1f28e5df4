"use client";

import { useState, useEffect } from "react";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import Link from "next/link";

interface SearchResult {
  id: string;
  title: string;
  description: string;
  type: "agent" | "template" | "page" | "setting";
  url: string;
  icon: string;
}

const mockSearchResults: SearchResult[] = [
  {
    id: "agent_zen_rizzo",
    title: "禅探二人组",
    description: "由禅意僧侣和街头老兵组成的侦探团队",
    type: "agent",
    url: "/manage",
    icon: "🤖"
  },
  {
    id: "template_detective",
    title: "侦探二人组模板",
    description: "创建侦探类型的Agent团队模板",
    type: "template",
    url: "/templates",
    icon: "📚"
  },
  {
    id: "page_create",
    title: "创建Agent",
    description: "创建新的AI Agent团队",
    type: "page",
    url: "/create",
    icon: "➕"
  },
  {
    id: "page_test",
    title: "测试中心",
    description: "测试Agent功能和性能",
    type: "page",
    url: "/test",
    icon: "🧪"
  },
  {
    id: "setting_api_keys",
    title: "AI密钥管理",
    description: "管理AI服务提供商的AI密钥",
    type: "setting",
    url: "/api-keys",
    icon: "🔑"
  },
  {
    id: "agent_tech_team",
    title: "技术咨询团队",
    description: "包含架构师、前端和后端专家的技术团队",
    type: "agent",
    url: "/manage",
    icon: "🤖"
  }
];

interface GlobalSearchProps {
  isOpen: boolean;
  onClose: () => void;
}

export function GlobalSearch({ isOpen, onClose }: GlobalSearchProps) {
  const [query, setQuery] = useState("");
  const [results, setResults] = useState<SearchResult[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(0);

  useEffect(() => {
    if (query.trim()) {
      const filtered = mockSearchResults.filter(result =>
        result.title.toLowerCase().includes(query.toLowerCase()) ||
        result.description.toLowerCase().includes(query.toLowerCase())
      );
      setResults(filtered);
      setSelectedIndex(0);
    } else {
      setResults([]);
    }
  }, [query]);

  useEffect(() => {
    if (isOpen) {
      setQuery("");
      setResults([]);
      setSelectedIndex(0);
    }
  }, [isOpen]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "ArrowDown") {
      e.preventDefault();
      setSelectedIndex(prev => Math.min(prev + 1, results.length - 1));
    } else if (e.key === "ArrowUp") {
      e.preventDefault();
      setSelectedIndex(prev => Math.max(prev - 1, 0));
    } else if (e.key === "Enter" && results[selectedIndex]) {
      e.preventDefault();
      window.location.href = results[selectedIndex].url;
      onClose();
    } else if (e.key === "Escape") {
      onClose();
    }
  };

  const getTypeLabel = (type: SearchResult["type"]) => {
    switch (type) {
      case "agent":
        return "Agent";
      case "template":
        return "模板";
      case "page":
        return "页面";
      case "setting":
        return "设置";
      default:
        return "";
    }
  };

  const getTypeColor = (type: SearchResult["type"]) => {
    switch (type) {
      case "agent":
        return "bg-blue-100 text-blue-800";
      case "template":
        return "bg-green-100 text-green-800";
      case "page":
        return "bg-purple-100 text-purple-800";
      case "setting":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>全局搜索</DialogTitle>
          <DialogDescription>
            搜索Agent、模板、页面和设置
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <Input
            placeholder="输入搜索关键词..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onKeyDown={handleKeyDown}
            autoFocus
            className="text-lg"
          />

          {query.trim() === "" && (
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-muted-foreground">快速访问</h4>
              <div className="grid grid-cols-2 gap-2">
                <Button variant="ghost" className="justify-start h-auto p-3" asChild>
                  <Link href="/create">
                    <div className="flex items-center gap-3">
                      <span className="text-xl">🤖</span>
                      <div className="text-left">
                        <div className="font-medium">创建Agent</div>
                        <div className="text-xs text-muted-foreground">新建AI团队</div>
                      </div>
                    </div>
                  </Link>
                </Button>
                <Button variant="ghost" className="justify-start h-auto p-3" asChild>
                  <Link href="/templates">
                    <div className="flex items-center gap-3">
                      <span className="text-xl">📚</span>
                      <div className="text-left">
                        <div className="font-medium">模板库</div>
                        <div className="text-xs text-muted-foreground">预设模板</div>
                      </div>
                    </div>
                  </Link>
                </Button>
                <Button variant="ghost" className="justify-start h-auto p-3" asChild>
                  <Link href="/test">
                    <div className="flex items-center gap-3">
                      <span className="text-xl">🧪</span>
                      <div className="text-left">
                        <div className="font-medium">测试中心</div>
                        <div className="text-xs text-muted-foreground">功能测试</div>
                      </div>
                    </div>
                  </Link>
                </Button>
                <Button variant="ghost" className="justify-start h-auto p-3" asChild>
                  <Link href="/monitoring">
                    <div className="flex items-center gap-3">
                      <span className="text-xl">📈</span>
                      <div className="text-left">
                        <div className="font-medium">性能监控</div>
                        <div className="text-xs text-muted-foreground">系统状态</div>
                      </div>
                    </div>
                  </Link>
                </Button>
              </div>
            </div>
          )}

          {results.length > 0 && (
            <div className="space-y-2 max-h-96 overflow-y-auto">
              <h4 className="text-sm font-medium text-muted-foreground">
                搜索结果 ({results.length})
              </h4>
              {results.map((result, index) => (
                <div
                  key={result.id}
                  className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                    index === selectedIndex 
                      ? "bg-accent border-primary" 
                      : "hover:bg-accent"
                  }`}
                  onClick={() => {
                    window.location.href = result.url;
                    onClose();
                  }}
                >
                  <div className="flex items-center gap-3">
                    <span className="text-xl">{result.icon}</span>
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{result.title}</span>
                        <Badge className={getTypeColor(result.type)}>
                          {getTypeLabel(result.type)}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {result.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {query.trim() && results.length === 0 && (
            <div className="text-center py-8">
              <div className="text-4xl mb-2">🔍</div>
              <p className="text-muted-foreground">未找到匹配的结果</p>
              <p className="text-sm text-muted-foreground mt-1">
                尝试使用不同的关键词
              </p>
            </div>
          )}

          <div className="flex items-center justify-between text-xs text-muted-foreground pt-2 border-t">
            <div className="flex gap-4">
              <span>↑↓ 导航</span>
              <span>↵ 选择</span>
              <span>⎋ 关闭</span>
            </div>
            <span>⌘K 快速搜索</span>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Hook for global search
export function useGlobalSearch() {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === "k") {
        e.preventDefault();
        setIsOpen(true);
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, []);

  return {
    isOpen,
    open: () => setIsOpen(true),
    close: () => setIsOpen(false)
  };
}
