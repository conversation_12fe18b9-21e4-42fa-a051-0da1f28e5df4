"use client";

import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface SearchBarProps {
  placeholder?: string;
  onSearch: (query: string) => void;
  suggestions?: string[];
  recentSearches?: string[];
  className?: string;
}

export function SearchBar({ 
  placeholder = "搜索...", 
  onSearch, 
  suggestions = [], 
  recentSearches = [],
  className 
}: SearchBarProps) {
  const [query, setQuery] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const [filteredSuggestions, setFilteredSuggestions] = useState<string[]>([]);

  useEffect(() => {
    if (query.trim()) {
      const filtered = suggestions.filter(suggestion =>
        suggestion.toLowerCase().includes(query.toLowerCase())
      );
      setFilteredSuggestions(filtered.slice(0, 5));
    } else {
      setFilteredSuggestions(recentSearches.slice(0, 5));
    }
  }, [query, suggestions, recentSearches]);

  const handleSearch = (searchQuery: string = query) => {
    if (searchQuery.trim()) {
      onSearch(searchQuery.trim());
      setIsOpen(false);
      setQuery(searchQuery);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch();
    } else if (e.key === "Escape") {
      setIsOpen(false);
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    handleSearch(suggestion);
  };

  return (
    <div className={cn("relative", className)}>
      <div className="flex gap-2">
        <div className="relative flex-1">
          <Input
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onFocus={() => setIsOpen(true)}
            onBlur={() => setTimeout(() => setIsOpen(false), 200)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            className="pr-10"
          />
          <Button
            variant="ghost"
            size="sm"
            className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0"
            onClick={() => handleSearch()}
          >
            🔍
          </Button>
        </div>
      </div>

      {/* Suggestions Dropdown */}
      {isOpen && (filteredSuggestions.length > 0 || query.trim()) && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-background border rounded-md shadow-lg z-50">
          <div className="p-2">
            {query.trim() === "" && recentSearches.length > 0 && (
              <div className="mb-2">
                <p className="text-xs text-muted-foreground mb-1">最近搜索</p>
                <div className="flex flex-wrap gap-1">
                  {recentSearches.slice(0, 3).map((search, index) => (
                    <Badge
                      key={index}
                      variant="secondary"
                      className="cursor-pointer hover:bg-accent"
                      onClick={() => handleSuggestionClick(search)}
                    >
                      {search}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
            
            {filteredSuggestions.length > 0 && (
              <div>
                {query.trim() && (
                  <p className="text-xs text-muted-foreground mb-1">建议搜索</p>
                )}
                <div className="space-y-1">
                  {filteredSuggestions.map((suggestion, index) => (
                    <div
                      key={index}
                      className="px-2 py-1 text-sm hover:bg-accent rounded cursor-pointer"
                      onClick={() => handleSuggestionClick(suggestion)}
                    >
                      {suggestion}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

interface QuickSearchProps {
  onSearch: (query: string) => void;
  quickFilters?: Array<{
    label: string;
    value: string;
    icon?: string;
  }>;
}

export function QuickSearch({ onSearch, quickFilters = [] }: QuickSearchProps) {
  return (
    <div className="space-y-3">
      <SearchBar
        placeholder="搜索Agent、模板或功能..."
        onSearch={onSearch}
        suggestions={[
          "侦探二人组",
          "技术咨询",
          "创意写作",
          "数据分析",
          "客服机器人",
          "营销策划"
        ]}
        recentSearches={[
          "禅探二人组",
          "GPT-4",
          "技术团队"
        ]}
      />
      
      {quickFilters.length > 0 && (
        <div className="flex flex-wrap gap-2">
          <span className="text-sm text-muted-foreground">快速筛选:</span>
          {quickFilters.map((filter, index) => (
            <Badge
              key={index}
              variant="outline"
              className="cursor-pointer hover:bg-accent"
              onClick={() => onSearch(filter.value)}
            >
              {filter.icon && <span className="mr-1">{filter.icon}</span>}
              {filter.label}
            </Badge>
          ))}
        </div>
      )}
    </div>
  );
}
