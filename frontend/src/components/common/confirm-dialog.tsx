"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

interface ConfirmDialogProps {
  trigger: React.ReactNode;
  title: string;
  description: string;
  confirmText?: string;
  cancelText?: string;
  variant?: "default" | "destructive";
  onConfirm: () => void | Promise<void>;
}

export function ConfirmDialog({
  trigger,
  title,
  description,
  confirmText = "确认",
  cancelText = "取消",
  variant = "default",
  onConfirm
}: ConfirmDialogProps) {
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleConfirm = async () => {
    setIsLoading(true);
    try {
      await onConfirm();
      setOpen(false);
    } catch (error) {
      console.error("Confirm action failed:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger}
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setOpen(false)}
            disabled={isLoading}
          >
            {cancelText}
          </Button>
          <Button
            variant={variant}
            onClick={handleConfirm}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                处理中...
              </>
            ) : (
              confirmText
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// 预设的确认对话框组件
export function DeleteConfirmDialog({
  trigger,
  itemName,
  onConfirm
}: {
  trigger: React.ReactNode;
  itemName: string;
  onConfirm: () => void | Promise<void>;
}) {
  return (
    <ConfirmDialog
      trigger={trigger}
      title="确认删除"
      description={`你确定要删除"${itemName}"吗？此操作无法撤销。`}
      confirmText="删除"
      variant="destructive"
      onConfirm={onConfirm}
    />
  );
}

export function DeactivateConfirmDialog({
  trigger,
  itemName,
  onConfirm
}: {
  trigger: React.ReactNode;
  itemName: string;
  onConfirm: () => void | Promise<void>;
}) {
  return (
    <ConfirmDialog
      trigger={trigger}
      title="确认停用"
      description={`你确定要停用"${itemName}"吗？停用后将无法使用此Agent。`}
      confirmText="停用"
      onConfirm={onConfirm}
    />
  );
}

export function ActivateConfirmDialog({
  trigger,
  itemName,
  onConfirm
}: {
  trigger: React.ReactNode;
  itemName: string;
  onConfirm: () => void | Promise<void>;
}) {
  return (
    <ConfirmDialog
      trigger={trigger}
      title="确认激活"
      description={`你确定要激活"${itemName}"吗？激活后Agent将开始运行。`}
      confirmText="激活"
      onConfirm={onConfirm}
    />
  );
}
