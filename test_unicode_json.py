#!/usr/bin/env python3
"""
测试Unicode JSON字符串解析
验证我们的JSON解析函数是否能正确处理包含Unicode转义序列的JSON字符串
"""

import json

def parse_template_json_fields(template_dict: dict) -> dict:
    """Parse JSON fields in template dictionary to ensure proper types and formats."""
    
    # Parse team_structure_template if it's a string
    if isinstance(template_dict.get("team_structure_template"), str):
        try:
            template_dict["team_structure_template"] = json.loads(template_dict["team_structure_template"])
        except (json.JSONDecodeError, TypeError):
            template_dict["team_structure_template"] = {}
    elif template_dict.get("team_structure_template") is None:
        template_dict["team_structure_template"] = {}
        
    # Parse default_config if it's a string
    if isinstance(template_dict.get("default_config"), str):
        try:
            template_dict["default_config"] = json.loads(template_dict["default_config"])
        except (json.JSONDecodeError, TypeError):
            template_dict["default_config"] = {}
    elif template_dict.get("default_config") is None:
        template_dict["default_config"] = {}
        
    # Parse template_metadata if it's a string
    if isinstance(template_dict.get("template_metadata"), str):
        try:
            template_dict["template_metadata"] = json.loads(template_dict["template_metadata"])
        except (json.JSONDecodeError, TypeError):
            template_dict["template_metadata"] = {}
    elif template_dict.get("template_metadata") is None:
        template_dict["template_metadata"] = {}
        
    # Parse tags if it's a string
    if isinstance(template_dict.get("tags"), str):
        try:
            template_dict["tags"] = json.loads(template_dict["tags"])
        except (json.JSONDecodeError, TypeError):
            template_dict["tags"] = []
    elif template_dict.get("tags") is None:
        template_dict["tags"] = []
        
    # Parse keywords if it's a string
    if isinstance(template_dict.get("keywords"), str):
        try:
            template_dict["keywords"] = json.loads(template_dict["keywords"])
        except (json.JSONDecodeError, TypeError):
            template_dict["keywords"] = []
    elif template_dict.get("keywords") is None:
        template_dict["keywords"] = []

    # Convert enum values to lowercase for Pydantic compatibility
    enum_fields = ["category", "difficulty", "visibility", "status"]
    for field in enum_fields:
        if template_dict.get(field) and isinstance(template_dict[field], str):
            template_dict[field] = template_dict[field].lower()
    
    return template_dict

def test_unicode_json_parsing():
    """测试Unicode JSON字符串解析"""
    print("🧪 测试Unicode JSON字符串解析...")
    
    # 模拟错误日志中的数据 - 包含Unicode转义序列的JSON字符串
    unicode_json_string = '{"team_name": "\\u4fa6\\u5bdf\\u56e2\\u961f", "description": "\\u8fd9\\u662f\\u4e00\\u4e2a\\u6d4b\\u8bd5\\u65b9\\u6848\\u3002"}'
    
    template_dict = {
        "id": 1,
        "template_id": "template_51b82452c9e5",
        "name": "测试模板",
        "description": "这是一个测试模板",
        "category": "BUSINESS",
        "difficulty": "INTERMEDIATE", 
        "visibility": "PRIVATE",
        "status": "ACTIVE",
        "team_structure_template": unicode_json_string,  # 包含Unicode转义序列的JSON字符串
        "default_config": '{"model": "gpt-4"}',
        "template_metadata": '{"created_from_agent": true}',
        "tags": '["测试"]',
        "keywords": '["test"]',
        "user_id": 1,
    }
    
    print("原始Unicode JSON字符串:")
    print(f"  {unicode_json_string}")
    
    # 测试直接JSON解析
    try:
        parsed_json = json.loads(unicode_json_string)
        print("\n直接JSON解析结果:")
        print(f"  team_name: {parsed_json['team_name']}")
        print(f"  description: {parsed_json['description']}")
    except Exception as e:
        print(f"\n❌ 直接JSON解析失败: {e}")
        return False
    
    # 测试我们的解析函数
    try:
        parsed_dict = parse_template_json_fields(template_dict.copy())
        print("\n我们的解析函数结果:")
        print(f"  team_structure_template类型: {type(parsed_dict['team_structure_template'])}")
        print(f"  team_name: {parsed_dict['team_structure_template']['team_name']}")
        print(f"  description: {parsed_dict['team_structure_template']['description']}")
        
        # 验证结果
        assert isinstance(parsed_dict["team_structure_template"], dict), "应该是字典类型"
        assert parsed_dict["team_structure_template"]["team_name"] == "侦察团队", "Unicode应该正确解码"
        assert parsed_dict["team_structure_template"]["description"] == "这是一个测试方案。", "Unicode应该正确解码"
        
        print("✅ Unicode JSON解析测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 我们的解析函数失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """测试更多边界情况"""
    print("\n🧪 测试更多边界情况...")
    
    # 测试各种可能的问题JSON
    test_cases = [
        # 正常的Unicode JSON
        '{"name": "\\u6d4b\\u8bd5", "value": 123}',
        # 混合ASCII和Unicode
        '{"name": "test\\u6d4b\\u8bd5", "value": "hello\\u4e16\\u754c"}',
        # 嵌套对象
        '{"team": {"name": "\\u56e2\\u961f", "members": [{"name": "\\u6210\\u5458"}]}}',
        # 空对象
        '{}',
        # 包含特殊字符
        '{"text": "\\u201c\\u6d4b\\u8bd5\\u201d"}',
    ]
    
    for i, test_json in enumerate(test_cases):
        print(f"\n测试用例 {i+1}: {test_json}")
        try:
            template_dict = {"team_structure_template": test_json}
            parsed_dict = parse_template_json_fields(template_dict)
            print(f"  ✅ 解析成功: {parsed_dict['team_structure_template']}")
        except Exception as e:
            print(f"  ❌ 解析失败: {e}")
            return False
    
    print("\n✅ 所有边界情况测试通过!")
    return True

def main():
    """主测试函数"""
    print("🚀 开始测试Unicode JSON解析...")
    print("=" * 60)
    
    success1 = test_unicode_json_parsing()
    success2 = test_edge_cases()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 所有Unicode JSON解析测试通过!")
        print("\n这证明我们的解析函数能够正确处理:")
        print("1. ✅ Unicode转义序列 (\\uXXXX)")
        print("2. ✅ 中文字符的JSON编码")
        print("3. ✅ 混合ASCII和Unicode内容")
        print("4. ✅ 嵌套对象结构")
        print("5. ✅ 各种边界情况")
        
        print("\n如果后端仍然出现错误，可能的原因:")
        print("- 服务器缓存问题，需要重启")
        print("- 数据库中的数据格式问题")
        print("- 其他代码路径没有使用我们的解析函数")
    else:
        print("❌ 测试失败，需要进一步调试")
    
    return success1 and success2

if __name__ == "__main__":
    main()
