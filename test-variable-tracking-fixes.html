<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>变量跟踪修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
            margin: 10px 0;
        }
        .status.connected { background: #d4edda; color: #155724; }
        .status.connecting { background: #fff3cd; color: #856404; }
        .status.disconnected { background: #f8d7da; color: #721c24; }
        .variable-card {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 12px;
            margin: 8px 0;
            background: #f9f9f9;
        }
        .variable-card.resolved {
            background: #d4edda;
            border-color: #c3e6cb;
        }
        .variable-name {
            font-family: monospace;
            background: #e9ecef;
            padding: 4px 8px;
            border-radius: 3px;
            font-weight: bold;
        }
        .step-badge {
            background: #007bff;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-left: 8px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .test-section {
            border-left: 4px solid #007bff;
            padding-left: 16px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>🔧 变量跟踪修复测试</h1>
    
    <div class="container">
        <h2>测试目标</h2>
        <ul>
            <li><strong>问题1</strong>: WebSocket连接状态显示 - 确保状态徽章能实时反映连接状态</li>
            <li><strong>问题2</strong>: 变量名显示格式 - 确保显示原始变量名而非中文格式</li>
            <li><strong>问题3</strong>: 工作流步骤编号 - 确保显示正确的步骤编号而非都是"步骤1"</li>
        </ul>
    </div>

    <div class="container">
        <h2>🔌 WebSocket连接状态测试</h2>
        <div>
            <span>当前状态: </span>
            <span id="ws-status" class="status disconnected">📡 未连接</span>
        </div>
        <div>
            <button onclick="testWebSocketConnection()">测试连接</button>
            <button onclick="disconnectWebSocket()">断开连接</button>
            <button onclick="simulateConnectionError()">模拟连接错误</button>
        </div>
        <div class="log" id="connection-log"></div>
    </div>

    <div class="container">
        <h2>📝 变量名格式测试</h2>
        <div class="test-section">
            <h3>测试变量</h3>
            <div id="variable-list">
                <!-- 变量卡片将在这里动态生成 -->
            </div>
            <button onclick="addTestVariable()">添加测试变量</button>
            <button onclick="updateVariableValue()">更新变量值</button>
            <button onclick="clearVariables()">清空变量</button>
        </div>
    </div>

    <div class="container">
        <h2>📊 步骤编号测试</h2>
        <div class="test-section">
            <h3>工作流步骤变量</h3>
            <div id="workflow-variables">
                <!-- 工作流变量将在这里显示 -->
            </div>
            <button onclick="createWorkflowVariables()">创建工作流变量</button>
            <button onclick="simulateStepExecution()">模拟步骤执行</button>
        </div>
    </div>

    <div class="container">
        <h2>📋 测试日志</h2>
        <div class="log" id="test-log"></div>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <script>
        let websocket = null;
        let variables = [];
        let connectionStatus = 'disconnected';

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('test-log');
            const connectionLogElement = document.getElementById('connection-log');
            
            const logEntry = `[${timestamp}] ${message}`;
            
            if (logElement) {
                logElement.innerHTML += `<div style="color: ${type === 'error' ? 'red' : type === 'success' ? 'green' : 'black'}">${logEntry}</div>`;
                logElement.scrollTop = logElement.scrollHeight;
            }
            
            if (type === 'connection' && connectionLogElement) {
                connectionLogElement.innerHTML += `<div>${logEntry}</div>`;
                connectionLogElement.scrollTop = connectionLogElement.scrollHeight;
            }
            
            console.log(logEntry);
        }

        // WebSocket连接测试
        function testWebSocketConnection() {
            updateConnectionStatus('connecting');
            log('🔌 开始测试WebSocket连接...', 'connection');
            
            // 模拟连接过程
            setTimeout(() => {
                updateConnectionStatus('connected');
                log('✅ WebSocket连接成功', 'success');
            }, 2000);
        }

        function disconnectWebSocket() {
            updateConnectionStatus('disconnected');
            log('🔌 WebSocket连接已断开', 'connection');
        }

        function simulateConnectionError() {
            updateConnectionStatus('connecting');
            log('🔌 尝试连接...', 'connection');
            
            setTimeout(() => {
                updateConnectionStatus('disconnected');
                log('❌ 连接失败 - 模拟网络错误', 'error');
            }, 1500);
        }

        function updateConnectionStatus(status) {
            connectionStatus = status;
            const statusElement = document.getElementById('ws-status');
            
            statusElement.className = `status ${status}`;
            
            switch(status) {
                case 'connected':
                    statusElement.textContent = '🔗 实时连接';
                    break;
                case 'connecting':
                    statusElement.textContent = '🔄 连接中';
                    break;
                case 'disconnected':
                default:
                    statusElement.textContent = '📡 离线模式';
                    break;
            }
            
            log(`🔌 连接状态更新: ${status}`, 'connection');
        }

        // 变量管理测试
        function addTestVariable() {
            const variableNames = [
                '{user.requirements}',
                '{system.execution_id}',
                '{analysis.result}',
                '{team.output}',
                '{workflow.status}'
            ];
            
            const randomName = variableNames[Math.floor(Math.random() * variableNames.length)];
            const stepIndex = Math.floor(Math.random() * 5);
            
            const variable = {
                id: `var_${Date.now()}`,
                placeholderName: randomName,
                value: null,
                stepIndex: stepIndex,
                sourceAgent: `agent_${stepIndex + 1}`,
                resolvedAt: null
            };
            
            variables.push(variable);
            renderVariables();
            log(`➕ 添加变量: ${randomName} (步骤 ${stepIndex + 1})`, 'info');
        }

        function updateVariableValue() {
            if (variables.length === 0) {
                log('⚠️ 没有变量可更新', 'error');
                return;
            }
            
            const randomIndex = Math.floor(Math.random() * variables.length);
            const variable = variables[randomIndex];
            
            variable.value = `测试值_${Date.now()}`;
            variable.resolvedAt = new Date().toISOString();
            
            renderVariables();
            log(`🔄 更新变量: ${variable.placeholderName} = ${variable.value}`, 'success');
        }

        function clearVariables() {
            variables = [];
            renderVariables();
            log('🗑️ 清空所有变量', 'info');
        }

        function renderVariables() {
            const container = document.getElementById('variable-list');
            
            if (variables.length === 0) {
                container.innerHTML = '<p style="color: #666;">暂无变量</p>';
                return;
            }
            
            container.innerHTML = variables.map(variable => `
                <div class="variable-card ${variable.value ? 'resolved' : ''}">
                    <div>
                        <span class="variable-name">${variable.placeholderName}</span>
                        <span class="step-badge">步骤 ${variable.stepIndex + 1}</span>
                    </div>
                    <div style="margin-top: 8px; font-size: 12px; color: #666;">
                        来源: ${variable.sourceAgent} | 
                        状态: ${variable.value ? '✅ 已解析' : '⏳ 待解析'}
                    </div>
                    ${variable.value ? `<div style="margin-top: 4px; font-size: 12px;">值: ${variable.value}</div>` : ''}
                </div>
            `).join('');
        }

        // 工作流步骤测试
        function createWorkflowVariables() {
            const workflowVariables = [
                { name: '{step1.analysis}', step: 0, agent: 'analyzer' },
                { name: '{step2.design}', step: 1, agent: 'designer' },
                { name: '{step3.implementation}', step: 2, agent: 'developer' },
                { name: '{step4.testing}', step: 3, agent: 'tester' },
                { name: '{step5.deployment}', step: 4, agent: 'deployer' }
            ];
            
            variables = workflowVariables.map((wv, index) => ({
                id: `workflow_${index}`,
                placeholderName: wv.name,
                value: null,
                stepIndex: wv.step,
                sourceAgent: wv.agent,
                resolvedAt: null
            }));
            
            renderWorkflowVariables();
            log('🔄 创建工作流变量 (5个步骤)', 'info');
        }

        function simulateStepExecution() {
            if (variables.length === 0) {
                log('⚠️ 请先创建工作流变量', 'error');
                return;
            }
            
            // 按步骤顺序解析变量
            const unresolvedVars = variables.filter(v => !v.value);
            if (unresolvedVars.length === 0) {
                log('✅ 所有步骤已完成', 'success');
                return;
            }
            
            const nextVar = unresolvedVars.sort((a, b) => a.stepIndex - b.stepIndex)[0];
            nextVar.value = `步骤${nextVar.stepIndex + 1}的输出结果`;
            nextVar.resolvedAt = new Date().toISOString();
            
            renderWorkflowVariables();
            log(`✅ 执行步骤 ${nextVar.stepIndex + 1}: ${nextVar.placeholderName}`, 'success');
        }

        function renderWorkflowVariables() {
            const container = document.getElementById('workflow-variables');
            
            if (variables.length === 0) {
                container.innerHTML = '<p style="color: #666;">暂无工作流变量</p>';
                return;
            }
            
            // 按步骤排序
            const sortedVariables = [...variables].sort((a, b) => a.stepIndex - b.stepIndex);
            
            container.innerHTML = sortedVariables.map(variable => `
                <div class="variable-card ${variable.value ? 'resolved' : ''}">
                    <div>
                        <span class="variable-name">${variable.placeholderName}</span>
                        <span class="step-badge">步骤 ${variable.stepIndex + 1}</span>
                    </div>
                    <div style="margin-top: 8px; font-size: 12px; color: #666;">
                        执行者: ${variable.sourceAgent} | 
                        状态: ${variable.value ? '✅ 已完成' : '⏳ 等待执行'}
                    </div>
                    ${variable.value ? `<div style="margin-top: 4px; font-size: 12px;">输出: ${variable.value}</div>` : ''}
                </div>
            `).join('');
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
            document.getElementById('connection-log').innerHTML = '';
        }

        // 页面加载时初始化
        window.onload = function() {
            log('🚀 变量跟踪修复测试页面已加载', 'success');
            log('📋 请按照测试目标逐一验证修复效果', 'info');
        };
    </script>
</body>
</html>
