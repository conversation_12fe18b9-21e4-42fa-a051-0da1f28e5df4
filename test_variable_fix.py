#!/usr/bin/env python3
"""
测试变量发现修复效果
"""

def test_assignee_to_role_mapping():
    """测试assignee到role的映射逻辑"""
    print("🧪 测试assignee到role的映射逻辑...")
    
    # 模拟team_members数据
    team_members = [
        {
            "name": "内容策略师",
            "role": "planner",
            "description": "负责内容策略规划",
            "system_prompt": "你是内容策略师，根据{user.requirements}制定{planner.content_strategy}。"
        },
        {
            "name": "内容撰写专家", 
            "role": "writer",
            "description": "负责内容撰写",
            "system_prompt": "你是撰写专家，基于{planner.content_strategy}撰写{writer.content_draft}。"
        },
        {
            "name": "社交媒体专家",
            "role": "social_media_specialist", 
            "description": "负责社交媒体优化",
            "system_prompt": "你是社交媒体专家，基于{writer.content_draft}优化{social_media_specialist.social_content}。"
        }
    ]
    
    # 模拟workflow steps
    workflow_steps = [
        {
            "name": "内容策略制定",
            "assignee": "内容策略师",  # 中文名称
            "description": "制定内容策略"
        },
        {
            "name": "内容撰写",
            "assignee": "内容撰写专家",  # 中文名称
            "description": "撰写内容"
        },
        {
            "name": "社交媒体优化",
            "assignee": "社交媒体专家",  # 中文名称
            "description": "优化社交媒体内容"
        }
    ]
    
    print(f"📊 测试数据:")
    print(f"  - 团队成员: {len(team_members)} 个")
    print(f"  - 工作流步骤: {len(workflow_steps)} 个")
    
    # 测试映射逻辑
    def get_role_from_assignee(assignee: str, team_members: list) -> str:
        """模拟_get_role_from_assignee方法"""
        for member in team_members:
            if member.get("name") == assignee:
                return member.get("role", assignee)
        return assignee
    
    print(f"\n🔍 测试assignee到role的映射:")
    for step in workflow_steps:
        assignee = step["assignee"]
        role = get_role_from_assignee(assignee, team_members)
        print(f"  - '{assignee}' -> '{role}'")
    
    # 模拟变量发现
    print(f"\n📝 模拟变量发现结果:")
    expected_variables = [
        {"placeholder": "{user.requirements}", "source_agent": None, "variable_type": "USER_INPUT"},
        {"placeholder": "{planner.content_strategy}", "source_agent": "planner", "variable_type": "INTER_AGENT"},
        {"placeholder": "{writer.content_draft}", "source_agent": "writer", "variable_type": "INTER_AGENT"},
        {"placeholder": "{social_media_specialist.social_content}", "source_agent": "social_media_specialist", "variable_type": "INTER_AGENT"},
    ]
    
    for var in expected_variables:
        print(f"  - {var['placeholder']}: 来源={var['source_agent']}, 类型={var['variable_type']}")
    
    # 测试变量匹配
    print(f"\n🎯 测试变量匹配:")
    for step in workflow_steps:
        assignee = step["assignee"]
        role = get_role_from_assignee(assignee, team_members)
        
        # 查找该role应该产生的变量
        agent_variables = [var for var in expected_variables if var["source_agent"] == role]
        
        print(f"  - assignee '{assignee}' (role: {role}): {len(agent_variables)} 个变量")
        for var in agent_variables:
            print(f"    - {var['placeholder']}")
    
    return True

def test_variable_discovery_flow():
    """测试完整的变量发现流程"""
    print(f"\n🔄 测试完整的变量发现流程...")
    
    # 模拟team_plan
    team_plan = {
        "team_name": "内容创作团队",
        "team_members": [
            {
                "name": "内容策略师",
                "role": "planner",
                "system_prompt": "你是内容策略师，根据{user.requirements}制定{planner.content_strategy}。"
            },
            {
                "name": "内容撰写专家", 
                "role": "writer",
                "system_prompt": "你是撰写专家，基于{planner.content_strategy}撰写{writer.content_draft}。"
            }
        ],
        "workflow_steps": [
            {"name": "策略制定", "assignee": "内容策略师"},
            {"name": "内容撰写", "assignee": "内容撰写专家"}
        ]
    }
    
    print(f"✅ 模拟team_plan结构完成")
    print(f"  - 团队成员使用role字段: {[m['role'] for m in team_plan['team_members']]}")
    print(f"  - 工作流步骤使用assignee字段: {[s['assignee'] for s in team_plan['workflow_steps']]}")
    print(f"  - 变量占位符使用role前缀: {{planner.content_strategy}}, {{writer.content_draft}}")
    
    return True

def main():
    """主测试函数"""
    print("🚀 开始测试变量发现修复效果\n")
    
    # 测试1: assignee到role映射
    test1_result = test_assignee_to_role_mapping()
    
    # 测试2: 完整流程
    test2_result = test_variable_discovery_flow()
    
    print(f"\n📊 测试结果:")
    print(f"✅ assignee到role映射: {'通过' if test1_result else '失败'}")
    print(f"✅ 完整变量发现流程: {'通过' if test2_result else '失败'}")
    
    if test1_result and test2_result:
        print(f"\n🎉 修复验证成功！")
        print(f"🔧 修复要点:")
        print(f"  - team_members中使用role字段（英文）")
        print(f"  - workflow steps中使用assignee字段（中文名称）")
        print(f"  - 变量占位符使用role作为前缀（英文）")
        print(f"  - _get_role_from_assignee方法正确映射中文名称到英文role")
        print(f"  - 变量匹配使用role而不是assignee")
    else:
        print(f"\n❌ 修复验证失败，需要进一步调试")

if __name__ == "__main__":
    main()
